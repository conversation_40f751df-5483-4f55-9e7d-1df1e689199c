buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:3.2.5")
    }
}

plugins {
    id 'java'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'com.github.kt3k.coveralls' version '2.6.3'
    id 'org.sonarqube' version '5.0.0.4638'
    id 'checkstyle'
    id 'maven-publish'
    id 'jacoco'
}

apply from: "${rootDir}/quality.gradle"

ext {
    set('gnpStarterMessages', "3.3.1")
    set('gnpStarterErrorVersion', "3.2.3")
    set('gnpStarterJwtVersion', "3.2.2")
    set('gnpStarterCuentasVersion', "3.1.3")
    set('springCloudVersion', "2024.0.0")
    set('googleCloudVersion', "6.0.0")
    set('lombokVersion', "1.18.36")
    set('lombokMapstructBindingVersion', "0.2.0")
    set('mapstructProcessorVersion', "1.6.1")
    set('gitVersion', "1.0.0-SNAPSHOT")
    set('gnpCuentaCorrienteCommonVersion', "1.0.0-SNAPSHOT")
}

allprojects {
    group = rootProject.group

    repositories {
/*
        maven {
            name "GNP repository"
            url "http://nexus.gnp.mx/repository/maven-releases"
            allowInsecureProtocol = true
        }
        maven {
            name "Maven Central"
            url "http://nexus.gnp.mx/repository/maven-central/"
            allowInsecureProtocol = true
        }
        maven {
            name "third_party_libraries"
            url "http://nexus.gnp.mx/repository/third_party_libraries/"
            allowInsecureProtocol = true
        }
        
 */
        mavenLocal()
        mavenCentral()
    }

    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }

    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
            mavenBom "com.google.cloud:spring-cloud-gcp-dependencies:${googleCloudVersion}"
        }
    }

}

subprojects {

    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'com.github.kt3k.coveralls'
    apply plugin: 'org.sonarqube'
    apply plugin: 'maven-publish'
    apply plugin: 'jacoco'

    apply from: "${rootDir}/quality.gradle"

    dependencies {
        implementation platform('org.springframework.boot:spring-boot-dependencies:3.2.5')
        implementation platform("org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}")
/*
        implementation "mx.com.gnp.boot:gnp-starter-errors:${gnpStarterErrorVersion}"
        implementation "mx.com.gnp.boot:gnp-starter-messages:${gnpStarterMessages}"

 */

        compileOnly "org.projectlombok:lombok:${lombokVersion}"
        annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
        annotationProcessor "org.projectlombok:lombok-mapstruct-binding:${lombokMapstructBindingVersion}"
        annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructProcessorVersion}"

        testImplementation 'org.springframework.boot:spring-boot-starter-test'

    }

    tasks.named('test') {
        useJUnitPlatform()
    }

    tasks.withType(JavaCompile).configureEach {
        options.encoding = 'UTF-8'
    }

    tasks.named("test") {
        enabled = false
    }

    tasks.named("compileTestJava") {
        enabled = false
    }

    task copyTtsortx64(type: Copy) {
        from "$rootDir/ttsort/ttsortx64"
        into layout.buildDirectory.dir('libs')
        fileMode 0755
    }

    tasks.named('jar') {
        dependsOn copyTtsortx64
    }

}
