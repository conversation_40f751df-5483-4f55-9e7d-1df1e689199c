package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CAMPOS-TRABAJO.
 *
 */
class WsCamposTrabajoTest {

    private WsCamposTrabajo wsCamposTrabajo;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCamposTrabajo = new WsCamposTrabajo();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsCamposTrabajo.setWsCdoper("");
        assertEquals("", wsCamposTrabajo.getWsCdoper());

        wsCamposTrabajo.setWsOpcion("");
        assertEquals("", wsCamposTrabajo.getWsOpcion());

        wsCamposTrabajo.setWsAgtIdr(0);
        assertEquals(0, wsCamposTrabajo.getWsAgtIdr());

        assertTrue(wsCamposTrabajo.getWsAgtIdrAux().isZero());

        wsCamposTrabajo.setWsInd_1(0);
        assertEquals(0, wsCamposTrabajo.getWsInd_1());

    }

    // property wsCdoper
    @Test
    void testGetterSetterWsCdoper() {

        assertNotNull(wsCamposTrabajo.getWsCdoper());

        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsCdoper(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsCdoper()));
    }

    // property wsOpcion
    @Test
    void testGetterSetterWsOpcion() {

        assertNotNull(wsCamposTrabajo.getWsOpcion());

        String vString = "A";
        wsCamposTrabajo.setWsOpcion(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsOpcion()));
    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        assertNotNull(wsCamposTrabajo.getWsAgtIdr());

        int vint = 123456789;
        wsCamposTrabajo.setWsAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsAgtIdr()));
    }

    // property wsAgtIdrAux
    @Test
    void testGetterSetterWsAgtIdrAux() {

        assertNotNull(wsCamposTrabajo.getWsAgtIdrAux());

        BigDecimal vBigDecimal = new BigDecimal("1234567890");
        wsCamposTrabajo.setWsAgtIdrAux(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsCamposTrabajo.getWsAgtIdrAux().toBigDecimal());

        int vint = 123456789;
        wsCamposTrabajo.setWsAgtIdrAux(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsAgtIdrAux()));

        NumericVar vINumericValue = new NumericVar(10, 0);;
        vINumericValue.setValue("1234567890");
        wsCamposTrabajo.setWsAgtIdrAux(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCamposTrabajo.getWsAgtIdrAux()));
    }

    // property wsInd_1
    @Test
    void testGetterSetterWsInd_1() {

        assertNotNull(wsCamposTrabajo.getWsInd_1());

        int vint = 12345;
        wsCamposTrabajo.setWsInd_1(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsInd_1()));
    }
}

