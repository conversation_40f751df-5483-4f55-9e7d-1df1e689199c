package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FLA-NUM.
 *
 */
class WsFlaNumTest {

    private WsFlaNum wsFlaNum;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFlaNum = new WsFlaNum();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFlaNum wsFlaNum2 = new WsFlaNum(parent);
        assertNotNull(wsFlaNum2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFlaNum wsFlaNum3 = new WsFlaNum(parent, sibling);
        assertNotNull(wsFlaNum3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFlaNum.getWsFlaNum0());

        assertNotNull(wsFlaNum.getWsFlaNum1());

    }

    // property wsFlaNum0
    @Test
    void testGetterSetterWsFlaNum0() {

        assertNotNull(wsFlaNum.getWsFlaNum0());

        String vString = "A";
        wsFlaNum.setWsFlaNum0(vString); // String
        assertEquals(asStr(vString), asStr(wsFlaNum.getWsFlaNum0()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFlaNum.setWsFlaNum0(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFlaNum.getWsFlaNum0()));
    }

    // property wsFlaNum1
    @Test
    void testGetterSetterWsFlaNum1() {

        assertNotNull(wsFlaNum.getWsFlaNum1());

        int vint = 1234567;
        wsFlaNum.setWsFlaNum1(vint); // int
        assertEquals(asInt(vint), asInt(wsFlaNum.getWsFlaNum1()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsFlaNum.setWsFlaNum1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFlaNum.getWsFlaNum1()));
    }
}

