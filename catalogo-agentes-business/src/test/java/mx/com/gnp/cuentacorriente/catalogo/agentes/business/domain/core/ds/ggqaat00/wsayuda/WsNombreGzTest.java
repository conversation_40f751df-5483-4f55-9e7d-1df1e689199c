package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-NOMBRE-GZ.
 *
 */
class WsNombreGzTest {

    private WsNombreGz wsNombreGz;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsNombreGz = new WsNombreGz();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsNombreGz wsNombreGz2 = new WsNombreGz(parent);
        assertNotNull(wsNombreGz2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 35);
        WsNombreGz wsNombreGz3 = new WsNombreGz(parent, sibling);
        assertNotNull(wsNombreGz3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsNombreGz.getWsNombreGzk());

        assertNotNull(wsNombreGz.getWsNombreGzNo());

        assertNotNull(wsNombreGz.getWsNombreFil());

    }

    // property wsNombreGzk
    @Test
    void testGetterSetterWsNombreGzk() {

        assertNotNull(wsNombreGz.getWsNombreGzk());

        String vString = "ABCabc1230";
        wsNombreGz.setWsNombreGzk(vString); // String
        assertEquals(asStr(vString), asStr(wsNombreGz.getWsNombreGzk()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsNombreGz.setWsNombreGzk(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsNombreGz.getWsNombreGzk()));
    }

    // property wsNombreGzNo
    @Test
    void testGetterSetterWsNombreGzNo() {

        assertNotNull(wsNombreGz.getWsNombreGzNo());

        String vString = "ABC";
        wsNombreGz.setWsNombreGzNo(vString); // String
        assertEquals(asStr(vString), asStr(wsNombreGz.getWsNombreGzNo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsNombreGz.setWsNombreGzNo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsNombreGz.getWsNombreGzNo()));
    }

    // property wsNombreFil
    @Test
    void testGetterSetterWsNombreFil() {

        assertNotNull(wsNombreGz.getWsNombreFil());

        String vString = "ABCabc12";
        wsNombreGz.setWsNombreFil(vString); // String
        assertEquals(asStr(vString), asStr(wsNombreGz.getWsNombreFil()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsNombreGz.setWsNombreFil(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsNombreGz.getWsNombreFil()));
    }
}

