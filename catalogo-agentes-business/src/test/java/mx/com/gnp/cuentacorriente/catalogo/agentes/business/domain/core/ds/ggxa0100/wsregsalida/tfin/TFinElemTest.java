package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfin;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-FIN-ELEM.
 *
 */
class TFinElemTest {

    private TFinElem tFinElem;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tFinElem = new TFinElem();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TFinElem tFinElem2 = new TFinElem(parent);
        assertNotNull(tFinElem2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 9);
        TFinElem tFinElem3 = new TFinElem(parent, sibling);
        assertNotNull(tFinElem3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tFinElem.getSTablaFin());

    }

    // property sTablaFin
    @Test
    void testGetterSetterSTablaFin() {

        assertNotNull(tFinElem.getSTablaFin());

        String vString = "ABCabc123";
        tFinElem.setSTablaFin(vString); // String
        assertEquals(asStr(vString), asStr(tFinElem.getSTablaFin()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc123");
        tFinElem.setSTablaFin(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tFinElem.getSTablaFin()));
    }
}

