package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: PK-NUM-CEDULA-PR.
 *
 */
class PkNumCedulaPrTest {

    private PkNumCedulaPr pkNumCedulaPr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        pkNumCedulaPr = new PkNumCedulaPr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        PkNumCedulaPr pkNumCedulaPr2 = new PkNumCedulaPr(parent);
        assertNotNull(pkNumCedulaPr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 20);
        PkNumCedulaPr pkNumCedulaPr3 = new PkNumCedulaPr(parent, sibling);
        assertNotNull(pkNumCedulaPr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(pkNumCedulaPr.getPkNumCedulaPDos());

        assertNotNull(pkNumCedulaPr.getPkNumCedulaPR18());

    }

    // property pkNumCedulaPDos
    @Test
    void testGetterSetterPkNumCedulaPDos() {

        assertNotNull(pkNumCedulaPr.getPkNumCedulaPDos());

        String vString = "AB";
        pkNumCedulaPr.setPkNumCedulaPDos(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedulaPr.getPkNumCedulaPDos()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        pkNumCedulaPr.setPkNumCedulaPDos(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedulaPr.getPkNumCedulaPDos()));
    }

    // property pkNumCedulaPR18
    @Test
    void testGetterSetterPkNumCedulaPR18() {

        assertNotNull(pkNumCedulaPr.getPkNumCedulaPR18());

        String vString = "ABCabc1230";
        pkNumCedulaPr.setPkNumCedulaPR18(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedulaPr.getPkNumCedulaPR18()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        pkNumCedulaPr.setPkNumCedulaPR18(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedulaPr.getPkNumCedulaPR18()));
    }
}

