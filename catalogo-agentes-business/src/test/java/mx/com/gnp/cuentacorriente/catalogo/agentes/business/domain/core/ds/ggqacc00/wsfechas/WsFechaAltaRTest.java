package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.wsfechas;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FECHA-ALTA-R.
 *
 */
class WsFechaAltaRTest {

    private WsFechaAltaR wsFechaAltaR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFechaAltaR = new WsFechaAltaR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFechaAltaR wsFechaAltaR2 = new WsFechaAltaR(parent);
        assertNotNull(wsFechaAltaR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsFechaAltaR wsFechaAltaR3 = new WsFechaAltaR(parent, sibling);
        assertNotNull(wsFechaAltaR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFechaAltaR.getWsFecAltAa());

        assertNotNull(wsFechaAltaR.getWsFecAltMm());

        assertNotNull(wsFechaAltaR.getWsFecAltDd());

    }

    // property wsFecAltAa
    @Test
    void testGetterSetterWsFecAltAa() {

        assertNotNull(wsFechaAltaR.getWsFecAltAa());

        int vint = 1234;
        wsFechaAltaR.setWsFecAltAa(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaAltaR.getWsFecAltAa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsFechaAltaR.setWsFecAltAa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaAltaR.getWsFecAltAa()));
    }

    // property wsFecAltMm
    @Test
    void testGetterSetterWsFecAltMm() {

        assertNotNull(wsFechaAltaR.getWsFecAltMm());

        int vint = 12;
        wsFechaAltaR.setWsFecAltMm(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaAltaR.getWsFecAltMm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaAltaR.setWsFecAltMm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaAltaR.getWsFecAltMm()));
    }

    // property wsFecAltDd
    @Test
    void testGetterSetterWsFecAltDd() {

        assertNotNull(wsFechaAltaR.getWsFecAltDd());

        int vint = 12;
        wsFechaAltaR.setWsFecAltDd(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaAltaR.getWsFecAltDd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaAltaR.setWsFecAltDd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaAltaR.getWsFecAltDd()));
    }
}

