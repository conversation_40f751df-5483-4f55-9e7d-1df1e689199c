package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AGT-REG.
 *
 */
class WsAgtRegTest {

    private WsAgtReg wsAgtReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAgtReg = new WsAgtReg();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAgtReg wsAgtReg2 = new WsAgtReg(parent);
        assertNotNull(wsAgtReg2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 480);
        WsAgtReg wsAgtReg3 = new WsAgtReg(parent, sibling);
        assertNotNull(wsAgtReg3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAgtReg.getWsAgtIdr());

        assertNotNull(wsAgtReg.getWsAgtRfc());

        assertNotNull(wsAgtReg.getWsAgtNom());

        assertNotNull(wsAgtReg.getWsAgtApePat());

        assertNotNull(wsAgtReg.getWsAgtApeMat());

        assertNotNull(wsAgtReg.getWsAgtFecNac());

        assertNotNull(wsAgtReg.getWsAgtNumCed());

        assertNotNull(wsAgtReg.getWsAgtFecIniCed());

        assertNotNull(wsAgtReg.getWsAgtFecFinCed());

        assertNotNull(wsAgtReg.getWsAgtExpSeg());

        assertNotNull(wsAgtReg.getWsAgtTraEsp());

        assertNotNull(wsAgtReg.getWsAgtClfLmr());

        assertNotNull(wsAgtReg.getWsFecUltAct());

        assertNotNull(wsAgtReg.getWsAgtRazSoc());

        assertNotNull(wsAgtReg.getWsUsuCveAct());

        assertNotNull(wsAgtReg.getWsNalCve());

        assertNotNull(wsAgtReg.getWsPefCve());

        assertNotNull(wsAgtReg.getWsTcaCve());

        assertNotNull(wsAgtReg.getWsMteCve());

        assertNotNull(wsAgtReg.getWsAgtNumFolInv());

        assertNotNull(wsAgtReg.getWsAgtNomExi());

        assertNotNull(wsAgtReg.getWsAgtRstInv());

        assertNotNull(wsAgtReg.getWsAgtFecIniInv());

        assertNotNull(wsAgtReg.getWsAgtFecFinInv());

        assertNotNull(wsAgtReg.getWsAgtNomDphInv());

        assertNotNull(wsAgtReg.getWsAgtFecReh());

        assertNotNull(wsAgtReg.getWsAgtFecMotEss());

        assertNotNull(wsAgtReg.getWsAgtFecMotAsgIn());

        assertNotNull(wsAgtReg.getWsAgtObs());

        assertNotNull(wsAgtReg.getWsAgtNip());

        assertNotNull(wsAgtReg.getWsAgtNumFacIso());

        assertNotNull(wsAgtReg.getWsAgtImpFacIso());

    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        assertNotNull(wsAgtReg.getWsAgtIdr());

        int vint = 123456789;
        wsAgtReg.setWsAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsAgtIdr()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsAgtReg.setWsAgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsAgtIdr()));
    }

    // property wsAgtRfc
    @Test
    void testGetterSetterWsAgtRfc() {

        assertNotNull(wsAgtReg.getWsAgtRfc());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtRfc(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtRfc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtRfc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtRfc()));
    }

    // property wsAgtNom
    @Test
    void testGetterSetterWsAgtNom() {

        assertNotNull(wsAgtReg.getWsAgtNom());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtNom(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtNom()));
    }

    // property wsAgtApePat
    @Test
    void testGetterSetterWsAgtApePat() {

        assertNotNull(wsAgtReg.getWsAgtApePat());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtApePat(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtApePat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtApePat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtApePat()));
    }

    // property wsAgtApeMat
    @Test
    void testGetterSetterWsAgtApeMat() {

        assertNotNull(wsAgtReg.getWsAgtApeMat());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtApeMat(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtApeMat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtApeMat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtApeMat()));
    }

    // property wsAgtFecNac
    @Test
    void testGetterSetterWsAgtFecNac() {

        assertNotNull(wsAgtReg.getWsAgtFecNac());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecNac(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecNac()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecNac(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecNac()));
    }

    // property wsAgtNumCed
    @Test
    void testGetterSetterWsAgtNumCed() {

        assertNotNull(wsAgtReg.getWsAgtNumCed());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtNumCed(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtNumCed()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtNumCed(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtNumCed()));
    }

    // property wsAgtFecIniCed
    @Test
    void testGetterSetterWsAgtFecIniCed() {

        assertNotNull(wsAgtReg.getWsAgtFecIniCed());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecIniCed(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecIniCed()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecIniCed(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecIniCed()));
    }

    // property wsAgtFecFinCed
    @Test
    void testGetterSetterWsAgtFecFinCed() {

        assertNotNull(wsAgtReg.getWsAgtFecFinCed());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecFinCed(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecFinCed()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecFinCed(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecFinCed()));
    }

    // property wsAgtExpSeg
    @Test
    void testGetterSetterWsAgtExpSeg() {

        assertNotNull(wsAgtReg.getWsAgtExpSeg());

        String vString = "A";
        wsAgtReg.setWsAgtExpSeg(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtExpSeg()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsAgtReg.setWsAgtExpSeg(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtExpSeg()));
    }

    // property wsAgtTraEsp
    @Test
    void testGetterSetterWsAgtTraEsp() {

        assertNotNull(wsAgtReg.getWsAgtTraEsp());

        String vString = "A";
        wsAgtReg.setWsAgtTraEsp(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtTraEsp()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsAgtReg.setWsAgtTraEsp(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtTraEsp()));
    }

    // property wsAgtClfLmr
    @Test
    void testGetterSetterWsAgtClfLmr() {

        assertNotNull(wsAgtReg.getWsAgtClfLmr());

        int vint = 1234;
        wsAgtReg.setWsAgtClfLmr(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsAgtClfLmr()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsAgtReg.setWsAgtClfLmr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsAgtClfLmr()));
    }

    // property wsFecUltAct
    @Test
    void testGetterSetterWsFecUltAct() {

        assertNotNull(wsAgtReg.getWsFecUltAct());

        String vString = "ABCabc1230";
        wsAgtReg.setWsFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsFecUltAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsFecUltAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsFecUltAct()));
    }

    // property wsAgtRazSoc
    @Test
    void testGetterSetterWsAgtRazSoc() {

        assertNotNull(wsAgtReg.getWsAgtRazSoc());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtRazSoc(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtRazSoc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtRazSoc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtRazSoc()));
    }

    // property wsUsuCveAct
    @Test
    void testGetterSetterWsUsuCveAct() {

        assertNotNull(wsAgtReg.getWsUsuCveAct());

        String vString = "ABCabc12";
        wsAgtReg.setWsUsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsUsuCveAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsAgtReg.setWsUsuCveAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsUsuCveAct()));
    }

    // property wsNalCve
    @Test
    void testGetterSetterWsNalCve() {

        assertNotNull(wsAgtReg.getWsNalCve());

        String vString = "ABC";
        wsAgtReg.setWsNalCve(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsNalCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsAgtReg.setWsNalCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsNalCve()));
    }

    // property wsPefCve
    @Test
    void testGetterSetterWsPefCve() {

        assertNotNull(wsAgtReg.getWsPefCve());

        int vint = 1234;
        wsAgtReg.setWsPefCve(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsPefCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsAgtReg.setWsPefCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsPefCve()));
    }

    // property wsTcaCve
    @Test
    void testGetterSetterWsTcaCve() {

        assertNotNull(wsAgtReg.getWsTcaCve());

        String vString = "AB";
        wsAgtReg.setWsTcaCve(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsTcaCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAgtReg.setWsTcaCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsTcaCve()));
    }

    // property wsMteCve
    @Test
    void testGetterSetterWsMteCve() {

        assertNotNull(wsAgtReg.getWsMteCve());

        int vint = 1234;
        wsAgtReg.setWsMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsMteCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsAgtReg.setWsMteCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsMteCve()));
    }

    // property wsAgtNumFolInv
    @Test
    void testGetterSetterWsAgtNumFolInv() {

        assertNotNull(wsAgtReg.getWsAgtNumFolInv());

        int vint = 123456789;
        wsAgtReg.setWsAgtNumFolInv(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsAgtNumFolInv()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsAgtReg.setWsAgtNumFolInv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsAgtNumFolInv()));
    }

    // property wsAgtNomExi
    @Test
    void testGetterSetterWsAgtNomExi() {

        assertNotNull(wsAgtReg.getWsAgtNomExi());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtNomExi(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtNomExi()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtNomExi(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtNomExi()));
    }

    // property wsAgtRstInv
    @Test
    void testGetterSetterWsAgtRstInv() {

        assertNotNull(wsAgtReg.getWsAgtRstInv());

        String vString = "ABCab";
        wsAgtReg.setWsAgtRstInv(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtRstInv()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsAgtReg.setWsAgtRstInv(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtRstInv()));
    }

    // property wsAgtFecIniInv
    @Test
    void testGetterSetterWsAgtFecIniInv() {

        assertNotNull(wsAgtReg.getWsAgtFecIniInv());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecIniInv(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecIniInv()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecIniInv(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecIniInv()));
    }

    // property wsAgtFecFinInv
    @Test
    void testGetterSetterWsAgtFecFinInv() {

        assertNotNull(wsAgtReg.getWsAgtFecFinInv());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecFinInv(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecFinInv()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecFinInv(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecFinInv()));
    }

    // property wsAgtNomDphInv
    @Test
    void testGetterSetterWsAgtNomDphInv() {

        assertNotNull(wsAgtReg.getWsAgtNomDphInv());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtNomDphInv(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtNomDphInv()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtNomDphInv(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtNomDphInv()));
    }

    // property wsAgtFecReh
    @Test
    void testGetterSetterWsAgtFecReh() {

        assertNotNull(wsAgtReg.getWsAgtFecReh());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecReh(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecReh()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecReh(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecReh()));
    }

    // property wsAgtFecMotEss
    @Test
    void testGetterSetterWsAgtFecMotEss() {

        assertNotNull(wsAgtReg.getWsAgtFecMotEss());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecMotEss(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecMotEss()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecMotEss(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecMotEss()));
    }

    // property wsAgtFecMotAsgIn
    @Test
    void testGetterSetterWsAgtFecMotAsgIn() {

        assertNotNull(wsAgtReg.getWsAgtFecMotAsgIn());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtFecMotAsgIn(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtFecMotAsgIn()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtFecMotAsgIn(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtFecMotAsgIn()));
    }

    // property wsAgtObs
    @Test
    void testGetterSetterWsAgtObs() {

        assertNotNull(wsAgtReg.getWsAgtObs());

        String vString = "ABCabc1230";
        wsAgtReg.setWsAgtObs(vString); // String
        assertEquals(asStr(vString), asStr(wsAgtReg.getWsAgtObs()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAgtReg.setWsAgtObs(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgtReg.getWsAgtObs()));
    }

    // property wsAgtNip
    @Test
    void testGetterSetterWsAgtNip() {

        assertNotNull(wsAgtReg.getWsAgtNip());

        int vint = 1234;
        wsAgtReg.setWsAgtNip(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsAgtNip()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsAgtReg.setWsAgtNip(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsAgtNip()));
    }

    // property wsAgtNumFacIso
    @Test
    void testGetterSetterWsAgtNumFacIso() {

        assertNotNull(wsAgtReg.getWsAgtNumFacIso());

        int vint = 123456789;
        wsAgtReg.setWsAgtNumFacIso(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsAgtNumFacIso()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsAgtReg.setWsAgtNumFacIso(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsAgtNumFacIso()));
    }

    // property wsAgtImpFacIso
    @Test
    void testGetterSetterWsAgtImpFacIso() {

        assertNotNull(wsAgtReg.getWsAgtImpFacIso());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsAgtReg.setWsAgtImpFacIso(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAgtReg.getWsAgtImpFacIso().toBigDecimal());

        int vint = 123456789;
        wsAgtReg.setWsAgtImpFacIso(vint); // int
        assertEquals(asInt(vint), asInt(wsAgtReg.getWsAgtImpFacIso()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsAgtReg.setWsAgtImpFacIso(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAgtReg.getWsAgtImpFacIso()));
    }
}

