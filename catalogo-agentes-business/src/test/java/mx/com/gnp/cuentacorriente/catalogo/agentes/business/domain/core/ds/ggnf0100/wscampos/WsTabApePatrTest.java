package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TAB-APE-PATR.
 *
 */
class WsTabApePatrTest {

    private WsTabApePatr wsTabApePatr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTabApePatr = new WsTabApePatr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsTabApePatr wsTabApePatr2 = new WsTabApePatr(parent);
        assertNotNull(wsTabApePatr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 40);
        WsTabApePatr wsTabApePatr3 = new WsTabApePatr(parent, sibling);
        assertNotNull(wsTabApePatr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsTabApePatr.getEleApePat());

    }

    // property eleApePat
    @Test
    void testGetterSetterEleApePat() {

        assertNotNull(wsTabApePatr.getEleApePat());
        assertNotNull(wsTabApePatr.getEleApePat(1));

        String vString = "A";
        wsTabApePatr.getEleApePat().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsTabApePatr.getEleApePat(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsTabApePatr.getEleApePat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsTabApePatr.getEleApePat(1)));
    }
}

