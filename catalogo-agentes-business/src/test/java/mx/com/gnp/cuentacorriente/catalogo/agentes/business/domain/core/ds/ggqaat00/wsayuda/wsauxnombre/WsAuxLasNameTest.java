package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-LAS-NAME.
 *
 */
class WsAuxLasNameTest {

    private WsAuxLasName wsAuxLasName;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxLasName = new WsAuxLasName();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAuxLasName wsAuxLasName2 = new WsAuxLasName(parent);
        assertNotNull(wsAuxLasName2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 40);
        WsAuxLasName wsAuxLasName3 = new WsAuxLasName(parent, sibling);
        assertNotNull(wsAuxLasName3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAuxLasName.getWsAuxLastName());

    }

    // property wsAuxLastName
    @Test
    void testGetterSetterWsAuxLastName() {

        assertNotNull(wsAuxLasName.getWsAuxLastName());
        assertNotNull(wsAuxLasName.getWsAuxLastName(1));

        String vString = "A";
        wsAuxLasName.getWsAuxLastName().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxLasName.getWsAuxLastName(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxLasName.getWsAuxLastName().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsAuxLasName.getWsAuxLastName(1)));
    }
}

