package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGCCTEDO0.
 *
 */
class WsDclgcctedo0Test {

    private WsDclgcctedo0 wsDclgcctedo0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgcctedo0 = new WsDclgcctedo0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgcctedo0.setEdoEdoCve("");
        assertEquals("", wsDclgcctedo0.getEdoEdoCve());

        wsDclgcctedo0.setEdoPaiCve("");
        assertEquals("", wsDclgcctedo0.getEdoPaiCve());

        wsDclgcctedo0.setEdoEdoNom("");
        assertEquals("", wsDclgcctedo0.getEdoEdoNom());

        wsDclgcctedo0.setEdoEdoCodNum(0);
        assertEquals(0, wsDclgcctedo0.getEdoEdoCodNum());

        wsDclgcctedo0.setEdoFecUltAct("");
        assertEquals("", wsDclgcctedo0.getEdoFecUltAct());

        wsDclgcctedo0.setEdoUsuCveAct("");
        assertEquals("", wsDclgcctedo0.getEdoUsuCveAct());

    }

    // property edoEdoCve
    @Test
    void testGetterSetterEdoEdoCve() {

        assertNotNull(wsDclgcctedo0.getEdoEdoCve());

        String vString = "ABCa";
        wsDclgcctedo0.setEdoEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctedo0.getEdoEdoCve()));
    }

    // property edoPaiCve
    @Test
    void testGetterSetterEdoPaiCve() {

        assertNotNull(wsDclgcctedo0.getEdoPaiCve());

        String vString = "ABC";
        wsDclgcctedo0.setEdoPaiCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctedo0.getEdoPaiCve()));
    }

    // property edoEdoNom
    @Test
    void testGetterSetterEdoEdoNom() {

        assertNotNull(wsDclgcctedo0.getEdoEdoNom());

        String vString = "ABCabc1230";
        wsDclgcctedo0.setEdoEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctedo0.getEdoEdoNom()));
    }

    // property edoEdoCodNum
    @Test
    void testGetterSetterEdoEdoCodNum() {

        assertNotNull(wsDclgcctedo0.getEdoEdoCodNum());

        int vint = 12;
        wsDclgcctedo0.setEdoEdoCodNum(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgcctedo0.getEdoEdoCodNum()));
    }

    // property edoFecUltAct
    @Test
    void testGetterSetterEdoFecUltAct() {

        assertNotNull(wsDclgcctedo0.getEdoFecUltAct());

        String vString = "ABCabc1230";
        wsDclgcctedo0.setEdoFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctedo0.getEdoFecUltAct()));
    }

    // property edoUsuCveAct
    @Test
    void testGetterSetterEdoUsuCveAct() {

        assertNotNull(wsDclgcctedo0.getEdoUsuCveAct());

        String vString = "ABCabc12";
        wsDclgcctedo0.setEdoUsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctedo0.getEdoUsuCveAct()));
    }
}

