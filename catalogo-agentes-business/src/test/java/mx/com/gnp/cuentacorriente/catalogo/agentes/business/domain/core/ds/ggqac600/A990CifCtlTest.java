package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: A990-CIF-CTL.
 *
 */
class A990CifCtlTest {

    private A990CifCtl a990CifCtl;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        a990CifCtl = new A990CifCtl();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        a990CifCtl.setA990RegLeiAgtA1(0);
        assertEquals(0, a990CifCtl.getA990RegLeiAgtA1());

        a990CifCtl.setA990RegLeiInsHfa(0);
        assertEquals(0, a990CifCtl.getA990RegLeiInsHfa());

        a990CifCtl.setA990RegLeiUpdHfa(0);
        assertEquals(0, a990CifCtl.getA990RegLeiUpdHfa());

        a990CifCtl.setA990RegLeiAgtExc(0);
        assertEquals(0, a990CifCtl.getA990RegLeiAgtExc());

    }

    // property a990RegLeiAgtA1
    @Test
    void testGetterSetterA990RegLeiAgtA1() {

        assertNotNull(a990CifCtl.getA990RegLeiAgtA1());

        int vint = 123456789;
        a990CifCtl.setA990RegLeiAgtA1(vint); // int
        assertEquals(asInt(vint), asInt(a990CifCtl.getA990RegLeiAgtA1()));
    }

    // property a990RegLeiInsHfa
    @Test
    void testGetterSetterA990RegLeiInsHfa() {

        assertNotNull(a990CifCtl.getA990RegLeiInsHfa());

        int vint = 123456789;
        a990CifCtl.setA990RegLeiInsHfa(vint); // int
        assertEquals(asInt(vint), asInt(a990CifCtl.getA990RegLeiInsHfa()));
    }

    // property a990RegLeiUpdHfa
    @Test
    void testGetterSetterA990RegLeiUpdHfa() {

        assertNotNull(a990CifCtl.getA990RegLeiUpdHfa());

        int vint = 123456789;
        a990CifCtl.setA990RegLeiUpdHfa(vint); // int
        assertEquals(asInt(vint), asInt(a990CifCtl.getA990RegLeiUpdHfa()));
    }

    // property a990RegLeiAgtExc
    @Test
    void testGetterSetterA990RegLeiAgtExc() {

        assertNotNull(a990CifCtl.getA990RegLeiAgtExc());

        int vint = 123456789;
        a990CifCtl.setA990RegLeiAgtExc(vint); // int
        assertEquals(asInt(vint), asInt(a990CifCtl.getA990RegLeiAgtExc()));
    }
}

