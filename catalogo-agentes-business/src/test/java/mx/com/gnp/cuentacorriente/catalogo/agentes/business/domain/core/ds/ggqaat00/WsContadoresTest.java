package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CONTADORES.
 *
 */
class WsContadoresTest {

    private WsContadores wsContadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsContadores = new WsContadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsContadores.setWcRegistrosLeiAgt(0);
        assertEquals(0, wsContadores.getWcRegistrosLeiAgt());

        wsContadores.setWcRegistrosGrab(0);
        assertEquals(0, wsContadores.getWcRegistrosGrab());

        wsContadores.setWcC1(0);
        assertEquals(0, wsContadores.getWcC1());

        wsContadores.setWcC2(0);
        assertEquals(0, wsContadores.getWcC2());

        wsContadores.setWcC3(0);
        assertEquals(0, wsContadores.getWcC3());

        wsContadores.setWcC4(0);
        assertEquals(0, wsContadores.getWcC4());

        wsContadores.setWcC5(0);
        assertEquals(0, wsContadores.getWcC5());

    }

    // property wcRegistrosLeiAgt
    @Test
    void testGetterSetterWcRegistrosLeiAgt() {

        assertNotNull(wsContadores.getWcRegistrosLeiAgt());

        int vint = 123456789;
        wsContadores.setWcRegistrosLeiAgt(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWcRegistrosLeiAgt()));
    }

    // property wcRegistrosGrab
    @Test
    void testGetterSetterWcRegistrosGrab() {

        assertNotNull(wsContadores.getWcRegistrosGrab());

        int vint = 123456789;
        wsContadores.setWcRegistrosGrab(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWcRegistrosGrab()));
    }

    // property wcC1
    @Test
    void testGetterSetterWcC1() {

        assertNotNull(wsContadores.getWcC1());

        int vint = 123;
        wsContadores.setWcC1(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWcC1()));
    }

    // property wcC2
    @Test
    void testGetterSetterWcC2() {

        assertNotNull(wsContadores.getWcC2());

        int vint = 123;
        wsContadores.setWcC2(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWcC2()));
    }

    // property wcC3
    @Test
    void testGetterSetterWcC3() {

        assertNotNull(wsContadores.getWcC3());

        int vint = 123;
        wsContadores.setWcC3(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWcC3()));
    }

    // property wcC4
    @Test
    void testGetterSetterWcC4() {

        assertNotNull(wsContadores.getWcC4());

        int vint = 123;
        wsContadores.setWcC4(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWcC4()));
    }

    // property wcC5
    @Test
    void testGetterSetterWcC5() {

        assertNotNull(wsContadores.getWcC5());

        int vint = 123;
        wsContadores.setWcC5(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWcC5()));
    }
}

