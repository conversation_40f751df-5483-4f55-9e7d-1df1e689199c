package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo.variables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: CON-INCIDENCIAS.
 *
 */
class ConIncidenciasTest {

    private ConIncidencias conIncidencias;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        conIncidencias = new ConIncidencias();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        conIncidencias.setConMensaje("");
        assertEquals("", conIncidencias.getConMensaje());

    }

    // property conMensaje
    @Test
    void testGetterSetterConMensaje() {

        assertNotNull(conIncidencias.getConMensaje());

        String vString = "ABCabc1230";
        conIncidencias.setConMensaje(vString); // String
        assertEquals(asStr(vString), asStr(conIncidencias.getConMensaje()));
    }
}

