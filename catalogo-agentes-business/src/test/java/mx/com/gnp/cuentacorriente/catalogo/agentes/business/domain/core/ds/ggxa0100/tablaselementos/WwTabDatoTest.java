package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaselementos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WW-TAB-DATO.
 *
 */
class WwTabDatoTest {

    private WwTabDato wwTabDato;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wwTabDato = new WwTabDato();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WwTabDato wwTabDato2 = new WwTabDato(parent);
        assertNotNull(wwTabDato2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 3);
        WwTabDato wwTabDato3 = new WwTabDato(parent, sibling);
        assertNotNull(wwTabDato3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wwTabDato.getWwCampo());

    }

    // property wwCampo
    @Test
    void testGetterSetterWwCampo() {

        assertNotNull(wwTabDato.getWwCampo());
        assertNotNull(wwTabDato.getWwCampo(1));

        String vString = "A";
        wwTabDato.getWwCampo().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wwTabDato.getWwCampo(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wwTabDato.getWwCampo().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wwTabDato.getWwCampo(1)));
    }
}

