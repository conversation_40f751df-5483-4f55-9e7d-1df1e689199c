package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-SALISR.
 *
 */
class RegSalisrTest {

    private RegSalisr regSalisr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regSalisr = new RegSalisr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegSalisr regSalisr2 = new RegSalisr(parent);
        assertNotNull(regSalisr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 785);
        RegSalisr regSalisr3 = new RegSalisr(parent, sibling);
        assertNotNull(regSalisr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regSalisr.getWsSalwfAgtIdr());

        assertNotNull(regSalisr.getWsSalwfFecIniPdo());

        assertNotNull(regSalisr.getWsSalwfFecFinPdo());

        assertNotNull(regSalisr.getWsSalwfEsaCve());

        assertNotNull(regSalisr.getWsSalwfPefCve());

        assertNotNull(regSalisr.getWsSalwfOfnCve());

        assertNotNull(regSalisr.getWsSalwfCnaGerZonEnv());

        assertNotNull(regSalisr.getWsSalwfDsgCve());

        assertNotNull(regSalisr.getWsSalwfAgtRfc());

        assertNotNull(regSalisr.getWsSalwfNomEmi());

        assertNotNull(regSalisr.getWsSalwfDmaCae());

        assertNotNull(regSalisr.getWsSalwfDmaNum());

        assertNotNull(regSalisr.getWsSalwfDmaInt());

        assertNotNull(regSalisr.getWsSalwfDmaCol());

        assertNotNull(regSalisr.getWsSalwfMndNom());

        assertNotNull(regSalisr.getWsSalwfEdoNom());

        assertNotNull(regSalisr.getWsSalwfPaiNom());

        assertNotNull(regSalisr.getWsSalwfCpoCve());

        assertNotNull(regSalisr.getWsSalwfMteCve());

        assertNotNull(regSalisr.getWsSalwfTfpCve());

        assertNotNull(regSalisr.getWsSalwfCtbNumCta());

        assertNotNull(regSalisr.getWsSalwfIndPag());

        assertNotNull(regSalisr.getWsSalwfIndExc());

        assertNotNull(regSalisr.getWsSalwfUsuCve());

        assertNotNull(regSalisr.getWsSalwfNomPrg());

        assertNotNull(regSalisr.getWsSalwfFecUltAct());

        assertNotNull(regSalisr.getWsSalwfPjeIva());

        assertNotNull(regSalisr.getWsSalwfPjeIsr());

        assertNotNull(regSalisr.getWsSalwfCdregfis());

        assertNotNull(regSalisr.getWsSalwfNomrznso());

        assertNotNull(regSalisr.getWsSalwfVerCfd());

        assertNotNull(regSalisr.getWsSalwfCedNvi());

        assertNotNull(regSalisr.getWsSalwfCedVid());

    }

    // property wsSalwfAgtIdr
    @Test
    void testGetterSetterWsSalwfAgtIdr() {

        assertNotNull(regSalisr.getWsSalwfAgtIdr());

        String vString = "ABCabc123";
        regSalisr.setWsSalwfAgtIdr(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfAgtIdr()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc123");
        regSalisr.setWsSalwfAgtIdr(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfAgtIdr()));
    }

    // property wsSalwfFecIniPdo
    @Test
    void testGetterSetterWsSalwfFecIniPdo() {

        assertNotNull(regSalisr.getWsSalwfFecIniPdo());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfFecIniPdo(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfFecIniPdo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfFecIniPdo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfFecIniPdo()));
    }

    // property wsSalwfFecFinPdo
    @Test
    void testGetterSetterWsSalwfFecFinPdo() {

        assertNotNull(regSalisr.getWsSalwfFecFinPdo());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfFecFinPdo(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfFecFinPdo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfFecFinPdo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfFecFinPdo()));
    }

    // property wsSalwfEsaCve
    @Test
    void testGetterSetterWsSalwfEsaCve() {

        assertNotNull(regSalisr.getWsSalwfEsaCve());

        String vString = "ABCabc";
        regSalisr.setWsSalwfEsaCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfEsaCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfEsaCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfEsaCve()));
    }

    // property wsSalwfPefCve
    @Test
    void testGetterSetterWsSalwfPefCve() {

        assertNotNull(regSalisr.getWsSalwfPefCve());

        String vString = "ABCabc";
        regSalisr.setWsSalwfPefCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfPefCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfPefCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfPefCve()));
    }

    // property wsSalwfOfnCve
    @Test
    void testGetterSetterWsSalwfOfnCve() {

        assertNotNull(regSalisr.getWsSalwfOfnCve());

        String vString = "ABCabc";
        regSalisr.setWsSalwfOfnCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfOfnCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfOfnCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfOfnCve()));
    }

    // property wsSalwfCnaGerZonEnv
    @Test
    void testGetterSetterWsSalwfCnaGerZonEnv() {

        assertNotNull(regSalisr.getWsSalwfCnaGerZonEnv());

        String vString = "ABCabc";
        regSalisr.setWsSalwfCnaGerZonEnv(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfCnaGerZonEnv()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfCnaGerZonEnv(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfCnaGerZonEnv()));
    }

    // property wsSalwfDsgCve
    @Test
    void testGetterSetterWsSalwfDsgCve() {

        assertNotNull(regSalisr.getWsSalwfDsgCve());

        String vString = "ABCabc";
        regSalisr.setWsSalwfDsgCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfDsgCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfDsgCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfDsgCve()));
    }

    // property wsSalwfAgtRfc
    @Test
    void testGetterSetterWsSalwfAgtRfc() {

        assertNotNull(regSalisr.getWsSalwfAgtRfc());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfAgtRfc(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfAgtRfc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfAgtRfc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfAgtRfc()));
    }

    // property wsSalwfNomEmi
    @Test
    void testGetterSetterWsSalwfNomEmi() {

        assertNotNull(regSalisr.getWsSalwfNomEmi());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfNomEmi(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfNomEmi()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfNomEmi(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfNomEmi()));
    }

    // property wsSalwfDmaCae
    @Test
    void testGetterSetterWsSalwfDmaCae() {

        assertNotNull(regSalisr.getWsSalwfDmaCae());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfDmaCae(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfDmaCae()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfDmaCae(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfDmaCae()));
    }

    // property wsSalwfDmaNum
    @Test
    void testGetterSetterWsSalwfDmaNum() {

        assertNotNull(regSalisr.getWsSalwfDmaNum());

        String vString = "ABCab";
        regSalisr.setWsSalwfDmaNum(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfDmaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regSalisr.setWsSalwfDmaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfDmaNum()));
    }

    // property wsSalwfDmaInt
    @Test
    void testGetterSetterWsSalwfDmaInt() {

        assertNotNull(regSalisr.getWsSalwfDmaInt());

        String vString = "ABCab";
        regSalisr.setWsSalwfDmaInt(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfDmaInt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regSalisr.setWsSalwfDmaInt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfDmaInt()));
    }

    // property wsSalwfDmaCol
    @Test
    void testGetterSetterWsSalwfDmaCol() {

        assertNotNull(regSalisr.getWsSalwfDmaCol());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfDmaCol(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfDmaCol()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfDmaCol(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfDmaCol()));
    }

    // property wsSalwfMndNom
    @Test
    void testGetterSetterWsSalwfMndNom() {

        assertNotNull(regSalisr.getWsSalwfMndNom());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfMndNom(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfMndNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfMndNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfMndNom()));
    }

    // property wsSalwfEdoNom
    @Test
    void testGetterSetterWsSalwfEdoNom() {

        assertNotNull(regSalisr.getWsSalwfEdoNom());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfEdoNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfEdoNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfEdoNom()));
    }

    // property wsSalwfPaiNom
    @Test
    void testGetterSetterWsSalwfPaiNom() {

        assertNotNull(regSalisr.getWsSalwfPaiNom());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfPaiNom(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfPaiNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfPaiNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfPaiNom()));
    }

    // property wsSalwfCpoCve
    @Test
    void testGetterSetterWsSalwfCpoCve() {

        assertNotNull(regSalisr.getWsSalwfCpoCve());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfCpoCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfCpoCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfCpoCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfCpoCve()));
    }

    // property wsSalwfMteCve
    @Test
    void testGetterSetterWsSalwfMteCve() {

        assertNotNull(regSalisr.getWsSalwfMteCve());

        String vString = "ABCabc";
        regSalisr.setWsSalwfMteCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfMteCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfMteCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfMteCve()));
    }

    // property wsSalwfTfpCve
    @Test
    void testGetterSetterWsSalwfTfpCve() {

        assertNotNull(regSalisr.getWsSalwfTfpCve());

        String vString = "ABCabc";
        regSalisr.setWsSalwfTfpCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfTfpCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfTfpCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfTfpCve()));
    }

    // property wsSalwfCtbNumCta
    @Test
    void testGetterSetterWsSalwfCtbNumCta() {

        assertNotNull(regSalisr.getWsSalwfCtbNumCta());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfCtbNumCta(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfCtbNumCta()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfCtbNumCta(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfCtbNumCta()));
    }

    // property wsSalwfIndPag
    @Test
    void testGetterSetterWsSalwfIndPag() {

        assertNotNull(regSalisr.getWsSalwfIndPag());

        String vString = "A";
        regSalisr.setWsSalwfIndPag(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfIndPag()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regSalisr.setWsSalwfIndPag(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfIndPag()));
    }

    // property wsSalwfIndExc
    @Test
    void testGetterSetterWsSalwfIndExc() {

        assertNotNull(regSalisr.getWsSalwfIndExc());

        String vString = "A";
        regSalisr.setWsSalwfIndExc(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfIndExc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regSalisr.setWsSalwfIndExc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfIndExc()));
    }

    // property wsSalwfUsuCve
    @Test
    void testGetterSetterWsSalwfUsuCve() {

        assertNotNull(regSalisr.getWsSalwfUsuCve());

        String vString = "ABCabc12";
        regSalisr.setWsSalwfUsuCve(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfUsuCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        regSalisr.setWsSalwfUsuCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfUsuCve()));
    }

    // property wsSalwfNomPrg
    @Test
    void testGetterSetterWsSalwfNomPrg() {

        assertNotNull(regSalisr.getWsSalwfNomPrg());

        String vString = "ABCabc12";
        regSalisr.setWsSalwfNomPrg(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfNomPrg()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        regSalisr.setWsSalwfNomPrg(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfNomPrg()));
    }

    // property wsSalwfFecUltAct
    @Test
    void testGetterSetterWsSalwfFecUltAct() {

        assertNotNull(regSalisr.getWsSalwfFecUltAct());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfFecUltAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfFecUltAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfFecUltAct()));
    }

    // property wsSalwfPjeIva
    @Test
    void testGetterSetterWsSalwfPjeIva() {

        assertNotNull(regSalisr.getWsSalwfPjeIva());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        regSalisr.setWsSalwfPjeIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, regSalisr.getWsSalwfPjeIva().toBigDecimal());

        int vint = 123;
        regSalisr.setWsSalwfPjeIva(vint); // int
        assertEquals(asInt(vint), asInt(regSalisr.getWsSalwfPjeIva()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        regSalisr.setWsSalwfPjeIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regSalisr.getWsSalwfPjeIva()));
    }

    // property wsSalwfPjeIsr
    @Test
    void testGetterSetterWsSalwfPjeIsr() {

        assertNotNull(regSalisr.getWsSalwfPjeIsr());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        regSalisr.setWsSalwfPjeIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, regSalisr.getWsSalwfPjeIsr().toBigDecimal());

        int vint = 123;
        regSalisr.setWsSalwfPjeIsr(vint); // int
        assertEquals(asInt(vint), asInt(regSalisr.getWsSalwfPjeIsr()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        regSalisr.setWsSalwfPjeIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regSalisr.getWsSalwfPjeIsr()));
    }

    // property wsSalwfCdregfis
    @Test
    void testGetterSetterWsSalwfCdregfis() {

        assertNotNull(regSalisr.getWsSalwfCdregfis());

        String vString = "ABC";
        regSalisr.setWsSalwfCdregfis(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfCdregfis()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regSalisr.setWsSalwfCdregfis(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfCdregfis()));
    }

    // property wsSalwfNomrznso
    @Test
    void testGetterSetterWsSalwfNomrznso() {

        assertNotNull(regSalisr.getWsSalwfNomrznso());

        String vString = "ABCabc1230";
        regSalisr.setWsSalwfNomrznso(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfNomrznso()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regSalisr.setWsSalwfNomrznso(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfNomrznso()));
    }

    // property wsSalwfVerCfd
    @Test
    void testGetterSetterWsSalwfVerCfd() {

        assertNotNull(regSalisr.getWsSalwfVerCfd());

        String vString = "ABCabc";
        regSalisr.setWsSalwfVerCfd(vString); // String
        assertEquals(asStr(vString), asStr(regSalisr.getWsSalwfVerCfd()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regSalisr.setWsSalwfVerCfd(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regSalisr.getWsSalwfVerCfd()));
    }

    // property wsSalwfCedNvi
    @Test
    void testGetterSetterWsSalwfCedNvi() {

        assertNotNull(regSalisr.getWsSalwfCedNvi());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        regSalisr.setWsSalwfCedNvi(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, regSalisr.getWsSalwfCedNvi().toBigDecimal());

        int vint = 123;
        regSalisr.setWsSalwfCedNvi(vint); // int
        assertEquals(asInt(vint), asInt(regSalisr.getWsSalwfCedNvi()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        regSalisr.setWsSalwfCedNvi(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regSalisr.getWsSalwfCedNvi()));
    }

    // property wsSalwfCedVid
    @Test
    void testGetterSetterWsSalwfCedVid() {

        assertNotNull(regSalisr.getWsSalwfCedVid());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        regSalisr.setWsSalwfCedVid(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, regSalisr.getWsSalwfCedVid().toBigDecimal());

        int vint = 123;
        regSalisr.setWsSalwfCedVid(vint); // int
        assertEquals(asInt(vint), asInt(regSalisr.getWsSalwfCedVid()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        regSalisr.setWsSalwfCedVid(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regSalisr.getWsSalwfCedVid()));
    }
}

