package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.tablaexcagt;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TAB-EXC-AGT.
 *
 */
class TabExcAgtTest {

    private TabExcAgt tabExcAgt;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tabExcAgt = new TabExcAgt(1);
        tabExcAgt.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TabExcAgt tabExcAgt2 = new TabExcAgt(parent, 1);
        assertNotNull(tabExcAgt2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TabExcAgt tabExcAgt3 = new TabExcAgt(parent, sibling, 1);
        assertNotNull(tabExcAgt3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tabExcAgt.getTbAgente());

        assertNotNull(tabExcAgt.getTbIndExcAgt());

    }

    // property tbAgente
    @Test
    void testGetterSetterTbAgente() {

        assertNotNull(tabExcAgt.getTbAgente());

        int vint = 12345;
        tabExcAgt.setTbAgente(vint); // int
        assertEquals(asInt(vint), asInt(tabExcAgt.getTbAgente()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        tabExcAgt.setTbAgente(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabExcAgt.getTbAgente()));
    }

    // property tbIndExcAgt
    @Test
    void testGetterSetterTbIndExcAgt() {

        assertNotNull(tabExcAgt.getTbIndExcAgt());

        String vString = "A";
        tabExcAgt.setTbIndExcAgt(vString); // String
        assertEquals(asStr(vString), asStr(tabExcAgt.getTbIndExcAgt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tabExcAgt.setTbIndExcAgt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabExcAgt.getTbIndExcAgt()));
    }
}

