package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: AGT0.
 *
 */
class Agt0Test {

    private Agt0 agt0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        agt0 = new Agt0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        agt0.setWsAgtAnt(0);
        assertEquals(0, agt0.getWsAgtAnt());

        agt0.setAgtAgtIdr(0);
        assertEquals(0, agt0.getAgtAgtIdr());

        agt0.setAgtAgtNom("");
        assertEquals("", agt0.getAgtAgtNom());

        agt0.setAgtAgtApePat("");
        assertEquals("", agt0.getAgtAgtApePat());

        agt0.setAgtAgtApeMat("");
        assertEquals("", agt0.getAgtAgtApeMat());

        agt0.setAgtAgtNip(0);
        assertEquals(0, agt0.getAgtAgtNip());

        agt0.setCmaCmaDes("");
        assertEquals("", agt0.getCmaCmaDes());

        agt0.setWsIndGerente("");
        assertEquals("", agt0.getWsIndGerente());

        agt0.setWsOrvCve(0);
        assertEquals(0, agt0.getWsOrvCve());

        agt0.setAgtRazSoc("");
        assertEquals("", agt0.getAgtRazSoc());

        agt0.setAgtPefCve(0);
        assertEquals(0, agt0.getAgtPefCve());

    }

    // property wsAgtAnt
    @Test
    void testGetterSetterWsAgtAnt() {

        assertNotNull(agt0.getWsAgtAnt());

        int vint = 123456789;
        agt0.setWsAgtAnt(vint); // int
        assertEquals(asInt(vint), asInt(agt0.getWsAgtAnt()));
    }

    // property agtAgtIdr
    @Test
    void testGetterSetterAgtAgtIdr() {

        assertNotNull(agt0.getAgtAgtIdr());

        int vint = 123456789;
        agt0.setAgtAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(agt0.getAgtAgtIdr()));
    }

    // property agtAgtNom
    @Test
    void testGetterSetterAgtAgtNom() {

        assertNotNull(agt0.getAgtAgtNom());

        String vString = "ABCabc1230";
        agt0.setAgtAgtNom(vString); // String
        assertEquals(asStr(vString), asStr(agt0.getAgtAgtNom()));
    }

    // property agtAgtApePat
    @Test
    void testGetterSetterAgtAgtApePat() {

        assertNotNull(agt0.getAgtAgtApePat());

        String vString = "ABCabc1230";
        agt0.setAgtAgtApePat(vString); // String
        assertEquals(asStr(vString), asStr(agt0.getAgtAgtApePat()));
    }

    // property agtAgtApeMat
    @Test
    void testGetterSetterAgtAgtApeMat() {

        assertNotNull(agt0.getAgtAgtApeMat());

        String vString = "ABCabc1230";
        agt0.setAgtAgtApeMat(vString); // String
        assertEquals(asStr(vString), asStr(agt0.getAgtAgtApeMat()));
    }

    // property agtAgtNip
    @Test
    void testGetterSetterAgtAgtNip() {

        assertNotNull(agt0.getAgtAgtNip());

        int vint = 1234;
        agt0.setAgtAgtNip(vint); // int
        assertEquals(asInt(vint), asInt(agt0.getAgtAgtNip()));
    }

    // property cmaCmaDes
    @Test
    void testGetterSetterCmaCmaDes() {

        assertNotNull(agt0.getCmaCmaDes());

        String vString = "ABCabc1230";
        agt0.setCmaCmaDes(vString); // String
        assertEquals(asStr(vString), asStr(agt0.getCmaCmaDes()));
    }

    // property wsIndGerente
    @Test
    void testGetterSetterWsIndGerente() {

        assertNotNull(agt0.getWsIndGerente());

        String vString = "ABCabc1";
        agt0.setWsIndGerente(vString); // String
        assertEquals(asStr(vString), asStr(agt0.getWsIndGerente()));
    }

    // property wsOrvCve
    @Test
    void testGetterSetterWsOrvCve() {

        assertNotNull(agt0.getWsOrvCve());

        int vint = 1234;
        agt0.setWsOrvCve(vint); // int
        assertEquals(asInt(vint), asInt(agt0.getWsOrvCve()));
    }

    // property agtRazSoc
    @Test
    void testGetterSetterAgtRazSoc() {

        assertNotNull(agt0.getAgtRazSoc());

        String vString = "ABCabc1230";
        agt0.setAgtRazSoc(vString); // String
        assertEquals(asStr(vString), asStr(agt0.getAgtRazSoc()));
    }

    // property agtPefCve
    @Test
    void testGetterSetterAgtPefCve() {

        assertNotNull(agt0.getAgtPefCve());

        int vint = 1234;
        agt0.setAgtPefCve(vint); // int
        assertEquals(asInt(vint), asInt(agt0.getAgtPefCve()));
    }
}

