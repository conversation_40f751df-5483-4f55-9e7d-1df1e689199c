package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.common.cbl.exception.GobackException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;
import com.base100.caravel.support.common.cbl.context.IProgramCblContext;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa002qcpy.LcevLinkReg;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

class Ggnf0800Test {
    Ggnf0800 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggnf0800.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);

        // Mock program context to break infinite loops
        IProgramCblContext programCblContext = pgm.getProgramCblContext();
        doAnswer(invocation -> {
            String programName = invocation.getArgument(0);
            if (programName.equals("GGNF0800")){
                LcevLinkReg lbdeLinkReg = invocation.getArgument(1);
                lbdeLinkReg.setLcevSwErr("1");
            }
            return null;
        }).when(programCblContext).runProgram(anyString(), any(LcevLinkReg.class));
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        pgm.getSqlca().setSqlcode(-1);
        pgm.setFsFolios(0);

        Exception exception = assertThrows(GobackException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p2000ProcesoGfvtfla0 test conditions:
        fsFolios : [1, 0]
        wkAbriCursor : [1, 2]
    */
    @Test
    void p2000ProcesoGfvtfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setFsFolios(1);
        pgm.setWkAbriCursor(1);
        try {
            pgm.p2000ProcesoGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2000ProcesoGfvtfla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.setFsFolios(0);
            pgm.setWkAbriCursor(2);
            try {
                pgm.p2000ProcesoGfvtfla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3000ProcesoGfvtfdc0 test conditions:
        wkAbriCursor : [1, 2]
        wsFinCaf : [0, 1]
        tbFla03 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla04 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla05 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla11 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla12 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla13 : ["XXLOW_VALUE", "LOW_VALUE"]
        fsFolios : [0, 1]
    */
    @Test
    void p3000ProcesoGfvtfdc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setWkAbriCursor(1);
        pgm.getWsIndicadores().setWsFinCaf(0);
        pgm.getTbDclgfvtfla0().setTbFla03("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("XXLOW_VALUE");
        pgm.setFsFolios(0);
        try {
            pgm.p3000ProcesoGfvtfdc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3000ProcesoGfvtfdc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.setWkAbriCursor(2);
            pgm.getWsIndicadores().setWsFinCaf(1);
            pgm.getTbDclgfvtfla0().setTbFla03("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla04("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla05("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla11("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla12("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla13("LOW_VALUE");
            pgm.setFsFolios(1);
            try {
                pgm.p3000ProcesoGfvtfdc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3100ActualizaOAlta test conditions:
        wsFinCaf : [0, 1]
    */
    @Test
    void p3100ActualizaOAltaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinCaf(0);
        try {
            pgm.p3100ActualizaOAlta();
        } catch (Exception e) {
            assertExceptionInstanceOf(ArrayIndexOutOfBoundsException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3100ActualizaOAltaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsFinCaf(1);
            try {
                pgm.p3100ActualizaOAlta();
            } catch (Exception e) {
                assertExceptionInstanceOf(ArrayIndexOutOfBoundsException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3500AltaGfvtcaf0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p3500AltaGfvtcaf0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p3500AltaGfvtcaf0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3500AltaGfvtcaf0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p3500AltaGfvtcaf0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3550ImpGfvtcaf0 test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p3550ImpGfvtcaf0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p3550ImpGfvtcaf0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3550ImpGfvtcaf0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setContLineas(61);
            try {
                pgm.p3550ImpGfvtcaf0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p6000Imprimir test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p6000ImprimirTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p6000Imprimir();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p6000ImprimirTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setContLineas(61);
            try {
                pgm.p6000Imprimir();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p6100Enc1 test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p6100Enc1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p6100Enc1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p6100Enc1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setContLineas(61);
            try {
                pgm.p6100Enc1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7100LeerGfvtcaf0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7100LeerGfvtcaf0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7100LeerGfvtcaf0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7100LeerGfvtcaf0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7100LeerGfvtcaf0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850InicioGfvcfla0 test conditions:
        fsFolios : [1, 0]
        tbFla03 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla04 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla05 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla11 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla12 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla13 : ["XXLOW_VALUE", "LOW_VALUE"]
    */
    @Test
    void p7850InicioGfvcfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setFsFolios(1);
        pgm.getTbDclgfvtfla0().setTbFla03("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("XXLOW_VALUE");
        try {
            pgm.p7850InicioGfvcfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850InicioGfvcfla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.setFsFolios(0);
            pgm.getTbDclgfvtfla0().setTbFla03("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla04("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla05("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla11("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla12("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla13("LOW_VALUE");
            try {
                pgm.p7850InicioGfvcfla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850LeerGfvtfla0 test conditions:
        tbFla03 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla04 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla05 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla11 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla12 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla13 : ["XXLOW_VALUE", "LOW_VALUE"]
        fsFolios : [0, 1]
    */
    @Test
    void p7850LeerGfvtfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtfla0().setTbFla03("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("XXLOW_VALUE");
        pgm.setFsFolios(0);
        try {
            pgm.p7850LeerGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850LeerGfvtfla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getTbDclgfvtfla0().setTbFla03("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla04("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla05("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla11("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla12("LOW_VALUE");
            pgm.getTbDclgfvtfla0().setTbFla13("LOW_VALUE");
            pgm.setFsFolios(1);
            try {
                pgm.p7850LeerGfvtfla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850InicioGfvcfdc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
        tbdInd : [5001, 5000]
    */
    @Test
    void p7850InicioGfvcfdc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        pgm.setTbdInd(5001);
        try {
            pgm.p7850InicioGfvcfdc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850InicioGfvcfdc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            pgm.setTbdInd(5000);
            try {
                pgm.p7850InicioGfvcfdc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850LeerGfvtfdc0 test conditions:
        tbdInd : [5001, 5000]
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850LeerGfvtfdc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setTbdInd(5001);
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850LeerGfvtfdc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850LeerGfvtfdc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.setTbdInd(5000);
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850LeerGfvtfdc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850CierroGfvcfdc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850CierroGfvcfdc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850CierroGfvcfdc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850CierroGfvcfdc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850CierroGfvcfdc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      db2check test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void db2checkTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.db2check();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void db2checkTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.db2check();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p9999AnalizaSql test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p9999AnalizaSqlTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p9999AnalizaSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p9999AnalizaSqlTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p9999AnalizaSql();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3000Fin test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p3000FinTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p3000Fin();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3000FinTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setContLineas(61);
            try {
                pgm.p3000Fin();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
}
