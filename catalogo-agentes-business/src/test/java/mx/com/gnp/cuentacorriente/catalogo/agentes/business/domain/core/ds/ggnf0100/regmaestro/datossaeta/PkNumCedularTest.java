package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: PK-NUM-CEDULAR.
 *
 */
class PkNumCedularTest {

    private PkNumCedular pkNumCedular;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        pkNumCedular = new PkNumCedular();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        PkNumCedular pkNumCedular2 = new PkNumCedular(parent);
        assertNotNull(pkNumCedular2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 20);
        PkNumCedular pkNumCedular3 = new PkNumCedular(parent, sibling);
        assertNotNull(pkNumCedular3);
    }

    @Test
    void testInitialize() {
        assertNotNull(pkNumCedular.getPkNumCedulaDos());

        assertNotNull(pkNumCedular.getPkNumCedulaR18());

    }

    // property pkNumCedulaDos
    @Test
    void testGetterSetterPkNumCedulaDos() {

        assertNotNull(pkNumCedular.getPkNumCedulaDos());

        String vString = "AB";
        pkNumCedular.setPkNumCedulaDos(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedular.getPkNumCedulaDos()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        pkNumCedular.setPkNumCedulaDos(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedular.getPkNumCedulaDos()));
    }

    // property pkNumCedulaR18
    @Test
    void testGetterSetterPkNumCedulaR18() {

        assertNotNull(pkNumCedular.getPkNumCedulaR18());

        String vString = "ABCabc1230";
        pkNumCedular.setPkNumCedulaR18(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedular.getPkNumCedulaR18()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        pkNumCedular.setPkNumCedulaR18(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedular.getPkNumCedulaR18()));
    }
}

