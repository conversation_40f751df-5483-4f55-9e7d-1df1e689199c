package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaofn0;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-OFN0.
 *
 */
class TOfn0Test {

    private TOfn0 tOfn0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tOfn0 = new TOfn0(1);
        tOfn0.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TOfn0 tOfn02 = new TOfn0(parent, 1);
        assertNotNull(tOfn02);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TOfn0 tOfn03 = new TOfn0(parent, sibling, 1);
        assertNotNull(tOfn03);
    }

    @Test
    void testInitialize() {

    }

    // property tOfn0Elem
    @Test
    void testGetterSetterTOfn0Elem() {

        assertNotNull(tOfn0.getTOfn0Elem());
    }

    // property sCnaOfnCve
    @Test
    void testGetterSetterSCnaOfnCve() {

        // Property: tOfn0Elem.sCnaOfnCve -> 20 S-CNA-OFN-CVE X(04)
        assertNotNull(tOfn0.getSCnaOfnCve());
        String vString = "ABCa";
        tOfn0.setSCnaOfnCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tOfn0.setSCnaOfnCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sOfnLimitad
    @Test
    void testGetterSetterSOfnLimitad() {

        // Property: tOfn0Elem.sOfnLimitad -> 20 S-OFN-LIMITAD X(01)
        assertNotNull(tOfn0.getSOfnLimitad());
        String vString = "A";
        tOfn0.setSOfnLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tOfn0.setSOfnLimitad(vIAlphanumericValue); // IAlphanumericValue
    }
}

