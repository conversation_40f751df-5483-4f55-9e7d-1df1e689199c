package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-INDICADORES.
 *
 */
class WsIndicadoresTest {

    private WsIndicadores wsIndicadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsIndicadores = new WsIndicadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsIndicadores.setWsN(0);
        assertEquals(0, wsIndicadores.getWsN());

        wsIndicadores.setWsP(0);
        assertEquals(0, wsIndicadores.getWsP());

        wsIndicadores.setWsFin(0);
        assertEquals(0, wsIndicadores.getWsFin());

        wsIndicadores.setWsFinAgt(0);
        assertEquals(0, wsIndicadores.getWsFinAgt());

        wsIndicadores.setWsFinPra(0);
        assertEquals(0, wsIndicadores.getWsFinPra());

        wsIndicadores.setWsFinDma(0);
        assertEquals(0, wsIndicadores.getWsFinDma());

        wsIndicadores.setWsFinDmz(0);
        assertEquals(0, wsIndicadores.getWsFinDmz());

        wsIndicadores.setWsFinPob(0);
        assertEquals(0, wsIndicadores.getWsFinPob());

        wsIndicadores.setWsFinMnd(0);
        assertEquals(0, wsIndicadores.getWsFinMnd());

        wsIndicadores.setWsFinEdo(0);
        assertEquals(0, wsIndicadores.getWsFinEdo());

        wsIndicadores.setWsFinCma(0);
        assertEquals(0, wsIndicadores.getWsFinCma());

        wsIndicadores.setWsFinMte(0);
        assertEquals(0, wsIndicadores.getWsFinMte());

        wsIndicadores.setWsFinOrv(0);
        assertEquals(0, wsIndicadores.getWsFinOrv());

        wsIndicadores.setWsFinDrc(0);
        assertEquals(0, wsIndicadores.getWsFinDrc());

        wsIndicadores.setWsFinCna(0);
        assertEquals(0, wsIndicadores.getWsFinCna());

        wsIndicadores.setWsFinCna1(0);
        assertEquals(0, wsIndicadores.getWsFinCna1());

        wsIndicadores.setWsFinFla(0);
        assertEquals(0, wsIndicadores.getWsFinFla());

        wsIndicadores.setWsFinFla1(0);
        assertEquals(0, wsIndicadores.getWsFinFla1());

        wsIndicadores.setWsFinFla2(0);
        assertEquals(0, wsIndicadores.getWsFinFla2());

        wsIndicadores.setWsFinFla3(0);
        assertEquals(0, wsIndicadores.getWsFinFla3());

        wsIndicadores.setWsIndNal(0);
        assertEquals(0, wsIndicadores.getWsIndNal());

        wsIndicadores.setWsIndProv(0);
        assertEquals(0, wsIndicadores.getWsIndProv());

        wsIndicadores.setWsFinAfo(0);
        assertEquals(0, wsIndicadores.getWsFinAfo());

        wsIndicadores.setWsIndExiste(0);
        assertEquals(0, wsIndicadores.getWsIndExiste());

    }

    // property wsN
    @Test
    void testGetterSetterWsN() {

        assertNotNull(wsIndicadores.getWsN());

        int vint = 1;
        wsIndicadores.setWsN(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsN()));
    }

    // property wsP
    @Test
    void testGetterSetterWsP() {

        assertNotNull(wsIndicadores.getWsP());

        int vint = 1;
        wsIndicadores.setWsP(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsP()));
    }

    // property wsFin
    @Test
    void testGetterSetterWsFin() {

        assertNotNull(wsIndicadores.getWsFin());

        int vint = 1;
        wsIndicadores.setWsFin(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFin()));
    }

    // property wsFinAgt
    @Test
    void testGetterSetterWsFinAgt() {

        assertNotNull(wsIndicadores.getWsFinAgt());

        int vint = 1;
        wsIndicadores.setWsFinAgt(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinAgt()));
    }

    // property wsFinPra
    @Test
    void testGetterSetterWsFinPra() {

        assertNotNull(wsIndicadores.getWsFinPra());

        int vint = 1;
        wsIndicadores.setWsFinPra(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinPra()));
    }

    // property wsFinDma
    @Test
    void testGetterSetterWsFinDma() {

        assertNotNull(wsIndicadores.getWsFinDma());

        int vint = 1;
        wsIndicadores.setWsFinDma(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinDma()));
    }

    // property wsFinDmz
    @Test
    void testGetterSetterWsFinDmz() {

        assertNotNull(wsIndicadores.getWsFinDmz());

        int vint = 1;
        wsIndicadores.setWsFinDmz(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinDmz()));
    }

    // property wsFinPob
    @Test
    void testGetterSetterWsFinPob() {

        assertNotNull(wsIndicadores.getWsFinPob());

        int vint = 1;
        wsIndicadores.setWsFinPob(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinPob()));
    }

    // property wsFinMnd
    @Test
    void testGetterSetterWsFinMnd() {

        assertNotNull(wsIndicadores.getWsFinMnd());

        int vint = 1;
        wsIndicadores.setWsFinMnd(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinMnd()));
    }

    // property wsFinEdo
    @Test
    void testGetterSetterWsFinEdo() {

        assertNotNull(wsIndicadores.getWsFinEdo());

        int vint = 1;
        wsIndicadores.setWsFinEdo(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinEdo()));
    }

    // property wsFinCma
    @Test
    void testGetterSetterWsFinCma() {

        assertNotNull(wsIndicadores.getWsFinCma());

        int vint = 1;
        wsIndicadores.setWsFinCma(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinCma()));
    }

    // property wsFinMte
    @Test
    void testGetterSetterWsFinMte() {

        assertNotNull(wsIndicadores.getWsFinMte());

        int vint = 1;
        wsIndicadores.setWsFinMte(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinMte()));
    }

    // property wsFinOrv
    @Test
    void testGetterSetterWsFinOrv() {

        assertNotNull(wsIndicadores.getWsFinOrv());

        int vint = 1;
        wsIndicadores.setWsFinOrv(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinOrv()));
    }

    // property wsFinDrc
    @Test
    void testGetterSetterWsFinDrc() {

        assertNotNull(wsIndicadores.getWsFinDrc());

        int vint = 1;
        wsIndicadores.setWsFinDrc(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinDrc()));
    }

    // property wsFinCna
    @Test
    void testGetterSetterWsFinCna() {

        assertNotNull(wsIndicadores.getWsFinCna());

        int vint = 1;
        wsIndicadores.setWsFinCna(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinCna()));
    }

    // property wsFinCna1
    @Test
    void testGetterSetterWsFinCna1() {

        assertNotNull(wsIndicadores.getWsFinCna1());

        int vint = 1;
        wsIndicadores.setWsFinCna1(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinCna1()));
    }

    // property wsFinFla
    @Test
    void testGetterSetterWsFinFla() {

        assertNotNull(wsIndicadores.getWsFinFla());

        int vint = 1;
        wsIndicadores.setWsFinFla(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFla()));
    }

    // property wsFinFla1
    @Test
    void testGetterSetterWsFinFla1() {

        assertNotNull(wsIndicadores.getWsFinFla1());

        int vint = 1;
        wsIndicadores.setWsFinFla1(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFla1()));
    }

    // property wsFinFla2
    @Test
    void testGetterSetterWsFinFla2() {

        assertNotNull(wsIndicadores.getWsFinFla2());

        int vint = 1;
        wsIndicadores.setWsFinFla2(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFla2()));
    }

    // property wsFinFla3
    @Test
    void testGetterSetterWsFinFla3() {

        assertNotNull(wsIndicadores.getWsFinFla3());

        int vint = 1;
        wsIndicadores.setWsFinFla3(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFla3()));
    }

    // property wsIndNal
    @Test
    void testGetterSetterWsIndNal() {

        assertNotNull(wsIndicadores.getWsIndNal());

        int vint = 1;
        wsIndicadores.setWsIndNal(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsIndNal()));
    }

    // property wsIndProv
    @Test
    void testGetterSetterWsIndProv() {

        assertNotNull(wsIndicadores.getWsIndProv());

        int vint = 1;
        wsIndicadores.setWsIndProv(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsIndProv()));
    }

    // property wsFinAfo
    @Test
    void testGetterSetterWsFinAfo() {

        assertNotNull(wsIndicadores.getWsFinAfo());

        int vint = 1;
        wsIndicadores.setWsFinAfo(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinAfo()));
    }

    // property wsIndExiste
    @Test
    void testGetterSetterWsIndExiste() {

        assertNotNull(wsIndicadores.getWsIndExiste());

        int vint = 1;
        wsIndicadores.setWsIndExiste(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsIndExiste()));
    }
}

