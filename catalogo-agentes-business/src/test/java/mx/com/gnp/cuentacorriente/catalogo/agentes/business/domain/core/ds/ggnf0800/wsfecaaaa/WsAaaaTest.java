package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.wsfecaaaa;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AAAA.
 *
 */
class WsAaaaTest {

    private WsAaaa wsAaaa;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAaaa = new WsAaaa();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAaaa wsAaaa2 = new WsAaaa(parent);
        assertNotNull(wsAaaa2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 4);
        WsAaaa wsAaaa3 = new WsAaaa(parent, sibling);
        assertNotNull(wsAaaa3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAaaa.getWsAa1());

        assertNotNull(wsAaaa.getWsAa2());

    }

    // property wsAa1
    @Test
    void testGetterSetterWsAa1() {

        assertNotNull(wsAaaa.getWsAa1());

        int vint = 12;
        wsAaaa.setWsAa1(vint); // int
        assertEquals(asInt(vint), asInt(wsAaaa.getWsAa1()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAaaa.setWsAa1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAaaa.getWsAa1()));
    }

    // property wsAa2
    @Test
    void testGetterSetterWsAa2() {

        assertNotNull(wsAaaa.getWsAa2());

        int vint = 12;
        wsAaaa.setWsAa2(vint); // int
        assertEquals(asInt(vint), asInt(wsAaaa.getWsAa2()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAaaa.setWsAa2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAaaa.getWsAa2()));
    }
}

