package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TIT31.
 *
 */
class Tit31Test {

    private Tit31 tit31;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tit31 = new Tit31();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Tit31 tit312 = new Tit31(parent);
        assertNotNull(tit312);
        AlphanumericVar sibling = new AlphanumericVar(parent, 132);
        Tit31 tit313 = new Tit31(parent, sibling);
        assertNotNull(tit313);
    }

    @Test
    void testInitialize() {
    }
}

