package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GCCTORV0-NULLR.
 *
 */
class WsGcctorv0NullrTest {

    private WsGcctorv0Nullr wsGcctorv0Nullr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGcctorv0Nullr = new WsGcctorv0Nullr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsGcctorv0Nullr wsGcctorv0Nullr2 = new WsGcctorv0Nullr(parent);
        assertNotNull(wsGcctorv0Nullr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 36);
        WsGcctorv0Nullr wsGcctorv0Nullr3 = new WsGcctorv0Nullr(parent, sibling);
        assertNotNull(wsGcctorv0Nullr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsGcctorv0Nullr.getWsGcctorv0Ele());

    }

    // property wsGcctorv0Ele
    @Test
    void testGetterSetterWsGcctorv0Ele() {

        assertNotNull(wsGcctorv0Nullr.getWsGcctorv0Ele());
        assertNotNull(wsGcctorv0Nullr.getWsGcctorv0Ele(1));

        int vint = 1234;
        wsGcctorv0Nullr.getWsGcctorv0Ele().at(1).setValue(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Nullr.getWsGcctorv0Ele(1)));

        NumericVar vNumericVar = new NumericVar(4, 0);;
        vNumericVar.setValue("1234");
        wsGcctorv0Nullr.getWsGcctorv0Ele().at(1).setValue(vNumericVar); // NumericVar
        assertEquals(asStr(vNumericVar), asStr(wsGcctorv0Nullr.getWsGcctorv0Ele(1)));
    }
}

