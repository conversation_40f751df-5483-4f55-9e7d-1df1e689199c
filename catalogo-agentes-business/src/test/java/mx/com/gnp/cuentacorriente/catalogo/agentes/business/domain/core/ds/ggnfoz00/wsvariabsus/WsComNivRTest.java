package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-COM-NIV-R.
 *
 */
class WsComNivRTest {

    private WsComNivR wsComNivR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsComNivR = new WsComNivR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsComNivR wsComNivR2 = new WsComNivR(parent);
        assertNotNull(wsComNivR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 13);
        WsComNivR wsComNivR3 = new WsComNivR(parent, sibling);
        assertNotNull(wsComNivR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsComNivR.getRsComNiv11());

        assertNotNull(wsComNivR.getRsComNiv2());

    }

    // property rsComNiv11
    @Test
    void testGetterSetterRsComNiv11() {

        assertNotNull(wsComNivR.getRsComNiv11());

        String vString = "ABCabc1230";
        wsComNivR.setRsComNiv11(vString); // String
        assertEquals(asStr(vString), asStr(wsComNivR.getRsComNiv11()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsComNivR.setRsComNiv11(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsComNivR.getRsComNiv11()));
    }

    // property rsComNiv2
    @Test
    void testGetterSetterRsComNiv2() {

        assertNotNull(wsComNivR.getRsComNiv2());

        String vString = "AB";
        wsComNivR.setRsComNiv2(vString); // String
        assertEquals(asStr(vString), asStr(wsComNivR.getRsComNiv2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsComNivR.setRsComNiv2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsComNivR.getRsComNiv2()));
    }
}

