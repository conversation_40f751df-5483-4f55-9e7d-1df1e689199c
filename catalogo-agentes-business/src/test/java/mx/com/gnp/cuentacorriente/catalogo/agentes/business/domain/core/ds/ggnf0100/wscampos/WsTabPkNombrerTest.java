package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TAB-PK-NOMBRER.
 *
 */
class WsTabPkNombrerTest {

    private WsTabPkNombrer wsTabPkNombrer;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTabPkNombrer = new WsTabPkNombrer();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsTabPkNombrer wsTabPkNombrer2 = new WsTabPkNombrer(parent);
        assertNotNull(wsTabPkNombrer2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 35);
        WsTabPkNombrer wsTabPkNombrer3 = new WsTabPkNombrer(parent, sibling);
        assertNotNull(wsTabPkNombrer3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsTabPkNombrer.getElePkNombre());

    }

    // property elePkNombre
    @Test
    void testGetterSetterElePkNombre() {

        assertNotNull(wsTabPkNombrer.getElePkNombre());
        assertNotNull(wsTabPkNombrer.getElePkNombre(1));

        String vString = "A";
        wsTabPkNombrer.getElePkNombre().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsTabPkNombrer.getElePkNombre(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsTabPkNombrer.getElePkNombre().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsTabPkNombrer.getElePkNombre(1)));
    }
}

