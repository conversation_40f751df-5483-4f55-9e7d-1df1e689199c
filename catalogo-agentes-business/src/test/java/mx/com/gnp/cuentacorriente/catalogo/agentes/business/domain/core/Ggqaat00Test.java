package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

class Ggqaat00Test {
    Ggqaat00 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggqaat00.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      testRun test conditions:
        plhcMktLevel : ["GM", "XXGM"]
        wsFlaNum_1 : ["N", "XXN"]
    */
    @Test
    void testRun1() {
        // Set test conditions
        pgm.getWsInterfaz().setPlhcMktLevel("GM");
        pgm.getWsAyuda().setWsFlaNum_1("N");

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    @Test
    void testRun2() {
        // Set test conditions
        pgm.getWsInterfaz().setPlhcMktLevel("XXGM");
        pgm.getWsAyuda().setWsFlaNum_1("XXN");

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p0000IniciaProceso test conditions:
        plhcMktLevel : ["GM", "XXGM"]
        wsFlaNum_1 : ["N", "XXN"]
    */
    @Test
    void p0000IniciaProcesoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsInterfaz().setPlhcMktLevel("GM");
        pgm.getWsAyuda().setWsFlaNum_1("N");
        try {
            pgm.p0000IniciaProceso();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p0000IniciaProcesoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsInterfaz().setPlhcMktLevel("XXGM");
            pgm.getWsAyuda().setWsFlaNum_1("XXN");
            try {
                pgm.p0000IniciaProceso();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p1000ProcesaAgt test conditions:
        plhcMktLevel : ["GM", "XXGM"]
        wsFlaNum_1 : ["N", "XXN"]
        tdmCve : [1, 2]
        wsDmaIdr : [5, 4]
        cnaIndGer : ["S", "XXS"]
        wcC2 : [31, 30]
        agtApePat : ["XX", ""]
        wcC4 : [14, 13]
        flaDes : ["", "XX"]
        plhcMktNameLasta : ["", "XX"]
        pefCve : [1, 2, 3]
    */
    @Test
    void p1000ProcesaAgtTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsInterfaz().setPlhcMktLevel("GM");
        pgm.getWsAyuda().setWsFlaNum_1("N");
        pgm.getGfvtdma0().setTdmCve(1);
        pgm.getWsAyuda().setWsDmaIdr(5);
        pgm.getDclgfvtagc0().setCnaIndGer("S");
        pgm.getWsContadores().setWcC2(31);
        pgm.getDclgfvtagt0().setAgtApePat("XX");
        pgm.getWsContadores().setWcC4(14);
        pgm.getDclgfvtfla0().setFlaDes("");
        pgm.getWsInterfaz().setPlhcMktNameLasta("");
        pgm.getDclgfvtagt0().setPefCve(1);
        try {
            pgm.p1000ProcesaAgt();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1000ProcesaAgtTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsInterfaz().setPlhcMktLevel("XXGM");
            pgm.getWsAyuda().setWsFlaNum_1("XXN");
            pgm.getGfvtdma0().setTdmCve(2);
            pgm.getWsAyuda().setWsDmaIdr(4);
            pgm.getDclgfvtagc0().setCnaIndGer("XXS");
            pgm.getWsContadores().setWcC2(30);
            pgm.getDclgfvtagt0().setAgtApePat("");
            pgm.getWsContadores().setWcC4(13);
            pgm.getDclgfvtfla0().setFlaDes("XX");
            pgm.getWsInterfaz().setPlhcMktNameLasta("XX");
            pgm.getDclgfvtagt0().setPefCve(2);
            try {
                pgm.p1000ProcesaAgt();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p1000ProcesaAgtTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getWsInterfaz().setPlhcMktLevel("XXGM");
                pgm.getWsAyuda().setWsFlaNum_1("XXN");
                pgm.getGfvtdma0().setTdmCve(2);
                pgm.getWsAyuda().setWsDmaIdr(4);
                pgm.getDclgfvtagc0().setCnaIndGer("XXS");
                pgm.getWsContadores().setWcC2(30);
                pgm.getDclgfvtagt0().setAgtApePat("");
                pgm.getWsContadores().setWcC4(13);
                pgm.getDclgfvtfla0().setFlaDes("XX");
                pgm.getWsInterfaz().setPlhcMktNameLasta("XX");
                pgm.getDclgfvtagt0().setPefCve(3);
                try {
                    pgm.p1000ProcesaAgt();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
    /*
      p1050ConcatenaApellidos test conditions:
        wcC2 : [31, 30]
        agtApePat : ["XX", ""]
    */
    @Test
    void p1050ConcatenaApellidosTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsContadores().setWcC2(31);
        pgm.getDclgfvtagt0().setAgtApePat("XX");
        try {
            pgm.p1050ConcatenaApellidos();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1050ConcatenaApellidosTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsContadores().setWcC2(30);
            pgm.getDclgfvtagt0().setAgtApePat("");
            try {
                pgm.p1050ConcatenaApellidos();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p1060EditaTelefono test conditions:
        wcC4 : [14, 13]
    */
    @Test
    void p1060EditaTelefonoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsContadores().setWcC4(14);
        try {
            pgm.p1060EditaTelefono();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1060EditaTelefonoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsContadores().setWcC4(13);
            try {
                pgm.p1060EditaTelefono();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p1100ArmaSalidaAgt test conditions:
        flaDes : ["", "XX"]
        plhcMktNameLasta : ["", "XX"]
        pefCve : [1, 2, 3]
    */
    @Test
    void p1100ArmaSalidaAgtTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtfla0().setFlaDes("");
        pgm.getWsInterfaz().setPlhcMktNameLasta("");
        pgm.getDclgfvtagt0().setPefCve(1);
        try {
            pgm.p1100ArmaSalidaAgt();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1100ArmaSalidaAgtTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getDclgfvtfla0().setFlaDes("XX");
            pgm.getWsInterfaz().setPlhcMktNameLasta("XX");
            pgm.getDclgfvtagt0().setPefCve(2);
            try {
                pgm.p1100ArmaSalidaAgt();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p1100ArmaSalidaAgtTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getDclgfvtfla0().setFlaDes("XX");
                pgm.getWsInterfaz().setPlhcMktNameLasta("XX");
                pgm.getDclgfvtagt0().setPefCve(3);
                try {
                    pgm.p1100ArmaSalidaAgt();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
    /*
      p5100ConsultaDma0 test conditions:
        tdmCve : [1, 2]
        wsDmaIdr : [5, 4]
        sqlcode : [0, 1]
    */
    @Test
    void p5100ConsultaDma0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getGfvtdma0().setTdmCve(1);
        pgm.getWsAyuda().setWsDmaIdr(5);
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5100ConsultaDma0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5100ConsultaDma0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getGfvtdma0().setTdmCve(2);
            pgm.getWsAyuda().setWsDmaIdr(4);
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5100ConsultaDma0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p5100ConsultaAgc0 test conditions:
        cnaIndGer : ["S", "XXS"]
        cnaIndGer : ["S", "XXS"]
    */
    @Test
    void p5100ConsultaAgc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtagc0().setCnaIndGer("S");
        pgm.getDclgfvtcna0().setCnaIndGer("S");
        try {
            pgm.p5100ConsultaAgc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5100ConsultaAgc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getDclgfvtagc0().setCnaIndGer("XXS");
            pgm.getDclgfvtcna0().setCnaIndGer("XXS");
            try {
                pgm.p5100ConsultaAgc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p5100ConsultaCna0 test conditions:
        cnaIndGer : ["S", "XXS"]
    */
    @Test
    void p5100ConsultaCna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtcna0().setCnaIndGer("S");
        try {
            pgm.p5100ConsultaCna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5100ConsultaCna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getDclgfvtcna0().setCnaIndGer("XXS");
            try {
                pgm.p5100ConsultaCna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7000Inicio test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p7000InicioTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p7000Inicio();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7000InicioTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p7000Inicio();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7200AbreCursorFla test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p7200AbreCursorFlaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p7200AbreCursorFla();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7200AbreCursorFlaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p7200AbreCursorFla();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7201AbreCursorDma test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p7201AbreCursorDmaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p7201AbreCursorDma();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7201AbreCursorDmaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p7201AbreCursorDma();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7202AbreCursorOfn test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p7202AbreCursorOfnTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p7202AbreCursorOfn();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7202AbreCursorOfnTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p7202AbreCursorOfn();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7203AbreCursorCeo test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p7203AbreCursorCeoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p7203AbreCursorCeo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7203AbreCursorCeoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p7203AbreCursorCeo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p8000Termina test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p8000TerminaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p8000Termina();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p8000TerminaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p8000Termina();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p8200CierraCursorFla test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p8200CierraCursorFlaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p8200CierraCursorFla();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p8200CierraCursorFlaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p8200CierraCursorFla();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p8201CierraCursorDma test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p8201CierraCursorDmaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p8201CierraCursorDma();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p8201CierraCursorDmaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p8201CierraCursorDma();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p8202CierraCursorOfn test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p8202CierraCursorOfnTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p8202CierraCursorOfn();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p8202CierraCursorOfnTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p8202CierraCursorOfn();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p8203CierraCursorCeo test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void p8203CierraCursorCeoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p8203CierraCursorCeo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p8203CierraCursorCeoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p8203CierraCursorCeo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
}
