package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: I-INDICES.
 *
 */
class IIndicesTest {

    private IIndices iIndices;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        iIndices = new IIndices();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertTrue(iIndices.getI().isZero());

    }

    // property i
    @Test
    void testGetterSetterI() {

        assertNotNull(iIndices.getI());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        iIndices.setI(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, iIndices.getI().toBigDecimal());

        int vint = 123456789;
        iIndices.setI(vint); // int
        assertEquals(asInt(vint), asInt(iIndices.getI()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        iIndices.setI(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(iIndices.getI()));
    }
}

