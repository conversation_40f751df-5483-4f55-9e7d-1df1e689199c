package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tceo0;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-CEO0-ELEM.
 *
 */
class TCeo0ElemTest {

    private TCeo0Elem tCeo0Elem;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tCeo0Elem = new TCeo0Elem();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TCeo0Elem tCeo0Elem2 = new TCeo0Elem(parent);
        assertNotNull(tCeo0Elem2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 31);
        TCeo0Elem tCeo0Elem3 = new TCeo0Elem(parent, sibling);
        assertNotNull(tCeo0Elem3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tCeo0Elem.getSCeoCeoNom());

        assertNotNull(tCeo0Elem.getSCeoLimitad());

    }

    // property sCeoCeoNom
    @Test
    void testGetterSetterSCeoCeoNom() {

        assertNotNull(tCeo0Elem.getSCeoCeoNom());

        String vString = "ABCabc1230";
        tCeo0Elem.setSCeoCeoNom(vString); // String
        assertEquals(asStr(vString), asStr(tCeo0Elem.getSCeoCeoNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tCeo0Elem.setSCeoCeoNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tCeo0Elem.getSCeoCeoNom()));
    }

    // property sCeoLimitad
    @Test
    void testGetterSetterSCeoLimitad() {

        assertNotNull(tCeo0Elem.getSCeoLimitad());

        String vString = "A";
        tCeo0Elem.setSCeoLimitad(vString); // String
        assertEquals(asStr(vString), asStr(tCeo0Elem.getSCeoLimitad()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tCeo0Elem.setSCeoLimitad(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tCeo0Elem.getSCeoLimitad()));
    }
}

