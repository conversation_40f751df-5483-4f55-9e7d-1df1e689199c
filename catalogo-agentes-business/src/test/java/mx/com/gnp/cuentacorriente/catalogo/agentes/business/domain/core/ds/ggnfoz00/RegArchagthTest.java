package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-ARCHAGTH.
 *
 */
class RegArchagthTest {

    private RegArchagth regArchagth;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regArchagth = new RegArchagth();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegArchagth regArchagth2 = new RegArchagth(parent);
        assertNotNull(regArchagth2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 479);
        RegArchagth regArchagth3 = new RegArchagth(parent, sibling);
        assertNotNull(regArchagth3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regArchagth.getAgtIdr1());

        assertNotNull(regArchagth.getAgtFecCamRazSo1());

        assertNotNull(regArchagth.getAgtRfc1());

        assertNotNull(regArchagth.getAgtNom1());

        assertNotNull(regArchagth.getAgtApePat1());

        assertNotNull(regArchagth.getAgtApeMat1());

        assertNotNull(regArchagth.getAgtFecNac1());

        assertNotNull(regArchagth.getAgtNumCed1());

        assertNotNull(regArchagth.getAgtFecIniCed1());

        assertNotNull(regArchagth.getAgtFecFinCed1());

        assertNotNull(regArchagth.getAgtExpSeg1());

        assertNotNull(regArchagth.getAgtTraEsp1());

        assertNotNull(regArchagth.getAgtClfLmr1());

        assertNotNull(regArchagth.getFecUltAct1());

        assertNotNull(regArchagth.getAgtRazSoc1());

        assertNotNull(regArchagth.getNalCve1());

        assertNotNull(regArchagth.getPefCve1());

        assertNotNull(regArchagth.getTcaCve1());

        assertNotNull(regArchagth.getMteCve1());

        assertNotNull(regArchagth.getAgtNumFolInv1());

        assertNotNull(regArchagth.getAgtNomExi1());

        assertNotNull(regArchagth.getAgtRstInv1());

        assertNotNull(regArchagth.getAgtFecIniInv1());

        assertNotNull(regArchagth.getAgtFecFinInv1());

        assertNotNull(regArchagth.getAgtNomDphInv1());

        assertNotNull(regArchagth.getAgtFecMotEss1());

        assertNotNull(regArchagth.getAgtFecMotAsgIn1());

        assertNotNull(regArchagth.getAgtObs1());

        assertNotNull(regArchagth.getEssRek1());

    }

    // property agtIdr1
    @Test
    void testGetterSetterAgtIdr1() {

        assertNotNull(regArchagth.getAgtIdr1());

        int vint = 123456789;
        regArchagth.setAgtIdr1(vint); // int
        assertEquals(asInt(vint), asInt(regArchagth.getAgtIdr1()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchagth.setAgtIdr1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchagth.getAgtIdr1()));
    }

    // property agtFecCamRazSo1
    @Test
    void testGetterSetterAgtFecCamRazSo1() {

        assertNotNull(regArchagth.getAgtFecCamRazSo1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecCamRazSo1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecCamRazSo1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecCamRazSo1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecCamRazSo1()));
    }

    // property agtRfc1
    @Test
    void testGetterSetterAgtRfc1() {

        assertNotNull(regArchagth.getAgtRfc1());

        String vString = "ABCabc1230";
        regArchagth.setAgtRfc1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtRfc1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtRfc1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtRfc1()));
    }

    // property agtNom1
    @Test
    void testGetterSetterAgtNom1() {

        assertNotNull(regArchagth.getAgtNom1());

        String vString = "ABCabc1230";
        regArchagth.setAgtNom1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtNom1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtNom1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtNom1()));
    }

    // property agtApePat1
    @Test
    void testGetterSetterAgtApePat1() {

        assertNotNull(regArchagth.getAgtApePat1());

        String vString = "ABCabc1230";
        regArchagth.setAgtApePat1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtApePat1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtApePat1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtApePat1()));
    }

    // property agtApeMat1
    @Test
    void testGetterSetterAgtApeMat1() {

        assertNotNull(regArchagth.getAgtApeMat1());

        String vString = "ABCabc1230";
        regArchagth.setAgtApeMat1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtApeMat1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtApeMat1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtApeMat1()));
    }

    // property agtFecNac1
    @Test
    void testGetterSetterAgtFecNac1() {

        assertNotNull(regArchagth.getAgtFecNac1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecNac1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecNac1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecNac1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecNac1()));
    }

    // property agtNumCed1
    @Test
    void testGetterSetterAgtNumCed1() {

        assertNotNull(regArchagth.getAgtNumCed1());

        String vString = "ABCabc1230";
        regArchagth.setAgtNumCed1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtNumCed1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtNumCed1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtNumCed1()));
    }

    // property agtFecIniCed1
    @Test
    void testGetterSetterAgtFecIniCed1() {

        assertNotNull(regArchagth.getAgtFecIniCed1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecIniCed1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecIniCed1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecIniCed1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecIniCed1()));
    }

    // property agtFecFinCed1
    @Test
    void testGetterSetterAgtFecFinCed1() {

        assertNotNull(regArchagth.getAgtFecFinCed1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecFinCed1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecFinCed1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecFinCed1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecFinCed1()));
    }

    // property agtExpSeg1
    @Test
    void testGetterSetterAgtExpSeg1() {

        assertNotNull(regArchagth.getAgtExpSeg1());

        String vString = "A";
        regArchagth.setAgtExpSeg1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtExpSeg1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchagth.setAgtExpSeg1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtExpSeg1()));
    }

    // property agtTraEsp1
    @Test
    void testGetterSetterAgtTraEsp1() {

        assertNotNull(regArchagth.getAgtTraEsp1());

        String vString = "A";
        regArchagth.setAgtTraEsp1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtTraEsp1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchagth.setAgtTraEsp1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtTraEsp1()));
    }

    // property agtClfLmr1
    @Test
    void testGetterSetterAgtClfLmr1() {

        assertNotNull(regArchagth.getAgtClfLmr1());

        int vint = 12345;
        regArchagth.setAgtClfLmr1(vint); // int
        assertEquals(asInt(vint), asInt(regArchagth.getAgtClfLmr1()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchagth.setAgtClfLmr1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchagth.getAgtClfLmr1()));
    }

    // property fecUltAct1
    @Test
    void testGetterSetterFecUltAct1() {

        assertNotNull(regArchagth.getFecUltAct1());

        String vString = "ABCabc1230";
        regArchagth.setFecUltAct1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getFecUltAct1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setFecUltAct1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getFecUltAct1()));
    }

    // property agtRazSoc1
    @Test
    void testGetterSetterAgtRazSoc1() {

        assertNotNull(regArchagth.getAgtRazSoc1());

        String vString = "ABCabc1230";
        regArchagth.setAgtRazSoc1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtRazSoc1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtRazSoc1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtRazSoc1()));
    }

    // property nalCve1
    @Test
    void testGetterSetterNalCve1() {

        assertNotNull(regArchagth.getNalCve1());

        String vString = "ABC";
        regArchagth.setNalCve1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getNalCve1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regArchagth.setNalCve1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getNalCve1()));
    }

    // property pefCve1
    @Test
    void testGetterSetterPefCve1() {

        assertNotNull(regArchagth.getPefCve1());

        int vint = 12345;
        regArchagth.setPefCve1(vint); // int
        assertEquals(asInt(vint), asInt(regArchagth.getPefCve1()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchagth.setPefCve1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchagth.getPefCve1()));
    }

    // property tcaCve1
    @Test
    void testGetterSetterTcaCve1() {

        assertNotNull(regArchagth.getTcaCve1());

        String vString = "AB";
        regArchagth.setTcaCve1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getTcaCve1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regArchagth.setTcaCve1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getTcaCve1()));
    }

    // property mteCve1
    @Test
    void testGetterSetterMteCve1() {

        assertNotNull(regArchagth.getMteCve1());

        int vint = 12345;
        regArchagth.setMteCve1(vint); // int
        assertEquals(asInt(vint), asInt(regArchagth.getMteCve1()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchagth.setMteCve1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchagth.getMteCve1()));
    }

    // property agtNumFolInv1
    @Test
    void testGetterSetterAgtNumFolInv1() {

        assertNotNull(regArchagth.getAgtNumFolInv1());

        int vint = 123456789;
        regArchagth.setAgtNumFolInv1(vint); // int
        assertEquals(asInt(vint), asInt(regArchagth.getAgtNumFolInv1()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchagth.setAgtNumFolInv1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchagth.getAgtNumFolInv1()));
    }

    // property agtNomExi1
    @Test
    void testGetterSetterAgtNomExi1() {

        assertNotNull(regArchagth.getAgtNomExi1());

        String vString = "ABCabc1230";
        regArchagth.setAgtNomExi1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtNomExi1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtNomExi1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtNomExi1()));
    }

    // property agtRstInv1
    @Test
    void testGetterSetterAgtRstInv1() {

        assertNotNull(regArchagth.getAgtRstInv1());

        String vString = "ABCab";
        regArchagth.setAgtRstInv1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtRstInv1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regArchagth.setAgtRstInv1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtRstInv1()));
    }

    // property agtFecIniInv1
    @Test
    void testGetterSetterAgtFecIniInv1() {

        assertNotNull(regArchagth.getAgtFecIniInv1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecIniInv1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecIniInv1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecIniInv1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecIniInv1()));
    }

    // property agtFecFinInv1
    @Test
    void testGetterSetterAgtFecFinInv1() {

        assertNotNull(regArchagth.getAgtFecFinInv1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecFinInv1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecFinInv1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecFinInv1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecFinInv1()));
    }

    // property agtNomDphInv1
    @Test
    void testGetterSetterAgtNomDphInv1() {

        assertNotNull(regArchagth.getAgtNomDphInv1());

        String vString = "ABCabc1230";
        regArchagth.setAgtNomDphInv1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtNomDphInv1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtNomDphInv1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtNomDphInv1()));
    }

    // property agtFecMotEss1
    @Test
    void testGetterSetterAgtFecMotEss1() {

        assertNotNull(regArchagth.getAgtFecMotEss1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecMotEss1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecMotEss1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecMotEss1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecMotEss1()));
    }

    // property agtFecMotAsgIn1
    @Test
    void testGetterSetterAgtFecMotAsgIn1() {

        assertNotNull(regArchagth.getAgtFecMotAsgIn1());

        String vString = "ABCabc1230";
        regArchagth.setAgtFecMotAsgIn1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtFecMotAsgIn1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtFecMotAsgIn1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtFecMotAsgIn1()));
    }

    // property agtObs1
    @Test
    void testGetterSetterAgtObs1() {

        assertNotNull(regArchagth.getAgtObs1());

        String vString = "ABCabc1230";
        regArchagth.setAgtObs1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getAgtObs1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchagth.setAgtObs1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getAgtObs1()));
    }

    // property essRek1
    @Test
    void testGetterSetterEssRek1() {

        assertNotNull(regArchagth.getEssRek1());

        String vString = "A";
        regArchagth.setEssRek1(vString); // String
        assertEquals(asStr(vString), asStr(regArchagth.getEssRek1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchagth.setEssRek1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchagth.getEssRek1()));
    }
}

