package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FOLIOZZ.
 *
 */
class WsFoliozzTest {

    private WsFoliozz wsFoliozz;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFoliozz = new WsFoliozz();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFoliozz wsFoliozz2 = new WsFoliozz(parent);
        assertNotNull(wsFoliozz2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFoliozz wsFoliozz3 = new WsFoliozz(parent, sibling);
        assertNotNull(wsFoliozz3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFoliozz.getWsFoliozz1());

        assertNotNull(wsFoliozz.getWsFoliozz2());

    }

    // property wsFoliozz1
    @Test
    void testGetterSetterWsFoliozz1() {

        assertNotNull(wsFoliozz.getWsFoliozz1());

        String vString = "A";
        wsFoliozz.setWsFoliozz1(vString); // String
        assertEquals(asStr(vString), asStr(wsFoliozz.getWsFoliozz1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFoliozz.setWsFoliozz1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFoliozz.getWsFoliozz1()));
    }

    // property wsFoliozz2
    @Test
    void testGetterSetterWsFoliozz2() {

        assertNotNull(wsFoliozz.getWsFoliozz2());

        int vint = 1234567;
        wsFoliozz.setWsFoliozz2(vint); // int
        assertEquals(asInt(vint), asInt(wsFoliozz.getWsFoliozz2()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsFoliozz.setWsFoliozz2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFoliozz.getWsFoliozz2()));
    }
}

