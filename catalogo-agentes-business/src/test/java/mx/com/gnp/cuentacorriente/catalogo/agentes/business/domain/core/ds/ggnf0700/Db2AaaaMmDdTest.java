package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DB2-AAAA-MM-DD.
 *
 */
class Db2AaaaMmDdTest {

    private Db2AaaaMmDd db2AaaaMmDd;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        db2AaaaMmDd = new Db2AaaaMmDd();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        db2AaaaMmDd.setDb2Aaaa(0);
        assertEquals(0, db2AaaaMmDd.getDb2Aaaa());

        db2AaaaMmDd.setDb2Mm(0);
        assertEquals(0, db2AaaaMmDd.getDb2Mm());

        db2AaaaMmDd.setDb2Dd(0);
        assertEquals(0, db2AaaaMmDd.getDb2Dd());

    }

    // property db2Aaaa
    @Test
    void testGetterSetterDb2Aaaa() {

        assertNotNull(db2AaaaMmDd.getDb2Aaaa());

        int vint = 1234;
        db2AaaaMmDd.setDb2Aaaa(vint); // int
        assertEquals(asInt(vint), asInt(db2AaaaMmDd.getDb2Aaaa()));
    }

    // property db2Mm
    @Test
    void testGetterSetterDb2Mm() {

        assertNotNull(db2AaaaMmDd.getDb2Mm());

        int vint = 12;
        db2AaaaMmDd.setDb2Mm(vint); // int
        assertEquals(asInt(vint), asInt(db2AaaaMmDd.getDb2Mm()));
    }

    // property db2Dd
    @Test
    void testGetterSetterDb2Dd() {

        assertNotNull(db2AaaaMmDd.getDb2Dd());

        int vint = 12;
        db2AaaaMmDd.setDb2Dd(vint); // int
        assertEquals(asInt(vint), asInt(db2AaaaMmDd.getDb2Dd()));
    }
}

