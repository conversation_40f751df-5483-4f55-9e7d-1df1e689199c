package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FECHA-SYS.
 *
 */
class WsFechaSysTest {

    private WsFechaSys wsFechaSys;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFechaSys = new WsFechaSys();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFechaSys wsFechaSys2 = new WsFechaSys(parent);
        assertNotNull(wsFechaSys2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFechaSys wsFechaSys3 = new WsFechaSys(parent, sibling);
        assertNotNull(wsFechaSys3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFechaSys.getWsAaSys());

        assertNotNull(wsFechaSys.getWsMmSys());

        assertNotNull(wsFechaSys.getWsDdSys());

    }

    // property wsAaSys
    @Test
    void testGetterSetterWsAaSys() {

        assertNotNull(wsFechaSys.getWsAaSys());

        int vint = 1234;
        wsFechaSys.setWsAaSys(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaSys.getWsAaSys()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsFechaSys.setWsAaSys(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaSys.getWsAaSys()));
    }

    // property wsMmSys
    @Test
    void testGetterSetterWsMmSys() {

        assertNotNull(wsFechaSys.getWsMmSys());

        int vint = 12;
        wsFechaSys.setWsMmSys(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaSys.getWsMmSys()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaSys.setWsMmSys(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaSys.getWsMmSys()));
    }

    // property wsDdSys
    @Test
    void testGetterSetterWsDdSys() {

        assertNotNull(wsFechaSys.getWsDdSys());

        int vint = 12;
        wsFechaSys.setWsDdSys(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaSys.getWsDdSys()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaSys.setWsDdSys(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaSys.getWsDdSys()));
    }
}

