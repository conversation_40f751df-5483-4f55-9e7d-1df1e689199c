package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-DATE.
 *
 */
class WsAuxDateTest {

    private WsAuxDate wsAuxDate;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxDate = new WsAuxDate();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAuxDate wsAuxDate2 = new WsAuxDate(parent);
        assertNotNull(wsAuxDate2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsAuxDate wsAuxDate3 = new WsAuxDate(parent, sibling);
        assertNotNull(wsAuxDate3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAuxDate.getWsAuxDateAaaa());

        assertNotNull(wsAuxDate.getWsAuxDateMm());

        assertNotNull(wsAuxDate.getWsAuxDateDd());

    }

    // property wsAuxDateAaaa
    @Test
    void testGetterSetterWsAuxDateAaaa() {

        assertNotNull(wsAuxDate.getWsAuxDateAaaa());

        String vString = "ABCa";
        wsAuxDate.setWsAuxDateAaaa(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxDate.getWsAuxDateAaaa()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsAuxDate.setWsAuxDateAaaa(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAuxDate.getWsAuxDateAaaa()));
    }

    // property wsAuxDateMm
    @Test
    void testGetterSetterWsAuxDateMm() {

        assertNotNull(wsAuxDate.getWsAuxDateMm());

        String vString = "AB";
        wsAuxDate.setWsAuxDateMm(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxDate.getWsAuxDateMm()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAuxDate.setWsAuxDateMm(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAuxDate.getWsAuxDateMm()));
    }

    // property wsAuxDateDd
    @Test
    void testGetterSetterWsAuxDateDd() {

        assertNotNull(wsAuxDate.getWsAuxDateDd());

        String vString = "AB";
        wsAuxDate.setWsAuxDateDd(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxDate.getWsAuxDateDd()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAuxDate.setWsAuxDateDd(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAuxDate.getWsAuxDateDd()));
    }
}

