package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGFVTDMA0.
 *
 */
class WsDclgfvtdma0Test {

    private WsDclgfvtdma0 wsDclgfvtdma0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgfvtdma0 = new WsDclgfvtdma0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgfvtdma0.setDmaAgtIdr(0);
        assertEquals(0, wsDclgfvtdma0.getDmaAgtIdr());

        wsDclgfvtdma0.setDmaDmaIdr(0);
        assertEquals(0, wsDclgfvtdma0.getDmaDmaIdr());

        wsDclgfvtdma0.setDmaDmaCae("");
        assertEquals("", wsDclgfvtdma0.getDmaDmaCae());

        wsDclgfvtdma0.setDmaDmaNum("");
        assertEquals("", wsDclgfvtdma0.getDmaDmaNum());

        wsDclgfvtdma0.setDmaDmaInt("");
        assertEquals("", wsDclgfvtdma0.getDmaDmaInt());

        wsDclgfvtdma0.setDmaDmaCol("");
        assertEquals("", wsDclgfvtdma0.getDmaDmaCol());

        wsDclgfvtdma0.setDmaTdmCve(0);
        assertEquals(0, wsDclgfvtdma0.getDmaTdmCve());

        wsDclgfvtdma0.setDmaPobCve(0);
        assertEquals(0, wsDclgfvtdma0.getDmaPobCve());

        wsDclgfvtdma0.setDmaCpoCve(0);
        assertEquals(0, wsDclgfvtdma0.getDmaCpoCve());

        wsDclgfvtdma0.setDmaMndCve(0);
        assertEquals(0, wsDclgfvtdma0.getDmaMndCve());

        wsDclgfvtdma0.setDmaDmaDirExt("");
        assertEquals("", wsDclgfvtdma0.getDmaDmaDirExt());

    }

    // property dmaAgtIdr
    @Test
    void testGetterSetterDmaAgtIdr() {

        assertNotNull(wsDclgfvtdma0.getDmaAgtIdr());

        int vint = 123456789;
        wsDclgfvtdma0.setDmaAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtdma0.getDmaAgtIdr()));
    }

    // property dmaDmaIdr
    @Test
    void testGetterSetterDmaDmaIdr() {

        assertNotNull(wsDclgfvtdma0.getDmaDmaIdr());

        int vint = 1234;
        wsDclgfvtdma0.setDmaDmaIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtdma0.getDmaDmaIdr()));
    }

    // property dmaDmaCae
    @Test
    void testGetterSetterDmaDmaCae() {

        assertNotNull(wsDclgfvtdma0.getDmaDmaCae());

        String vString = "ABCabc1230";
        wsDclgfvtdma0.setDmaDmaCae(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtdma0.getDmaDmaCae()));
    }

    // property dmaDmaNum
    @Test
    void testGetterSetterDmaDmaNum() {

        assertNotNull(wsDclgfvtdma0.getDmaDmaNum());

        String vString = "ABCab";
        wsDclgfvtdma0.setDmaDmaNum(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtdma0.getDmaDmaNum()));
    }

    // property dmaDmaInt
    @Test
    void testGetterSetterDmaDmaInt() {

        assertNotNull(wsDclgfvtdma0.getDmaDmaInt());

        String vString = "ABCab";
        wsDclgfvtdma0.setDmaDmaInt(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtdma0.getDmaDmaInt()));
    }

    // property dmaDmaCol
    @Test
    void testGetterSetterDmaDmaCol() {

        assertNotNull(wsDclgfvtdma0.getDmaDmaCol());

        String vString = "ABCabc1230";
        wsDclgfvtdma0.setDmaDmaCol(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtdma0.getDmaDmaCol()));
    }

    // property dmaTdmCve
    @Test
    void testGetterSetterDmaTdmCve() {

        assertNotNull(wsDclgfvtdma0.getDmaTdmCve());

        int vint = 1234;
        wsDclgfvtdma0.setDmaTdmCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtdma0.getDmaTdmCve()));
    }

    // property dmaPobCve
    @Test
    void testGetterSetterDmaPobCve() {

        assertNotNull(wsDclgfvtdma0.getDmaPobCve());

        int vint = 123456789;
        wsDclgfvtdma0.setDmaPobCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtdma0.getDmaPobCve()));
    }

    // property dmaCpoCve
    @Test
    void testGetterSetterDmaCpoCve() {

        assertNotNull(wsDclgfvtdma0.getDmaCpoCve());

        int vint = 123456789;
        wsDclgfvtdma0.setDmaCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtdma0.getDmaCpoCve()));
    }

    // property dmaMndCve
    @Test
    void testGetterSetterDmaMndCve() {

        assertNotNull(wsDclgfvtdma0.getDmaMndCve());

        int vint = 123456789;
        wsDclgfvtdma0.setDmaMndCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtdma0.getDmaMndCve()));
    }

    // property dmaDmaDirExt
    @Test
    void testGetterSetterDmaDmaDirExt() {

        assertNotNull(wsDclgfvtdma0.getDmaDmaDirExt());

        String vString = "ABCabc1230";
        wsDclgfvtdma0.setDmaDmaDirExt(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtdma0.getDmaDmaDirExt()));
    }
}

