package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CODIGOS.
 *
 */
class WsCodigosTest {

    private WsCodigos wsCodigos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCodigos = new WsCodigos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsCodigos.setWsSqlcode(0);
        assertEquals(0, wsCodigos.getWsSqlcode());


    }

    // property wsSqlcode
    @Test
    void testGetterSetterWsSqlcode() {

        assertNotNull(wsCodigos.getWsSqlcode());

        int vint = 123456789;
        wsCodigos.setWsSqlcode(vint); // int
        assertEquals(asInt(vint), asInt(wsCodigos.getWsSqlcode()));
    }

    // property wsCodError
    @Test
    void testGetterSetterWsCodError() {

        assertNotNull(wsCodigos.getWsCodError());
    }

    // property wsDis1
    @Test
    void testGetterSetterWsDis1() {

        // Property: wsCodError.wsDis1 -> 05 WS-DIS1 X
        assertNotNull(wsCodigos.getWsDis1());
        String vString = "A";
        wsCodigos.setWsDis1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCodigos.setWsDis1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsDis2
    @Test
    void testGetterSetterWsDis2() {

        // Property: wsCodError.wsDis2 -> 05 WS-DIS2 9(9)
        assertNotNull(wsCodigos.getWsDis2());
        int vint = 123456789;
        wsCodigos.setWsDis2(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCodigos.setWsDis2(vINumericValue); // INumericValue
    }
}

