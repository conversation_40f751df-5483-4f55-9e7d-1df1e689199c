package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: W500-FECHA.
 *
 */
class W500FechaTest {

    private W500Fecha w500Fecha;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        w500Fecha = new W500Fecha();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        W500Fecha w500Fecha2 = new W500Fecha(parent);
        assertNotNull(w500Fecha2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        W500Fecha w500Fecha3 = new W500Fecha(parent, sibling);
        assertNotNull(w500Fecha3);
    }

    @Test
    void testInitialize() {
        assertNotNull(w500Fecha.getW500Ano());

        assertNotNull(w500Fecha.getW500Mes());

        assertNotNull(w500Fecha.getW500Dia());

    }

    // property w500Ano
    @Test
    void testGetterSetterW500Ano() {

        assertNotNull(w500Fecha.getW500Ano());

        int vint = 1234;
        w500Fecha.setW500Ano(vint); // int
        assertEquals(asInt(vint), asInt(w500Fecha.getW500Ano()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        w500Fecha.setW500Ano(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(w500Fecha.getW500Ano()));
    }

    // property w500Mes
    @Test
    void testGetterSetterW500Mes() {

        assertNotNull(w500Fecha.getW500Mes());

        int vint = 12;
        w500Fecha.setW500Mes(vint); // int
        assertEquals(asInt(vint), asInt(w500Fecha.getW500Mes()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        w500Fecha.setW500Mes(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(w500Fecha.getW500Mes()));
    }

    // property w500Dia
    @Test
    void testGetterSetterW500Dia() {

        assertNotNull(w500Fecha.getW500Dia());

        int vint = 12;
        w500Fecha.setW500Dia(vint); // int
        assertEquals(asInt(vint), asInt(w500Fecha.getW500Dia()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        w500Fecha.setW500Dia(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(w500Fecha.getW500Dia()));
    }
}

