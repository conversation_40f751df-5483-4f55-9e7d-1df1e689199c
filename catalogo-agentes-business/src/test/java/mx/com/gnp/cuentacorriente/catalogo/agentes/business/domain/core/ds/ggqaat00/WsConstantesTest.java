package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CONSTANTES.
 *
 */
class WsConstantesTest {

    private WsConstantes wsConstantes;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsConstantes = new WsConstantes();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsConstantes wsConstantes2 = new WsConstantes(parent);
        assertNotNull(wsConstantes2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        WsConstantes wsConstantes3 = new WsConstantes(parent, sibling);
        assertNotNull(wsConstantes3);
    }

    @Test
    void testInitialize() {

    }

    // property plhjConstantes
    @Test
    void testGetterSetterPlhjConstantes() {

        assertNotNull(wsConstantes.getPlhjConstantes());
    }

    // property plhjDireccionGnp1
    @Test
    void testGetterSetterPlhjDireccionGnp1() {

        // Property: ggqa00agcpy.plhjConstantes.plhjDireccionGnp1 -> 10 PLHJ-DIRECCION-GNP1 X(35)
        assertNotNull(wsConstantes.getPlhjDireccionGnp1());
        String vString = "ABCabc1230";
        wsConstantes.setPlhjDireccionGnp1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsConstantes.setPlhjDireccionGnp1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjDireccionGnp2
    @Test
    void testGetterSetterPlhjDireccionGnp2() {

        // Property: ggqa00agcpy.plhjConstantes.plhjDireccionGnp2 -> 10 PLHJ-DIRECCION-GNP2 X(35)
        assertNotNull(wsConstantes.getPlhjDireccionGnp2());
        String vString = "ABCabc1230";
        wsConstantes.setPlhjDireccionGnp2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsConstantes.setPlhjDireccionGnp2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjDireccionGnp3
    @Test
    void testGetterSetterPlhjDireccionGnp3() {

        // Property: ggqa00agcpy.plhjConstantes.plhjDireccionGnp3 -> 10 PLHJ-DIRECCION-GNP3 X(35)
        assertNotNull(wsConstantes.getPlhjDireccionGnp3());
        String vString = "ABCabc1230";
        wsConstantes.setPlhjDireccionGnp3(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsConstantes.setPlhjDireccionGnp3(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjNombreGz
    @Test
    void testGetterSetterPlhjNombreGz() {

        // Property: ggqa00agcpy.plhjConstantes.plhjNombreGz -> 10 PLHJ-NOMBRE-GZ X(24)
        assertNotNull(wsConstantes.getPlhjNombreGz());
        String vString = "ABCabc1230";
        wsConstantes.setPlhjNombreGz(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsConstantes.setPlhjNombreGz(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjNumeros
    @Test
    void testGetterSetterPlhjNumeros() {

        // Property: ggqa00agcpy.plhjConstantes.plhjNumeros -> 10 PLHJ-NUMEROS X(01)
        assertNotNull(wsConstantes.getPlhjNumeros());
        String vString = "A";
        wsConstantes.setPlhjNumeros(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsConstantes.setPlhjNumeros(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjEstadosSaga
    @Test
    void testGetterSetterPlhjEstadosSaga() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosSaga -> 10 PLHJ-ESTADOS-SAGA X(02)
        assertNotNull(wsConstantes.getPlhjEstadosSaga());
        String vString = "AB";
        wsConstantes.setPlhjEstadosSaga(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsConstantes.setPlhjEstadosSaga(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjEstadosCveTrac
    @Test
    void testGetterSetterPlhjEstadosCveTrac() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac -> 10 PLHJ-ESTADOS-CVE-TRAC
        assertNotNull(wsConstantes.getPlhjEstadosCveTrac());
    }

    // property plhjAgst
    @Test
    void testGetterSetterPlhjAgst() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjAgst -> 15 PLHJ-AGST X(03)
        assertNotNull(wsConstantes.getPlhjAgst());
        String vString = "ABC";
        wsConstantes.setPlhjAgst(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjAgst(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjBcnt
    @Test
    void testGetterSetterPlhjBcnt() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjBcnt -> 15 PLHJ-BCNT X(03)
        assertNotNull(wsConstantes.getPlhjBcnt());
        String vString = "ABC";
        wsConstantes.setPlhjBcnt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjBcnt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjBcst
    @Test
    void testGetterSetterPlhjBcst() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjBcst -> 15 PLHJ-BCST X(03)
        assertNotNull(wsConstantes.getPlhjBcst());
        String vString = "ABC";
        wsConstantes.setPlhjBcst(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjBcst(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjCampechet
    @Test
    void testGetterSetterPlhjCampechet() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjCampechet -> 15 PLHJ-CAMPECHET X(03)
        assertNotNull(wsConstantes.getPlhjCampechet());
        String vString = "ABC";
        wsConstantes.setPlhjCampechet(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjCampechet(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjCoahuilat
    @Test
    void testGetterSetterPlhjCoahuilat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjCoahuilat -> 15 PLHJ-COAHUILAT X(03)
        assertNotNull(wsConstantes.getPlhjCoahuilat());
        String vString = "ABC";
        wsConstantes.setPlhjCoahuilat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjCoahuilat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjColimat
    @Test
    void testGetterSetterPlhjColimat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjColimat -> 15 PLHJ-COLIMAT X(03)
        assertNotNull(wsConstantes.getPlhjColimat());
        String vString = "ABC";
        wsConstantes.setPlhjColimat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjColimat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjChiapast
    @Test
    void testGetterSetterPlhjChiapast() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjChiapast -> 15 PLHJ-CHIAPAST X(03)
        assertNotNull(wsConstantes.getPlhjChiapast());
        String vString = "ABC";
        wsConstantes.setPlhjChiapast(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjChiapast(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjChihuahuat
    @Test
    void testGetterSetterPlhjChihuahuat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjChihuahuat -> 15 PLHJ-CHIHUAHUAT X(03)
        assertNotNull(wsConstantes.getPlhjChihuahuat());
        String vString = "ABC";
        wsConstantes.setPlhjChihuahuat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjChihuahuat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjDft
    @Test
    void testGetterSetterPlhjDft() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjDft -> 15 PLHJ-DFT X(03)
        assertNotNull(wsConstantes.getPlhjDft());
        String vString = "ABC";
        wsConstantes.setPlhjDft(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjDft(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjDurangot
    @Test
    void testGetterSetterPlhjDurangot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjDurangot -> 15 PLHJ-DURANGOT X(03)
        assertNotNull(wsConstantes.getPlhjDurangot());
        String vString = "ABC";
        wsConstantes.setPlhjDurangot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjDurangot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjGtot
    @Test
    void testGetterSetterPlhjGtot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjGtot -> 15 PLHJ-GTOT X(03)
        assertNotNull(wsConstantes.getPlhjGtot());
        String vString = "ABC";
        wsConstantes.setPlhjGtot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjGtot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjGuerrerot
    @Test
    void testGetterSetterPlhjGuerrerot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjGuerrerot -> 15 PLHJ-GUERREROT X(03)
        assertNotNull(wsConstantes.getPlhjGuerrerot());
        String vString = "ABC";
        wsConstantes.setPlhjGuerrerot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjGuerrerot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjHidalgot
    @Test
    void testGetterSetterPlhjHidalgot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjHidalgot -> 15 PLHJ-HIDALGOT X(03)
        assertNotNull(wsConstantes.getPlhjHidalgot());
        String vString = "ABC";
        wsConstantes.setPlhjHidalgot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjHidalgot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjJaliscot
    @Test
    void testGetterSetterPlhjJaliscot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjJaliscot -> 15 PLHJ-JALISCOT X(03)
        assertNotNull(wsConstantes.getPlhjJaliscot());
        String vString = "ABC";
        wsConstantes.setPlhjJaliscot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjJaliscot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjEdomext
    @Test
    void testGetterSetterPlhjEdomext() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjEdomext -> 15 PLHJ-EDOMEXT X(03)
        assertNotNull(wsConstantes.getPlhjEdomext());
        String vString = "ABC";
        wsConstantes.setPlhjEdomext(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjEdomext(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjMichoacant
    @Test
    void testGetterSetterPlhjMichoacant() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjMichoacant -> 15 PLHJ-MICHOACANT X(03)
        assertNotNull(wsConstantes.getPlhjMichoacant());
        String vString = "ABC";
        wsConstantes.setPlhjMichoacant(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjMichoacant(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjMorelost
    @Test
    void testGetterSetterPlhjMorelost() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjMorelost -> 15 PLHJ-MORELOST X(03)
        assertNotNull(wsConstantes.getPlhjMorelost());
        String vString = "ABC";
        wsConstantes.setPlhjMorelost(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjMorelost(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjNayaritt
    @Test
    void testGetterSetterPlhjNayaritt() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjNayaritt -> 15 PLHJ-NAYARITT X(03)
        assertNotNull(wsConstantes.getPlhjNayaritt());
        String vString = "ABC";
        wsConstantes.setPlhjNayaritt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjNayaritt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjNuevoleont
    @Test
    void testGetterSetterPlhjNuevoleont() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjNuevoleont -> 15 PLHJ-NUEVOLEONT X(03)
        assertNotNull(wsConstantes.getPlhjNuevoleont());
        String vString = "ABC";
        wsConstantes.setPlhjNuevoleont(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjNuevoleont(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjOaxacat
    @Test
    void testGetterSetterPlhjOaxacat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjOaxacat -> 15 PLHJ-OAXACAT X(03)
        assertNotNull(wsConstantes.getPlhjOaxacat());
        String vString = "ABC";
        wsConstantes.setPlhjOaxacat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjOaxacat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjPueblat
    @Test
    void testGetterSetterPlhjPueblat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjPueblat -> 15 PLHJ-PUEBLAT X(03)
        assertNotNull(wsConstantes.getPlhjPueblat());
        String vString = "ABC";
        wsConstantes.setPlhjPueblat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjPueblat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjQueretarot
    @Test
    void testGetterSetterPlhjQueretarot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjQueretarot -> 15 PLHJ-QUERETAROT X(03)
        assertNotNull(wsConstantes.getPlhjQueretarot());
        String vString = "ABC";
        wsConstantes.setPlhjQueretarot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjQueretarot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjQroot
    @Test
    void testGetterSetterPlhjQroot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjQroot -> 15 PLHJ-QROOT X(03)
        assertNotNull(wsConstantes.getPlhjQroot());
        String vString = "ABC";
        wsConstantes.setPlhjQroot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjQroot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjSlpt
    @Test
    void testGetterSetterPlhjSlpt() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjSlpt -> 15 PLHJ-SLPT X(03)
        assertNotNull(wsConstantes.getPlhjSlpt());
        String vString = "ABC";
        wsConstantes.setPlhjSlpt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjSlpt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjSinaloat
    @Test
    void testGetterSetterPlhjSinaloat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjSinaloat -> 15 PLHJ-SINALOAT X(03)
        assertNotNull(wsConstantes.getPlhjSinaloat());
        String vString = "ABC";
        wsConstantes.setPlhjSinaloat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjSinaloat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjSonorat
    @Test
    void testGetterSetterPlhjSonorat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjSonorat -> 15 PLHJ-SONORAT X(03)
        assertNotNull(wsConstantes.getPlhjSonorat());
        String vString = "ABC";
        wsConstantes.setPlhjSonorat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjSonorat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjTabascot
    @Test
    void testGetterSetterPlhjTabascot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjTabascot -> 15 PLHJ-TABASCOT X(03)
        assertNotNull(wsConstantes.getPlhjTabascot());
        String vString = "ABC";
        wsConstantes.setPlhjTabascot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjTabascot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjTamaulipast
    @Test
    void testGetterSetterPlhjTamaulipast() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjTamaulipast -> 15 PLHJ-TAMAULIPAST X(03)
        assertNotNull(wsConstantes.getPlhjTamaulipast());
        String vString = "ABC";
        wsConstantes.setPlhjTamaulipast(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjTamaulipast(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjTlaxcalat
    @Test
    void testGetterSetterPlhjTlaxcalat() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjTlaxcalat -> 15 PLHJ-TLAXCALAT X(03)
        assertNotNull(wsConstantes.getPlhjTlaxcalat());
        String vString = "ABC";
        wsConstantes.setPlhjTlaxcalat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjTlaxcalat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjVeracruzt
    @Test
    void testGetterSetterPlhjVeracruzt() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjVeracruzt -> 15 PLHJ-VERACRUZT X(03)
        assertNotNull(wsConstantes.getPlhjVeracruzt());
        String vString = "ABC";
        wsConstantes.setPlhjVeracruzt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjVeracruzt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjYucatant
    @Test
    void testGetterSetterPlhjYucatant() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjYucatant -> 15 PLHJ-YUCATANT X(03)
        assertNotNull(wsConstantes.getPlhjYucatant());
        String vString = "ABC";
        wsConstantes.setPlhjYucatant(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjYucatant(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjZacatecast
    @Test
    void testGetterSetterPlhjZacatecast() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjZacatecast -> 15 PLHJ-ZACATECAST X(03)
        assertNotNull(wsConstantes.getPlhjZacatecast());
        String vString = "ABC";
        wsConstantes.setPlhjZacatecast(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjZacatecast(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjExtranjerot
    @Test
    void testGetterSetterPlhjExtranjerot() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjExtranjerot -> 15 PLHJ-EXTRANJEROT X(03)
        assertNotNull(wsConstantes.getPlhjExtranjerot());
        String vString = "ABC";
        wsConstantes.setPlhjExtranjerot(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjExtranjerot(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhjOtrost
    @Test
    void testGetterSetterPlhjOtrost() {

        // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjOtrost -> 15 PLHJ-OTROST X(03)
        assertNotNull(wsConstantes.getPlhjOtrost());
        String vString = "ABC";
        wsConstantes.setPlhjOtrost(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsConstantes.setPlhjOtrost(vIAlphanumericValue); // IAlphanumericValue
    }
}

