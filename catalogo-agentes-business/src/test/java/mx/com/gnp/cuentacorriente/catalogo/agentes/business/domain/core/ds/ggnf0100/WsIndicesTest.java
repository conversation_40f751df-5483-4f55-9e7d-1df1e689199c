package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-INDICES.
 *
 */
class WsIndicesTest {

    private WsIndices wsIndices;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsIndices = new WsIndices();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsIndices.setI(0);
        assertEquals(0, wsIndices.getI());

        wsIndices.setJ(0);
        assertEquals(0, wsIndices.getJ());

        wsIndices.setK(0);
        assertEquals(0, wsIndices.getK());

        wsIndices.setL(0);
        assertEquals(0, wsIndices.getL());

    }

    // property i
    @Test
    void testGetterSetterI() {

        assertNotNull(wsIndices.getI());

        int vint = 12;
        wsIndices.setI(vint); // int
        assertEquals(asInt(vint), asInt(wsIndices.getI()));
    }

    // property j
    @Test
    void testGetterSetterJ() {

        assertNotNull(wsIndices.getJ());

        int vint = 12;
        wsIndices.setJ(vint); // int
        assertEquals(asInt(vint), asInt(wsIndices.getJ()));
    }

    // property k
    @Test
    void testGetterSetterK() {

        assertNotNull(wsIndices.getK());

        int vint = 12;
        wsIndices.setK(vint); // int
        assertEquals(asInt(vint), asInt(wsIndices.getK()));
    }

    // property l
    @Test
    void testGetterSetterL() {

        assertNotNull(wsIndices.getL());

        int vint = 12;
        wsIndices.setL(vint); // int
        assertEquals(asInt(vint), asInt(wsIndices.getL()));
    }
}

