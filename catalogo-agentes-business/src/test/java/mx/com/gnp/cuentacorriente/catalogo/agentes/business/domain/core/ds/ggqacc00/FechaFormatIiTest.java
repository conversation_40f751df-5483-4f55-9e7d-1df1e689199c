package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: FECHA-FORMAT-II.
 *
 */
class FechaFormatIiTest {

    private FechaFormatIi fechaFormatIi;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        fechaFormatIi = new FechaFormatIi();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        FechaFormatIi fechaFormatIi2 = new FechaFormatIi(parent);
        assertNotNull(fechaFormatIi2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        FechaFormatIi fechaFormatIi3 = new FechaFormatIi(parent, sibling);
        assertNotNull(fechaFormatIi3);
    }

    @Test
    void testInitialize() {
        assertNotNull(fechaFormatIi.getFechaFormatIiAa());

        assertNotNull(fechaFormatIi.getFechaFormatIiF1());

        assertNotNull(fechaFormatIi.getFechaFormatIiMm());

        assertNotNull(fechaFormatIi.getFechaFormatIiF2());

        assertNotNull(fechaFormatIi.getFechaFormatIiDd());

    }

    // property fechaFormatIiAa
    @Test
    void testGetterSetterFechaFormatIiAa() {

        assertNotNull(fechaFormatIi.getFechaFormatIiAa());

        int vint = 1234;
        fechaFormatIi.setFechaFormatIiAa(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatIi.getFechaFormatIiAa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        fechaFormatIi.setFechaFormatIiAa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(fechaFormatIi.getFechaFormatIiAa()));
    }

    // property fechaFormatIiF1
    @Test
    void testGetterSetterFechaFormatIiF1() {

        assertNotNull(fechaFormatIi.getFechaFormatIiF1());

        String vString = "A";
        fechaFormatIi.setFechaFormatIiF1(vString); // String
        assertEquals(asStr(vString), asStr(fechaFormatIi.getFechaFormatIiF1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        fechaFormatIi.setFechaFormatIiF1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(fechaFormatIi.getFechaFormatIiF1()));
    }

    // property fechaFormatIiMm
    @Test
    void testGetterSetterFechaFormatIiMm() {

        assertNotNull(fechaFormatIi.getFechaFormatIiMm());

        int vint = 12;
        fechaFormatIi.setFechaFormatIiMm(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatIi.getFechaFormatIiMm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        fechaFormatIi.setFechaFormatIiMm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(fechaFormatIi.getFechaFormatIiMm()));
    }

    // property fechaFormatIiF2
    @Test
    void testGetterSetterFechaFormatIiF2() {

        assertNotNull(fechaFormatIi.getFechaFormatIiF2());

        String vString = "A";
        fechaFormatIi.setFechaFormatIiF2(vString); // String
        assertEquals(asStr(vString), asStr(fechaFormatIi.getFechaFormatIiF2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        fechaFormatIi.setFechaFormatIiF2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(fechaFormatIi.getFechaFormatIiF2()));
    }

    // property fechaFormatIiDd
    @Test
    void testGetterSetterFechaFormatIiDd() {

        assertNotNull(fechaFormatIi.getFechaFormatIiDd());

        int vint = 12;
        fechaFormatIi.setFechaFormatIiDd(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatIi.getFechaFormatIiDd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        fechaFormatIi.setFechaFormatIiDd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(fechaFormatIi.getFechaFormatIiDd()));
    }
}

