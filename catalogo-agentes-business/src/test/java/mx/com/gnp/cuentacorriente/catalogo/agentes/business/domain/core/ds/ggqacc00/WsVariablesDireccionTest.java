package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-VARIABLES-DIRECCION.
 *
 */
class WsVariablesDireccionTest {

    private WsVariablesDireccion wsVariablesDireccion;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsVariablesDireccion = new WsVariablesDireccion();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsVariablesDireccion.setWsCae("");
        assertEquals("", wsVariablesDireccion.getWsCae());

        wsVariablesDireccion.setWsNum("");
        assertEquals("", wsVariablesDireccion.getWsNum());

        wsVariablesDireccion.setWsInt("");
        assertEquals("", wsVariablesDireccion.getWsInt());

        wsVariablesDireccion.setWsCol("");
        assertEquals("", wsVariablesDireccion.getWsCol());

        wsVariablesDireccion.setWsMndNom("");
        assertEquals("", wsVariablesDireccion.getWsMndNom());

        wsVariablesDireccion.setWsPobNom("");
        assertEquals("", wsVariablesDireccion.getWsPobNom());

        wsVariablesDireccion.setWsMndEdoCve("");
        assertEquals("", wsVariablesDireccion.getWsMndEdoCve());

        wsVariablesDireccion.setWsMndEdoNom("");
        assertEquals("", wsVariablesDireccion.getWsMndEdoNom());

        wsVariablesDireccion.setWsMndPaiCve("");
        assertEquals("", wsVariablesDireccion.getWsMndPaiCve());

        wsVariablesDireccion.setWsMndPaiNom("");
        assertEquals("", wsVariablesDireccion.getWsMndPaiNom());

        wsVariablesDireccion.setWsCpoCve(0);
        assertEquals(0, wsVariablesDireccion.getWsCpoCve());

        wsVariablesDireccion.setWsGerZonEnv(0);
        assertEquals(0, wsVariablesDireccion.getWsGerZonEnv());

    }

    // property wsCae
    @Test
    void testGetterSetterWsCae() {

        assertNotNull(wsVariablesDireccion.getWsCae());

        String vString = "ABCabc1230";
        wsVariablesDireccion.setWsCae(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsCae()));
    }

    // property wsNum
    @Test
    void testGetterSetterWsNum() {

        assertNotNull(wsVariablesDireccion.getWsNum());

        String vString = "ABCab";
        wsVariablesDireccion.setWsNum(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsNum()));
    }

    // property wsInt
    @Test
    void testGetterSetterWsInt() {

        assertNotNull(wsVariablesDireccion.getWsInt());

        String vString = "ABCab";
        wsVariablesDireccion.setWsInt(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsInt()));
    }

    // property wsCol
    @Test
    void testGetterSetterWsCol() {

        assertNotNull(wsVariablesDireccion.getWsCol());

        String vString = "ABCabc1230";
        wsVariablesDireccion.setWsCol(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsCol()));
    }

    // property wsMndNom
    @Test
    void testGetterSetterWsMndNom() {

        assertNotNull(wsVariablesDireccion.getWsMndNom());

        String vString = "ABCabc1230";
        wsVariablesDireccion.setWsMndNom(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsMndNom()));
    }

    // property wsPobNom
    @Test
    void testGetterSetterWsPobNom() {

        assertNotNull(wsVariablesDireccion.getWsPobNom());

        String vString = "ABCabc1230";
        wsVariablesDireccion.setWsPobNom(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsPobNom()));
    }

    // property wsMndEdoCve
    @Test
    void testGetterSetterWsMndEdoCve() {

        assertNotNull(wsVariablesDireccion.getWsMndEdoCve());

        String vString = "ABCa";
        wsVariablesDireccion.setWsMndEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsMndEdoCve()));
    }

    // property wsMndEdoNom
    @Test
    void testGetterSetterWsMndEdoNom() {

        assertNotNull(wsVariablesDireccion.getWsMndEdoNom());

        String vString = "ABCabc1230";
        wsVariablesDireccion.setWsMndEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsMndEdoNom()));
    }

    // property wsMndPaiCve
    @Test
    void testGetterSetterWsMndPaiCve() {

        assertNotNull(wsVariablesDireccion.getWsMndPaiCve());

        String vString = "ABC";
        wsVariablesDireccion.setWsMndPaiCve(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsMndPaiCve()));
    }

    // property wsMndPaiNom
    @Test
    void testGetterSetterWsMndPaiNom() {

        assertNotNull(wsVariablesDireccion.getWsMndPaiNom());

        String vString = "ABCabc1230";
        wsVariablesDireccion.setWsMndPaiNom(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesDireccion.getWsMndPaiNom()));
    }

    // property wsCpoCve
    @Test
    void testGetterSetterWsCpoCve() {

        assertNotNull(wsVariablesDireccion.getWsCpoCve());

        int vint = 12345;
        wsVariablesDireccion.setWsCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesDireccion.getWsCpoCve()));
    }

    // property wsGerZonEnv
    @Test
    void testGetterSetterWsGerZonEnv() {

        assertNotNull(wsVariablesDireccion.getWsGerZonEnv());

        int vint = 1234;
        wsVariablesDireccion.setWsGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesDireccion.getWsGerZonEnv()));
    }
}

