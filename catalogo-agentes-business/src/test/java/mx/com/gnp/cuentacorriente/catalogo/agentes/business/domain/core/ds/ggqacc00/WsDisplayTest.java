package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DISPLAY.
 *
 */
class WsDisplayTest {

    private WsDisplay wsDisplay;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDisplay = new WsDisplay();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsDisplay.getWsDiferenciaEdit());

    }

    // property wsDiferenciaEdit
    @Test
    void testGetterSetterWsDiferenciaEdit() {

        assertNotNull(wsDisplay.getWsDiferenciaEdit());

        String vString = "1230,0000000";
        wsDisplay.setWsDiferenciaEdit(vString); // String
        assertEquals(asStr(vString), asStr(wsDisplay.getWsDiferenciaEdit()));

        NumericVar vIValue = new NumericVar(12, 0);;
        vIValue.setValue("123456789012");
        wsDisplay.setWsDiferenciaEdit(vIValue); // IValue
        // format("123456789012", "----,---,--9")
        assertNotNull(asStr(wsDisplay.getWsDiferenciaEdit()));
    }
}

