package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

class Ggqaf900Test {
    Ggqaf900 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggqaf900.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p020Procesa test conditions:
        bdErr : ["0", "XX0", "1", "XX1"]
    */
    @Test
    void p020ProcesaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getGgqa0053cpy().setBdErr("0");
        try {
            pgm.p020Procesa();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p020ProcesaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getGgqa0053cpy().setBdErr("XX0");
            try {
                pgm.p020Procesa();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p020ProcesaTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getGgqa0053cpy().setBdErr("1");
                try {
                    pgm.p020Procesa();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p020ProcesaTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getGgqa0053cpy().setBdErr("XX1");
                    try {
                        pgm.p020Procesa();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p050ValidaSelect test conditions:
        bdErr : ["0", "XX0"]
    */
    @Test
    void p050ValidaSelectTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getGgqa0053cpy().setBdErr("0");
        try {
            pgm.p050ValidaSelect();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p050ValidaSelectTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getGgqa0053cpy().setBdErr("XX0");
            try {
                pgm.p050ValidaSelect();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p110SelectHfo test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p110SelectHfoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p110SelectHfo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p110SelectHfoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p110SelectHfo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p110SelectHfoTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p110SelectHfo();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p110SelectHfoTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p110SelectHfo();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p120InsertHfo test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p120InsertHfoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p120InsertHfo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p120InsertHfoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p120InsertHfo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p120InsertHfoTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p120InsertHfo();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p120InsertHfoTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p120InsertHfo();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p130UpdateHfo test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p130UpdateHfoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p130UpdateHfo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p130UpdateHfoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p130UpdateHfo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p130UpdateHfoTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p130UpdateHfo();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p130UpdateHfoTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p130UpdateHfo();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p152OpenC1cd test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p152OpenC1cdTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p152OpenC1cd();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p152OpenC1cdTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p152OpenC1cd();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p152OpenC1cdTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p152OpenC1cd();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p152OpenC1cdTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p152OpenC1cd();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p160ValidaFetch test conditions:
        bdErr : ["1", "XX1"]
    */
    @Test
    void p160ValidaFetchTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getGgqa0053cpy().setBdErr("1");
        try {
            pgm.p160ValidaFetch();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p160ValidaFetchTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getGgqa0053cpy().setBdErr("XX1");
            try {
                pgm.p160ValidaFetch();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p162LeeCursorC1cd test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p162LeeCursorC1cdTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p162LeeCursorC1cd();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p162LeeCursorC1cdTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p162LeeCursorC1cd();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p162LeeCursorC1cdTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p162LeeCursorC1cd();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p162LeeCursorC1cdTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p162LeeCursorC1cd();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p170ValidaRegActivo test conditions:
        bdErr : ["0", "XX0"]
    */
    @Test
    void p170ValidaRegActivoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getGgqa0053cpy().setBdErr("0");
        try {
            pgm.p170ValidaRegActivo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p170ValidaRegActivoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getGgqa0053cpy().setBdErr("XX0");
            try {
                pgm.p170ValidaRegActivo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p999CloseC1cd test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p999CloseC1cdTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p999CloseC1cd();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p999CloseC1cdTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p999CloseC1cd();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p999CloseC1cdTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p999CloseC1cd();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p999CloseC1cdTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p999CloseC1cd();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p999VerErrSql test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p999VerErrSqlTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p999VerErrSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p999VerErrSqlTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p999VerErrSql();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p999VerErrSqlTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p999VerErrSql();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p999VerErrSqlTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p999VerErrSql();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
}
