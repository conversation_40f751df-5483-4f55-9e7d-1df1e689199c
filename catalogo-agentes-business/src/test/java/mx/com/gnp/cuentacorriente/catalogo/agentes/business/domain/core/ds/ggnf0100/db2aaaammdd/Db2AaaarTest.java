package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.db2aaaammdd;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DB2-AAAAR.
 *
 */
class Db2AaaarTest {

    private Db2Aaaar db2Aaaar;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        db2Aaaar = new Db2Aaaar();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Db2Aaaar db2Aaaar2 = new Db2Aaaar(parent);
        assertNotNull(db2Aaaar2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 4);
        Db2Aaaar db2Aaaar3 = new Db2Aaaar(parent, sibling);
        assertNotNull(db2Aaaar3);
    }

    @Test
    void testInitialize() {
        assertNotNull(db2Aaaar.getDb2Aa1());

        assertNotNull(db2Aaaar.getDb2Aa2());

    }

    // property db2Aa1
    @Test
    void testGetterSetterDb2Aa1() {

        assertNotNull(db2Aaaar.getDb2Aa1());

        int vint = 12;
        db2Aaaar.setDb2Aa1(vint); // int
        assertEquals(asInt(vint), asInt(db2Aaaar.getDb2Aa1()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        db2Aaaar.setDb2Aa1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(db2Aaaar.getDb2Aa1()));
    }

    // property db2Aa2
    @Test
    void testGetterSetterDb2Aa2() {

        assertNotNull(db2Aaaar.getDb2Aa2());

        int vint = 12;
        db2Aaaar.setDb2Aa2(vint); // int
        assertEquals(asInt(vint), asInt(db2Aaaar.getDb2Aa2()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        db2Aaaar.setDb2Aa2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(db2Aaaar.getDb2Aa2()));
    }
}

