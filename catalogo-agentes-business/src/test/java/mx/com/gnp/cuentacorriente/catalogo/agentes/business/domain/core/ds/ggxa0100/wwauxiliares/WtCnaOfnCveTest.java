package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WT-CNA-OFN-CVE.
 *
 */
class WtCnaOfnCveTest {

    private WtCnaOfnCve wtCnaOfnCve;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wtCnaOfnCve = new WtCnaOfnCve();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WtCnaOfnCve wtCnaOfnCve2 = new WtCnaOfnCve(parent);
        assertNotNull(wtCnaOfnCve2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 4);
        WtCnaOfnCve wtCnaOfnCve3 = new WtCnaOfnCve(parent, sibling);
        assertNotNull(wtCnaOfnCve3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wtCnaOfnCve.getWtCnaOfnCveA());

    }

    // property wtCnaOfnCveA
    @Test
    void testGetterSetterWtCnaOfnCveA() {

        assertNotNull(wtCnaOfnCve.getWtCnaOfnCveA());
        assertNotNull(wtCnaOfnCve.getWtCnaOfnCveA(1));

        String vString = "A";
        wtCnaOfnCve.getWtCnaOfnCveA().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wtCnaOfnCve.getWtCnaOfnCveA(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wtCnaOfnCve.getWtCnaOfnCveA().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wtCnaOfnCve.getWtCnaOfnCveA(1)));
    }
}

