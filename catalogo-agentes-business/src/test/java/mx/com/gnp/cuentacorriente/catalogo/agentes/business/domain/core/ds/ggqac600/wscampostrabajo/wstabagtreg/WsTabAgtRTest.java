package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.wstabagtreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TAB-AGT-R.
 *
 */
class WsTabAgtRTest {

    private WsTabAgtR wsTabAgtR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTabAgtR = new WsTabAgtR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsTabAgtR wsTabAgtR2 = new WsTabAgtR(parent);
        assertNotNull(wsTabAgtR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsTabAgtR wsTabAgtR3 = new WsTabAgtR(parent, sibling);
        assertNotNull(wsTabAgtR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsTabAgtR.getWsTabFiller());

        assertNotNull(wsTabAgtR.getWsTabAgt());

    }

    // property wsTabFiller
    @Test
    void testGetterSetterWsTabFiller() {

        assertNotNull(wsTabAgtR.getWsTabFiller());

        String vString = "ABC";
        wsTabAgtR.setWsTabFiller(vString); // String
        assertEquals(asStr(vString), asStr(wsTabAgtR.getWsTabFiller()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsTabAgtR.setWsTabFiller(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsTabAgtR.getWsTabFiller()));
    }

    // property wsTabAgt
    @Test
    void testGetterSetterWsTabAgt() {

        assertNotNull(wsTabAgtR.getWsTabAgt());

        String vString = "ABCabc1";
        wsTabAgtR.setWsTabAgt(vString); // String
        assertEquals(asStr(vString), asStr(wsTabAgtR.getWsTabAgt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wsTabAgtR.setWsTabAgt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsTabAgtR.getWsTabAgt()));
    }
}

