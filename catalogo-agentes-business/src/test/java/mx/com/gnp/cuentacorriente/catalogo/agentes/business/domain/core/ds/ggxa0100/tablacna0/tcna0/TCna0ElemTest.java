package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablacna0.tcna0;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-CNA0-ELEM.
 *
 */
class TCna0ElemTest {

    private TCna0Elem tCna0Elem;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tCna0Elem = new TCna0Elem();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TCna0Elem tCna0Elem2 = new TCna0Elem(parent);
        assertNotNull(tCna0Elem2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 3);
        TCna0Elem tCna0Elem3 = new TCna0Elem(parent, sibling);
        assertNotNull(tCna0Elem3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tCna0Elem.getSCnaCnaNum());

        assertNotNull(tCna0Elem.getSCnaLimitad());

    }

    // property sCnaCnaNum
    @Test
    void testGetterSetterSCnaCnaNum() {

        assertNotNull(tCna0Elem.getSCnaCnaNum());

        String vString = "AB";
        tCna0Elem.setSCnaCnaNum(vString); // String
        assertEquals(asStr(vString), asStr(tCna0Elem.getSCnaCnaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        tCna0Elem.setSCnaCnaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tCna0Elem.getSCnaCnaNum()));
    }

    // property sCnaLimitad
    @Test
    void testGetterSetterSCnaLimitad() {

        assertNotNull(tCna0Elem.getSCnaLimitad());

        String vString = "A";
        tCna0Elem.setSCnaLimitad(vString); // String
        assertEquals(asStr(vString), asStr(tCna0Elem.getSCnaLimitad()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tCna0Elem.setSCnaLimitad(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tCna0Elem.getSCnaLimitad()));
    }
}

