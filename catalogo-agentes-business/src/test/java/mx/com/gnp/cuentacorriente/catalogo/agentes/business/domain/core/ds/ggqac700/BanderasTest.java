package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: BANDERAS.
 *
 */
class BanderasTest {

    private Banderas banderas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        banderas = new Banderas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property banderasGlobales
    @Test
    void testGetterSetterBanderasGlobales() {

        assertNotNull(banderas.getBanderasGlobales());
    }

    // property stCatDirec
    @Test
    void testGetterSetterStCatDirec() {

        // Property: banderasGlobales.stCatDirec -> 03 ST-CAT-DIREC X(02)
        assertNotNull(banderas.getStCatDirec());
        String vString = "AB";
        banderas.setStCatDirec(vString); // String
    }

    // property wsFinCatDirecc
    @Test
    void testGetterSetterWsFinCatDirecc() {

        // Property: banderasGlobales.wsFinCatDirecc -> 03 WS-FIN-CAT-DIRECC 9(01)
        assertNotNull(banderas.getWsFinCatDirecc());
        int vint = 1;
        banderas.setWsFinCatDirecc(vint); // int
    }
}

