package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: LINEA.
 *
 */
class LineaTest {

    private Linea linea;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        linea = new Linea();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Linea linea2 = new Linea(parent);
        assertNotNull(linea2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 133);
        Linea linea3 = new Linea(parent, sibling);
        assertNotNull(linea3);
    }

    @Test
    void testInitialize() {
        assertNotNull(linea.getLinSalto());

        assertNotNull(linea.getLinResto());





    }

    // property linSalto
    @Test
    void testGetterSetterLinSalto() {

        assertNotNull(linea.getLinSalto());

        String vString = "A";
        linea.setLinSalto(vString); // String
        assertEquals(asStr(vString), asStr(linea.getLinSalto()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        linea.setLinSalto(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(linea.getLinSalto()));
    }

    // property linResto
    @Test
    void testGetterSetterLinResto() {

        assertNotNull(linea.getLinResto());

        String vString = "ABCabc1230";
        linea.setLinResto(vString); // String
        assertEquals(asStr(vString), asStr(linea.getLinResto()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setLinResto(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(linea.getLinResto()));
    }

    // property det1
    @Test
    void testGetterSetterDet1() {

        assertNotNull(linea.getDet1());
    }

    // property det2
    @Test
    void testGetterSetterDet2() {

        assertNotNull(linea.getDet2());
    }

    // property det22
    @Test
    void testGetterSetterDet22() {

        assertNotNull(linea.getDet22());
    }

    // property det3
    @Test
    void testGetterSetterDet3() {

        assertNotNull(linea.getDet3());
    }

    // property d1FlaNum
    @Test
    void testGetterSetterD1FlaNum() {

        // Property: det1.d1FlaNum -> 05 D1-FLA-NUM X(8)
        assertNotNull(linea.getD1FlaNum());
        String vString = "ABCabc12";
        linea.setD1FlaNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        linea.setD1FlaNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1EmpCve
    @Test
    void testGetterSetterD1EmpCve() {

        // Property: det1.d1EmpCve -> 05 D1-EMP-CVE X(5)
        assertNotNull(linea.getD1EmpCve());
        String vString = "ABCab";
        linea.setD1EmpCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        linea.setD1EmpCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1EsaCve
    @Test
    void testGetterSetterD1EsaCve() {

        // Property: det1.d1EsaCve -> 05 D1-ESA-CVE ZZZ9
        assertNotNull(linea.getD1EsaCve());
        String vString = "ABCa";
        linea.setD1EsaCve(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD1EsaCve(vIValue); // IValue
    }

    // property d1OfnCve
    @Test
    void testGetterSetterD1OfnCve() {

        // Property: det1.d1OfnCve -> 05 D1-OFN-CVE ZZZ9
        assertNotNull(linea.getD1OfnCve());
        String vString = "ABCa";
        linea.setD1OfnCve(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD1OfnCve(vIValue); // IValue
    }

    // property d1OrvCve
    @Test
    void testGetterSetterD1OrvCve() {

        // Property: det1.d1OrvCve -> 05 D1-ORV-CVE ZZZ9
        assertNotNull(linea.getD1OrvCve());
        String vString = "ABCa";
        linea.setD1OrvCve(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD1OrvCve(vIValue); // IValue
    }

    // property d1PefCve
    @Test
    void testGetterSetterD1PefCve() {

        // Property: det1.d1PefCve -> 05 D1-PEF-CVE ZZZ9
        assertNotNull(linea.getD1PefCve());
        String vString = "ABCa";
        linea.setD1PefCve(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD1PefCve(vIValue); // IValue
    }

    // property d1AgtNom
    @Test
    void testGetterSetterD1AgtNom() {

        // Property: det1.d1AgtNom -> 05 D1-AGT-NOM X(10)
        assertNotNull(linea.getD1AgtNom());
        String vString = "ABCabc1230";
        linea.setD1AgtNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD1AgtNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1AgtApePat
    @Test
    void testGetterSetterD1AgtApePat() {

        // Property: det1.d1AgtApePat -> 05 D1-AGT-APE-PAT X(10)
        assertNotNull(linea.getD1AgtApePat());
        String vString = "ABCabc1230";
        linea.setD1AgtApePat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD1AgtApePat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1AgtApeMat
    @Test
    void testGetterSetterD1AgtApeMat() {

        // Property: det1.d1AgtApeMat -> 05 D1-AGT-APE-MAT X(10)
        assertNotNull(linea.getD1AgtApeMat());
        String vString = "ABCabc1230";
        linea.setD1AgtApeMat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD1AgtApeMat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1AgtRazSoc
    @Test
    void testGetterSetterD1AgtRazSoc() {

        // Property: det1.d1AgtRazSoc -> 05 D1-AGT-RAZ-SOC X(8)
        assertNotNull(linea.getD1AgtRazSoc());
        String vString = "ABCabc12";
        linea.setD1AgtRazSoc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        linea.setD1AgtRazSoc(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1Obs
    @Test
    void testGetterSetterD1Obs() {

        // Property: det1.d1Obs -> 05 D1-OBS X(20)
        assertNotNull(linea.getD1Obs());
        String vString = "ABCabc1230";
        linea.setD1Obs(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD1Obs(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d2AgtIdr
    @Test
    void testGetterSetterD2AgtIdr() {

        // Property: det2.d2AgtIdr -> 05 D2-AGT-IDR ZZZZZZZZ9
        assertNotNull(linea.getD2AgtIdr());
        String vString = "ABCabc123";
        linea.setD2AgtIdr(vString); // String
        NumericVar vIValue = new NumericVar(9, 0);;
        vIValue.setValue("123456789");
        linea.setD2AgtIdr(vIValue); // IValue
    }

    // property d2CnaNum
    @Test
    void testGetterSetterD2CnaNum() {

        // Property: det2.d2CnaNum -> 05 D2-CNA-NUM ZZZ9
        assertNotNull(linea.getD2CnaNum());
        String vString = "ABCa";
        linea.setD2CnaNum(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD2CnaNum(vIValue); // IValue
    }

    // property d2TfpCve
    @Test
    void testGetterSetterD2TfpCve() {

        // Property: det2.d2TfpCve -> 05 D2-TFP-CVE ZZZ9
        assertNotNull(linea.getD2TfpCve());
        String vString = "ABCa";
        linea.setD2TfpCve(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD2TfpCve(vIValue); // IValue
    }

    // property d2CnaIndGer
    @Test
    void testGetterSetterD2CnaIndGer() {

        // Property: det2.d2CnaIndGer -> 05 D2-CNA-IND-GER X
        assertNotNull(linea.getD2CnaIndGer());
        String vString = "A";
        linea.setD2CnaIndGer(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        linea.setD2CnaIndGer(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d2MteCve
    @Test
    void testGetterSetterD2MteCve() {

        // Property: det2.d2MteCve -> 05 D2-MTE-CVE ZZZ9
        assertNotNull(linea.getD2MteCve());
        String vString = "ABCa";
        linea.setD2MteCve(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD2MteCve(vIValue); // IValue
    }

    // property d2FlaFecMotEss
    @Test
    void testGetterSetterD2FlaFecMotEss() {

        // Property: det2.d2FlaFecMotEss -> 05 D2-FLA-FEC-MOT-ESS X(10)
        assertNotNull(linea.getD2FlaFecMotEss());
        String vString = "ABCabc1230";
        linea.setD2FlaFecMotEss(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD2FlaFecMotEss(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d2FlaNumGte
    @Test
    void testGetterSetterD2FlaNumGte() {

        // Property: det2.d2FlaNumGte -> 05 D2-FLA-NUM-GTE X(08)
        assertNotNull(linea.getD2FlaNumGte());
        String vString = "ABCabc12";
        linea.setD2FlaNumGte(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        linea.setD2FlaNumGte(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d2CnaGerZonEnv
    @Test
    void testGetterSetterD2CnaGerZonEnv() {

        // Property: det2.d2CnaGerZonEnv -> 05 D2-CNA-GER-ZON-ENV 9999
        assertNotNull(linea.getD2CnaGerZonEnv());
        int vint = 1234;
        linea.setD2CnaGerZonEnv(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        linea.setD2CnaGerZonEnv(vINumericValue); // INumericValue
    }

    // property d2UsuCveAct
    @Test
    void testGetterSetterD2UsuCveAct() {

        // Property: det2.d2UsuCveAct -> 05 D2-USU-CVE-ACT X(08)
        assertNotNull(linea.getD2UsuCveAct());
        String vString = "ABCabc12";
        linea.setD2UsuCveAct(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        linea.setD2UsuCveAct(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d2FecUltAct
    @Test
    void testGetterSetterD2FecUltAct() {

        // Property: det2.d2FecUltAct -> 05 D2-FEC-ULT-ACT X(10)
        assertNotNull(linea.getD2FecUltAct());
        String vString = "ABCabc1230";
        linea.setD2FecUltAct(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD2FecUltAct(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d3FlaDes
    @Test
    void testGetterSetterD3FlaDes() {

        // Property: det22.d3FlaDes -> 05 D3-FLA-DES X(40)
        assertNotNull(linea.getD3FlaDes());
        String vString = "ABCabc1230";
        linea.setD3FlaDes(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD3FlaDes(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d3AgtTraEsp
    @Test
    void testGetterSetterD3AgtTraEsp() {

        // Property: det22.d3AgtTraEsp -> 05 D3-AGT-TRA-ESP X(1)
        assertNotNull(linea.getD3AgtTraEsp());
        String vString = "A";
        linea.setD3AgtTraEsp(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        linea.setD3AgtTraEsp(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d3CnaFecCnx
    @Test
    void testGetterSetterD3CnaFecCnx() {

        // Property: det22.d3CnaFecCnx -> 05 D3-CNA-FEC-CNX X(10)
        assertNotNull(linea.getD3CnaFecCnx());
        String vString = "ABCabc1230";
        linea.setD3CnaFecCnx(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD3CnaFecCnx(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d3GnaCve
    @Test
    void testGetterSetterD3GnaCve() {

        // Property: det22.d3GnaCve -> 05 D3-GNA-CVE ZZZ9
        assertNotNull(linea.getD3GnaCve());
        String vString = "ABCa";
        linea.setD3GnaCve(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD3GnaCve(vIValue); // IValue
    }

    // property d3EsaCveFla
    @Test
    void testGetterSetterD3EsaCveFla() {

        // Property: det22.d3EsaCveFla -> 05 D3-ESA-CVE-FLA ZZZ9
        assertNotNull(linea.getD3EsaCveFla());
        String vString = "ABCa";
        linea.setD3EsaCveFla(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD3EsaCveFla(vIValue); // IValue
    }

    // property d3FlaGerZon
    @Test
    void testGetterSetterD3FlaGerZon() {

        // Property: det22.d3FlaGerZon -> 05 D3-FLA-GER-ZON Z999
        assertNotNull(linea.getD3FlaGerZon());
        String vString = "ABCa";
        linea.setD3FlaGerZon(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD3FlaGerZon(vIValue); // IValue
    }

    // property d3OfnCveCna
    @Test
    void testGetterSetterD3OfnCveCna() {

        // Property: det22.d3OfnCveCna -> 05 D3-OFN-CVE-CNA ZZ99
        assertNotNull(linea.getD3OfnCveCna());
        String vString = "ABCa";
        linea.setD3OfnCveCna(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD3OfnCveCna(vIValue); // IValue
    }

    // property d3AgtIdrGte
    @Test
    void testGetterSetterD3AgtIdrGte() {

        // Property: det22.d3AgtIdrGte -> 05 D3-AGT-IDR-GTE 9(05)
        assertNotNull(linea.getD3AgtIdrGte());
        int vint = 12345;
        linea.setD3AgtIdrGte(vint); // int
        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        linea.setD3AgtIdrGte(vINumericValue); // INumericValue
    }

    // property det12Letrero
    @Test
    void testGetterSetterDet12Letrero() {

        // Property: det3.det12Letrero -> 10 DET12-LETRERO X(25)
        assertNotNull(linea.getDet12Letrero());
        String vString = "ABCabc1230";
        linea.setDet12Letrero(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setDet12Letrero(vIAlphanumericValue); // IAlphanumericValue
    }

    // property det12Total
    @Test
    void testGetterSetterDet12Total() {

        // Property: det3.det12Total -> 10 DET12-TOTAL ZZZZZZ9
        assertNotNull(linea.getDet12Total());
        String vString = "ABCabc1";
        linea.setDet12Total(vString); // String
        NumericVar vIValue = new NumericVar(7, 0);;
        vIValue.setValue("1234567");
        linea.setDet12Total(vIValue); // IValue
    }
}

