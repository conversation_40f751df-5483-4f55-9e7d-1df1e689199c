package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: STATUS-CODE.
 *
 */
class StatusCodeTest {

    private StatusCode statusCode;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        statusCode = new StatusCode();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        statusCode.setWsFile("");
        assertEquals("", statusCode.getWsFile());

    }

    // property wsFile
    @Test
    void testGetterSetterWsFile() {

        assertNotNull(statusCode.getWsFile());

        String vString = "AB";
        statusCode.setWsFile(vString); // String
        assertEquals(asStr(vString), asStr(statusCode.getWsFile()));
    }
}

