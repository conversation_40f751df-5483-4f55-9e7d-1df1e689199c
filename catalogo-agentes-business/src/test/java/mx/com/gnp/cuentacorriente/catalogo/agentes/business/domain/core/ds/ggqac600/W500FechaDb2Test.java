package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: W500-FECHA-DB2.
 *
 */
class W500FechaDb2Test {

    private W500FechaDb2 w500FechaDb2;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        w500FechaDb2 = new W500FechaDb2();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        W500FechaDb2 w500FechaDb22 = new W500FechaDb2(parent);
        assertNotNull(w500FechaDb22);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        W500FechaDb2 w500FechaDb23 = new W500FechaDb2(parent, sibling);
        assertNotNull(w500FechaDb23);
    }

    @Test
    void testInitialize() {
        assertNotNull(w500FechaDb2.getW500AnoDb2());

        assertNotNull(w500FechaDb2.getW500MesDb2());

        assertNotNull(w500FechaDb2.getW500DiaDb2());

    }

    // property w500AnoDb2
    @Test
    void testGetterSetterW500AnoDb2() {

        assertNotNull(w500FechaDb2.getW500AnoDb2());

        int vint = 1234;
        w500FechaDb2.setW500AnoDb2(vint); // int
        assertEquals(asInt(vint), asInt(w500FechaDb2.getW500AnoDb2()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        w500FechaDb2.setW500AnoDb2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(w500FechaDb2.getW500AnoDb2()));
    }

    // property w500MesDb2
    @Test
    void testGetterSetterW500MesDb2() {

        assertNotNull(w500FechaDb2.getW500MesDb2());

        int vint = 12;
        w500FechaDb2.setW500MesDb2(vint); // int
        assertEquals(asInt(vint), asInt(w500FechaDb2.getW500MesDb2()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        w500FechaDb2.setW500MesDb2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(w500FechaDb2.getW500MesDb2()));
    }

    // property w500DiaDb2
    @Test
    void testGetterSetterW500DiaDb2() {

        assertNotNull(w500FechaDb2.getW500DiaDb2());

        int vint = 12;
        w500FechaDb2.setW500DiaDb2(vint); // int
        assertEquals(asInt(vint), asInt(w500FechaDb2.getW500DiaDb2()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        w500FechaDb2.setW500DiaDb2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(w500FechaDb2.getW500DiaDb2()));
    }
}

