package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICADORES-AGT0.
 *
 */
class IndicadoresAgt0Test {

    private IndicadoresAgt0 indicadoresAgt0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indicadoresAgt0 = new IndicadoresAgt0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        indicadoresAgt0.setIndagtnal(0);
        assertEquals(0, indicadoresAgt0.getIndagtnal());

        indicadoresAgt0.setIndagtpef(0);
        assertEquals(0, indicadoresAgt0.getIndagtpef());

        indicadoresAgt0.setIndagttca(0);
        assertEquals(0, indicadoresAgt0.getIndagttca());

        indicadoresAgt0.setIndagtmte(0);
        assertEquals(0, indicadoresAgt0.getIndagtmte());

    }

    // property indagtnal
    @Test
    void testGetterSetterIndagtnal() {

        assertNotNull(indicadoresAgt0.getIndagtnal());

        int vint = 1234;
        indicadoresAgt0.setIndagtnal(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresAgt0.getIndagtnal()));
    }

    // property indagtpef
    @Test
    void testGetterSetterIndagtpef() {

        assertNotNull(indicadoresAgt0.getIndagtpef());

        int vint = 1234;
        indicadoresAgt0.setIndagtpef(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresAgt0.getIndagtpef()));
    }

    // property indagttca
    @Test
    void testGetterSetterIndagttca() {

        assertNotNull(indicadoresAgt0.getIndagttca());

        int vint = 1234;
        indicadoresAgt0.setIndagttca(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresAgt0.getIndagttca()));
    }

    // property indagtmte
    @Test
    void testGetterSetterIndagtmte() {

        assertNotNull(indicadoresAgt0.getIndagtmte());

        int vint = 1234;
        indicadoresAgt0.setIndagtmte(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresAgt0.getIndagtmte()));
    }
}

