package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.common.cbl.exception.GobackException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;
import com.base100.caravel.support.common.cbl.context.IProgramCblContext;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa002qcpy.LcevLinkReg;

import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

class Ggnf0700Test {
    Ggnf0700 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggnf0700.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);

        IProgramCblContext programCblContext = pgm.getProgramCblContext();
        doAnswer(invocation -> {
            String programName = invocation.getArgument(0);
            Object linkReg = invocation.getArgument(1);

            if (linkReg instanceof LcevLinkReg) {
                LcevLinkReg lbdeLinkReg = (LcevLinkReg) linkReg;
                lbdeLinkReg.setLcevSwErr("1");
            }

            if (programName.contains("GGNF0700") || programName.contains("0700")) {
                pgm.setFsFolios(0);
                pgm.setFsAgentes(0);
                pgm.setAgFinArch(1);
                if (pgm.getWsIndicadores() != null) {
                    pgm.getWsIndicadores().setWsFinAgc(1);
                }
            }

            return null;
        }).when(programCblContext).runProgram(anyString(), any());
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        pgm.getSqlca().setSqlcode(-1);
        pgm.setFsFolios(0);
        pgm.setFsAgentes(0);
        pgm.setAgFinArch(1);
        pgm.getWsIndicadores().setWsFinAgc(1);

        Exception exception = assertThrows(GobackException.class, () -> pgm.run());
        assertNotNull(exception);
    }

    /*
      p2000ProcesoFolios test conditions:
        fsFolios : [1, 0]
        fsAgentes : [1, 0]
        agFinArch : [2, 1]
        wsFinAgc : [0, 1]
    */
    @Test
    void p2000ProcesoFoliosTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setFsFolios(1);
        pgm.setFsAgentes(1);
        pgm.setAgFinArch(2);
        pgm.getWsIndicadores().setWsFinAgc(0);
        try {
            pgm.p2000ProcesoFolios();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p2000ProcesoFoliosTest2() {
        pgm.getSqlca().setSqlcode(-1);
        pgm.setFsFolios(0);
        pgm.setFsAgentes(0);
        pgm.setAgFinArch(1);
        pgm.getWsIndicadores().setWsFinAgc(1);
        try {
            pgm.p2000ProcesoFolios();
        } catch (Exception e) {
            assertExceptionInstanceOf(GobackException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3000ActualizaOAlta test conditions:
        agFinArch : [2, 1]
        wsFinAgc : [0, 1]
        fsAgentes : [0, 1]
        wsFinOrv : [0, 1]
        wsFinMte : [0, 1]
        wsFinCna : [0, 1]
        wsFinAgt : [0, 1]
        tbFla03 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla04 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla05 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla11 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla12 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla13 : ["XXLOW_VALUE", "LOW_VALUE"]
        fsFolios : [0, 1]
    */
    @Test
    void p3000ActualizaOAltaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setAgFinArch(2);
        pgm.getWsIndicadores().setWsFinAgc(0);
        pgm.setFsAgentes(0);
        pgm.getWsIndicadores().setWsFinOrv(0);
        pgm.getWsIndicadores().setWsFinMte(0);
        pgm.getWsIndicadores().setWsFinCna(0);
        pgm.getWsIndicadores().setWsFinAgt(0);
        pgm.getTbDclgfvtfla0().setTbFla03("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("XXLOW_VALUE");
        pgm.setFsFolios(0);
        try {
            pgm.p3000ActualizaOAlta();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3000ActualizaOAltaTest2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setAgFinArch(1);
        pgm.getWsIndicadores().setWsFinAgc(1);
        pgm.setFsAgentes(1);
        pgm.getWsIndicadores().setWsFinOrv(1);
        pgm.getWsIndicadores().setWsFinMte(1);
        pgm.getWsIndicadores().setWsFinCna(1);
        pgm.getWsIndicadores().setWsFinAgt(1);
        pgm.getTbDclgfvtfla0().setTbFla03("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("LOW_VALUE");
        pgm.setFsFolios(1);
        try {
            pgm.p3000ActualizaOAlta();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3100ActualizaGfvtagc0 test conditions:
        wsFinOrv : [0, 1]
        wsFinMte : [0, 1]
        wsFinCna : [0, 1]
        wsFinAgt : [0, 1]
        flaNum : ["P0011026", "XXP0011026"]
    */
    @Test
    void p3100ActualizaGfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinOrv(0);
        pgm.getWsIndicadores().setWsFinMte(0);
        pgm.getWsIndicadores().setWsFinCna(0);
        pgm.getWsIndicadores().setWsFinAgt(0);
        pgm.getDclgfvtfla0().setFlaNum("P0011026");
        try {
            pgm.p3100ActualizaGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3100ActualizaGfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinOrv(1);
        pgm.getWsIndicadores().setWsFinMte(1);
        pgm.getWsIndicadores().setWsFinCna(1);
        pgm.getWsIndicadores().setWsFinAgt(1);
        pgm.getDclgfvtfla0().setFlaNum("XXP0011026");
        try {
            pgm.p3100ActualizaGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3200Pregunta test conditions:
        flaNum : ["P0011026", "XXP0011026"]
    */
    @Test
    void p3200PreguntaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtfla0().setFlaNum("P0011026");
        try {
            pgm.p3200Pregunta();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3200PreguntaTest2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtfla0().setFlaNum("XXP0011026");
        try {
            pgm.p3200Pregunta();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3400UpdateGfvtagc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p3400UpdateGfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p3400UpdateGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3400UpdateGfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p3400UpdateGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3400AltaGfvtagc0 test conditions:
        wsFinMte : [0, 1]
        wsFinCna : [0, 1]
        wsFinAgt : [0, 1]
    */
    @Test
    void p3400AltaGfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinMte(0);
        pgm.getWsIndicadores().setWsFinCna(0);
        pgm.getWsIndicadores().setWsFinAgt(0);
        try {
            pgm.p3400AltaGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3400AltaGfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinMte(1);
        pgm.getWsIndicadores().setWsFinCna(1);
        pgm.getWsIndicadores().setWsFinAgt(1);
        try {
            pgm.p3400AltaGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3500InsertaGfvtagc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p3500InsertaGfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p3500InsertaGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3500InsertaGfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p3500InsertaGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3550ImpGfvtagc0 test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p3550ImpGfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p3550ImpGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3550ImpGfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(61);
        try {
            pgm.p3550ImpGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3600ArmaDclgfvtagc0 test conditions:
        wsFolioxx1n : ["N", "XXN"]
        wsFolioxx1p : ["P", "XXP"]
        wsFolioxx1 : ["N", "XXN"]
        wsFinCna1 : [0, 1]
    */
    @Test
    void p3600ArmaDclgfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFolioxx1n("N");
        pgm.getWsIndicadores().setWsFolioxx1p("P");
        pgm.getWsIndicadores().setWsFolioxx1("N");
        pgm.getWsIndicadores().setWsFinCna1(0);
        try {
            pgm.p3600ArmaDclgfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3600ArmaDclgfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFolioxx1n("XXN");
        pgm.getWsIndicadores().setWsFolioxx1p("XXP");
        pgm.getWsIndicadores().setWsFolioxx1("XXN");
        pgm.getWsIndicadores().setWsFinCna1(1);
        try {
            pgm.p3600ArmaDclgfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3700FolioAgente test conditions:
        wsFolioxx1n : ["N", "XXN"]
        wsFolioxx1p : ["P", "XXP"]
        wsFolioxx1 : ["N", "XXN"]
        wsFinCna1 : [0, 1]
        wsIndNal : [0, 1]
        wsFoliozz1 : ["N", "XXN"]
        wsIndProv : [0, 1]
    */
    @Test
    void p3700FolioAgenteTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFolioxx1n("N");
        pgm.getWsIndicadores().setWsFolioxx1p("P");
        pgm.getWsIndicadores().setWsFolioxx1("N");
        pgm.getWsIndicadores().setWsFinCna1(0);
        pgm.getWsRegistros().setWsIndNal(0);
        pgm.getWsIndicadores().setWsFoliozz1("N");
        pgm.getWsRegistros().setWsIndProv(0);
        try {
            pgm.p3700FolioAgente();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3700FolioAgenteTest2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFolioxx1n("XXN");
        pgm.getWsIndicadores().setWsFolioxx1p("XXP");
        pgm.getWsIndicadores().setWsFolioxx1("XXN");
        pgm.getWsIndicadores().setWsFinCna1(1);
        pgm.getWsRegistros().setWsIndNal(1);
        pgm.getWsIndicadores().setWsFoliozz1("XXN");
        pgm.getWsRegistros().setWsIndProv(1);
        try {
            pgm.p3700FolioAgente();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3400AccesaGfvtfla2 test conditions:
        wsIndNal : [0, 1]
        wsFoliozz1 : ["N", "XXN"]
        wsIndProv : [0, 1]
    */
    @Test
    void p3400AccesaGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsRegistros().setWsIndNal(0);
        pgm.getWsIndicadores().setWsFoliozz1("N");
        pgm.getWsRegistros().setWsIndProv(0);
        try {
            pgm.p3400AccesaGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3400AccesaGfvtfla2Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsRegistros().setWsIndNal(1);
        pgm.getWsIndicadores().setWsFoliozz1("XXN");
        pgm.getWsRegistros().setWsIndProv(1);
        try {
            pgm.p3400AccesaGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p6000Imprimir test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p6000ImprimirTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p6000Imprimir();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p6000ImprimirTest2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(61);
        try {
            pgm.p6000Imprimir();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p6100Enc1 test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p6100Enc1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p6100Enc1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p6100Enc1Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(61);
        try {
            pgm.p6100Enc1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7100LeerGfvtmte0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7100LeerGfvtmte0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7100LeerGfvtmte0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7100LeerGfvtmte0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7100LeerGfvtmte0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7100LeerGcctorv0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7100LeerGcctorv0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7100LeerGcctorv0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7100LeerGcctorv0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7100LeerGcctorv0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7100LeerGfvtagt0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7100LeerGfvtagt0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7100LeerGfvtagt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7100LeerGfvtagt0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7100LeerGfvtagt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7100LeerGfvtcna0 test conditions:
        tbFlaNum : ["N", "XXN"]
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7100LeerGfvtcna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtfla0().setTbFlaNum("N");
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7100LeerGfvtcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7100LeerGfvtcna0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtfla0().setTbFlaNum("XXN");
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7100LeerGfvtcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7100LeerGfvtdrc0 test conditions:
        tbFlaNum : ["N", "XXN"]
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7100LeerGfvtdrc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtfla0().setTbFlaNum("N");
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7100LeerGfvtdrc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7100LeerGfvtdrc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtfla0().setTbFlaNum("XXN");
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7100LeerGfvtdrc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7100LeerGfvtagc0 test conditions:
        fsAgentes : [0, 1]
        ttAgc02 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc03 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc04 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc05 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc06 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc07 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc08 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc09 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc10 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc11 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc12 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc13 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc14 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc15 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc16 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc17 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc18 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc20 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc21 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc22 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc23 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc24 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc25 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc26 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc27 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc28 : ["XXLOW_VALUE", "LOW_VALUE"]
    */
    @Test
    void p7100LeerGfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setFsAgentes(0);
        pgm.getTbDclgfvtagc0().setTtAgc02("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc03("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc04("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc05("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc06("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc07("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc08("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc09("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc10("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc11("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc12("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc13("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc14("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc15("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc16("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc17("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc18("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc20("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc21("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc22("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc23("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc24("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc25("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc26("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc27("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc28("XXLOW_VALUE");
        try {
            pgm.p7100LeerGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7100LeerGfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setFsAgentes(1);
        pgm.getTbDclgfvtagc0().setTtAgc02("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc03("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc04("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc05("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc06("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc07("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc08("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc09("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc10("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc11("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc12("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc13("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc14("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc15("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc16("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc17("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc18("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc20("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc21("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc22("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc23("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc24("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc25("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc26("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc27("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc28("LOW_VALUE");
        try {
            pgm.p7100LeerGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7101MueveGfvtagc0 test conditions:
        ttAgc02 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc03 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc04 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc05 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc06 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc07 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc08 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc09 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc10 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc11 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc12 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc13 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc14 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc15 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc16 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc17 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc18 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc20 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc21 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc22 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc23 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc24 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc25 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc26 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc27 : ["XXLOW_VALUE", "LOW_VALUE"]
        ttAgc28 : ["XXLOW_VALUE", "LOW_VALUE"]
    */
    @Test
    void p7101MueveGfvtagc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtagc0().setTtAgc02("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc03("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc04("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc05("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc06("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc07("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc08("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc09("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc10("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc11("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc12("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc13("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc14("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc15("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc16("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc17("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc18("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc20("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc21("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc22("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc23("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc24("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc25("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc26("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc27("XXLOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc28("XXLOW_VALUE");
        try {
            pgm.p7101MueveGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7101MueveGfvtagc0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtagc0().setTtAgc02("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc03("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc04("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc05("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc06("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc07("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc08("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc09("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc10("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc11("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc12("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc13("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc14("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc15("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc16("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc17("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc18("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc20("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc21("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc22("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc23("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc24("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc25("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc26("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc27("LOW_VALUE");
        pgm.getTbDclgfvtagc0().setTtAgc28("LOW_VALUE");
        try {
            pgm.p7101MueveGfvtagc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7850InicioGfvcfla0 test conditions:
        fsFolios : [1, 0]
        fsAgentes : [1, 0]
        tbFla03 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla04 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla05 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla11 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla12 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla13 : ["XXLOW_VALUE", "LOW_VALUE"]
    */
    @Test
    void p7850InicioGfvcfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setFsFolios(1);
        pgm.setFsAgentes(1);
        pgm.getTbDclgfvtfla0().setTbFla03("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("XXLOW_VALUE");
        try {
            pgm.p7850InicioGfvcfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7850InicioGfvcfla0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.setFsFolios(0);
        pgm.setFsAgentes(0);
        pgm.getTbDclgfvtfla0().setTbFla03("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("LOW_VALUE");
        try {
            pgm.p7850InicioGfvcfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7850LeerGfvtfla0 test conditions:
        tbFla03 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla04 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla05 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla11 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla12 : ["XXLOW_VALUE", "LOW_VALUE"]
        tbFla13 : ["XXLOW_VALUE", "LOW_VALUE"]
        fsFolios : [0, 1]
    */
    @Test
    void p7850LeerGfvtfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtfla0().setTbFla03("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("XXLOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("XXLOW_VALUE");
        pgm.setFsFolios(0);
        try {
            pgm.p7850LeerGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7850LeerGfvtfla0Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getTbDclgfvtfla0().setTbFla03("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla04("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla05("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla11("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla12("LOW_VALUE");
        pgm.getTbDclgfvtfla0().setTbFla13("LOW_VALUE");
        pgm.setFsFolios(1);
        try {
            pgm.p7850LeerGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7960InicioGfvtcna1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7960InicioGfvtcna1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7960InicioGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7960InicioGfvtcna1Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7960InicioGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7960LeerGfvtcna1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7960LeerGfvtcna1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7960LeerGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7960LeerGfvtcna1Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7960LeerGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7960CerrarGfvtcna1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7960CerrarGfvtcna1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7960CerrarGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7960CerrarGfvtcna1Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7960CerrarGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7970InicioGfvtfla2 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7970InicioGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7970InicioGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7970InicioGfvtfla2Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7970InicioGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7970LeerGfvtfla2 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7970LeerGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7970LeerGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7970LeerGfvtfla2Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7970LeerGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p7970CerrarGfvtfla2 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7970CerrarGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7970CerrarGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p7970CerrarGfvtfla2Test2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p7970CerrarGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      db2check test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void db2checkTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.db2check();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void db2checkTest2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.db2check();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p9999AnalizaSql test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p9999AnalizaSqlTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p9999AnalizaSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p9999AnalizaSqlTest2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getSqlca().setSqlerrml(0);
        pgm.getSqlca().setSqlwarn6("XXW");
        pgm.getSqlca().setSqlwarn9("XXW");
        pgm.getSqlca().setSqlwarn5("XXW");
        try {
            pgm.p9999AnalizaSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    /*
      p3000Fin test conditions:
        contLineas : [60, 61]
    */
    @Test
    void p3000FinTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(60);
        try {
            pgm.p3000Fin();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void p3000FinTest2() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setContLineas(61);
        try {
            pgm.p3000Fin();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
}
