package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: BANDERAS.
 *
 */
class BanderasTest {

    private Banderas banderas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        banderas = new Banderas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property banderasGlobales
    @Test
    void testGetterSetterBanderasGlobales() {

        assertNotNull(banderas.getBanderasGlobales());
    }

    // property wkDisp
    @Test
    void testGetterSetterWkDisp() {

        // Property: banderasGlobales.wkDisp -> 03 WK-DISP 9(01)
        assertNotNull(banderas.getWkDisp());
        int vint = 1;
        banderas.setWkDisp(vint); // int
    }

    // property wkDisp1
    @Test
    void testGetterSetterWkDisp1() {

        // Property: banderasGlobales.wkDisp1 -> 03 WK-DISP1 9(01)
        assertNotNull(banderas.getWkDisp1());
        int vint = 1;
        banderas.setWkDisp1(vint); // int
    }

    // property stExtraccion
    @Test
    void testGetterSetterStExtraccion() {

        // Property: banderasGlobales.stExtraccion -> 03 ST-EXTRACCION X(02)
        assertNotNull(banderas.getStExtraccion());
        String vString = "AB";
        banderas.setStExtraccion(vString); // String
    }

    // property stFacturacion
    @Test
    void testGetterSetterStFacturacion() {

        // Property: banderasGlobales.stFacturacion -> 03 ST-FACTURACION X(02)
        assertNotNull(banderas.getStFacturacion());
        String vString = "AB";
        banderas.setStFacturacion(vString); // String
    }

    // property stDetSusp
    @Test
    void testGetterSetterStDetSusp() {

        // Property: banderasGlobales.stDetSusp -> 03 ST-DET-SUSP X(02)
        assertNotNull(banderas.getStDetSusp());
        String vString = "AB";
        banderas.setStDetSusp(vString); // String
    }

    // property stCatDirec
    @Test
    void testGetterSetterStCatDirec() {

        // Property: banderasGlobales.stCatDirec -> 03 ST-CAT-DIREC X(02)
        assertNotNull(banderas.getStCatDirec());
        String vString = "AB";
        banderas.setStCatDirec(vString); // String
    }

    // property wsSt
    @Test
    void testGetterSetterWsSt() {

        // Property: banderasGlobales.wsSt -> 03 WS-ST X(02)
        assertNotNull(banderas.getWsSt());
        String vString = "AB";
        banderas.setWsSt(vString); // String
    }

    // property wsFinExtraccion
    @Test
    void testGetterSetterWsFinExtraccion() {

        // Property: banderasGlobales.wsFinExtraccion -> 03 WS-FIN-EXTRACCION 9(01)
        assertNotNull(banderas.getWsFinExtraccion());
        int vint = 1;
        banderas.setWsFinExtraccion(vint); // int
    }

    // property wsFinCatDirecc
    @Test
    void testGetterSetterWsFinCatDirecc() {

        // Property: banderasGlobales.wsFinCatDirecc -> 03 WS-FIN-CAT-DIRECC 9(01)
        assertNotNull(banderas.getWsFinCatDirecc());
        int vint = 1;
        banderas.setWsFinCatDirecc(vint); // int
    }

    // property wsFinCurMte
    @Test
    void testGetterSetterWsFinCurMte() {

        // Property: banderasGlobales.wsFinCurMte -> 03 WS-FIN-CUR-MTE 9(01)
        assertNotNull(banderas.getWsFinCurMte());
        int vint = 1;
        banderas.setWsFinCurMte(vint); // int
    }

    // property wsFinCurEsa
    @Test
    void testGetterSetterWsFinCurEsa() {

        // Property: banderasGlobales.wsFinCurEsa -> 03 WS-FIN-CUR-ESA 9(01)
        assertNotNull(banderas.getWsFinCurEsa());
        int vint = 1;
        banderas.setWsFinCurEsa(vint); // int
    }

    // property wsEncDirecc
    @Test
    void testGetterSetterWsEncDirecc() {

        // Property: banderasGlobales.wsEncDirecc -> 03 WS-ENC-DIRECC 9(01)
        assertNotNull(banderas.getWsEncDirecc());
        int vint = 1;
        banderas.setWsEncDirecc(vint); // int
    }

    // property wsEncAgente
    @Test
    void testGetterSetterWsEncAgente() {

        // Property: banderasGlobales.wsEncAgente -> 03 WS-ENC-AGENTE 9(01)
        assertNotNull(banderas.getWsEncAgente());
        int vint = 1;
        banderas.setWsEncAgente(vint); // int
    }

    // property wsEncCna
    @Test
    void testGetterSetterWsEncCna() {

        // Property: banderasGlobales.wsEncCna -> 03 WS-ENC-CNA 9(01)
        assertNotNull(banderas.getWsEncCna());
        int vint = 1;
        banderas.setWsEncCna(vint); // int
    }

    // property wsEncDsc
    @Test
    void testGetterSetterWsEncDsc() {

        // Property: banderasGlobales.wsEncDsc -> 03 WS-ENC-DSC 9(01)
        assertNotNull(banderas.getWsEncDsc());
        int vint = 1;
        banderas.setWsEncDsc(vint); // int
    }

    // property wsEncEstatus
    @Test
    void testGetterSetterWsEncEstatus() {

        // Property: banderasGlobales.wsEncEstatus -> 03 WS-ENC-ESTATUS 9(01)
        assertNotNull(banderas.getWsEncEstatus());
        int vint = 1;
        banderas.setWsEncEstatus(vint); // int
    }

    // property wsEncEstatusCna
    @Test
    void testGetterSetterWsEncEstatusCna() {

        // Property: banderasGlobales.wsEncEstatusCna -> 03 WS-ENC-ESTATUS-CNA 9(01)
        assertNotNull(banderas.getWsEncEstatusCna());
        int vint = 1;
        banderas.setWsEncEstatusCna(vint); // int
    }

    // property wsEncExcluido
    @Test
    void testGetterSetterWsEncExcluido() {

        // Property: banderasGlobales.wsEncExcluido -> 03 WS-ENC-EXCLUIDO 9(01)
        assertNotNull(banderas.getWsEncExcluido());
        int vint = 1;
        banderas.setWsEncExcluido(vint); // int
    }

    // property wsAgenteEstatus
    @Test
    void testGetterSetterWsAgenteEstatus() {

        // Property: banderasGlobales.wsAgenteEstatus -> 03 WS-AGENTE-ESTATUS 9(04)
        assertNotNull(banderas.getWsAgenteEstatus());
        int vint = 1234;
        banderas.setWsAgenteEstatus(vint); // int
    }

    // property wsCnaEstatus
    @Test
    void testGetterSetterWsCnaEstatus() {

        // Property: banderasGlobales.wsCnaEstatus -> 03 WS-CNA-ESTATUS 9(04)
        assertNotNull(banderas.getWsCnaEstatus());
        int vint = 1234;
        banderas.setWsCnaEstatus(vint); // int
    }

    // property wsFinCurDma
    @Test
    void testGetterSetterWsFinCurDma() {

        // Property: banderasGlobales.wsFinCurDma -> 03 WS-FIN-CUR-DMA 9(01)
        assertNotNull(banderas.getWsFinCurDma());
        int vint = 1;
        banderas.setWsFinCurDma(vint); // int
    }

    // property wsEncGfvtagt
    @Test
    void testGetterSetterWsEncGfvtagt() {

        // Property: banderasGlobales.wsEncGfvtagt -> 03 WS-ENC-GFVTAGT 9(01)
        assertNotNull(banderas.getWsEncGfvtagt());
        int vint = 1;
        banderas.setWsEncGfvtagt(vint); // int
    }

    // property wsEncGfvtcna
    @Test
    void testGetterSetterWsEncGfvtcna() {

        // Property: banderasGlobales.wsEncGfvtcna -> 03 WS-ENC-GFVTCNA 9(01)
        assertNotNull(banderas.getWsEncGfvtcna());
        int vint = 1;
        banderas.setWsEncGfvtcna(vint); // int
    }

    // property wsEncGfvtpra
    @Test
    void testGetterSetterWsEncGfvtpra() {

        // Property: banderasGlobales.wsEncGfvtpra -> 03 WS-ENC-GFVTPRA 9(01)
        assertNotNull(banderas.getWsEncGfvtpra());
        int vint = 1;
        banderas.setWsEncGfvtpra(vint); // int
    }

    // property wsEncGcctmnd
    @Test
    void testGetterSetterWsEncGcctmnd() {

        // Property: banderasGlobales.wsEncGcctmnd -> 03 WS-ENC-GCCTMND 9(01)
        assertNotNull(banderas.getWsEncGcctmnd());
        int vint = 1;
        banderas.setWsEncGcctmnd(vint); // int
    }

    // property wsEncGcctpob
    @Test
    void testGetterSetterWsEncGcctpob() {

        // Property: banderasGlobales.wsEncGcctpob -> 03 WS-ENC-GCCTPOB 9(01)
        assertNotNull(banderas.getWsEncGcctpob());
        int vint = 1;
        banderas.setWsEncGcctpob(vint); // int
    }

    // property wsEncGcctedo
    @Test
    void testGetterSetterWsEncGcctedo() {

        // Property: banderasGlobales.wsEncGcctedo -> 03 WS-ENC-GCCTEDO 9(01)
        assertNotNull(banderas.getWsEncGcctedo());
        int vint = 1;
        banderas.setWsEncGcctedo(vint); // int
    }

    // property wsEncGcctpai
    @Test
    void testGetterSetterWsEncGcctpai() {

        // Property: banderasGlobales.wsEncGcctpai -> 03 WS-ENC-GCCTPAI 9(01)
        assertNotNull(banderas.getWsEncGcctpai());
        int vint = 1;
        banderas.setWsEncGcctpai(vint); // int
    }

    // property wsEncDirec
    @Test
    void testGetterSetterWsEncDirec() {

        // Property: banderasGlobales.wsEncDirec -> 03 WS-ENC-DIREC 9(01)
        assertNotNull(banderas.getWsEncDirec());
        int vint = 1;
        banderas.setWsEncDirec(vint); // int
    }

    // property wsEncGfvtcma
    @Test
    void testGetterSetterWsEncGfvtcma() {

        // Property: banderasGlobales.wsEncGfvtcma -> 03 WS-ENC-GFVTCMA 9(01)
        assertNotNull(banderas.getWsEncGfvtcma());
        int vint = 1;
        banderas.setWsEncGfvtcma(vint); // int
    }

    // property wsEncTrn
    @Test
    void testGetterSetterWsEncTrn() {

        // Property: banderasGlobales.wsEncTrn -> 03 WS-ENC-TRN 9(01)
        assertNotNull(banderas.getWsEncTrn());
        int vint = 1;
        banderas.setWsEncTrn(vint); // int
    }

    // property wsEncConcepto
    @Test
    void testGetterSetterWsEncConcepto() {

        // Property: banderasGlobales.wsEncConcepto -> 03 WS-ENC-CONCEPTO 9(01)
        assertNotNull(banderas.getWsEncConcepto());
        int vint = 1;
        banderas.setWsEncConcepto(vint); // int
    }

    // property wsSinMovs
    @Test
    void testGetterSetterWsSinMovs() {

        // Property: banderasGlobales.wsSinMovs -> 03 WS-SIN-MOVS 9(01)
        assertNotNull(banderas.getWsSinMovs());
        int vint = 1;
        banderas.setWsSinMovs(vint); // int
    }
}

