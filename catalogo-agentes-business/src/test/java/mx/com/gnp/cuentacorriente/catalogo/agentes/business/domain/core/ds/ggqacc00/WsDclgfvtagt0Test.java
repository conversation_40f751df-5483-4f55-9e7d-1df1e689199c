package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGFVTAGT0.
 *
 */
class WsDclgfvtagt0Test {

    private WsDclgfvtagt0 wsDclgfvtagt0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgfvtagt0 = new WsDclgfvtagt0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgfvtagt0.setAgtNomConcatenado("");
        assertEquals("", wsDclgfvtagt0.getAgtNomConcatenado());

        wsDclgfvtagt0.setAgtNalCve("");
        assertEquals("", wsDclgfvtagt0.getAgtNalCve());

        wsDclgfvtagt0.setAgtPefCve(0);
        assertEquals(0, wsDclgfvtagt0.getAgtPefCve());

        wsDclgfvtagt0.setAgtTcaCve("");
        assertEquals("", wsDclgfvtagt0.getAgtTcaCve());

        wsDclgfvtagt0.setAgtMteCve(0);
        assertEquals(0, wsDclgfvtagt0.getAgtMteCve());

    }

    // property agtNomConcatenado
    @Test
    void testGetterSetterAgtNomConcatenado() {

        assertNotNull(wsDclgfvtagt0.getAgtNomConcatenado());

        String vString = "ABCabc1230";
        wsDclgfvtagt0.setAgtNomConcatenado(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtagt0.getAgtNomConcatenado()));
    }

    // property agtNalCve
    @Test
    void testGetterSetterAgtNalCve() {

        assertNotNull(wsDclgfvtagt0.getAgtNalCve());

        String vString = "ABC";
        wsDclgfvtagt0.setAgtNalCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtagt0.getAgtNalCve()));
    }

    // property agtPefCve
    @Test
    void testGetterSetterAgtPefCve() {

        assertNotNull(wsDclgfvtagt0.getAgtPefCve());

        int vint = 1234;
        wsDclgfvtagt0.setAgtPefCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtagt0.getAgtPefCve()));
    }

    // property agtTcaCve
    @Test
    void testGetterSetterAgtTcaCve() {

        assertNotNull(wsDclgfvtagt0.getAgtTcaCve());

        String vString = "AB";
        wsDclgfvtagt0.setAgtTcaCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtagt0.getAgtTcaCve()));
    }

    // property agtMteCve
    @Test
    void testGetterSetterAgtMteCve() {

        assertNotNull(wsDclgfvtagt0.getAgtMteCve());

        int vint = 1234;
        wsDclgfvtagt0.setAgtMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtagt0.getAgtMteCve()));
    }
}

