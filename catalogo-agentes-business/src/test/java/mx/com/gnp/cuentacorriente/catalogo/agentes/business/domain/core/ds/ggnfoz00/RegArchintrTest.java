package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-ARCHINTR.
 *
 */
class RegArchintrTest {

    private RegArchintr regArchintr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regArchintr = new RegArchintr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegArchintr regArchintr2 = new RegArchintr(parent);
        assertNotNull(regArchintr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 28);
        RegArchintr regArchintr3 = new RegArchintr(parent, sibling);
        assertNotNull(regArchintr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regArchintr.getSalAgtIdr());

        assertNotNull(regArchintr.getSalTfpCvev());

        assertNotNull(regArchintr.getSalTfpCven());

        assertNotNull(regArchintr.getSalAgtFecCamRazSo());

        assertNotNull(regArchintr.getSalFlaNum());

    }

    // property salAgtIdr
    @Test
    void testGetterSetterSalAgtIdr() {

        assertNotNull(regArchintr.getSalAgtIdr());

        String vString = "ABCab";
        regArchintr.setSalAgtIdr(vString); // String
        assertEquals(asStr(vString), asStr(regArchintr.getSalAgtIdr()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regArchintr.setSalAgtIdr(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchintr.getSalAgtIdr()));
    }

    // property salTfpCvev
    @Test
    void testGetterSetterSalTfpCvev() {

        assertNotNull(regArchintr.getSalTfpCvev());

        String vString = "ABC";
        regArchintr.setSalTfpCvev(vString); // String
        assertEquals(asStr(vString), asStr(regArchintr.getSalTfpCvev()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regArchintr.setSalTfpCvev(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchintr.getSalTfpCvev()));
    }

    // property salTfpCven
    @Test
    void testGetterSetterSalTfpCven() {

        assertNotNull(regArchintr.getSalTfpCven());

        String vString = "ABC";
        regArchintr.setSalTfpCven(vString); // String
        assertEquals(asStr(vString), asStr(regArchintr.getSalTfpCven()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regArchintr.setSalTfpCven(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchintr.getSalTfpCven()));
    }

    // property salAgtFecCamRazSo
    @Test
    void testGetterSetterSalAgtFecCamRazSo() {

        assertNotNull(regArchintr.getSalAgtFecCamRazSo());

        String vString = "ABCabc1230";
        regArchintr.setSalAgtFecCamRazSo(vString); // String
        assertEquals(asStr(vString), asStr(regArchintr.getSalAgtFecCamRazSo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchintr.setSalAgtFecCamRazSo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchintr.getSalAgtFecCamRazSo()));
    }

    // property salFlaNum
    @Test
    void testGetterSetterSalFlaNum() {

        assertNotNull(regArchintr.getSalFlaNum());

        String vString = "ABCabc1";
        regArchintr.setSalFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(regArchintr.getSalFlaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        regArchintr.setSalFlaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchintr.getSalFlaNum()));
    }
}

