package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablacna0;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-CNA0.
 *
 */
class TCna0Test {

    private TCna0 tCna0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tCna0 = new TCna0(1);
        tCna0.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TCna0 tCna02 = new TCna0(parent, 1);
        assertNotNull(tCna02);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TCna0 tCna03 = new TCna0(parent, sibling, 1);
        assertNotNull(tCna03);
    }

    @Test
    void testInitialize() {

    }

    // property tCna0Elem
    @Test
    void testGetterSetterTCna0Elem() {

        assertNotNull(tCna0.getTCna0Elem());
    }

    // property sCnaCnaNum
    @Test
    void testGetterSetterSCnaCnaNum() {

        // Property: tCna0Elem.sCnaCnaNum -> 20 S-CNA-CNA-NUM X(02)
        assertNotNull(tCna0.getSCnaCnaNum());
        String vString = "AB";
        tCna0.setSCnaCnaNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        tCna0.setSCnaCnaNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sCnaLimitad
    @Test
    void testGetterSetterSCnaLimitad() {

        // Property: tCna0Elem.sCnaLimitad -> 20 S-CNA-LIMITAD X(01)
        assertNotNull(tCna0.getSCnaLimitad());
        String vString = "A";
        tCna0.setSCnaLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tCna0.setSCnaLimitad(vIAlphanumericValue); // IAlphanumericValue
    }
}

