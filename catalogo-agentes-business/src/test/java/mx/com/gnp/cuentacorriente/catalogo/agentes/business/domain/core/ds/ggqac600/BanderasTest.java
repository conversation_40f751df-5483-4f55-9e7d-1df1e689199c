package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: BANDERAS.
 *
 */
class BanderasTest {

    private Banderas banderas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        banderas = new Banderas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property banderasGlobales
    @Test
    void testGetterSetterBanderasGlobales() {

        assertNotNull(banderas.getBanderasGlobales());
    }

    // property stExcAgt
    @Test
    void testGetterSetterStExcAgt() {

        // Property: banderasGlobales.stExcAgt -> 03 ST-EXC-AGT X(02)
        assertNotNull(banderas.getStExcAgt());
        String vString = "AB";
        banderas.setStExcAgt(vString); // String
    }

    // property wsFinAgtExc
    @Test
    void testGetterSetterWsFinAgtExc() {

        // Property: banderasGlobales.wsFinAgtExc -> 03 WS-FIN-AGT-EXC 9(01)
        assertNotNull(banderas.getWsFinAgtExc());
        int vint = 1;
        banderas.setWsFinAgtExc(vint); // int
    }

    // property wsEncAgtExc
    @Test
    void testGetterSetterWsEncAgtExc() {

        // Property: banderasGlobales.wsEncAgtExc -> 03 WS-ENC-AGT-EXC 9(01)
        assertNotNull(banderas.getWsEncAgtExc());
        int vint = 1;
        banderas.setWsEncAgtExc(vint); // int
    }
}

