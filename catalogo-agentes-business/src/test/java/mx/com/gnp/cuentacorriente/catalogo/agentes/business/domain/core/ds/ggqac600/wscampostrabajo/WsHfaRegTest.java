package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-HFA-REG.
 *
 */
class WsHfaRegTest {

    private WsHfaReg wsHfaReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsHfaReg = new WsHfaReg();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsHfaReg wsHfaReg2 = new WsHfaReg(parent);
        assertNotNull(wsHfaReg2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 733);
        WsHfaReg wsHfaReg3 = new WsHfaReg(parent, sibling);
        assertNotNull(wsHfaReg3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsHfaReg.getWsHfaAgtIdr());

        assertNotNull(wsHfaReg.getWsHfaFecIniPdo());

        assertNotNull(wsHfaReg.getWsHfaFecFinPdo());

        assertNotNull(wsHfaReg.getWsHfaEsaCve());

        assertNotNull(wsHfaReg.getWsHfaPefCve());

        assertNotNull(wsHfaReg.getWsHfaOfnCve());

        assertNotNull(wsHfaReg.getWsHfaCnaGerZonEnv());

        assertNotNull(wsHfaReg.getWsHfaDsgCve());

        assertNotNull(wsHfaReg.getWsHfaAgtRfc());

        assertNotNull(wsHfaReg.getWsHfaNomEmi());

        assertNotNull(wsHfaReg.getWsHfaDmaCae());

        assertNotNull(wsHfaReg.getWsHfaDmaNum());

        assertNotNull(wsHfaReg.getWsHfaDmaInt());

        assertNotNull(wsHfaReg.getWsHfaDmaCol());

        assertNotNull(wsHfaReg.getWsHfaMndNom());

        assertNotNull(wsHfaReg.getWsHfaEdoNom());

        assertNotNull(wsHfaReg.getWsHfaPaiNom());

        assertNotNull(wsHfaReg.getWsHfaCpoCve());

        assertNotNull(wsHfaReg.getWsHfaMteCve());

        assertNotNull(wsHfaReg.getWsHfaTfpCve());

        assertNotNull(wsHfaReg.getWsHfaCtbNumCta());

        assertNotNull(wsHfaReg.getWsHfaIndPag());

        assertNotNull(wsHfaReg.getWsHfaIndExc());

        assertNotNull(wsHfaReg.getWsHfaUsuCve());

        assertNotNull(wsHfaReg.getWsHfaNomPrg());

        assertNotNull(wsHfaReg.getWsHfaFecUltAct());

        assertNotNull(wsHfaReg.getWsHfaPjeIva());

        assertNotNull(wsHfaReg.getWsHfaPjeIsr());

        assertNotNull(wsHfaReg.getWsHfaCdregfis());

        assertNotNull(wsHfaReg.getWsHfaNomrznso());

        assertNotNull(wsHfaReg.getWsHfaCedNovi());

        assertNotNull(wsHfaReg.getWsHfaCedVida());

    }

    // property wsHfaAgtIdr
    @Test
    void testGetterSetterWsHfaAgtIdr() {

        assertNotNull(wsHfaReg.getWsHfaAgtIdr());

        int vint = 123456789;
        wsHfaReg.setWsHfaAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaAgtIdr()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsHfaReg.setWsHfaAgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaAgtIdr()));
    }

    // property wsHfaFecIniPdo
    @Test
    void testGetterSetterWsHfaFecIniPdo() {

        assertNotNull(wsHfaReg.getWsHfaFecIniPdo());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaFecIniPdo(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaFecIniPdo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaFecIniPdo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaFecIniPdo()));
    }

    // property wsHfaFecFinPdo
    @Test
    void testGetterSetterWsHfaFecFinPdo() {

        assertNotNull(wsHfaReg.getWsHfaFecFinPdo());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaFecFinPdo(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaFecFinPdo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaFecFinPdo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaFecFinPdo()));
    }

    // property wsHfaEsaCve
    @Test
    void testGetterSetterWsHfaEsaCve() {

        assertNotNull(wsHfaReg.getWsHfaEsaCve());

        int vint = 1234;
        wsHfaReg.setWsHfaEsaCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaEsaCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfaReg.setWsHfaEsaCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaEsaCve()));
    }

    // property wsHfaPefCve
    @Test
    void testGetterSetterWsHfaPefCve() {

        assertNotNull(wsHfaReg.getWsHfaPefCve());

        int vint = 1234;
        wsHfaReg.setWsHfaPefCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaPefCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfaReg.setWsHfaPefCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaPefCve()));
    }

    // property wsHfaOfnCve
    @Test
    void testGetterSetterWsHfaOfnCve() {

        assertNotNull(wsHfaReg.getWsHfaOfnCve());

        int vint = 1234;
        wsHfaReg.setWsHfaOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaOfnCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfaReg.setWsHfaOfnCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaOfnCve()));
    }

    // property wsHfaCnaGerZonEnv
    @Test
    void testGetterSetterWsHfaCnaGerZonEnv() {

        assertNotNull(wsHfaReg.getWsHfaCnaGerZonEnv());

        int vint = 1234;
        wsHfaReg.setWsHfaCnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaCnaGerZonEnv()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfaReg.setWsHfaCnaGerZonEnv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaCnaGerZonEnv()));
    }

    // property wsHfaDsgCve
    @Test
    void testGetterSetterWsHfaDsgCve() {

        assertNotNull(wsHfaReg.getWsHfaDsgCve());

        int vint = 1234;
        wsHfaReg.setWsHfaDsgCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaDsgCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfaReg.setWsHfaDsgCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaDsgCve()));
    }

    // property wsHfaAgtRfc
    @Test
    void testGetterSetterWsHfaAgtRfc() {

        assertNotNull(wsHfaReg.getWsHfaAgtRfc());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaAgtRfc(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaAgtRfc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaAgtRfc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaAgtRfc()));
    }

    // property wsHfaNomEmi
    @Test
    void testGetterSetterWsHfaNomEmi() {

        assertNotNull(wsHfaReg.getWsHfaNomEmi());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaNomEmi(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaNomEmi()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaNomEmi(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaNomEmi()));
    }

    // property wsHfaDmaCae
    @Test
    void testGetterSetterWsHfaDmaCae() {

        assertNotNull(wsHfaReg.getWsHfaDmaCae());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaDmaCae(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaDmaCae()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaDmaCae(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaDmaCae()));
    }

    // property wsHfaDmaNum
    @Test
    void testGetterSetterWsHfaDmaNum() {

        assertNotNull(wsHfaReg.getWsHfaDmaNum());

        String vString = "ABCab";
        wsHfaReg.setWsHfaDmaNum(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaDmaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsHfaReg.setWsHfaDmaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaDmaNum()));
    }

    // property wsHfaDmaInt
    @Test
    void testGetterSetterWsHfaDmaInt() {

        assertNotNull(wsHfaReg.getWsHfaDmaInt());

        String vString = "ABCab";
        wsHfaReg.setWsHfaDmaInt(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaDmaInt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsHfaReg.setWsHfaDmaInt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaDmaInt()));
    }

    // property wsHfaDmaCol
    @Test
    void testGetterSetterWsHfaDmaCol() {

        assertNotNull(wsHfaReg.getWsHfaDmaCol());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaDmaCol(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaDmaCol()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaDmaCol(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaDmaCol()));
    }

    // property wsHfaMndNom
    @Test
    void testGetterSetterWsHfaMndNom() {

        assertNotNull(wsHfaReg.getWsHfaMndNom());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaMndNom(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaMndNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaMndNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaMndNom()));
    }

    // property wsHfaEdoNom
    @Test
    void testGetterSetterWsHfaEdoNom() {

        assertNotNull(wsHfaReg.getWsHfaEdoNom());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaEdoNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaEdoNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaEdoNom()));
    }

    // property wsHfaPaiNom
    @Test
    void testGetterSetterWsHfaPaiNom() {

        assertNotNull(wsHfaReg.getWsHfaPaiNom());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaPaiNom(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaPaiNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaPaiNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaPaiNom()));
    }

    // property wsHfaCpoCve
    @Test
    void testGetterSetterWsHfaCpoCve() {

        assertNotNull(wsHfaReg.getWsHfaCpoCve());

        int vint = 123456789;
        wsHfaReg.setWsHfaCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaCpoCve()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsHfaReg.setWsHfaCpoCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaCpoCve()));
    }

    // property wsHfaMteCve
    @Test
    void testGetterSetterWsHfaMteCve() {

        assertNotNull(wsHfaReg.getWsHfaMteCve());

        int vint = 1234;
        wsHfaReg.setWsHfaMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaMteCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfaReg.setWsHfaMteCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaMteCve()));
    }

    // property wsHfaTfpCve
    @Test
    void testGetterSetterWsHfaTfpCve() {

        assertNotNull(wsHfaReg.getWsHfaTfpCve());

        int vint = 1234;
        wsHfaReg.setWsHfaTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaTfpCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfaReg.setWsHfaTfpCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaTfpCve()));
    }

    // property wsHfaCtbNumCta
    @Test
    void testGetterSetterWsHfaCtbNumCta() {

        assertNotNull(wsHfaReg.getWsHfaCtbNumCta());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaCtbNumCta(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaCtbNumCta()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaCtbNumCta(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaCtbNumCta()));
    }

    // property wsHfaIndPag
    @Test
    void testGetterSetterWsHfaIndPag() {

        assertNotNull(wsHfaReg.getWsHfaIndPag());

        String vString = "A";
        wsHfaReg.setWsHfaIndPag(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaIndPag()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsHfaReg.setWsHfaIndPag(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaIndPag()));
    }

    // property wsHfaIndExc
    @Test
    void testGetterSetterWsHfaIndExc() {

        assertNotNull(wsHfaReg.getWsHfaIndExc());

        String vString = "A";
        wsHfaReg.setWsHfaIndExc(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaIndExc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsHfaReg.setWsHfaIndExc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaIndExc()));
    }

    // property wsHfaUsuCve
    @Test
    void testGetterSetterWsHfaUsuCve() {

        assertNotNull(wsHfaReg.getWsHfaUsuCve());

        String vString = "ABCabc12";
        wsHfaReg.setWsHfaUsuCve(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaUsuCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsHfaReg.setWsHfaUsuCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaUsuCve()));
    }

    // property wsHfaNomPrg
    @Test
    void testGetterSetterWsHfaNomPrg() {

        assertNotNull(wsHfaReg.getWsHfaNomPrg());

        String vString = "ABCabc12";
        wsHfaReg.setWsHfaNomPrg(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaNomPrg()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsHfaReg.setWsHfaNomPrg(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaNomPrg()));
    }

    // property wsHfaFecUltAct
    @Test
    void testGetterSetterWsHfaFecUltAct() {

        assertNotNull(wsHfaReg.getWsHfaFecUltAct());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaFecUltAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaFecUltAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaFecUltAct()));
    }

    // property wsHfaPjeIva
    @Test
    void testGetterSetterWsHfaPjeIva() {

        assertNotNull(wsHfaReg.getWsHfaPjeIva());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsHfaReg.setWsHfaPjeIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsHfaReg.getWsHfaPjeIva().toBigDecimal());

        int vint = 123;
        wsHfaReg.setWsHfaPjeIva(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaPjeIva()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsHfaReg.setWsHfaPjeIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaPjeIva()));
    }

    // property wsHfaPjeIsr
    @Test
    void testGetterSetterWsHfaPjeIsr() {

        assertNotNull(wsHfaReg.getWsHfaPjeIsr());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsHfaReg.setWsHfaPjeIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsHfaReg.getWsHfaPjeIsr().toBigDecimal());

        int vint = 123;
        wsHfaReg.setWsHfaPjeIsr(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaPjeIsr()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsHfaReg.setWsHfaPjeIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaPjeIsr()));
    }

    // property wsHfaCdregfis
    @Test
    void testGetterSetterWsHfaCdregfis() {

        assertNotNull(wsHfaReg.getWsHfaCdregfis());

        String vString = "ABC";
        wsHfaReg.setWsHfaCdregfis(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaCdregfis()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsHfaReg.setWsHfaCdregfis(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaCdregfis()));
    }

    // property wsHfaNomrznso
    @Test
    void testGetterSetterWsHfaNomrznso() {

        assertNotNull(wsHfaReg.getWsHfaNomrznso());

        String vString = "ABCabc1230";
        wsHfaReg.setWsHfaNomrznso(vString); // String
        assertEquals(asStr(vString), asStr(wsHfaReg.getWsHfaNomrznso()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfaReg.setWsHfaNomrznso(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfaReg.getWsHfaNomrznso()));
    }

    // property wsHfaCedNovi
    @Test
    void testGetterSetterWsHfaCedNovi() {

        assertNotNull(wsHfaReg.getWsHfaCedNovi());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsHfaReg.setWsHfaCedNovi(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsHfaReg.getWsHfaCedNovi().toBigDecimal());

        int vint = 123;
        wsHfaReg.setWsHfaCedNovi(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaCedNovi()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsHfaReg.setWsHfaCedNovi(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaCedNovi()));
    }

    // property wsHfaCedVida
    @Test
    void testGetterSetterWsHfaCedVida() {

        assertNotNull(wsHfaReg.getWsHfaCedVida());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsHfaReg.setWsHfaCedVida(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsHfaReg.getWsHfaCedVida().toBigDecimal());

        int vint = 123;
        wsHfaReg.setWsHfaCedVida(vint); // int
        assertEquals(asInt(vint), asInt(wsHfaReg.getWsHfaCedVida()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsHfaReg.setWsHfaCedVida(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfaReg.getWsHfaCedVida()));
    }
}

