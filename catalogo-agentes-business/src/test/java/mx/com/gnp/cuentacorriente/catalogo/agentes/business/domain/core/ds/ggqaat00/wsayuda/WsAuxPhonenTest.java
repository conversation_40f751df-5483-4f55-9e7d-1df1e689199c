package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-PHONEN.
 *
 */
class WsAuxPhonenTest {

    private WsAuxPhonen wsAuxPhonen;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxPhonen = new WsAuxPhonen();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAuxPhonen wsAuxPhonen2 = new WsAuxPhonen(parent);
        assertNotNull(wsAuxPhonen2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 14);
        WsAuxPhonen wsAuxPhonen3 = new WsAuxPhonen(parent, sibling);
        assertNotNull(wsAuxPhonen3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAuxPhonen.getWsAuxPhone());

    }

    // property wsAuxPhone
    @Test
    void testGetterSetterWsAuxPhone() {

        assertNotNull(wsAuxPhonen.getWsAuxPhone());
        assertNotNull(wsAuxPhonen.getWsAuxPhone(1));

        String vString = "A";
        wsAuxPhonen.getWsAuxPhone().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxPhonen.getWsAuxPhone(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxPhonen.getWsAuxPhone().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsAuxPhonen.getWsAuxPhone(1)));
    }
}

