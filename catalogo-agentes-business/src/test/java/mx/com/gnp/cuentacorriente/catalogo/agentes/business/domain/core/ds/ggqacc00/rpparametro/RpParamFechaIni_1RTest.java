package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpparametro;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-PARAM-FECHA-INI-1-R.
 *
 */
class RpParamFechaIni_1RTest {

    private RpParamFechaIni_1R rpParamFechaIni_1R;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpParamFechaIni_1R = new RpParamFechaIni_1R();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RpParamFechaIni_1R rpParamFechaIni_1R2 = new RpParamFechaIni_1R(parent);
        assertNotNull(rpParamFechaIni_1R2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        RpParamFechaIni_1R rpParamFechaIni_1R3 = new RpParamFechaIni_1R(parent, sibling);
        assertNotNull(rpParamFechaIni_1R3);
    }

    @Test
    void testInitialize() {
        assertNotNull(rpParamFechaIni_1R.getRpFechaIni_1Aaaa());

        assertNotNull(rpParamFechaIni_1R.getRpGuionIni_1_1());

        assertNotNull(rpParamFechaIni_1R.getRpFechaIni_1Mm());

        assertNotNull(rpParamFechaIni_1R.getRpGuionIni_1_2());

        assertNotNull(rpParamFechaIni_1R.getRpFechaIni_1Dd());

    }

    // property rpFechaIni_1Aaaa
    @Test
    void testGetterSetterRpFechaIni_1Aaaa() {

        assertNotNull(rpParamFechaIni_1R.getRpFechaIni_1Aaaa());

        int vint = 1234;
        rpParamFechaIni_1R.setRpFechaIni_1Aaaa(vint); // int
        assertEquals(asInt(vint), asInt(rpParamFechaIni_1R.getRpFechaIni_1Aaaa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        rpParamFechaIni_1R.setRpFechaIni_1Aaaa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpParamFechaIni_1R.getRpFechaIni_1Aaaa()));
        // Conditionals (88) - RP-FECHA-INI-1-AAAA
        rpParamFechaIni_1R.setFechaIni_1AaaaOk();
        assertTrue(rpParamFechaIni_1R.isFechaIni_1AaaaOk());

    }

    // property rpGuionIni_1_1
    @Test
    void testGetterSetterRpGuionIni_1_1() {

        assertNotNull(rpParamFechaIni_1R.getRpGuionIni_1_1());

        String vString = "A";
        rpParamFechaIni_1R.setRpGuionIni_1_1(vString); // String
        assertEquals(asStr(vString), asStr(rpParamFechaIni_1R.getRpGuionIni_1_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParamFechaIni_1R.setRpGuionIni_1_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rpParamFechaIni_1R.getRpGuionIni_1_1()));
    }

    // property rpFechaIni_1Mm
    @Test
    void testGetterSetterRpFechaIni_1Mm() {

        assertNotNull(rpParamFechaIni_1R.getRpFechaIni_1Mm());

        int vint = 12;
        rpParamFechaIni_1R.setRpFechaIni_1Mm(vint); // int
        assertEquals(asInt(vint), asInt(rpParamFechaIni_1R.getRpFechaIni_1Mm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParamFechaIni_1R.setRpFechaIni_1Mm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpParamFechaIni_1R.getRpFechaIni_1Mm()));
        // Conditionals (88) - RP-FECHA-INI-1-MM
        rpParamFechaIni_1R.setFechaIni_1MmOk();
        assertTrue(rpParamFechaIni_1R.isFechaIni_1MmOk());

    }

    // property rpGuionIni_1_2
    @Test
    void testGetterSetterRpGuionIni_1_2() {

        assertNotNull(rpParamFechaIni_1R.getRpGuionIni_1_2());

        String vString = "A";
        rpParamFechaIni_1R.setRpGuionIni_1_2(vString); // String
        assertEquals(asStr(vString), asStr(rpParamFechaIni_1R.getRpGuionIni_1_2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParamFechaIni_1R.setRpGuionIni_1_2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rpParamFechaIni_1R.getRpGuionIni_1_2()));
    }

    // property rpFechaIni_1Dd
    @Test
    void testGetterSetterRpFechaIni_1Dd() {

        assertNotNull(rpParamFechaIni_1R.getRpFechaIni_1Dd());

        int vint = 12;
        rpParamFechaIni_1R.setRpFechaIni_1Dd(vint); // int
        assertEquals(asInt(vint), asInt(rpParamFechaIni_1R.getRpFechaIni_1Dd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParamFechaIni_1R.setRpFechaIni_1Dd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpParamFechaIni_1R.getRpFechaIni_1Dd()));
        // Conditionals (88) - RP-FECHA-INI-1-DD
        rpParamFechaIni_1R.setFechaIni_1DdOk();
        assertTrue(rpParamFechaIni_1R.isFechaIni_1DdOk());

    }
}

