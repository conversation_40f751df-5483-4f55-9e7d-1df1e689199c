package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CHAIN.
 *
 */
class WsChainTest {

    private WsChain wsChain;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsChain = new WsChain();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsChain wsChain2 = new WsChain(parent);
        assertNotNull(wsChain2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsChain wsChain3 = new WsChain(parent, sibling);
        assertNotNull(wsChain3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsChain.getWsChain1());

        assertNotNull(wsChain.getWsChain2());

    }

    // property wsChain1
    @Test
    void testGetterSetterWsChain1() {

        assertNotNull(wsChain.getWsChain1());

        String vString = "ABCab";
        wsChain.setWsChain1(vString); // String
        assertEquals(asStr(vString), asStr(wsChain.getWsChain1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsChain.setWsChain1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsChain.getWsChain1()));
    }

    // property wsChain2
    @Test
    void testGetterSetterWsChain2() {

        assertNotNull(wsChain.getWsChain2());

        String vString = "ABCab";
        wsChain.setWsChain2(vString); // String
        assertEquals(asStr(vString), asStr(wsChain.getWsChain2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsChain.setWsChain2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsChain.getWsChain2()));
    }
}

