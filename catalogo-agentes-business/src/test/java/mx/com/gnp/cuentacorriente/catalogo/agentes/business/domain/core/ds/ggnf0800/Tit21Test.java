package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TIT21.
 *
 */
class Tit21Test {

    private Tit21 tit21;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tit21 = new Tit21();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Tit21 tit212 = new Tit21(parent);
        assertNotNull(tit212);
        AlphanumericVar sibling = new AlphanumericVar(parent, 132);
        Tit21 tit213 = new Tit21(parent, sibling);
        assertNotNull(tit213);
    }

    @Test
    void testInitialize() {
    }
}

