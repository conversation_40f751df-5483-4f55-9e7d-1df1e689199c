package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TIMESTAMPR.
 *
 */
class WsTimestamprTest {

    private WsTimestampr wsTimestampr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTimestampr = new WsTimestampr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsTimestampr wsTimestampr2 = new WsTimestampr(parent);
        assertNotNull(wsTimestampr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 26);
        WsTimestampr wsTimestampr3 = new WsTimestampr(parent, sibling);
        assertNotNull(wsTimestampr3);
    }

    @Test
    void testInitialize() {



    }

    // property wsFechaTimestamp
    @Test
    void testGetterSetterWsFechaTimestamp() {

        assertNotNull(wsTimestampr.getWsFechaTimestamp());
    }

    // property wsHoraTimestamp
    @Test
    void testGetterSetterWsHoraTimestamp() {

        assertNotNull(wsTimestampr.getWsHoraTimestamp());
    }

    // property wsMicrTimestamp
    @Test
    void testGetterSetterWsMicrTimestamp() {

        assertNotNull(wsTimestampr.getWsMicrTimestamp());
    }

    // property wsAaaaTimestamp
    @Test
    void testGetterSetterWsAaaaTimestamp() {

        // Property: wsFechaTimestamp.wsAaaaTimestamp -> 05 WS-AAAA-TIMESTAMP 9(04)
        assertNotNull(wsTimestampr.getWsAaaaTimestamp());
        int vint = 1234;
        wsTimestampr.setWsAaaaTimestamp(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsTimestampr.setWsAaaaTimestamp(vINumericValue); // INumericValue
    }

    // property wsMmTimestamp
    @Test
    void testGetterSetterWsMmTimestamp() {

        // Property: wsFechaTimestamp.wsMmTimestamp -> 05 WS-MM-TIMESTAMP 9(02)
        assertNotNull(wsTimestampr.getWsMmTimestamp());
        int vint = 12;
        wsTimestampr.setWsMmTimestamp(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsTimestampr.setWsMmTimestamp(vINumericValue); // INumericValue
    }

    // property wsDdTimestamp
    @Test
    void testGetterSetterWsDdTimestamp() {

        // Property: wsFechaTimestamp.wsDdTimestamp -> 05 WS-DD-TIMESTAMP 9(02)
        assertNotNull(wsTimestampr.getWsDdTimestamp());
        int vint = 12;
        wsTimestampr.setWsDdTimestamp(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsTimestampr.setWsDdTimestamp(vINumericValue); // INumericValue
    }

    // property wsMinTimestamp
    @Test
    void testGetterSetterWsMinTimestamp() {

        // Property: wsHoraTimestamp.wsMinTimestamp -> 05 WS-MIN-TIMESTAMP 9(02)
        assertNotNull(wsTimestampr.getWsMinTimestamp());
        int vint = 12;
        wsTimestampr.setWsMinTimestamp(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsTimestampr.setWsMinTimestamp(vINumericValue); // INumericValue
    }

    // property wsSegTimestamp
    @Test
    void testGetterSetterWsSegTimestamp() {

        // Property: wsHoraTimestamp.wsSegTimestamp -> 05 WS-SEG-TIMESTAMP 9(02)
        assertNotNull(wsTimestampr.getWsSegTimestamp());
        int vint = 12;
        wsTimestampr.setWsSegTimestamp(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsTimestampr.setWsSegTimestamp(vINumericValue); // INumericValue
    }

    // property wsMicrosegTimestamp
    @Test
    void testGetterSetterWsMicrosegTimestamp() {

        // Property: wsMicrTimestamp.wsMicrosegTimestamp -> 05 WS-MICROSEG-TIMESTAMP 9(06)
        assertNotNull(wsTimestampr.getWsMicrosegTimestamp());
        int vint = 123456;
        wsTimestampr.setWsMicrosegTimestamp(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        wsTimestampr.setWsMicrosegTimestamp(vINumericValue); // INumericValue
    }
}

