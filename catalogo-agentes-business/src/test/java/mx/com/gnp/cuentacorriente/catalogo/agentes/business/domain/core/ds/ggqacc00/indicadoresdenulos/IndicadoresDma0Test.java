package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICADORES-DMA0.
 *
 */
class IndicadoresDma0Test {

    private IndicadoresDma0 indicadoresDma0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indicadoresDma0 = new IndicadoresDma0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        indicadoresDma0.setInddmaagt(0);
        assertEquals(0, indicadoresDma0.getInddmaagt());

        indicadoresDma0.setInddmaidr(0);
        assertEquals(0, indicadoresDma0.getInddmaidr());

        indicadoresDma0.setInddmacol(0);
        assertEquals(0, indicadoresDma0.getInddmacol());

        indicadoresDma0.setInddmatdm(0);
        assertEquals(0, indicadoresDma0.getInddmatdm());

        indicadoresDma0.setInddmacpo(0);
        assertEquals(0, indicadoresDma0.getInddmacpo());

    }

    // property inddmaagt
    @Test
    void testGetterSetterInddmaagt() {

        assertNotNull(indicadoresDma0.getInddmaagt());

        int vint = 1234;
        indicadoresDma0.setInddmaagt(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresDma0.getInddmaagt()));
    }

    // property inddmaidr
    @Test
    void testGetterSetterInddmaidr() {

        assertNotNull(indicadoresDma0.getInddmaidr());

        int vint = 1234;
        indicadoresDma0.setInddmaidr(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresDma0.getInddmaidr()));
    }

    // property inddmacol
    @Test
    void testGetterSetterInddmacol() {

        assertNotNull(indicadoresDma0.getInddmacol());

        int vint = 1234;
        indicadoresDma0.setInddmacol(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresDma0.getInddmacol()));
    }

    // property inddmatdm
    @Test
    void testGetterSetterInddmatdm() {

        assertNotNull(indicadoresDma0.getInddmatdm());

        int vint = 1234;
        indicadoresDma0.setInddmatdm(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresDma0.getInddmatdm()));
    }

    // property inddmacpo
    @Test
    void testGetterSetterInddmacpo() {

        assertNotNull(indicadoresDma0.getInddmacpo());

        int vint = 1234;
        indicadoresDma0.setInddmacpo(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresDma0.getInddmacpo()));
    }
}

