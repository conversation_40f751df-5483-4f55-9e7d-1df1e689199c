package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GCCTORV0-NULL.
 *
 */
class WsGcctorv0NullTest {

    private WsGcctorv0Null wsGcctorv0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGcctorv0Null = new WsGcctorv0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsGcctorv0Null wsGcctorv0Null2 = new WsGcctorv0Null(parent);
        assertNotNull(wsGcctorv0Null2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 18);
        WsGcctorv0Null wsGcctorv0Null3 = new WsGcctorv0Null(parent, sibling);
        assertNotNull(wsGcctorv0Null3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsGcctorv0Null.getOrv01());

        assertNotNull(wsGcctorv0Null.getOrv02());

        assertNotNull(wsGcctorv0Null.getOrv03());

        assertNotNull(wsGcctorv0Null.getOrv04());

        assertNotNull(wsGcctorv0Null.getOrv05());

        assertNotNull(wsGcctorv0Null.getOrv06());

        assertNotNull(wsGcctorv0Null.getOrv07());

        assertNotNull(wsGcctorv0Null.getOrv08());

        assertNotNull(wsGcctorv0Null.getOrv09());

    }

    // property orv01
    @Test
    void testGetterSetterOrv01() {

        assertNotNull(wsGcctorv0Null.getOrv01());

        int vint = 1234;
        wsGcctorv0Null.setOrv01(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv01()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv01(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv01()));
    }

    // property orv02
    @Test
    void testGetterSetterOrv02() {

        assertNotNull(wsGcctorv0Null.getOrv02());

        int vint = 1234;
        wsGcctorv0Null.setOrv02(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv02()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv02(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv02()));
    }

    // property orv03
    @Test
    void testGetterSetterOrv03() {

        assertNotNull(wsGcctorv0Null.getOrv03());

        int vint = 1234;
        wsGcctorv0Null.setOrv03(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv03()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv03(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv03()));
    }

    // property orv04
    @Test
    void testGetterSetterOrv04() {

        assertNotNull(wsGcctorv0Null.getOrv04());

        int vint = 1234;
        wsGcctorv0Null.setOrv04(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv04()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv04(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv04()));
    }

    // property orv05
    @Test
    void testGetterSetterOrv05() {

        assertNotNull(wsGcctorv0Null.getOrv05());

        int vint = 1234;
        wsGcctorv0Null.setOrv05(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv05()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv05(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv05()));
    }

    // property orv06
    @Test
    void testGetterSetterOrv06() {

        assertNotNull(wsGcctorv0Null.getOrv06());

        int vint = 1234;
        wsGcctorv0Null.setOrv06(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv06()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv06(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv06()));
    }

    // property orv07
    @Test
    void testGetterSetterOrv07() {

        assertNotNull(wsGcctorv0Null.getOrv07());

        int vint = 1234;
        wsGcctorv0Null.setOrv07(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv07()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv07(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv07()));
    }

    // property orv08
    @Test
    void testGetterSetterOrv08() {

        assertNotNull(wsGcctorv0Null.getOrv08());

        int vint = 1234;
        wsGcctorv0Null.setOrv08(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv08()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv08(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv08()));
    }

    // property orv09
    @Test
    void testGetterSetterOrv09() {

        assertNotNull(wsGcctorv0Null.getOrv09());

        int vint = 1234;
        wsGcctorv0Null.setOrv09(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv09()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGcctorv0Null.setOrv09(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGcctorv0Null.getOrv09()));
    }
}

