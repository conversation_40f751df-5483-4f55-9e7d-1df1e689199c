package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: LLAVES.
 *
 */
class LlavesTest {

    private Llaves llaves;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        llaves = new Llaves();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        llaves.setWsEmpCve("");
        assertEquals("", llaves.getWsEmpCve());

        llaves.setWsCnaNum(0);
        assertEquals(0, llaves.getWsCnaNum());

        llaves.setWsConcepto("");
        assertEquals("", llaves.getWsConcepto());

        llaves.setWsCdoper("");
        assertEquals("", llaves.getWsCdoper());

        llaves.setWsOpcion("");
        assertEquals("", llaves.getWsOpcion());

        llaves.setWsAgtIdr(0);
        assertEquals(0, llaves.getWsAgtIdr());

        assertTrue(llaves.getWsAgtIdrAux().isZero());

        llaves.setWsAgtIdrAnt(0);
        assertEquals(0, llaves.getWsAgtIdrAnt());

        llaves.setWsEmpCveAnt("");
        assertEquals("", llaves.getWsEmpCveAnt());

        llaves.setWsCnaNumAnt(0);
        assertEquals(0, llaves.getWsCnaNumAnt());

        llaves.setWsCfbCveDseCmpAnt("");
        assertEquals("", llaves.getWsCfbCveDseCmpAnt());

        assertTrue(llaves.getWsCf5PjeCtoDseAnt().isZero());

    }

    // property wsEmpCve
    @Test
    void testGetterSetterWsEmpCve() {

        assertNotNull(llaves.getWsEmpCve());

        String vString = "ABCab";
        llaves.setWsEmpCve(vString); // String
        assertEquals(asStr(vString), asStr(llaves.getWsEmpCve()));
    }

    // property wsCnaNum
    @Test
    void testGetterSetterWsCnaNum() {

        assertNotNull(llaves.getWsCnaNum());

        int vint = 1234;
        llaves.setWsCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(llaves.getWsCnaNum()));
    }

    // property wsConcepto
    @Test
    void testGetterSetterWsConcepto() {

        assertNotNull(llaves.getWsConcepto());

        String vString = "ABCa";
        llaves.setWsConcepto(vString); // String
        assertEquals(asStr(vString), asStr(llaves.getWsConcepto()));
    }

    // property wsCdoper
    @Test
    void testGetterSetterWsCdoper() {

        assertNotNull(llaves.getWsCdoper());

        String vString = "ABCabc1230";
        llaves.setWsCdoper(vString); // String
        assertEquals(asStr(vString), asStr(llaves.getWsCdoper()));
    }

    // property wsOpcion
    @Test
    void testGetterSetterWsOpcion() {

        assertNotNull(llaves.getWsOpcion());

        String vString = "A";
        llaves.setWsOpcion(vString); // String
        assertEquals(asStr(vString), asStr(llaves.getWsOpcion()));
    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        assertNotNull(llaves.getWsAgtIdr());

        int vint = 123456789;
        llaves.setWsAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(llaves.getWsAgtIdr()));
    }

    // property wsAgtIdrAux
    @Test
    void testGetterSetterWsAgtIdrAux() {

        assertNotNull(llaves.getWsAgtIdrAux());

        BigDecimal vBigDecimal = new BigDecimal("1234567890");
        llaves.setWsAgtIdrAux(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, llaves.getWsAgtIdrAux().toBigDecimal());

        int vint = 123456789;
        llaves.setWsAgtIdrAux(vint); // int
        assertEquals(asInt(vint), asInt(llaves.getWsAgtIdrAux()));

        NumericVar vINumericValue = new NumericVar(10, 0);;
        vINumericValue.setValue("1234567890");
        llaves.setWsAgtIdrAux(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(llaves.getWsAgtIdrAux()));
    }

    // property wsAgtIdrAnt
    @Test
    void testGetterSetterWsAgtIdrAnt() {

        assertNotNull(llaves.getWsAgtIdrAnt());

        int vint = 123456789;
        llaves.setWsAgtIdrAnt(vint); // int
        assertEquals(asInt(vint), asInt(llaves.getWsAgtIdrAnt()));
    }

    // property wsEmpCveAnt
    @Test
    void testGetterSetterWsEmpCveAnt() {

        assertNotNull(llaves.getWsEmpCveAnt());

        String vString = "ABCab";
        llaves.setWsEmpCveAnt(vString); // String
        assertEquals(asStr(vString), asStr(llaves.getWsEmpCveAnt()));
    }

    // property wsCnaNumAnt
    @Test
    void testGetterSetterWsCnaNumAnt() {

        assertNotNull(llaves.getWsCnaNumAnt());

        int vint = 1234;
        llaves.setWsCnaNumAnt(vint); // int
        assertEquals(asInt(vint), asInt(llaves.getWsCnaNumAnt()));
    }

    // property wsCfbCveDseCmpAnt
    @Test
    void testGetterSetterWsCfbCveDseCmpAnt() {

        assertNotNull(llaves.getWsCfbCveDseCmpAnt());

        String vString = "ABCa";
        llaves.setWsCfbCveDseCmpAnt(vString); // String
        assertEquals(asStr(vString), asStr(llaves.getWsCfbCveDseCmpAnt()));
    }

    // property wsCf5PjeCtoDseAnt
    @Test
    void testGetterSetterWsCf5PjeCtoDseAnt() {

        assertNotNull(llaves.getWsCf5PjeCtoDseAnt());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        llaves.setWsCf5PjeCtoDseAnt(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, llaves.getWsCf5PjeCtoDseAnt().toBigDecimal());

        int vint = 123;
        llaves.setWsCf5PjeCtoDseAnt(vint); // int
        assertEquals(asInt(vint), asInt(llaves.getWsCf5PjeCtoDseAnt()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        llaves.setWsCf5PjeCtoDseAnt(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(llaves.getWsCf5PjeCtoDseAnt()));
    }
}

