package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTFLA2-NULL.
 *
 */
class WsGfvtfla2NullTest {

    private WsGfvtfla2Null wsGfvtfla2Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtfla2Null = new WsGfvtfla2Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtfla2Null.setFla0111(0);
        assertEquals(0, wsGfvtfla2Null.getFla0111());

        wsGfvtfla2Null.setFla0222(0);
        assertEquals(0, wsGfvtfla2Null.getFla0222());

        wsGfvtfla2Null.setFla0333(0);
        assertEquals(0, wsGfvtfla2Null.getFla0333());

        wsGfvtfla2Null.setFla0444(0);
        assertEquals(0, wsGfvtfla2Null.getFla0444());

        wsGfvtfla2Null.setFla0555(0);
        assertEquals(0, wsGfvtfla2Null.getFla0555());

        wsGfvtfla2Null.setFla0666(0);
        assertEquals(0, wsGfvtfla2Null.getFla0666());

        wsGfvtfla2Null.setFla0777(0);
        assertEquals(0, wsGfvtfla2Null.getFla0777());

        wsGfvtfla2Null.setFla0888(0);
        assertEquals(0, wsGfvtfla2Null.getFla0888());

        wsGfvtfla2Null.setFla0999(0);
        assertEquals(0, wsGfvtfla2Null.getFla0999());

        wsGfvtfla2Null.setFla1000(0);
        assertEquals(0, wsGfvtfla2Null.getFla1000());

        wsGfvtfla2Null.setFla1111(0);
        assertEquals(0, wsGfvtfla2Null.getFla1111());

        wsGfvtfla2Null.setFla1222(0);
        assertEquals(0, wsGfvtfla2Null.getFla1222());

        wsGfvtfla2Null.setFla1333(0);
        assertEquals(0, wsGfvtfla2Null.getFla1333());

    }

    // property fla0111
    @Test
    void testGetterSetterFla0111() {

        assertNotNull(wsGfvtfla2Null.getFla0111());

        int vint = 1234;
        wsGfvtfla2Null.setFla0111(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0111()));
    }

    // property fla0222
    @Test
    void testGetterSetterFla0222() {

        assertNotNull(wsGfvtfla2Null.getFla0222());

        int vint = 1234;
        wsGfvtfla2Null.setFla0222(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0222()));
    }

    // property fla0333
    @Test
    void testGetterSetterFla0333() {

        assertNotNull(wsGfvtfla2Null.getFla0333());

        int vint = 1234;
        wsGfvtfla2Null.setFla0333(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0333()));
    }

    // property fla0444
    @Test
    void testGetterSetterFla0444() {

        assertNotNull(wsGfvtfla2Null.getFla0444());

        int vint = 1234;
        wsGfvtfla2Null.setFla0444(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0444()));
    }

    // property fla0555
    @Test
    void testGetterSetterFla0555() {

        assertNotNull(wsGfvtfla2Null.getFla0555());

        int vint = 1234;
        wsGfvtfla2Null.setFla0555(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0555()));
    }

    // property fla0666
    @Test
    void testGetterSetterFla0666() {

        assertNotNull(wsGfvtfla2Null.getFla0666());

        int vint = 1234;
        wsGfvtfla2Null.setFla0666(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0666()));
    }

    // property fla0777
    @Test
    void testGetterSetterFla0777() {

        assertNotNull(wsGfvtfla2Null.getFla0777());

        int vint = 1234;
        wsGfvtfla2Null.setFla0777(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0777()));
    }

    // property fla0888
    @Test
    void testGetterSetterFla0888() {

        assertNotNull(wsGfvtfla2Null.getFla0888());

        int vint = 1234;
        wsGfvtfla2Null.setFla0888(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0888()));
    }

    // property fla0999
    @Test
    void testGetterSetterFla0999() {

        assertNotNull(wsGfvtfla2Null.getFla0999());

        int vint = 1234;
        wsGfvtfla2Null.setFla0999(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla0999()));
    }

    // property fla1000
    @Test
    void testGetterSetterFla1000() {

        assertNotNull(wsGfvtfla2Null.getFla1000());

        int vint = 1234;
        wsGfvtfla2Null.setFla1000(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla1000()));
    }

    // property fla1111
    @Test
    void testGetterSetterFla1111() {

        assertNotNull(wsGfvtfla2Null.getFla1111());

        int vint = 1234;
        wsGfvtfla2Null.setFla1111(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla1111()));
    }

    // property fla1222
    @Test
    void testGetterSetterFla1222() {

        assertNotNull(wsGfvtfla2Null.getFla1222());

        int vint = 1234;
        wsGfvtfla2Null.setFla1222(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla1222()));
    }

    // property fla1333
    @Test
    void testGetterSetterFla1333() {

        assertNotNull(wsGfvtfla2Null.getFla1333());

        int vint = 1234;
        wsGfvtfla2Null.setFla1333(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla2Null.getFla1333()));
    }
}

