package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TIT41.
 *
 */
class Tit41Test {

    private Tit41 tit41;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tit41 = new Tit41();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Tit41 tit412 = new Tit41(parent);
        assertNotNull(tit412);
        AlphanumericVar sibling = new AlphanumericVar(parent, 144);
        Tit41 tit413 = new Tit41(parent, sibling);
        assertNotNull(tit413);
    }

    @Test
    void testInitialize() {
    }
}

