package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaf900;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TRABAJO.
 *
 */
class WsTrabajoTest {

    private WsTrabajo wsTrabajo;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTrabajo = new WsTrabajo();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsTrabajo.setWsOfnCve(0);
        assertEquals(0, wsTrabajo.getWsOfnCve());

    }

    // property wsOfnCve
    @Test
    void testGetterSetterWsOfnCve() {

        assertNotNull(wsTrabajo.getWsOfnCve());

        int vint = 1234;
        wsTrabajo.setWsOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(wsTrabajo.getWsOfnCve()));
    }
}

