package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: FLA-NUM-AUX.
 *
 */
class FlaNumAuxTest {

    private FlaNumAux flaNumAux;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        flaNumAux = new FlaNumAux();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        flaNumAux.setFlaNum0Aux("");
        assertEquals("", flaNumAux.getFlaNum0Aux());

        flaNumAux.setFlaNum1Aux(0);
        assertEquals(0, flaNumAux.getFlaNum1Aux());

    }

    // property flaNum0Aux
    @Test
    void testGetterSetterFlaNum0Aux() {

        assertNotNull(flaNumAux.getFlaNum0Aux());

        String vString = "A";
        flaNumAux.setFlaNum0Aux(vString); // String
        assertEquals(asStr(vString), asStr(flaNumAux.getFlaNum0Aux()));
    }

    // property flaNum1Aux
    @Test
    void testGetterSetterFlaNum1Aux() {

        assertNotNull(flaNumAux.getFlaNum1Aux());

        int vint = 1234567;
        flaNumAux.setFlaNum1Aux(vint); // int
        assertEquals(asInt(vint), asInt(flaNumAux.getFlaNum1Aux()));
    }
}

