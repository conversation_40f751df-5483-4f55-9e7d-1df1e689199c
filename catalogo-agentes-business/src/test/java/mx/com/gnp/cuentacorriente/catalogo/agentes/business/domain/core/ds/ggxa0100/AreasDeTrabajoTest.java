package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: AREAS-DE-TRABAJO.
 *
 */
class AreasDeTrabajoTest {

    private AreasDeTrabajo areasDeTrabajo;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        areasDeTrabajo = new AreasDeTrabajo();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        areasDeTrabajo.setSwContratos(0);
        assertEquals(0, areasDeTrabajo.getSwContratos());

        areasDeTrabajo.setSwFolios(0);
        assertEquals(0, areasDeTrabajo.getSwFolios());

        areasDeTrabajo.setSwGerencias(0);
        assertEquals(0, areasDeTrabajo.getSwGerencias());

        areasDeTrabajo.setSwRegiones(0);
        assertEquals(0, areasDeTrabajo.getSwRegiones());

        areasDeTrabajo.setSwOficinas(0);
        assertEquals(0, areasDeTrabajo.getSwOficinas());

        areasDeTrabajo.setSwFla2(1);
        assertEquals(1, areasDeTrabajo.getSwFla2());

        areasDeTrabajo.setSwFla3(1);
        assertEquals(1, areasDeTrabajo.getSwFla3());

        areasDeTrabajo.setSwEofAgentes("N");
        assertEquals("N", areasDeTrabajo.getSwEofAgentes());

        areasDeTrabajo.setSwEofContratos("N");
        assertEquals("N", areasDeTrabajo.getSwEofContratos());

        areasDeTrabajo.setSwEofFolios("N");
        assertEquals("N", areasDeTrabajo.getSwEofFolios());

        areasDeTrabajo.setSwEofGerencias("N");
        assertEquals("N", areasDeTrabajo.getSwEofGerencias());

        areasDeTrabajo.setSwEofRegiones("N");
        assertEquals("N", areasDeTrabajo.getSwEofRegiones());

        areasDeTrabajo.setSwEofOficinas("N");
        assertEquals("N", areasDeTrabajo.getSwEofOficinas());



    }

    // property swContratos
    @Test
    void testGetterSetterSwContratos() {

        assertNotNull(areasDeTrabajo.getSwContratos());

        int vint = 1;
        areasDeTrabajo.setSwContratos(vint); // int
        assertEquals(asInt(vint), asInt(areasDeTrabajo.getSwContratos()));
    }

    // property swFolios
    @Test
    void testGetterSetterSwFolios() {

        assertNotNull(areasDeTrabajo.getSwFolios());

        int vint = 1;
        areasDeTrabajo.setSwFolios(vint); // int
        assertEquals(asInt(vint), asInt(areasDeTrabajo.getSwFolios()));
    }

    // property swGerencias
    @Test
    void testGetterSetterSwGerencias() {

        assertNotNull(areasDeTrabajo.getSwGerencias());

        int vint = 1;
        areasDeTrabajo.setSwGerencias(vint); // int
        assertEquals(asInt(vint), asInt(areasDeTrabajo.getSwGerencias()));
    }

    // property swRegiones
    @Test
    void testGetterSetterSwRegiones() {

        assertNotNull(areasDeTrabajo.getSwRegiones());

        int vint = 1;
        areasDeTrabajo.setSwRegiones(vint); // int
        assertEquals(asInt(vint), asInt(areasDeTrabajo.getSwRegiones()));
    }

    // property swOficinas
    @Test
    void testGetterSetterSwOficinas() {

        assertNotNull(areasDeTrabajo.getSwOficinas());

        int vint = 1;
        areasDeTrabajo.setSwOficinas(vint); // int
        assertEquals(asInt(vint), asInt(areasDeTrabajo.getSwOficinas()));
    }

    // property swFla2
    @Test
    void testGetterSetterSwFla2() {

        assertNotNull(areasDeTrabajo.getSwFla2());

        int vint = 1;
        areasDeTrabajo.setSwFla2(vint); // int
        assertEquals(asInt(vint), asInt(areasDeTrabajo.getSwFla2()));
    }

    // property swFla3
    @Test
    void testGetterSetterSwFla3() {

        assertNotNull(areasDeTrabajo.getSwFla3());

        int vint = 1;
        areasDeTrabajo.setSwFla3(vint); // int
        assertEquals(asInt(vint), asInt(areasDeTrabajo.getSwFla3()));
    }

    // property swEofAgentes
    @Test
    void testGetterSetterSwEofAgentes() {

        assertNotNull(areasDeTrabajo.getSwEofAgentes());

        String vString = "A";
        areasDeTrabajo.setSwEofAgentes(vString); // String
        assertEquals(asStr(vString), asStr(areasDeTrabajo.getSwEofAgentes()));
        // Conditionals (88) - SW-EOF-AGENTES
        areasDeTrabajo.setEofAgentes();
        assertTrue(areasDeTrabajo.isEofAgentes());

    }

    // property swEofContratos
    @Test
    void testGetterSetterSwEofContratos() {

        assertNotNull(areasDeTrabajo.getSwEofContratos());

        String vString = "A";
        areasDeTrabajo.setSwEofContratos(vString); // String
        assertEquals(asStr(vString), asStr(areasDeTrabajo.getSwEofContratos()));
        // Conditionals (88) - SW-EOF-CONTRATOS
        areasDeTrabajo.setEofContratos();
        assertTrue(areasDeTrabajo.isEofContratos());

    }

    // property swEofFolios
    @Test
    void testGetterSetterSwEofFolios() {

        assertNotNull(areasDeTrabajo.getSwEofFolios());

        String vString = "A";
        areasDeTrabajo.setSwEofFolios(vString); // String
        assertEquals(asStr(vString), asStr(areasDeTrabajo.getSwEofFolios()));
        // Conditionals (88) - SW-EOF-FOLIOS
        areasDeTrabajo.setEofFolios();
        assertTrue(areasDeTrabajo.isEofFolios());

    }

    // property swEofGerencias
    @Test
    void testGetterSetterSwEofGerencias() {

        assertNotNull(areasDeTrabajo.getSwEofGerencias());

        String vString = "A";
        areasDeTrabajo.setSwEofGerencias(vString); // String
        assertEquals(asStr(vString), asStr(areasDeTrabajo.getSwEofGerencias()));
        // Conditionals (88) - SW-EOF-GERENCIAS
        areasDeTrabajo.setEofGerencias();
        assertTrue(areasDeTrabajo.isEofGerencias());

    }

    // property swEofRegiones
    @Test
    void testGetterSetterSwEofRegiones() {

        assertNotNull(areasDeTrabajo.getSwEofRegiones());

        String vString = "A";
        areasDeTrabajo.setSwEofRegiones(vString); // String
        assertEquals(asStr(vString), asStr(areasDeTrabajo.getSwEofRegiones()));
        // Conditionals (88) - SW-EOF-REGIONES
        areasDeTrabajo.setEofRegiones();
        assertTrue(areasDeTrabajo.isEofRegiones());

    }

    // property swEofOficinas
    @Test
    void testGetterSetterSwEofOficinas() {

        assertNotNull(areasDeTrabajo.getSwEofOficinas());

        String vString = "A";
        areasDeTrabajo.setSwEofOficinas(vString); // String
        assertEquals(asStr(vString), asStr(areasDeTrabajo.getSwEofOficinas()));
        // Conditionals (88) - SW-EOF-OFICINAS
        areasDeTrabajo.setEofOficinas();
        assertTrue(areasDeTrabajo.isEofOficinas());

    }

    // property variables
    @Test
    void testGetterSetterVariables() {

        assertNotNull(areasDeTrabajo.getVariables());
    }

    // property contadores
    @Test
    void testGetterSetterContadores() {

        assertNotNull(areasDeTrabajo.getContadores());
    }

    // property ctnError
    @Test
    void testGetterSetterCtnError() {

        // Property: variables.ctnError -> 10 CTN-ERROR S9(03)
        assertNotNull(areasDeTrabajo.getCtnError());
        int vint = 123;
        areasDeTrabajo.setCtnError(vint); // int
    }

    // property conIncidencias
    @Test
    void testGetterSetterConIncidencias() {

        // Property: variables.conIncidencias -> 10 CON-INCIDENCIAS
        assertNotNull(areasDeTrabajo.getConIncidencias());
    }

    // property conMensaje
    @Test
    void testGetterSetterConMensaje() {

        // Property: variables.conIncidencias.conMensaje -> 15 CON-MENSAJE X(40)
        assertNotNull(areasDeTrabajo.getConMensaje());
        String vString = "ABCabc1230";
        areasDeTrabajo.setConMensaje(vString); // String
    }

    // property conError
    @Test
    void testGetterSetterConError() {

        // Property: variables.conError -> 10 CON-ERROR ZZZZZZZZZ+
        assertNotNull(areasDeTrabajo.getConError());
        String vString = "ABCabc1230";
        areasDeTrabajo.setConError(vString); // String
        NumericVar vIValue = new NumericVar(10, 0);;
        vIValue.setValue("1234567890");
        areasDeTrabajo.setConError(vIValue); // IValue
    }

    // property contadorProc
    @Test
    void testGetterSetterContadorProc() {

        // Property: contadores.contadorProc -> 10 CONTADOR-PROC 9(07)
        assertNotNull(areasDeTrabajo.getContadorProc());
        int vint = 1234567;
        areasDeTrabajo.setContadorProc(vint); // int
    }

    // property wsContador
    @Test
    void testGetterSetterWsContador() {

        // Property: contadores.wsContador -> 10 WS-CONTADOR 9(03)
        assertNotNull(areasDeTrabajo.getWsContador());
        int vint = 123;
        areasDeTrabajo.setWsContador(vint); // int
    }

    // property wsContTcna
    @Test
    void testGetterSetterWsContTcna() {

        // Property: contadores.wsContTcna -> 10 WS-CONT-TCNA 9(03)
        assertNotNull(areasDeTrabajo.getWsContTcna());
        int vint = 123;
        areasDeTrabajo.setWsContTcna(vint); // int
    }

    // property wsContTcna_1
    @Test
    void testGetterSetterWsContTcna_1() {

        // Property: contadores.wsContTcna_1 -> 10 WS-CONT-TCNA-1 9(03)
        assertNotNull(areasDeTrabajo.getWsContTcna_1());
        int vint = 123;
        areasDeTrabajo.setWsContTcna_1(vint); // int
    }

    // property wsContTfla
    @Test
    void testGetterSetterWsContTfla() {

        // Property: contadores.wsContTfla -> 10 WS-CONT-TFLA 9(02)
        assertNotNull(areasDeTrabajo.getWsContTfla());
        int vint = 12;
        areasDeTrabajo.setWsContTfla(vint); // int
    }

    // property wsContTger
    @Test
    void testGetterSetterWsContTger() {

        // Property: contadores.wsContTger -> 10 WS-CONT-TGER 9(02)
        assertNotNull(areasDeTrabajo.getWsContTger());
        int vint = 12;
        areasDeTrabajo.setWsContTger(vint); // int
    }

    // property wsContTger_1
    @Test
    void testGetterSetterWsContTger_1() {

        // Property: contadores.wsContTger_1 -> 10 WS-CONT-TGER-1 9(03)
        assertNotNull(areasDeTrabajo.getWsContTger_1());
        int vint = 123;
        areasDeTrabajo.setWsContTger_1(vint); // int
    }

    // property wsContTceo
    @Test
    void testGetterSetterWsContTceo() {

        // Property: contadores.wsContTceo -> 10 WS-CONT-TCEO 9(02)
        assertNotNull(areasDeTrabajo.getWsContTceo());
        int vint = 12;
        areasDeTrabajo.setWsContTceo(vint); // int
    }

    // property wsContTofn
    @Test
    void testGetterSetterWsContTofn() {

        // Property: contadores.wsContTofn -> 10 WS-CONT-TOFN 9(02)
        assertNotNull(areasDeTrabajo.getWsContTofn());
        int vint = 12;
        areasDeTrabajo.setWsContTofn(vint); // int
    }

    // property wsContTofn_1
    @Test
    void testGetterSetterWsContTofn_1() {

        // Property: contadores.wsContTofn_1 -> 10 WS-CONT-TOFN-1 9(03)
        assertNotNull(areasDeTrabajo.getWsContTofn_1());
        int vint = 123;
        areasDeTrabajo.setWsContTofn_1(vint); // int
    }

    // property wsContAgt0
    @Test
    void testGetterSetterWsContAgt0() {

        // Property: contadores.wsContAgt0 -> 10 WS-CONT-AGT0 9(04)
        assertNotNull(areasDeTrabajo.getWsContAgt0());
        int vint = 1234;
        areasDeTrabajo.setWsContAgt0(vint); // int
    }

    // property wsContTfin
    @Test
    void testGetterSetterWsContTfin() {

        // Property: contadores.wsContTfin -> 10 WS-CONT-TFIN 9(04)
        assertNotNull(areasDeTrabajo.getWsContTfin());
        int vint = 1234;
        areasDeTrabajo.setWsContTfin(vint); // int
    }

    // property wsContFolios
    @Test
    void testGetterSetterWsContFolios() {

        // Property: contadores.wsContFolios -> 10 WS-CONT-FOLIOS 9(04)
        assertNotNull(areasDeTrabajo.getWsContFolios());
        int vint = 1234;
        areasDeTrabajo.setWsContFolios(vint); // int
    }

    // property wsContGrabas
    @Test
    void testGetterSetterWsContGrabas() {

        // Property: contadores.wsContGrabas -> 10 WS-CONT-GRABAS 9(04)
        assertNotNull(areasDeTrabajo.getWsContGrabas());
        int vint = 1234;
        areasDeTrabajo.setWsContGrabas(vint); // int
    }

    // property wsEditLeidos
    @Test
    void testGetterSetterWsEditLeidos() {

        // Property: contadores.wsEditLeidos -> 10 WS-EDIT-LEIDOS ZZZZZZZ,ZZZ,ZZZ
        assertNotNull(areasDeTrabajo.getWsEditLeidos());
        String vString = "ABCabc1230";
        areasDeTrabajo.setWsEditLeidos(vString); // String
        NumericVar vIValue = new NumericVar(15, 0);;
        vIValue.setValue("123456789012345");
        areasDeTrabajo.setWsEditLeidos(vIValue); // IValue
    }

    // property wsEditGrabado
    @Test
    void testGetterSetterWsEditGrabado() {

        // Property: contadores.wsEditGrabado -> 10 WS-EDIT-GRABADO ZZZZZZZ,ZZZ,ZZZ
        assertNotNull(areasDeTrabajo.getWsEditGrabado());
        String vString = "ABCabc1230";
        areasDeTrabajo.setWsEditGrabado(vString); // String
        NumericVar vIValue = new NumericVar(15, 0);;
        vIValue.setValue("123456789012345");
        areasDeTrabajo.setWsEditGrabado(vIValue); // IValue
    }
}

