package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FOLIOXX.
 *
 */
class WsFolioxxTest {

    private WsFolioxx wsFolioxx;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFolioxx = new WsFolioxx();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFolioxx wsFolioxx2 = new WsFolioxx(parent);
        assertNotNull(wsFolioxx2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFolioxx wsFolioxx3 = new WsFolioxx(parent, sibling);
        assertNotNull(wsFolioxx3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFolioxx.getWsFolioxx1());

        assertNotNull(wsFolioxx.getWsFolioxx2());

    }

    // property wsFolioxx1
    @Test
    void testGetterSetterWsFolioxx1() {

        assertNotNull(wsFolioxx.getWsFolioxx1());

        String vString = "A";
        wsFolioxx.setWsFolioxx1(vString); // String
        assertEquals(asStr(vString), asStr(wsFolioxx.getWsFolioxx1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFolioxx.setWsFolioxx1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFolioxx.getWsFolioxx1()));
    }

    // property wsFolioxx2
    @Test
    void testGetterSetterWsFolioxx2() {

        assertNotNull(wsFolioxx.getWsFolioxx2());

        int vint = 1234567;
        wsFolioxx.setWsFolioxx2(vint); // int
        assertEquals(asInt(vint), asInt(wsFolioxx.getWsFolioxx2()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsFolioxx.setWsFolioxx2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFolioxx.getWsFolioxx2()));
    }
}

