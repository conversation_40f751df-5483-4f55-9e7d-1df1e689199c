package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaselementos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WW-TAB-DATO5.
 *
 */
class WwTabDato5Test {

    private WwTabDato5 wwTabDato5;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wwTabDato5 = new WwTabDato5();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WwTabDato5 wwTabDato52 = new WwTabDato5(parent);
        assertNotNull(wwTabDato52);
        AlphanumericVar sibling = new AlphanumericVar(parent, 5);
        WwTabDato5 wwTabDato53 = new WwTabDato5(parent, sibling);
        assertNotNull(wwTabDato53);
    }

    @Test
    void testInitialize() {
        assertNotNull(wwTabDato5.getWwCampo5());

    }

    // property wwCampo5
    @Test
    void testGetterSetterWwCampo5() {

        assertNotNull(wwTabDato5.getWwCampo5());
        assertNotNull(wwTabDato5.getWwCampo5(1));

        String vString = "A";
        wwTabDato5.getWwCampo5().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wwTabDato5.getWwCampo5(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wwTabDato5.getWwCampo5().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wwTabDato5.getWwCampo5(1)));
    }
}

