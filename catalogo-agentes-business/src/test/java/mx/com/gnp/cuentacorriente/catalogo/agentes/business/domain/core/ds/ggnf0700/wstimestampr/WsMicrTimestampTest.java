package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-MICR-TIMESTAMP.
 *
 */
class WsMicrTimestampTest {

    private WsMicrTimestamp wsMicrTimestamp;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsMicrTimestamp = new WsMicrTimestamp();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsMicrTimestamp wsMicrTimestamp2 = new WsMicrTimestamp(parent);
        assertNotNull(wsMicrTimestamp2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 7);
        WsMicrTimestamp wsMicrTimestamp3 = new WsMicrTimestamp(parent, sibling);
        assertNotNull(wsMicrTimestamp3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsMicrTimestamp.getWsMicrosegTimestamp());

    }

    // property wsMicrosegTimestamp
    @Test
    void testGetterSetterWsMicrosegTimestamp() {

        assertNotNull(wsMicrTimestamp.getWsMicrosegTimestamp());

        int vint = 123456;
        wsMicrTimestamp.setWsMicrosegTimestamp(vint); // int
        assertEquals(asInt(vint), asInt(wsMicrTimestamp.getWsMicrosegTimestamp()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        wsMicrTimestamp.setWsMicrosegTimestamp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsMicrTimestamp.getWsMicrosegTimestamp()));
    }
}

