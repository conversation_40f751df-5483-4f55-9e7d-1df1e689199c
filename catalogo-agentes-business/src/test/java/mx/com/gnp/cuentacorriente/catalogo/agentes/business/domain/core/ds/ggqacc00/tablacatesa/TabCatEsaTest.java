package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablacatesa;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TAB-CAT-ESA.
 *
 */
class TabCatEsaTest {

    private TabCatEsa tabCatEsa;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tabCatEsa = new TabCatEsa(1);
        tabCatEsa.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TabCatEsa tabCatEsa2 = new TabCatEsa(parent, 1);
        assertNotNull(tabCatEsa2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TabCatEsa tabCatEsa3 = new TabCatEsa(parent, sibling, 1);
        assertNotNull(tabCatEsa3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tabCatEsa.getTbEsaDes());

    }

    // property tbEsaDes
    @Test
    void testGetterSetterTbEsaDes() {

        assertNotNull(tabCatEsa.getTbEsaDes());

        String vString = "ABCabc1230";
        tabCatEsa.setTbEsaDes(vString); // String
        assertEquals(asStr(vString), asStr(tabCatEsa.getTbEsaDes()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tabCatEsa.setTbEsaDes(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabCatEsa.getTbEsaDes()));
    }
}

