package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-PK-CMA-DESR.
 *
 */
class WsPkCmaDesrTest {

    private WsPkCmaDesr wsPkCmaDesr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsPkCmaDesr = new WsPkCmaDesr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsPkCmaDesr wsPkCmaDesr2 = new WsPkCmaDesr(parent);
        assertNotNull(wsPkCmaDesr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 60);
        WsPkCmaDesr wsPkCmaDesr3 = new WsPkCmaDesr(parent, sibling);
        assertNotNull(wsPkCmaDesr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsPkCmaDesr.getWsPkTelr());


    }

    // property wsPkTelr
    @Test
    void testGetterSetterWsPkTelr() {

        assertNotNull(wsPkCmaDesr.getWsPkTelr());

        String vString = "ABCabc1230";
        wsPkCmaDesr.setWsPkTelr(vString); // String
        assertEquals(asStr(vString), asStr(wsPkCmaDesr.getWsPkTelr()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsPkCmaDesr.setWsPkTelr(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPkCmaDesr.getWsPkTelr()));
    }

    // property wsPkTel
    @Test
    void testGetterSetterWsPkTel() {

        assertNotNull(wsPkCmaDesr.getWsPkTel());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        wsPkCmaDesr.setWsPkTel(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsPkCmaDesr.getWsPkTel().toBigDecimal());

        int vint = 123456789;
        wsPkCmaDesr.setWsPkTel(vint); // int
        assertEquals(asInt(vint), asInt(wsPkCmaDesr.getWsPkTel()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        wsPkCmaDesr.setWsPkTel(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsPkCmaDesr.getWsPkTel()));
    }
}

