package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-PARAMETRO.
 *
 */
class RpParametroTest {

    private RpParametro rpParametro;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpParametro = new RpParametro();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        rpParametro.setRpParamOpcion("");
        assertEquals("", rpParametro.getRpParamOpcion());

        assertNotNull(rpParametro.getRpParamFechaIni_1());


        assertNotNull(rpParametro.getRpParamFechaFin_1());


        rpParametro.setRpResto("");
        assertEquals("", rpParametro.getRpResto());

    }

    // property rpParamOpcion
    @Test
    void testGetterSetterRpParamOpcion() {

        assertNotNull(rpParametro.getRpParamOpcion());

        String vString = "A";
        rpParametro.setRpParamOpcion(vString); // String
        assertEquals(asStr(vString), asStr(rpParametro.getRpParamOpcion()));
        // Conditionals (88) - RP-PARAM-OPCION
        rpParametro.setParamOpcionOk();
        assertTrue(rpParametro.isParamOpcionOk());
        rpParametro.setParamAutomatico();
        assertTrue(rpParametro.isParamAutomatico());
        rpParametro.setParamReproceso();
        assertTrue(rpParametro.isParamReproceso());

    }

    // property rpParamFechaIni_1
    @Test
    void testGetterSetterRpParamFechaIni_1() {

        assertNotNull(rpParametro.getRpParamFechaIni_1());

        String vString = "ABCabc1230";
        rpParametro.setRpParamFechaIni_1(vString); // String
        assertEquals(asStr(vString), asStr(rpParametro.getRpParamFechaIni_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        rpParametro.setRpParamFechaIni_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rpParametro.getRpParamFechaIni_1()));
    }

    // property rpParamFechaIni_1R
    @Test
    void testGetterSetterRpParamFechaIni_1R() {

        assertNotNull(rpParametro.getRpParamFechaIni_1R());
    }

    // property rpParamFechaFin_1
    @Test
    void testGetterSetterRpParamFechaFin_1() {

        assertNotNull(rpParametro.getRpParamFechaFin_1());

        String vString = "ABCabc1230";
        rpParametro.setRpParamFechaFin_1(vString); // String
        assertEquals(asStr(vString), asStr(rpParametro.getRpParamFechaFin_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        rpParametro.setRpParamFechaFin_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rpParametro.getRpParamFechaFin_1()));
    }

    // property rpParamFechaFin_1R
    @Test
    void testGetterSetterRpParamFechaFin_1R() {

        assertNotNull(rpParametro.getRpParamFechaFin_1R());
    }

    // property rpResto
    @Test
    void testGetterSetterRpResto() {

        assertNotNull(rpParametro.getRpResto());

        String vString = "ABCabc1230";
        rpParametro.setRpResto(vString); // String
        assertEquals(asStr(vString), asStr(rpParametro.getRpResto()));
    }

    // property rpFechaIni_1Aaaa
    @Test
    void testGetterSetterRpFechaIni_1Aaaa() {

        // Property: rpParamFechaIni_1R.rpFechaIni_1Aaaa -> 07 RP-FECHA-INI-1-AAAA 9(04)
        assertNotNull(rpParametro.getRpFechaIni_1Aaaa());
        int vint = 1234;
        rpParametro.setRpFechaIni_1Aaaa(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        rpParametro.setRpFechaIni_1Aaaa(vINumericValue); // INumericValue
    }

    // property rpGuionIni_1_1
    @Test
    void testGetterSetterRpGuionIni_1_1() {

        // Property: rpParamFechaIni_1R.rpGuionIni_1_1 -> 07 RP-GUION-INI-1-1 X(01)
        assertNotNull(rpParametro.getRpGuionIni_1_1());
        String vString = "A";
        rpParametro.setRpGuionIni_1_1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParametro.setRpGuionIni_1_1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rpFechaIni_1Mm
    @Test
    void testGetterSetterRpFechaIni_1Mm() {

        // Property: rpParamFechaIni_1R.rpFechaIni_1Mm -> 07 RP-FECHA-INI-1-MM 9(02)
        assertNotNull(rpParametro.getRpFechaIni_1Mm());
        int vint = 12;
        rpParametro.setRpFechaIni_1Mm(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParametro.setRpFechaIni_1Mm(vINumericValue); // INumericValue
    }

    // property rpGuionIni_1_2
    @Test
    void testGetterSetterRpGuionIni_1_2() {

        // Property: rpParamFechaIni_1R.rpGuionIni_1_2 -> 07 RP-GUION-INI-1-2 X(01)
        assertNotNull(rpParametro.getRpGuionIni_1_2());
        String vString = "A";
        rpParametro.setRpGuionIni_1_2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParametro.setRpGuionIni_1_2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rpFechaIni_1Dd
    @Test
    void testGetterSetterRpFechaIni_1Dd() {

        // Property: rpParamFechaIni_1R.rpFechaIni_1Dd -> 07 RP-FECHA-INI-1-DD 9(02)
        assertNotNull(rpParametro.getRpFechaIni_1Dd());
        int vint = 12;
        rpParametro.setRpFechaIni_1Dd(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParametro.setRpFechaIni_1Dd(vINumericValue); // INumericValue
    }

    // property rpFechaFin_1Aaaa
    @Test
    void testGetterSetterRpFechaFin_1Aaaa() {

        // Property: rpParamFechaFin_1R.rpFechaFin_1Aaaa -> 07 RP-FECHA-FIN-1-AAAA 9(04)
        assertNotNull(rpParametro.getRpFechaFin_1Aaaa());
        int vint = 1234;
        rpParametro.setRpFechaFin_1Aaaa(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        rpParametro.setRpFechaFin_1Aaaa(vINumericValue); // INumericValue
    }

    // property rpGuionFin_1_1
    @Test
    void testGetterSetterRpGuionFin_1_1() {

        // Property: rpParamFechaFin_1R.rpGuionFin_1_1 -> 07 RP-GUION-FIN-1-1 X(01)
        assertNotNull(rpParametro.getRpGuionFin_1_1());
        String vString = "A";
        rpParametro.setRpGuionFin_1_1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParametro.setRpGuionFin_1_1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rpFechaFin_1Mm
    @Test
    void testGetterSetterRpFechaFin_1Mm() {

        // Property: rpParamFechaFin_1R.rpFechaFin_1Mm -> 07 RP-FECHA-FIN-1-MM 9(02)
        assertNotNull(rpParametro.getRpFechaFin_1Mm());
        int vint = 12;
        rpParametro.setRpFechaFin_1Mm(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParametro.setRpFechaFin_1Mm(vINumericValue); // INumericValue
    }

    // property rpGuionFin_1_2
    @Test
    void testGetterSetterRpGuionFin_1_2() {

        // Property: rpParamFechaFin_1R.rpGuionFin_1_2 -> 07 RP-GUION-FIN-1-2 X(01)
        assertNotNull(rpParametro.getRpGuionFin_1_2());
        String vString = "A";
        rpParametro.setRpGuionFin_1_2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParametro.setRpGuionFin_1_2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rpFechaFin_1Dd
    @Test
    void testGetterSetterRpFechaFin_1Dd() {

        // Property: rpParamFechaFin_1R.rpFechaFin_1Dd -> 07 RP-FECHA-FIN-1-DD 9(02)
        assertNotNull(rpParametro.getRpFechaFin_1Dd());
        int vint = 12;
        rpParametro.setRpFechaFin_1Dd(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParametro.setRpFechaFin_1Dd(vINumericValue); // INumericValue
    }
}

