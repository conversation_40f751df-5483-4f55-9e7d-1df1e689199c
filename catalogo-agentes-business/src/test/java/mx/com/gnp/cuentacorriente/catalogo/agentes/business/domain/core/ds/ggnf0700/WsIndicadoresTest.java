package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-INDICADORES.
 *
 */
class WsIndicadoresTest {

    private WsIndicadores wsIndicadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsIndicadores = new WsIndicadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsIndicadores.setWsGerZon(0);
        assertEquals(0, wsIndicadores.getWsGerZon());

        wsIndicadores.setWsAgtIdr(0);
        assertEquals(0, wsIndicadores.getWsAgtIdr());

        wsIndicadores.setWsFlaNum("");
        assertEquals("", wsIndicadores.getWsFlaNum());

        wsIndicadores.setWsDraCve(0);
        assertEquals(0, wsIndicadores.getWsDraCve());

        wsIndicadores.setWsMesAux(0);
        assertEquals(0, wsIndicadores.getWsMesAux());

        wsIndicadores.setWsFinAgc(0);
        assertEquals(0, wsIndicadores.getWsFinAgc());

        wsIndicadores.setWsFinAgt(0);
        assertEquals(0, wsIndicadores.getWsFinAgt());

        wsIndicadores.setWsFinCna(0);
        assertEquals(0, wsIndicadores.getWsFinCna());

        wsIndicadores.setWsFinCna1(0);
        assertEquals(0, wsIndicadores.getWsFinCna1());

        wsIndicadores.setWsFinFla(0);
        assertEquals(0, wsIndicadores.getWsFinFla());

        wsIndicadores.setWsFinFla2(0);
        assertEquals(0, wsIndicadores.getWsFinFla2());

        wsIndicadores.setWsFinMte(0);
        assertEquals(0, wsIndicadores.getWsFinMte());

        wsIndicadores.setWsFinOrv(0);
        assertEquals(0, wsIndicadores.getWsFinOrv());

        wsIndicadores.setWsFinDrc(0);
        assertEquals(0, wsIndicadores.getWsFinDrc());

        wsIndicadores.setI(0);
        assertEquals(0, wsIndicadores.getI());

        wsIndicadores.setJ(0);
        assertEquals(0, wsIndicadores.getJ());

        wsIndicadores.setContador(0);
        assertEquals(0, wsIndicadores.getContador());

        wsIndicadores.setContLineas(0);
        assertEquals(0, wsIndicadores.getContLineas());

        wsIndicadores.setContLineas1(0);
        assertEquals(0, wsIndicadores.getContLineas1());

        wsIndicadores.setContHojas(0);
        assertEquals(0, wsIndicadores.getContHojas());

        wsIndicadores.setContHojas1(0);
        assertEquals(0, wsIndicadores.getContHojas1());







    }

    // property wsGerZon
    @Test
    void testGetterSetterWsGerZon() {

        assertNotNull(wsIndicadores.getWsGerZon());

        int vint = 1234;
        wsIndicadores.setWsGerZon(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsGerZon()));
    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        assertNotNull(wsIndicadores.getWsAgtIdr());

        int vint = 123456789;
        wsIndicadores.setWsAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsAgtIdr()));
    }

    // property wsFlaNum
    @Test
    void testGetterSetterWsFlaNum() {

        assertNotNull(wsIndicadores.getWsFlaNum());

        String vString = "ABCabc12";
        wsIndicadores.setWsFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(wsIndicadores.getWsFlaNum()));
    }

    // property wsDraCve
    @Test
    void testGetterSetterWsDraCve() {

        assertNotNull(wsIndicadores.getWsDraCve());

        int vint = 1234;
        wsIndicadores.setWsDraCve(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsDraCve()));
    }

    // property wsMesAux
    @Test
    void testGetterSetterWsMesAux() {

        assertNotNull(wsIndicadores.getWsMesAux());

        int vint = 12;
        wsIndicadores.setWsMesAux(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsMesAux()));
    }

    // property wsFinAgc
    @Test
    void testGetterSetterWsFinAgc() {

        assertNotNull(wsIndicadores.getWsFinAgc());

        int vint = 1;
        wsIndicadores.setWsFinAgc(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinAgc()));
    }

    // property wsFinAgt
    @Test
    void testGetterSetterWsFinAgt() {

        assertNotNull(wsIndicadores.getWsFinAgt());

        int vint = 1;
        wsIndicadores.setWsFinAgt(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinAgt()));
    }

    // property wsFinCna
    @Test
    void testGetterSetterWsFinCna() {

        assertNotNull(wsIndicadores.getWsFinCna());

        int vint = 1;
        wsIndicadores.setWsFinCna(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinCna()));
    }

    // property wsFinCna1
    @Test
    void testGetterSetterWsFinCna1() {

        assertNotNull(wsIndicadores.getWsFinCna1());

        int vint = 1;
        wsIndicadores.setWsFinCna1(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinCna1()));
    }

    // property wsFinFla
    @Test
    void testGetterSetterWsFinFla() {

        assertNotNull(wsIndicadores.getWsFinFla());

        int vint = 1;
        wsIndicadores.setWsFinFla(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFla()));
    }

    // property wsFinFla2
    @Test
    void testGetterSetterWsFinFla2() {

        assertNotNull(wsIndicadores.getWsFinFla2());

        int vint = 1;
        wsIndicadores.setWsFinFla2(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFla2()));
    }

    // property wsFinMte
    @Test
    void testGetterSetterWsFinMte() {

        assertNotNull(wsIndicadores.getWsFinMte());

        int vint = 1;
        wsIndicadores.setWsFinMte(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinMte()));
    }

    // property wsFinOrv
    @Test
    void testGetterSetterWsFinOrv() {

        assertNotNull(wsIndicadores.getWsFinOrv());

        int vint = 1;
        wsIndicadores.setWsFinOrv(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinOrv()));
    }

    // property wsFinDrc
    @Test
    void testGetterSetterWsFinDrc() {

        assertNotNull(wsIndicadores.getWsFinDrc());

        int vint = 1;
        wsIndicadores.setWsFinDrc(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinDrc()));
    }

    // property i
    @Test
    void testGetterSetterI() {

        assertNotNull(wsIndicadores.getI());

        int vint = 123;
        wsIndicadores.setI(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getI()));
    }

    // property j
    @Test
    void testGetterSetterJ() {

        assertNotNull(wsIndicadores.getJ());

        int vint = 123;
        wsIndicadores.setJ(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getJ()));
    }

    // property contador
    @Test
    void testGetterSetterContador() {

        assertNotNull(wsIndicadores.getContador());

        int vint = 12345;
        wsIndicadores.setContador(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContador()));
    }

    // property contLineas
    @Test
    void testGetterSetterContLineas() {

        assertNotNull(wsIndicadores.getContLineas());

        int vint = 123;
        wsIndicadores.setContLineas(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContLineas()));
    }

    // property contLineas1
    @Test
    void testGetterSetterContLineas1() {

        assertNotNull(wsIndicadores.getContLineas1());

        int vint = 123;
        wsIndicadores.setContLineas1(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContLineas1()));
    }

    // property contHojas
    @Test
    void testGetterSetterContHojas() {

        assertNotNull(wsIndicadores.getContHojas());

        int vint = 123;
        wsIndicadores.setContHojas(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContHojas()));
    }

    // property contHojas1
    @Test
    void testGetterSetterContHojas1() {

        assertNotNull(wsIndicadores.getContHojas1());

        int vint = 123;
        wsIndicadores.setContHojas1(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContHojas1()));
    }

    // property flaNumAux
    @Test
    void testGetterSetterFlaNumAux() {

        assertNotNull(wsIndicadores.getFlaNumAux());
    }

    // property wsFolioxx
    @Test
    void testGetterSetterWsFolioxx() {

        assertNotNull(wsIndicadores.getWsFolioxx());
    }

    // property wsFoliozz
    @Test
    void testGetterSetterWsFoliozz() {

        assertNotNull(wsIndicadores.getWsFoliozz());
    }

    // property wsFolioxxn
    @Test
    void testGetterSetterWsFolioxxn() {

        assertNotNull(wsIndicadores.getWsFolioxxn());
    }

    // property wsFolioxxp
    @Test
    void testGetterSetterWsFolioxxp() {

        assertNotNull(wsIndicadores.getWsFolioxxp());
    }

    // property wsFlaDes
    @Test
    void testGetterSetterWsFlaDes() {

        assertNotNull(wsIndicadores.getWsFlaDes());
    }

    // property flaNum0Aux
    @Test
    void testGetterSetterFlaNum0Aux() {

        // Property: flaNumAux.flaNum0Aux -> 05 FLA-NUM0-AUX X
        assertNotNull(wsIndicadores.getFlaNum0Aux());
        String vString = "A";
        wsIndicadores.setFlaNum0Aux(vString); // String
    }

    // property flaNum1Aux
    @Test
    void testGetterSetterFlaNum1Aux() {

        // Property: flaNumAux.flaNum1Aux -> 05 FLA-NUM1-AUX 9(7)
        assertNotNull(wsIndicadores.getFlaNum1Aux());
        int vint = 1234567;
        wsIndicadores.setFlaNum1Aux(vint); // int
    }

    // property wsFolioxx1
    @Test
    void testGetterSetterWsFolioxx1() {

        // Property: wsFolioxx.wsFolioxx1 -> 05 WS-FOLIOXX1 X
        assertNotNull(wsIndicadores.getWsFolioxx1());
        String vString = "A";
        wsIndicadores.setWsFolioxx1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsIndicadores.setWsFolioxx1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFolioxx2
    @Test
    void testGetterSetterWsFolioxx2() {

        // Property: wsFolioxx.wsFolioxx2 -> 05 WS-FOLIOXX2 9(7)
        assertNotNull(wsIndicadores.getWsFolioxx2());
        int vint = 1234567;
        wsIndicadores.setWsFolioxx2(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsIndicadores.setWsFolioxx2(vINumericValue); // INumericValue
    }

    // property wsFoliozz1
    @Test
    void testGetterSetterWsFoliozz1() {

        // Property: wsFoliozz.wsFoliozz1 -> 05 WS-FOLIOZZ1 X
        assertNotNull(wsIndicadores.getWsFoliozz1());
        String vString = "A";
        wsIndicadores.setWsFoliozz1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsIndicadores.setWsFoliozz1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFoliozz2
    @Test
    void testGetterSetterWsFoliozz2() {

        // Property: wsFoliozz.wsFoliozz2 -> 05 WS-FOLIOZZ2 9(7)
        assertNotNull(wsIndicadores.getWsFoliozz2());
        int vint = 1234567;
        wsIndicadores.setWsFoliozz2(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsIndicadores.setWsFoliozz2(vINumericValue); // INumericValue
    }

    // property wsFolioxx1n
    @Test
    void testGetterSetterWsFolioxx1n() {

        // Property: wsFolioxxn.wsFolioxx1n -> 05 WS-FOLIOXX1N X
        assertNotNull(wsIndicadores.getWsFolioxx1n());
        String vString = "A";
        wsIndicadores.setWsFolioxx1n(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsIndicadores.setWsFolioxx1n(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFolioxx2n
    @Test
    void testGetterSetterWsFolioxx2n() {

        // Property: wsFolioxxn.wsFolioxx2n -> 05 WS-FOLIOXX2N 9(7)
        assertNotNull(wsIndicadores.getWsFolioxx2n());
        int vint = 1234567;
        wsIndicadores.setWsFolioxx2n(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsIndicadores.setWsFolioxx2n(vINumericValue); // INumericValue
    }

    // property wsFolioxx1p
    @Test
    void testGetterSetterWsFolioxx1p() {

        // Property: wsFolioxxp.wsFolioxx1p -> 05 WS-FOLIOXX1P X
        assertNotNull(wsIndicadores.getWsFolioxx1p());
        String vString = "A";
        wsIndicadores.setWsFolioxx1p(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsIndicadores.setWsFolioxx1p(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFolioxx2p
    @Test
    void testGetterSetterWsFolioxx2p() {

        // Property: wsFolioxxp.wsFolioxx2p -> 05 WS-FOLIOXX2P 9(7)
        assertNotNull(wsIndicadores.getWsFolioxx2p());
        int vint = 1234567;
        wsIndicadores.setWsFolioxx2p(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsIndicadores.setWsFolioxx2p(vINumericValue); // INumericValue
    }

    // property wsFlaDesDos
    @Test
    void testGetterSetterWsFlaDesDos() {

        // Property: wsFlaDes.wsFlaDesDos -> 05 WS-FLA-DES-DOS XX
        assertNotNull(wsIndicadores.getWsFlaDesDos());
        String vString = "AB";
        wsIndicadores.setWsFlaDesDos(vString); // String
    }

    // property wsFlaDesResto
    @Test
    void testGetterSetterWsFlaDesResto() {

        // Property: wsFlaDes.wsFlaDesResto -> 05 WS-FLA-DES-RESTO X(38)
        assertNotNull(wsIndicadores.getWsFlaDesResto());
        String vString = "ABCabc1230";
        wsIndicadores.setWsFlaDesResto(vString); // String
    }
}

