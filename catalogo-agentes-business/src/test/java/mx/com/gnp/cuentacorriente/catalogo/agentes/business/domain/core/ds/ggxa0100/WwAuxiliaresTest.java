package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WW-AUXILIARES.
 *
 */
class WwAuxiliaresTest {

    private WwAuxiliares wwAuxiliares;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wwAuxiliares = new WwAuxiliares();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wwAuxiliares.setWkInd(0);
        assertEquals(0, wwAuxiliares.getWkInd());

        wwAuxiliares.setWkInd2(0);
        assertEquals(0, wwAuxiliares.getWkInd2());

        wwAuxiliares.setWkInd3(0);
        assertEquals(0, wwAuxiliares.getWkInd3());

        wwAuxiliares.setWkInd4(0);
        assertEquals(0, wwAuxiliares.getWkInd4());

        wwAuxiliares.setWkInd5(0);
        assertEquals(0, wwAuxiliares.getWkInd5());

        wwAuxiliares.setWkInd6(0);
        assertEquals(0, wwAuxiliares.getWkInd6());

        assertNotNull(wwAuxiliares.getWwCnaCnaNum());


        wwAuxiliares.setWwCnaCnaNumJ("");
        assertEquals("", wwAuxiliares.getWwCnaCnaNumJ());




    }

    // property wkInd
    @Test
    void testGetterSetterWkInd() {

        assertNotNull(wwAuxiliares.getWkInd());

        int vint = 12;
        wwAuxiliares.setWkInd(vint); // int
        assertEquals(asInt(vint), asInt(wwAuxiliares.getWkInd()));
    }

    // property wkInd2
    @Test
    void testGetterSetterWkInd2() {

        assertNotNull(wwAuxiliares.getWkInd2());

        int vint = 12;
        wwAuxiliares.setWkInd2(vint); // int
        assertEquals(asInt(vint), asInt(wwAuxiliares.getWkInd2()));
    }

    // property wkInd3
    @Test
    void testGetterSetterWkInd3() {

        assertNotNull(wwAuxiliares.getWkInd3());

        int vint = 12;
        wwAuxiliares.setWkInd3(vint); // int
        assertEquals(asInt(vint), asInt(wwAuxiliares.getWkInd3()));
    }

    // property wkInd4
    @Test
    void testGetterSetterWkInd4() {

        assertNotNull(wwAuxiliares.getWkInd4());

        int vint = 12;
        wwAuxiliares.setWkInd4(vint); // int
        assertEquals(asInt(vint), asInt(wwAuxiliares.getWkInd4()));
    }

    // property wkInd5
    @Test
    void testGetterSetterWkInd5() {

        assertNotNull(wwAuxiliares.getWkInd5());

        int vint = 123;
        wwAuxiliares.setWkInd5(vint); // int
        assertEquals(asInt(vint), asInt(wwAuxiliares.getWkInd5()));
    }

    // property wkInd6
    @Test
    void testGetterSetterWkInd6() {

        assertNotNull(wwAuxiliares.getWkInd6());

        int vint = 123;
        wwAuxiliares.setWkInd6(vint); // int
        assertEquals(asInt(vint), asInt(wwAuxiliares.getWkInd6()));
    }

    // property wwCnaCnaNum
    @Test
    void testGetterSetterWwCnaCnaNum() {

        assertNotNull(wwAuxiliares.getWwCnaCnaNum());

        int vint = 12;
        wwAuxiliares.setWwCnaCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(wwAuxiliares.getWwCnaCnaNum()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wwAuxiliares.setWwCnaCnaNum(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wwAuxiliares.getWwCnaCnaNum()));
    }

    // property wwCnaCnaNumR
    @Test
    void testGetterSetterWwCnaCnaNumR() {

        assertNotNull(wwAuxiliares.getWwCnaCnaNumR());

        String vString = "AB";
        wwAuxiliares.setWwCnaCnaNumR(vString); // String
        assertEquals(asStr(vString), asStr(wwAuxiliares.getWwCnaCnaNumR()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wwAuxiliares.setWwCnaCnaNumR(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wwAuxiliares.getWwCnaCnaNumR()));
    }

    // property wwCnaCnaNumJ
    @Test
    void testGetterSetterWwCnaCnaNumJ() {

        assertNotNull(wwAuxiliares.getWwCnaCnaNumJ());

        String vString = "AB";
        wwAuxiliares.setWwCnaCnaNumJ(vString); // String
        assertEquals(asStr(vString), asStr(wwAuxiliares.getWwCnaCnaNumJ()));
    }

    // property wtCnaOfnCve
    @Test
    void testGetterSetterWtCnaOfnCve() {

        assertNotNull(wwAuxiliares.getWtCnaOfnCve());
    }

    // property wtCnaGerZon
    @Test
    void testGetterSetterWtCnaGerZon() {

        assertNotNull(wwAuxiliares.getWtCnaGerZon());
    }

    // property wtClavePos
    @Test
    void testGetterSetterWtClavePos() {

        assertNotNull(wwAuxiliares.getWtClavePos());
    }

    // property wtCnaOfnCveA
    @Test
    void testGetterSetterWtCnaOfnCveA() {

        // Property: wtCnaOfnCve.wtCnaOfnCveA -> 15 WT-CNA-OFN-CVE-A X(01) [4]
        assertNotNull(wwAuxiliares.getWtCnaOfnCveA());
        String vString = "A";
        wwAuxiliares.getWtCnaOfnCveA().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wwAuxiliares.getWtCnaOfnCveA().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wtCnaGerZonA
    @Test
    void testGetterSetterWtCnaGerZonA() {

        // Property: wtCnaGerZon.wtCnaGerZonA -> 15 WT-CNA-GER-ZON-A X(01) [4]
        assertNotNull(wwAuxiliares.getWtCnaGerZonA());
        String vString = "A";
        wwAuxiliares.getWtCnaGerZonA().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wwAuxiliares.getWtCnaGerZonA().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wtDigito
    @Test
    void testGetterSetterWtDigito() {

        // Property: wtClavePos.wtDigito -> 15 WT-DIGITO X(01) [4]
        assertNotNull(wwAuxiliares.getWtDigito());
        String vString = "A";
        wwAuxiliares.getWtDigito().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wwAuxiliares.getWtDigito().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }
}

