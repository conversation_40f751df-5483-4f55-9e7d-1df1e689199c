package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICADORES-CMA0.
 *
 */
class IndicadoresCma0Test {

    private IndicadoresCma0 indicadoresCma0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indicadoresCma0 = new IndicadoresCma0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        indicadoresCma0.setIndcmades(0);
        assertEquals(0, indicadoresCma0.getIndcmades());

    }

    // property indcmades
    @Test
    void testGetterSetterIndcmades() {

        assertNotNull(indicadoresCma0.getIndcmades());

        int vint = 1234;
        indicadoresCma0.setIndcmades(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresCma0.getIndcmades()));
    }
}

