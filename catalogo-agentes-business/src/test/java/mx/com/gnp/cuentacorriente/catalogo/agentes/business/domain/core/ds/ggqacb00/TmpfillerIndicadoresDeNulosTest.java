package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: tmpFILLER-INDICADORES-DE-NULOS.
 *
 */
class TmpfillerIndicadoresDeNulosTest {

    private TmpfillerIndicadoresDeNulos tmpfillerIndicadoresDeNulos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tmpfillerIndicadoresDeNulos = new TmpfillerIndicadoresDeNulos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property indicadoresDsc
    @Test
    void testGetterSetterIndicadoresDsc() {

        assertNotNull(tmpfillerIndicadoresDeNulos.getIndicadoresDsc());
    }

    // property inddscdsg
    @Test
    void testGetterSetterInddscdsg() {

        // Property: indicadoresDsc.inddscdsg -> 05 INDDSCDSG S9(4) COMPUTATIONAL
        assertNotNull(tmpfillerIndicadoresDeNulos.getInddscdsg());
        int vint = 1234;
        tmpfillerIndicadoresDeNulos.setInddscdsg(vint); // int
    }
}

