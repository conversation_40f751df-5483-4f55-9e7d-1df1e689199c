package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CONTADORES.
 *
 */
class WsContadoresTest {

    private WsContadores wsContadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsContadores = new WsContadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsContadores.getWsContador());

        wsContadores.setWsLeiExtraccion(0);
        assertEquals(0, wsContadores.getWsLeiExtraccion());

        wsContadores.setWsLeiCatDirecc(0);
        assertEquals(0, wsContadores.getWsLeiCatDirecc());

        wsContadores.setWsLeiAgentes(0);
        assertEquals(0, wsContadores.getWsLeiAgentes());

        wsContadores.setWsLeiCna(0);
        assertEquals(0, wsContadores.getWsLeiCna());

        wsContadores.setWsLeiDsc(0);
        assertEquals(0, wsContadores.getWsLeiDsc());

        wsContadores.setWsLeiCurMte(0);
        assertEquals(0, wsContadores.getWsLeiCurMte());

        wsContadores.setWsLeiCurEsa(0);
        assertEquals(0, wsContadores.getWsLeiCurEsa());

        wsContadores.setWsLeiAgentesVigentes(0);
        assertEquals(0, wsContadores.getWsLeiAgentesVigentes());

        wsContadores.setWsLeiAgentesCancelados(0);
        assertEquals(0, wsContadores.getWsLeiAgentesCancelados());

        wsContadores.setWsLeiAgentesSuspendidos(0);
        assertEquals(0, wsContadores.getWsLeiAgentesSuspendidos());

        wsContadores.setWsLeiAgentesSinEstatus(0);
        assertEquals(0, wsContadores.getWsLeiAgentesSinEstatus());

        wsContadores.setWsLeiCnaVigentes(0);
        assertEquals(0, wsContadores.getWsLeiCnaVigentes());

        wsContadores.setWsLeiCnaCancelados(0);
        assertEquals(0, wsContadores.getWsLeiCnaCancelados());

        wsContadores.setWsLeiCnaSuspendidos(0);
        assertEquals(0, wsContadores.getWsLeiCnaSuspendidos());

        wsContadores.setWsLeiCnaSinEstatus(0);
        assertEquals(0, wsContadores.getWsLeiCnaSinEstatus());

        wsContadores.setWsLeiEmpleados(0);
        assertEquals(0, wsContadores.getWsLeiEmpleados());

        wsContadores.setWsLeiExtranjeros(0);
        assertEquals(0, wsContadores.getWsLeiExtranjeros());

        wsContadores.setWsLeiGfvtagt(0);
        assertEquals(0, wsContadores.getWsLeiGfvtagt());

        wsContadores.setWsLeiGfvtcna(0);
        assertEquals(0, wsContadores.getWsLeiGfvtcna());

        wsContadores.setWsLeiGfvtpra(0);
        assertEquals(0, wsContadores.getWsLeiGfvtpra());

        wsContadores.setWsLeiCurDma(0);
        assertEquals(0, wsContadores.getWsLeiCurDma());

        wsContadores.setWsLeiGcctmnd(0);
        assertEquals(0, wsContadores.getWsLeiGcctmnd());

        wsContadores.setWsLeiGcctpob(0);
        assertEquals(0, wsContadores.getWsLeiGcctpob());

        wsContadores.setWsLeiGfvtcma(0);
        assertEquals(0, wsContadores.getWsLeiGfvtcma());

        wsContadores.setWsLeiGcctedo(0);
        assertEquals(0, wsContadores.getWsLeiGcctedo());

        wsContadores.setWsLeiGcctpai(0);
        assertEquals(0, wsContadores.getWsLeiGcctpai());

        wsContadores.setWsSinGfvtagt(0);
        assertEquals(0, wsContadores.getWsSinGfvtagt());

        wsContadores.setWsSinGfvtcna(0);
        assertEquals(0, wsContadores.getWsSinGfvtcna());

        wsContadores.setWsSinGfvtdsc(0);
        assertEquals(0, wsContadores.getWsSinGfvtdsc());

        wsContadores.setWsSinEsquemaFact(0);
        assertEquals(0, wsContadores.getWsSinEsquemaFact());

        wsContadores.setWsSinGfvtpra(0);
        assertEquals(0, wsContadores.getWsSinGfvtpra());

        wsContadores.setWsSinGfvtdma(0);
        assertEquals(0, wsContadores.getWsSinGfvtdma());

        wsContadores.setWsLeiCurDmaNulos(0);
        assertEquals(0, wsContadores.getWsLeiCurDmaNulos());

        wsContadores.setWsSinGcctmnd(0);
        assertEquals(0, wsContadores.getWsSinGcctmnd());

        wsContadores.setWsSinGcctpob(0);
        assertEquals(0, wsContadores.getWsSinGcctpob());

        wsContadores.setWsSinGfvtcma(0);
        assertEquals(0, wsContadores.getWsSinGfvtcma());

        wsContadores.setWsSinGcctedo(0);
        assertEquals(0, wsContadores.getWsSinGcctedo());

        wsContadores.setWsSinGcctpai(0);
        assertEquals(0, wsContadores.getWsSinGcctpai());

        wsContadores.setWsSinRazSocial(0);
        assertEquals(0, wsContadores.getWsSinRazSocial());

        wsContadores.setWsSinDirFiscal(0);
        assertEquals(0, wsContadores.getWsSinDirFiscal());

        wsContadores.setWsConDirFiscal(0);
        assertEquals(0, wsContadores.getWsConDirFiscal());

        wsContadores.setWsSinDirReceptor(0);
        assertEquals(0, wsContadores.getWsSinDirReceptor());

        wsContadores.setWsRegsGrabe(0);
        assertEquals(0, wsContadores.getWsRegsGrabe());

        wsContadores.setWsCont(0);
        assertEquals(0, wsContadores.getWsCont());

        wsContadores.setWsExcluidos(0);
        assertEquals(0, wsContadores.getWsExcluidos());

    }

    // property wsContador
    @Test
    void testGetterSetterWsContador() {

        assertNotNull(wsContadores.getWsContador());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234");
        wsContadores.setWsContador(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsContadores.getWsContador().toBigDecimal());

        int vint = 123456789;
        wsContadores.setWsContador(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsContador()));

        NumericVar vINumericValue = new NumericVar(14, 0);;
        vINumericValue.setValue("12345678901234");
        wsContadores.setWsContador(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsContadores.getWsContador()));
    }

    // property wsLeiExtraccion
    @Test
    void testGetterSetterWsLeiExtraccion() {

        assertNotNull(wsContadores.getWsLeiExtraccion());

        int vint = 12345;
        wsContadores.setWsLeiExtraccion(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiExtraccion()));
    }

    // property wsLeiCatDirecc
    @Test
    void testGetterSetterWsLeiCatDirecc() {

        assertNotNull(wsContadores.getWsLeiCatDirecc());

        int vint = 12345;
        wsContadores.setWsLeiCatDirecc(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCatDirecc()));
    }

    // property wsLeiAgentes
    @Test
    void testGetterSetterWsLeiAgentes() {

        assertNotNull(wsContadores.getWsLeiAgentes());

        int vint = 12345;
        wsContadores.setWsLeiAgentes(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiAgentes()));
    }

    // property wsLeiCna
    @Test
    void testGetterSetterWsLeiCna() {

        assertNotNull(wsContadores.getWsLeiCna());

        int vint = 12345;
        wsContadores.setWsLeiCna(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCna()));
    }

    // property wsLeiDsc
    @Test
    void testGetterSetterWsLeiDsc() {

        assertNotNull(wsContadores.getWsLeiDsc());

        int vint = 12345;
        wsContadores.setWsLeiDsc(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiDsc()));
    }

    // property wsLeiCurMte
    @Test
    void testGetterSetterWsLeiCurMte() {

        assertNotNull(wsContadores.getWsLeiCurMte());

        int vint = 12345;
        wsContadores.setWsLeiCurMte(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCurMte()));
    }

    // property wsLeiCurEsa
    @Test
    void testGetterSetterWsLeiCurEsa() {

        assertNotNull(wsContadores.getWsLeiCurEsa());

        int vint = 12345;
        wsContadores.setWsLeiCurEsa(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCurEsa()));
    }

    // property wsLeiAgentesVigentes
    @Test
    void testGetterSetterWsLeiAgentesVigentes() {

        assertNotNull(wsContadores.getWsLeiAgentesVigentes());

        int vint = 12345;
        wsContadores.setWsLeiAgentesVigentes(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiAgentesVigentes()));
    }

    // property wsLeiAgentesCancelados
    @Test
    void testGetterSetterWsLeiAgentesCancelados() {

        assertNotNull(wsContadores.getWsLeiAgentesCancelados());

        int vint = 12345;
        wsContadores.setWsLeiAgentesCancelados(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiAgentesCancelados()));
    }

    // property wsLeiAgentesSuspendidos
    @Test
    void testGetterSetterWsLeiAgentesSuspendidos() {

        assertNotNull(wsContadores.getWsLeiAgentesSuspendidos());

        int vint = 12345;
        wsContadores.setWsLeiAgentesSuspendidos(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiAgentesSuspendidos()));
    }

    // property wsLeiAgentesSinEstatus
    @Test
    void testGetterSetterWsLeiAgentesSinEstatus() {

        assertNotNull(wsContadores.getWsLeiAgentesSinEstatus());

        int vint = 12345;
        wsContadores.setWsLeiAgentesSinEstatus(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiAgentesSinEstatus()));
    }

    // property wsLeiCnaVigentes
    @Test
    void testGetterSetterWsLeiCnaVigentes() {

        assertNotNull(wsContadores.getWsLeiCnaVigentes());

        int vint = 12345;
        wsContadores.setWsLeiCnaVigentes(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCnaVigentes()));
    }

    // property wsLeiCnaCancelados
    @Test
    void testGetterSetterWsLeiCnaCancelados() {

        assertNotNull(wsContadores.getWsLeiCnaCancelados());

        int vint = 12345;
        wsContadores.setWsLeiCnaCancelados(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCnaCancelados()));
    }

    // property wsLeiCnaSuspendidos
    @Test
    void testGetterSetterWsLeiCnaSuspendidos() {

        assertNotNull(wsContadores.getWsLeiCnaSuspendidos());

        int vint = 12345;
        wsContadores.setWsLeiCnaSuspendidos(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCnaSuspendidos()));
    }

    // property wsLeiCnaSinEstatus
    @Test
    void testGetterSetterWsLeiCnaSinEstatus() {

        assertNotNull(wsContadores.getWsLeiCnaSinEstatus());

        int vint = 12345;
        wsContadores.setWsLeiCnaSinEstatus(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCnaSinEstatus()));
    }

    // property wsLeiEmpleados
    @Test
    void testGetterSetterWsLeiEmpleados() {

        assertNotNull(wsContadores.getWsLeiEmpleados());

        int vint = 12345;
        wsContadores.setWsLeiEmpleados(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiEmpleados()));
    }

    // property wsLeiExtranjeros
    @Test
    void testGetterSetterWsLeiExtranjeros() {

        assertNotNull(wsContadores.getWsLeiExtranjeros());

        int vint = 12345;
        wsContadores.setWsLeiExtranjeros(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiExtranjeros()));
    }

    // property wsLeiGfvtagt
    @Test
    void testGetterSetterWsLeiGfvtagt() {

        assertNotNull(wsContadores.getWsLeiGfvtagt());

        int vint = 12345;
        wsContadores.setWsLeiGfvtagt(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGfvtagt()));
    }

    // property wsLeiGfvtcna
    @Test
    void testGetterSetterWsLeiGfvtcna() {

        assertNotNull(wsContadores.getWsLeiGfvtcna());

        int vint = 12345;
        wsContadores.setWsLeiGfvtcna(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGfvtcna()));
    }

    // property wsLeiGfvtpra
    @Test
    void testGetterSetterWsLeiGfvtpra() {

        assertNotNull(wsContadores.getWsLeiGfvtpra());

        int vint = 12345;
        wsContadores.setWsLeiGfvtpra(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGfvtpra()));
    }

    // property wsLeiCurDma
    @Test
    void testGetterSetterWsLeiCurDma() {

        assertNotNull(wsContadores.getWsLeiCurDma());

        int vint = 12345;
        wsContadores.setWsLeiCurDma(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCurDma()));
    }

    // property wsLeiGcctmnd
    @Test
    void testGetterSetterWsLeiGcctmnd() {

        assertNotNull(wsContadores.getWsLeiGcctmnd());

        int vint = 12345;
        wsContadores.setWsLeiGcctmnd(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGcctmnd()));
    }

    // property wsLeiGcctpob
    @Test
    void testGetterSetterWsLeiGcctpob() {

        assertNotNull(wsContadores.getWsLeiGcctpob());

        int vint = 12345;
        wsContadores.setWsLeiGcctpob(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGcctpob()));
    }

    // property wsLeiGfvtcma
    @Test
    void testGetterSetterWsLeiGfvtcma() {

        assertNotNull(wsContadores.getWsLeiGfvtcma());

        int vint = 12345;
        wsContadores.setWsLeiGfvtcma(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGfvtcma()));
    }

    // property wsLeiGcctedo
    @Test
    void testGetterSetterWsLeiGcctedo() {

        assertNotNull(wsContadores.getWsLeiGcctedo());

        int vint = 12345;
        wsContadores.setWsLeiGcctedo(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGcctedo()));
    }

    // property wsLeiGcctpai
    @Test
    void testGetterSetterWsLeiGcctpai() {

        assertNotNull(wsContadores.getWsLeiGcctpai());

        int vint = 12345;
        wsContadores.setWsLeiGcctpai(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiGcctpai()));
    }

    // property wsSinGfvtagt
    @Test
    void testGetterSetterWsSinGfvtagt() {

        assertNotNull(wsContadores.getWsSinGfvtagt());

        int vint = 12345;
        wsContadores.setWsSinGfvtagt(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGfvtagt()));
    }

    // property wsSinGfvtcna
    @Test
    void testGetterSetterWsSinGfvtcna() {

        assertNotNull(wsContadores.getWsSinGfvtcna());

        int vint = 12345;
        wsContadores.setWsSinGfvtcna(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGfvtcna()));
    }

    // property wsSinGfvtdsc
    @Test
    void testGetterSetterWsSinGfvtdsc() {

        assertNotNull(wsContadores.getWsSinGfvtdsc());

        int vint = 12345;
        wsContadores.setWsSinGfvtdsc(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGfvtdsc()));
    }

    // property wsSinEsquemaFact
    @Test
    void testGetterSetterWsSinEsquemaFact() {

        assertNotNull(wsContadores.getWsSinEsquemaFact());

        int vint = 12345;
        wsContadores.setWsSinEsquemaFact(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinEsquemaFact()));
    }

    // property wsSinGfvtpra
    @Test
    void testGetterSetterWsSinGfvtpra() {

        assertNotNull(wsContadores.getWsSinGfvtpra());

        int vint = 12345;
        wsContadores.setWsSinGfvtpra(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGfvtpra()));
    }

    // property wsSinGfvtdma
    @Test
    void testGetterSetterWsSinGfvtdma() {

        assertNotNull(wsContadores.getWsSinGfvtdma());

        int vint = 12345;
        wsContadores.setWsSinGfvtdma(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGfvtdma()));
    }

    // property wsLeiCurDmaNulos
    @Test
    void testGetterSetterWsLeiCurDmaNulos() {

        assertNotNull(wsContadores.getWsLeiCurDmaNulos());

        int vint = 12345;
        wsContadores.setWsLeiCurDmaNulos(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsLeiCurDmaNulos()));
    }

    // property wsSinGcctmnd
    @Test
    void testGetterSetterWsSinGcctmnd() {

        assertNotNull(wsContadores.getWsSinGcctmnd());

        int vint = 12345;
        wsContadores.setWsSinGcctmnd(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGcctmnd()));
    }

    // property wsSinGcctpob
    @Test
    void testGetterSetterWsSinGcctpob() {

        assertNotNull(wsContadores.getWsSinGcctpob());

        int vint = 12345;
        wsContadores.setWsSinGcctpob(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGcctpob()));
    }

    // property wsSinGfvtcma
    @Test
    void testGetterSetterWsSinGfvtcma() {

        assertNotNull(wsContadores.getWsSinGfvtcma());

        int vint = 12345;
        wsContadores.setWsSinGfvtcma(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGfvtcma()));
    }

    // property wsSinGcctedo
    @Test
    void testGetterSetterWsSinGcctedo() {

        assertNotNull(wsContadores.getWsSinGcctedo());

        int vint = 12345;
        wsContadores.setWsSinGcctedo(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGcctedo()));
    }

    // property wsSinGcctpai
    @Test
    void testGetterSetterWsSinGcctpai() {

        assertNotNull(wsContadores.getWsSinGcctpai());

        int vint = 12345;
        wsContadores.setWsSinGcctpai(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinGcctpai()));
    }

    // property wsSinRazSocial
    @Test
    void testGetterSetterWsSinRazSocial() {

        assertNotNull(wsContadores.getWsSinRazSocial());

        int vint = 12345;
        wsContadores.setWsSinRazSocial(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinRazSocial()));
    }

    // property wsSinDirFiscal
    @Test
    void testGetterSetterWsSinDirFiscal() {

        assertNotNull(wsContadores.getWsSinDirFiscal());

        int vint = 12345;
        wsContadores.setWsSinDirFiscal(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinDirFiscal()));
    }

    // property wsConDirFiscal
    @Test
    void testGetterSetterWsConDirFiscal() {

        assertNotNull(wsContadores.getWsConDirFiscal());

        int vint = 12345;
        wsContadores.setWsConDirFiscal(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsConDirFiscal()));
    }

    // property wsSinDirReceptor
    @Test
    void testGetterSetterWsSinDirReceptor() {

        assertNotNull(wsContadores.getWsSinDirReceptor());

        int vint = 12345;
        wsContadores.setWsSinDirReceptor(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsSinDirReceptor()));
    }

    // property wsRegsGrabe
    @Test
    void testGetterSetterWsRegsGrabe() {

        assertNotNull(wsContadores.getWsRegsGrabe());

        int vint = 12345;
        wsContadores.setWsRegsGrabe(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsRegsGrabe()));
    }

    // property wsCont
    @Test
    void testGetterSetterWsCont() {

        assertNotNull(wsContadores.getWsCont());

        int vint = 12345;
        wsContadores.setWsCont(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsCont()));
    }

    // property wsExcluidos
    @Test
    void testGetterSetterWsExcluidos() {

        assertNotNull(wsContadores.getWsExcluidos());

        int vint = 12345;
        wsContadores.setWsExcluidos(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsExcluidos()));
    }
}

