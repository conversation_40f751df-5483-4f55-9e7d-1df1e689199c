package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-GER0.
 *
 */
class TablaGer0Test {

    private TablaGer0 tablaGer0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaGer0 = new TablaGer0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tGer0
    @Test
    void testGetterSetterTGer0() {

        assertNotNull(tablaGer0.getTGer0());
        assertNotNull(tablaGer0.getTGer0(1));
    }

    // property tGer0Elem
    @Test
    void testGetterSetterTGer0Elem() {

        // Property: tGer0.tGer0Elem -> 15 T-GER0-ELEM UsedAsParameter
        assertNotNull(tablaGer0.getTGer0Elem());
    }

    // property sCnaGerZon
    @Test
    void testGetterSetterSCnaGerZon() {

        // Property: tGer0.tGer0Elem.sCnaGerZon -> 20 S-CNA-GER-ZON X(04)
        assertNotNull(tablaGer0.getSCnaGerZon());
        String vString = "ABCa";
        tablaGer0.setSCnaGerZon(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tablaGer0.setSCnaGerZon(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sGerLimitad
    @Test
    void testGetterSetterSGerLimitad() {

        // Property: tGer0.tGer0Elem.sGerLimitad -> 20 S-GER-LIMITAD X(01)
        assertNotNull(tablaGer0.getSGerLimitad());
        String vString = "A";
        tablaGer0.setSGerLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tablaGer0.setSGerLimitad(vIAlphanumericValue); // IAlphanumericValue
    }
}

