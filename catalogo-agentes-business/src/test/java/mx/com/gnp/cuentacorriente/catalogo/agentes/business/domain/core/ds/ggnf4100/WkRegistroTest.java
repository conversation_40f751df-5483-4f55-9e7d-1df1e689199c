package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WK-REGISTRO.
 *
 */
class WkRegistroTest {

    private WkRegistro wkRegistro;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wkRegistro = new WkRegistro();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WkRegistro wkRegistro2 = new WkRegistro(parent);
        assertNotNull(wkRegistro2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 137);
        WkRegistro wkRegistro3 = new WkRegistro(parent, sibling);
        assertNotNull(wkRegistro3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wkRegistro.getTablaTabAfe());

        assertNotNull(wkRegistro.getTablaNum());

        assertNotNull(wkRegistro.getTablaTipOpe());

        assertNotNull(wkRegistro.getTablaCodSql());

        assertNotNull(wkRegistro.getTablaCodEdo());

        assertNotNull(wkRegistro.getTablaCadOpe());

        assertNotNull(wkRegistro.getTablaAgtIncEss());

        assertNotNull(wkRegistro.getTablaAgtIdrInc());

        assertNotNull(wkRegistro.getTablaCdusuari());

        assertNotNull(wkRegistro.getTablaCdempusu());

        assertNotNull(wkRegistro.getTablaNoprogra());

        assertNotNull(wkRegistro.getTablaTsultmod());

    }

    // property tablaTabAfe
    @Test
    void testGetterSetterTablaTabAfe() {

        assertNotNull(wkRegistro.getTablaTabAfe());

        String vString = "ABCabc12";
        wkRegistro.setTablaTabAfe(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaTabAfe()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wkRegistro.setTablaTabAfe(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaTabAfe()));
    }

    // property tablaNum
    @Test
    void testGetterSetterTablaNum() {

        assertNotNull(wkRegistro.getTablaNum());

        String vString = "ABCabc123";
        wkRegistro.setTablaNum(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc123");
        wkRegistro.setTablaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaNum()));
    }

    // property tablaTipOpe
    @Test
    void testGetterSetterTablaTipOpe() {

        assertNotNull(wkRegistro.getTablaTipOpe());

        String vString = "A";
        wkRegistro.setTablaTipOpe(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaTipOpe()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wkRegistro.setTablaTipOpe(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaTipOpe()));
    }

    // property tablaCodSql
    @Test
    void testGetterSetterTablaCodSql() {

        assertNotNull(wkRegistro.getTablaCodSql());

        String vString = "ABCabc123";
        wkRegistro.setTablaCodSql(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaCodSql()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc123");
        wkRegistro.setTablaCodSql(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaCodSql()));
    }

    // property tablaCodEdo
    @Test
    void testGetterSetterTablaCodEdo() {

        assertNotNull(wkRegistro.getTablaCodEdo());

        String vString = "ABCab";
        wkRegistro.setTablaCodEdo(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaCodEdo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wkRegistro.setTablaCodEdo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaCodEdo()));
    }

    // property tablaCadOpe
    @Test
    void testGetterSetterTablaCadOpe() {

        assertNotNull(wkRegistro.getTablaCadOpe());

        String vString = "ABCabc1230";
        wkRegistro.setTablaCadOpe(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaCadOpe()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wkRegistro.setTablaCadOpe(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaCadOpe()));
    }

    // property tablaAgtIncEss
    @Test
    void testGetterSetterTablaAgtIncEss() {

        assertNotNull(wkRegistro.getTablaAgtIncEss());

        String vString = "ABCa";
        wkRegistro.setTablaAgtIncEss(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaAgtIncEss()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wkRegistro.setTablaAgtIncEss(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaAgtIncEss()));
    }

    // property tablaAgtIdrInc
    @Test
    void testGetterSetterTablaAgtIdrInc() {

        assertNotNull(wkRegistro.getTablaAgtIdrInc());

        String vString = "ABCabc123";
        wkRegistro.setTablaAgtIdrInc(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaAgtIdrInc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc123");
        wkRegistro.setTablaAgtIdrInc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaAgtIdrInc()));
    }

    // property tablaCdusuari
    @Test
    void testGetterSetterTablaCdusuari() {

        assertNotNull(wkRegistro.getTablaCdusuari());

        String vString = "ABCabc1230";
        wkRegistro.setTablaCdusuari(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaCdusuari()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wkRegistro.setTablaCdusuari(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaCdusuari()));
    }

    // property tablaCdempusu
    @Test
    void testGetterSetterTablaCdempusu() {

        assertNotNull(wkRegistro.getTablaCdempusu());

        String vString = "ABCa";
        wkRegistro.setTablaCdempusu(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaCdempusu()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wkRegistro.setTablaCdempusu(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaCdempusu()));
    }

    // property tablaNoprogra
    @Test
    void testGetterSetterTablaNoprogra() {

        assertNotNull(wkRegistro.getTablaNoprogra());

        String vString = "ABCabc12";
        wkRegistro.setTablaNoprogra(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaNoprogra()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wkRegistro.setTablaNoprogra(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaNoprogra()));
    }

    // property tablaTsultmod
    @Test
    void testGetterSetterTablaTsultmod() {

        assertNotNull(wkRegistro.getTablaTsultmod());

        String vString = "ABCabc1230";
        wkRegistro.setTablaTsultmod(vString); // String
        assertEquals(asStr(vString), asStr(wkRegistro.getTablaTsultmod()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wkRegistro.setTablaTsultmod(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wkRegistro.getTablaTsultmod()));
    }
}

