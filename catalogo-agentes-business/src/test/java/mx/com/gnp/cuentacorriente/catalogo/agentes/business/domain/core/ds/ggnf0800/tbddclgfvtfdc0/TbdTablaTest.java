package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.tbddclgfvtfdc0;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TBD-TABLA.
 *
 */
class TbdTablaTest {

    private TbdTabla tbdTabla;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tbdTabla = new TbdTabla(1);
        tbdTabla.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TbdTabla tbdTabla2 = new TbdTabla(parent, 1);
        assertNotNull(tbdTabla2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TbdTabla tbdTabla3 = new TbdTabla(parent, sibling, 1);
        assertNotNull(tbdTabla3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tbdTabla.getTbdAgtIdr());

        assertNotNull(tbdTabla.getTbdEmpCve());

        assertNotNull(tbdTabla.getTbdCnaNum());

        assertNotNull(tbdTabla.getTbdRamCve());

        assertNotNull(tbdTabla.getTbdSraCve());

    }

    // property tbdAgtIdr
    @Test
    void testGetterSetterTbdAgtIdr() {

        assertNotNull(tbdTabla.getTbdAgtIdr());

        int vint = 123456789;
        tbdTabla.setTbdAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(tbdTabla.getTbdAgtIdr()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        tbdTabla.setTbdAgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbdTabla.getTbdAgtIdr()));
    }

    // property tbdEmpCve
    @Test
    void testGetterSetterTbdEmpCve() {

        assertNotNull(tbdTabla.getTbdEmpCve());

        String vString = "ABCab";
        tbdTabla.setTbdEmpCve(vString); // String
        assertEquals(asStr(vString), asStr(tbdTabla.getTbdEmpCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tbdTabla.setTbdEmpCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbdTabla.getTbdEmpCve()));
    }

    // property tbdCnaNum
    @Test
    void testGetterSetterTbdCnaNum() {

        assertNotNull(tbdTabla.getTbdCnaNum());

        int vint = 1234;
        tbdTabla.setTbdCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(tbdTabla.getTbdCnaNum()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbdTabla.setTbdCnaNum(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbdTabla.getTbdCnaNum()));
    }

    // property tbdRamCve
    @Test
    void testGetterSetterTbdRamCve() {

        assertNotNull(tbdTabla.getTbdRamCve());

        String vString = "AB";
        tbdTabla.setTbdRamCve(vString); // String
        assertEquals(asStr(vString), asStr(tbdTabla.getTbdRamCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        tbdTabla.setTbdRamCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbdTabla.getTbdRamCve()));
    }

    // property tbdSraCve
    @Test
    void testGetterSetterTbdSraCve() {

        assertNotNull(tbdTabla.getTbdSraCve());

        String vString = "ABCa";
        tbdTabla.setTbdSraCve(vString); // String
        assertEquals(asStr(vString), asStr(tbdTabla.getTbdSraCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tbdTabla.setTbdSraCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbdTabla.getTbdSraCve()));
    }
}

