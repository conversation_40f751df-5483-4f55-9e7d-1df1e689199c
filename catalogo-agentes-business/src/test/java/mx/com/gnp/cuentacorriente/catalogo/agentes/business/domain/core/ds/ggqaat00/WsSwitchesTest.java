package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-SWITCHES.
 *
 */
class WsSwitchesTest {

    private WsSwitches wsSwitches;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsSwitches = new WsSwitches();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsSwitches.setWsTelok("");
        assertEquals("", wsSwitches.getWsTelok());

        wsSwitches.setWsNoesp("");
        assertEquals("", wsSwitches.getWsNoesp());

        wsSwitches.setWsFinAgt("");
        assertEquals("", wsSwitches.getWsFinAgt());

        wsSwitches.setWsFinOfn("");
        assertEquals("", wsSwitches.getWsFinOfn());

        wsSwitches.setWsFinCeo("");
        assertEquals("", wsSwitches.getWsFinCeo());

        wsSwitches.setWsPhone("");
        assertEquals("", wsSwitches.getWsPhone());

        wsSwitches.setWsFinDma("");
        assertEquals("", wsSwitches.getWsFinDma());

        wsSwitches.setWsOkDma("");
        assertEquals("", wsSwitches.getWsOkDma());

        wsSwitches.setSwAgtLuv("N");
        assertEquals("N", wsSwitches.getSwAgtLuv());

        wsSwitches.setSwGteLuv("N");
        assertEquals("N", wsSwitches.getSwGteLuv());

    }

    // property wsTelok
    @Test
    void testGetterSetterWsTelok() {

        assertNotNull(wsSwitches.getWsTelok());

        String vString = "A";
        wsSwitches.setWsTelok(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsTelok()));
        // Conditionals (88) - WS-TELOK
        wsSwitches.setTelok();
        assertTrue(wsSwitches.isTelok());

    }

    // property wsNoesp
    @Test
    void testGetterSetterWsNoesp() {

        assertNotNull(wsSwitches.getWsNoesp());

        String vString = "A";
        wsSwitches.setWsNoesp(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsNoesp()));
        // Conditionals (88) - WS-NOESP
        wsSwitches.setNoesp();
        assertTrue(wsSwitches.isNoesp());

    }

    // property wsFinAgt
    @Test
    void testGetterSetterWsFinAgt() {

        assertNotNull(wsSwitches.getWsFinAgt());

        String vString = "A";
        wsSwitches.setWsFinAgt(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsFinAgt()));
        // Conditionals (88) - WS-FIN-AGT
        wsSwitches.setSwFinAgt();
        assertTrue(wsSwitches.isSwFinAgt());

    }

    // property wsFinOfn
    @Test
    void testGetterSetterWsFinOfn() {

        assertNotNull(wsSwitches.getWsFinOfn());

        String vString = "A";
        wsSwitches.setWsFinOfn(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsFinOfn()));
        // Conditionals (88) - WS-FIN-OFN
        wsSwitches.setSwFinOfn();
        assertTrue(wsSwitches.isSwFinOfn());

    }

    // property wsFinCeo
    @Test
    void testGetterSetterWsFinCeo() {

        assertNotNull(wsSwitches.getWsFinCeo());

        String vString = "A";
        wsSwitches.setWsFinCeo(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsFinCeo()));
        // Conditionals (88) - WS-FIN-CEO
        wsSwitches.setSwFinCeo();
        assertTrue(wsSwitches.isSwFinCeo());

    }

    // property wsPhone
    @Test
    void testGetterSetterWsPhone() {

        assertNotNull(wsSwitches.getWsPhone());

        String vString = "A";
        wsSwitches.setWsPhone(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsPhone()));
        // Conditionals (88) - WS-PHONE
        wsSwitches.setSwPhone();
        assertTrue(wsSwitches.isSwPhone());

    }

    // property wsFinDma
    @Test
    void testGetterSetterWsFinDma() {

        assertNotNull(wsSwitches.getWsFinDma());

        String vString = "A";
        wsSwitches.setWsFinDma(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsFinDma()));
        // Conditionals (88) - WS-FIN-DMA
        wsSwitches.setSwFinDma();
        assertTrue(wsSwitches.isSwFinDma());

    }

    // property wsOkDma
    @Test
    void testGetterSetterWsOkDma() {

        assertNotNull(wsSwitches.getWsOkDma());

        String vString = "A";
        wsSwitches.setWsOkDma(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getWsOkDma()));
        // Conditionals (88) - WS-OK-DMA
        wsSwitches.setSwOkDma();
        assertTrue(wsSwitches.isSwOkDma());

    }

    // property swAgtLuv
    @Test
    void testGetterSetterSwAgtLuv() {

        assertNotNull(wsSwitches.getSwAgtLuv());

        String vString = "A";
        wsSwitches.setSwAgtLuv(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getSwAgtLuv()));
        // Conditionals (88) - SW-AGT-LUV
        wsSwitches.setSiAgtLuv();
        assertTrue(wsSwitches.isSiAgtLuv());

    }

    // property swGteLuv
    @Test
    void testGetterSetterSwGteLuv() {

        assertNotNull(wsSwitches.getSwGteLuv());

        String vString = "A";
        wsSwitches.setSwGteLuv(vString); // String
        assertEquals(asStr(vString), asStr(wsSwitches.getSwGteLuv()));
        // Conditionals (88) - SW-GTE-LUV
        wsSwitches.setSiGteLuv();
        assertTrue(wsSwitches.isSiGteLuv());

    }
}

