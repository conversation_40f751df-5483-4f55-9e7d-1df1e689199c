package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-PJE-ISR-R.
 *
 */
class WsPjeIsrRTest {

    private WsPjeIsrR wsPjeIsrR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsPjeIsrR = new WsPjeIsrR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsPjeIsrR wsPjeIsrR2 = new WsPjeIsrR(parent);
        assertNotNull(wsPjeIsrR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 5);
        WsPjeIsrR wsPjeIsrR3 = new WsPjeIsrR(parent, sibling);
        assertNotNull(wsPjeIsrR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsPjeIsrR.getRsPjeIsr3());

        assertNotNull(wsPjeIsrR.getRsPjeIsr2());

    }

    // property rsPjeIsr3
    @Test
    void testGetterSetterRsPjeIsr3() {

        assertNotNull(wsPjeIsrR.getRsPjeIsr3());

        String vString = "ABC";
        wsPjeIsrR.setRsPjeIsr3(vString); // String
        assertEquals(asStr(vString), asStr(wsPjeIsrR.getRsPjeIsr3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsPjeIsrR.setRsPjeIsr3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPjeIsrR.getRsPjeIsr3()));
    }

    // property rsPjeIsr2
    @Test
    void testGetterSetterRsPjeIsr2() {

        assertNotNull(wsPjeIsrR.getRsPjeIsr2());

        String vString = "AB";
        wsPjeIsrR.setRsPjeIsr2(vString); // String
        assertEquals(asStr(vString), asStr(wsPjeIsrR.getRsPjeIsr2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsPjeIsrR.setRsPjeIsr2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPjeIsrR.getRsPjeIsr2()));
    }
}

