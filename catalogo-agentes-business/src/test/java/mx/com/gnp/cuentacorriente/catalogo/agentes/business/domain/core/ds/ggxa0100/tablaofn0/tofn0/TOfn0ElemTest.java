package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaofn0.tofn0;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-OFN0-ELEM.
 *
 */
class TOfn0ElemTest {

    private TOfn0Elem tOfn0Elem;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tOfn0Elem = new TOfn0Elem();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TOfn0Elem tOfn0Elem2 = new TOfn0Elem(parent);
        assertNotNull(tOfn0Elem2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 5);
        TOfn0Elem tOfn0Elem3 = new TOfn0Elem(parent, sibling);
        assertNotNull(tOfn0Elem3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tOfn0Elem.getSCnaOfnCve());

        assertNotNull(tOfn0Elem.getSOfnLimitad());

    }

    // property sCnaOfnCve
    @Test
    void testGetterSetterSCnaOfnCve() {

        assertNotNull(tOfn0Elem.getSCnaOfnCve());

        String vString = "ABCa";
        tOfn0Elem.setSCnaOfnCve(vString); // String
        assertEquals(asStr(vString), asStr(tOfn0Elem.getSCnaOfnCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tOfn0Elem.setSCnaOfnCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tOfn0Elem.getSCnaOfnCve()));
    }

    // property sOfnLimitad
    @Test
    void testGetterSetterSOfnLimitad() {

        assertNotNull(tOfn0Elem.getSOfnLimitad());

        String vString = "A";
        tOfn0Elem.setSOfnLimitad(vString); // String
        assertEquals(asStr(vString), asStr(tOfn0Elem.getSOfnLimitad()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tOfn0Elem.setSOfnLimitad(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tOfn0Elem.getSOfnLimitad()));
    }
}

