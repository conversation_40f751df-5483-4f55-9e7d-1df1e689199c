package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: C-CONTADORES.
 *
 */
class CContadoresTest {

    private CContadores cContadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        cContadores = new CContadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertTrue(cContadores.getCFetchAgtced().isZero());

    }

    // property cFetchAgtced
    @Test
    void testGetterSetterCFetchAgtced() {

        assertNotNull(cContadores.getCFetchAgtced());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        cContadores.setCFetchAgtced(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, cContadores.getCFetchAgtced().toBigDecimal());

        int vint = 123456789;
        cContadores.setCFetchAgtced(vint); // int
        assertEquals(asInt(vint), asInt(cContadores.getCFetchAgtced()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        cContadores.setCFetchAgtced(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(cContadores.getCFetchAgtced()));
    }
}

