package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CALLE-NUM-IN.
 *
 */
class WsCalleNumInTest {

    private WsCalleNumIn wsCalleNumIn;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCalleNumIn = new WsCalleNumIn();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsCalleNumIn wsCalleNumIn2 = new WsCalleNumIn(parent);
        assertNotNull(wsCalleNumIn2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 35);
        WsCalleNumIn wsCalleNumIn3 = new WsCalleNumIn(parent, sibling);
        assertNotNull(wsCalleNumIn3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsCalleNumIn.getWsPkCalle());

        assertNotNull(wsCalleNumIn.getWsPkNum());

        assertNotNull(wsCalleNumIn.getWsPkInt());

    }

    // property wsPkCalle
    @Test
    void testGetterSetterWsPkCalle() {

        assertNotNull(wsCalleNumIn.getWsPkCalle());

        String vString = "ABCabc1230";
        wsCalleNumIn.setWsPkCalle(vString); // String
        assertEquals(asStr(vString), asStr(wsCalleNumIn.getWsPkCalle()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCalleNumIn.setWsPkCalle(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCalleNumIn.getWsPkCalle()));
    }

    // property wsPkNum
    @Test
    void testGetterSetterWsPkNum() {

        assertNotNull(wsCalleNumIn.getWsPkNum());

        String vString = "ABCab";
        wsCalleNumIn.setWsPkNum(vString); // String
        assertEquals(asStr(vString), asStr(wsCalleNumIn.getWsPkNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCalleNumIn.setWsPkNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCalleNumIn.getWsPkNum()));
    }

    // property wsPkInt
    @Test
    void testGetterSetterWsPkInt() {

        assertNotNull(wsCalleNumIn.getWsPkInt());

        String vString = "ABCab";
        wsCalleNumIn.setWsPkInt(vString); // String
        assertEquals(asStr(vString), asStr(wsCalleNumIn.getWsPkInt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCalleNumIn.setWsPkInt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCalleNumIn.getWsPkInt()));
    }
}

