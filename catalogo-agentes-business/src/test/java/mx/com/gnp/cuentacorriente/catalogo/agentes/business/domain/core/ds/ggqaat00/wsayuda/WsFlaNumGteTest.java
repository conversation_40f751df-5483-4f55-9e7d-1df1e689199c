package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FLA-NUM-GTE.
 *
 */
class WsFlaNumGteTest {

    private WsFlaNumGte wsFlaNumGte;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFlaNumGte = new WsFlaNumGte();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFlaNumGte wsFlaNumGte2 = new WsFlaNumGte(parent);
        assertNotNull(wsFlaNumGte2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFlaNumGte wsFlaNumGte3 = new WsFlaNumGte(parent, sibling);
        assertNotNull(wsFlaNumGte3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFlaNumGte.getWsFlaNumGte_1());

        assertNotNull(wsFlaNumGte.getWsFlaNumGteX());

        assertNotNull(wsFlaNumGte.getWsFlaNumGte_2());

    }

    // property wsFlaNumGte_1
    @Test
    void testGetterSetterWsFlaNumGte_1() {

        assertNotNull(wsFlaNumGte.getWsFlaNumGte_1());

        String vString = "A";
        wsFlaNumGte.setWsFlaNumGte_1(vString); // String
        assertEquals(asStr(vString), asStr(wsFlaNumGte.getWsFlaNumGte_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFlaNumGte.setWsFlaNumGte_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFlaNumGte.getWsFlaNumGte_1()));
    }

    // property wsFlaNumGteX
    @Test
    void testGetterSetterWsFlaNumGteX() {

        assertNotNull(wsFlaNumGte.getWsFlaNumGteX());

        String vString = "AB";
        wsFlaNumGte.setWsFlaNumGteX(vString); // String
        assertEquals(asStr(vString), asStr(wsFlaNumGte.getWsFlaNumGteX()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsFlaNumGte.setWsFlaNumGteX(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFlaNumGte.getWsFlaNumGteX()));
    }

    // property wsFlaNumGte_2
    @Test
    void testGetterSetterWsFlaNumGte_2() {

        assertNotNull(wsFlaNumGte.getWsFlaNumGte_2());

        int vint = 12345;
        wsFlaNumGte.setWsFlaNumGte_2(vint); // int
        assertEquals(asInt(vint), asInt(wsFlaNumGte.getWsFlaNumGte_2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        wsFlaNumGte.setWsFlaNumGte_2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFlaNumGte.getWsFlaNumGte_2()));
    }
}

