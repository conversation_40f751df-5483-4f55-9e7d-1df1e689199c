package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-ARCHDMAH.
 *
 */
class RegArchdmahTest {

    private RegArchdmah regArchdmah;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regArchdmah = new RegArchdmah();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegArchdmah regArchdmah2 = new RegArchdmah(parent);
        assertNotNull(regArchdmah2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 347);
        RegArchdmah regArchdmah3 = new RegArchdmah(parent, sibling);
        assertNotNull(regArchdmah3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regArchdmah.getAgtIdr3());

        assertNotNull(regArchdmah.getAgtFecCamRazSo3());

        assertNotNull(regArchdmah.getDmaIdr3());

        assertNotNull(regArchdmah.getDmaCae3());

        assertNotNull(regArchdmah.getDmaNum3());

        assertNotNull(regArchdmah.getDmaInt3());

        assertNotNull(regArchdmah.getDmaCol3());

        assertNotNull(regArchdmah.getTdmCve3());

        assertNotNull(regArchdmah.getPobCve3());

        assertNotNull(regArchdmah.getCpoCve3());

        assertNotNull(regArchdmah.getMndCve3());

        assertNotNull(regArchdmah.getDmaDirExt3());

        assertNotNull(regArchdmah.getEssRek3());

    }

    // property agtIdr3
    @Test
    void testGetterSetterAgtIdr3() {

        assertNotNull(regArchdmah.getAgtIdr3());

        int vint = 123456789;
        regArchdmah.setAgtIdr3(vint); // int
        assertEquals(asInt(vint), asInt(regArchdmah.getAgtIdr3()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchdmah.setAgtIdr3(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdmah.getAgtIdr3()));
    }

    // property agtFecCamRazSo3
    @Test
    void testGetterSetterAgtFecCamRazSo3() {

        assertNotNull(regArchdmah.getAgtFecCamRazSo3());

        String vString = "ABCabc1230";
        regArchdmah.setAgtFecCamRazSo3(vString); // String
        assertEquals(asStr(vString), asStr(regArchdmah.getAgtFecCamRazSo3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdmah.setAgtFecCamRazSo3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdmah.getAgtFecCamRazSo3()));
    }

    // property dmaIdr3
    @Test
    void testGetterSetterDmaIdr3() {

        assertNotNull(regArchdmah.getDmaIdr3());

        int vint = 12345;
        regArchdmah.setDmaIdr3(vint); // int
        assertEquals(asInt(vint), asInt(regArchdmah.getDmaIdr3()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchdmah.setDmaIdr3(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdmah.getDmaIdr3()));
    }

    // property dmaCae3
    @Test
    void testGetterSetterDmaCae3() {

        assertNotNull(regArchdmah.getDmaCae3());

        String vString = "ABCabc1230";
        regArchdmah.setDmaCae3(vString); // String
        assertEquals(asStr(vString), asStr(regArchdmah.getDmaCae3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdmah.setDmaCae3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdmah.getDmaCae3()));
    }

    // property dmaNum3
    @Test
    void testGetterSetterDmaNum3() {

        assertNotNull(regArchdmah.getDmaNum3());

        String vString = "ABCab";
        regArchdmah.setDmaNum3(vString); // String
        assertEquals(asStr(vString), asStr(regArchdmah.getDmaNum3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regArchdmah.setDmaNum3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdmah.getDmaNum3()));
    }

    // property dmaInt3
    @Test
    void testGetterSetterDmaInt3() {

        assertNotNull(regArchdmah.getDmaInt3());

        String vString = "ABCab";
        regArchdmah.setDmaInt3(vString); // String
        assertEquals(asStr(vString), asStr(regArchdmah.getDmaInt3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regArchdmah.setDmaInt3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdmah.getDmaInt3()));
    }

    // property dmaCol3
    @Test
    void testGetterSetterDmaCol3() {

        assertNotNull(regArchdmah.getDmaCol3());

        String vString = "ABCabc1230";
        regArchdmah.setDmaCol3(vString); // String
        assertEquals(asStr(vString), asStr(regArchdmah.getDmaCol3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdmah.setDmaCol3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdmah.getDmaCol3()));
    }

    // property tdmCve3
    @Test
    void testGetterSetterTdmCve3() {

        assertNotNull(regArchdmah.getTdmCve3());

        int vint = 12345;
        regArchdmah.setTdmCve3(vint); // int
        assertEquals(asInt(vint), asInt(regArchdmah.getTdmCve3()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchdmah.setTdmCve3(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdmah.getTdmCve3()));
    }

    // property pobCve3
    @Test
    void testGetterSetterPobCve3() {

        assertNotNull(regArchdmah.getPobCve3());

        int vint = 123456789;
        regArchdmah.setPobCve3(vint); // int
        assertEquals(asInt(vint), asInt(regArchdmah.getPobCve3()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchdmah.setPobCve3(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdmah.getPobCve3()));
    }

    // property cpoCve3
    @Test
    void testGetterSetterCpoCve3() {

        assertNotNull(regArchdmah.getCpoCve3());

        int vint = 123456789;
        regArchdmah.setCpoCve3(vint); // int
        assertEquals(asInt(vint), asInt(regArchdmah.getCpoCve3()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchdmah.setCpoCve3(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdmah.getCpoCve3()));
    }

    // property mndCve3
    @Test
    void testGetterSetterMndCve3() {

        assertNotNull(regArchdmah.getMndCve3());

        int vint = 123456789;
        regArchdmah.setMndCve3(vint); // int
        assertEquals(asInt(vint), asInt(regArchdmah.getMndCve3()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchdmah.setMndCve3(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdmah.getMndCve3()));
    }

    // property dmaDirExt3
    @Test
    void testGetterSetterDmaDirExt3() {

        assertNotNull(regArchdmah.getDmaDirExt3());

        String vString = "ABCabc1230";
        regArchdmah.setDmaDirExt3(vString); // String
        assertEquals(asStr(vString), asStr(regArchdmah.getDmaDirExt3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdmah.setDmaDirExt3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdmah.getDmaDirExt3()));
    }

    // property essRek3
    @Test
    void testGetterSetterEssRek3() {

        assertNotNull(regArchdmah.getEssRek3());

        String vString = "A";
        regArchdmah.setEssRek3(vString); // String
        assertEquals(asStr(vString), asStr(regArchdmah.getEssRek3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchdmah.setEssRek3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdmah.getEssRek3()));
    }
}

