package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DET2.
 *
 */
class Det2Test {

    private Det2 det2;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        det2 = new Det2();
    }


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Det2 det22 = new Det2(parent);
        assertNotNull(det22);
        AlphanumericVar sibling = new AlphanumericVar(parent, 132);
        Det2 det23 = new Det2(parent, sibling);
        assertNotNull(det23);
    }

    @Test
    void testInitialize() {
        assertNotNull(det2.getD2AgtIdr());

        assertNotNull(det2.getD2CnaNum());

        assertNotNull(det2.getD2TfpCve());

        assertNotNull(det2.getD2CnaIndGer());

        assertNotNull(det2.getD2MteCve());

        assertNotNull(det2.getD2FlaFecMotEss());

        assertNotNull(det2.getD2FlaNumGte());

        assertNotNull(det2.getD2CnaGerZonEnv());

        assertNotNull(det2.getD2UsuCveAct());

        assertNotNull(det2.getD2FecUltAct());

    }

    // property d2AgtIdr
    @Test
    void testGetterSetterD2AgtIdr() {

        assertNotNull(det2.getD2AgtIdr());

        String vString = "      123";
        det2.setD2AgtIdr(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2AgtIdr()));

        NumericVar vIValue = new NumericVar(9, 0);;
        vIValue.setValue("123456789");
        det2.setD2AgtIdr(vIValue); // IValue
        // format("123456789", "ZZZZZZZZ9")
        assertNotNull(asStr(det2.getD2AgtIdr()));
    }

    // property d2CnaNum
    @Test
    void testGetterSetterD2CnaNum() {

        assertNotNull(det2.getD2CnaNum());

        String vString = "   0";
        det2.setD2CnaNum(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2CnaNum()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det2.setD2CnaNum(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det2.getD2CnaNum()));
    }

    // property d2TfpCve
    @Test
    void testGetterSetterD2TfpCve() {

        assertNotNull(det2.getD2TfpCve());

        String vString = "   0";
        det2.setD2TfpCve(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2TfpCve()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det2.setD2TfpCve(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det2.getD2TfpCve()));
    }

    // property d2CnaIndGer
    @Test
    void testGetterSetterD2CnaIndGer() {

        assertNotNull(det2.getD2CnaIndGer());

        String vString = "A";
        det2.setD2CnaIndGer(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2CnaIndGer()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        det2.setD2CnaIndGer(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det2.getD2CnaIndGer()));
    }

    // property d2MteCve
    @Test
    void testGetterSetterD2MteCve() {

        assertNotNull(det2.getD2MteCve());

        String vString = "   0";
        det2.setD2MteCve(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2MteCve()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det2.setD2MteCve(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det2.getD2MteCve()));
    }

    // property d2FlaFecMotEss
    @Test
    void testGetterSetterD2FlaFecMotEss() {

        assertNotNull(det2.getD2FlaFecMotEss());

        String vString = "ABCabc1230";
        det2.setD2FlaFecMotEss(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2FlaFecMotEss()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det2.setD2FlaFecMotEss(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det2.getD2FlaFecMotEss()));
    }

    // property d2FlaNumGte
    @Test
    void testGetterSetterD2FlaNumGte() {

        assertNotNull(det2.getD2FlaNumGte());

        String vString = "ABCabc12";
        det2.setD2FlaNumGte(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2FlaNumGte()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        det2.setD2FlaNumGte(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det2.getD2FlaNumGte()));
    }

    // property d2CnaGerZonEnv
    @Test
    void testGetterSetterD2CnaGerZonEnv() {

        assertNotNull(det2.getD2CnaGerZonEnv());

        int vint = 1234;
        det2.setD2CnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(det2.getD2CnaGerZonEnv()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        det2.setD2CnaGerZonEnv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(det2.getD2CnaGerZonEnv()));
    }

    // property d2UsuCveAct
    @Test
    void testGetterSetterD2UsuCveAct() {

        assertNotNull(det2.getD2UsuCveAct());

        String vString = "ABCabc12";
        det2.setD2UsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2UsuCveAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        det2.setD2UsuCveAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det2.getD2UsuCveAct()));
    }

    // property d2FecUltAct
    @Test
    void testGetterSetterD2FecUltAct() {

        assertNotNull(det2.getD2FecUltAct());

        String vString = "ABCabc1230";
        det2.setD2FecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(det2.getD2FecUltAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det2.setD2FecUltAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det2.getD2FecUltAct()));
    }
}
