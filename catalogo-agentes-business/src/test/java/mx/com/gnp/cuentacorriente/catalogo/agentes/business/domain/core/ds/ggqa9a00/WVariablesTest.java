package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: W-VARIABLES.
 *
 */
class WVariablesTest {

    private WVariables wVariables;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wVariables = new WVariables();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wVariables.setWFecSist("");
        assertEquals("", wVariables.getWFecSist());

    }

    // property wFecSist
    @Test
    void testGetterSetterWFecSist() {

        assertNotNull(wVariables.getWFecSist());

        String vString = "ABCabc1230";
        wVariables.setWFecSist(vString); // String
        assertEquals(asStr(vString), asStr(wVariables.getWFecSist()));
    }
}

