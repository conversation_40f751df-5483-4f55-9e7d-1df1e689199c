package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DATOS-PARAM.
 *
 */
class WsDatosParamTest {

    private WsDatosParam wsDatosParam;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDatosParam = new WsDatosParam();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDatosParam.setWsTipoNomina(0);
        assertEquals(0, wsDatosParam.getWsTipoNomina());

    }

    // property wsTipoNomina
    @Test
    void testGetterSetterWsTipoNomina() {

        assertNotNull(wsDatosParam.getWsTipoNomina());

        int vint = 1;
        wsDatosParam.setWsTipoNomina(vint); // int
        assertEquals(asInt(vint), asInt(wsDatosParam.getWsTipoNomina()));
    }
}

