package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DET1.
 *
 */
class Det1Test {

    private Det1 det1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        det1 = new Det1();
    }


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Det1 det12 = new Det1(parent);
        assertNotNull(det12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 120);
        Det1 det13 = new Det1(parent, sibling);
        assertNotNull(det13);
    }

    @Test
    void testInitialize() {
        assertNotNull(det1.getD1FlaNum());

        assertNotNull(det1.getD1EmpCve());

        assertNotNull(det1.getD1EsaCve());

        assertNotNull(det1.getD1OfnCve());

        assertNotNull(det1.getD1OrvCve());

        assertNotNull(det1.getD1PefCve());

        assertNotNull(det1.getD1AgtNom());

        assertNotNull(det1.getD1AgtApePat());

        assertNotNull(det1.getD1AgtApeMat());

        assertNotNull(det1.getD1AgtRazSoc());

        assertNotNull(det1.getD1Obs());

    }

    // property d1FlaNum
    @Test
    void testGetterSetterD1FlaNum() {

        assertNotNull(det1.getD1FlaNum());

        String vString = "ABCabc12";
        det1.setD1FlaNum(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1FlaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        det1.setD1FlaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1FlaNum()));
    }

    // property d1EmpCve
    @Test
    void testGetterSetterD1EmpCve() {

        assertNotNull(det1.getD1EmpCve());

        String vString = "ABCab";
        det1.setD1EmpCve(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1EmpCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        det1.setD1EmpCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1EmpCve()));
    }

    // property d1EsaCve
    @Test
    void testGetterSetterD1EsaCve() {

        assertNotNull(det1.getD1EsaCve());

        String vString = "   0";
        det1.setD1EsaCve(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1EsaCve()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det1.setD1EsaCve(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det1.getD1EsaCve()));
    }

    // property d1OfnCve
    @Test
    void testGetterSetterD1OfnCve() {

        assertNotNull(det1.getD1OfnCve());

        String vString = "   0";
        det1.setD1OfnCve(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1OfnCve()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det1.setD1OfnCve(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det1.getD1OfnCve()));
    }

    // property d1OrvCve
    @Test
    void testGetterSetterD1OrvCve() {

        assertNotNull(det1.getD1OrvCve());

        String vString = "   0";
        det1.setD1OrvCve(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1OrvCve()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det1.setD1OrvCve(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det1.getD1OrvCve()));
    }

    // property d1PefCve
    @Test
    void testGetterSetterD1PefCve() {

        assertNotNull(det1.getD1PefCve());

        String vString = "   0";
        det1.setD1PefCve(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1PefCve()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det1.setD1PefCve(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det1.getD1PefCve()));
    }

    // property d1AgtNom
    @Test
    void testGetterSetterD1AgtNom() {

        assertNotNull(det1.getD1AgtNom());

        String vString = "ABCabc1230";
        det1.setD1AgtNom(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1AgtNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det1.setD1AgtNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1AgtNom()));
    }

    // property d1AgtApePat
    @Test
    void testGetterSetterD1AgtApePat() {

        assertNotNull(det1.getD1AgtApePat());

        String vString = "ABCabc1230";
        det1.setD1AgtApePat(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1AgtApePat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det1.setD1AgtApePat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1AgtApePat()));
    }

    // property d1AgtApeMat
    @Test
    void testGetterSetterD1AgtApeMat() {

        assertNotNull(det1.getD1AgtApeMat());

        String vString = "ABCabc1230";
        det1.setD1AgtApeMat(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1AgtApeMat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det1.setD1AgtApeMat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1AgtApeMat()));
    }

    // property d1AgtRazSoc
    @Test
    void testGetterSetterD1AgtRazSoc() {

        assertNotNull(det1.getD1AgtRazSoc());

        String vString = "ABCabc12";
        det1.setD1AgtRazSoc(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1AgtRazSoc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        det1.setD1AgtRazSoc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1AgtRazSoc()));
    }

    // property d1Obs
    @Test
    void testGetterSetterD1Obs() {

        assertNotNull(det1.getD1Obs());

        String vString = "ABCabc1230";
        det1.setD1Obs(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1Obs()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det1.setD1Obs(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1Obs()));
    }
}
