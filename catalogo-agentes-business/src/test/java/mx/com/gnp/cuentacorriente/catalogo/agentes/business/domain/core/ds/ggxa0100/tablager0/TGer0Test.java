package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablager0;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-GER0.
 *
 */
class TGer0Test {

    private TGer0 tGer0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tGer0 = new TGer0(1);
        tGer0.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TGer0 tGer02 = new TGer0(parent, 1);
        assertNotNull(tGer02);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TGer0 tGer03 = new TGer0(parent, sibling, 1);
        assertNotNull(tGer03);
    }

    @Test
    void testInitialize() {

    }

    // property tGer0Elem
    @Test
    void testGetterSetterTGer0Elem() {

        assertNotNull(tGer0.getTGer0Elem());
    }

    // property sCnaGerZon
    @Test
    void testGetterSetterSCnaGerZon() {

        // Property: tGer0Elem.sCnaGerZon -> 20 S-CNA-GER-ZON X(04)
        assertNotNull(tGer0.getSCnaGerZon());
        String vString = "ABCa";
        tGer0.setSCnaGerZon(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tGer0.setSCnaGerZon(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sGerLimitad
    @Test
    void testGetterSetterSGerLimitad() {

        // Property: tGer0Elem.sGerLimitad -> 20 S-GER-LIMITAD X(01)
        assertNotNull(tGer0.getSGerLimitad());
        String vString = "A";
        tGer0.setSGerLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tGer0.setSGerLimitad(vIAlphanumericValue); // IAlphanumericValue
    }
}

