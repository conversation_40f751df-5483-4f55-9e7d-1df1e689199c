package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WL-PARAMS.
 *
 */
class WlParamsTest {

    private WlParams wlParams;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wlParams = new WlParams();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WlParams wlParams2 = new WlParams(parent);
        assertNotNull(wlParams2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 11);
        WlParams wlParams3 = new WlParams(parent, sibling);
        assertNotNull(wlParams3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wlParams.getWlOpcion());

        assertNotNull(wlParams.getWlAgente());

    }

    // property wlOpcion
    @Test
    void testGetterSetterWlOpcion() {

        assertNotNull(wlParams.getWlOpcion());

        String vString = "ABCa";
        wlParams.setWlOpcion(vString); // String
        assertEquals(asStr(vString), asStr(wlParams.getWlOpcion()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wlParams.setWlOpcion(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wlParams.getWlOpcion()));
    }

    // property wlAgente
    @Test
    void testGetterSetterWlAgente() {

        assertNotNull(wlParams.getWlAgente());

        String vString = "ABCabc1";
        wlParams.setWlAgente(vString); // String
        assertEquals(asStr(vString), asStr(wlParams.getWlAgente()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wlParams.setWlAgente(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wlParams.getWlAgente()));
    }
}

