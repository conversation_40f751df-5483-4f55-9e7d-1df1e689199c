package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.banderas;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: BANDERAS-GLOBALES.
 *
 */
class BanderasGlobalesTest {

    private BanderasGlobales banderasGlobales;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        banderasGlobales = new BanderasGlobales();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        banderasGlobales.setStCatDirec("00");
        assertEquals("00", banderasGlobales.getStCatDirec());

        banderasGlobales.setWsFinCatDirecc(0);
        assertEquals(0, banderasGlobales.getWsFinCatDirecc());

    }

    // property stCatDirec
    @Test
    void testGetterSetterStCatDirec() {

        assertNotNull(banderasGlobales.getStCatDirec());

        String vString = "AB";
        banderasGlobales.setStCatDirec(vString); // String
        assertEquals(asStr(vString), asStr(banderasGlobales.getStCatDirec()));
    }

    // property wsFinCatDirecc
    @Test
    void testGetterSetterWsFinCatDirecc() {

        assertNotNull(banderasGlobales.getWsFinCatDirecc());

        int vint = 1;
        banderasGlobales.setWsFinCatDirecc(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsFinCatDirecc()));
        // Conditionals (88) - WS-FIN-CAT-DIRECC
        banderasGlobales.setFinCatDirecc();
        assertTrue(banderasGlobales.isFinCatDirecc());

    }
}

