package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DATOS-SAETA.
 *
 */
class DatosSaetaTest {

    private DatosSaeta datosSaeta;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        datosSaeta = new DatosSaeta();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        DatosSaeta datosSaeta2 = new DatosSaeta(parent);
        assertNotNull(datosSaeta2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 660);
        DatosSaeta datosSaeta3 = new DatosSaeta(parent, sibling);
        assertNotNull(datosSaeta3);
    }

    @Test
    void testInitialize() {
        assertNotNull(datosSaeta.getDatosCons());


        assertNotNull(datosSaeta.getPkClaveNal());

        assertNotNull(datosSaeta.getPkClaveProv());

        assertNotNull(datosSaeta.getPkNombre());

        assertNotNull(datosSaeta.getPkCompania());

        assertNotNull(datosSaeta.getPkFecConexion());


        assertNotNull(datosSaeta.getPkCalleP());

        assertNotNull(datosSaeta.getPkColoniaP());

        assertNotNull(datosSaeta.getPkCodPostP());

        assertNotNull(datosSaeta.getPkPoblP());

        assertNotNull(datosSaeta.getPkEstadoP());

        assertNotNull(datosSaeta.getPkPaisP());

        assertNotNull(datosSaeta.getPkTelefP());


        assertNotNull(datosSaeta.getPkCalleC());

        assertNotNull(datosSaeta.getPkColoniaC());

        assertNotNull(datosSaeta.getPkCodPostC());

        assertNotNull(datosSaeta.getPkPoblC());

        assertNotNull(datosSaeta.getPkEstadoC());

        assertNotNull(datosSaeta.getPkPaisC());

        assertNotNull(datosSaeta.getPkTelefC());


        assertNotNull(datosSaeta.getPkFecNacim());

        assertNotNull(datosSaeta.getPkNacionalidad());

        assertNotNull(datosSaeta.getPkSexo());

        assertNotNull(datosSaeta.getPkEdoCivil());

        assertNotNull(datosSaeta.getPkEscolaridad());

        assertNotNull(datosSaeta.getPkNomConyuge());

        assertNotNull(datosSaeta.getPkFecCasam());

        assertNotNull(datosSaeta.getPkStatus());

        assertNotNull(datosSaeta.getPkMotStatus());

        assertNotNull(datosSaeta.getPkFecStatus());

        assertNotNull(datosSaeta.getPkFecAlta());

        assertNotNull(datosSaeta.getPkFecTerm());

        assertNotNull(datosSaeta.getPkTipCedula());

        assertNotNull(datosSaeta.getPkNumCedula());




        assertNotNull(datosSaeta.getPkIniCedula());

        assertNotNull(datosSaeta.getPkFinCedula());

        assertNotNull(datosSaeta.getPkRamoP_1());

        assertNotNull(datosSaeta.getPkRamoP_2());

        assertNotNull(datosSaeta.getPkRamoP_3());

        assertNotNull(datosSaeta.getPkRamoP_4());

        assertNotNull(datosSaeta.getPkRamoP_5());

        assertNotNull(datosSaeta.getPkRamoP_6());

        assertNotNull(datosSaeta.getPkRamoP_7());

        assertNotNull(datosSaeta.getPkRamoP_8());

        assertNotNull(datosSaeta.getPkRamoP_9());

        assertNotNull(datosSaeta.getPkRamoP_10());

        assertNotNull(datosSaeta.getPkObservOficS());

        assertNotNull(datosSaeta.getPkRegIva());

        assertNotNull(datosSaeta.getPkTipo());

        assertNotNull(datosSaeta.getPkCodPago());

        assertNotNull(datosSaeta.getPkCvePagoImp());

        assertNotNull(datosSaeta.getPkPorIva());

        assertNotNull(datosSaeta.getPkPorIsr());

        assertNotNull(datosSaeta.getPkNumFianza());

        assertNotNull(datosSaeta.getPkCanFianza());

        assertNotNull(datosSaeta.getPkIniFianza());

        assertNotNull(datosSaeta.getPkFinFianza());

        assertNotNull(datosSaeta.getPkAgenteRec());

        assertNotNull(datosSaeta.getPkOtrasCias());

        assertNotNull(datosSaeta.getPkEsGerente());

        assertNotNull(datosSaeta.getPkZonaNal());

        assertNotNull(datosSaeta.getPkZonaProv());

        assertNotNull(datosSaeta.getPkZonaNalGte());

        assertNotNull(datosSaeta.getPkZonaProvGte());

        assertNotNull(datosSaeta.getPkOficinaNal());

        assertNotNull(datosSaeta.getPkOficinaProv());

        assertNotNull(datosSaeta.getPkFecZonaNal());

        assertNotNull(datosSaeta.getPkFecZonaProv());

        assertNotNull(datosSaeta.getPkFecZonaNalGte());

        assertNotNull(datosSaeta.getPkFecZonaProvGte());

        assertNotNull(datosSaeta.getPkFecOficinaNal());

        assertNotNull(datosSaeta.getPkFecOficinaProv());

        assertNotNull(datosSaeta.getPkCveAgrup());

        assertNotNull(datosSaeta.getPkCveEnvio());

        assertNotNull(datosSaeta.getPkCalidad());

        assertNotNull(datosSaeta.getPkJefeGpoNal());

        assertNotNull(datosSaeta.getPkJefeGpoProv());

        assertNotNull(datosSaeta.getPkFecConexionP());


        assertNotNull(datosSaeta.getPkNumCedulaP());



        assertNotNull(datosSaeta.getPkFinCedulaP());

        assertNotNull(datosSaeta.getPkStatusProv());

        assertNotNull(datosSaeta.getPkMotStatusProv());

        assertNotNull(datosSaeta.getPkGteZonaNal());

        assertNotNull(datosSaeta.getPkGteZonaProv());

        assertNotNull(datosSaeta.getPkFuente());

        assertNotNull(datosSaeta.getPkGrupo());

        assertNotNull(datosSaeta.getPkSubgrupo());

    }

    // property datosCons
    @Test
    void testGetterSetterDatosCons() {

        assertNotNull(datosSaeta.getDatosCons());

        int vint = 1234567;
        datosSaeta.setDatosCons(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getDatosCons()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setDatosCons(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getDatosCons()));
    }

    // property pkRfc
    @Test
    void testGetterSetterPkRfc() {

        assertNotNull(datosSaeta.getPkRfc());
    }

    // property pkClaveNal
    @Test
    void testGetterSetterPkClaveNal() {

        assertNotNull(datosSaeta.getPkClaveNal());

        int vint = 1234567;
        datosSaeta.setPkClaveNal(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkClaveNal()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setPkClaveNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkClaveNal()));
    }

    // property pkClaveProv
    @Test
    void testGetterSetterPkClaveProv() {

        assertNotNull(datosSaeta.getPkClaveProv());

        int vint = 1234567;
        datosSaeta.setPkClaveProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkClaveProv()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setPkClaveProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkClaveProv()));
    }

    // property pkNombre
    @Test
    void testGetterSetterPkNombre() {

        assertNotNull(datosSaeta.getPkNombre());

        String vString = "ABCabc1230";
        datosSaeta.setPkNombre(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkNombre()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNombre(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkNombre()));
    }

    // property pkCompania
    @Test
    void testGetterSetterPkCompania() {

        assertNotNull(datosSaeta.getPkCompania());

        String vString = "A";
        datosSaeta.setPkCompania(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkCompania()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkCompania(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkCompania()));
    }

    // property pkFecConexion
    @Test
    void testGetterSetterPkFecConexion() {

        assertNotNull(datosSaeta.getPkFecConexion());

        int vint = 123456;
        datosSaeta.setPkFecConexion(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecConexion()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecConexion(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecConexion()));
    }

    // property pkFecConexionr
    @Test
    void testGetterSetterPkFecConexionr() {

        assertNotNull(datosSaeta.getPkFecConexionr());

        String vString = "ABCabc";
        datosSaeta.setPkFecConexionr(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkFecConexionr()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        datosSaeta.setPkFecConexionr(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkFecConexionr()));
    }

    // property pkCalleP
    @Test
    void testGetterSetterPkCalleP() {

        assertNotNull(datosSaeta.getPkCalleP());

        String vString = "ABCabc1230";
        datosSaeta.setPkCalleP(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkCalleP()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkCalleP(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkCalleP()));
    }

    // property pkColoniaP
    @Test
    void testGetterSetterPkColoniaP() {

        assertNotNull(datosSaeta.getPkColoniaP());

        String vString = "ABCabc1230";
        datosSaeta.setPkColoniaP(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkColoniaP()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkColoniaP(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkColoniaP()));
    }

    // property pkCodPostP
    @Test
    void testGetterSetterPkCodPostP() {

        assertNotNull(datosSaeta.getPkCodPostP());

        int vint = 12345;
        datosSaeta.setPkCodPostP(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCodPostP()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        datosSaeta.setPkCodPostP(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCodPostP()));
    }

    // property pkPoblP
    @Test
    void testGetterSetterPkPoblP() {

        assertNotNull(datosSaeta.getPkPoblP());

        String vString = "ABCabc1230";
        datosSaeta.setPkPoblP(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkPoblP()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkPoblP(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkPoblP()));
    }

    // property pkEstadoP
    @Test
    void testGetterSetterPkEstadoP() {

        assertNotNull(datosSaeta.getPkEstadoP());

        int vint = 12;
        datosSaeta.setPkEstadoP(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkEstadoP()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkEstadoP(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkEstadoP()));
    }

    // property pkPaisP
    @Test
    void testGetterSetterPkPaisP() {

        assertNotNull(datosSaeta.getPkPaisP());

        int vint = 12;
        datosSaeta.setPkPaisP(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkPaisP()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkPaisP(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkPaisP()));
    }

    // property pkTelefP
    @Test
    void testGetterSetterPkTelefP() {

        assertNotNull(datosSaeta.getPkTelefP());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        datosSaeta.setPkTelefP(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, datosSaeta.getPkTelefP().toBigDecimal());

        int vint = 123456789;
        datosSaeta.setPkTelefP(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkTelefP()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        datosSaeta.setPkTelefP(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkTelefP()));
    }

    // property pkTelefPr
    @Test
    void testGetterSetterPkTelefPr() {

        assertNotNull(datosSaeta.getPkTelefPr());

        String vString = "ABCabc1230";
        datosSaeta.setPkTelefPr(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkTelefPr()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkTelefPr(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkTelefPr()));
    }

    // property pkCalleC
    @Test
    void testGetterSetterPkCalleC() {

        assertNotNull(datosSaeta.getPkCalleC());

        String vString = "ABCabc1230";
        datosSaeta.setPkCalleC(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkCalleC()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkCalleC(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkCalleC()));
    }

    // property pkColoniaC
    @Test
    void testGetterSetterPkColoniaC() {

        assertNotNull(datosSaeta.getPkColoniaC());

        String vString = "ABCabc1230";
        datosSaeta.setPkColoniaC(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkColoniaC()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkColoniaC(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkColoniaC()));
    }

    // property pkCodPostC
    @Test
    void testGetterSetterPkCodPostC() {

        assertNotNull(datosSaeta.getPkCodPostC());

        int vint = 12345;
        datosSaeta.setPkCodPostC(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCodPostC()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        datosSaeta.setPkCodPostC(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCodPostC()));
    }

    // property pkPoblC
    @Test
    void testGetterSetterPkPoblC() {

        assertNotNull(datosSaeta.getPkPoblC());

        String vString = "ABCabc1230";
        datosSaeta.setPkPoblC(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkPoblC()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkPoblC(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkPoblC()));
    }

    // property pkEstadoC
    @Test
    void testGetterSetterPkEstadoC() {

        assertNotNull(datosSaeta.getPkEstadoC());

        int vint = 12;
        datosSaeta.setPkEstadoC(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkEstadoC()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkEstadoC(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkEstadoC()));
    }

    // property pkPaisC
    @Test
    void testGetterSetterPkPaisC() {

        assertNotNull(datosSaeta.getPkPaisC());

        int vint = 12;
        datosSaeta.setPkPaisC(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkPaisC()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkPaisC(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkPaisC()));
    }

    // property pkTelefC
    @Test
    void testGetterSetterPkTelefC() {

        assertNotNull(datosSaeta.getPkTelefC());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        datosSaeta.setPkTelefC(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, datosSaeta.getPkTelefC().toBigDecimal());

        int vint = 123456789;
        datosSaeta.setPkTelefC(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkTelefC()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        datosSaeta.setPkTelefC(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkTelefC()));
    }

    // property pkTelefCr
    @Test
    void testGetterSetterPkTelefCr() {

        assertNotNull(datosSaeta.getPkTelefCr());

        String vString = "ABCabc1230";
        datosSaeta.setPkTelefCr(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkTelefCr()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkTelefCr(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkTelefCr()));
    }

    // property pkFecNacim
    @Test
    void testGetterSetterPkFecNacim() {

        assertNotNull(datosSaeta.getPkFecNacim());

        int vint = 123456;
        datosSaeta.setPkFecNacim(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecNacim()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecNacim(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecNacim()));
    }

    // property pkNacionalidad
    @Test
    void testGetterSetterPkNacionalidad() {

        assertNotNull(datosSaeta.getPkNacionalidad());

        int vint = 12;
        datosSaeta.setPkNacionalidad(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkNacionalidad()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkNacionalidad(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkNacionalidad()));
    }

    // property pkSexo
    @Test
    void testGetterSetterPkSexo() {

        assertNotNull(datosSaeta.getPkSexo());

        String vString = "A";
        datosSaeta.setPkSexo(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkSexo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkSexo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkSexo()));
    }

    // property pkEdoCivil
    @Test
    void testGetterSetterPkEdoCivil() {

        assertNotNull(datosSaeta.getPkEdoCivil());

        String vString = "A";
        datosSaeta.setPkEdoCivil(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkEdoCivil()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkEdoCivil(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkEdoCivil()));
    }

    // property pkEscolaridad
    @Test
    void testGetterSetterPkEscolaridad() {

        assertNotNull(datosSaeta.getPkEscolaridad());

        int vint = 1;
        datosSaeta.setPkEscolaridad(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkEscolaridad()));

        NumericVar vINumericValue = new NumericVar(1, 0);;
        vINumericValue.setValue("1");
        datosSaeta.setPkEscolaridad(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkEscolaridad()));
    }

    // property pkNomConyuge
    @Test
    void testGetterSetterPkNomConyuge() {

        assertNotNull(datosSaeta.getPkNomConyuge());

        String vString = "ABCabc1230";
        datosSaeta.setPkNomConyuge(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkNomConyuge()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNomConyuge(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkNomConyuge()));
    }

    // property pkFecCasam
    @Test
    void testGetterSetterPkFecCasam() {

        assertNotNull(datosSaeta.getPkFecCasam());

        int vint = 123456;
        datosSaeta.setPkFecCasam(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecCasam()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecCasam(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecCasam()));
    }

    // property pkStatus
    @Test
    void testGetterSetterPkStatus() {

        assertNotNull(datosSaeta.getPkStatus());

        String vString = "A";
        datosSaeta.setPkStatus(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkStatus()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkStatus(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkStatus()));
    }

    // property pkMotStatus
    @Test
    void testGetterSetterPkMotStatus() {

        assertNotNull(datosSaeta.getPkMotStatus());

        int vint = 12;
        datosSaeta.setPkMotStatus(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkMotStatus()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkMotStatus(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkMotStatus()));
    }

    // property pkFecStatus
    @Test
    void testGetterSetterPkFecStatus() {

        assertNotNull(datosSaeta.getPkFecStatus());

        int vint = 123456;
        datosSaeta.setPkFecStatus(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecStatus()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecStatus(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecStatus()));
    }

    // property pkFecAlta
    @Test
    void testGetterSetterPkFecAlta() {

        assertNotNull(datosSaeta.getPkFecAlta());

        int vint = 123456;
        datosSaeta.setPkFecAlta(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecAlta()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecAlta(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecAlta()));
    }

    // property pkFecTerm
    @Test
    void testGetterSetterPkFecTerm() {

        assertNotNull(datosSaeta.getPkFecTerm());

        int vint = 123456;
        datosSaeta.setPkFecTerm(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecTerm()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecTerm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecTerm()));
    }

    // property pkTipCedula
    @Test
    void testGetterSetterPkTipCedula() {

        assertNotNull(datosSaeta.getPkTipCedula());

        String vString = "A";
        datosSaeta.setPkTipCedula(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkTipCedula()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkTipCedula(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkTipCedula()));
    }

    // property pkNumCedula
    @Test
    void testGetterSetterPkNumCedula() {

        assertNotNull(datosSaeta.getPkNumCedula());

        String vString = "ABCabc1230";
        datosSaeta.setPkNumCedula(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkNumCedula()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNumCedula(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkNumCedula()));
    }

    // property pkNumCedular
    @Test
    void testGetterSetterPkNumCedular() {

        assertNotNull(datosSaeta.getPkNumCedular());
    }

    // property pkNumCedularr
    @Test
    void testGetterSetterPkNumCedularr() {

        assertNotNull(datosSaeta.getPkNumCedularr());
    }

    // property pkNumCedularrr
    @Test
    void testGetterSetterPkNumCedularrr() {

        assertNotNull(datosSaeta.getPkNumCedularrr());
    }

    // property pkIniCedula
    @Test
    void testGetterSetterPkIniCedula() {

        assertNotNull(datosSaeta.getPkIniCedula());

        int vint = 123456;
        datosSaeta.setPkIniCedula(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkIniCedula()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkIniCedula(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkIniCedula()));
    }

    // property pkFinCedula
    @Test
    void testGetterSetterPkFinCedula() {

        assertNotNull(datosSaeta.getPkFinCedula());

        int vint = 123456;
        datosSaeta.setPkFinCedula(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFinCedula()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFinCedula(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFinCedula()));
    }

    // property pkRamoP_1
    @Test
    void testGetterSetterPkRamoP_1() {

        assertNotNull(datosSaeta.getPkRamoP_1());

        String vString = "AB";
        datosSaeta.setPkRamoP_1(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_1()));
    }

    // property pkRamoP_2
    @Test
    void testGetterSetterPkRamoP_2() {

        assertNotNull(datosSaeta.getPkRamoP_2());

        String vString = "AB";
        datosSaeta.setPkRamoP_2(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_2()));
    }

    // property pkRamoP_3
    @Test
    void testGetterSetterPkRamoP_3() {

        assertNotNull(datosSaeta.getPkRamoP_3());

        String vString = "AB";
        datosSaeta.setPkRamoP_3(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_3()));
    }

    // property pkRamoP_4
    @Test
    void testGetterSetterPkRamoP_4() {

        assertNotNull(datosSaeta.getPkRamoP_4());

        String vString = "AB";
        datosSaeta.setPkRamoP_4(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_4()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_4(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_4()));
    }

    // property pkRamoP_5
    @Test
    void testGetterSetterPkRamoP_5() {

        assertNotNull(datosSaeta.getPkRamoP_5());

        String vString = "AB";
        datosSaeta.setPkRamoP_5(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_5()));
    }

    // property pkRamoP_6
    @Test
    void testGetterSetterPkRamoP_6() {

        assertNotNull(datosSaeta.getPkRamoP_6());

        String vString = "AB";
        datosSaeta.setPkRamoP_6(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_6()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_6(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_6()));
    }

    // property pkRamoP_7
    @Test
    void testGetterSetterPkRamoP_7() {

        assertNotNull(datosSaeta.getPkRamoP_7());

        String vString = "AB";
        datosSaeta.setPkRamoP_7(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_7()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_7(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_7()));
    }

    // property pkRamoP_8
    @Test
    void testGetterSetterPkRamoP_8() {

        assertNotNull(datosSaeta.getPkRamoP_8());

        String vString = "AB";
        datosSaeta.setPkRamoP_8(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_8()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_8(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_8()));
    }

    // property pkRamoP_9
    @Test
    void testGetterSetterPkRamoP_9() {

        assertNotNull(datosSaeta.getPkRamoP_9());

        String vString = "AB";
        datosSaeta.setPkRamoP_9(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_9()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_9(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_9()));
    }

    // property pkRamoP_10
    @Test
    void testGetterSetterPkRamoP_10() {

        assertNotNull(datosSaeta.getPkRamoP_10());

        String vString = "AB";
        datosSaeta.setPkRamoP_10(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRamoP_10()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRamoP_10(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRamoP_10()));
    }

    // property pkObservOficS
    @Test
    void testGetterSetterPkObservOficS() {

        assertNotNull(datosSaeta.getPkObservOficS());

        String vString = "ABCabc1230";
        datosSaeta.setPkObservOficS(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkObservOficS()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkObservOficS(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkObservOficS()));
    }

    // property pkRegIva
    @Test
    void testGetterSetterPkRegIva() {

        assertNotNull(datosSaeta.getPkRegIva());

        String vString = "ABCabc1230";
        datosSaeta.setPkRegIva(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkRegIva()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkRegIva(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkRegIva()));
    }

    // property pkTipo
    @Test
    void testGetterSetterPkTipo() {

        assertNotNull(datosSaeta.getPkTipo());

        int vint = 12;
        datosSaeta.setPkTipo(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkTipo()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkTipo(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkTipo()));
    }

    // property pkCodPago
    @Test
    void testGetterSetterPkCodPago() {

        assertNotNull(datosSaeta.getPkCodPago());

        int vint = 12;
        datosSaeta.setPkCodPago(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCodPago()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkCodPago(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCodPago()));
    }

    // property pkCvePagoImp
    @Test
    void testGetterSetterPkCvePagoImp() {

        assertNotNull(datosSaeta.getPkCvePagoImp());

        int vint = 12;
        datosSaeta.setPkCvePagoImp(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCvePagoImp()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkCvePagoImp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCvePagoImp()));
    }

    // property pkPorIva
    @Test
    void testGetterSetterPkPorIva() {

        assertNotNull(datosSaeta.getPkPorIva());

        BigDecimal vBigDecimal = new BigDecimal("12.12");
        datosSaeta.setPkPorIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, datosSaeta.getPkPorIva().toBigDecimal());

        int vint = 12;
        datosSaeta.setPkPorIva(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkPorIva()));

        NumericVar vINumericValue = new NumericVar(4, 2);;
        vINumericValue.setValue("12.12");
        datosSaeta.setPkPorIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkPorIva()));
    }

    // property pkPorIsr
    @Test
    void testGetterSetterPkPorIsr() {

        assertNotNull(datosSaeta.getPkPorIsr());

        BigDecimal vBigDecimal = new BigDecimal("12.12");
        datosSaeta.setPkPorIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, datosSaeta.getPkPorIsr().toBigDecimal());

        int vint = 12;
        datosSaeta.setPkPorIsr(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkPorIsr()));

        NumericVar vINumericValue = new NumericVar(4, 2);;
        vINumericValue.setValue("12.12");
        datosSaeta.setPkPorIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkPorIsr()));
    }

    // property pkNumFianza
    @Test
    void testGetterSetterPkNumFianza() {

        assertNotNull(datosSaeta.getPkNumFianza());

        String vString = "ABCabc1230";
        datosSaeta.setPkNumFianza(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkNumFianza()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNumFianza(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkNumFianza()));
    }

    // property pkCanFianza
    @Test
    void testGetterSetterPkCanFianza() {

        assertNotNull(datosSaeta.getPkCanFianza());

        BigDecimal vBigDecimal = new BigDecimal("1234567.12");
        datosSaeta.setPkCanFianza(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, datosSaeta.getPkCanFianza().toBigDecimal());

        int vint = 1234567;
        datosSaeta.setPkCanFianza(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCanFianza()));

        NumericVar vINumericValue = new NumericVar(9, 2);;
        vINumericValue.setValue("1234567.12");
        datosSaeta.setPkCanFianza(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCanFianza()));
    }

    // property pkIniFianza
    @Test
    void testGetterSetterPkIniFianza() {

        assertNotNull(datosSaeta.getPkIniFianza());

        int vint = 123456;
        datosSaeta.setPkIniFianza(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkIniFianza()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkIniFianza(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkIniFianza()));
    }

    // property pkFinFianza
    @Test
    void testGetterSetterPkFinFianza() {

        assertNotNull(datosSaeta.getPkFinFianza());

        int vint = 123456;
        datosSaeta.setPkFinFianza(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFinFianza()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFinFianza(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFinFianza()));
    }

    // property pkAgenteRec
    @Test
    void testGetterSetterPkAgenteRec() {

        assertNotNull(datosSaeta.getPkAgenteRec());

        int vint = 1234567;
        datosSaeta.setPkAgenteRec(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkAgenteRec()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setPkAgenteRec(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkAgenteRec()));
    }

    // property pkOtrasCias
    @Test
    void testGetterSetterPkOtrasCias() {

        assertNotNull(datosSaeta.getPkOtrasCias());

        String vString = "A";
        datosSaeta.setPkOtrasCias(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkOtrasCias()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkOtrasCias(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkOtrasCias()));
    }

    // property pkEsGerente
    @Test
    void testGetterSetterPkEsGerente() {

        assertNotNull(datosSaeta.getPkEsGerente());

        String vString = "A";
        datosSaeta.setPkEsGerente(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkEsGerente()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkEsGerente(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkEsGerente()));
    }

    // property pkZonaNal
    @Test
    void testGetterSetterPkZonaNal() {

        assertNotNull(datosSaeta.getPkZonaNal());

        int vint = 1234;
        datosSaeta.setPkZonaNal(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkZonaNal()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        datosSaeta.setPkZonaNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkZonaNal()));
    }

    // property pkZonaProv
    @Test
    void testGetterSetterPkZonaProv() {

        assertNotNull(datosSaeta.getPkZonaProv());

        int vint = 1234;
        datosSaeta.setPkZonaProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkZonaProv()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        datosSaeta.setPkZonaProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkZonaProv()));
    }

    // property pkZonaNalGte
    @Test
    void testGetterSetterPkZonaNalGte() {

        assertNotNull(datosSaeta.getPkZonaNalGte());

        int vint = 1234;
        datosSaeta.setPkZonaNalGte(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkZonaNalGte()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        datosSaeta.setPkZonaNalGte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkZonaNalGte()));
    }

    // property pkZonaProvGte
    @Test
    void testGetterSetterPkZonaProvGte() {

        assertNotNull(datosSaeta.getPkZonaProvGte());

        int vint = 1234;
        datosSaeta.setPkZonaProvGte(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkZonaProvGte()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        datosSaeta.setPkZonaProvGte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkZonaProvGte()));
    }

    // property pkOficinaNal
    @Test
    void testGetterSetterPkOficinaNal() {

        assertNotNull(datosSaeta.getPkOficinaNal());

        int vint = 123;
        datosSaeta.setPkOficinaNal(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkOficinaNal()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        datosSaeta.setPkOficinaNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkOficinaNal()));
    }

    // property pkOficinaProv
    @Test
    void testGetterSetterPkOficinaProv() {

        assertNotNull(datosSaeta.getPkOficinaProv());

        int vint = 123;
        datosSaeta.setPkOficinaProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkOficinaProv()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        datosSaeta.setPkOficinaProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkOficinaProv()));
    }

    // property pkFecZonaNal
    @Test
    void testGetterSetterPkFecZonaNal() {

        assertNotNull(datosSaeta.getPkFecZonaNal());

        int vint = 123456;
        datosSaeta.setPkFecZonaNal(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecZonaNal()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecZonaNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecZonaNal()));
    }

    // property pkFecZonaProv
    @Test
    void testGetterSetterPkFecZonaProv() {

        assertNotNull(datosSaeta.getPkFecZonaProv());

        int vint = 123456;
        datosSaeta.setPkFecZonaProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecZonaProv()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecZonaProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecZonaProv()));
    }

    // property pkFecZonaNalGte
    @Test
    void testGetterSetterPkFecZonaNalGte() {

        assertNotNull(datosSaeta.getPkFecZonaNalGte());

        int vint = 123456;
        datosSaeta.setPkFecZonaNalGte(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecZonaNalGte()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecZonaNalGte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecZonaNalGte()));
    }

    // property pkFecZonaProvGte
    @Test
    void testGetterSetterPkFecZonaProvGte() {

        assertNotNull(datosSaeta.getPkFecZonaProvGte());

        int vint = 123456;
        datosSaeta.setPkFecZonaProvGte(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecZonaProvGte()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecZonaProvGte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecZonaProvGte()));
    }

    // property pkFecOficinaNal
    @Test
    void testGetterSetterPkFecOficinaNal() {

        assertNotNull(datosSaeta.getPkFecOficinaNal());

        int vint = 123456;
        datosSaeta.setPkFecOficinaNal(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecOficinaNal()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecOficinaNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecOficinaNal()));
    }

    // property pkFecOficinaProv
    @Test
    void testGetterSetterPkFecOficinaProv() {

        assertNotNull(datosSaeta.getPkFecOficinaProv());

        int vint = 123456;
        datosSaeta.setPkFecOficinaProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecOficinaProv()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecOficinaProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecOficinaProv()));
    }

    // property pkCveAgrup
    @Test
    void testGetterSetterPkCveAgrup() {

        assertNotNull(datosSaeta.getPkCveAgrup());

        int vint = 1;
        datosSaeta.setPkCveAgrup(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCveAgrup()));

        NumericVar vINumericValue = new NumericVar(1, 0);;
        vINumericValue.setValue("1");
        datosSaeta.setPkCveAgrup(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCveAgrup()));
    }

    // property pkCveEnvio
    @Test
    void testGetterSetterPkCveEnvio() {

        assertNotNull(datosSaeta.getPkCveEnvio());

        int vint = 12;
        datosSaeta.setPkCveEnvio(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCveEnvio()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkCveEnvio(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCveEnvio()));
    }

    // property pkCalidad
    @Test
    void testGetterSetterPkCalidad() {

        assertNotNull(datosSaeta.getPkCalidad());

        int vint = 12;
        datosSaeta.setPkCalidad(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkCalidad()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkCalidad(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkCalidad()));
    }

    // property pkJefeGpoNal
    @Test
    void testGetterSetterPkJefeGpoNal() {

        assertNotNull(datosSaeta.getPkJefeGpoNal());

        int vint = 1234567;
        datosSaeta.setPkJefeGpoNal(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkJefeGpoNal()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setPkJefeGpoNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkJefeGpoNal()));
    }

    // property pkJefeGpoProv
    @Test
    void testGetterSetterPkJefeGpoProv() {

        assertNotNull(datosSaeta.getPkJefeGpoProv());

        int vint = 1234567;
        datosSaeta.setPkJefeGpoProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkJefeGpoProv()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setPkJefeGpoProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkJefeGpoProv()));
    }

    // property pkFecConexionP
    @Test
    void testGetterSetterPkFecConexionP() {

        assertNotNull(datosSaeta.getPkFecConexionP());

        int vint = 123456;
        datosSaeta.setPkFecConexionP(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFecConexionP()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFecConexionP(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFecConexionP()));
    }

    // property pkFecConexionPr
    @Test
    void testGetterSetterPkFecConexionPr() {

        assertNotNull(datosSaeta.getPkFecConexionPr());

        String vString = "ABCabc";
        datosSaeta.setPkFecConexionPr(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkFecConexionPr()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        datosSaeta.setPkFecConexionPr(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkFecConexionPr()));
    }

    // property pkNumCedulaP
    @Test
    void testGetterSetterPkNumCedulaP() {

        assertNotNull(datosSaeta.getPkNumCedulaP());

        String vString = "ABCabc1230";
        datosSaeta.setPkNumCedulaP(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkNumCedulaP()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNumCedulaP(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkNumCedulaP()));
    }

    // property pkNumCedulaPr
    @Test
    void testGetterSetterPkNumCedulaPr() {

        assertNotNull(datosSaeta.getPkNumCedulaPr());
    }

    // property pkNumCedulapRr
    @Test
    void testGetterSetterPkNumCedulapRr() {

        assertNotNull(datosSaeta.getPkNumCedulapRr());
    }

    // property pkFinCedulaP
    @Test
    void testGetterSetterPkFinCedulaP() {

        assertNotNull(datosSaeta.getPkFinCedulaP());

        int vint = 123456;
        datosSaeta.setPkFinCedulaP(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFinCedulaP()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosSaeta.setPkFinCedulaP(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFinCedulaP()));
    }

    // property pkStatusProv
    @Test
    void testGetterSetterPkStatusProv() {

        assertNotNull(datosSaeta.getPkStatusProv());

        String vString = "A";
        datosSaeta.setPkStatusProv(vString); // String
        assertEquals(asStr(vString), asStr(datosSaeta.getPkStatusProv()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkStatusProv(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosSaeta.getPkStatusProv()));
    }

    // property pkMotStatusProv
    @Test
    void testGetterSetterPkMotStatusProv() {

        assertNotNull(datosSaeta.getPkMotStatusProv());

        int vint = 12;
        datosSaeta.setPkMotStatusProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkMotStatusProv()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkMotStatusProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkMotStatusProv()));
    }

    // property pkGteZonaNal
    @Test
    void testGetterSetterPkGteZonaNal() {

        assertNotNull(datosSaeta.getPkGteZonaNal());

        int vint = 1234567;
        datosSaeta.setPkGteZonaNal(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkGteZonaNal()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setPkGteZonaNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkGteZonaNal()));
    }

    // property pkGteZonaProv
    @Test
    void testGetterSetterPkGteZonaProv() {

        assertNotNull(datosSaeta.getPkGteZonaProv());

        int vint = 1234567;
        datosSaeta.setPkGteZonaProv(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkGteZonaProv()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosSaeta.setPkGteZonaProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkGteZonaProv()));
    }

    // property pkFuente
    @Test
    void testGetterSetterPkFuente() {

        assertNotNull(datosSaeta.getPkFuente());

        int vint = 12;
        datosSaeta.setPkFuente(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkFuente()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkFuente(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkFuente()));
    }

    // property pkGrupo
    @Test
    void testGetterSetterPkGrupo() {

        assertNotNull(datosSaeta.getPkGrupo());

        int vint = 12;
        datosSaeta.setPkGrupo(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkGrupo()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosSaeta.setPkGrupo(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkGrupo()));
    }

    // property pkSubgrupo
    @Test
    void testGetterSetterPkSubgrupo() {

        assertNotNull(datosSaeta.getPkSubgrupo());

        int vint = 123;
        datosSaeta.setPkSubgrupo(vint); // int
        assertEquals(asInt(vint), asInt(datosSaeta.getPkSubgrupo()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        datosSaeta.setPkSubgrupo(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosSaeta.getPkSubgrupo()));
    }

    // property pkRfc1
    @Test
    void testGetterSetterPkRfc1() {

        // Property: pkRfc.pkRfc1 -> 07 PK-RFC1 X(13)
        assertNotNull(datosSaeta.getPkRfc1());
        String vString = "ABCabc1230";
        datosSaeta.setPkRfc1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkRfc1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRfc2
    @Test
    void testGetterSetterPkRfc2() {

        // Property: pkRfc.pkRfc2 -> 07 PK-RFC2 XX
        assertNotNull(datosSaeta.getPkRfc2());
        String vString = "AB";
        datosSaeta.setPkRfc2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkRfc2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaDos
    @Test
    void testGetterSetterPkNumCedulaDos() {

        // Property: pkNumCedular.pkNumCedulaDos -> 07 PK-NUM-CEDULA-DOS XX
        assertNotNull(datosSaeta.getPkNumCedulaDos());
        String vString = "AB";
        datosSaeta.setPkNumCedulaDos(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkNumCedulaDos(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaR18
    @Test
    void testGetterSetterPkNumCedulaR18() {

        // Property: pkNumCedular.pkNumCedulaR18 -> 07 PK-NUM-CEDULA-R18 X(18)
        assertNotNull(datosSaeta.getPkNumCedulaR18());
        String vString = "ABCabc1230";
        datosSaeta.setPkNumCedulaR18(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNumCedulaR18(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaTres
    @Test
    void testGetterSetterPkNumCedulaTres() {

        // Property: pkNumCedularr.pkNumCedulaTres -> 07 PK-NUM-CEDULA-TRES XXX
        assertNotNull(datosSaeta.getPkNumCedulaTres());
        String vString = "ABC";
        datosSaeta.setPkNumCedulaTres(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        datosSaeta.setPkNumCedulaTres(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaXx
    @Test
    void testGetterSetterPkNumCedulaXx() {

        // Property: pkNumCedularrr.pkNumCedulaXx -> 07 PK-NUM-CEDULA-XX XX
        assertNotNull(datosSaeta.getPkNumCedulaXx());
        String vString = "AB";
        datosSaeta.setPkNumCedulaXx(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkNumCedulaXx(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaX
    @Test
    void testGetterSetterPkNumCedulaX() {

        // Property: pkNumCedularrr.pkNumCedulaX -> 07 PK-NUM-CEDULA-X X
        assertNotNull(datosSaeta.getPkNumCedulaX());
        String vString = "A";
        datosSaeta.setPkNumCedulaX(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosSaeta.setPkNumCedulaX(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaPDos
    @Test
    void testGetterSetterPkNumCedulaPDos() {

        // Property: pkNumCedulaPr.pkNumCedulaPDos -> 07 PK-NUM-CEDULA-P-DOS XX
        assertNotNull(datosSaeta.getPkNumCedulaPDos());
        String vString = "AB";
        datosSaeta.setPkNumCedulaPDos(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        datosSaeta.setPkNumCedulaPDos(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaPR18
    @Test
    void testGetterSetterPkNumCedulaPR18() {

        // Property: pkNumCedulaPr.pkNumCedulaPR18 -> 07 PK-NUM-CEDULA-P-R18 X(18)
        assertNotNull(datosSaeta.getPkNumCedulaPR18());
        String vString = "ABCabc1230";
        datosSaeta.setPkNumCedulaPR18(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNumCedulaPR18(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaPTres
    @Test
    void testGetterSetterPkNumCedulaPTres() {

        // Property: pkNumCedulapRr.pkNumCedulaPTres -> 07 PK-NUM-CEDULA-P-TRES XXX
        assertNotNull(datosSaeta.getPkNumCedulaPTres());
        String vString = "ABC";
        datosSaeta.setPkNumCedulaPTres(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        datosSaeta.setPkNumCedulaPTres(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaPR17
    @Test
    void testGetterSetterPkNumCedulaPR17() {

        // Property: pkNumCedulapRr.pkNumCedulaPR17 -> 07 PK-NUM-CEDULA-P-R17 X(17)
        assertNotNull(datosSaeta.getPkNumCedulaPR17());
        String vString = "ABCabc1230";
        datosSaeta.setPkNumCedulaPR17(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosSaeta.setPkNumCedulaPR17(vIAlphanumericValue); // IAlphanumericValue
    }
}

