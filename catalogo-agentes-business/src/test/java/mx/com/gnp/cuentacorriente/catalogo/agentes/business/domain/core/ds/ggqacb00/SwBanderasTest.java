package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: SW-BANDERAS.
 *
 */
class SwBanderasTest {

    private SwBanderas swBanderas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        swBanderas = new SwBanderas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        swBanderas.setFiller1("");
        assertEquals("", swBanderas.getFiller1());

        swBanderas.setDb2ReturnCode(0);
        assertEquals(0, swBanderas.getDb2ReturnCode());

    }

    // property filler1
    @Test
    void testGetterSetterFiller1() {

        assertNotNull(swBanderas.getFiller1());

        String vString = "A";
        swBanderas.setFiller1(vString); // String
        assertEquals(asStr(vString), asStr(swBanderas.getFiller1()));
        // Conditionals (88) - FILLER1
        swBanderas.setSwCna0Fin();
        assertTrue(swBanderas.isSwCna0Fin());

    }

    // property db2ReturnCode
    @Test
    void testGetterSetterDb2ReturnCode() {

        assertNotNull(swBanderas.getDb2ReturnCode());

        int vint = *********;
        swBanderas.setDb2ReturnCode(vint); // int
        assertEquals(asInt(vint), asInt(swBanderas.getDb2ReturnCode()));
        // Conditionals (88) - DB2-RETURN-CODE
        swBanderas.setDb2Ok();
        assertTrue(swBanderas.isDb2Ok());
        swBanderas.setDb2Notfnd();
        assertTrue(swBanderas.isDb2Notfnd());

    }
}

