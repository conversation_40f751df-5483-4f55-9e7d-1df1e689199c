package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-PK-NOMBRE.
 *
 */
class WsPkNombreTest {

    private WsPkNombre wsPkNombre;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsPkNombre = new WsPkNombre();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsPkNombre.setWsPkApePat("");
        assertEquals("", wsPkNombre.getWsPkApePat());

        wsPkNombre.setWsPkApeMat("");
        assertEquals("", wsPkNombre.getWsPkApeMat());

        wsPkNombre.setWsPkNom("");
        assertEquals("", wsPkNombre.getWsPkNom());

    }

    // property wsPkApePat
    @Test
    void testGetterSetterWsPkApePat() {

        assertNotNull(wsPkNombre.getWsPkApePat());

        String vString = "ABCabc1230";
        wsPkNombre.setWsPkApePat(vString); // String
        assertEquals(asStr(vString), asStr(wsPkNombre.getWsPkApePat()));
    }

    // property wsPkApeMat
    @Test
    void testGetterSetterWsPkApeMat() {

        assertNotNull(wsPkNombre.getWsPkApeMat());

        String vString = "ABCabc1230";
        wsPkNombre.setWsPkApeMat(vString); // String
        assertEquals(asStr(vString), asStr(wsPkNombre.getWsPkApeMat()));
    }

    // property wsPkNom
    @Test
    void testGetterSetterWsPkNom() {

        assertNotNull(wsPkNombre.getWsPkNom());

        String vString = "ABCabc1230";
        wsPkNombre.setWsPkNom(vString); // String
        assertEquals(asStr(vString), asStr(wsPkNombre.getWsPkNom()));
    }
}

