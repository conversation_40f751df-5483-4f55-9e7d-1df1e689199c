package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: C-CONTADORES.
 *
 */
class CContadoresTest {

    private CContadores cContadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        cContadores = new CContadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertTrue(cContadores.getCFetchDepura().isZero());

        assertTrue(cContadores.getCWriteDepura().isZero());

        assertTrue(cContadores.getCDeleteGfvtbrt0().isZero());

    }

    // property cFetchDepura
    @Test
    void testGetterSetterCFetchDepura() {

        assertNotNull(cContadores.getCFetchDepura());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        cContadores.setCFetchDepura(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, cContadores.getCFetchDepura().toBigDecimal());

        int vint = 123456789;
        cContadores.setCFetchDepura(vint); // int
        assertEquals(asInt(vint), asInt(cContadores.getCFetchDepura()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        cContadores.setCFetchDepura(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(cContadores.getCFetchDepura()));
    }

    // property cWriteDepura
    @Test
    void testGetterSetterCWriteDepura() {

        assertNotNull(cContadores.getCWriteDepura());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        cContadores.setCWriteDepura(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, cContadores.getCWriteDepura().toBigDecimal());

        int vint = 123456789;
        cContadores.setCWriteDepura(vint); // int
        assertEquals(asInt(vint), asInt(cContadores.getCWriteDepura()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        cContadores.setCWriteDepura(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(cContadores.getCWriteDepura()));
    }

    // property cDeleteGfvtbrt0
    @Test
    void testGetterSetterCDeleteGfvtbrt0() {

        assertNotNull(cContadores.getCDeleteGfvtbrt0());

        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        cContadores.setCDeleteGfvtbrt0(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, cContadores.getCDeleteGfvtbrt0().toBigDecimal());

        int vint = 123456789;
        cContadores.setCDeleteGfvtbrt0(vint); // int
        assertEquals(asInt(vint), asInt(cContadores.getCDeleteGfvtbrt0()));

        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        cContadores.setCDeleteGfvtbrt0(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(cContadores.getCDeleteGfvtbrt0()));
    }
}

