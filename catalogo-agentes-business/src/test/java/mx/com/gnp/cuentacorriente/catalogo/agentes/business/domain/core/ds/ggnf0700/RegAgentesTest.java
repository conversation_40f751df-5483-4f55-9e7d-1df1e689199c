package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-AGENTES.
 *
 */
class RegAgentesTest {

    private RegAgentes regAgentes;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regAgentes = new RegAgentes();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegAgentes regAgentes2 = new RegAgentes(parent);
        assertNotNull(regAgentes2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 333);
        RegAgentes regAgentes3 = new RegAgentes(parent, sibling);
        assertNotNull(regAgentes3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regAgentes.getAgFlaNum());

    }

    // property agFlaNum
    @Test
    void testGetterSetterAgFlaNum() {

        assertNotNull(regAgentes.getAgFlaNum());

        String vString = "ABCabc12";
        regAgentes.setAgFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(regAgentes.getAgFlaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        regAgentes.setAgFlaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regAgentes.getAgFlaNum()));
    }
}

