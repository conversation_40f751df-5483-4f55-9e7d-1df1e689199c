package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WT-CNA-GER-ZON.
 *
 */
class WtCnaGerZonTest {

    private WtCnaGerZon wtCnaGerZon;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wtCnaGerZon = new WtCnaGerZon();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WtCnaGerZon wtCnaGerZon2 = new WtCnaGerZon(parent);
        assertNotNull(wtCnaGerZon2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 4);
        WtCnaGerZon wtCnaGerZon3 = new WtCnaGerZon(parent, sibling);
        assertNotNull(wtCnaGerZon3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wtCnaGerZon.getWtCnaGerZonA());

    }

    // property wtCnaGerZonA
    @Test
    void testGetterSetterWtCnaGerZonA() {

        assertNotNull(wtCnaGerZon.getWtCnaGerZonA());
        assertNotNull(wtCnaGerZon.getWtCnaGerZonA(1));

        String vString = "A";
        wtCnaGerZon.getWtCnaGerZonA().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wtCnaGerZon.getWtCnaGerZonA(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wtCnaGerZon.getWtCnaGerZonA().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wtCnaGerZon.getWtCnaGerZonA(1)));
    }
}

