package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TAB-NOMBRER.
 *
 */
class WsTabNombrerTest {

    private WsTabNombrer wsTabNombrer;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTabNombrer = new WsTabNombrer();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsTabNombrer wsTabNombrer2 = new WsTabNombrer(parent);
        assertNotNull(wsTabNombrer2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 40);
        WsTabNombrer wsTabNombrer3 = new WsTabNombrer(parent, sibling);
        assertNotNull(wsTabNombrer3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsTabNombrer.getEleNombre());

    }

    // property eleNombre
    @Test
    void testGetterSetterEleNombre() {

        assertNotNull(wsTabNombrer.getEleNombre());
        assertNotNull(wsTabNombrer.getEleNombre(1));

        String vString = "A";
        wsTabNombrer.getEleNombre().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsTabNombrer.getEleNombre(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsTabNombrer.getEleNombre().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsTabNombrer.getEleNombre(1)));
    }
}

