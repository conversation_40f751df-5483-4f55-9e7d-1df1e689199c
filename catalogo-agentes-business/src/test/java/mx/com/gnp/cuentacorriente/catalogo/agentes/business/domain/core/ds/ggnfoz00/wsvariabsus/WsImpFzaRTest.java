package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-IMP-FZA-R.
 *
 */
class WsImpFzaRTest {

    private WsImpFzaR wsImpFzaR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsImpFzaR = new WsImpFzaR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsImpFzaR wsImpFzaR2 = new WsImpFzaR(parent);
        assertNotNull(wsImpFzaR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 13);
        WsImpFzaR wsImpFzaR3 = new WsImpFzaR(parent, sibling);
        assertNotNull(wsImpFzaR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsImpFzaR.getRsImpFza11());

        assertNotNull(wsImpFzaR.getRsImpFza2());

    }

    // property rsImpFza11
    @Test
    void testGetterSetterRsImpFza11() {

        assertNotNull(wsImpFzaR.getRsImpFza11());

        String vString = "ABCabc1230";
        wsImpFzaR.setRsImpFza11(vString); // String
        assertEquals(asStr(vString), asStr(wsImpFzaR.getRsImpFza11()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsImpFzaR.setRsImpFza11(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsImpFzaR.getRsImpFza11()));
    }

    // property rsImpFza2
    @Test
    void testGetterSetterRsImpFza2() {

        assertNotNull(wsImpFzaR.getRsImpFza2());

        String vString = "AB";
        wsImpFzaR.setRsImpFza2(vString); // String
        assertEquals(asStr(vString), asStr(wsImpFzaR.getRsImpFza2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsImpFzaR.setRsImpFza2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsImpFzaR.getRsImpFza2()));
    }
}

