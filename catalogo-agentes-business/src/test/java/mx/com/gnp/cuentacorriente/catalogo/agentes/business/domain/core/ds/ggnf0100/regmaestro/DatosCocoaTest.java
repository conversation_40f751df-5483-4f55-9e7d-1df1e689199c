package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DATOS-COCOA.
 *
 */
class DatosCocoaTest {

    private DatosCocoa datosCocoa;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        datosCocoa = new DatosCocoa();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        DatosCocoa datosCocoa2 = new DatosCocoa(parent);
        assertNotNull(datosCocoa2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 140);
        DatosCocoa datosCocoa3 = new DatosCocoa(parent, sibling);
        assertNotNull(datosCocoa3);
    }

    @Test
    void testInitialize() {
        assertNotNull(datosCocoa.getPkExiste());

        assertNotNull(datosCocoa.getPkCveSist());

        assertNotNull(datosCocoa.getPkFecRehab());

        assertNotNull(datosCocoa.getPkQuienExime());

        assertNotNull(datosCocoa.getPkFolioInves());


        assertNotNull(datosCocoa.getPkAdicionales());

        assertNotNull(datosCocoa.getPkFotos());

        assertNotNull(datosCocoa.getPkActaNac());

        assertNotNull(datosCocoa.getPkCertificado());

        assertNotNull(datosCocoa.getPkCartaRecom());

        assertNotNull(datosCocoa.getPkAltaShcp());

        assertNotNull(datosCocoa.getPkHomoclave());

        assertNotNull(datosCocoa.getPkCartaBaja());

        assertNotNull(datosCocoa.getPkCedulaDef());

        assertNotNull(datosCocoa.getPkEdoCta());

        assertNotNull(datosCocoa.getPkFm2());

        assertNotNull(datosCocoa.getPkSolicitud());

        assertNotNull(datosCocoa.getPkFas2());

        assertNotNull(datosCocoa.getPkCartaArt10());

        assertNotNull(datosCocoa.getPkSeguroCol());

        assertNotNull(datosCocoa.getPkContrato());

        assertNotNull(datosCocoa.getPkChequePagoDer());

        assertNotNull(datosCocoa.getPkCopiaCedDef());

        assertNotNull(datosCocoa.getPkCursoPade());

        assertNotNull(datosCocoa.getPkInvestigacion());

        assertNotNull(datosCocoa.getPkFechaPlazo());

        assertNotNull(datosCocoa.getPkTipCedulaAux());

        assertNotNull(datosCocoa.getPkNivelNal());

        assertNotNull(datosCocoa.getPkNivelProv());

    }

    // property pkExiste
    @Test
    void testGetterSetterPkExiste() {

        assertNotNull(datosCocoa.getPkExiste());

        String vString = "A";
        datosCocoa.setPkExiste(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkExiste()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkExiste(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkExiste()));
    }

    // property pkCveSist
    @Test
    void testGetterSetterPkCveSist() {

        assertNotNull(datosCocoa.getPkCveSist());

        String vString = "A";
        datosCocoa.setPkCveSist(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCveSist()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCveSist(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCveSist()));
    }

    // property pkFecRehab
    @Test
    void testGetterSetterPkFecRehab() {

        assertNotNull(datosCocoa.getPkFecRehab());

        int vint = 123456;
        datosCocoa.setPkFecRehab(vint); // int
        assertEquals(asInt(vint), asInt(datosCocoa.getPkFecRehab()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosCocoa.setPkFecRehab(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosCocoa.getPkFecRehab()));
    }

    // property pkQuienExime
    @Test
    void testGetterSetterPkQuienExime() {

        assertNotNull(datosCocoa.getPkQuienExime());

        String vString = "ABCabc1230";
        datosCocoa.setPkQuienExime(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkQuienExime()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosCocoa.setPkQuienExime(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkQuienExime()));
    }

    // property pkFolioInves
    @Test
    void testGetterSetterPkFolioInves() {

        assertNotNull(datosCocoa.getPkFolioInves());

        int vint = 1234567;
        datosCocoa.setPkFolioInves(vint); // int
        assertEquals(asInt(vint), asInt(datosCocoa.getPkFolioInves()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        datosCocoa.setPkFolioInves(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosCocoa.getPkFolioInves()));
    }

    // property pkFolioInvesr
    @Test
    void testGetterSetterPkFolioInvesr() {

        assertNotNull(datosCocoa.getPkFolioInvesr());
    }

    // property pkAdicionales
    @Test
    void testGetterSetterPkAdicionales() {

        assertNotNull(datosCocoa.getPkAdicionales());

        String vString = "ABCabc1230";
        datosCocoa.setPkAdicionales(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkAdicionales()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        datosCocoa.setPkAdicionales(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkAdicionales()));
    }

    // property pkFotos
    @Test
    void testGetterSetterPkFotos() {

        assertNotNull(datosCocoa.getPkFotos());

        String vString = "A";
        datosCocoa.setPkFotos(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkFotos()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkFotos(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkFotos()));
    }

    // property pkActaNac
    @Test
    void testGetterSetterPkActaNac() {

        assertNotNull(datosCocoa.getPkActaNac());

        String vString = "A";
        datosCocoa.setPkActaNac(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkActaNac()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkActaNac(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkActaNac()));
    }

    // property pkCertificado
    @Test
    void testGetterSetterPkCertificado() {

        assertNotNull(datosCocoa.getPkCertificado());

        String vString = "A";
        datosCocoa.setPkCertificado(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCertificado()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCertificado(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCertificado()));
    }

    // property pkCartaRecom
    @Test
    void testGetterSetterPkCartaRecom() {

        assertNotNull(datosCocoa.getPkCartaRecom());

        String vString = "A";
        datosCocoa.setPkCartaRecom(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCartaRecom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCartaRecom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCartaRecom()));
    }

    // property pkAltaShcp
    @Test
    void testGetterSetterPkAltaShcp() {

        assertNotNull(datosCocoa.getPkAltaShcp());

        String vString = "A";
        datosCocoa.setPkAltaShcp(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkAltaShcp()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkAltaShcp(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkAltaShcp()));
    }

    // property pkHomoclave
    @Test
    void testGetterSetterPkHomoclave() {

        assertNotNull(datosCocoa.getPkHomoclave());

        String vString = "A";
        datosCocoa.setPkHomoclave(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkHomoclave()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkHomoclave(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkHomoclave()));
    }

    // property pkCartaBaja
    @Test
    void testGetterSetterPkCartaBaja() {

        assertNotNull(datosCocoa.getPkCartaBaja());

        String vString = "A";
        datosCocoa.setPkCartaBaja(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCartaBaja()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCartaBaja(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCartaBaja()));
    }

    // property pkCedulaDef
    @Test
    void testGetterSetterPkCedulaDef() {

        assertNotNull(datosCocoa.getPkCedulaDef());

        String vString = "A";
        datosCocoa.setPkCedulaDef(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCedulaDef()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCedulaDef(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCedulaDef()));
    }

    // property pkEdoCta
    @Test
    void testGetterSetterPkEdoCta() {

        assertNotNull(datosCocoa.getPkEdoCta());

        String vString = "A";
        datosCocoa.setPkEdoCta(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkEdoCta()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkEdoCta(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkEdoCta()));
    }

    // property pkFm2
    @Test
    void testGetterSetterPkFm2() {

        assertNotNull(datosCocoa.getPkFm2());

        String vString = "A";
        datosCocoa.setPkFm2(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkFm2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkFm2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkFm2()));
    }

    // property pkSolicitud
    @Test
    void testGetterSetterPkSolicitud() {

        assertNotNull(datosCocoa.getPkSolicitud());

        String vString = "A";
        datosCocoa.setPkSolicitud(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkSolicitud()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkSolicitud(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkSolicitud()));
    }

    // property pkFas2
    @Test
    void testGetterSetterPkFas2() {

        assertNotNull(datosCocoa.getPkFas2());

        String vString = "A";
        datosCocoa.setPkFas2(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkFas2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkFas2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkFas2()));
    }

    // property pkCartaArt10
    @Test
    void testGetterSetterPkCartaArt10() {

        assertNotNull(datosCocoa.getPkCartaArt10());

        String vString = "A";
        datosCocoa.setPkCartaArt10(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCartaArt10()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCartaArt10(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCartaArt10()));
    }

    // property pkSeguroCol
    @Test
    void testGetterSetterPkSeguroCol() {

        assertNotNull(datosCocoa.getPkSeguroCol());

        String vString = "A";
        datosCocoa.setPkSeguroCol(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkSeguroCol()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkSeguroCol(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkSeguroCol()));
    }

    // property pkContrato
    @Test
    void testGetterSetterPkContrato() {

        assertNotNull(datosCocoa.getPkContrato());

        String vString = "A";
        datosCocoa.setPkContrato(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkContrato()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkContrato(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkContrato()));
    }

    // property pkChequePagoDer
    @Test
    void testGetterSetterPkChequePagoDer() {

        assertNotNull(datosCocoa.getPkChequePagoDer());

        String vString = "A";
        datosCocoa.setPkChequePagoDer(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkChequePagoDer()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkChequePagoDer(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkChequePagoDer()));
    }

    // property pkCopiaCedDef
    @Test
    void testGetterSetterPkCopiaCedDef() {

        assertNotNull(datosCocoa.getPkCopiaCedDef());

        String vString = "A";
        datosCocoa.setPkCopiaCedDef(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCopiaCedDef()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCopiaCedDef(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCopiaCedDef()));
    }

    // property pkCursoPade
    @Test
    void testGetterSetterPkCursoPade() {

        assertNotNull(datosCocoa.getPkCursoPade());

        String vString = "A";
        datosCocoa.setPkCursoPade(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkCursoPade()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkCursoPade(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkCursoPade()));
    }

    // property pkInvestigacion
    @Test
    void testGetterSetterPkInvestigacion() {

        assertNotNull(datosCocoa.getPkInvestigacion());

        String vString = "A";
        datosCocoa.setPkInvestigacion(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkInvestigacion()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkInvestigacion(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkInvestigacion()));
    }

    // property pkFechaPlazo
    @Test
    void testGetterSetterPkFechaPlazo() {

        assertNotNull(datosCocoa.getPkFechaPlazo());

        int vint = 123456;
        datosCocoa.setPkFechaPlazo(vint); // int
        assertEquals(asInt(vint), asInt(datosCocoa.getPkFechaPlazo()));

        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        datosCocoa.setPkFechaPlazo(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosCocoa.getPkFechaPlazo()));
    }

    // property pkTipCedulaAux
    @Test
    void testGetterSetterPkTipCedulaAux() {

        assertNotNull(datosCocoa.getPkTipCedulaAux());

        String vString = "A";
        datosCocoa.setPkTipCedulaAux(vString); // String
        assertEquals(asStr(vString), asStr(datosCocoa.getPkTipCedulaAux()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        datosCocoa.setPkTipCedulaAux(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(datosCocoa.getPkTipCedulaAux()));
    }

    // property pkNivelNal
    @Test
    void testGetterSetterPkNivelNal() {

        assertNotNull(datosCocoa.getPkNivelNal());

        int vint = 12;
        datosCocoa.setPkNivelNal(vint); // int
        assertEquals(asInt(vint), asInt(datosCocoa.getPkNivelNal()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosCocoa.setPkNivelNal(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosCocoa.getPkNivelNal()));
    }

    // property pkNivelProv
    @Test
    void testGetterSetterPkNivelProv() {

        assertNotNull(datosCocoa.getPkNivelProv());

        int vint = 12;
        datosCocoa.setPkNivelProv(vint); // int
        assertEquals(asInt(vint), asInt(datosCocoa.getPkNivelProv()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        datosCocoa.setPkNivelProv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(datosCocoa.getPkNivelProv()));
    }

    // property pkFolio1
    @Test
    void testGetterSetterPkFolio1() {

        // Property: pkFolioInvesr.pkFolio1 -> 07 PK-FOLIO1 999
        assertNotNull(datosCocoa.getPkFolio1());
        int vint = 123;
        datosCocoa.setPkFolio1(vint); // int
        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        datosCocoa.setPkFolio1(vINumericValue); // INumericValue
    }

    // property pkFolio2
    @Test
    void testGetterSetterPkFolio2() {

        // Property: pkFolioInvesr.pkFolio2 -> 07 PK-FOLIO2 9999
        assertNotNull(datosCocoa.getPkFolio2());
        int vint = 1234;
        datosCocoa.setPkFolio2(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        datosCocoa.setPkFolio2(vINumericValue); // INumericValue
    }
}

