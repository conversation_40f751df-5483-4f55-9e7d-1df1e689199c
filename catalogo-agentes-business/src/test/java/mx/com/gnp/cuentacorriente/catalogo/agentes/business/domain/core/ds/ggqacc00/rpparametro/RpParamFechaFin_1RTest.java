package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpparametro;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-PARAM-FECHA-FIN-1-R.
 *
 */
class RpParamFechaFin_1RTest {

    private RpParamFechaFin_1R rpParamFechaFin_1R;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpParamFechaFin_1R = new RpParamFechaFin_1R();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RpParamFechaFin_1R rpParamFechaFin_1R2 = new RpParamFechaFin_1R(parent);
        assertNotNull(rpParamFechaFin_1R2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        RpParamFechaFin_1R rpParamFechaFin_1R3 = new RpParamFechaFin_1R(parent, sibling);
        assertNotNull(rpParamFechaFin_1R3);
    }

    @Test
    void testInitialize() {
        assertNotNull(rpParamFechaFin_1R.getRpFechaFin_1Aaaa());

        assertNotNull(rpParamFechaFin_1R.getRpGuionFin_1_1());

        assertNotNull(rpParamFechaFin_1R.getRpFechaFin_1Mm());

        assertNotNull(rpParamFechaFin_1R.getRpGuionFin_1_2());

        assertNotNull(rpParamFechaFin_1R.getRpFechaFin_1Dd());

    }

    // property rpFechaFin_1Aaaa
    @Test
    void testGetterSetterRpFechaFin_1Aaaa() {

        assertNotNull(rpParamFechaFin_1R.getRpFechaFin_1Aaaa());

        int vint = 1234;
        rpParamFechaFin_1R.setRpFechaFin_1Aaaa(vint); // int
        assertEquals(asInt(vint), asInt(rpParamFechaFin_1R.getRpFechaFin_1Aaaa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        rpParamFechaFin_1R.setRpFechaFin_1Aaaa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpParamFechaFin_1R.getRpFechaFin_1Aaaa()));
        // Conditionals (88) - RP-FECHA-FIN-1-AAAA
        rpParamFechaFin_1R.setFechaFin_1AaaaOk();
        assertTrue(rpParamFechaFin_1R.isFechaFin_1AaaaOk());

    }

    // property rpGuionFin_1_1
    @Test
    void testGetterSetterRpGuionFin_1_1() {

        assertNotNull(rpParamFechaFin_1R.getRpGuionFin_1_1());

        String vString = "A";
        rpParamFechaFin_1R.setRpGuionFin_1_1(vString); // String
        assertEquals(asStr(vString), asStr(rpParamFechaFin_1R.getRpGuionFin_1_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParamFechaFin_1R.setRpGuionFin_1_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rpParamFechaFin_1R.getRpGuionFin_1_1()));
    }

    // property rpFechaFin_1Mm
    @Test
    void testGetterSetterRpFechaFin_1Mm() {

        assertNotNull(rpParamFechaFin_1R.getRpFechaFin_1Mm());

        int vint = 12;
        rpParamFechaFin_1R.setRpFechaFin_1Mm(vint); // int
        assertEquals(asInt(vint), asInt(rpParamFechaFin_1R.getRpFechaFin_1Mm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParamFechaFin_1R.setRpFechaFin_1Mm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpParamFechaFin_1R.getRpFechaFin_1Mm()));
        // Conditionals (88) - RP-FECHA-FIN-1-MM
        rpParamFechaFin_1R.setFechaFin_1MmOk();
        assertTrue(rpParamFechaFin_1R.isFechaFin_1MmOk());

    }

    // property rpGuionFin_1_2
    @Test
    void testGetterSetterRpGuionFin_1_2() {

        assertNotNull(rpParamFechaFin_1R.getRpGuionFin_1_2());

        String vString = "A";
        rpParamFechaFin_1R.setRpGuionFin_1_2(vString); // String
        assertEquals(asStr(vString), asStr(rpParamFechaFin_1R.getRpGuionFin_1_2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        rpParamFechaFin_1R.setRpGuionFin_1_2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rpParamFechaFin_1R.getRpGuionFin_1_2()));
    }

    // property rpFechaFin_1Dd
    @Test
    void testGetterSetterRpFechaFin_1Dd() {

        assertNotNull(rpParamFechaFin_1R.getRpFechaFin_1Dd());

        int vint = 12;
        rpParamFechaFin_1R.setRpFechaFin_1Dd(vint); // int
        assertEquals(asInt(vint), asInt(rpParamFechaFin_1R.getRpFechaFin_1Dd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rpParamFechaFin_1R.setRpFechaFin_1Dd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpParamFechaFin_1R.getRpFechaFin_1Dd()));
        // Conditionals (88) - RP-FECHA-FIN-1-DD
        rpParamFechaFin_1R.setFechaFin_1DdOk();
        assertTrue(rpParamFechaFin_1R.isFechaFin_1DdOk());

    }
}

