package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-DATA-IMP.
 *
 */
class RpDataImpTest {

    private RpDataImp rpDataImp;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpDataImp = new RpDataImp();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(rpDataImp.getRpCf1ImpSbtFct());

        assertNotNull(rpDataImp.getRpCf1ImpTotFct());

    }

    // property rpCf1ImpSbtFct
    @Test
    void testGetterSetterRpCf1ImpSbtFct() {

        assertNotNull(rpDataImp.getRpCf1ImpSbtFct());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataImp.setRpCf1ImpSbtFct(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataImp.getRpCf1ImpSbtFct().toBigDecimal());

        int vint = 123456789;
        rpDataImp.setRpCf1ImpSbtFct(vint); // int
        assertEquals(asInt(vint), asInt(rpDataImp.getRpCf1ImpSbtFct()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataImp.setRpCf1ImpSbtFct(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataImp.getRpCf1ImpSbtFct()));
    }

    // property rpCf1ImpTotFct
    @Test
    void testGetterSetterRpCf1ImpTotFct() {

        assertNotNull(rpDataImp.getRpCf1ImpTotFct());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataImp.setRpCf1ImpTotFct(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataImp.getRpCf1ImpTotFct().toBigDecimal());

        int vint = 123456789;
        rpDataImp.setRpCf1ImpTotFct(vint); // int
        assertEquals(asInt(vint), asInt(rpDataImp.getRpCf1ImpTotFct()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataImp.setRpCf1ImpTotFct(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataImp.getRpCf1ImpTotFct()));
    }
}

