package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-REG-SALIDA.
 *
 */
class WsRegSalidaTest {

    private WsRegSalida wsRegSalida;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsRegSalida = new WsRegSalida();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsRegSalida wsRegSalida2 = new WsRegSalida(parent);
        assertNotNull(wsRegSalida2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 221);
        WsRegSalida wsRegSalida3 = new WsRegSalida(parent, sibling);
        assertNotNull(wsRegSalida3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsRegSalida.getSIdentificador());

        assertTrue(wsRegSalida.getSAgtAgtIdr().isZero());

        assertNotNull(wsRegSalida.getSAgtAgtNom());

        assertNotNull(wsRegSalida.getSAgtAgtApePat());

        assertNotNull(wsRegSalida.getSAgtAgtApeMat());

        assertNotNull(wsRegSalida.getSWsIndGerente());

        assertNotNull(wsRegSalida.getSCmaCmaDes());

        assertTrue(wsRegSalida.getSAgtAgtNip().isZero());







        assertNotNull(wsRegSalida.getWsLimitadFin());

    }

    // property sIdentificador
    @Test
    void testGetterSetterSIdentificador() {

        assertNotNull(wsRegSalida.getSIdentificador());

        String vString = "ABCa";
        wsRegSalida.setSIdentificador(vString); // String
        assertEquals(asStr(vString), asStr(wsRegSalida.getSIdentificador()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsRegSalida.setSIdentificador(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsRegSalida.getSIdentificador()));
    }

    // property sAgtAgtIdr
    @Test
    void testGetterSetterSAgtAgtIdr() {

        assertNotNull(wsRegSalida.getSAgtAgtIdr());

        int vint = 12345;
        wsRegSalida.setSAgtAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsRegSalida.getSAgtAgtIdr()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        wsRegSalida.setSAgtAgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsRegSalida.getSAgtAgtIdr()));
    }

    // property sAgtAgtNom
    @Test
    void testGetterSetterSAgtAgtNom() {

        assertNotNull(wsRegSalida.getSAgtAgtNom());

        String vString = "ABCabc1230";
        wsRegSalida.setSAgtAgtNom(vString); // String
        assertEquals(asStr(vString), asStr(wsRegSalida.getSAgtAgtNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsRegSalida.setSAgtAgtNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsRegSalida.getSAgtAgtNom()));
    }

    // property sAgtAgtApePat
    @Test
    void testGetterSetterSAgtAgtApePat() {

        assertNotNull(wsRegSalida.getSAgtAgtApePat());

        String vString = "ABCabc1230";
        wsRegSalida.setSAgtAgtApePat(vString); // String
        assertEquals(asStr(vString), asStr(wsRegSalida.getSAgtAgtApePat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsRegSalida.setSAgtAgtApePat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsRegSalida.getSAgtAgtApePat()));
    }

    // property sAgtAgtApeMat
    @Test
    void testGetterSetterSAgtAgtApeMat() {

        assertNotNull(wsRegSalida.getSAgtAgtApeMat());

        String vString = "ABCabc1230";
        wsRegSalida.setSAgtAgtApeMat(vString); // String
        assertEquals(asStr(vString), asStr(wsRegSalida.getSAgtAgtApeMat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsRegSalida.setSAgtAgtApeMat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsRegSalida.getSAgtAgtApeMat()));
    }

    // property sWsIndGerente
    @Test
    void testGetterSetterSWsIndGerente() {

        assertNotNull(wsRegSalida.getSWsIndGerente());

        String vString = "ABCabc1";
        wsRegSalida.setSWsIndGerente(vString); // String
        assertEquals(asStr(vString), asStr(wsRegSalida.getSWsIndGerente()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wsRegSalida.setSWsIndGerente(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsRegSalida.getSWsIndGerente()));
    }

    // property sCmaCmaDes
    @Test
    void testGetterSetterSCmaCmaDes() {

        assertNotNull(wsRegSalida.getSCmaCmaDes());

        String vString = "ABCabc1230";
        wsRegSalida.setSCmaCmaDes(vString); // String
        assertEquals(asStr(vString), asStr(wsRegSalida.getSCmaCmaDes()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsRegSalida.setSCmaCmaDes(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsRegSalida.getSCmaCmaDes()));
    }

    // property sAgtAgtNip
    @Test
    void testGetterSetterSAgtAgtNip() {

        assertNotNull(wsRegSalida.getSAgtAgtNip());

        int vint = 1234;
        wsRegSalida.setSAgtAgtNip(vint); // int
        assertEquals(asInt(vint), asInt(wsRegSalida.getSAgtAgtNip()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsRegSalida.setSAgtAgtNip(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsRegSalida.getSAgtAgtNip()));
    }

    // property tCna0_1
    @Test
    void testGetterSetterTCna0_1() {

        assertNotNull(wsRegSalida.getTCna0_1());
        assertNotNull(wsRegSalida.getTCna0_1(1));
    }

    // property tFla1
    @Test
    void testGetterSetterTFla1() {

        assertNotNull(wsRegSalida.getTFla1());
        assertNotNull(wsRegSalida.getTFla1(1));
    }

    // property tGer0_1
    @Test
    void testGetterSetterTGer0_1() {

        assertNotNull(wsRegSalida.getTGer0_1());
        assertNotNull(wsRegSalida.getTGer0_1(1));
    }

    // property tCeo0
    @Test
    void testGetterSetterTCeo0() {

        assertNotNull(wsRegSalida.getTCeo0());
        assertNotNull(wsRegSalida.getTCeo0(1));
    }

    // property tOfn0_1
    @Test
    void testGetterSetterTOfn0_1() {

        assertNotNull(wsRegSalida.getTOfn0_1());
        assertNotNull(wsRegSalida.getTOfn0_1(1));
    }

    // property tFin
    @Test
    void testGetterSetterTFin() {

        assertNotNull(wsRegSalida.getTFin());
        assertNotNull(wsRegSalida.getTFin(1));
    }

    // property wsLimitadFin
    @Test
    void testGetterSetterWsLimitadFin() {

        assertNotNull(wsRegSalida.getWsLimitadFin());

        String vString = "ABC";
        wsRegSalida.setWsLimitadFin(vString); // String
        assertEquals(asStr(vString), asStr(wsRegSalida.getWsLimitadFin()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsRegSalida.setWsLimitadFin(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsRegSalida.getWsLimitadFin()));
    }

    // property tCna0Elem_1
    @Test
    void testGetterSetterTCna0Elem_1() {

        // Property: tCna0_1.tCna0Elem_1 -> 15 T-CNA0-ELEM-1
        assertNotNull(wsRegSalida.getTCna0Elem_1());
    }

    // property sCnaCnaNumElem
    @Test
    void testGetterSetterSCnaCnaNumElem() {

        // Property: tCna0_1.tCna0Elem_1.sCnaCnaNumElem -> 20 S-CNA-CNA-NUM-ELEM X(01)
        assertNotNull(wsRegSalida.getSCnaCnaNumElem());
        String vString = "A";
        wsRegSalida.setSCnaCnaNumElem(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsRegSalida.setSCnaCnaNumElem(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tFla0Elem
    @Test
    void testGetterSetterTFla0Elem() {

        // Property: tFla1.tFla0Elem -> 15 T-FLA0-ELEM
        assertNotNull(wsRegSalida.getTFla0Elem());
    }

    // property sFlaFlaNum1
    @Test
    void testGetterSetterSFlaFlaNum1() {

        // Property: tFla1.tFla0Elem.sFlaFlaNum1 -> 20 S-FLA-FLA-NUM1 X(08)
        assertNotNull(wsRegSalida.getSFlaFlaNum1());
        String vString = "ABCabc12";
        wsRegSalida.setSFlaFlaNum1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsRegSalida.setSFlaFlaNum1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sFlaLimitad1
    @Test
    void testGetterSetterSFlaLimitad1() {

        // Property: tFla1.tFla0Elem.sFlaLimitad1 -> 20 S-FLA-LIMITAD1 X(01)
        assertNotNull(wsRegSalida.getSFlaLimitad1());
        String vString = "A";
        wsRegSalida.setSFlaLimitad1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsRegSalida.setSFlaLimitad1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tGer0Elem_1
    @Test
    void testGetterSetterTGer0Elem_1() {

        // Property: tGer0_1.tGer0Elem_1 -> 15 T-GER0-ELEM-1
        assertNotNull(wsRegSalida.getTGer0Elem_1());
    }

    // property sCnaGerZonElem
    @Test
    void testGetterSetterSCnaGerZonElem() {

        // Property: tGer0_1.tGer0Elem_1.sCnaGerZonElem -> 20 S-CNA-GER-ZON-ELEM X(01)
        assertNotNull(wsRegSalida.getSCnaGerZonElem());
        String vString = "A";
        wsRegSalida.setSCnaGerZonElem(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsRegSalida.setSCnaGerZonElem(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tCeo0Elem
    @Test
    void testGetterSetterTCeo0Elem() {

        // Property: tCeo0.tCeo0Elem -> 15 T-CEO0-ELEM
        assertNotNull(wsRegSalida.getTCeo0Elem());
    }

    // property sCeoCeoNom
    @Test
    void testGetterSetterSCeoCeoNom() {

        // Property: tCeo0.tCeo0Elem.sCeoCeoNom -> 20 S-CEO-CEO-NOM X(30)
        assertNotNull(wsRegSalida.getSCeoCeoNom());
        String vString = "ABCabc1230";
        wsRegSalida.setSCeoCeoNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsRegSalida.setSCeoCeoNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sCeoLimitad
    @Test
    void testGetterSetterSCeoLimitad() {

        // Property: tCeo0.tCeo0Elem.sCeoLimitad -> 20 S-CEO-LIMITAD X(01)
        assertNotNull(wsRegSalida.getSCeoLimitad());
        String vString = "A";
        wsRegSalida.setSCeoLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsRegSalida.setSCeoLimitad(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tOfn0Elem_1
    @Test
    void testGetterSetterTOfn0Elem_1() {

        // Property: tOfn0_1.tOfn0Elem_1 -> 15 T-OFN0-ELEM-1
        assertNotNull(wsRegSalida.getTOfn0Elem_1());
    }

    // property sCnaOfnCveElem
    @Test
    void testGetterSetterSCnaOfnCveElem() {

        // Property: tOfn0_1.tOfn0Elem_1.sCnaOfnCveElem -> 20 S-CNA-OFN-CVE-ELEM X(01)
        assertNotNull(wsRegSalida.getSCnaOfnCveElem());
        String vString = "A";
        wsRegSalida.setSCnaOfnCveElem(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsRegSalida.setSCnaOfnCveElem(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tFinElem
    @Test
    void testGetterSetterTFinElem() {

        // Property: tFin.tFinElem -> 15 T-FIN-ELEM
        assertNotNull(wsRegSalida.getTFinElem());
    }

    // property sTablaFin
    @Test
    void testGetterSetterSTablaFin() {

        // Property: tFin.tFinElem.sTablaFin -> 20 S-TABLA-FIN X(09)
        assertNotNull(wsRegSalida.getSTablaFin());
        String vString = "ABCabc123";
        wsRegSalida.setSTablaFin(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc123");
        wsRegSalida.setSTablaFin(vIAlphanumericValue); // IAlphanumericValue
    }
}

