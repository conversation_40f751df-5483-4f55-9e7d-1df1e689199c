package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGCCTMND0.
 *
 */
class WsDclgcctmnd0Test {

    private WsDclgcctmnd0 wsDclgcctmnd0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgcctmnd0 = new WsDclgcctmnd0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgcctmnd0.setMndMndCve(0);
        assertEquals(0, wsDclgcctmnd0.getMndMndCve());

        wsDclgcctmnd0.setMndMndNom("");
        assertEquals("", wsDclgcctmnd0.getMndMndNom());

        wsDclgcctmnd0.setMndFecUltAct("");
        assertEquals("", wsDclgcctmnd0.getMndFecUltAct());

        wsDclgcctmnd0.setMndUsuCveAct("");
        assertEquals("", wsDclgcctmnd0.getMndUsuCveAct());

        wsDclgcctmnd0.setMndEdoCve("");
        assertEquals("", wsDclgcctmnd0.getMndEdoCve());

    }

    // property mndMndCve
    @Test
    void testGetterSetterMndMndCve() {

        assertNotNull(wsDclgcctmnd0.getMndMndCve());

        int vint = 123456789;
        wsDclgcctmnd0.setMndMndCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgcctmnd0.getMndMndCve()));
    }

    // property mndMndNom
    @Test
    void testGetterSetterMndMndNom() {

        assertNotNull(wsDclgcctmnd0.getMndMndNom());

        String vString = "ABCabc1230";
        wsDclgcctmnd0.setMndMndNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctmnd0.getMndMndNom()));
    }

    // property mndFecUltAct
    @Test
    void testGetterSetterMndFecUltAct() {

        assertNotNull(wsDclgcctmnd0.getMndFecUltAct());

        String vString = "ABCabc1230";
        wsDclgcctmnd0.setMndFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctmnd0.getMndFecUltAct()));
    }

    // property mndUsuCveAct
    @Test
    void testGetterSetterMndUsuCveAct() {

        assertNotNull(wsDclgcctmnd0.getMndUsuCveAct());

        String vString = "ABCabc12";
        wsDclgcctmnd0.setMndUsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctmnd0.getMndUsuCveAct()));
    }

    // property mndEdoCve
    @Test
    void testGetterSetterMndEdoCve() {

        assertNotNull(wsDclgcctmnd0.getMndEdoCve());

        String vString = "ABCa";
        wsDclgcctmnd0.setMndEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctmnd0.getMndEdoCve()));
    }
}

