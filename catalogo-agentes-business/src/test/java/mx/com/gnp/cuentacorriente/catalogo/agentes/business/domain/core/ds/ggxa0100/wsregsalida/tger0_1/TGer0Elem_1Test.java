package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tger0_1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-GER0-ELEM-1.
 *
 */
class TGer0Elem_1Test {

    private TGer0Elem_1 tGer0Elem_1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tGer0Elem_1 = new TGer0Elem_1();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TGer0Elem_1 tGer0Elem_12 = new TGer0Elem_1(parent);
        assertNotNull(tGer0Elem_12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 1);
        TGer0Elem_1 tGer0Elem_13 = new TGer0Elem_1(parent, sibling);
        assertNotNull(tGer0Elem_13);
    }

    @Test
    void testInitialize() {
        assertNotNull(tGer0Elem_1.getSCnaGerZonElem());

    }

    // property sCnaGerZonElem
    @Test
    void testGetterSetterSCnaGerZonElem() {

        assertNotNull(tGer0Elem_1.getSCnaGerZonElem());

        String vString = "A";
        tGer0Elem_1.setSCnaGerZonElem(vString); // String
        assertEquals(asStr(vString), asStr(tGer0Elem_1.getSCnaGerZonElem()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tGer0Elem_1.setSCnaGerZonElem(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tGer0Elem_1.getSCnaGerZonElem()));
    }
}

