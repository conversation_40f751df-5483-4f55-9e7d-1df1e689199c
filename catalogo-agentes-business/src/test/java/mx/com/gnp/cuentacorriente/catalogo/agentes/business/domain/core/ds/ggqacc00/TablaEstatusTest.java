package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-ESTATUS.
 *
 */
class TablaEstatusTest {

    private TablaEstatus tablaEstatus;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaEstatus = new TablaEstatus();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tabEstatus
    @Test
    void testGetterSetterTabEstatus() {

        assertNotNull(tablaEstatus.getTabEstatus());
        assertNotNull(tablaEstatus.getTabEstatus(1));
    }

    // property tbMteCve
    @Test
    void testGetterSetterTbMteCve() {

        // Property: tabEstatus.tbMteCve -> 07 TB-MTE-CVE 9(04)
        assertNotNull(tablaEstatus.getTbMteCve());
        int vint = 1234;
        tablaEstatus.setTbMteCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tablaEstatus.setTbMteCve(vINumericValue); // INumericValue
    }

    // property tbMteEsaCve
    @Test
    void testGetterSetterTbMteEsaCve() {

        // Property: tabEstatus.tbMteEsaCve -> 07 TB-MTE-ESA-CVE 9(04)
        assertNotNull(tablaEstatus.getTbMteEsaCve());
        int vint = 1234;
        tablaEstatus.setTbMteEsaCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tablaEstatus.setTbMteEsaCve(vINumericValue); // INumericValue
    }
}

