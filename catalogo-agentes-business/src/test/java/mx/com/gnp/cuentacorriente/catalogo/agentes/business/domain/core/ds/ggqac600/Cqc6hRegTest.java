package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: CQC6H-REG.
 *
 */
class Cqc6hRegTest {

    private Cqc6hReg cqc6hReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        cqc6hReg = new Cqc6hReg();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Cqc6hReg cqc6hReg2 = new Cqc6hReg(parent);
        assertNotNull(cqc6hReg2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 7);
        Cqc6hReg cqc6hReg3 = new Cqc6hReg(parent, sibling);
        assertNotNull(cqc6hReg3);
    }

    @Test
    void testInitialize() {

    }

    // property cqc6hDatos
    @Test
    void testGetterSetterCqc6hDatos() {

        assertNotNull(cqc6hReg.getCqc6hDatos());
    }

    // property cqc6hAgt
    @Test
    void testGetterSetterCqc6hAgt() {

        // Property: cqc6hDatos.cqc6hAgt -> 10 CQC6H-AGT X(05)
        assertNotNull(cqc6hReg.getCqc6hAgt());
        String vString = "ABCab";
        cqc6hReg.setCqc6hAgt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        cqc6hReg.setCqc6hAgt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property cqc6hSpaces
    @Test
    void testGetterSetterCqc6hSpaces() {

        // Property: cqc6hDatos.cqc6hSpaces -> 10 CQC6H-SPACES X(01)
        assertNotNull(cqc6hReg.getCqc6hSpaces());
        String vString = "A";
        cqc6hReg.setCqc6hSpaces(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        cqc6hReg.setCqc6hSpaces(vIAlphanumericValue); // IAlphanumericValue
    }

    // property cqc6hIndExc
    @Test
    void testGetterSetterCqc6hIndExc() {

        // Property: cqc6hDatos.cqc6hIndExc -> 10 CQC6H-IND-EXC X(01)
        assertNotNull(cqc6hReg.getCqc6hIndExc());
        String vString = "A";
        cqc6hReg.setCqc6hIndExc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        cqc6hReg.setCqc6hIndExc(vIAlphanumericValue); // IAlphanumericValue
    }
}

