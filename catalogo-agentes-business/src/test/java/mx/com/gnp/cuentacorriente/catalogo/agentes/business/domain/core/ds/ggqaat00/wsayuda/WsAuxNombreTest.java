package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-NOMBRE.
 *
 */
class WsAuxNombreTest {

    private WsAuxNombre wsAuxNombre;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxNombre = new WsAuxNombre();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {



    }

    // property wsAuxAPat
    @Test
    void testGetterSetterWsAuxAPat() {

        assertNotNull(wsAuxNombre.getWsAuxAPat());
    }

    // property wsAuxAMat
    @Test
    void testGetterSetterWsAuxAMat() {

        assertNotNull(wsAuxNombre.getWsAuxAMat());
    }

    // property wsAuxLasName
    @Test
    void testGetterSetterWsAuxLasName() {

        assertNotNull(wsAuxNombre.getWsAuxLasName());
    }

    // property wsAuxApPat
    @Test
    void testGetterSetterWsAuxApPat() {

        // Property: wsAuxAPat.wsAuxApPat -> 15 WS-AUX-AP-PAT X(01) [40]
        assertNotNull(wsAuxNombre.getWsAuxApPat());
        String vString = "A";
        wsAuxNombre.getWsAuxApPat().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxNombre.getWsAuxApPat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsAuxApMat
    @Test
    void testGetterSetterWsAuxApMat() {

        // Property: wsAuxAMat.wsAuxApMat -> 15 WS-AUX-AP-MAT X(01) [30]
        assertNotNull(wsAuxNombre.getWsAuxApMat());
        String vString = "A";
        wsAuxNombre.getWsAuxApMat().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxNombre.getWsAuxApMat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsAuxLastName
    @Test
    void testGetterSetterWsAuxLastName() {

        // Property: wsAuxLasName.wsAuxLastName -> 15 WS-AUX-LAST-NAME X(01) [40]
        assertNotNull(wsAuxNombre.getWsAuxLastName());
        String vString = "A";
        wsAuxNombre.getWsAuxLastName().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxNombre.getWsAuxLastName().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }
}

