package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-NULLS.
 *
 */
class WsNullsTest {

    private WsNulls wsNulls;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsNulls = new WsNulls();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsNulls.setWsNull01(0);
        assertEquals(0, wsNulls.getWsNull01());

        wsNulls.setWsNull02(0);
        assertEquals(0, wsNulls.getWsNull02());

        wsNulls.setWsNull03(0);
        assertEquals(0, wsNulls.getWsNull03());

        wsNulls.setWsNull04(0);
        assertEquals(0, wsNulls.getWsNull04());

        wsNulls.setWsNull05(0);
        assertEquals(0, wsNulls.getWsNull05());

        wsNulls.setWsNull06(0);
        assertEquals(0, wsNulls.getWsNull06());

        wsNulls.setWsNull07(0);
        assertEquals(0, wsNulls.getWsNull07());

        wsNulls.setWsNull08(0);
        assertEquals(0, wsNulls.getWsNull08());

        wsNulls.setWsNull09(0);
        assertEquals(0, wsNulls.getWsNull09());

        wsNulls.setWsNull10(0);
        assertEquals(0, wsNulls.getWsNull10());

    }

    // property wsNull01
    @Test
    void testGetterSetterWsNull01() {

        assertNotNull(wsNulls.getWsNull01());

        int vint = 1234;
        wsNulls.setWsNull01(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull01()));
    }

    // property wsNull02
    @Test
    void testGetterSetterWsNull02() {

        assertNotNull(wsNulls.getWsNull02());

        int vint = 1234;
        wsNulls.setWsNull02(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull02()));
    }

    // property wsNull03
    @Test
    void testGetterSetterWsNull03() {

        assertNotNull(wsNulls.getWsNull03());

        int vint = 1234;
        wsNulls.setWsNull03(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull03()));
    }

    // property wsNull04
    @Test
    void testGetterSetterWsNull04() {

        assertNotNull(wsNulls.getWsNull04());

        int vint = 1234;
        wsNulls.setWsNull04(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull04()));
    }

    // property wsNull05
    @Test
    void testGetterSetterWsNull05() {

        assertNotNull(wsNulls.getWsNull05());

        int vint = 1234;
        wsNulls.setWsNull05(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull05()));
    }

    // property wsNull06
    @Test
    void testGetterSetterWsNull06() {

        assertNotNull(wsNulls.getWsNull06());

        int vint = 1234;
        wsNulls.setWsNull06(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull06()));
    }

    // property wsNull07
    @Test
    void testGetterSetterWsNull07() {

        assertNotNull(wsNulls.getWsNull07());

        int vint = 1234;
        wsNulls.setWsNull07(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull07()));
    }

    // property wsNull08
    @Test
    void testGetterSetterWsNull08() {

        assertNotNull(wsNulls.getWsNull08());

        int vint = 1234;
        wsNulls.setWsNull08(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull08()));
    }

    // property wsNull09
    @Test
    void testGetterSetterWsNull09() {

        assertNotNull(wsNulls.getWsNull09());

        int vint = 1234;
        wsNulls.setWsNull09(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull09()));
    }

    // property wsNull10
    @Test
    void testGetterSetterWsNull10() {

        assertNotNull(wsNulls.getWsNull10());

        int vint = 1234;
        wsNulls.setWsNull10(vint); // int
        assertEquals(asInt(vint), asInt(wsNulls.getWsNull10()));
    }
}

