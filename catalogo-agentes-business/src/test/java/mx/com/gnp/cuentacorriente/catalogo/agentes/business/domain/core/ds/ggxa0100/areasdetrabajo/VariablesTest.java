package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: VARIABLES.
 *
 */
class VariablesTest {

    private Variables variables;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        variables = new Variables();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        variables.setCtnError(0);
        assertEquals(0, variables.getCtnError());


        assertNotNull(variables.getConError());

    }

    // property ctnError
    @Test
    void testGetterSetterCtnError() {

        assertNotNull(variables.getCtnError());

        int vint = 123;
        variables.setCtnError(vint); // int
        assertEquals(asInt(vint), asInt(variables.getCtnError()));
    }

    // property conIncidencias
    @Test
    void testGetterSetterConIncidencias() {

        assertNotNull(variables.getConIncidencias());
    }

    // property conError
    @Test
    void testGetterSetterConError() {

        assertNotNull(variables.getConError());

        String vString = "     1230+";
        variables.setConError(vString); // String
        assertEquals(asStr(vString), asStr(variables.getConError()));

        NumericVar vIValue = new NumericVar(10, 0);;
        vIValue.setValue("1234567890");
        variables.setConError(vIValue); // IValue
        // format("1234567890", "ZZZZZZZZZ+")
        assertNotNull(asStr(variables.getConError()));
    }

    // property conMensaje
    @Test
    void testGetterSetterConMensaje() {

        // Property: conIncidencias.conMensaje -> 15 CON-MENSAJE X(40)
        assertNotNull(variables.getConMensaje());
        String vString = "ABCabc1230";
        variables.setConMensaje(vString); // String
    }
}

