package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tabladirecciones;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TAB-DIRECCIONES.
 *
 */
class TabDireccionesTest {

    private TabDirecciones tabDirecciones;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tabDirecciones = new TabDirecciones(1);
        tabDirecciones.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TabDirecciones tabDirecciones2 = new TabDirecciones(parent, 1);
        assertNotNull(tabDirecciones2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TabDirecciones tabDirecciones3 = new TabDirecciones(parent, sibling, 1);
        assertNotNull(tabDirecciones3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tabDirecciones.getTbDirOfna());

        assertNotNull(tabDirecciones.getTbDirCae());

        assertNotNull(tabDirecciones.getTbDirNum());

        assertNotNull(tabDirecciones.getTbDirInt());

        assertNotNull(tabDirecciones.getTbDirCol());

        assertNotNull(tabDirecciones.getTbDirMndNom());

        assertNotNull(tabDirecciones.getTbDirPobNom());

        assertNotNull(tabDirecciones.getTbDirMndEdoCve());

        assertNotNull(tabDirecciones.getTbDirMndEdoNom());

        assertNotNull(tabDirecciones.getTbDirMndPaiCve());

        assertNotNull(tabDirecciones.getTbDirMndPaiNom());

        assertNotNull(tabDirecciones.getTbDirCpoCve());

        assertNotNull(tabDirecciones.getTbDirGerZonEnv());

    }

    // property tbDirOfna
    @Test
    void testGetterSetterTbDirOfna() {

        assertNotNull(tabDirecciones.getTbDirOfna());

        int vint = 1234;
        tabDirecciones.setTbDirOfna(vint); // int
        assertEquals(asInt(vint), asInt(tabDirecciones.getTbDirOfna()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tabDirecciones.setTbDirOfna(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabDirecciones.getTbDirOfna()));
    }

    // property tbDirCae
    @Test
    void testGetterSetterTbDirCae() {

        assertNotNull(tabDirecciones.getTbDirCae());

        String vString = "ABCabc1230";
        tabDirecciones.setTbDirCae(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirCae()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tabDirecciones.setTbDirCae(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirCae()));
    }

    // property tbDirNum
    @Test
    void testGetterSetterTbDirNum() {

        assertNotNull(tabDirecciones.getTbDirNum());

        String vString = "ABCab";
        tabDirecciones.setTbDirNum(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tabDirecciones.setTbDirNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirNum()));
    }

    // property tbDirInt
    @Test
    void testGetterSetterTbDirInt() {

        assertNotNull(tabDirecciones.getTbDirInt());

        String vString = "ABCab";
        tabDirecciones.setTbDirInt(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirInt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tabDirecciones.setTbDirInt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirInt()));
    }

    // property tbDirCol
    @Test
    void testGetterSetterTbDirCol() {

        assertNotNull(tabDirecciones.getTbDirCol());

        String vString = "ABCabc1230";
        tabDirecciones.setTbDirCol(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirCol()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tabDirecciones.setTbDirCol(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirCol()));
    }

    // property tbDirMndNom
    @Test
    void testGetterSetterTbDirMndNom() {

        assertNotNull(tabDirecciones.getTbDirMndNom());

        String vString = "ABCabc1230";
        tabDirecciones.setTbDirMndNom(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirMndNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tabDirecciones.setTbDirMndNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirMndNom()));
    }

    // property tbDirPobNom
    @Test
    void testGetterSetterTbDirPobNom() {

        assertNotNull(tabDirecciones.getTbDirPobNom());

        String vString = "ABCabc1230";
        tabDirecciones.setTbDirPobNom(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirPobNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tabDirecciones.setTbDirPobNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirPobNom()));
    }

    // property tbDirMndEdoCve
    @Test
    void testGetterSetterTbDirMndEdoCve() {

        assertNotNull(tabDirecciones.getTbDirMndEdoCve());

        String vString = "ABCa";
        tabDirecciones.setTbDirMndEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirMndEdoCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tabDirecciones.setTbDirMndEdoCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirMndEdoCve()));
    }

    // property tbDirMndEdoNom
    @Test
    void testGetterSetterTbDirMndEdoNom() {

        assertNotNull(tabDirecciones.getTbDirMndEdoNom());

        String vString = "ABCabc1230";
        tabDirecciones.setTbDirMndEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirMndEdoNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tabDirecciones.setTbDirMndEdoNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirMndEdoNom()));
    }

    // property tbDirMndPaiCve
    @Test
    void testGetterSetterTbDirMndPaiCve() {

        assertNotNull(tabDirecciones.getTbDirMndPaiCve());

        String vString = "ABC";
        tabDirecciones.setTbDirMndPaiCve(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirMndPaiCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        tabDirecciones.setTbDirMndPaiCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirMndPaiCve()));
    }

    // property tbDirMndPaiNom
    @Test
    void testGetterSetterTbDirMndPaiNom() {

        assertNotNull(tabDirecciones.getTbDirMndPaiNom());

        String vString = "ABCabc1230";
        tabDirecciones.setTbDirMndPaiNom(vString); // String
        assertEquals(asStr(vString), asStr(tabDirecciones.getTbDirMndPaiNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tabDirecciones.setTbDirMndPaiNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tabDirecciones.getTbDirMndPaiNom()));
    }

    // property tbDirCpoCve
    @Test
    void testGetterSetterTbDirCpoCve() {

        assertNotNull(tabDirecciones.getTbDirCpoCve());

        int vint = 12345;
        tabDirecciones.setTbDirCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(tabDirecciones.getTbDirCpoCve()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        tabDirecciones.setTbDirCpoCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabDirecciones.getTbDirCpoCve()));
    }

    // property tbDirGerZonEnv
    @Test
    void testGetterSetterTbDirGerZonEnv() {

        assertNotNull(tabDirecciones.getTbDirGerZonEnv());

        int vint = 1234;
        tabDirecciones.setTbDirGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(tabDirecciones.getTbDirGerZonEnv()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tabDirecciones.setTbDirGerZonEnv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabDirecciones.getTbDirGerZonEnv()));
    }
}

