package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: L-PARAMS.
 *
 */
class LParamsTest {

    private LParams lParams;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        lParams = new LParams();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        LParams lParams2 = new LParams(parent);
        assertNotNull(lParams2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 11);
        LParams lParams3 = new LParams(parent, sibling);
        assertNotNull(lParams3);
    }

    @Test
    void testInitialize() {
        assertNotNull(lParams.getLOption());

        assertNotNull(lParams.getLAgente());

    }

    // property lOption
    @Test
    void testGetterSetterLOption() {

        assertNotNull(lParams.getLOption());

        String vString = "ABCa";
        lParams.setLOption(vString); // String
        assertEquals(asStr(vString), asStr(lParams.getLOption()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        lParams.setLOption(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(lParams.getLOption()));
    }

    // property lAgente
    @Test
    void testGetterSetterLAgente() {

        assertNotNull(lParams.getLAgente());

        String vString = "ABCabc1";
        lParams.setLAgente(vString); // String
        assertEquals(asStr(vString), asStr(lParams.getLAgente()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        lParams.setLAgente(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(lParams.getLAgente()));
    }
}

