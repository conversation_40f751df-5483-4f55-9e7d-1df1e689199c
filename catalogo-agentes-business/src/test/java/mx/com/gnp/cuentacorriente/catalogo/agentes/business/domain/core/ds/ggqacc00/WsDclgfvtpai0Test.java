package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGFVTPAI0.
 *
 */
class WsDclgfvtpai0Test {

    private WsDclgfvtpai0 wsDclgfvtpai0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgfvtpai0 = new WsDclgfvtpai0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgfvtpai0.setPaiPaiCve(0);
        assertEquals(0, wsDclgfvtpai0.getPaiPaiCve());

    }

    // property paiPaiCve
    @Test
    void testGetterSetterPaiPaiCve() {

        assertNotNull(wsDclgfvtpai0.getPaiPaiCve());

        int vint = 1234;
        wsDclgfvtpai0.setPaiPaiCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtpai0.getPaiPaiCve()));
    }
}

