package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FECHA-TIMESTAMP.
 *
 */
class WsFechaTimestampTest {

    private WsFechaTimestamp wsFechaTimestamp;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFechaTimestamp = new WsFechaTimestamp();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFechaTimestamp wsFechaTimestamp2 = new WsFechaTimestamp(parent);
        assertNotNull(wsFechaTimestamp2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsFechaTimestamp wsFechaTimestamp3 = new WsFechaTimestamp(parent, sibling);
        assertNotNull(wsFechaTimestamp3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFechaTimestamp.getWsAaaaTimestamp());

        assertNotNull(wsFechaTimestamp.getWsMmTimestamp());

        assertNotNull(wsFechaTimestamp.getWsDdTimestamp());

    }

    // property wsAaaaTimestamp
    @Test
    void testGetterSetterWsAaaaTimestamp() {

        assertNotNull(wsFechaTimestamp.getWsAaaaTimestamp());

        int vint = 1234;
        wsFechaTimestamp.setWsAaaaTimestamp(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaTimestamp.getWsAaaaTimestamp()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsFechaTimestamp.setWsAaaaTimestamp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaTimestamp.getWsAaaaTimestamp()));
    }

    // property wsMmTimestamp
    @Test
    void testGetterSetterWsMmTimestamp() {

        assertNotNull(wsFechaTimestamp.getWsMmTimestamp());

        int vint = 12;
        wsFechaTimestamp.setWsMmTimestamp(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaTimestamp.getWsMmTimestamp()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaTimestamp.setWsMmTimestamp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaTimestamp.getWsMmTimestamp()));
    }

    // property wsDdTimestamp
    @Test
    void testGetterSetterWsDdTimestamp() {

        assertNotNull(wsFechaTimestamp.getWsDdTimestamp());

        int vint = 12;
        wsFechaTimestamp.setWsDdTimestamp(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaTimestamp.getWsDdTimestamp()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaTimestamp.setWsDdTimestamp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaTimestamp.getWsDdTimestamp()));
    }
}

