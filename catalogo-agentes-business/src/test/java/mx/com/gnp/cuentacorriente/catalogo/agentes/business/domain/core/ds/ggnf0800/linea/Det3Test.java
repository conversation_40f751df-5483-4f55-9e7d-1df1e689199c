package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.linea;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DET3.
 *
 */
class Det3Test {

    private Det3 det3;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        det3 = new Det3();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Det3 det32 = new Det3(parent);
        assertNotNull(det32);
        AlphanumericVar sibling = new AlphanumericVar(parent, 132);
        Det3 det33 = new Det3(parent, sibling);
        assertNotNull(det33);
    }

    @Test
    void testInitialize() {
        assertNotNull(det3.getDet12Letrero());

        assertNotNull(det3.getDet12Total());

    }

    // property det12Letrero
    @Test
    void testGetterSetterDet12Letrero() {

        assertNotNull(det3.getDet12Letrero());

        String vString = "ABCabc1230";
        det3.setDet12Letrero(vString); // String
        assertEquals(asStr(vString), asStr(det3.getDet12Letrero()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det3.setDet12Letrero(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det3.getDet12Letrero()));
    }

    // property det12Total
    @Test
    void testGetterSetterDet12Total() {

        assertNotNull(det3.getDet12Total());

        String vString = "      1";
        det3.setDet12Total(vString); // String
        assertEquals(asStr(vString), asStr(det3.getDet12Total()));

        NumericVar vIValue = new NumericVar(7, 0);;
        vIValue.setValue("1234567");
        det3.setDet12Total(vIValue); // IValue
        // format("1234567", "ZZZZZZ9")
        assertNotNull(asStr(det3.getDet12Total()));
    }
}

