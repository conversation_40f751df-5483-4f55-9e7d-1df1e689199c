package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: PK-RFC.
 *
 */
class PkRfcTest {

    private PkRfc pkRfc;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        pkRfc = new PkRfc();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        PkRfc pkRfc2 = new PkRfc(parent);
        assertNotNull(pkRfc2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 15);
        PkRfc pkRfc3 = new PkRfc(parent, sibling);
        assertNotNull(pkRfc3);
    }

    @Test
    void testInitialize() {
        assertNotNull(pkRfc.getPkRfc1());

        assertNotNull(pkRfc.getPkRfc2());

    }

    // property pkRfc1
    @Test
    void testGetterSetterPkRfc1() {

        assertNotNull(pkRfc.getPkRfc1());

        String vString = "ABCabc1230";
        pkRfc.setPkRfc1(vString); // String
        assertEquals(asStr(vString), asStr(pkRfc.getPkRfc1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        pkRfc.setPkRfc1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkRfc.getPkRfc1()));
    }

    // property pkRfc2
    @Test
    void testGetterSetterPkRfc2() {

        assertNotNull(pkRfc.getPkRfc2());

        String vString = "AB";
        pkRfc.setPkRfc2(vString); // String
        assertEquals(asStr(vString), asStr(pkRfc.getPkRfc2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        pkRfc.setPkRfc2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkRfc.getPkRfc2()));
    }
}

