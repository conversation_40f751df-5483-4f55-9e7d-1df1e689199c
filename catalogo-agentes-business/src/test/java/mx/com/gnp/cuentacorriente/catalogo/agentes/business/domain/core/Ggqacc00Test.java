package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import com.base100.caravel.support.common.cbl.exception.StopRunException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

class Ggqacc00Test {
    Ggqacc00 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggqacc00.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(StopRunException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p1100Estatus test conditions:
        sqlcode : [1, 0, 100, 101]
        wkDisp : [1, 2]
    */
    @Test
    void p1100EstatusTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(1);
        pgm.getBanderas().setWkDisp(1);
        try {
            pgm.p1100Estatus();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1100EstatusTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getBanderas().setWkDisp(2);
            try {
                pgm.p1100Estatus();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p1100EstatusTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                pgm.getBanderas().setWkDisp(2);
                try {
                    pgm.p1100Estatus();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p1100EstatusTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    pgm.getBanderas().setWkDisp(2);
                    try {
                        pgm.p1100Estatus();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(NullPointerException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p1105OpenCurMte test conditions:
        sqlcode : [1, 0]
    */
    @Test
    void p1105OpenCurMteTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(1);
        try {
            pgm.p1105OpenCurMte();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1105OpenCurMteTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            try {
                pgm.p1105OpenCurMte();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p1110FetchMte test conditions:
        wkDisp : [1, 2]
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p1110FetchMteTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getBanderas().setWkDisp(1);
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p1110FetchMte();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1110FetchMteTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getBanderas().setWkDisp(2);
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p1110FetchMte();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p1110FetchMteTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getBanderas().setWkDisp(2);
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p1110FetchMte();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p1110FetchMteTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getBanderas().setWkDisp(2);
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p1110FetchMte();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(NullPointerException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p1120LoopMte test conditions:
        wkDisp : [1, 2]
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p1120LoopMteTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getBanderas().setWkDisp(1);
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p1120LoopMte();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1120LoopMteTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getBanderas().setWkDisp(2);
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p1120LoopMte();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p1120LoopMteTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getBanderas().setWkDisp(2);
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p1120LoopMte();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p1120LoopMteTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getBanderas().setWkDisp(2);
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p1120LoopMte();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(NullPointerException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p1180CloseCurMte test conditions:
        sqlcode : [1, 0]
    */
    @Test
    void p1180CloseCurMteTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(1);
        try {
            pgm.p1180CloseCurMte();
        } catch (Exception e) {
            assertExceptionInstanceOf(StopRunException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1180CloseCurMteTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            try {
                pgm.p1180CloseCurMte();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p5000ValidaAgente test conditions:
        sqlcode : [0, 1, 100, 101]
        agtRazSoc : ["", "XX"]
    */
    @Test
    void p5000ValidaAgenteTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        pgm.getDclgfvtagt0().setAgtRazSoc("");
        try {
            pgm.p5000ValidaAgente();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5000ValidaAgenteTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            pgm.getDclgfvtagt0().setAgtRazSoc("XX");
            try {
                pgm.p5000ValidaAgente();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5000ValidaAgenteTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                pgm.getDclgfvtagt0().setAgtRazSoc("XX");
                try {
                    pgm.p5000ValidaAgente();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5000ValidaAgenteTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    pgm.getDclgfvtagt0().setAgtRazSoc("XX");
                    try {
                        pgm.p5000ValidaAgente();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5020LeeAgente test conditions:
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5020LeeAgenteTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5020LeeAgente();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5020LeeAgenteTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5020LeeAgente();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5020LeeAgenteTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5020LeeAgente();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5020LeeAgenteTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5020LeeAgente();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5500ObtieneDatosAgente test conditions:
        agtRazSoc : ["", "XX"]
        sqlcode : [0, 1, 100, 101]
        dmaTdmCve : [4, 3]
        indcmades : [-1, 0]
    */
    @Test
    void p5500ObtieneDatosAgenteTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getDclgfvtagt0().setAgtRazSoc("");
        pgm.getSqlca().setSqlcode(0);
        pgm.getWsDclgfvtdma0().setDmaTdmCve(4);
        pgm.getIndicadoresDeNulos().setIndcmades(-1);
        try {
            pgm.p5500ObtieneDatosAgente();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5500ObtieneDatosAgenteTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getDclgfvtagt0().setAgtRazSoc("XX");
            pgm.getSqlca().setSqlcode(1);
            pgm.getWsDclgfvtdma0().setDmaTdmCve(3);
            pgm.getIndicadoresDeNulos().setIndcmades(0);
            try {
                pgm.p5500ObtieneDatosAgente();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5500ObtieneDatosAgenteTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getDclgfvtagt0().setAgtRazSoc("XX");
                pgm.getSqlca().setSqlcode(100);
                pgm.getWsDclgfvtdma0().setDmaTdmCve(3);
                pgm.getIndicadoresDeNulos().setIndcmades(0);
                try {
                    pgm.p5500ObtieneDatosAgente();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5500ObtieneDatosAgenteTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getDclgfvtagt0().setAgtRazSoc("XX");
                    pgm.getSqlca().setSqlcode(101);
                    pgm.getWsDclgfvtdma0().setDmaTdmCve(3);
                    pgm.getIndicadoresDeNulos().setIndcmades(0);
                    try {
                        pgm.p5500ObtieneDatosAgente();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5510LeeGfvtpra test conditions:
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5510LeeGfvtpraTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5510LeeGfvtpra();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5510LeeGfvtpraTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5510LeeGfvtpra();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5510LeeGfvtpraTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5510LeeGfvtpra();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5510LeeGfvtpraTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5510LeeGfvtpra();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5520ObtieneDirec test conditions:
        dmaTdmCve : [4, 3]
        sqlcode : [1, 0, 100, 101]
        inddmaagt : [-1, 0]
        inddmaidr : [-1, 0]
        inddmatdm : [-1, 0]
        inddmacol : [-1, 0]
        inddmacpo : [-1, 0]
    */
    @Test
    void p5520ObtieneDirecTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsDclgfvtdma0().setDmaTdmCve(4);
        pgm.getSqlca().setSqlcode(1);
        pgm.getIndicadoresDeNulos().setInddmaagt(-1);
        pgm.getIndicadoresDeNulos().setInddmaidr(-1);
        pgm.getIndicadoresDeNulos().setInddmatdm(-1);
        pgm.getIndicadoresDeNulos().setInddmacol(-1);
        pgm.getIndicadoresDeNulos().setInddmacpo(-1);
        try {
            pgm.p5520ObtieneDirec();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5520ObtieneDirecTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsDclgfvtdma0().setDmaTdmCve(3);
            pgm.getSqlca().setSqlcode(0);
            pgm.getIndicadoresDeNulos().setInddmaagt(0);
            pgm.getIndicadoresDeNulos().setInddmaidr(0);
            pgm.getIndicadoresDeNulos().setInddmatdm(0);
            pgm.getIndicadoresDeNulos().setInddmacol(0);
            pgm.getIndicadoresDeNulos().setInddmacpo(0);
            try {
                pgm.p5520ObtieneDirec();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5520ObtieneDirecTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getWsDclgfvtdma0().setDmaTdmCve(3);
                pgm.getSqlca().setSqlcode(100);
                pgm.getIndicadoresDeNulos().setInddmaagt(0);
                pgm.getIndicadoresDeNulos().setInddmaidr(0);
                pgm.getIndicadoresDeNulos().setInddmatdm(0);
                pgm.getIndicadoresDeNulos().setInddmacol(0);
                pgm.getIndicadoresDeNulos().setInddmacpo(0);
                try {
                    pgm.p5520ObtieneDirec();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5520ObtieneDirecTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getWsDclgfvtdma0().setDmaTdmCve(3);
                    pgm.getSqlca().setSqlcode(101);
                    pgm.getIndicadoresDeNulos().setInddmaagt(0);
                    pgm.getIndicadoresDeNulos().setInddmaidr(0);
                    pgm.getIndicadoresDeNulos().setInddmatdm(0);
                    pgm.getIndicadoresDeNulos().setInddmacol(0);
                    pgm.getIndicadoresDeNulos().setInddmacpo(0);
                    try {
                        pgm.p5520ObtieneDirec();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(NullPointerException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5530AbreCursorDma test conditions:
        sqlcode : [1, 0]
    */
    @Test
    void p5530AbreCursorDmaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(1);
        try {
            pgm.p5530AbreCursorDma();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5530AbreCursorDmaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            try {
                pgm.p5530AbreCursorDma();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p5540LeeCursorDma test conditions:
        inddmaagt : [-1, 0]
        inddmaidr : [-1, 0]
        inddmatdm : [-1, 0]
        inddmacol : [-1, 0]
        inddmacpo : [-1, 0]
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5540LeeCursorDmaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getIndicadoresDeNulos().setInddmaagt(-1);
        pgm.getIndicadoresDeNulos().setInddmaidr(-1);
        pgm.getIndicadoresDeNulos().setInddmatdm(-1);
        pgm.getIndicadoresDeNulos().setInddmacol(-1);
        pgm.getIndicadoresDeNulos().setInddmacpo(-1);
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5540LeeCursorDma();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5540LeeCursorDmaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getIndicadoresDeNulos().setInddmaagt(0);
            pgm.getIndicadoresDeNulos().setInddmaidr(0);
            pgm.getIndicadoresDeNulos().setInddmatdm(0);
            pgm.getIndicadoresDeNulos().setInddmacol(0);
            pgm.getIndicadoresDeNulos().setInddmacpo(0);
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5540LeeCursorDma();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5540LeeCursorDmaTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getIndicadoresDeNulos().setInddmaagt(0);
                pgm.getIndicadoresDeNulos().setInddmaidr(0);
                pgm.getIndicadoresDeNulos().setInddmatdm(0);
                pgm.getIndicadoresDeNulos().setInddmacol(0);
                pgm.getIndicadoresDeNulos().setInddmacpo(0);
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5540LeeCursorDma();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5540LeeCursorDmaTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getIndicadoresDeNulos().setInddmaagt(0);
                    pgm.getIndicadoresDeNulos().setInddmaidr(0);
                    pgm.getIndicadoresDeNulos().setInddmatdm(0);
                    pgm.getIndicadoresDeNulos().setInddmacol(0);
                    pgm.getIndicadoresDeNulos().setInddmacpo(0);
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5540LeeCursorDma();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(NullPointerException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5550CierraCursorDma test conditions:
        sqlcode : [1, 0]
    */
    @Test
    void p5550CierraCursorDmaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(1);
        try {
            pgm.p5550CierraCursorDma();
        } catch (Exception e) {
            assertExceptionInstanceOf(StopRunException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5550CierraCursorDmaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            try {
                pgm.p5550CierraCursorDma();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p5560NombreDeleg test conditions:
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5560NombreDelegTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5560NombreDeleg();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5560NombreDelegTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5560NombreDeleg();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5560NombreDelegTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5560NombreDeleg();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5560NombreDelegTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5560NombreDeleg();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5565Pais test conditions:
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5565PaisTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5565Pais();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5565PaisTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5565Pais();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5565PaisTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5565Pais();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5565PaisTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5565Pais();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5570NombrePob test conditions:
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5570NombrePobTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5570NombrePob();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5570NombrePobTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5570NombrePob();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5570NombrePobTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5570NombrePob();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5570NombrePobTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5570NombrePob();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5580Telefono test conditions:
        indcmades : [-1, 0]
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5580TelefonoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getIndicadoresDeNulos().setIndcmades(-1);
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5580Telefono();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5580TelefonoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getIndicadoresDeNulos().setIndcmades(0);
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5580Telefono();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5580TelefonoTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getIndicadoresDeNulos().setIndcmades(0);
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5580Telefono();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5580TelefonoTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getIndicadoresDeNulos().setIndcmades(0);
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5580Telefono();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p5585NombrePais test conditions:
        sqlcode : [0, 1, 100, 101]
    */
    @Test
    void p5585NombrePaisTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p5585NombrePais();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p5585NombrePaisTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p5585NombrePais();
            } catch (Exception e) {
                assertExceptionInstanceOf(StopRunException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p5585NombrePaisTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(100);
                try {
                    pgm.p5585NombrePais();
                } catch (Exception e) {
                    assertExceptionInstanceOf(NullPointerException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p5585NombrePaisTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(101);
                    try {
                        pgm.p5585NombrePais();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(StopRunException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
}
