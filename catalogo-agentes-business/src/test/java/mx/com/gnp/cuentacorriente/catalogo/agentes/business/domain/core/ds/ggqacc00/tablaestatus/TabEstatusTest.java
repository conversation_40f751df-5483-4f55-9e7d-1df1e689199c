package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablaestatus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TAB-ESTATUS.
 *
 */
class TabEstatusTest {

    private TabEstatus tabEstatus;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tabEstatus = new TabEstatus(1);
        tabEstatus.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TabEstatus tabEstatus2 = new TabEstatus(parent, 1);
        assertNotNull(tabEstatus2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TabEstatus tabEstatus3 = new TabEstatus(parent, sibling, 1);
        assertNotNull(tabEstatus3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tabEstatus.getTbMteCve());

        assertNotNull(tabEstatus.getTbMteEsaCve());

    }

    // property tbMteCve
    @Test
    void testGetterSetterTbMteCve() {

        assertNotNull(tabEstatus.getTbMteCve());

        int vint = 1234;
        tabEstatus.setTbMteCve(vint); // int
        assertEquals(asInt(vint), asInt(tabEstatus.getTbMteCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tabEstatus.setTbMteCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabEstatus.getTbMteCve()));
    }

    // property tbMteEsaCve
    @Test
    void testGetterSetterTbMteEsaCve() {

        assertNotNull(tabEstatus.getTbMteEsaCve());

        int vint = 1234;
        tabEstatus.setTbMteEsaCve(vint); // int
        assertEquals(asInt(vint), asInt(tabEstatus.getTbMteEsaCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tabEstatus.setTbMteEsaCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabEstatus.getTbMteEsaCve()));
    }
}

