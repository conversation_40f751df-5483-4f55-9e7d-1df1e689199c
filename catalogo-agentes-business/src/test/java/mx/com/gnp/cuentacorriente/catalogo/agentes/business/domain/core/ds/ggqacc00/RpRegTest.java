package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-REG.
 *
 */
class RpRegTest {

    private RpReg rpReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpReg = new RpReg();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {






    }

    // property rpLlave
    @Test
    void testGetterSetterRpLlave() {

        assertNotNull(rpReg.getRpLlave());
    }

    // property rpData
    @Test
    void testGetterSetterRpData() {

        assertNotNull(rpReg.getRpData());
    }

    // property rpDataImp
    @Test
    void testGetterSetterRpDataImp() {

        assertNotNull(rpReg.getRpDataImp());
    }

    // property rpDataSat
    @Test
    void testGetterSetterRpDataSat() {

        assertNotNull(rpReg.getRpDataSat());
    }

    // property rpDataAgente
    @Test
    void testGetterSetterRpDataAgente() {

        assertNotNull(rpReg.getRpDataAgente());
    }

    // property rpDataReceptor
    @Test
    void testGetterSetterRpDataReceptor() {

        assertNotNull(rpReg.getRpDataReceptor());
    }

    // property rpAgtIdr
    @Test
    void testGetterSetterRpAgtIdr() {

        // Property: rpLlave.rpAgtIdr -> 10 RP-AGT-IDR 9(09)
        assertNotNull(rpReg.getRpAgtIdr());
        int vint = 123456789;
        rpReg.setRpAgtIdr(vint); // int
    }

    // property rpEmpCve
    @Test
    void testGetterSetterRpEmpCve() {

        // Property: rpLlave.rpEmpCve -> 10 RP-EMP-CVE X(05)
        assertNotNull(rpReg.getRpEmpCve());
        String vString = "ABCab";
        rpReg.setRpEmpCve(vString); // String
    }

    // property rpCnaNum
    @Test
    void testGetterSetterRpCnaNum() {

        // Property: rpLlave.rpCnaNum -> 10 RP-CNA-NUM 9(04)
        assertNotNull(rpReg.getRpCnaNum());
        int vint = 1234;
        rpReg.setRpCnaNum(vint); // int
    }

    // property rpFecPrxAgt
    @Test
    void testGetterSetterRpFecPrxAgt() {

        // Property: rpLlave.rpFecPrxAgt -> 10 RP-FEC-PRX-AGT X(10)
        assertNotNull(rpReg.getRpFecPrxAgt());
        String vString = "ABCabc1230";
        rpReg.setRpFecPrxAgt(vString); // String
    }

    // property rpDsgCve
    @Test
    void testGetterSetterRpDsgCve() {

        // Property: rpData.rpDsgCve -> 10 RP-DSG-CVE 9(04)
        assertNotNull(rpReg.getRpDsgCve());
        int vint = 1234;
        rpReg.setRpDsgCve(vint); // int
    }

    // property rpFecPrxFac
    @Test
    void testGetterSetterRpFecPrxFac() {

        // Property: rpData.rpFecPrxFac -> 10 RP-FEC-PRX-FAC X(10)
        assertNotNull(rpReg.getRpFecPrxFac());
        String vString = "ABCabc1230";
        rpReg.setRpFecPrxFac(vString); // String
    }

    // property rpEsaCve
    @Test
    void testGetterSetterRpEsaCve() {

        // Property: rpData.rpEsaCve -> 10 RP-ESA-CVE 9(04)
        assertNotNull(rpReg.getRpEsaCve());
        int vint = 1234;
        rpReg.setRpEsaCve(vint); // int
    }

    // property rpEssPrx
    @Test
    void testGetterSetterRpEssPrx() {

        // Property: rpData.rpEssPrx -> 10 RP-ESS-PRX X(01)
        assertNotNull(rpReg.getRpEssPrx());
        String vString = "A";
        rpReg.setRpEssPrx(vString); // String
    }

    // property rpCf5CfbCveDseCmp
    @Test
    void testGetterSetterRpCf5CfbCveDseCmp() {

        // Property: rpData.rpCf5CfbCveDseCmp -> 10 RP-CF5-CFB-CVE-DSE-CMP X(04)
        assertNotNull(rpReg.getRpCf5CfbCveDseCmp());
        String vString = "ABCa";
        rpReg.setRpCf5CfbCveDseCmp(vString); // String
    }

    // property rpCf5ImpCtoDse
    @Test
    void testGetterSetterRpCf5ImpCtoDse() {

        // Property: rpData.rpCf5ImpCtoDse -> 10 RP-CF5-IMP-CTO-DSE S9(11)V9(2)
        assertNotNull(rpReg.getRpCf5ImpCtoDse());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpCf5ImpCtoDse(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpCf5ImpCtoDse(vINumericValue); // INumericValue
    }

    // property rpCf5PjeCtoDse
    @Test
    void testGetterSetterRpCf5PjeCtoDse() {

        // Property: rpData.rpCf5PjeCtoDse -> 10 RP-CF5-PJE-CTO-DSE S9(03)V9(2)
        assertNotNull(rpReg.getRpCf5PjeCtoDse());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        rpReg.setRpCf5PjeCtoDse(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        rpReg.setRpCf5PjeCtoDse(vINumericValue); // INumericValue
    }

    // property rpCf1ImpSbtFct
    @Test
    void testGetterSetterRpCf1ImpSbtFct() {

        // Property: rpDataImp.rpCf1ImpSbtFct -> 10 RP-CF1-IMP-SBT-FCT S9(11)V9(2)
        assertNotNull(rpReg.getRpCf1ImpSbtFct());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpCf1ImpSbtFct(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpCf1ImpSbtFct(vINumericValue); // INumericValue
    }

    // property rpCf1ImpTotFct
    @Test
    void testGetterSetterRpCf1ImpTotFct() {

        // Property: rpDataImp.rpCf1ImpTotFct -> 10 RP-CF1-IMP-TOT-FCT S9(11)V9(2)
        assertNotNull(rpReg.getRpCf1ImpTotFct());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpCf1ImpTotFct(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpCf1ImpTotFct(vINumericValue); // INumericValue
    }

    // property rpFolioFiscal
    @Test
    void testGetterSetterRpFolioFiscal() {

        // Property: rpDataSat.rpFolioFiscal -> 10 RP-FOLIO-FISCAL X(20)
        assertNotNull(rpReg.getRpFolioFiscal());
        String vString = "ABCabc1230";
        rpReg.setRpFolioFiscal(vString); // String
    }

    // property rpPercepGrav
    @Test
    void testGetterSetterRpPercepGrav() {

        // Property: rpDataSat.rpPercepGrav -> 10 RP-PERCEP-GRAV S9(11)V9(02)
        assertNotNull(rpReg.getRpPercepGrav());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpPercepGrav(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpPercepGrav(vINumericValue); // INumericValue
    }

    // property rpPercepExen
    @Test
    void testGetterSetterRpPercepExen() {

        // Property: rpDataSat.rpPercepExen -> 10 RP-PERCEP-EXEN S9(11)V9(02)
        assertNotNull(rpReg.getRpPercepExen());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpPercepExen(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpPercepExen(vINumericValue); // INumericValue
    }

    // property rpIva
    @Test
    void testGetterSetterRpIva() {

        // Property: rpDataSat.rpIva -> 10 RP-IVA S9(11)V9(02)
        assertNotNull(rpReg.getRpIva());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpIva(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpIva(vINumericValue); // INumericValue
    }

    // property rpIsr
    @Test
    void testGetterSetterRpIsr() {

        // Property: rpDataSat.rpIsr -> 10 RP-ISR S9(11)V9(02)
        assertNotNull(rpReg.getRpIsr());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpIsr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpIsr(vINumericValue); // INumericValue
    }

    // property rpIvaRtn
    @Test
    void testGetterSetterRpIvaRtn() {

        // Property: rpDataSat.rpIvaRtn -> 10 RP-IVA-RTN S9(11)V9(02)
        assertNotNull(rpReg.getRpIvaRtn());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpIvaRtn(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpIvaRtn(vINumericValue); // INumericValue
    }

    // property rpIvaAcr
    @Test
    void testGetterSetterRpIvaAcr() {

        // Property: rpDataSat.rpIvaAcr -> 10 RP-IVA-ACR S9(11)V9(02)
        assertNotNull(rpReg.getRpIvaAcr());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpIvaAcr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpIvaAcr(vINumericValue); // INumericValue
    }

    // property rpIsrRet
    @Test
    void testGetterSetterRpIsrRet() {

        // Property: rpDataSat.rpIsrRet -> 10 RP-ISR-RET S9(11)V9(02)
        assertNotNull(rpReg.getRpIsrRet());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpIsrRet(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpIsrRet(vINumericValue); // INumericValue
    }

    // property rpIpuRtnCed
    @Test
    void testGetterSetterRpIpuRtnCed() {

        // Property: rpDataSat.rpIpuRtnCed -> 10 RP-IPU-RTN-CED S9(11)V9(02)
        assertNotNull(rpReg.getRpIpuRtnCed());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpReg.setRpIpuRtnCed(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpReg.setRpIpuRtnCed(vINumericValue); // INumericValue
    }

    // property rpTasaIva
    @Test
    void testGetterSetterRpTasaIva() {

        // Property: rpDataSat.rpTasaIva -> 10 RP-TASA-IVA S9(03)V9(02)
        assertNotNull(rpReg.getRpTasaIva());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        rpReg.setRpTasaIva(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        rpReg.setRpTasaIva(vINumericValue); // INumericValue
    }

    // property rpTasaCed
    @Test
    void testGetterSetterRpTasaCed() {

        // Property: rpDataSat.rpTasaCed -> 10 RP-TASA-CED S9(03)V9(02)
        assertNotNull(rpReg.getRpTasaCed());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        rpReg.setRpTasaCed(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        rpReg.setRpTasaCed(vINumericValue); // INumericValue
    }

    // property rpAgtRfc
    @Test
    void testGetterSetterRpAgtRfc() {

        // Property: rpDataAgente.rpAgtRfc -> 10 RP-AGT-RFC X(13)
        assertNotNull(rpReg.getRpAgtRfc());
        String vString = "ABCabc1230";
        rpReg.setRpAgtRfc(vString); // String
    }

    // property rpAgtNom
    @Test
    void testGetterSetterRpAgtNom() {

        // Property: rpDataAgente.rpAgtNom -> 10 RP-AGT-NOM X(40)
        assertNotNull(rpReg.getRpAgtNom());
        String vString = "ABCabc1230";
        rpReg.setRpAgtNom(vString); // String
    }

    // property rpAgtApePat
    @Test
    void testGetterSetterRpAgtApePat() {

        // Property: rpDataAgente.rpAgtApePat -> 10 RP-AGT-APE-PAT X(40)
        assertNotNull(rpReg.getRpAgtApePat());
        String vString = "ABCabc1230";
        rpReg.setRpAgtApePat(vString); // String
    }

    // property rpAgtApeMat
    @Test
    void testGetterSetterRpAgtApeMat() {

        // Property: rpDataAgente.rpAgtApeMat -> 10 RP-AGT-APE-MAT X(30)
        assertNotNull(rpReg.getRpAgtApeMat());
        String vString = "ABCabc1230";
        rpReg.setRpAgtApeMat(vString); // String
    }

    // property rpAgtRazSoc
    @Test
    void testGetterSetterRpAgtRazSoc() {

        // Property: rpDataAgente.rpAgtRazSoc -> 10 RP-AGT-RAZ-SOC X(60)
        assertNotNull(rpReg.getRpAgtRazSoc());
        String vString = "ABCabc1230";
        rpReg.setRpAgtRazSoc(vString); // String
    }

    // property rpAgtNomConcatenado
    @Test
    void testGetterSetterRpAgtNomConcatenado() {

        // Property: rpDataAgente.rpAgtNomConcatenado -> 10 RP-AGT-NOM-CONCATENADO X(100)
        assertNotNull(rpReg.getRpAgtNomConcatenado());
        String vString = "ABCabc1230";
        rpReg.setRpAgtNomConcatenado(vString); // String
    }

    // property rpPraAgtCxxCurp
    @Test
    void testGetterSetterRpPraAgtCxxCurp() {

        // Property: rpDataAgente.rpPraAgtCxxCurp -> 10 RP-PRA-AGT-CXX-CURP X(18)
        assertNotNull(rpReg.getRpPraAgtCxxCurp());
        String vString = "ABCabc1230";
        rpReg.setRpPraAgtCxxCurp(vString); // String
    }

    // property rpAgtNalCve
    @Test
    void testGetterSetterRpAgtNalCve() {

        // Property: rpDataAgente.rpAgtNalCve -> 10 RP-AGT-NAL-CVE X(03)
        assertNotNull(rpReg.getRpAgtNalCve());
        String vString = "ABC";
        rpReg.setRpAgtNalCve(vString); // String
    }

    // property rpMteCve
    @Test
    void testGetterSetterRpMteCve() {

        // Property: rpDataAgente.rpMteCve -> 10 RP-MTE-CVE 9(04)
        assertNotNull(rpReg.getRpMteCve());
        int vint = 1234;
        rpReg.setRpMteCve(vint); // int
    }

    // property rpPefCve
    @Test
    void testGetterSetterRpPefCve() {

        // Property: rpDataAgente.rpPefCve -> 10 RP-PEF-CVE 9(04)
        assertNotNull(rpReg.getRpPefCve());
        int vint = 1234;
        rpReg.setRpPefCve(vint); // int
    }

    // property rpDmaCae
    @Test
    void testGetterSetterRpDmaCae() {

        // Property: rpDataAgente.rpDmaCae -> 10 RP-DMA-CAE X(40)
        assertNotNull(rpReg.getRpDmaCae());
        String vString = "ABCabc1230";
        rpReg.setRpDmaCae(vString); // String
    }

    // property rpDmaNum
    @Test
    void testGetterSetterRpDmaNum() {

        // Property: rpDataAgente.rpDmaNum -> 10 RP-DMA-NUM X(05)
        assertNotNull(rpReg.getRpDmaNum());
        String vString = "ABCab";
        rpReg.setRpDmaNum(vString); // String
    }

    // property rpDmaInt
    @Test
    void testGetterSetterRpDmaInt() {

        // Property: rpDataAgente.rpDmaInt -> 10 RP-DMA-INT X(05)
        assertNotNull(rpReg.getRpDmaInt());
        String vString = "ABCab";
        rpReg.setRpDmaInt(vString); // String
    }

    // property rpDmaCol
    @Test
    void testGetterSetterRpDmaCol() {

        // Property: rpDataAgente.rpDmaCol -> 10 RP-DMA-COL X(40)
        assertNotNull(rpReg.getRpDmaCol());
        String vString = "ABCabc1230";
        rpReg.setRpDmaCol(vString); // String
    }

    // property rpMndNom
    @Test
    void testGetterSetterRpMndNom() {

        // Property: rpDataAgente.rpMndNom -> 10 RP-MND-NOM X(50)
        assertNotNull(rpReg.getRpMndNom());
        String vString = "ABCabc1230";
        rpReg.setRpMndNom(vString); // String
    }

    // property rpDmaCpoCve
    @Test
    void testGetterSetterRpDmaCpoCve() {

        // Property: rpDataAgente.rpDmaCpoCve -> 10 RP-DMA-CPO-CVE 9(05)
        assertNotNull(rpReg.getRpDmaCpoCve());
        int vint = 12345;
        rpReg.setRpDmaCpoCve(vint); // int
    }

    // property rpPobNom
    @Test
    void testGetterSetterRpPobNom() {

        // Property: rpDataAgente.rpPobNom -> 10 RP-POB-NOM X(40)
        assertNotNull(rpReg.getRpPobNom());
        String vString = "ABCabc1230";
        rpReg.setRpPobNom(vString); // String
    }

    // property rpMndEdoCve
    @Test
    void testGetterSetterRpMndEdoCve() {

        // Property: rpDataAgente.rpMndEdoCve -> 10 RP-MND-EDO-CVE X(04)
        assertNotNull(rpReg.getRpMndEdoCve());
        String vString = "ABCa";
        rpReg.setRpMndEdoCve(vString); // String
    }

    // property rpEdoEdoNom
    @Test
    void testGetterSetterRpEdoEdoNom() {

        // Property: rpDataAgente.rpEdoEdoNom -> 10 RP-EDO-EDO-NOM X(30)
        assertNotNull(rpReg.getRpEdoEdoNom());
        String vString = "ABCabc1230";
        rpReg.setRpEdoEdoNom(vString); // String
    }

    // property rpEdoPaiCve
    @Test
    void testGetterSetterRpEdoPaiCve() {

        // Property: rpDataAgente.rpEdoPaiCve -> 10 RP-EDO-PAI-CVE X(03)
        assertNotNull(rpReg.getRpEdoPaiCve());
        String vString = "ABC";
        rpReg.setRpEdoPaiCve(vString); // String
    }

    // property rpCmaDesTelefon
    @Test
    void testGetterSetterRpCmaDesTelefon() {

        // Property: rpDataAgente.rpCmaDesTelefon -> 10 RP-CMA-DES-TELEFON X(60)
        assertNotNull(rpReg.getRpCmaDesTelefon());
        String vString = "ABCabc1230";
        rpReg.setRpCmaDesTelefon(vString); // String
    }

    // property rpCnaGerZonEnv
    @Test
    void testGetterSetterRpCnaGerZonEnv() {

        // Property: rpDataAgente.rpCnaGerZonEnv -> 10 RP-CNA-GER-ZON-ENV 9(04)
        assertNotNull(rpReg.getRpCnaGerZonEnv());
        int vint = 1234;
        rpReg.setRpCnaGerZonEnv(vint); // int
    }

    // property rpCnaOfnCve
    @Test
    void testGetterSetterRpCnaOfnCve() {

        // Property: rpDataAgente.rpCnaOfnCve -> 10 RP-CNA-OFN-CVE 9(04)
        assertNotNull(rpReg.getRpCnaOfnCve());
        int vint = 1234;
        rpReg.setRpCnaOfnCve(vint); // int
    }

    // property rpRecCae
    @Test
    void testGetterSetterRpRecCae() {

        // Property: rpDataReceptor.rpRecCae -> 10 RP-REC-CAE X(40)
        assertNotNull(rpReg.getRpRecCae());
        String vString = "ABCabc1230";
        rpReg.setRpRecCae(vString); // String
    }

    // property rpRecNum
    @Test
    void testGetterSetterRpRecNum() {

        // Property: rpDataReceptor.rpRecNum -> 10 RP-REC-NUM X(05)
        assertNotNull(rpReg.getRpRecNum());
        String vString = "ABCab";
        rpReg.setRpRecNum(vString); // String
    }

    // property rpRecInt
    @Test
    void testGetterSetterRpRecInt() {

        // Property: rpDataReceptor.rpRecInt -> 10 RP-REC-INT X(05)
        assertNotNull(rpReg.getRpRecInt());
        String vString = "ABCab";
        rpReg.setRpRecInt(vString); // String
    }

    // property rpRecCol
    @Test
    void testGetterSetterRpRecCol() {

        // Property: rpDataReceptor.rpRecCol -> 10 RP-REC-COL X(40)
        assertNotNull(rpReg.getRpRecCol());
        String vString = "ABCabc1230";
        rpReg.setRpRecCol(vString); // String
    }

    // property rpRecMndNom
    @Test
    void testGetterSetterRpRecMndNom() {

        // Property: rpDataReceptor.rpRecMndNom -> 10 RP-REC-MND-NOM X(50)
        assertNotNull(rpReg.getRpRecMndNom());
        String vString = "ABCabc1230";
        rpReg.setRpRecMndNom(vString); // String
    }

    // property rpRecPobNom
    @Test
    void testGetterSetterRpRecPobNom() {

        // Property: rpDataReceptor.rpRecPobNom -> 10 RP-REC-POB-NOM X(40)
        assertNotNull(rpReg.getRpRecPobNom());
        String vString = "ABCabc1230";
        rpReg.setRpRecPobNom(vString); // String
    }

    // property rpRecMndEdoCve
    @Test
    void testGetterSetterRpRecMndEdoCve() {

        // Property: rpDataReceptor.rpRecMndEdoCve -> 10 RP-REC-MND-EDO-CVE X(04)
        assertNotNull(rpReg.getRpRecMndEdoCve());
        String vString = "ABCa";
        rpReg.setRpRecMndEdoCve(vString); // String
    }

    // property rpRecMndEdoNom
    @Test
    void testGetterSetterRpRecMndEdoNom() {

        // Property: rpDataReceptor.rpRecMndEdoNom -> 10 RP-REC-MND-EDO-NOM X(30)
        assertNotNull(rpReg.getRpRecMndEdoNom());
        String vString = "ABCabc1230";
        rpReg.setRpRecMndEdoNom(vString); // String
    }

    // property rpRecMndPaiCve
    @Test
    void testGetterSetterRpRecMndPaiCve() {

        // Property: rpDataReceptor.rpRecMndPaiCve -> 10 RP-REC-MND-PAI-CVE X(03)
        assertNotNull(rpReg.getRpRecMndPaiCve());
        String vString = "ABC";
        rpReg.setRpRecMndPaiCve(vString); // String
    }

    // property rpRecMndPaiNom
    @Test
    void testGetterSetterRpRecMndPaiNom() {

        // Property: rpDataReceptor.rpRecMndPaiNom -> 10 RP-REC-MND-PAI-NOM X(30)
        assertNotNull(rpReg.getRpRecMndPaiNom());
        String vString = "ABCabc1230";
        rpReg.setRpRecMndPaiNom(vString); // String
    }

    // property rpRecCpoCve
    @Test
    void testGetterSetterRpRecCpoCve() {

        // Property: rpDataReceptor.rpRecCpoCve -> 10 RP-REC-CPO-CVE 9(05)
        assertNotNull(rpReg.getRpRecCpoCve());
        int vint = 12345;
        rpReg.setRpRecCpoCve(vint); // int
    }

    // property rpRecGerZonEnv
    @Test
    void testGetterSetterRpRecGerZonEnv() {

        // Property: rpDataReceptor.rpRecGerZonEnv -> 10 RP-REC-GER-ZON-ENV 9(04)
        assertNotNull(rpReg.getRpRecGerZonEnv());
        int vint = 1234;
        rpReg.setRpRecGerZonEnv(vint); // int
    }
}

