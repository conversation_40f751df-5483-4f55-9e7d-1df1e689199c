package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-CAT-ESA.
 *
 */
class TablaCatEsaTest {

    private TablaCatEsa tablaCatEsa;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaCatEsa = new TablaCatEsa();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tabCatEsa
    @Test
    void testGetterSetterTabCatEsa() {

        assertNotNull(tablaCatEsa.getTabCatEsa());
        assertNotNull(tablaCatEsa.getTabCatEsa(1));
    }

    // property tbEsaDes
    @Test
    void testGetterSetterTbEsaDes() {

        // Property: tabCatEsa.tbEsaDes -> 07 TB-ESA-DES X(10)
        assertNotNull(tablaCatEsa.getTbEsaDes());
        String vString = "ABCabc1230";
        tablaCatEsa.setTbEsaDes(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaCatEsa.setTbEsaDes(vIAlphanumericValue); // IAlphanumericValue
    }
}

