package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTPRA0-NULL.
 *
 */
class WsGfvtpra0NullTest {

    private WsGfvtpra0Null wsGfvtpra0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtpra0Null = new WsGfvtpra0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtpra0Null.setPra01(0);
        assertEquals(0, wsGfvtpra0Null.getPra01());

        wsGfvtpra0Null.setPra02(0);
        assertEquals(0, wsGfvtpra0Null.getPra02());

        wsGfvtpra0Null.setPra03(0);
        assertEquals(0, wsGfvtpra0Null.getPra03());

        wsGfvtpra0Null.setPra04(0);
        assertEquals(0, wsGfvtpra0Null.getPra04());

        wsGfvtpra0Null.setPra05(0);
        assertEquals(0, wsGfvtpra0Null.getPra05());

        wsGfvtpra0Null.setPra06(0);
        assertEquals(0, wsGfvtpra0Null.getPra06());

        wsGfvtpra0Null.setPra07(0);
        assertEquals(0, wsGfvtpra0Null.getPra07());

        wsGfvtpra0Null.setPra08(0);
        assertEquals(0, wsGfvtpra0Null.getPra08());

        wsGfvtpra0Null.setPra09(0);
        assertEquals(0, wsGfvtpra0Null.getPra09());

        wsGfvtpra0Null.setPra10(0);
        assertEquals(0, wsGfvtpra0Null.getPra10());

        wsGfvtpra0Null.setPra11(0);
        assertEquals(0, wsGfvtpra0Null.getPra11());

        wsGfvtpra0Null.setPra12(0);
        assertEquals(0, wsGfvtpra0Null.getPra12());

        wsGfvtpra0Null.setPra13(0);
        assertEquals(0, wsGfvtpra0Null.getPra13());

        wsGfvtpra0Null.setPra14(0);
        assertEquals(0, wsGfvtpra0Null.getPra14());

        wsGfvtpra0Null.setDma01(0);
        assertEquals(0, wsGfvtpra0Null.getDma01());

        wsGfvtpra0Null.setDma02(0);
        assertEquals(0, wsGfvtpra0Null.getDma02());

        wsGfvtpra0Null.setDma03(0);
        assertEquals(0, wsGfvtpra0Null.getDma03());

        wsGfvtpra0Null.setDma04(0);
        assertEquals(0, wsGfvtpra0Null.getDma04());

        wsGfvtpra0Null.setDma05(0);
        assertEquals(0, wsGfvtpra0Null.getDma05());

        wsGfvtpra0Null.setDma06(0);
        assertEquals(0, wsGfvtpra0Null.getDma06());

        wsGfvtpra0Null.setDma07(0);
        assertEquals(0, wsGfvtpra0Null.getDma07());

        wsGfvtpra0Null.setDma08(0);
        assertEquals(0, wsGfvtpra0Null.getDma08());

        wsGfvtpra0Null.setDma09(0);
        assertEquals(0, wsGfvtpra0Null.getDma09());

        wsGfvtpra0Null.setDma10(0);
        assertEquals(0, wsGfvtpra0Null.getDma10());

        wsGfvtpra0Null.setDma11(0);
        assertEquals(0, wsGfvtpra0Null.getDma11());

    }

    // property pra01
    @Test
    void testGetterSetterPra01() {

        assertNotNull(wsGfvtpra0Null.getPra01());

        int vint = 1234;
        wsGfvtpra0Null.setPra01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra01()));
    }

    // property pra02
    @Test
    void testGetterSetterPra02() {

        assertNotNull(wsGfvtpra0Null.getPra02());

        int vint = 1234;
        wsGfvtpra0Null.setPra02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra02()));
    }

    // property pra03
    @Test
    void testGetterSetterPra03() {

        assertNotNull(wsGfvtpra0Null.getPra03());

        int vint = 1234;
        wsGfvtpra0Null.setPra03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra03()));
    }

    // property pra04
    @Test
    void testGetterSetterPra04() {

        assertNotNull(wsGfvtpra0Null.getPra04());

        int vint = 1234;
        wsGfvtpra0Null.setPra04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra04()));
    }

    // property pra05
    @Test
    void testGetterSetterPra05() {

        assertNotNull(wsGfvtpra0Null.getPra05());

        int vint = 1234;
        wsGfvtpra0Null.setPra05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra05()));
    }

    // property pra06
    @Test
    void testGetterSetterPra06() {

        assertNotNull(wsGfvtpra0Null.getPra06());

        int vint = 1234;
        wsGfvtpra0Null.setPra06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra06()));
    }

    // property pra07
    @Test
    void testGetterSetterPra07() {

        assertNotNull(wsGfvtpra0Null.getPra07());

        int vint = 1234;
        wsGfvtpra0Null.setPra07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra07()));
    }

    // property pra08
    @Test
    void testGetterSetterPra08() {

        assertNotNull(wsGfvtpra0Null.getPra08());

        int vint = 1234;
        wsGfvtpra0Null.setPra08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra08()));
    }

    // property pra09
    @Test
    void testGetterSetterPra09() {

        assertNotNull(wsGfvtpra0Null.getPra09());

        int vint = 1234;
        wsGfvtpra0Null.setPra09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra09()));
    }

    // property pra10
    @Test
    void testGetterSetterPra10() {

        assertNotNull(wsGfvtpra0Null.getPra10());

        int vint = 1234;
        wsGfvtpra0Null.setPra10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra10()));
    }

    // property pra11
    @Test
    void testGetterSetterPra11() {

        assertNotNull(wsGfvtpra0Null.getPra11());

        int vint = 1234;
        wsGfvtpra0Null.setPra11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra11()));
    }

    // property pra12
    @Test
    void testGetterSetterPra12() {

        assertNotNull(wsGfvtpra0Null.getPra12());

        int vint = 1234;
        wsGfvtpra0Null.setPra12(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra12()));
    }

    // property pra13
    @Test
    void testGetterSetterPra13() {

        assertNotNull(wsGfvtpra0Null.getPra13());

        int vint = 1234;
        wsGfvtpra0Null.setPra13(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra13()));
    }

    // property pra14
    @Test
    void testGetterSetterPra14() {

        assertNotNull(wsGfvtpra0Null.getPra14());

        int vint = 1234;
        wsGfvtpra0Null.setPra14(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getPra14()));
    }

    // property dma01
    @Test
    void testGetterSetterDma01() {

        assertNotNull(wsGfvtpra0Null.getDma01());

        int vint = 1234;
        wsGfvtpra0Null.setDma01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma01()));
    }

    // property dma02
    @Test
    void testGetterSetterDma02() {

        assertNotNull(wsGfvtpra0Null.getDma02());

        int vint = 1234;
        wsGfvtpra0Null.setDma02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma02()));
    }

    // property dma03
    @Test
    void testGetterSetterDma03() {

        assertNotNull(wsGfvtpra0Null.getDma03());

        int vint = 1234;
        wsGfvtpra0Null.setDma03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma03()));
    }

    // property dma04
    @Test
    void testGetterSetterDma04() {

        assertNotNull(wsGfvtpra0Null.getDma04());

        int vint = 1234;
        wsGfvtpra0Null.setDma04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma04()));
    }

    // property dma05
    @Test
    void testGetterSetterDma05() {

        assertNotNull(wsGfvtpra0Null.getDma05());

        int vint = 1234;
        wsGfvtpra0Null.setDma05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma05()));
    }

    // property dma06
    @Test
    void testGetterSetterDma06() {

        assertNotNull(wsGfvtpra0Null.getDma06());

        int vint = 1234;
        wsGfvtpra0Null.setDma06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma06()));
    }

    // property dma07
    @Test
    void testGetterSetterDma07() {

        assertNotNull(wsGfvtpra0Null.getDma07());

        int vint = 1234;
        wsGfvtpra0Null.setDma07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma07()));
    }

    // property dma08
    @Test
    void testGetterSetterDma08() {

        assertNotNull(wsGfvtpra0Null.getDma08());

        int vint = 1234;
        wsGfvtpra0Null.setDma08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma08()));
    }

    // property dma09
    @Test
    void testGetterSetterDma09() {

        assertNotNull(wsGfvtpra0Null.getDma09());

        int vint = 1234;
        wsGfvtpra0Null.setDma09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma09()));
    }

    // property dma10
    @Test
    void testGetterSetterDma10() {

        assertNotNull(wsGfvtpra0Null.getDma10());

        int vint = 1234;
        wsGfvtpra0Null.setDma10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma10()));
    }

    // property dma11
    @Test
    void testGetterSetterDma11() {

        assertNotNull(wsGfvtpra0Null.getDma11());

        int vint = 1234;
        wsGfvtpra0Null.setDma11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtpra0Null.getDma11()));
    }
}

