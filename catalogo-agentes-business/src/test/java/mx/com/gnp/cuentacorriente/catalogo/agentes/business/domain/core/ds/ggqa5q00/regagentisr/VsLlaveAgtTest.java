package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.regagentisr;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: VS-LLAVE-AGT.
 *
 */
class VsLlaveAgtTest {

    private VsLlaveAgt vsLlaveAgt;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        vsLlaveAgt = new VsLlaveAgt();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        VsLlaveAgt vsLlaveAgt2 = new VsLlaveAgt(parent);
        assertNotNull(vsLlaveAgt2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 7);
        VsLlaveAgt vsLlaveAgt3 = new VsLlaveAgt(parent, sibling);
        assertNotNull(vsLlaveAgt3);
    }

    @Test
    void testInitialize() {
        assertNotNull(vsLlaveAgt.getVsAgente());

    }

    // property vsAgente
    @Test
    void testGetterSetterVsAgente() {

        assertNotNull(vsLlaveAgt.getVsAgente());

        String vString = "ABCabc1";
        vsLlaveAgt.setVsAgente(vString); // String
        assertEquals(asStr(vString), asStr(vsLlaveAgt.getVsAgente()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        vsLlaveAgt.setVsAgente(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(vsLlaveAgt.getVsAgente()));
    }
}

