package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGFVTESA0.
 *
 */
class WsDclgfvtesa0Test {

    private WsDclgfvtesa0 wsDclgfvtesa0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgfvtesa0 = new WsDclgfvtesa0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgfvtesa0.setEsaDes("");
        assertEquals("", wsDclgfvtesa0.getEsaDes());

    }

    // property esaDes
    @Test
    void testGetterSetterEsaDes() {

        assertNotNull(wsDclgfvtesa0.getEsaDes());

        String vString = "ABCabc1230";
        wsDclgfvtesa0.setEsaDes(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtesa0.getEsaDes()));
    }
}

