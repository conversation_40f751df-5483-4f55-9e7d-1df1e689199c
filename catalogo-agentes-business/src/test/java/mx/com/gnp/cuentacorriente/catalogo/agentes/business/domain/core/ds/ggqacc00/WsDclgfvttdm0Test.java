package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGFVTTDM0.
 *
 */
class WsDclgfvttdm0Test {

    private WsDclgfvttdm0 wsDclgfvttdm0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgfvttdm0 = new WsDclgfvttdm0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgfvttdm0.setTdmTdmCve(0);
        assertEquals(0, wsDclgfvttdm0.getTdmTdmCve());

        wsDclgfvttdm0.setTdmTdmDes("");
        assertEquals("", wsDclgfvttdm0.getTdmTdmDes());

        wsDclgfvttdm0.setTdmFecUltAct("");
        assertEquals("", wsDclgfvttdm0.getTdmFecUltAct());

        wsDclgfvttdm0.setTdmUsuCveAct("");
        assertEquals("", wsDclgfvttdm0.getTdmUsuCveAct());

    }

    // property tdmTdmCve
    @Test
    void testGetterSetterTdmTdmCve() {

        assertNotNull(wsDclgfvttdm0.getTdmTdmCve());

        int vint = 1234;
        wsDclgfvttdm0.setTdmTdmCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvttdm0.getTdmTdmCve()));
    }

    // property tdmTdmDes
    @Test
    void testGetterSetterTdmTdmDes() {

        assertNotNull(wsDclgfvttdm0.getTdmTdmDes());

        String vString = "ABCabc1230";
        wsDclgfvttdm0.setTdmTdmDes(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvttdm0.getTdmTdmDes()));
    }

    // property tdmFecUltAct
    @Test
    void testGetterSetterTdmFecUltAct() {

        assertNotNull(wsDclgfvttdm0.getTdmFecUltAct());

        String vString = "ABCabc1230";
        wsDclgfvttdm0.setTdmFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvttdm0.getTdmFecUltAct()));
    }

    // property tdmUsuCveAct
    @Test
    void testGetterSetterTdmUsuCveAct() {

        assertNotNull(wsDclgfvttdm0.getTdmUsuCveAct());

        String vString = "ABCabc12";
        wsDclgfvttdm0.setTdmUsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvttdm0.getTdmUsuCveAct()));
    }
}

