package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-ACUMULADORES.
 *
 */
class WsAcumuladoresTest {

    private WsAcumuladores wsAcumuladores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAcumuladores = new WsAcumuladores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAcumuladores.getWsImporte());

        assertNotNull(wsAcumuladores.getWsTotImporte());

        assertNotNull(wsAcumuladores.getWsTotComision());

        assertNotNull(wsAcumuladores.getWsImcomis());

        assertNotNull(wsAcumuladores.getWsImpDssIpu());

        assertNotNull(wsAcumuladores.getWsImpAncIpu());

        assertNotNull(wsAcumuladores.getWsDiferencia());

        assertNotNull(wsAcumuladores.getWsTotalDiferencia());

        assertNotNull(wsAcumuladores.getWsCf5ImpCtoDse());

    }

    // property wsImporte
    @Test
    void testGetterSetterWsImporte() {

        assertNotNull(wsAcumuladores.getWsImporte());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        wsAcumuladores.setWsImporte(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsImporte().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsImporte(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsImporte()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        wsAcumuladores.setWsImporte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsImporte()));
    }

    // property wsTotImporte
    @Test
    void testGetterSetterWsTotImporte() {

        assertNotNull(wsAcumuladores.getWsTotImporte());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        wsAcumuladores.setWsTotImporte(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsTotImporte().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsTotImporte(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsTotImporte()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        wsAcumuladores.setWsTotImporte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsTotImporte()));
    }

    // property wsTotComision
    @Test
    void testGetterSetterWsTotComision() {

        assertNotNull(wsAcumuladores.getWsTotComision());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        wsAcumuladores.setWsTotComision(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsTotComision().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsTotComision(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsTotComision()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        wsAcumuladores.setWsTotComision(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsTotComision()));
    }

    // property wsImcomis
    @Test
    void testGetterSetterWsImcomis() {

        assertNotNull(wsAcumuladores.getWsImcomis());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsAcumuladores.setWsImcomis(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsImcomis().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsImcomis(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsImcomis()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsAcumuladores.setWsImcomis(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsImcomis()));
    }

    // property wsImpDssIpu
    @Test
    void testGetterSetterWsImpDssIpu() {

        assertNotNull(wsAcumuladores.getWsImpDssIpu());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsAcumuladores.setWsImpDssIpu(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsImpDssIpu().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsImpDssIpu(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsImpDssIpu()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsAcumuladores.setWsImpDssIpu(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsImpDssIpu()));
    }

    // property wsImpAncIpu
    @Test
    void testGetterSetterWsImpAncIpu() {

        assertNotNull(wsAcumuladores.getWsImpAncIpu());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsAcumuladores.setWsImpAncIpu(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsImpAncIpu().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsImpAncIpu(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsImpAncIpu()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsAcumuladores.setWsImpAncIpu(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsImpAncIpu()));
    }

    // property wsDiferencia
    @Test
    void testGetterSetterWsDiferencia() {

        assertNotNull(wsAcumuladores.getWsDiferencia());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        wsAcumuladores.setWsDiferencia(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsDiferencia().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsDiferencia(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsDiferencia()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        wsAcumuladores.setWsDiferencia(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsDiferencia()));
    }

    // property wsTotalDiferencia
    @Test
    void testGetterSetterWsTotalDiferencia() {

        assertNotNull(wsAcumuladores.getWsTotalDiferencia());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        wsAcumuladores.setWsTotalDiferencia(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsTotalDiferencia().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsTotalDiferencia(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsTotalDiferencia()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        wsAcumuladores.setWsTotalDiferencia(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsTotalDiferencia()));
    }

    // property wsCf5ImpCtoDse
    @Test
    void testGetterSetterWsCf5ImpCtoDse() {

        assertNotNull(wsAcumuladores.getWsCf5ImpCtoDse());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        wsAcumuladores.setWsCf5ImpCtoDse(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAcumuladores.getWsCf5ImpCtoDse().toBigDecimal());

        int vint = 123456789;
        wsAcumuladores.setWsCf5ImpCtoDse(vint); // int
        assertEquals(asInt(vint), asInt(wsAcumuladores.getWsCf5ImpCtoDse()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        wsAcumuladores.setWsCf5ImpCtoDse(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAcumuladores.getWsCf5ImpCtoDse()));
    }
}

