package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GCCTORV0-NULL.
 *
 */
class WsGcctorv0NullTest {

    private WsGcctorv0Null wsGcctorv0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGcctorv0Null = new WsGcctorv0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGcctorv0Null.setOrv01(0);
        assertEquals(0, wsGcctorv0Null.getOrv01());

        wsGcctorv0Null.setOrv02(0);
        assertEquals(0, wsGcctorv0Null.getOrv02());

        wsGcctorv0Null.setOrv03(0);
        assertEquals(0, wsGcctorv0Null.getOrv03());

        wsGcctorv0Null.setOrv04(0);
        assertEquals(0, wsGcctorv0Null.getOrv04());

        wsGcctorv0Null.setOrv05(0);
        assertEquals(0, wsGcctorv0Null.getOrv05());

        wsGcctorv0Null.setOrv06(0);
        assertEquals(0, wsGcctorv0Null.getOrv06());

        wsGcctorv0Null.setOrv07(0);
        assertEquals(0, wsGcctorv0Null.getOrv07());

        wsGcctorv0Null.setOrv08(0);
        assertEquals(0, wsGcctorv0Null.getOrv08());

        wsGcctorv0Null.setOrv09(0);
        assertEquals(0, wsGcctorv0Null.getOrv09());

    }

    // property orv01
    @Test
    void testGetterSetterOrv01() {

        assertNotNull(wsGcctorv0Null.getOrv01());

        int vint = 1234;
        wsGcctorv0Null.setOrv01(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv01()));
    }

    // property orv02
    @Test
    void testGetterSetterOrv02() {

        assertNotNull(wsGcctorv0Null.getOrv02());

        int vint = 1234;
        wsGcctorv0Null.setOrv02(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv02()));
    }

    // property orv03
    @Test
    void testGetterSetterOrv03() {

        assertNotNull(wsGcctorv0Null.getOrv03());

        int vint = 1234;
        wsGcctorv0Null.setOrv03(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv03()));
    }

    // property orv04
    @Test
    void testGetterSetterOrv04() {

        assertNotNull(wsGcctorv0Null.getOrv04());

        int vint = 1234;
        wsGcctorv0Null.setOrv04(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv04()));
    }

    // property orv05
    @Test
    void testGetterSetterOrv05() {

        assertNotNull(wsGcctorv0Null.getOrv05());

        int vint = 1234;
        wsGcctorv0Null.setOrv05(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv05()));
    }

    // property orv06
    @Test
    void testGetterSetterOrv06() {

        assertNotNull(wsGcctorv0Null.getOrv06());

        int vint = 1234;
        wsGcctorv0Null.setOrv06(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv06()));
    }

    // property orv07
    @Test
    void testGetterSetterOrv07() {

        assertNotNull(wsGcctorv0Null.getOrv07());

        int vint = 1234;
        wsGcctorv0Null.setOrv07(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv07()));
    }

    // property orv08
    @Test
    void testGetterSetterOrv08() {

        assertNotNull(wsGcctorv0Null.getOrv08());

        int vint = 1234;
        wsGcctorv0Null.setOrv08(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv08()));
    }

    // property orv09
    @Test
    void testGetterSetterOrv09() {

        assertNotNull(wsGcctorv0Null.getOrv09());

        int vint = 1234;
        wsGcctorv0Null.setOrv09(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctorv0Null.getOrv09()));
    }
}

