package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
@Disabled
class Ggqa5q00Test {
    Ggqa5q00 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggqa5q00.class);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      testRun test conditions:
        filstat : [0, 1]
    */
    @Test
    void testRun1() {
        // Set test conditions
        pgm.setFilstat(0);

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    @Test
    void testRun2() {
        // Set test conditions
        pgm.setFilstat(1);

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p000ModuloControl test conditions:
        filstat : [0, 1]
    */
    @Test
    void p000ModuloControlTest1() {
        //TODO setup test
        // Set test conditions
        pgm.setFilstat(0);
        try {
            pgm.p000ModuloControl();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p000ModuloControlTest2() {
            //TODO setup test
            // Set test conditions
            pgm.setFilstat(1);
            try {
                pgm.p000ModuloControl();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      abreVsam test conditions:
        filstat : [0, 1]
    */
    @Test
    void abreVsamTest1() {
        //TODO setup test
        // Set test conditions
        pgm.setFilstat(0);
        try {
            pgm.abreVsam();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void abreVsamTest2() {
            //TODO setup test
            // Set test conditions
            pgm.setFilstat(1);
            try {
                pgm.abreVsam();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      recuperaDatoFiscal test conditions:
        filstat : [0, 1]
    */
    @Test
    void recuperaDatoFiscalTest1() {
        //TODO setup test
        // Set test conditions
        pgm.setFilstat(0);
        try {
            pgm.recuperaDatoFiscal();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void recuperaDatoFiscalTest2() {
            //TODO setup test
            // Set test conditions
            pgm.setFilstat(1);
            try {
                pgm.recuperaDatoFiscal();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      cierraVsam test conditions:
        filstat : [1, 0]
    */
    @Test
    void cierraVsamTest1() {
        //TODO setup test
        // Set test conditions
        pgm.setFilstat(1);
        try {
            pgm.cierraVsam();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void cierraVsamTest2() {
            //TODO setup test
            // Set test conditions
            pgm.setFilstat(0);
            try {
                pgm.cierraVsam();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
}
