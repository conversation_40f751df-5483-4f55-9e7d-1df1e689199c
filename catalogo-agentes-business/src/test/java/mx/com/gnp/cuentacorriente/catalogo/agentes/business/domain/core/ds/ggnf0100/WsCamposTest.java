package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CAMPOS.
 *
 */
class WsCamposTest {

    private WsCampos wsCampos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCampos = new WsCampos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsCampos.setWsStatus_1(0);
        assertEquals(0, wsCampos.getWsStatus_1());

        wsCampos.setWsCuantosAgt(0);
        assertEquals(0, wsCampos.getWsCuantosAgt());

        wsCampos.setWsCuantosCna(0);
        assertEquals(0, wsCampos.getWsCuantosCna());

        wsCampos.setWsCuantosFla(0);
        assertEquals(0, wsCampos.getWsCuantosFla());

        wsCampos.setWsFechaCu("");
        assertEquals("", wsCampos.getWsFechaCu());

        wsCampos.setWsParFec("");
        assertEquals("", wsCampos.getWsParFec());

        wsCampos.setWsSpaces("");
        assertEquals("", wsCampos.getWsSpaces());

        wsCampos.setWsNP("");
        assertEquals("", wsCampos.getWsNP());

        wsCampos.setWsAgtIdr(0);
        assertEquals(0, wsCampos.getWsAgtIdr());

        wsCampos.setWsTdmCve(0);
        assertEquals(0, wsCampos.getWsTdmCve());

        wsCampos.setWsDmaPobCve(0);
        assertEquals(0, wsCampos.getWsDmaPobCve());

        wsCampos.setWsDmaMndCve(0);
        assertEquals(0, wsCampos.getWsDmaMndCve());

        wsCampos.setWsMndEdoCve("");
        assertEquals("", wsCampos.getWsMndEdoCve());

        wsCampos.setWsGerZon(0);
        assertEquals(0, wsCampos.getWsGerZon());










        assertNotNull(wsCampos.getWsCodPag());


        assertNotNull(wsCampos.getWsTabApePat());


        assertNotNull(wsCampos.getWsTabApeMat());


        assertNotNull(wsCampos.getWsTabNombre());


        assertNotNull(wsCampos.getWsTabPkNombre());




    }

    // property wsStatus_1
    @Test
    void testGetterSetterWsStatus_1() {

        assertNotNull(wsCampos.getWsStatus_1());

        int vint = 12;
        wsCampos.setWsStatus_1(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsStatus_1()));
    }

    // property wsCuantosAgt
    @Test
    void testGetterSetterWsCuantosAgt() {

        assertNotNull(wsCampos.getWsCuantosAgt());

        int vint = 123456;
        wsCampos.setWsCuantosAgt(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsCuantosAgt()));
    }

    // property wsCuantosCna
    @Test
    void testGetterSetterWsCuantosCna() {

        assertNotNull(wsCampos.getWsCuantosCna());

        int vint = 123456;
        wsCampos.setWsCuantosCna(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsCuantosCna()));
    }

    // property wsCuantosFla
    @Test
    void testGetterSetterWsCuantosFla() {

        assertNotNull(wsCampos.getWsCuantosFla());

        int vint = 123456;
        wsCampos.setWsCuantosFla(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsCuantosFla()));
    }

    // property wsFechaCu
    @Test
    void testGetterSetterWsFechaCu() {

        assertNotNull(wsCampos.getWsFechaCu());

        String vString = "ABCabc1230";
        wsCampos.setWsFechaCu(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsFechaCu()));
    }

    // property wsParFec
    @Test
    void testGetterSetterWsParFec() {

        assertNotNull(wsCampos.getWsParFec());

        String vString = "ABCabc1230";
        wsCampos.setWsParFec(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsParFec()));
    }

    // property wsSpaces
    @Test
    void testGetterSetterWsSpaces() {

        assertNotNull(wsCampos.getWsSpaces());

        String vString = "ABCabc1230";
        wsCampos.setWsSpaces(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsSpaces()));
    }

    // property wsNP
    @Test
    void testGetterSetterWsNP() {

        assertNotNull(wsCampos.getWsNP());

        String vString = "A";
        wsCampos.setWsNP(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsNP()));
    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        assertNotNull(wsCampos.getWsAgtIdr());

        int vint = 123456789;
        wsCampos.setWsAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsAgtIdr()));
    }

    // property wsTdmCve
    @Test
    void testGetterSetterWsTdmCve() {

        assertNotNull(wsCampos.getWsTdmCve());

        int vint = 1234;
        wsCampos.setWsTdmCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsTdmCve()));
    }

    // property wsDmaPobCve
    @Test
    void testGetterSetterWsDmaPobCve() {

        assertNotNull(wsCampos.getWsDmaPobCve());

        int vint = 123456789;
        wsCampos.setWsDmaPobCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsDmaPobCve()));
    }

    // property wsDmaMndCve
    @Test
    void testGetterSetterWsDmaMndCve() {

        assertNotNull(wsCampos.getWsDmaMndCve());

        int vint = 123456789;
        wsCampos.setWsDmaMndCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsDmaMndCve()));
    }

    // property wsMndEdoCve
    @Test
    void testGetterSetterWsMndEdoCve() {

        assertNotNull(wsCampos.getWsMndEdoCve());

        String vString = "ABCa";
        wsCampos.setWsMndEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsMndEdoCve()));
    }

    // property wsGerZon
    @Test
    void testGetterSetterWsGerZon() {

        assertNotNull(wsCampos.getWsGerZon());

        int vint = 1234;
        wsCampos.setWsGerZon(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsGerZon()));
    }

    // property flaNumAux
    @Test
    void testGetterSetterFlaNumAux() {

        assertNotNull(wsCampos.getFlaNumAux());
    }

    // property wsFlaNum
    @Test
    void testGetterSetterWsFlaNum() {

        assertNotNull(wsCampos.getWsFlaNum());
    }

    // property wsFolLid
    @Test
    void testGetterSetterWsFolLid() {

        assertNotNull(wsCampos.getWsFolLid());
    }

    // property wsFolioxx
    @Test
    void testGetterSetterWsFolioxx() {

        assertNotNull(wsCampos.getWsFolioxx());
    }

    // property wsFlaDes
    @Test
    void testGetterSetterWsFlaDes() {

        assertNotNull(wsCampos.getWsFlaDes());
    }

    // property wsPkEstador
    @Test
    void testGetterSetterWsPkEstador() {

        assertNotNull(wsCampos.getWsPkEstador());
    }

    // property wsPkEstado
    @Test
    void testGetterSetterWsPkEstado() {

        assertNotNull(wsCampos.getWsPkEstado());

        String vString = "ABCa";
        wsCampos.setWsPkEstado(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsPkEstado()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsCampos.setWsPkEstado(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCampos.getWsPkEstado()));
    }

    // property wsPkCmaDesr
    @Test
    void testGetterSetterWsPkCmaDesr() {

        assertNotNull(wsCampos.getWsPkCmaDesr());
    }

    // property wsPkCmaDes
    @Test
    void testGetterSetterWsPkCmaDes() {

        assertNotNull(wsCampos.getWsPkCmaDes());

        String vString = "ABCabc1230";
        wsCampos.setWsPkCmaDes(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsPkCmaDes()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCampos.setWsPkCmaDes(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCampos.getWsPkCmaDes()));
    }

    // property wsCodPag
    @Test
    void testGetterSetterWsCodPag() {

        assertNotNull(wsCampos.getWsCodPag());

        String vString = "A";
        wsCampos.setWsCodPag(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsCodPag()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCampos.setWsCodPag(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCampos.getWsCodPag()));
    }

    // property wsCodPagr
    @Test
    void testGetterSetterWsCodPagr() {

        assertNotNull(wsCampos.getWsCodPagr());

        int vint = 1;
        wsCampos.setWsCodPagr(vint); // int
        assertEquals(asInt(vint), asInt(wsCampos.getWsCodPagr()));

        NumericVar vINumericValue = new NumericVar(1, 0);;
        vINumericValue.setValue("1");
        wsCampos.setWsCodPagr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCampos.getWsCodPagr()));
    }

    // property wsTabApePat
    @Test
    void testGetterSetterWsTabApePat() {

        assertNotNull(wsCampos.getWsTabApePat());

        String vString = "ABCabc1230";
        wsCampos.setWsTabApePat(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsTabApePat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCampos.setWsTabApePat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCampos.getWsTabApePat()));
    }

    // property wsTabApePatr
    @Test
    void testGetterSetterWsTabApePatr() {

        assertNotNull(wsCampos.getWsTabApePatr());
    }

    // property wsTabApeMat
    @Test
    void testGetterSetterWsTabApeMat() {

        assertNotNull(wsCampos.getWsTabApeMat());

        String vString = "ABCabc1230";
        wsCampos.setWsTabApeMat(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsTabApeMat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCampos.setWsTabApeMat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCampos.getWsTabApeMat()));
    }

    // property wsTabApeMatr
    @Test
    void testGetterSetterWsTabApeMatr() {

        assertNotNull(wsCampos.getWsTabApeMatr());
    }

    // property wsTabNombre
    @Test
    void testGetterSetterWsTabNombre() {

        assertNotNull(wsCampos.getWsTabNombre());

        String vString = "ABCabc1230";
        wsCampos.setWsTabNombre(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsTabNombre()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCampos.setWsTabNombre(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCampos.getWsTabNombre()));
    }

    // property wsTabNombrer
    @Test
    void testGetterSetterWsTabNombrer() {

        assertNotNull(wsCampos.getWsTabNombrer());
    }

    // property wsTabPkNombre
    @Test
    void testGetterSetterWsTabPkNombre() {

        assertNotNull(wsCampos.getWsTabPkNombre());

        String vString = "ABCabc1230";
        wsCampos.setWsTabPkNombre(vString); // String
        assertEquals(asStr(vString), asStr(wsCampos.getWsTabPkNombre()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCampos.setWsTabPkNombre(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCampos.getWsTabPkNombre()));
    }

    // property wsTabPkNombrer
    @Test
    void testGetterSetterWsTabPkNombrer() {

        assertNotNull(wsCampos.getWsTabPkNombrer());
    }

    // property wsPkNombre
    @Test
    void testGetterSetterWsPkNombre() {

        assertNotNull(wsCampos.getWsPkNombre());
    }

    // property wsCalleNumIn
    @Test
    void testGetterSetterWsCalleNumIn() {

        assertNotNull(wsCampos.getWsCalleNumIn());
    }

    // property flaNum0Aux
    @Test
    void testGetterSetterFlaNum0Aux() {

        // Property: flaNumAux.flaNum0Aux -> 05 FLA-NUM0-AUX X
        assertNotNull(wsCampos.getFlaNum0Aux());
        String vString = "A";
        wsCampos.setFlaNum0Aux(vString); // String
    }

    // property flaNum1Aux
    @Test
    void testGetterSetterFlaNum1Aux() {

        // Property: flaNumAux.flaNum1Aux -> 05 FLA-NUM1-AUX 9(7)
        assertNotNull(wsCampos.getFlaNum1Aux());
        int vint = 1234567;
        wsCampos.setFlaNum1Aux(vint); // int
    }

    // property wsFlaNum0
    @Test
    void testGetterSetterWsFlaNum0() {

        // Property: wsFlaNum.wsFlaNum0 -> 05 WS-FLA-NUM0 X
        assertNotNull(wsCampos.getWsFlaNum0());
        String vString = "A";
        wsCampos.setWsFlaNum0(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCampos.setWsFlaNum0(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFlaNum1
    @Test
    void testGetterSetterWsFlaNum1() {

        // Property: wsFlaNum.wsFlaNum1 -> 05 WS-FLA-NUM1 9(7)
        assertNotNull(wsCampos.getWsFlaNum1());
        int vint = 1234567;
        wsCampos.setWsFlaNum1(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsCampos.setWsFlaNum1(vINumericValue); // INumericValue
    }

    // property wsFolLid0
    @Test
    void testGetterSetterWsFolLid0() {

        // Property: wsFolLid.wsFolLid0 -> 05 WS-FOL-LID0 X
        assertNotNull(wsCampos.getWsFolLid0());
        String vString = "A";
        wsCampos.setWsFolLid0(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCampos.setWsFolLid0(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFolLid1
    @Test
    void testGetterSetterWsFolLid1() {

        // Property: wsFolLid.wsFolLid1 -> 05 WS-FOL-LID1 9(7)
        assertNotNull(wsCampos.getWsFolLid1());
        int vint = 1234567;
        wsCampos.setWsFolLid1(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsCampos.setWsFolLid1(vINumericValue); // INumericValue
    }

    // property wsFolioxx1
    @Test
    void testGetterSetterWsFolioxx1() {

        // Property: wsFolioxx.wsFolioxx1 -> 05 WS-FOLIOXX1 X
        assertNotNull(wsCampos.getWsFolioxx1());
        String vString = "A";
        wsCampos.setWsFolioxx1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCampos.setWsFolioxx1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFolioxx2
    @Test
    void testGetterSetterWsFolioxx2() {

        // Property: wsFolioxx.wsFolioxx2 -> 05 WS-FOLIOXX2 9(7)
        assertNotNull(wsCampos.getWsFolioxx2());
        int vint = 1234567;
        wsCampos.setWsFolioxx2(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsCampos.setWsFolioxx2(vINumericValue); // INumericValue
    }

    // property wsFlaDesDos
    @Test
    void testGetterSetterWsFlaDesDos() {

        // Property: wsFlaDes.wsFlaDesDos -> 05 WS-FLA-DES-DOS XX
        assertNotNull(wsCampos.getWsFlaDesDos());
        String vString = "AB";
        wsCampos.setWsFlaDesDos(vString); // String
    }

    // property wsFlaDesResto
    @Test
    void testGetterSetterWsFlaDesResto() {

        // Property: wsFlaDes.wsFlaDesResto -> 05 WS-FLA-DES-RESTO X(38)
        assertNotNull(wsCampos.getWsFlaDesResto());
        String vString = "ABCabc1230";
        wsCampos.setWsFlaDesResto(vString); // String
    }

    // property wsPkEstado1
    @Test
    void testGetterSetterWsPkEstado1() {

        // Property: wsPkEstador.wsPkEstado1 -> 05 WS-PK-ESTADO1 99
        assertNotNull(wsCampos.getWsPkEstado1());
        int vint = 12;
        wsCampos.setWsPkEstado1(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsCampos.setWsPkEstado1(vINumericValue); // INumericValue
    }

    // property wsPkEstado2
    @Test
    void testGetterSetterWsPkEstado2() {

        // Property: wsPkEstador.wsPkEstado2 -> 05 WS-PK-ESTADO2 XX
        assertNotNull(wsCampos.getWsPkEstado2());
        String vString = "AB";
        wsCampos.setWsPkEstado2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsCampos.setWsPkEstado2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsPkTelr
    @Test
    void testGetterSetterWsPkTelr() {

        // Property: wsPkCmaDesr.wsPkTelr -> 05 WS-PK-TELR X(12) REDEFINED BY WS-PK-TEL
        assertNotNull(wsCampos.getWsPkTelr());
        String vString = "ABCabc1230";
        wsCampos.setWsPkTelr(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCampos.setWsPkTelr(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsPkTel
    @Test
    void testGetterSetterWsPkTel() {

        // Property: wsPkCmaDesr.wsPkTel -> 05 WS-PK-TEL REDEFINES WS-PK-TELR 9(12)
        assertNotNull(wsCampos.getWsPkTel());
        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        wsCampos.setWsPkTel(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        wsCampos.setWsPkTel(vINumericValue); // INumericValue
    }

    // property eleApePat
    @Test
    void testGetterSetterEleApePat() {

        // Property: wsTabApePatr.eleApePat -> 05 ELE-APE-PAT X [40]
        assertNotNull(wsCampos.getEleApePat());
        String vString = "A";
        wsCampos.getEleApePat().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsCampos.getEleApePat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property eleApeMat
    @Test
    void testGetterSetterEleApeMat() {

        // Property: wsTabApeMatr.eleApeMat -> 05 ELE-APE-MAT X [30]
        assertNotNull(wsCampos.getEleApeMat());
        String vString = "A";
        wsCampos.getEleApeMat().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsCampos.getEleApeMat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property eleNombre
    @Test
    void testGetterSetterEleNombre() {

        // Property: wsTabNombrer.eleNombre -> 05 ELE-NOMBRE X [40]
        assertNotNull(wsCampos.getEleNombre());
        String vString = "A";
        wsCampos.getEleNombre().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsCampos.getEleNombre().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property elePkNombre
    @Test
    void testGetterSetterElePkNombre() {

        // Property: wsTabPkNombrer.elePkNombre -> 05 ELE-PK-NOMBRE X [35]
        assertNotNull(wsCampos.getElePkNombre());
        String vString = "A";
        wsCampos.getElePkNombre().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsCampos.getElePkNombre().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsPkApePat
    @Test
    void testGetterSetterWsPkApePat() {

        // Property: wsPkNombre.wsPkApePat -> 05 WS-PK-APE-PAT X(15)
        assertNotNull(wsCampos.getWsPkApePat());
        String vString = "ABCabc1230";
        wsCampos.setWsPkApePat(vString); // String
    }

    // property wsPkApeMat
    @Test
    void testGetterSetterWsPkApeMat() {

        // Property: wsPkNombre.wsPkApeMat -> 05 WS-PK-APE-MAT X(10)
        assertNotNull(wsCampos.getWsPkApeMat());
        String vString = "ABCabc1230";
        wsCampos.setWsPkApeMat(vString); // String
    }

    // property wsPkNom
    @Test
    void testGetterSetterWsPkNom() {

        // Property: wsPkNombre.wsPkNom -> 05 WS-PK-NOM X(10)
        assertNotNull(wsCampos.getWsPkNom());
        String vString = "ABCabc1230";
        wsCampos.setWsPkNom(vString); // String
    }

    // property wsPkCalle
    @Test
    void testGetterSetterWsPkCalle() {

        // Property: wsCalleNumIn.wsPkCalle -> 05 WS-PK-CALLE X(25)
        assertNotNull(wsCampos.getWsPkCalle());
        String vString = "ABCabc1230";
        wsCampos.setWsPkCalle(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCampos.setWsPkCalle(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsPkNum
    @Test
    void testGetterSetterWsPkNum() {

        // Property: wsCalleNumIn.wsPkNum -> 05 WS-PK-NUM X(05)
        assertNotNull(wsCampos.getWsPkNum());
        String vString = "ABCab";
        wsCampos.setWsPkNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCampos.setWsPkNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsPkInt
    @Test
    void testGetterSetterWsPkInt() {

        // Property: wsCalleNumIn.wsPkInt -> 05 WS-PK-INT X(05)
        assertNotNull(wsCampos.getWsPkInt());
        String vString = "ABCab";
        wsCampos.setWsPkInt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCampos.setWsPkInt(vIAlphanumericValue); // IAlphanumericValue
    }
}

