package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: PK-NUM-CEDULARR.
 *
 */
class PkNumCedularrTest {

    private PkNumCedularr pkNumCedularr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        pkNumCedularr = new PkNumCedularr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        PkNumCedularr pkNumCedularr2 = new PkNumCedularr(parent);
        assertNotNull(pkNumCedularr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 20);
        PkNumCedularr pkNumCedularr3 = new PkNumCedularr(parent, sibling);
        assertNotNull(pkNumCedularr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(pkNumCedularr.getPkNumCedulaTres());

        assertNotNull(pkNumCedularr.getPkNumCedulaR17());

    }

    // property pkNumCedulaTres
    @Test
    void testGetterSetterPkNumCedulaTres() {

        assertNotNull(pkNumCedularr.getPkNumCedulaTres());

        String vString = "ABC";
        pkNumCedularr.setPkNumCedulaTres(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedularr.getPkNumCedulaTres()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        pkNumCedularr.setPkNumCedulaTres(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedularr.getPkNumCedulaTres()));
    }

    // property pkNumCedulaR17
    @Test
    void testGetterSetterPkNumCedulaR17() {

        assertNotNull(pkNumCedularr.getPkNumCedulaR17());

        String vString = "ABCabc1230";
        pkNumCedularr.setPkNumCedulaR17(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedularr.getPkNumCedulaR17()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        pkNumCedularr.setPkNumCedulaR17(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedularr.getPkNumCedulaR17()));
    }
}

