package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.string.StringUtils;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CAMPOS-TRABAJO.
 *
 */
class WsCamposTrabajoTest {

    private WsCamposTrabajo wsCamposTrabajo;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCamposTrabajo = new WsCamposTrabajo();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsCamposTrabajo.getS999Err());

        wsCamposTrabajo.setWsSt("");
        assertEquals("", wsCamposTrabajo.getWsSt());

        wsCamposTrabajo.setWsArchivo("");
        assertEquals("", wsCamposTrabajo.getWsArchivo());

        wsCamposTrabajo.setWsFecMas_1("");
        assertEquals("", wsCamposTrabajo.getWsFecMas_1());

        wsCamposTrabajo.setWsFecHfo(StringUtils.all("0", 10));
        assertEquals(StringUtils.all("0", 10), wsCamposTrabajo.getWsFecHfo());

        wsCamposTrabajo.setWsOfnCveAux(0);
        assertEquals(0, wsCamposTrabajo.getWsOfnCveAux());


    }

    // property s999Err
    @Test
    void testGetterSetterS999Err() {

        assertNotNull(wsCamposTrabajo.getS999Err());

        String vString = "A";
        wsCamposTrabajo.setS999Err(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getS999Err()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCamposTrabajo.setS999Err(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCamposTrabajo.getS999Err()));
    }

    // property wsSt
    @Test
    void testGetterSetterWsSt() {

        assertNotNull(wsCamposTrabajo.getWsSt());

        String vString = "AB";
        wsCamposTrabajo.setWsSt(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsSt()));
    }

    // property wsArchivo
    @Test
    void testGetterSetterWsArchivo() {

        assertNotNull(wsCamposTrabajo.getWsArchivo());

        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsArchivo(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsArchivo()));
    }

    // property wsFecMas_1
    @Test
    void testGetterSetterWsFecMas_1() {

        assertNotNull(wsCamposTrabajo.getWsFecMas_1());

        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsFecMas_1(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsFecMas_1()));
    }

    // property wsFecHfo
    @Test
    void testGetterSetterWsFecHfo() {

        assertNotNull(wsCamposTrabajo.getWsFecHfo());

        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsFecHfo(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsFecHfo()));
    }

    // property wsOfnCveAux
    @Test
    void testGetterSetterWsOfnCveAux() {

        assertNotNull(wsCamposTrabajo.getWsOfnCveAux());

        int vint = 1234;
        wsCamposTrabajo.setWsOfnCveAux(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsOfnCveAux()));
    }

    // property wsHfoReg
    @Test
    void testGetterSetterWsHfoReg() {

        assertNotNull(wsCamposTrabajo.getWsHfoReg());
    }

    // property wsHfoOfnCve
    @Test
    void testGetterSetterWsHfoOfnCve() {

        // Property: wsHfoReg.wsHfoOfnCve -> 10 WS-HFO-OFN-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfoOfnCve());
        int vint = 1234;
        wsCamposTrabajo.setWsHfoOfnCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfoOfnCve(vINumericValue); // INumericValue
    }

    // property wsHfoFecIniPdo
    @Test
    void testGetterSetterWsHfoFecIniPdo() {

        // Property: wsHfoReg.wsHfoFecIniPdo -> 10 WS-HFO-FEC-INI-PDO X(10)
        assertNotNull(wsCamposTrabajo.getWsHfoFecIniPdo());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoFecIniPdo(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoFecIniPdo(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoFecFinPdo
    @Test
    void testGetterSetterWsHfoFecFinPdo() {

        // Property: wsHfoReg.wsHfoFecFinPdo -> 10 WS-HFO-FEC-FIN-PDO X(10)
        assertNotNull(wsCamposTrabajo.getWsHfoFecFinPdo());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoFecFinPdo(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoFecFinPdo(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoRfcRcp
    @Test
    void testGetterSetterWsHfoRfcRcp() {

        // Property: wsHfoReg.wsHfoRfcRcp -> 10 WS-HFO-RFC-RCP X(13)
        assertNotNull(wsCamposTrabajo.getWsHfoRfcRcp());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoRfcRcp(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoRfcRcp(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoNomRcp
    @Test
    void testGetterSetterWsHfoNomRcp() {

        // Property: wsHfoReg.wsHfoNomRcp -> 10 WS-HFO-NOM-RCP X(100)
        assertNotNull(wsCamposTrabajo.getWsHfoNomRcp());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoNomRcp(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoNomRcp(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoOfnCae
    @Test
    void testGetterSetterWsHfoOfnCae() {

        // Property: wsHfoReg.wsHfoOfnCae -> 10 WS-HFO-OFN-CAE X(40)
        assertNotNull(wsCamposTrabajo.getWsHfoOfnCae());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoOfnCae(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoOfnCae(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoOfnNum
    @Test
    void testGetterSetterWsHfoOfnNum() {

        // Property: wsHfoReg.wsHfoOfnNum -> 10 WS-HFO-OFN-NUM X(5)
        assertNotNull(wsCamposTrabajo.getWsHfoOfnNum());
        String vString = "ABCab";
        wsCamposTrabajo.setWsHfoOfnNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCamposTrabajo.setWsHfoOfnNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoOfnInt
    @Test
    void testGetterSetterWsHfoOfnInt() {

        // Property: wsHfoReg.wsHfoOfnInt -> 10 WS-HFO-OFN-INT X(5)
        assertNotNull(wsCamposTrabajo.getWsHfoOfnInt());
        String vString = "ABCab";
        wsCamposTrabajo.setWsHfoOfnInt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCamposTrabajo.setWsHfoOfnInt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoOfnCol
    @Test
    void testGetterSetterWsHfoOfnCol() {

        // Property: wsHfoReg.wsHfoOfnCol -> 10 WS-HFO-OFN-COL X(30)
        assertNotNull(wsCamposTrabajo.getWsHfoOfnCol());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoOfnCol(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoOfnCol(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoMndNom
    @Test
    void testGetterSetterWsHfoMndNom() {

        // Property: wsHfoReg.wsHfoMndNom -> 10 WS-HFO-MND-NOM X(50)
        assertNotNull(wsCamposTrabajo.getWsHfoMndNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoMndNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoMndNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoEdoNom
    @Test
    void testGetterSetterWsHfoEdoNom() {

        // Property: wsHfoReg.wsHfoEdoNom -> 10 WS-HFO-EDO-NOM X(30)
        assertNotNull(wsCamposTrabajo.getWsHfoEdoNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoEdoNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoEdoNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoPaiNom
    @Test
    void testGetterSetterWsHfoPaiNom() {

        // Property: wsHfoReg.wsHfoPaiNom -> 10 WS-HFO-PAI-NOM X(30)
        assertNotNull(wsCamposTrabajo.getWsHfoPaiNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoPaiNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoPaiNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoCpoCve
    @Test
    void testGetterSetterWsHfoCpoCve() {

        // Property: wsHfoReg.wsHfoCpoCve -> 10 WS-HFO-CPO-CVE S9(9) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfoCpoCve());
        int vint = 123456789;
        wsCamposTrabajo.setWsHfoCpoCve(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCamposTrabajo.setWsHfoCpoCve(vINumericValue); // INumericValue
    }

    // property wsHfoUsuCve
    @Test
    void testGetterSetterWsHfoUsuCve() {

        // Property: wsHfoReg.wsHfoUsuCve -> 10 WS-HFO-USU-CVE X(8)
        assertNotNull(wsCamposTrabajo.getWsHfoUsuCve());
        String vString = "ABCabc12";
        wsCamposTrabajo.setWsHfoUsuCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsCamposTrabajo.setWsHfoUsuCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoNomPrg
    @Test
    void testGetterSetterWsHfoNomPrg() {

        // Property: wsHfoReg.wsHfoNomPrg -> 10 WS-HFO-NOM-PRG X(8)
        assertNotNull(wsCamposTrabajo.getWsHfoNomPrg());
        String vString = "ABCabc12";
        wsCamposTrabajo.setWsHfoNomPrg(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsCamposTrabajo.setWsHfoNomPrg(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfoFecUltAct
    @Test
    void testGetterSetterWsHfoFecUltAct() {

        // Property: wsHfoReg.wsHfoFecUltAct -> 10 WS-HFO-FEC-ULT-ACT X(26)
        assertNotNull(wsCamposTrabajo.getWsHfoFecUltAct());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfoFecUltAct(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfoFecUltAct(vIAlphanumericValue); // IAlphanumericValue
    }
}

