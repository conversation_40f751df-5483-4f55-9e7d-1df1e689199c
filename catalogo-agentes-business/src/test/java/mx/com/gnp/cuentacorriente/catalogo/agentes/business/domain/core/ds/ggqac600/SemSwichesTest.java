package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: SEM-SWICHES.
 *
 */
class SemSwichesTest {

    private SemSwiches semSwiches;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        semSwiches = new SemSwiches();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        semSwiches.setSemAgt0("N");
        assertEquals("N", semSwiches.getSemAgt0());

        semSwiches.setSemPrimeraVez("N");
        assertEquals("N", semSwiches.getSemPrimeraVez());

        semSwiches.setSemCampoDifer("N");
        assertEquals("N", semSwiches.getSemCampoDifer());

        semSwiches.setSemCambiarStAgtVig("N");
        assertEquals("N", semSwiches.getSemCambiarStAgtVig());

    }

    // property semAgt0
    @Test
    void testGetterSetterSemAgt0() {

        assertNotNull(semSwiches.getSemAgt0());

        String vString = "A";
        semSwiches.setSemAgt0(vString); // String
        assertEquals(asStr(vString), asStr(semSwiches.getSemAgt0()));
        // Conditionals (88) - SEM-AGT0
        semSwiches.setSwSiFinAgt0();
        assertTrue(semSwiches.isSwSiFinAgt0());
        semSwiches.setSwNoFinAgt0();
        assertTrue(semSwiches.isSwNoFinAgt0());

    }

    // property semPrimeraVez
    @Test
    void testGetterSetterSemPrimeraVez() {

        assertNotNull(semSwiches.getSemPrimeraVez());

        String vString = "A";
        semSwiches.setSemPrimeraVez(vString); // String
        assertEquals(asStr(vString), asStr(semSwiches.getSemPrimeraVez()));
        // Conditionals (88) - SEM-PRIMERA-VEZ
        semSwiches.setSwSiPrimeraVez();
        assertTrue(semSwiches.isSwSiPrimeraVez());
        semSwiches.setSwNoPrimeraVez();
        assertTrue(semSwiches.isSwNoPrimeraVez());

    }

    // property semCampoDifer
    @Test
    void testGetterSetterSemCampoDifer() {

        assertNotNull(semSwiches.getSemCampoDifer());

        String vString = "A";
        semSwiches.setSemCampoDifer(vString); // String
        assertEquals(asStr(vString), asStr(semSwiches.getSemCampoDifer()));
        // Conditionals (88) - SEM-CAMPO-DIFER
        semSwiches.setSwSiCampoDifer();
        assertTrue(semSwiches.isSwSiCampoDifer());
        semSwiches.setSwNoCampoDifer();
        assertTrue(semSwiches.isSwNoCampoDifer());

    }

    // property semCambiarStAgtVig
    @Test
    void testGetterSetterSemCambiarStAgtVig() {

        assertNotNull(semSwiches.getSemCambiarStAgtVig());

        String vString = "A";
        semSwiches.setSemCambiarStAgtVig(vString); // String
        assertEquals(asStr(vString), asStr(semSwiches.getSemCambiarStAgtVig()));
        // Conditionals (88) - SEM-CAMBIAR-ST-AGT-VIG
        semSwiches.setSwNoCambiarStAgtVig();
        assertTrue(semSwiches.isSwNoCambiarStAgtVig());
        semSwiches.setSwSiCambiarStAgtVig();
        assertTrue(semSwiches.isSwSiCambiarStAgtVig());

    }
}

