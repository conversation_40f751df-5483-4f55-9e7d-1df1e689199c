package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.wsfechas;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FECHA-MOV-R.
 *
 */
class WsFechaMovRTest {

    private WsFechaMovR wsFechaMovR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFechaMovR = new WsFechaMovR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFechaMovR wsFechaMovR2 = new WsFechaMovR(parent);
        assertNotNull(wsFechaMovR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsFechaMovR wsFechaMovR3 = new WsFechaMovR(parent, sibling);
        assertNotNull(wsFechaMovR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFechaMovR.getWsFecMovAa());

        assertNotNull(wsFechaMovR.getWsFecMovGuion_1());

        assertNotNull(wsFechaMovR.getWsFecMovMm());

        assertNotNull(wsFechaMovR.getWsFecMovGuion_2());

        assertNotNull(wsFechaMovR.getWsFecMovDd());

    }

    // property wsFecMovAa
    @Test
    void testGetterSetterWsFecMovAa() {

        assertNotNull(wsFechaMovR.getWsFecMovAa());

        int vint = 1234;
        wsFechaMovR.setWsFecMovAa(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaMovR.getWsFecMovAa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsFechaMovR.setWsFecMovAa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaMovR.getWsFecMovAa()));
    }

    // property wsFecMovGuion_1
    @Test
    void testGetterSetterWsFecMovGuion_1() {

        assertNotNull(wsFechaMovR.getWsFecMovGuion_1());

        String vString = "A";
        wsFechaMovR.setWsFecMovGuion_1(vString); // String
        assertEquals(asStr(vString), asStr(wsFechaMovR.getWsFecMovGuion_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFechaMovR.setWsFecMovGuion_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFechaMovR.getWsFecMovGuion_1()));
    }

    // property wsFecMovMm
    @Test
    void testGetterSetterWsFecMovMm() {

        assertNotNull(wsFechaMovR.getWsFecMovMm());

        int vint = 12;
        wsFechaMovR.setWsFecMovMm(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaMovR.getWsFecMovMm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaMovR.setWsFecMovMm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaMovR.getWsFecMovMm()));
    }

    // property wsFecMovGuion_2
    @Test
    void testGetterSetterWsFecMovGuion_2() {

        assertNotNull(wsFechaMovR.getWsFecMovGuion_2());

        String vString = "A";
        wsFechaMovR.setWsFecMovGuion_2(vString); // String
        assertEquals(asStr(vString), asStr(wsFechaMovR.getWsFecMovGuion_2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFechaMovR.setWsFecMovGuion_2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFechaMovR.getWsFecMovGuion_2()));
    }

    // property wsFecMovDd
    @Test
    void testGetterSetterWsFecMovDd() {

        assertNotNull(wsFechaMovR.getWsFecMovDd());

        int vint = 12;
        wsFechaMovR.setWsFecMovDd(vint); // int
        assertEquals(asInt(vint), asInt(wsFechaMovR.getWsFecMovDd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechaMovR.setWsFecMovDd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFechaMovR.getWsFecMovDd()));
    }
}

