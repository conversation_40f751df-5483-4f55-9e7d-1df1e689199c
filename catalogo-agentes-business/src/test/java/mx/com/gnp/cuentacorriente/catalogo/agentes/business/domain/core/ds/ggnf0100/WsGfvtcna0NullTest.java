package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTCNA0-NULL.
 *
 */
class WsGfvtcna0NullTest {

    private WsGfvtcna0Null wsGfvtcna0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtcna0Null = new WsGfvtcna0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsGfvtcna0Null wsGfvtcna0Null2 = new WsGfvtcna0Null(parent);
        assertNotNull(wsGfvtcna0Null2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 72);
        WsGfvtcna0Null wsGfvtcna0Null3 = new WsGfvtcna0Null(parent, sibling);
        assertNotNull(wsGfvtcna0Null3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsGfvtcna0Null.getCna01());

        assertNotNull(wsGfvtcna0Null.getCna02());

        assertNotNull(wsGfvtcna0Null.getCna03());

        assertNotNull(wsGfvtcna0Null.getCna04());

        assertNotNull(wsGfvtcna0Null.getCna05());

        assertNotNull(wsGfvtcna0Null.getCna06());

        assertNotNull(wsGfvtcna0Null.getCna07());

        assertNotNull(wsGfvtcna0Null.getCna08());

        assertNotNull(wsGfvtcna0Null.getCna09());

        assertNotNull(wsGfvtcna0Null.getCna10());

        assertNotNull(wsGfvtcna0Null.getCna11());

        assertNotNull(wsGfvtcna0Null.getCna12());

        assertNotNull(wsGfvtcna0Null.getCna13());

        assertNotNull(wsGfvtcna0Null.getCna14());

        assertNotNull(wsGfvtcna0Null.getCna15());

        assertNotNull(wsGfvtcna0Null.getCna16());

        assertNotNull(wsGfvtcna0Null.getCna17());

        assertNotNull(wsGfvtcna0Null.getCna18());

        assertNotNull(wsGfvtcna0Null.getCna19());

        assertNotNull(wsGfvtcna0Null.getCna20());

        assertNotNull(wsGfvtcna0Null.getCna21());

        assertNotNull(wsGfvtcna0Null.getCna22());

        assertNotNull(wsGfvtcna0Null.getCna23());

        assertNotNull(wsGfvtcna0Null.getCna24());

        assertNotNull(wsGfvtcna0Null.getCna25());

        assertNotNull(wsGfvtcna0Null.getCna26());

        assertNotNull(wsGfvtcna0Null.getCna27());

        assertNotNull(wsGfvtcna0Null.getCna28());

        assertNotNull(wsGfvtcna0Null.getCna29());

        assertNotNull(wsGfvtcna0Null.getCna30());

        assertNotNull(wsGfvtcna0Null.getCna31());

        assertNotNull(wsGfvtcna0Null.getCna32());

        assertNotNull(wsGfvtcna0Null.getCna33());

        assertNotNull(wsGfvtcna0Null.getCna34());

        assertNotNull(wsGfvtcna0Null.getCna35());

        assertNotNull(wsGfvtcna0Null.getCna36());

    }

    // property cna01
    @Test
    void testGetterSetterCna01() {

        assertNotNull(wsGfvtcna0Null.getCna01());

        int vint = 1234;
        wsGfvtcna0Null.setCna01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna01()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna01(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna01()));
    }

    // property cna02
    @Test
    void testGetterSetterCna02() {

        assertNotNull(wsGfvtcna0Null.getCna02());

        int vint = 1234;
        wsGfvtcna0Null.setCna02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna02()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna02(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna02()));
    }

    // property cna03
    @Test
    void testGetterSetterCna03() {

        assertNotNull(wsGfvtcna0Null.getCna03());

        int vint = 1234;
        wsGfvtcna0Null.setCna03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna03()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna03(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna03()));
    }

    // property cna04
    @Test
    void testGetterSetterCna04() {

        assertNotNull(wsGfvtcna0Null.getCna04());

        int vint = 1234;
        wsGfvtcna0Null.setCna04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna04()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna04(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna04()));
    }

    // property cna05
    @Test
    void testGetterSetterCna05() {

        assertNotNull(wsGfvtcna0Null.getCna05());

        int vint = 1234;
        wsGfvtcna0Null.setCna05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna05()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna05(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna05()));
    }

    // property cna06
    @Test
    void testGetterSetterCna06() {

        assertNotNull(wsGfvtcna0Null.getCna06());

        int vint = 1234;
        wsGfvtcna0Null.setCna06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna06()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna06(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna06()));
    }

    // property cna07
    @Test
    void testGetterSetterCna07() {

        assertNotNull(wsGfvtcna0Null.getCna07());

        int vint = 1234;
        wsGfvtcna0Null.setCna07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna07()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna07(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna07()));
    }

    // property cna08
    @Test
    void testGetterSetterCna08() {

        assertNotNull(wsGfvtcna0Null.getCna08());

        int vint = 1234;
        wsGfvtcna0Null.setCna08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna08()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna08(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna08()));
    }

    // property cna09
    @Test
    void testGetterSetterCna09() {

        assertNotNull(wsGfvtcna0Null.getCna09());

        int vint = 1234;
        wsGfvtcna0Null.setCna09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna09()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna09(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna09()));
    }

    // property cna10
    @Test
    void testGetterSetterCna10() {

        assertNotNull(wsGfvtcna0Null.getCna10());

        int vint = 1234;
        wsGfvtcna0Null.setCna10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna10()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna10(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna10()));
    }

    // property cna11
    @Test
    void testGetterSetterCna11() {

        assertNotNull(wsGfvtcna0Null.getCna11());

        int vint = 1234;
        wsGfvtcna0Null.setCna11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna11()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna11(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna11()));
    }

    // property cna12
    @Test
    void testGetterSetterCna12() {

        assertNotNull(wsGfvtcna0Null.getCna12());

        int vint = 1234;
        wsGfvtcna0Null.setCna12(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna12()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna12(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna12()));
    }

    // property cna13
    @Test
    void testGetterSetterCna13() {

        assertNotNull(wsGfvtcna0Null.getCna13());

        int vint = 1234;
        wsGfvtcna0Null.setCna13(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna13()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna13(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna13()));
    }

    // property cna14
    @Test
    void testGetterSetterCna14() {

        assertNotNull(wsGfvtcna0Null.getCna14());

        int vint = 1234;
        wsGfvtcna0Null.setCna14(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna14()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna14(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna14()));
    }

    // property cna15
    @Test
    void testGetterSetterCna15() {

        assertNotNull(wsGfvtcna0Null.getCna15());

        int vint = 1234;
        wsGfvtcna0Null.setCna15(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna15()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna15(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna15()));
    }

    // property cna16
    @Test
    void testGetterSetterCna16() {

        assertNotNull(wsGfvtcna0Null.getCna16());

        int vint = 1234;
        wsGfvtcna0Null.setCna16(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna16()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna16(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna16()));
    }

    // property cna17
    @Test
    void testGetterSetterCna17() {

        assertNotNull(wsGfvtcna0Null.getCna17());

        int vint = 1234;
        wsGfvtcna0Null.setCna17(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna17()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna17(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna17()));
    }

    // property cna18
    @Test
    void testGetterSetterCna18() {

        assertNotNull(wsGfvtcna0Null.getCna18());

        int vint = 1234;
        wsGfvtcna0Null.setCna18(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna18()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna18(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna18()));
    }

    // property cna19
    @Test
    void testGetterSetterCna19() {

        assertNotNull(wsGfvtcna0Null.getCna19());

        int vint = 1234;
        wsGfvtcna0Null.setCna19(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna19()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna19(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna19()));
    }

    // property cna20
    @Test
    void testGetterSetterCna20() {

        assertNotNull(wsGfvtcna0Null.getCna20());

        int vint = 1234;
        wsGfvtcna0Null.setCna20(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna20()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna20(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna20()));
    }

    // property cna21
    @Test
    void testGetterSetterCna21() {

        assertNotNull(wsGfvtcna0Null.getCna21());

        int vint = 1234;
        wsGfvtcna0Null.setCna21(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna21()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna21(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna21()));
    }

    // property cna22
    @Test
    void testGetterSetterCna22() {

        assertNotNull(wsGfvtcna0Null.getCna22());

        int vint = 1234;
        wsGfvtcna0Null.setCna22(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna22()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna22(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna22()));
    }

    // property cna23
    @Test
    void testGetterSetterCna23() {

        assertNotNull(wsGfvtcna0Null.getCna23());

        int vint = 1234;
        wsGfvtcna0Null.setCna23(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna23()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna23(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna23()));
    }

    // property cna24
    @Test
    void testGetterSetterCna24() {

        assertNotNull(wsGfvtcna0Null.getCna24());

        int vint = 1234;
        wsGfvtcna0Null.setCna24(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna24()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna24(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna24()));
    }

    // property cna25
    @Test
    void testGetterSetterCna25() {

        assertNotNull(wsGfvtcna0Null.getCna25());

        int vint = 1234;
        wsGfvtcna0Null.setCna25(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna25()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna25(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna25()));
    }

    // property cna26
    @Test
    void testGetterSetterCna26() {

        assertNotNull(wsGfvtcna0Null.getCna26());

        int vint = 1234;
        wsGfvtcna0Null.setCna26(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna26()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna26(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna26()));
    }

    // property cna27
    @Test
    void testGetterSetterCna27() {

        assertNotNull(wsGfvtcna0Null.getCna27());

        int vint = 1234;
        wsGfvtcna0Null.setCna27(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna27()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna27(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna27()));
    }

    // property cna28
    @Test
    void testGetterSetterCna28() {

        assertNotNull(wsGfvtcna0Null.getCna28());

        int vint = 1234;
        wsGfvtcna0Null.setCna28(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna28()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna28(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna28()));
    }

    // property cna29
    @Test
    void testGetterSetterCna29() {

        assertNotNull(wsGfvtcna0Null.getCna29());

        int vint = 1234;
        wsGfvtcna0Null.setCna29(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna29()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna29(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna29()));
    }

    // property cna30
    @Test
    void testGetterSetterCna30() {

        assertNotNull(wsGfvtcna0Null.getCna30());

        int vint = 1234;
        wsGfvtcna0Null.setCna30(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna30()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna30(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna30()));
    }

    // property cna31
    @Test
    void testGetterSetterCna31() {

        assertNotNull(wsGfvtcna0Null.getCna31());

        int vint = 1234;
        wsGfvtcna0Null.setCna31(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna31()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna31(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna31()));
    }

    // property cna32
    @Test
    void testGetterSetterCna32() {

        assertNotNull(wsGfvtcna0Null.getCna32());

        int vint = 1234;
        wsGfvtcna0Null.setCna32(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna32()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna32(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna32()));
    }

    // property cna33
    @Test
    void testGetterSetterCna33() {

        assertNotNull(wsGfvtcna0Null.getCna33());

        int vint = 1234;
        wsGfvtcna0Null.setCna33(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna33()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna33(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna33()));
    }

    // property cna34
    @Test
    void testGetterSetterCna34() {

        assertNotNull(wsGfvtcna0Null.getCna34());

        int vint = 1234;
        wsGfvtcna0Null.setCna34(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna34()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna34(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna34()));
    }

    // property cna35
    @Test
    void testGetterSetterCna35() {

        assertNotNull(wsGfvtcna0Null.getCna35());

        int vint = 1234;
        wsGfvtcna0Null.setCna35(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna35()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna35(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna35()));
    }

    // property cna36
    @Test
    void testGetterSetterCna36() {

        assertNotNull(wsGfvtcna0Null.getCna36());

        int vint = 1234;
        wsGfvtcna0Null.setCna36(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null.getCna36()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtcna0Null.setCna36(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtcna0Null.getCna36()));
    }
}

