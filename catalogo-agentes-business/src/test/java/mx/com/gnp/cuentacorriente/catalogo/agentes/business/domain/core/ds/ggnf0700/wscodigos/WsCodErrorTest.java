package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wscodigos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-COD-ERROR.
 *
 */
class WsCodErrorTest {

    private WsCodError wsCodError;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCodError = new WsCodError();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsCodError wsCodError2 = new WsCodError(parent);
        assertNotNull(wsCodError2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsCodError wsCodError3 = new WsCodError(parent, sibling);
        assertNotNull(wsCodError3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsCodError.getWsDis1());

        assertNotNull(wsCodError.getWsDis2());

    }

    // property wsDis1
    @Test
    void testGetterSetterWsDis1() {

        assertNotNull(wsCodError.getWsDis1());

        String vString = "A";
        wsCodError.setWsDis1(vString); // String
        assertEquals(asStr(vString), asStr(wsCodError.getWsDis1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCodError.setWsDis1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCodError.getWsDis1()));
    }

    // property wsDis2
    @Test
    void testGetterSetterWsDis2() {

        assertNotNull(wsCodError.getWsDis2());

        int vint = 123456789;
        wsCodError.setWsDis2(vint); // int
        assertEquals(asInt(vint), asInt(wsCodError.getWsDis2()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCodError.setWsDis2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCodError.getWsDis2()));
    }
}

