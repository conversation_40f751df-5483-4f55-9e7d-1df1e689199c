package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: CONTADORES.
 *
 */
class ContadoresTest {

    private Contadores contadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        contadores = new Contadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        contadores.setContadorProc(0);
        assertEquals(0, contadores.getContadorProc());

        contadores.setWsContador(0);
        assertEquals(0, contadores.getWsContador());

        contadores.setWsContTcna(0);
        assertEquals(0, contadores.getWsContTcna());

        contadores.setWsContTcna_1(0);
        assertEquals(0, contadores.getWsContTcna_1());

        contadores.setWsContTfla(0);
        assertEquals(0, contadores.getWsContTfla());

        contadores.setWsContTger(0);
        assertEquals(0, contadores.getWsContTger());

        contadores.setWsContTger_1(0);
        assertEquals(0, contadores.getWsContTger_1());

        contadores.setWsContTceo(0);
        assertEquals(0, contadores.getWsContTceo());

        contadores.setWsContTofn(0);
        assertEquals(0, contadores.getWsContTofn());

        contadores.setWsContTofn_1(0);
        assertEquals(0, contadores.getWsContTofn_1());

        contadores.setWsContAgt0(0);
        assertEquals(0, contadores.getWsContAgt0());

        contadores.setWsContTfin(0);
        assertEquals(0, contadores.getWsContTfin());

        contadores.setWsContFolios(0);
        assertEquals(0, contadores.getWsContFolios());

        contadores.setWsContGrabas(0);
        assertEquals(0, contadores.getWsContGrabas());

        assertNotNull(contadores.getWsEditLeidos());

        assertNotNull(contadores.getWsEditGrabado());

    }

    // property contadorProc
    @Test
    void testGetterSetterContadorProc() {

        assertNotNull(contadores.getContadorProc());

        int vint = 1234567;
        contadores.setContadorProc(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getContadorProc()));
    }

    // property wsContador
    @Test
    void testGetterSetterWsContador() {

        assertNotNull(contadores.getWsContador());

        int vint = 123;
        contadores.setWsContador(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContador()));
    }

    // property wsContTcna
    @Test
    void testGetterSetterWsContTcna() {

        assertNotNull(contadores.getWsContTcna());

        int vint = 123;
        contadores.setWsContTcna(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTcna()));
    }

    // property wsContTcna_1
    @Test
    void testGetterSetterWsContTcna_1() {

        assertNotNull(contadores.getWsContTcna_1());

        int vint = 123;
        contadores.setWsContTcna_1(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTcna_1()));
    }

    // property wsContTfla
    @Test
    void testGetterSetterWsContTfla() {

        assertNotNull(contadores.getWsContTfla());

        int vint = 12;
        contadores.setWsContTfla(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTfla()));
    }

    // property wsContTger
    @Test
    void testGetterSetterWsContTger() {

        assertNotNull(contadores.getWsContTger());

        int vint = 12;
        contadores.setWsContTger(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTger()));
    }

    // property wsContTger_1
    @Test
    void testGetterSetterWsContTger_1() {

        assertNotNull(contadores.getWsContTger_1());

        int vint = 123;
        contadores.setWsContTger_1(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTger_1()));
    }

    // property wsContTceo
    @Test
    void testGetterSetterWsContTceo() {

        assertNotNull(contadores.getWsContTceo());

        int vint = 12;
        contadores.setWsContTceo(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTceo()));
    }

    // property wsContTofn
    @Test
    void testGetterSetterWsContTofn() {

        assertNotNull(contadores.getWsContTofn());

        int vint = 12;
        contadores.setWsContTofn(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTofn()));
    }

    // property wsContTofn_1
    @Test
    void testGetterSetterWsContTofn_1() {

        assertNotNull(contadores.getWsContTofn_1());

        int vint = 123;
        contadores.setWsContTofn_1(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTofn_1()));
    }

    // property wsContAgt0
    @Test
    void testGetterSetterWsContAgt0() {

        assertNotNull(contadores.getWsContAgt0());

        int vint = 1234;
        contadores.setWsContAgt0(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContAgt0()));
    }

    // property wsContTfin
    @Test
    void testGetterSetterWsContTfin() {

        assertNotNull(contadores.getWsContTfin());

        int vint = 1234;
        contadores.setWsContTfin(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContTfin()));
    }

    // property wsContFolios
    @Test
    void testGetterSetterWsContFolios() {

        assertNotNull(contadores.getWsContFolios());

        int vint = 1234;
        contadores.setWsContFolios(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContFolios()));
    }

    // property wsContGrabas
    @Test
    void testGetterSetterWsContGrabas() {

        assertNotNull(contadores.getWsContGrabas());

        int vint = 1234;
        contadores.setWsContGrabas(vint); // int
        assertEquals(asInt(vint), asInt(contadores.getWsContGrabas()));
    }

    // property wsEditLeidos
    @Test
    void testGetterSetterWsEditLeidos() {

        assertNotNull(contadores.getWsEditLeidos());

        String vString = "   1230,0000000";
        contadores.setWsEditLeidos(vString); // String
        assertEquals(asStr(vString), asStr(contadores.getWsEditLeidos()));

        NumericVar vIValue = new NumericVar(15, 0);;
        vIValue.setValue("123456789012345");
        contadores.setWsEditLeidos(vIValue); // IValue
        // format("123456789012345", "ZZZZZZZ,ZZZ,ZZZ")
        assertNotNull(asStr(contadores.getWsEditLeidos()));
    }

    // property wsEditGrabado
    @Test
    void testGetterSetterWsEditGrabado() {

        assertNotNull(contadores.getWsEditGrabado());

        String vString = "   1230,0000000";
        contadores.setWsEditGrabado(vString); // String
        assertEquals(asStr(vString), asStr(contadores.getWsEditGrabado()));

        NumericVar vIValue = new NumericVar(15, 0);;
        vIValue.setValue("123456789012345");
        contadores.setWsEditGrabado(vIValue); // IValue
        // format("123456789012345", "ZZZZZZZ,ZZZ,ZZZ")
        assertNotNull(asStr(contadores.getWsEditGrabado()));
    }
}

