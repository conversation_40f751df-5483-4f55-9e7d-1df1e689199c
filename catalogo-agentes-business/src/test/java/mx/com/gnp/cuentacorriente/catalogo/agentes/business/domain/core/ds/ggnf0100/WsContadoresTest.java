package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CONTADORES.
 *
 */
class WsContadoresTest {

    private WsContadores wsContadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsContadores = new WsContadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsContadores.setWsGrabados(0);
        assertEquals(0, wsContadores.getWsGrabados());

        wsContadores.setWsContador(0);
        assertEquals(0, wsContadores.getWsContador());

        wsContadores.setWsCuantos(0);
        assertEquals(0, wsContadores.getWsCuantos());

    }

    // property wsGrabados
    @Test
    void testGetterSetterWsGrabados() {

        assertNotNull(wsContadores.getWsGrabados());

        int vint = 1234567;
        wsContadores.setWsGrabados(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsGrabados()));
    }

    // property wsContador
    @Test
    void testGetterSetterWsContador() {

        assertNotNull(wsContadores.getWsContador());

        int vint = 1234567;
        wsContadores.setWsContador(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsContador()));
    }

    // property wsCuantos
    @Test
    void testGetterSetterWsCuantos() {

        assertNotNull(wsContadores.getWsCuantos());

        int vint = 1234567;
        wsContadores.setWsCuantos(vint); // int
        assertEquals(asInt(vint), asInt(wsContadores.getWsCuantos()));
    }
}

