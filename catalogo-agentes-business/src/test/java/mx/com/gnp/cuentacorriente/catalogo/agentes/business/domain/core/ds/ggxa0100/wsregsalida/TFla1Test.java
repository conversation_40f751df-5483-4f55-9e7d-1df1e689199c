package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-FLA1.
 *
 */
class TFla1Test {

    private TFla1 tFla1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tFla1 = new TFla1(1);
        tFla1.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TFla1 tFla12 = new TFla1(parent, 1);
        assertNotNull(tFla12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TFla1 tFla13 = new TFla1(parent, sibling, 1);
        assertNotNull(tFla13);
    }

    @Test
    void testInitialize() {

    }

    // property tFla0Elem
    @Test
    void testGetterSetterTFla0Elem() {

        assertNotNull(tFla1.getTFla0Elem());
    }

    // property sFlaFlaNum1
    @Test
    void testGetterSetterSFlaFlaNum1() {

        // Property: tFla0Elem.sFlaFlaNum1 -> 20 S-FLA-FLA-NUM1 X(08)
        assertNotNull(tFla1.getSFlaFlaNum1());
        String vString = "ABCabc12";
        tFla1.setSFlaFlaNum1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tFla1.setSFlaFlaNum1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sFlaLimitad1
    @Test
    void testGetterSetterSFlaLimitad1() {

        // Property: tFla0Elem.sFlaLimitad1 -> 20 S-FLA-LIMITAD1 X(01)
        assertNotNull(tFla1.getSFlaLimitad1());
        String vString = "A";
        tFla1.setSFlaLimitad1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tFla1.setSFlaLimitad1(vIAlphanumericValue); // IAlphanumericValue
    }
}

