package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DATOS-ABEND.
 *
 */
class WsDatosAbendTest {

    private WsDatosAbend wsDatosAbend;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDatosAbend = new WsDatosAbend();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDatosAbend.setWsPrograma("GGNF0800");
        assertEquals("GGNF0800", wsDatosAbend.getWsPrograma());

        wsDatosAbend.setWsTabla("");
        assertEquals("", wsDatosAbend.getWsTabla());

        wsDatosAbend.setWsProcesoLlama("");
        assertEquals("", wsDatosAbend.getWsProcesoLlama());

        wsDatosAbend.setWsProcesoEjecuta("");
        assertEquals("", wsDatosAbend.getWsProcesoEjecuta());

        wsDatosAbend.setWsInstruccion("");
        assertEquals("", wsDatosAbend.getWsInstruccion());

    }

    // property wsPrograma
    @Test
    void testGetterSetterWsPrograma() {

        assertNotNull(wsDatosAbend.getWsPrograma());

        String vString = "ABCabc1230";
        wsDatosAbend.setWsPrograma(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosAbend.getWsPrograma()));
    }

    // property wsTabla
    @Test
    void testGetterSetterWsTabla() {

        assertNotNull(wsDatosAbend.getWsTabla());

        String vString = "ABCabc1230";
        wsDatosAbend.setWsTabla(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosAbend.getWsTabla()));
    }

    // property wsProcesoLlama
    @Test
    void testGetterSetterWsProcesoLlama() {

        assertNotNull(wsDatosAbend.getWsProcesoLlama());

        String vString = "ABCabc1230";
        wsDatosAbend.setWsProcesoLlama(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosAbend.getWsProcesoLlama()));
    }

    // property wsProcesoEjecuta
    @Test
    void testGetterSetterWsProcesoEjecuta() {

        assertNotNull(wsDatosAbend.getWsProcesoEjecuta());

        String vString = "ABCabc1230";
        wsDatosAbend.setWsProcesoEjecuta(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosAbend.getWsProcesoEjecuta()));
    }

    // property wsInstruccion
    @Test
    void testGetterSetterWsInstruccion() {

        assertNotNull(wsDatosAbend.getWsInstruccion());

        String vString = "ABCabc1230";
        wsDatosAbend.setWsInstruccion(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosAbend.getWsInstruccion()));
    }
}

