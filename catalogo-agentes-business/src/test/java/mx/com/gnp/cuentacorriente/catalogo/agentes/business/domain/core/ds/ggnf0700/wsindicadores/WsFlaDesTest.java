package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FLA-DES.
 *
 */
class WsFlaDesTest {

    private WsFlaDes wsFlaDes;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFlaDes = new WsFlaDes();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsFlaDes.setWsFlaDesDos("");
        assertEquals("", wsFlaDes.getWsFlaDesDos());

        wsFlaDes.setWsFlaDesResto("");
        assertEquals("", wsFlaDes.getWsFlaDesResto());

    }

    // property wsFlaDesDos
    @Test
    void testGetterSetterWsFlaDesDos() {

        assertNotNull(wsFlaDes.getWsFlaDesDos());

        String vString = "AB";
        wsFlaDes.setWsFlaDesDos(vString); // String
        assertEquals(asStr(vString), asStr(wsFlaDes.getWsFlaDesDos()));
    }

    // property wsFlaDesResto
    @Test
    void testGetterSetterWsFlaDesResto() {

        assertNotNull(wsFlaDes.getWsFlaDesResto());

        String vString = "ABCabc1230";
        wsFlaDes.setWsFlaDesResto(vString); // String
        assertEquals(asStr(vString), asStr(wsFlaDes.getWsFlaDesResto()));
    }
}

