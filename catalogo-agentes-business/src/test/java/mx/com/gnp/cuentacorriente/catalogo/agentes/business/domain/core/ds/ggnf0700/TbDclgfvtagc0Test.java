package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TB-DCLGFVTAGC0.
 *
 */
class TbDclgfvtagc0Test {

    private TbDclgfvtagc0 tbDclgfvtagc0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tbDclgfvtagc0 = new TbDclgfvtagc0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TbDclgfvtagc0 tbDclgfvtagc02 = new TbDclgfvtagc0(parent);
        assertNotNull(tbDclgfvtagc02);
        AlphanumericVar sibling = new AlphanumericVar(parent, 333);
        TbDclgfvtagc0 tbDclgfvtagc03 = new TbDclgfvtagc0(parent, sibling);
        assertNotNull(tbDclgfvtagc03);
    }

    @Test
    void testInitialize() {
        assertNotNull(tbDclgfvtagc0.getTtFlaNum());

        assertNotNull(tbDclgfvtagc0.getTtAgtApeMat());

        assertNotNull(tbDclgfvtagc0.getTtAgc02());

        assertNotNull(tbDclgfvtagc0.getTtAgtApePat());

        assertNotNull(tbDclgfvtagc0.getTtAgc03());

        assertNotNull(tbDclgfvtagc0.getTtAgtNom());

        assertNotNull(tbDclgfvtagc0.getTtAgc04());

        assertNotNull(tbDclgfvtagc0.getTtAgtRazSoc());

        assertNotNull(tbDclgfvtagc0.getTtAgc05());

        assertNotNull(tbDclgfvtagc0.getTtEmpCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc06());

        assertNotNull(tbDclgfvtagc0.getTtEsaCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc07());

        assertNotNull(tbDclgfvtagc0.getTtOfnCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc08());

        assertNotNull(tbDclgfvtagc0.getTtOrvCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc09());

        assertNotNull(tbDclgfvtagc0.getTtPefCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc10());

        assertNotNull(tbDclgfvtagc0.getTtUsuCveAct());

        assertNotNull(tbDclgfvtagc0.getTtAgc11());

        assertNotNull(tbDclgfvtagc0.getTtFecUltAct());

        assertNotNull(tbDclgfvtagc0.getTtAgc12());

        assertNotNull(tbDclgfvtagc0.getTtAgtIdr());

        assertNotNull(tbDclgfvtagc0.getTtAgc13());

        assertNotNull(tbDclgfvtagc0.getTtCnaNum());

        assertNotNull(tbDclgfvtagc0.getTtAgc14());

        assertNotNull(tbDclgfvtagc0.getTtTfpCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc15());

        assertNotNull(tbDclgfvtagc0.getTtCnaIndGer());

        assertNotNull(tbDclgfvtagc0.getTtAgc16());

        assertNotNull(tbDclgfvtagc0.getTtMteCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc17());

        assertNotNull(tbDclgfvtagc0.getTtFlaFecMotEss());

        assertNotNull(tbDclgfvtagc0.getTtAgc18());

        assertNotNull(tbDclgfvtagc0.getTtFlaNumGte());

        assertNotNull(tbDclgfvtagc0.getTtCnaGerZonEnv());

        assertNotNull(tbDclgfvtagc0.getTtAgc20());

        assertNotNull(tbDclgfvtagc0.getTtDrcIdrAgt());

        assertNotNull(tbDclgfvtagc0.getTtAgcxx());

        assertNotNull(tbDclgfvtagc0.getTtDrcIdrGte());

        assertNotNull(tbDclgfvtagc0.getTtAgcyy());

        assertNotNull(tbDclgfvtagc0.getTtFlaDes());

        assertNotNull(tbDclgfvtagc0.getTtAgc21());

        assertNotNull(tbDclgfvtagc0.getTtAgtTraEsp());

        assertNotNull(tbDclgfvtagc0.getTtAgc22());

        assertNotNull(tbDclgfvtagc0.getTtCnaFecCnx());

        assertNotNull(tbDclgfvtagc0.getTtAgc23());

        assertNotNull(tbDclgfvtagc0.getTtGnaCve());

        assertNotNull(tbDclgfvtagc0.getTtAgc24());

        assertNotNull(tbDclgfvtagc0.getTtEsaCveFla());

        assertNotNull(tbDclgfvtagc0.getTtAgc25());

        assertNotNull(tbDclgfvtagc0.getTtFlaGerZon());

        assertNotNull(tbDclgfvtagc0.getTtAgc26());

        assertNotNull(tbDclgfvtagc0.getTtOfnCveCna());

        assertNotNull(tbDclgfvtagc0.getTtAgc27());

        assertNotNull(tbDclgfvtagc0.getTtAgtIdrGte());

        assertNotNull(tbDclgfvtagc0.getTtAgc28());

    }

    // property ttFlaNum
    @Test
    void testGetterSetterTtFlaNum() {

        assertNotNull(tbDclgfvtagc0.getTtFlaNum());

        String vString = "ABCabc12";
        tbDclgfvtagc0.setTtFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtFlaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tbDclgfvtagc0.setTtFlaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtFlaNum()));
    }

    // property ttAgtApeMat
    @Test
    void testGetterSetterTtAgtApeMat() {

        assertNotNull(tbDclgfvtagc0.getTtAgtApeMat());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtAgtApeMat(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgtApeMat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtAgtApeMat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgtApeMat()));
    }

    // property ttAgc02
    @Test
    void testGetterSetterTtAgc02() {

        assertNotNull(tbDclgfvtagc0.getTtAgc02());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc02(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc02()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc02(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc02()));
    }

    // property ttAgtApePat
    @Test
    void testGetterSetterTtAgtApePat() {

        assertNotNull(tbDclgfvtagc0.getTtAgtApePat());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtAgtApePat(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgtApePat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtAgtApePat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgtApePat()));
    }

    // property ttAgc03
    @Test
    void testGetterSetterTtAgc03() {

        assertNotNull(tbDclgfvtagc0.getTtAgc03());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc03(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc03()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc03(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc03()));
    }

    // property ttAgtNom
    @Test
    void testGetterSetterTtAgtNom() {

        assertNotNull(tbDclgfvtagc0.getTtAgtNom());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtAgtNom(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgtNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtAgtNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgtNom()));
    }

    // property ttAgc04
    @Test
    void testGetterSetterTtAgc04() {

        assertNotNull(tbDclgfvtagc0.getTtAgc04());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc04(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc04()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc04(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc04()));
    }

    // property ttAgtRazSoc
    @Test
    void testGetterSetterTtAgtRazSoc() {

        assertNotNull(tbDclgfvtagc0.getTtAgtRazSoc());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtAgtRazSoc(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgtRazSoc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtAgtRazSoc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgtRazSoc()));
    }

    // property ttAgc05
    @Test
    void testGetterSetterTtAgc05() {

        assertNotNull(tbDclgfvtagc0.getTtAgc05());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc05(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc05()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc05(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc05()));
    }

    // property ttEmpCve
    @Test
    void testGetterSetterTtEmpCve() {

        assertNotNull(tbDclgfvtagc0.getTtEmpCve());

        String vString = "ABCab";
        tbDclgfvtagc0.setTtEmpCve(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtEmpCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tbDclgfvtagc0.setTtEmpCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtEmpCve()));
    }

    // property ttAgc06
    @Test
    void testGetterSetterTtAgc06() {

        assertNotNull(tbDclgfvtagc0.getTtAgc06());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc06(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc06()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc06(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc06()));
    }

    // property ttEsaCve
    @Test
    void testGetterSetterTtEsaCve() {

        assertNotNull(tbDclgfvtagc0.getTtEsaCve());

        int vint = 1234;
        tbDclgfvtagc0.setTtEsaCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtEsaCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtEsaCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtEsaCve()));
    }

    // property ttAgc07
    @Test
    void testGetterSetterTtAgc07() {

        assertNotNull(tbDclgfvtagc0.getTtAgc07());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc07(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc07()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc07(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc07()));
    }

    // property ttOfnCve
    @Test
    void testGetterSetterTtOfnCve() {

        assertNotNull(tbDclgfvtagc0.getTtOfnCve());

        int vint = 1234;
        tbDclgfvtagc0.setTtOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtOfnCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtOfnCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtOfnCve()));
    }

    // property ttAgc08
    @Test
    void testGetterSetterTtAgc08() {

        assertNotNull(tbDclgfvtagc0.getTtAgc08());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc08(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc08()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc08(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc08()));
    }

    // property ttOrvCve
    @Test
    void testGetterSetterTtOrvCve() {

        assertNotNull(tbDclgfvtagc0.getTtOrvCve());

        int vint = 1234;
        tbDclgfvtagc0.setTtOrvCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtOrvCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtOrvCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtOrvCve()));
    }

    // property ttAgc09
    @Test
    void testGetterSetterTtAgc09() {

        assertNotNull(tbDclgfvtagc0.getTtAgc09());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc09(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc09()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc09(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc09()));
    }

    // property ttPefCve
    @Test
    void testGetterSetterTtPefCve() {

        assertNotNull(tbDclgfvtagc0.getTtPefCve());

        int vint = 1234;
        tbDclgfvtagc0.setTtPefCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtPefCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtPefCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtPefCve()));
    }

    // property ttAgc10
    @Test
    void testGetterSetterTtAgc10() {

        assertNotNull(tbDclgfvtagc0.getTtAgc10());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc10(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc10()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc10(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc10()));
    }

    // property ttUsuCveAct
    @Test
    void testGetterSetterTtUsuCveAct() {

        assertNotNull(tbDclgfvtagc0.getTtUsuCveAct());

        String vString = "ABCabc12";
        tbDclgfvtagc0.setTtUsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtUsuCveAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tbDclgfvtagc0.setTtUsuCveAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtUsuCveAct()));
    }

    // property ttAgc11
    @Test
    void testGetterSetterTtAgc11() {

        assertNotNull(tbDclgfvtagc0.getTtAgc11());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc11(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc11()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc11(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc11()));
    }

    // property ttFecUltAct
    @Test
    void testGetterSetterTtFecUltAct() {

        assertNotNull(tbDclgfvtagc0.getTtFecUltAct());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtFecUltAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtFecUltAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtFecUltAct()));
    }

    // property ttAgc12
    @Test
    void testGetterSetterTtAgc12() {

        assertNotNull(tbDclgfvtagc0.getTtAgc12());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc12(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc12()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc12(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc12()));
    }

    // property ttAgtIdr
    @Test
    void testGetterSetterTtAgtIdr() {

        assertNotNull(tbDclgfvtagc0.getTtAgtIdr());

        int vint = 123456789;
        tbDclgfvtagc0.setTtAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtAgtIdr()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        tbDclgfvtagc0.setTtAgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtAgtIdr()));
    }

    // property ttAgc13
    @Test
    void testGetterSetterTtAgc13() {

        assertNotNull(tbDclgfvtagc0.getTtAgc13());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc13(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc13()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc13(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc13()));
    }

    // property ttCnaNum
    @Test
    void testGetterSetterTtCnaNum() {

        assertNotNull(tbDclgfvtagc0.getTtCnaNum());

        int vint = 1234;
        tbDclgfvtagc0.setTtCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtCnaNum()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtCnaNum(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtCnaNum()));
    }

    // property ttAgc14
    @Test
    void testGetterSetterTtAgc14() {

        assertNotNull(tbDclgfvtagc0.getTtAgc14());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc14(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc14()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc14(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc14()));
    }

    // property ttTfpCve
    @Test
    void testGetterSetterTtTfpCve() {

        assertNotNull(tbDclgfvtagc0.getTtTfpCve());

        int vint = 1234;
        tbDclgfvtagc0.setTtTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtTfpCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtTfpCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtTfpCve()));
    }

    // property ttAgc15
    @Test
    void testGetterSetterTtAgc15() {

        assertNotNull(tbDclgfvtagc0.getTtAgc15());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc15(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc15()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc15(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc15()));
    }

    // property ttCnaIndGer
    @Test
    void testGetterSetterTtCnaIndGer() {

        assertNotNull(tbDclgfvtagc0.getTtCnaIndGer());

        String vString = "A";
        tbDclgfvtagc0.setTtCnaIndGer(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtCnaIndGer()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtCnaIndGer(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtCnaIndGer()));
    }

    // property ttAgc16
    @Test
    void testGetterSetterTtAgc16() {

        assertNotNull(tbDclgfvtagc0.getTtAgc16());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc16(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc16()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc16(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc16()));
    }

    // property ttMteCve
    @Test
    void testGetterSetterTtMteCve() {

        assertNotNull(tbDclgfvtagc0.getTtMteCve());

        int vint = 1234;
        tbDclgfvtagc0.setTtMteCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtMteCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtMteCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtMteCve()));
    }

    // property ttAgc17
    @Test
    void testGetterSetterTtAgc17() {

        assertNotNull(tbDclgfvtagc0.getTtAgc17());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc17(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc17()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc17(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc17()));
    }

    // property ttFlaFecMotEss
    @Test
    void testGetterSetterTtFlaFecMotEss() {

        assertNotNull(tbDclgfvtagc0.getTtFlaFecMotEss());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtFlaFecMotEss(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtFlaFecMotEss()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtFlaFecMotEss(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtFlaFecMotEss()));
    }

    // property ttAgc18
    @Test
    void testGetterSetterTtAgc18() {

        assertNotNull(tbDclgfvtagc0.getTtAgc18());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc18(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc18()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc18(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc18()));
    }

    // property ttFlaNumGte
    @Test
    void testGetterSetterTtFlaNumGte() {

        assertNotNull(tbDclgfvtagc0.getTtFlaNumGte());

        String vString = "ABCabc12";
        tbDclgfvtagc0.setTtFlaNumGte(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtFlaNumGte()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tbDclgfvtagc0.setTtFlaNumGte(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtFlaNumGte()));
    }

    // property ttCnaGerZonEnv
    @Test
    void testGetterSetterTtCnaGerZonEnv() {

        assertNotNull(tbDclgfvtagc0.getTtCnaGerZonEnv());

        int vint = 1234;
        tbDclgfvtagc0.setTtCnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtCnaGerZonEnv()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtCnaGerZonEnv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtCnaGerZonEnv()));
    }

    // property ttAgc20
    @Test
    void testGetterSetterTtAgc20() {

        assertNotNull(tbDclgfvtagc0.getTtAgc20());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc20(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc20()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc20(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc20()));
    }

    // property ttDrcIdrAgt
    @Test
    void testGetterSetterTtDrcIdrAgt() {

        assertNotNull(tbDclgfvtagc0.getTtDrcIdrAgt());

        String vString = "A";
        tbDclgfvtagc0.setTtDrcIdrAgt(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtDrcIdrAgt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtDrcIdrAgt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtDrcIdrAgt()));
    }

    // property ttAgcxx
    @Test
    void testGetterSetterTtAgcxx() {

        assertNotNull(tbDclgfvtagc0.getTtAgcxx());

        String vString = "A";
        tbDclgfvtagc0.setTtAgcxx(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgcxx()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgcxx(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgcxx()));
    }

    // property ttDrcIdrGte
    @Test
    void testGetterSetterTtDrcIdrGte() {

        assertNotNull(tbDclgfvtagc0.getTtDrcIdrGte());

        String vString = "A";
        tbDclgfvtagc0.setTtDrcIdrGte(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtDrcIdrGte()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtDrcIdrGte(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtDrcIdrGte()));
    }

    // property ttAgcyy
    @Test
    void testGetterSetterTtAgcyy() {

        assertNotNull(tbDclgfvtagc0.getTtAgcyy());

        String vString = "A";
        tbDclgfvtagc0.setTtAgcyy(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgcyy()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgcyy(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgcyy()));
    }

    // property ttFlaDes
    @Test
    void testGetterSetterTtFlaDes() {

        assertNotNull(tbDclgfvtagc0.getTtFlaDes());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtFlaDes(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtFlaDes()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtFlaDes(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtFlaDes()));
    }

    // property ttAgc21
    @Test
    void testGetterSetterTtAgc21() {

        assertNotNull(tbDclgfvtagc0.getTtAgc21());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc21(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc21()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc21(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc21()));
    }

    // property ttAgtTraEsp
    @Test
    void testGetterSetterTtAgtTraEsp() {

        assertNotNull(tbDclgfvtagc0.getTtAgtTraEsp());

        String vString = "A";
        tbDclgfvtagc0.setTtAgtTraEsp(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgtTraEsp()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgtTraEsp(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgtTraEsp()));
    }

    // property ttAgc22
    @Test
    void testGetterSetterTtAgc22() {

        assertNotNull(tbDclgfvtagc0.getTtAgc22());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc22(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc22()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc22(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc22()));
    }

    // property ttCnaFecCnx
    @Test
    void testGetterSetterTtCnaFecCnx() {

        assertNotNull(tbDclgfvtagc0.getTtCnaFecCnx());

        String vString = "ABCabc1230";
        tbDclgfvtagc0.setTtCnaFecCnx(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtCnaFecCnx()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtagc0.setTtCnaFecCnx(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtCnaFecCnx()));
    }

    // property ttAgc23
    @Test
    void testGetterSetterTtAgc23() {

        assertNotNull(tbDclgfvtagc0.getTtAgc23());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc23(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc23()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc23(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc23()));
    }

    // property ttGnaCve
    @Test
    void testGetterSetterTtGnaCve() {

        assertNotNull(tbDclgfvtagc0.getTtGnaCve());

        int vint = 1234;
        tbDclgfvtagc0.setTtGnaCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtGnaCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtGnaCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtGnaCve()));
    }

    // property ttAgc24
    @Test
    void testGetterSetterTtAgc24() {

        assertNotNull(tbDclgfvtagc0.getTtAgc24());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc24(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc24()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc24(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc24()));
    }

    // property ttEsaCveFla
    @Test
    void testGetterSetterTtEsaCveFla() {

        assertNotNull(tbDclgfvtagc0.getTtEsaCveFla());

        int vint = 1234;
        tbDclgfvtagc0.setTtEsaCveFla(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtEsaCveFla()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtEsaCveFla(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtEsaCveFla()));
    }

    // property ttAgc25
    @Test
    void testGetterSetterTtAgc25() {

        assertNotNull(tbDclgfvtagc0.getTtAgc25());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc25(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc25()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc25(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc25()));
    }

    // property ttFlaGerZon
    @Test
    void testGetterSetterTtFlaGerZon() {

        assertNotNull(tbDclgfvtagc0.getTtFlaGerZon());

        int vint = 1234;
        tbDclgfvtagc0.setTtFlaGerZon(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtFlaGerZon()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtFlaGerZon(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtFlaGerZon()));
    }

    // property ttAgc26
    @Test
    void testGetterSetterTtAgc26() {

        assertNotNull(tbDclgfvtagc0.getTtAgc26());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc26(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc26()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc26(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc26()));
    }

    // property ttOfnCveCna
    @Test
    void testGetterSetterTtOfnCveCna() {

        assertNotNull(tbDclgfvtagc0.getTtOfnCveCna());

        int vint = 1234;
        tbDclgfvtagc0.setTtOfnCveCna(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtOfnCveCna()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtagc0.setTtOfnCveCna(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtOfnCveCna()));
    }

    // property ttAgc27
    @Test
    void testGetterSetterTtAgc27() {

        assertNotNull(tbDclgfvtagc0.getTtAgc27());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc27(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc27()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc27(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc27()));
    }

    // property ttAgtIdrGte
    @Test
    void testGetterSetterTtAgtIdrGte() {

        assertNotNull(tbDclgfvtagc0.getTtAgtIdrGte());

        int vint = 123456789;
        tbDclgfvtagc0.setTtAgtIdrGte(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtagc0.getTtAgtIdrGte()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        tbDclgfvtagc0.setTtAgtIdrGte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtagc0.getTtAgtIdrGte()));
    }

    // property ttAgc28
    @Test
    void testGetterSetterTtAgc28() {

        assertNotNull(tbDclgfvtagc0.getTtAgc28());

        String vString = "A";
        tbDclgfvtagc0.setTtAgc28(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtagc0.getTtAgc28()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtagc0.setTtAgc28(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtagc0.getTtAgc28()));
    }
}

