package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TOTALES.
 *
 */
class WsTotalesTest {

    private WsTotales wsTotales;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTotales = new WsTotales();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsTotales.setTotFlaLeidos(0);
        assertEquals(0, wsTotales.getTotFlaLeidos());

        wsTotales.setTotFdcLeidos(0);
        assertEquals(0, wsTotales.getTotFdcLeidos());

        wsTotales.setTotCafLeidos(0);
        assertEquals(0, wsTotales.getTotCafLeidos());

        wsTotales.setTotCafGrabados(0);
        assertEquals(0, wsTotales.getTotCafGrabados());

        wsTotales.setTotCafDuplicados(0);
        assertEquals(0, wsTotales.getTotCafDuplicados());

    }

    // property totFlaLeidos
    @Test
    void testGetterSetterTotFlaLeidos() {

        assertNotNull(wsTotales.getTotFlaLeidos());

        int vint = 123456;
        wsTotales.setTotFlaLeidos(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotFlaLeidos()));
    }

    // property totFdcLeidos
    @Test
    void testGetterSetterTotFdcLeidos() {

        assertNotNull(wsTotales.getTotFdcLeidos());

        int vint = 123456;
        wsTotales.setTotFdcLeidos(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotFdcLeidos()));
    }

    // property totCafLeidos
    @Test
    void testGetterSetterTotCafLeidos() {

        assertNotNull(wsTotales.getTotCafLeidos());

        int vint = 123456;
        wsTotales.setTotCafLeidos(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotCafLeidos()));
    }

    // property totCafGrabados
    @Test
    void testGetterSetterTotCafGrabados() {

        assertNotNull(wsTotales.getTotCafGrabados());

        int vint = 123456;
        wsTotales.setTotCafGrabados(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotCafGrabados()));
    }

    // property totCafDuplicados
    @Test
    void testGetterSetterTotCafDuplicados() {

        assertNotNull(wsTotales.getTotCafDuplicados());

        int vint = 123456;
        wsTotales.setTotCafDuplicados(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotCafDuplicados()));
    }
}

