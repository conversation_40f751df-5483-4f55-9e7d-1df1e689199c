package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.fechaformatiii;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: FECHA-FORMAT-MMAA.
 *
 */
class FechaFormatMmaaTest {

    private FechaFormatMmaa fechaFormatMmaa;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        fechaFormatMmaa = new FechaFormatMmaa();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        fechaFormatMmaa.setFechaFormatFiller(0);
        assertEquals(0, fechaFormatMmaa.getFechaFormatFiller());

        fechaFormatMmaa.setFechaFormatAaaa(0);
        assertEquals(0, fechaFormatMmaa.getFechaFormatAaaa());

        fechaFormatMmaa.setFechaFormatMmIii(0);
        assertEquals(0, fechaFormatMmaa.getFechaFormatMmIii());

    }

    // property fechaFormatFiller
    @Test
    void testGetterSetterFechaFormatFiller() {

        assertNotNull(fechaFormatMmaa.getFechaFormatFiller());

        int vint = 12;
        fechaFormatMmaa.setFechaFormatFiller(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatMmaa.getFechaFormatFiller()));
    }

    // property fechaFormatAaaa
    @Test
    void testGetterSetterFechaFormatAaaa() {

        assertNotNull(fechaFormatMmaa.getFechaFormatAaaa());

        int vint = 1234;
        fechaFormatMmaa.setFechaFormatAaaa(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatMmaa.getFechaFormatAaaa()));
    }

    // property fechaFormatMmIii
    @Test
    void testGetterSetterFechaFormatMmIii() {

        assertNotNull(fechaFormatMmaa.getFechaFormatMmIii());

        int vint = 12;
        fechaFormatMmaa.setFechaFormatMmIii(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatMmaa.getFechaFormatMmIii()));
    }
}

