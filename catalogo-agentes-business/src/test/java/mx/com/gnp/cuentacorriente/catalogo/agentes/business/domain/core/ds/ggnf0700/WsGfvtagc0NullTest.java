package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTAGC0-NULL.
 *
 */
class WsGfvtagc0NullTest {

    private WsGfvtagc0Null wsGfvtagc0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtagc0Null = new WsGfvtagc0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtagc0Null.setAgc01(0);
        assertEquals(0, wsGfvtagc0Null.getAgc01());

        wsGfvtagc0Null.setAgc02(0);
        assertEquals(0, wsGfvtagc0Null.getAgc02());

        wsGfvtagc0Null.setAgc03(0);
        assertEquals(0, wsGfvtagc0Null.getAgc03());

        wsGfvtagc0Null.setAgc04(0);
        assertEquals(0, wsGfvtagc0Null.getAgc04());

        wsGfvtagc0Null.setAgc05(0);
        assertEquals(0, wsGfvtagc0Null.getAgc05());

        wsGfvtagc0Null.setAgc06(0);
        assertEquals(0, wsGfvtagc0Null.getAgc06());

        wsGfvtagc0Null.setAgc07(0);
        assertEquals(0, wsGfvtagc0Null.getAgc07());

        wsGfvtagc0Null.setAgc08(0);
        assertEquals(0, wsGfvtagc0Null.getAgc08());

        wsGfvtagc0Null.setAgc09(0);
        assertEquals(0, wsGfvtagc0Null.getAgc09());

        wsGfvtagc0Null.setAgc10(0);
        assertEquals(0, wsGfvtagc0Null.getAgc10());

        wsGfvtagc0Null.setAgc11(0);
        assertEquals(0, wsGfvtagc0Null.getAgc11());

        wsGfvtagc0Null.setAgc12(0);
        assertEquals(0, wsGfvtagc0Null.getAgc12());

        wsGfvtagc0Null.setAgc13(0);
        assertEquals(0, wsGfvtagc0Null.getAgc13());

        wsGfvtagc0Null.setAgc14(0);
        assertEquals(0, wsGfvtagc0Null.getAgc14());

        wsGfvtagc0Null.setAgc15(0);
        assertEquals(0, wsGfvtagc0Null.getAgc15());

        wsGfvtagc0Null.setAgc16(0);
        assertEquals(0, wsGfvtagc0Null.getAgc16());

        wsGfvtagc0Null.setAgc17(0);
        assertEquals(0, wsGfvtagc0Null.getAgc17());

        wsGfvtagc0Null.setAgc18(0);
        assertEquals(0, wsGfvtagc0Null.getAgc18());

        wsGfvtagc0Null.setAgc19(0);
        assertEquals(0, wsGfvtagc0Null.getAgc19());

        wsGfvtagc0Null.setAgc20(0);
        assertEquals(0, wsGfvtagc0Null.getAgc20());

        wsGfvtagc0Null.setAgc21(0);
        assertEquals(0, wsGfvtagc0Null.getAgc21());

        wsGfvtagc0Null.setAgc22(0);
        assertEquals(0, wsGfvtagc0Null.getAgc22());

        wsGfvtagc0Null.setAgc23(0);
        assertEquals(0, wsGfvtagc0Null.getAgc23());

        wsGfvtagc0Null.setAgc24(0);
        assertEquals(0, wsGfvtagc0Null.getAgc24());

        wsGfvtagc0Null.setAgc25(0);
        assertEquals(0, wsGfvtagc0Null.getAgc25());

        wsGfvtagc0Null.setAgc26(0);
        assertEquals(0, wsGfvtagc0Null.getAgc26());

        wsGfvtagc0Null.setAgc27(0);
        assertEquals(0, wsGfvtagc0Null.getAgc27());

        wsGfvtagc0Null.setAgc28(0);
        assertEquals(0, wsGfvtagc0Null.getAgc28());

    }

    // property agc01
    @Test
    void testGetterSetterAgc01() {

        assertNotNull(wsGfvtagc0Null.getAgc01());

        int vint = 1234;
        wsGfvtagc0Null.setAgc01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc01()));
    }

    // property agc02
    @Test
    void testGetterSetterAgc02() {

        assertNotNull(wsGfvtagc0Null.getAgc02());

        int vint = 1234;
        wsGfvtagc0Null.setAgc02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc02()));
    }

    // property agc03
    @Test
    void testGetterSetterAgc03() {

        assertNotNull(wsGfvtagc0Null.getAgc03());

        int vint = 1234;
        wsGfvtagc0Null.setAgc03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc03()));
    }

    // property agc04
    @Test
    void testGetterSetterAgc04() {

        assertNotNull(wsGfvtagc0Null.getAgc04());

        int vint = 1234;
        wsGfvtagc0Null.setAgc04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc04()));
    }

    // property agc05
    @Test
    void testGetterSetterAgc05() {

        assertNotNull(wsGfvtagc0Null.getAgc05());

        int vint = 1234;
        wsGfvtagc0Null.setAgc05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc05()));
    }

    // property agc06
    @Test
    void testGetterSetterAgc06() {

        assertNotNull(wsGfvtagc0Null.getAgc06());

        int vint = 1234;
        wsGfvtagc0Null.setAgc06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc06()));
    }

    // property agc07
    @Test
    void testGetterSetterAgc07() {

        assertNotNull(wsGfvtagc0Null.getAgc07());

        int vint = 1234;
        wsGfvtagc0Null.setAgc07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc07()));
    }

    // property agc08
    @Test
    void testGetterSetterAgc08() {

        assertNotNull(wsGfvtagc0Null.getAgc08());

        int vint = 1234;
        wsGfvtagc0Null.setAgc08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc08()));
    }

    // property agc09
    @Test
    void testGetterSetterAgc09() {

        assertNotNull(wsGfvtagc0Null.getAgc09());

        int vint = 1234;
        wsGfvtagc0Null.setAgc09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc09()));
    }

    // property agc10
    @Test
    void testGetterSetterAgc10() {

        assertNotNull(wsGfvtagc0Null.getAgc10());

        int vint = 1234;
        wsGfvtagc0Null.setAgc10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc10()));
    }

    // property agc11
    @Test
    void testGetterSetterAgc11() {

        assertNotNull(wsGfvtagc0Null.getAgc11());

        int vint = 1234;
        wsGfvtagc0Null.setAgc11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc11()));
    }

    // property agc12
    @Test
    void testGetterSetterAgc12() {

        assertNotNull(wsGfvtagc0Null.getAgc12());

        int vint = 1234;
        wsGfvtagc0Null.setAgc12(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc12()));
    }

    // property agc13
    @Test
    void testGetterSetterAgc13() {

        assertNotNull(wsGfvtagc0Null.getAgc13());

        int vint = 1234;
        wsGfvtagc0Null.setAgc13(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc13()));
    }

    // property agc14
    @Test
    void testGetterSetterAgc14() {

        assertNotNull(wsGfvtagc0Null.getAgc14());

        int vint = 1234;
        wsGfvtagc0Null.setAgc14(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc14()));
    }

    // property agc15
    @Test
    void testGetterSetterAgc15() {

        assertNotNull(wsGfvtagc0Null.getAgc15());

        int vint = 1234;
        wsGfvtagc0Null.setAgc15(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc15()));
    }

    // property agc16
    @Test
    void testGetterSetterAgc16() {

        assertNotNull(wsGfvtagc0Null.getAgc16());

        int vint = 1234;
        wsGfvtagc0Null.setAgc16(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc16()));
    }

    // property agc17
    @Test
    void testGetterSetterAgc17() {

        assertNotNull(wsGfvtagc0Null.getAgc17());

        int vint = 1234;
        wsGfvtagc0Null.setAgc17(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc17()));
    }

    // property agc18
    @Test
    void testGetterSetterAgc18() {

        assertNotNull(wsGfvtagc0Null.getAgc18());

        int vint = 1234;
        wsGfvtagc0Null.setAgc18(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc18()));
    }

    // property agc19
    @Test
    void testGetterSetterAgc19() {

        assertNotNull(wsGfvtagc0Null.getAgc19());

        int vint = 1234;
        wsGfvtagc0Null.setAgc19(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc19()));
    }

    // property agc20
    @Test
    void testGetterSetterAgc20() {

        assertNotNull(wsGfvtagc0Null.getAgc20());

        int vint = 1234;
        wsGfvtagc0Null.setAgc20(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc20()));
    }

    // property agc21
    @Test
    void testGetterSetterAgc21() {

        assertNotNull(wsGfvtagc0Null.getAgc21());

        int vint = 1234;
        wsGfvtagc0Null.setAgc21(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc21()));
    }

    // property agc22
    @Test
    void testGetterSetterAgc22() {

        assertNotNull(wsGfvtagc0Null.getAgc22());

        int vint = 1234;
        wsGfvtagc0Null.setAgc22(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc22()));
    }

    // property agc23
    @Test
    void testGetterSetterAgc23() {

        assertNotNull(wsGfvtagc0Null.getAgc23());

        int vint = 1234;
        wsGfvtagc0Null.setAgc23(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc23()));
    }

    // property agc24
    @Test
    void testGetterSetterAgc24() {

        assertNotNull(wsGfvtagc0Null.getAgc24());

        int vint = 1234;
        wsGfvtagc0Null.setAgc24(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc24()));
    }

    // property agc25
    @Test
    void testGetterSetterAgc25() {

        assertNotNull(wsGfvtagc0Null.getAgc25());

        int vint = 1234;
        wsGfvtagc0Null.setAgc25(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc25()));
    }

    // property agc26
    @Test
    void testGetterSetterAgc26() {

        assertNotNull(wsGfvtagc0Null.getAgc26());

        int vint = 1234;
        wsGfvtagc0Null.setAgc26(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc26()));
    }

    // property agc27
    @Test
    void testGetterSetterAgc27() {

        assertNotNull(wsGfvtagc0Null.getAgc27());

        int vint = 1234;
        wsGfvtagc0Null.setAgc27(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc27()));
    }

    // property agc28
    @Test
    void testGetterSetterAgc28() {

        assertNotNull(wsGfvtagc0Null.getAgc28());

        int vint = 1234;
        wsGfvtagc0Null.setAgc28(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc28()));
    }
}

