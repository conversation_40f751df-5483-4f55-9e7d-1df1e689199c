package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-EXC-AGT.
 *
 */
class TablaExcAgtTest {

    private TablaExcAgt tablaExcAgt;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaExcAgt = new TablaExcAgt();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tabExcAgt
    @Test
    void testGetterSetterTabExcAgt() {

        assertNotNull(tablaExcAgt.getTabExcAgt());
        assertNotNull(tablaExcAgt.getTabExcAgt(1));
    }

    // property tbAgente
    @Test
    void testGetterSetterTbAgente() {

        // Property: tabExcAgt.tbAgente -> 07 TB-AGENTE 9(05)
        assertNotNull(tablaExcAgt.getTbAgente());
        int vint = 12345;
        tablaExcAgt.setTbAgente(vint); // int
        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        tablaExcAgt.setTbAgente(vINumericValue); // INumericValue
    }

    // property tbIndExcAgt
    @Test
    void testGetterSetterTbIndExcAgt() {

        // Property: tabExcAgt.tbIndExcAgt -> 07 TB-IND-EXC-AGT X(01)
        assertNotNull(tablaExcAgt.getTbIndExcAgt());
        String vString = "A";
        tablaExcAgt.setTbIndExcAgt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tablaExcAgt.setTbIndExcAgt(vIAlphanumericValue); // IAlphanumericValue
    }
}

