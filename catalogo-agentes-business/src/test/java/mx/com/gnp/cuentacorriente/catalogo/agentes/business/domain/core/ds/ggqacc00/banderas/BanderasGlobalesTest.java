package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.banderas;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: BANDERAS-GLOBALES.
 *
 */
class BanderasGlobalesTest {

    private BanderasGlobales banderasGlobales;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        banderasGlobales = new BanderasGlobales();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        banderasGlobales.setWkDisp(0);
        assertEquals(0, banderasGlobales.getWkDisp());

        banderasGlobales.setWkDisp1(0);
        assertEquals(0, banderasGlobales.getWkDisp1());

        banderasGlobales.setStExtraccion("00");
        assertEquals("00", banderasGlobales.getStExtraccion());

        banderasGlobales.setStFacturacion("00");
        assertEquals("00", banderasGlobales.getStFacturacion());

        banderasGlobales.setStDetSusp("00");
        assertEquals("00", banderasGlobales.getStDetSusp());

        banderasGlobales.setStCatDirec("00");
        assertEquals("00", banderasGlobales.getStCatDirec());

        banderasGlobales.setWsSt("");
        assertEquals("", banderasGlobales.getWsSt());

        banderasGlobales.setWsFinExtraccion(0);
        assertEquals(0, banderasGlobales.getWsFinExtraccion());

        banderasGlobales.setWsFinCatDirecc(0);
        assertEquals(0, banderasGlobales.getWsFinCatDirecc());

        banderasGlobales.setWsFinCurMte(0);
        assertEquals(0, banderasGlobales.getWsFinCurMte());

        banderasGlobales.setWsFinCurEsa(0);
        assertEquals(0, banderasGlobales.getWsFinCurEsa());

        banderasGlobales.setWsEncDirecc(0);
        assertEquals(0, banderasGlobales.getWsEncDirecc());

        banderasGlobales.setWsEncAgente(0);
        assertEquals(0, banderasGlobales.getWsEncAgente());

        banderasGlobales.setWsEncCna(0);
        assertEquals(0, banderasGlobales.getWsEncCna());

        banderasGlobales.setWsEncDsc(0);
        assertEquals(0, banderasGlobales.getWsEncDsc());

        banderasGlobales.setWsEncEstatus(0);
        assertEquals(0, banderasGlobales.getWsEncEstatus());

        banderasGlobales.setWsEncEstatusCna(0);
        assertEquals(0, banderasGlobales.getWsEncEstatusCna());

        banderasGlobales.setWsEncExcluido(0);
        assertEquals(0, banderasGlobales.getWsEncExcluido());

        banderasGlobales.setWsAgenteEstatus(0);
        assertEquals(0, banderasGlobales.getWsAgenteEstatus());

        banderasGlobales.setWsCnaEstatus(0);
        assertEquals(0, banderasGlobales.getWsCnaEstatus());

        banderasGlobales.setWsFinCurDma(0);
        assertEquals(0, banderasGlobales.getWsFinCurDma());

        banderasGlobales.setWsEncGfvtagt(0);
        assertEquals(0, banderasGlobales.getWsEncGfvtagt());

        banderasGlobales.setWsEncGfvtcna(0);
        assertEquals(0, banderasGlobales.getWsEncGfvtcna());

        banderasGlobales.setWsEncGfvtpra(0);
        assertEquals(0, banderasGlobales.getWsEncGfvtpra());

        banderasGlobales.setWsEncGcctmnd(0);
        assertEquals(0, banderasGlobales.getWsEncGcctmnd());

        banderasGlobales.setWsEncGcctpob(0);
        assertEquals(0, banderasGlobales.getWsEncGcctpob());

        banderasGlobales.setWsEncGcctedo(0);
        assertEquals(0, banderasGlobales.getWsEncGcctedo());

        banderasGlobales.setWsEncGcctpai(0);
        assertEquals(0, banderasGlobales.getWsEncGcctpai());

        banderasGlobales.setWsEncDirec(0);
        assertEquals(0, banderasGlobales.getWsEncDirec());

        banderasGlobales.setWsEncGfvtcma(0);
        assertEquals(0, banderasGlobales.getWsEncGfvtcma());

        banderasGlobales.setWsEncTrn(0);
        assertEquals(0, banderasGlobales.getWsEncTrn());

        banderasGlobales.setWsEncConcepto(0);
        assertEquals(0, banderasGlobales.getWsEncConcepto());

        banderasGlobales.setWsSinMovs(0);
        assertEquals(0, banderasGlobales.getWsSinMovs());

    }

    // property wkDisp
    @Test
    void testGetterSetterWkDisp() {

        assertNotNull(banderasGlobales.getWkDisp());

        int vint = 1;
        banderasGlobales.setWkDisp(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWkDisp()));
    }

    // property wkDisp1
    @Test
    void testGetterSetterWkDisp1() {

        assertNotNull(banderasGlobales.getWkDisp1());

        int vint = 1;
        banderasGlobales.setWkDisp1(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWkDisp1()));
    }

    // property stExtraccion
    @Test
    void testGetterSetterStExtraccion() {

        assertNotNull(banderasGlobales.getStExtraccion());

        String vString = "AB";
        banderasGlobales.setStExtraccion(vString); // String
        assertEquals(asStr(vString), asStr(banderasGlobales.getStExtraccion()));
    }

    // property stFacturacion
    @Test
    void testGetterSetterStFacturacion() {

        assertNotNull(banderasGlobales.getStFacturacion());

        String vString = "AB";
        banderasGlobales.setStFacturacion(vString); // String
        assertEquals(asStr(vString), asStr(banderasGlobales.getStFacturacion()));
    }

    // property stDetSusp
    @Test
    void testGetterSetterStDetSusp() {

        assertNotNull(banderasGlobales.getStDetSusp());

        String vString = "AB";
        banderasGlobales.setStDetSusp(vString); // String
        assertEquals(asStr(vString), asStr(banderasGlobales.getStDetSusp()));
    }

    // property stCatDirec
    @Test
    void testGetterSetterStCatDirec() {

        assertNotNull(banderasGlobales.getStCatDirec());

        String vString = "AB";
        banderasGlobales.setStCatDirec(vString); // String
        assertEquals(asStr(vString), asStr(banderasGlobales.getStCatDirec()));
    }

    // property wsSt
    @Test
    void testGetterSetterWsSt() {

        assertNotNull(banderasGlobales.getWsSt());

        String vString = "AB";
        banderasGlobales.setWsSt(vString); // String
        assertEquals(asStr(vString), asStr(banderasGlobales.getWsSt()));
    }

    // property wsFinExtraccion
    @Test
    void testGetterSetterWsFinExtraccion() {

        assertNotNull(banderasGlobales.getWsFinExtraccion());

        int vint = 1;
        banderasGlobales.setWsFinExtraccion(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsFinExtraccion()));
        // Conditionals (88) - WS-FIN-EXTRACCION
        banderasGlobales.setFinExtraccion();
        assertTrue(banderasGlobales.isFinExtraccion());

    }

    // property wsFinCatDirecc
    @Test
    void testGetterSetterWsFinCatDirecc() {

        assertNotNull(banderasGlobales.getWsFinCatDirecc());

        int vint = 1;
        banderasGlobales.setWsFinCatDirecc(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsFinCatDirecc()));
        // Conditionals (88) - WS-FIN-CAT-DIRECC
        banderasGlobales.setFinCatDirecc();
        assertTrue(banderasGlobales.isFinCatDirecc());

    }

    // property wsFinCurMte
    @Test
    void testGetterSetterWsFinCurMte() {

        assertNotNull(banderasGlobales.getWsFinCurMte());

        int vint = 1;
        banderasGlobales.setWsFinCurMte(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsFinCurMte()));
        // Conditionals (88) - WS-FIN-CUR-MTE
        banderasGlobales.setFinCurMte();
        assertTrue(banderasGlobales.isFinCurMte());

    }

    // property wsFinCurEsa
    @Test
    void testGetterSetterWsFinCurEsa() {

        assertNotNull(banderasGlobales.getWsFinCurEsa());

        int vint = 1;
        banderasGlobales.setWsFinCurEsa(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsFinCurEsa()));
        // Conditionals (88) - WS-FIN-CUR-ESA
        banderasGlobales.setFinCurEsa();
        assertTrue(banderasGlobales.isFinCurEsa());

    }

    // property wsEncDirecc
    @Test
    void testGetterSetterWsEncDirecc() {

        assertNotNull(banderasGlobales.getWsEncDirecc());

        int vint = 1;
        banderasGlobales.setWsEncDirecc(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncDirecc()));
        // Conditionals (88) - WS-ENC-DIRECC
        banderasGlobales.setEncDirecc();
        assertTrue(banderasGlobales.isEncDirecc());

    }

    // property wsEncAgente
    @Test
    void testGetterSetterWsEncAgente() {

        assertNotNull(banderasGlobales.getWsEncAgente());

        int vint = 1;
        banderasGlobales.setWsEncAgente(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncAgente()));
        // Conditionals (88) - WS-ENC-AGENTE
        banderasGlobales.setEncAgente();
        assertTrue(banderasGlobales.isEncAgente());

    }

    // property wsEncCna
    @Test
    void testGetterSetterWsEncCna() {

        assertNotNull(banderasGlobales.getWsEncCna());

        int vint = 1;
        banderasGlobales.setWsEncCna(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncCna()));
        // Conditionals (88) - WS-ENC-CNA
        banderasGlobales.setEncCna();
        assertTrue(banderasGlobales.isEncCna());

    }

    // property wsEncDsc
    @Test
    void testGetterSetterWsEncDsc() {

        assertNotNull(banderasGlobales.getWsEncDsc());

        int vint = 1;
        banderasGlobales.setWsEncDsc(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncDsc()));
        // Conditionals (88) - WS-ENC-DSC
        banderasGlobales.setEncDsc();
        assertTrue(banderasGlobales.isEncDsc());

    }

    // property wsEncEstatus
    @Test
    void testGetterSetterWsEncEstatus() {

        assertNotNull(banderasGlobales.getWsEncEstatus());

        int vint = 1;
        banderasGlobales.setWsEncEstatus(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncEstatus()));
        // Conditionals (88) - WS-ENC-ESTATUS
        banderasGlobales.setEncEstatus();
        assertTrue(banderasGlobales.isEncEstatus());

    }

    // property wsEncEstatusCna
    @Test
    void testGetterSetterWsEncEstatusCna() {

        assertNotNull(banderasGlobales.getWsEncEstatusCna());

        int vint = 1;
        banderasGlobales.setWsEncEstatusCna(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncEstatusCna()));
        // Conditionals (88) - WS-ENC-ESTATUS-CNA
        banderasGlobales.setEncEstatusCna();
        assertTrue(banderasGlobales.isEncEstatusCna());

    }

    // property wsEncExcluido
    @Test
    void testGetterSetterWsEncExcluido() {

        assertNotNull(banderasGlobales.getWsEncExcluido());

        int vint = 1;
        banderasGlobales.setWsEncExcluido(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncExcluido()));
        // Conditionals (88) - WS-ENC-EXCLUIDO
        banderasGlobales.setEncExcluido();
        assertTrue(banderasGlobales.isEncExcluido());

    }

    // property wsAgenteEstatus
    @Test
    void testGetterSetterWsAgenteEstatus() {

        assertNotNull(banderasGlobales.getWsAgenteEstatus());

        int vint = 1234;
        banderasGlobales.setWsAgenteEstatus(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsAgenteEstatus()));
        // Conditionals (88) - WS-AGENTE-ESTATUS
        banderasGlobales.setAgenteEstatus();
        assertTrue(banderasGlobales.isAgenteEstatus());
        banderasGlobales.setAgenteVigente();
        assertTrue(banderasGlobales.isAgenteVigente());
        banderasGlobales.setAgenteCancelado();
        assertTrue(banderasGlobales.isAgenteCancelado());
        banderasGlobales.setAgenteSuspendido();
        assertTrue(banderasGlobales.isAgenteSuspendido());

    }

    // property wsCnaEstatus
    @Test
    void testGetterSetterWsCnaEstatus() {

        assertNotNull(banderasGlobales.getWsCnaEstatus());

        int vint = 1234;
        banderasGlobales.setWsCnaEstatus(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsCnaEstatus()));
        // Conditionals (88) - WS-CNA-ESTATUS
        banderasGlobales.setCnaEstatus();
        assertTrue(banderasGlobales.isCnaEstatus());
        banderasGlobales.setCnaVigente();
        assertTrue(banderasGlobales.isCnaVigente());
        banderasGlobales.setCnaCancelado();
        assertTrue(banderasGlobales.isCnaCancelado());
        banderasGlobales.setCnaSuspendido();
        assertTrue(banderasGlobales.isCnaSuspendido());

    }

    // property wsFinCurDma
    @Test
    void testGetterSetterWsFinCurDma() {

        assertNotNull(banderasGlobales.getWsFinCurDma());

        int vint = 1;
        banderasGlobales.setWsFinCurDma(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsFinCurDma()));
        // Conditionals (88) - WS-FIN-CUR-DMA
        banderasGlobales.setFinCurDma();
        assertTrue(banderasGlobales.isFinCurDma());

    }

    // property wsEncGfvtagt
    @Test
    void testGetterSetterWsEncGfvtagt() {

        assertNotNull(banderasGlobales.getWsEncGfvtagt());

        int vint = 1;
        banderasGlobales.setWsEncGfvtagt(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGfvtagt()));
        // Conditionals (88) - WS-ENC-GFVTAGT
        banderasGlobales.setEncGfvtagt();
        assertTrue(banderasGlobales.isEncGfvtagt());

    }

    // property wsEncGfvtcna
    @Test
    void testGetterSetterWsEncGfvtcna() {

        assertNotNull(banderasGlobales.getWsEncGfvtcna());

        int vint = 1;
        banderasGlobales.setWsEncGfvtcna(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGfvtcna()));
        // Conditionals (88) - WS-ENC-GFVTCNA
        banderasGlobales.setEncGfvtcna();
        assertTrue(banderasGlobales.isEncGfvtcna());

    }

    // property wsEncGfvtpra
    @Test
    void testGetterSetterWsEncGfvtpra() {

        assertNotNull(banderasGlobales.getWsEncGfvtpra());

        int vint = 1;
        banderasGlobales.setWsEncGfvtpra(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGfvtpra()));
        // Conditionals (88) - WS-ENC-GFVTPRA
        banderasGlobales.setEncGfvtpra();
        assertTrue(banderasGlobales.isEncGfvtpra());

    }

    // property wsEncGcctmnd
    @Test
    void testGetterSetterWsEncGcctmnd() {

        assertNotNull(banderasGlobales.getWsEncGcctmnd());

        int vint = 1;
        banderasGlobales.setWsEncGcctmnd(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGcctmnd()));
        // Conditionals (88) - WS-ENC-GCCTMND
        banderasGlobales.setEncGcctmnd();
        assertTrue(banderasGlobales.isEncGcctmnd());

    }

    // property wsEncGcctpob
    @Test
    void testGetterSetterWsEncGcctpob() {

        assertNotNull(banderasGlobales.getWsEncGcctpob());

        int vint = 1;
        banderasGlobales.setWsEncGcctpob(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGcctpob()));
        // Conditionals (88) - WS-ENC-GCCTPOB
        banderasGlobales.setEncGcctpob();
        assertTrue(banderasGlobales.isEncGcctpob());

    }

    // property wsEncGcctedo
    @Test
    void testGetterSetterWsEncGcctedo() {

        assertNotNull(banderasGlobales.getWsEncGcctedo());

        int vint = 1;
        banderasGlobales.setWsEncGcctedo(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGcctedo()));
        // Conditionals (88) - WS-ENC-GCCTEDO
        banderasGlobales.setEncGcctedo();
        assertTrue(banderasGlobales.isEncGcctedo());

    }

    // property wsEncGcctpai
    @Test
    void testGetterSetterWsEncGcctpai() {

        assertNotNull(banderasGlobales.getWsEncGcctpai());

        int vint = 1;
        banderasGlobales.setWsEncGcctpai(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGcctpai()));
        // Conditionals (88) - WS-ENC-GCCTPAI
        banderasGlobales.setEncGcctpai();
        assertTrue(banderasGlobales.isEncGcctpai());

    }

    // property wsEncDirec
    @Test
    void testGetterSetterWsEncDirec() {

        assertNotNull(banderasGlobales.getWsEncDirec());

        int vint = 1;
        banderasGlobales.setWsEncDirec(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncDirec()));
        // Conditionals (88) - WS-ENC-DIREC
        banderasGlobales.setEncDirec();
        assertTrue(banderasGlobales.isEncDirec());

    }

    // property wsEncGfvtcma
    @Test
    void testGetterSetterWsEncGfvtcma() {

        assertNotNull(banderasGlobales.getWsEncGfvtcma());

        int vint = 1;
        banderasGlobales.setWsEncGfvtcma(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncGfvtcma()));
        // Conditionals (88) - WS-ENC-GFVTCMA
        banderasGlobales.setEncGfvtcma();
        assertTrue(banderasGlobales.isEncGfvtcma());

    }

    // property wsEncTrn
    @Test
    void testGetterSetterWsEncTrn() {

        assertNotNull(banderasGlobales.getWsEncTrn());

        int vint = 1;
        banderasGlobales.setWsEncTrn(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncTrn()));
        // Conditionals (88) - WS-ENC-TRN
        banderasGlobales.setEncTrn();
        assertTrue(banderasGlobales.isEncTrn());

    }

    // property wsEncConcepto
    @Test
    void testGetterSetterWsEncConcepto() {

        assertNotNull(banderasGlobales.getWsEncConcepto());

        int vint = 1;
        banderasGlobales.setWsEncConcepto(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncConcepto()));
        // Conditionals (88) - WS-ENC-CONCEPTO
        banderasGlobales.setEncConcepto();
        assertTrue(banderasGlobales.isEncConcepto());

    }

    // property wsSinMovs
    @Test
    void testGetterSetterWsSinMovs() {

        assertNotNull(banderasGlobales.getWsSinMovs());

        int vint = 1;
        banderasGlobales.setWsSinMovs(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsSinMovs()));
        // Conditionals (88) - WS-SIN-MOVS
        banderasGlobales.setSinMovs();
        assertTrue(banderasGlobales.isSinMovs());

    }
}

