package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: R-AGENTES.
 *
 */
class RAgentesTest {

    private RAgentes rAgentes;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rAgentes = new RAgentes();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RAgentes rAgentes2 = new RAgentes(parent);
        assertNotNull(rAgentes2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 79);
        RAgentes rAgentes3 = new RAgentes(parent, sibling);
        assertNotNull(rAgentes3);
    }

    @Test
    void testInitialize() {
        assertNotNull(rAgentes.getRAgentesAgtIdr());

        assertNotNull(rAgentes.getRAgentesHfaPjeIva());

        assertNotNull(rAgentes.getRAgentesHfaPjeIsr());

        assertNotNull(rAgentes.getRAgentesCedNv());

        assertNotNull(rAgentes.getRAgentesCedVi());

        assertNotNull(rAgentes.getRAgentesPefCve());

        assertNotNull(rAgentes.getRAgentesCdregfis());

        assertNotNull(rAgentes.getRAgentesEdoCve());

        assertNotNull(rAgentes.getRAgentesTfpCve());

        assertNotNull(rAgentes.getRAgentesFiller());

    }

    // property rAgentesAgtIdr
    @Test
    void testGetterSetterRAgentesAgtIdr() {

        assertNotNull(rAgentes.getRAgentesAgtIdr());

        int vint = 1234567;
        rAgentes.setRAgentesAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(rAgentes.getRAgentesAgtIdr()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        rAgentes.setRAgentesAgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rAgentes.getRAgentesAgtIdr()));
    }

    // property rAgentesHfaPjeIva
    @Test
    void testGetterSetterRAgentesHfaPjeIva() {

        assertNotNull(rAgentes.getRAgentesHfaPjeIva());

        String vString = " 000.01";
        rAgentes.setRAgentesHfaPjeIva(vString); // String
        assertEquals(asStr(vString), asStr(rAgentes.getRAgentesHfaPjeIva()));

        NumericVar vIValue = new NumericVar(7, 0);;
        vIValue.setValue("1234567");
        rAgentes.setRAgentesHfaPjeIva(vIValue); // IValue
        // format("1234567", "-9(03).9(02)")
        assertNotNull(asStr(rAgentes.getRAgentesHfaPjeIva()));
    }

    // property rAgentesHfaPjeIsr
    @Test
    void testGetterSetterRAgentesHfaPjeIsr() {

        assertNotNull(rAgentes.getRAgentesHfaPjeIsr());

        String vString = " 000.01";
        rAgentes.setRAgentesHfaPjeIsr(vString); // String
        assertEquals(asStr(vString), asStr(rAgentes.getRAgentesHfaPjeIsr()));

        NumericVar vIValue = new NumericVar(7, 0);;
        vIValue.setValue("1234567");
        rAgentes.setRAgentesHfaPjeIsr(vIValue); // IValue
        // format("1234567", "-9(03).9(02)")
        assertNotNull(asStr(rAgentes.getRAgentesHfaPjeIsr()));
    }

    // property rAgentesCedNv
    @Test
    void testGetterSetterRAgentesCedNv() {

        assertNotNull(rAgentes.getRAgentesCedNv());

        String vString = " 000.01";
        rAgentes.setRAgentesCedNv(vString); // String
        assertEquals(asStr(vString), asStr(rAgentes.getRAgentesCedNv()));

        NumericVar vIValue = new NumericVar(7, 0);;
        vIValue.setValue("1234567");
        rAgentes.setRAgentesCedNv(vIValue); // IValue
        // format("1234567", "-9(03).9(02)")
        assertNotNull(asStr(rAgentes.getRAgentesCedNv()));
    }

    // property rAgentesCedVi
    @Test
    void testGetterSetterRAgentesCedVi() {

        assertNotNull(rAgentes.getRAgentesCedVi());

        String vString = " 000.01";
        rAgentes.setRAgentesCedVi(vString); // String
        assertEquals(asStr(vString), asStr(rAgentes.getRAgentesCedVi()));

        NumericVar vIValue = new NumericVar(7, 0);;
        vIValue.setValue("1234567");
        rAgentes.setRAgentesCedVi(vIValue); // IValue
        // format("1234567", "-9(03).9(02)")
        assertNotNull(asStr(rAgentes.getRAgentesCedVi()));
    }

    // property rAgentesPefCve
    @Test
    void testGetterSetterRAgentesPefCve() {

        assertNotNull(rAgentes.getRAgentesPefCve());

        int vint = 1;
        rAgentes.setRAgentesPefCve(vint); // int
        assertEquals(asInt(vint), asInt(rAgentes.getRAgentesPefCve()));

        NumericVar vINumericValue = new NumericVar(1, 0);;
        vINumericValue.setValue("1");
        rAgentes.setRAgentesPefCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rAgentes.getRAgentesPefCve()));
    }

    // property rAgentesCdregfis
    @Test
    void testGetterSetterRAgentesCdregfis() {

        assertNotNull(rAgentes.getRAgentesCdregfis());

        String vString = "ABC";
        rAgentes.setRAgentesCdregfis(vString); // String
        assertEquals(asStr(vString), asStr(rAgentes.getRAgentesCdregfis()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        rAgentes.setRAgentesCdregfis(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rAgentes.getRAgentesCdregfis()));
    }

    // property rAgentesEdoCve
    @Test
    void testGetterSetterRAgentesEdoCve() {

        assertNotNull(rAgentes.getRAgentesEdoCve());

        String vString = "ABCa";
        rAgentes.setRAgentesEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(rAgentes.getRAgentesEdoCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        rAgentes.setRAgentesEdoCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rAgentes.getRAgentesEdoCve()));
    }

    // property rAgentesTfpCve
    @Test
    void testGetterSetterRAgentesTfpCve() {

        assertNotNull(rAgentes.getRAgentesTfpCve());

        int vint = 12;
        rAgentes.setRAgentesTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(rAgentes.getRAgentesTfpCve()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        rAgentes.setRAgentesTfpCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rAgentes.getRAgentesTfpCve()));
    }

    // property rAgentesFiller
    @Test
    void testGetterSetterRAgentesFiller() {

        assertNotNull(rAgentes.getRAgentesFiller());

        String vString = "ABCabc1230";
        rAgentes.setRAgentesFiller(vString); // String
        assertEquals(asStr(vString), asStr(rAgentes.getRAgentesFiller()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        rAgentes.setRAgentesFiller(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(rAgentes.getRAgentesFiller()));
    }
}

