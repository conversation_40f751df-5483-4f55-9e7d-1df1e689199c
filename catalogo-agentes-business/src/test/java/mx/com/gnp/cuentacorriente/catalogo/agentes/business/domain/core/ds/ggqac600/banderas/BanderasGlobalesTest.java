package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.banderas;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: BANDERAS-GLOBALES.
 *
 */
class BanderasGlobalesTest {

    private BanderasGlobales banderasGlobales;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        banderasGlobales = new BanderasGlobales();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        banderasGlobales.setStExcAgt("00");
        assertEquals("00", banderasGlobales.getStExcAgt());

        banderasGlobales.setWsFinAgtExc(0);
        assertEquals(0, banderasGlobales.getWsFinAgtExc());

        banderasGlobales.setWsEncAgtExc(0);
        assertEquals(0, banderasGlobales.getWsEncAgtExc());

    }

    // property stExcAgt
    @Test
    void testGetterSetterStExcAgt() {

        assertNotNull(banderasGlobales.getStExcAgt());

        String vString = "AB";
        banderasGlobales.setStExcAgt(vString); // String
        assertEquals(asStr(vString), asStr(banderasGlobales.getStExcAgt()));
    }

    // property wsFinAgtExc
    @Test
    void testGetterSetterWsFinAgtExc() {

        assertNotNull(banderasGlobales.getWsFinAgtExc());

        int vint = 1;
        banderasGlobales.setWsFinAgtExc(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsFinAgtExc()));
        // Conditionals (88) - WS-FIN-AGT-EXC
        banderasGlobales.setFinAgtExc();
        assertTrue(banderasGlobales.isFinAgtExc());

    }

    // property wsEncAgtExc
    @Test
    void testGetterSetterWsEncAgtExc() {

        assertNotNull(banderasGlobales.getWsEncAgtExc());

        int vint = 1;
        banderasGlobales.setWsEncAgtExc(vint); // int
        assertEquals(asInt(vint), asInt(banderasGlobales.getWsEncAgtExc()));
        // Conditionals (88) - WS-ENC-AGT-EXC
        banderasGlobales.setEncAgtExc();
        assertTrue(banderasGlobales.isEncAgtExc());

    }
}

