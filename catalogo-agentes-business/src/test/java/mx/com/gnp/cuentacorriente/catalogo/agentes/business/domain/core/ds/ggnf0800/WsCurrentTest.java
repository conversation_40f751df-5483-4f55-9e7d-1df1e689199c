package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CURRENT.
 *
 */
class WsCurrentTest {

    private WsCurrent wsCurrent;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCurrent = new WsCurrent();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsCurrent.setCurrDd(0);
        assertEquals(0, wsCurrent.getCurrDd());

        wsCurrent.setCurrMm(0);
        assertEquals(0, wsCurrent.getCurrMm());

        wsCurrent.setCurrAa(0);
        assertEquals(0, wsCurrent.getCurrAa());

    }

    // property currDd
    @Test
    void testGetterSetterCurrDd() {

        assertNotNull(wsCurrent.getCurrDd());

        int vint = 12;
        wsCurrent.setCurrDd(vint); // int
        assertEquals(asInt(vint), asInt(wsCurrent.getCurrDd()));
    }

    // property currMm
    @Test
    void testGetterSetterCurrMm() {

        assertNotNull(wsCurrent.getCurrMm());

        int vint = 12;
        wsCurrent.setCurrMm(vint); // int
        assertEquals(asInt(vint), asInt(wsCurrent.getCurrMm()));
    }

    // property currAa
    @Test
    void testGetterSetterCurrAa() {

        assertNotNull(wsCurrent.getCurrAa());

        int vint = 12;
        wsCurrent.setCurrAa(vint); // int
        assertEquals(asInt(vint), asInt(wsCurrent.getCurrAa()));
    }
}

