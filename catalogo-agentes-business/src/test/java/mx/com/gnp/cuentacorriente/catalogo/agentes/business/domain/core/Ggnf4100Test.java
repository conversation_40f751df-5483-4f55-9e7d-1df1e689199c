package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.base100.caravel.support.common.sql.jdbc.IJdbcUtils;
import com.base100.caravel.support.os390.cbl.Os390ProgramMockUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

import java.time.LocalDate;

class Ggnf4100Test {
    Ggnf4100 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggnf4100.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      testRun test conditions:
        cFetchDepura : [1, 0]
    */
    @Test
    void testRun1() {
        // Set test conditions
        pgm.getCContadores().setCFetchDepura(1);

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    @Test
    void testRun2() {
        // Set test conditions
        pgm.getCContadores().setCFetchDepura(0);

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p000ModuloControl test conditions:
        cFetchDepura : [1, 0]
    */
    @Test
    void p000ModuloControlTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getCContadores().setCFetchDepura(1);
        try {
            pgm.p000ModuloControl();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p000ModuloControlTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getCContadores().setCFetchDepura(0);
            try {
                pgm.p000ModuloControl();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      proceso test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void procesoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.proceso();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void procesoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.proceso();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      fin test conditions:
        cFetchDepura : [1, 0]
    */
    @Test
    void finTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getCContadores().setCFetchDepura(1);
        try {
            pgm.fin();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void finTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getCContadores().setCFetchDepura(0);
            try {
                pgm.fin();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      fetchDepura test conditions:
        sqlcode : [0, 1]
    */
    @Test
    void fetchDepuraTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.fetchDepura();
        } catch (Exception e) {
            assertExceptionInstanceOf(NullPointerException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void fetchDepuraTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.fetchDepura();
            } catch (Exception e) {
                assertExceptionInstanceOf(NullPointerException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
}
