package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.linea;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DET1.
 *
 */
class Det1Test {

    private Det1 det1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        det1 = new Det1();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Det1 det12 = new Det1(parent);
        assertNotNull(det12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 132);
        Det1 det13 = new Det1(parent, sibling);
        assertNotNull(det13);
    }

    @Test
    void testInitialize() {
        assertNotNull(det1.getD1AgtIdr());

        assertNotNull(det1.getD1CnaNum());

        assertNotNull(det1.getD1FlaNum());

        assertNotNull(det1.getD1RamCve());

        assertNotNull(det1.getD1SraCve());

        assertNotNull(det1.getD1Obs());

    }

    // property d1AgtIdr
    @Test
    void testGetterSetterD1AgtIdr() {

        assertNotNull(det1.getD1AgtIdr());

        int vint = 123456789;
        det1.setD1AgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(det1.getD1AgtIdr()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        det1.setD1AgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(det1.getD1AgtIdr()));
    }

    // property d1CnaNum
    @Test
    void testGetterSetterD1CnaNum() {

        assertNotNull(det1.getD1CnaNum());

        String vString = "   0";
        det1.setD1CnaNum(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1CnaNum()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det1.setD1CnaNum(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det1.getD1CnaNum()));
    }

    // property d1FlaNum
    @Test
    void testGetterSetterD1FlaNum() {

        assertNotNull(det1.getD1FlaNum());

        String vString = "ABCabc12";
        det1.setD1FlaNum(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1FlaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        det1.setD1FlaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1FlaNum()));
    }

    // property d1RamCve
    @Test
    void testGetterSetterD1RamCve() {

        assertNotNull(det1.getD1RamCve());

        String vString = "ABCab";
        det1.setD1RamCve(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1RamCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        det1.setD1RamCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1RamCve()));
    }

    // property d1SraCve
    @Test
    void testGetterSetterD1SraCve() {

        assertNotNull(det1.getD1SraCve());

        String vString = "ABCa";
        det1.setD1SraCve(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1SraCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        det1.setD1SraCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1SraCve()));
    }

    // property d1Obs
    @Test
    void testGetterSetterD1Obs() {

        assertNotNull(det1.getD1Obs());

        String vString = "ABCabc1230";
        det1.setD1Obs(vString); // String
        assertEquals(asStr(vString), asStr(det1.getD1Obs()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det1.setD1Obs(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det1.getD1Obs()));
    }
}

