package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

class Ggnfoz00Test {
    Ggnfoz00 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggnfoz00.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      testRun test conditions:
        fsAgth : ["XX00", "00"]
        fsCnah : ["XX00", "00"]
        fsDmah : ["XX00", "00"]
        fsCmah : ["XX00", "00"]
        fsIntr : ["XX00", "00"]
    */
    @Test
    void testRun1() {
        // Set test conditions
        pgm.getWsConstants().setFsAgth("XX00");
        pgm.getWsConstants().setFsCnah("XX00");
        pgm.getWsConstants().setFsDmah("XX00");
        pgm.getWsConstants().setFsCmah("XX00");
        pgm.getWsConstants().setFsIntr("XX00");

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    @Test
    void testRun2() {
        // Set test conditions
        pgm.getWsConstants().setFsAgth("00");
        pgm.getWsConstants().setFsCnah("00");
        pgm.getWsConstants().setFsDmah("00");
        pgm.getWsConstants().setFsCmah("00");
        pgm.getWsConstants().setFsIntr("00");

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p00000Principal test conditions:
        fsAgth : ["XX00", "00"]
        fsCnah : ["XX00", "00"]
        fsDmah : ["XX00", "00"]
        fsCmah : ["XX00", "00"]
        fsIntr : ["XX00", "00"]
    */
    @Test
    void p00000PrincipalTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsConstants().setFsAgth("XX00");
        pgm.getWsConstants().setFsCnah("XX00");
        pgm.getWsConstants().setFsDmah("XX00");
        pgm.getWsConstants().setFsCmah("XX00");
        pgm.getWsConstants().setFsIntr("XX00");
        try {
            pgm.p00000Principal();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p00000PrincipalTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsConstants().setFsAgth("00");
            pgm.getWsConstants().setFsCnah("00");
            pgm.getWsConstants().setFsDmah("00");
            pgm.getWsConstants().setFsCmah("00");
            pgm.getWsConstants().setFsIntr("00");
            try {
                pgm.p00000Principal();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p10000Inicio test conditions:
        fhAnoSist : [81, 80]
    */
    @Test
    void p10000InicioTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getFhFechaHoraSist().setFhAnoSist(81);
        try {
            pgm.p10000Inicio();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p10000InicioTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getFhFechaHoraSist().setFhAnoSist(80);
            try {
                pgm.p10000Inicio();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p11000ObtnrFcha test conditions:
        fhAnoSist : [81, 80]
    */
    @Test
    void p11000ObtnrFchaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getFhFechaHoraSist().setFhAnoSist(81);
        try {
            pgm.p11000ObtnrFcha();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p11000ObtnrFchaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getFhFechaHoraSist().setFhAnoSist(80);
            try {
                pgm.p11000ObtnrFcha();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p20000Proceso test conditions:
        wsConintr : [0, 1]
        sqlcode : [0, 1]
    */
    @Test
    void p20000ProcesoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsVariables().setWsConintr(0);
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p20000Proceso();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p20000ProcesoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsVariables().setWsConintr(1);
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p20000Proceso();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p26000ProcsarIntr test conditions:
        wsConintr : [0, 1]
        sqlcode : [0, 1]
    */
    @Test
    void p26000ProcsarIntrTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsVariables().setWsConintr(0);
        pgm.getSqlca().setSqlcode(0);
        try {
            pgm.p26000ProcsarIntr();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p26000ProcsarIntrTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsVariables().setWsConintr(1);
            pgm.getSqlca().setSqlcode(1);
            try {
                pgm.p26000ProcsarIntr();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p30000Termina test conditions:
        fsAgth : ["XX00", "00"]
        fsCnah : ["XX00", "00"]
        fsDmah : ["XX00", "00"]
        fsCmah : ["XX00", "00"]
        fsIntr : ["XX00", "00"]
    */
    @Test
    void p30000TerminaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsConstants().setFsAgth("XX00");
        pgm.getWsConstants().setFsCnah("XX00");
        pgm.getWsConstants().setFsDmah("XX00");
        pgm.getWsConstants().setFsCmah("XX00");
        pgm.getWsConstants().setFsIntr("XX00");
        try {
            pgm.p30000Termina();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p30000TerminaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsConstants().setFsAgth("00");
            pgm.getWsConstants().setFsCnah("00");
            pgm.getWsConstants().setFsDmah("00");
            pgm.getWsConstants().setFsCmah("00");
            pgm.getWsConstants().setFsIntr("00");
            try {
                pgm.p30000Termina();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
}
