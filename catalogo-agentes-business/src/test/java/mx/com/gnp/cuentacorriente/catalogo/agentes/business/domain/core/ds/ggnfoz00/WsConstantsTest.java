package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CONSTANTS.
 *
 */
class WsConstantsTest {

    private WsConstants wsConstants;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsConstants = new WsConstants();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsConstants.setFsAgth("00");
        assertEquals("00", wsConstants.getFsAgth());

        wsConstants.setFsCnah("00");
        assertEquals("00", wsConstants.getFsCnah());

        wsConstants.setFsDmah("00");
        assertEquals("00", wsConstants.getFsDmah());

        wsConstants.setFsCmah("00");
        assertEquals("00", wsConstants.getFsCmah());

        wsConstants.setFsDrc0("00");
        assertEquals("00", wsConstants.getFsDrc0());

        wsConstants.setFsIntr("00");
        assertEquals("00", wsConstants.getFsIntr());

        wsConstants.setWcUno("1");
        assertEquals("1", wsConstants.getWcUno());

        wsConstants.setWc_2cr("00");
        assertEquals("00", wsConstants.getWc_2cr());

    }

    // property fsAgth
    @Test
    void testGetterSetterFsAgth() {

        assertNotNull(wsConstants.getFsAgth());

        String vString = "AB";
        wsConstants.setFsAgth(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getFsAgth()));
    }

    // property fsCnah
    @Test
    void testGetterSetterFsCnah() {

        assertNotNull(wsConstants.getFsCnah());

        String vString = "AB";
        wsConstants.setFsCnah(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getFsCnah()));
    }

    // property fsDmah
    @Test
    void testGetterSetterFsDmah() {

        assertNotNull(wsConstants.getFsDmah());

        String vString = "AB";
        wsConstants.setFsDmah(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getFsDmah()));
    }

    // property fsCmah
    @Test
    void testGetterSetterFsCmah() {

        assertNotNull(wsConstants.getFsCmah());

        String vString = "AB";
        wsConstants.setFsCmah(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getFsCmah()));
    }

    // property fsDrc0
    @Test
    void testGetterSetterFsDrc0() {

        assertNotNull(wsConstants.getFsDrc0());

        String vString = "AB";
        wsConstants.setFsDrc0(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getFsDrc0()));
    }

    // property fsIntr
    @Test
    void testGetterSetterFsIntr() {

        assertNotNull(wsConstants.getFsIntr());

        String vString = "AB";
        wsConstants.setFsIntr(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getFsIntr()));
    }

    // property wcUno
    @Test
    void testGetterSetterWcUno() {

        assertNotNull(wsConstants.getWcUno());

        String vString = "A";
        wsConstants.setWcUno(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getWcUno()));
    }

    // property wc_2cr
    @Test
    void testGetterSetterWc_2cr() {

        assertNotNull(wsConstants.getWc_2cr());

        String vString = "AB";
        wsConstants.setWc_2cr(vString); // String
        assertEquals(asStr(vString), asStr(wsConstants.getWc_2cr()));
    }
}

