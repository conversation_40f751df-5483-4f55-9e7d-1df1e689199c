package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FOLIOXXN.
 *
 */
class WsFolioxxnTest {

    private WsFolioxxn wsFolioxxn;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFolioxxn = new WsFolioxxn();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFolioxxn wsFolioxxn2 = new WsFolioxxn(parent);
        assertNotNull(wsFolioxxn2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFolioxxn wsFolioxxn3 = new WsFolioxxn(parent, sibling);
        assertNotNull(wsFolioxxn3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFolioxxn.getWsFolioxx1n());

        assertNotNull(wsFolioxxn.getWsFolioxx2n());

    }

    // property wsFolioxx1n
    @Test
    void testGetterSetterWsFolioxx1n() {

        assertNotNull(wsFolioxxn.getWsFolioxx1n());

        String vString = "A";
        wsFolioxxn.setWsFolioxx1n(vString); // String
        assertEquals(asStr(vString), asStr(wsFolioxxn.getWsFolioxx1n()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFolioxxn.setWsFolioxx1n(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFolioxxn.getWsFolioxx1n()));
    }

    // property wsFolioxx2n
    @Test
    void testGetterSetterWsFolioxx2n() {

        assertNotNull(wsFolioxxn.getWsFolioxx2n());

        int vint = 1234567;
        wsFolioxxn.setWsFolioxx2n(vint); // int
        assertEquals(asInt(vint), asInt(wsFolioxxn.getWsFolioxx2n()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsFolioxxn.setWsFolioxx2n(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFolioxxn.getWsFolioxx2n()));
    }
}

