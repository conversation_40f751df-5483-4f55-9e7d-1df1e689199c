package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-OFN0-1.
 *
 */
class TOfn0_1Test {

    private TOfn0_1 tOfn0_1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tOfn0_1 = new TOfn0_1(1);
        tOfn0_1.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TOfn0_1 tOfn0_12 = new TOfn0_1(parent, 1);
        assertNotNull(tOfn0_12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TOfn0_1 tOfn0_13 = new TOfn0_1(parent, sibling, 1);
        assertNotNull(tOfn0_13);
    }

    @Test
    void testInitialize() {

    }

    // property tOfn0Elem_1
    @Test
    void testGetterSetterTOfn0Elem_1() {

        assertNotNull(tOfn0_1.getTOfn0Elem_1());
    }

    // property sCnaOfnCveElem
    @Test
    void testGetterSetterSCnaOfnCveElem() {

        // Property: tOfn0Elem_1.sCnaOfnCveElem -> 20 S-CNA-OFN-CVE-ELEM X(01)
        assertNotNull(tOfn0_1.getSCnaOfnCveElem());
        String vString = "A";
        tOfn0_1.setSCnaOfnCveElem(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tOfn0_1.setSCnaOfnCveElem(vIAlphanumericValue); // IAlphanumericValue
    }
}

