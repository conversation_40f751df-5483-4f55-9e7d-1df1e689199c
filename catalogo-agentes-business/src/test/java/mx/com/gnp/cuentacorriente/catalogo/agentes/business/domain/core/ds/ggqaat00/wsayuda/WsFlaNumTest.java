package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FLA-NUM.
 *
 */
class WsFlaNumTest {

    private WsFlaNum wsFlaNum;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFlaNum = new WsFlaNum();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFlaNum wsFlaNum2 = new WsFlaNum(parent);
        assertNotNull(wsFlaNum2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFlaNum wsFlaNum3 = new WsFlaNum(parent, sibling);
        assertNotNull(wsFlaNum3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFlaNum.getWsFlaNum_1());

        assertNotNull(wsFlaNum.getWsFlaNumX());

        assertNotNull(wsFlaNum.getWsFlaNum_2());

    }

    // property wsFlaNum_1
    @Test
    void testGetterSetterWsFlaNum_1() {

        assertNotNull(wsFlaNum.getWsFlaNum_1());

        String vString = "A";
        wsFlaNum.setWsFlaNum_1(vString); // String
        assertEquals(asStr(vString), asStr(wsFlaNum.getWsFlaNum_1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFlaNum.setWsFlaNum_1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFlaNum.getWsFlaNum_1()));
    }

    // property wsFlaNumX
    @Test
    void testGetterSetterWsFlaNumX() {

        assertNotNull(wsFlaNum.getWsFlaNumX());

        int vint = 12;
        wsFlaNum.setWsFlaNumX(vint); // int
        assertEquals(asInt(vint), asInt(wsFlaNum.getWsFlaNumX()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFlaNum.setWsFlaNumX(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFlaNum.getWsFlaNumX()));
    }

    // property wsFlaNum_2
    @Test
    void testGetterSetterWsFlaNum_2() {

        assertNotNull(wsFlaNum.getWsFlaNum_2());

        int vint = 12345;
        wsFlaNum.setWsFlaNum_2(vint); // int
        assertEquals(asInt(vint), asInt(wsFlaNum.getWsFlaNum_2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        wsFlaNum.setWsFlaNum_2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFlaNum.getWsFlaNum_2()));
    }
}

