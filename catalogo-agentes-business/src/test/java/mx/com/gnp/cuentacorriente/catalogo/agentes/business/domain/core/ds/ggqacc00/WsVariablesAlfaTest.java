package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-VARIABLES-ALFA.
 *
 */
class WsVariablesAlfaTest {

    private WsVariablesAlfa wsVariablesAlfa;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsVariablesAlfa = new WsVariablesAlfa();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsVariablesAlfa.setWsTabla("");
        assertEquals("", wsVariablesAlfa.getWsTabla());

        wsVariablesAlfa.setWsArchivo("");
        assertEquals("", wsVariablesAlfa.getWsArchivo());

        wsVariablesAlfa.setWsAccion("");
        assertEquals("", wsVariablesAlfa.getWsAccion());

        wsVariablesAlfa.setWsUniNeg("");
        assertEquals("", wsVariablesAlfa.getWsUniNeg());

        wsVariablesAlfa.setWsTrnCveAnt("");
        assertEquals("", wsVariablesAlfa.getWsTrnCveAnt());

        wsVariablesAlfa.setWsAgtNalCve("");
        assertEquals("", wsVariablesAlfa.getWsAgtNalCve());

    }

    // property wsTabla
    @Test
    void testGetterSetterWsTabla() {

        assertNotNull(wsVariablesAlfa.getWsTabla());

        String vString = "ABCabc12";
        wsVariablesAlfa.setWsTabla(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesAlfa.getWsTabla()));
    }

    // property wsArchivo
    @Test
    void testGetterSetterWsArchivo() {

        assertNotNull(wsVariablesAlfa.getWsArchivo());

        String vString = "ABCabc1230";
        wsVariablesAlfa.setWsArchivo(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesAlfa.getWsArchivo()));
    }

    // property wsAccion
    @Test
    void testGetterSetterWsAccion() {

        assertNotNull(wsVariablesAlfa.getWsAccion());

        String vString = "ABCab";
        wsVariablesAlfa.setWsAccion(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesAlfa.getWsAccion()));
    }

    // property wsUniNeg
    @Test
    void testGetterSetterWsUniNeg() {

        assertNotNull(wsVariablesAlfa.getWsUniNeg());

        String vString = "ABCab";
        wsVariablesAlfa.setWsUniNeg(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesAlfa.getWsUniNeg()));
    }

    // property wsTrnCveAnt
    @Test
    void testGetterSetterWsTrnCveAnt() {

        assertNotNull(wsVariablesAlfa.getWsTrnCveAnt());

        String vString = "ABCabc1230";
        wsVariablesAlfa.setWsTrnCveAnt(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesAlfa.getWsTrnCveAnt()));
    }

    // property wsAgtNalCve
    @Test
    void testGetterSetterWsAgtNalCve() {

        assertNotNull(wsVariablesAlfa.getWsAgtNalCve());

        String vString = "ABC";
        wsVariablesAlfa.setWsAgtNalCve(vString); // String
        assertEquals(asStr(vString), asStr(wsVariablesAlfa.getWsAgtNalCve()));
        // Conditionals (88) - WS-AGT-NAL-CVE
        wsVariablesAlfa.setNacional();
        assertTrue(wsVariablesAlfa.isNacional());
        wsVariablesAlfa.setExtranjero();
        assertTrue(wsVariablesAlfa.isExtranjero());

    }
}

