package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.cqc6hreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: CQC6H-DATOS.
 *
 */
class Cqc6hDatosTest {

    private Cqc6hDatos cqc6hDatos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        cqc6hDatos = new Cqc6hDatos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Cqc6hDatos cqc6hDatos2 = new Cqc6hDatos(parent);
        assertNotNull(cqc6hDatos2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 7);
        Cqc6hDatos cqc6hDatos3 = new Cqc6hDatos(parent, sibling);
        assertNotNull(cqc6hDatos3);
    }

    @Test
    void testInitialize() {
        assertNotNull(cqc6hDatos.getCqc6hAgt());

        assertNotNull(cqc6hDatos.getCqc6hSpaces());

        assertNotNull(cqc6hDatos.getCqc6hIndExc());

    }

    // property cqc6hAgt
    @Test
    void testGetterSetterCqc6hAgt() {

        assertNotNull(cqc6hDatos.getCqc6hAgt());

        String vString = "ABCab";
        cqc6hDatos.setCqc6hAgt(vString); // String
        assertEquals(asStr(vString), asStr(cqc6hDatos.getCqc6hAgt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        cqc6hDatos.setCqc6hAgt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(cqc6hDatos.getCqc6hAgt()));
    }

    // property cqc6hSpaces
    @Test
    void testGetterSetterCqc6hSpaces() {

        assertNotNull(cqc6hDatos.getCqc6hSpaces());

        String vString = "A";
        cqc6hDatos.setCqc6hSpaces(vString); // String
        assertEquals(asStr(vString), asStr(cqc6hDatos.getCqc6hSpaces()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        cqc6hDatos.setCqc6hSpaces(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(cqc6hDatos.getCqc6hSpaces()));
    }

    // property cqc6hIndExc
    @Test
    void testGetterSetterCqc6hIndExc() {

        assertNotNull(cqc6hDatos.getCqc6hIndExc());

        String vString = "A";
        cqc6hDatos.setCqc6hIndExc(vString); // String
        assertEquals(asStr(vString), asStr(cqc6hDatos.getCqc6hIndExc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        cqc6hDatos.setCqc6hIndExc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(cqc6hDatos.getCqc6hIndExc()));
    }
}

