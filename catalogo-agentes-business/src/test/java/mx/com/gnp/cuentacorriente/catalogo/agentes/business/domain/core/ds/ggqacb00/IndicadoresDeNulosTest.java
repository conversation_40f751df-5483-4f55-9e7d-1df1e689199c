package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICADORES-DE-NULOS.
 *
 */
class IndicadoresDeNulosTest {

    private IndicadoresDeNulos indicadoresDeNulos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indicadoresDeNulos = new IndicadoresDeNulos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property indicadoresCna
    @Test
    void testGetterSetterIndicadoresCna() {

        assertNotNull(indicadoresDeNulos.getIndicadoresCna());
    }

    // property indcnatfp
    @Test
    void testGetterSetterIndcnatfp() {

        // Property: indicadoresCna.indcnatfp -> 05 INDCNATFP S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndcnatfp());
        int vint = 1234;
        indicadoresDeNulos.setIndcnatfp(vint); // int
    }

    // property indcnamte
    @Test
    void testGetterSetterIndcnamte() {

        // Property: indicadoresCna.indcnamte -> 05 INDCNAMTE S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndcnamte());
        int vint = 1234;
        indicadoresDeNulos.setIndcnamte(vint); // int
    }

    // property indcnactbnum
    @Test
    void testGetterSetterIndcnactbnum() {

        // Property: indicadoresCna.indcnactbnum -> 05 INDCNACTBNUM S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndcnactbnum());
        int vint = 1234;
        indicadoresDeNulos.setIndcnactbnum(vint); // int
    }
}

