package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablas.tablaimptes;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TAB-IMPTES.
 *
 */
class TabImptesTest {

    private TabImptes tabImptes;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tabImptes = new TabImptes(1);
        tabImptes.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TabImptes tabImptes2 = new TabImptes(parent, 1);
        assertNotNull(tabImptes2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TabImptes tabImptes3 = new TabImptes(parent, sibling, 1);
        assertNotNull(tabImptes3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tabImptes.getTbPercGravab());

        assertNotNull(tabImptes.getTbPercExenta());

        assertNotNull(tabImptes.getTbIva());

        assertNotNull(tabImptes.getTbIsr());

        assertNotNull(tabImptes.getTbIvaAcr());

        assertNotNull(tabImptes.getTbIvaRtn());

        assertNotNull(tabImptes.getTbIsrRet());

        assertNotNull(tabImptes.getTbIpuCed());

    }

    // property tbPercGravab
    @Test
    void testGetterSetterTbPercGravab() {

        assertNotNull(tabImptes.getTbPercGravab());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbPercGravab(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbPercGravab().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbPercGravab(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbPercGravab()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbPercGravab(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbPercGravab()));
    }

    // property tbPercExenta
    @Test
    void testGetterSetterTbPercExenta() {

        assertNotNull(tabImptes.getTbPercExenta());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbPercExenta(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbPercExenta().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbPercExenta(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbPercExenta()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbPercExenta(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbPercExenta()));
    }

    // property tbIva
    @Test
    void testGetterSetterTbIva() {

        assertNotNull(tabImptes.getTbIva());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbIva().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbIva(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbIva()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbIva()));
    }

    // property tbIsr
    @Test
    void testGetterSetterTbIsr() {

        assertNotNull(tabImptes.getTbIsr());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbIsr().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbIsr(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbIsr()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbIsr()));
    }

    // property tbIvaAcr
    @Test
    void testGetterSetterTbIvaAcr() {

        assertNotNull(tabImptes.getTbIvaAcr());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbIvaAcr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbIvaAcr().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbIvaAcr(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbIvaAcr()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbIvaAcr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbIvaAcr()));
    }

    // property tbIvaRtn
    @Test
    void testGetterSetterTbIvaRtn() {

        assertNotNull(tabImptes.getTbIvaRtn());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbIvaRtn(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbIvaRtn().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbIvaRtn(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbIvaRtn()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbIvaRtn(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbIvaRtn()));
    }

    // property tbIsrRet
    @Test
    void testGetterSetterTbIsrRet() {

        assertNotNull(tabImptes.getTbIsrRet());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbIsrRet(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbIsrRet().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbIsrRet(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbIsrRet()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbIsrRet(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbIsrRet()));
    }

    // property tbIpuCed
    @Test
    void testGetterSetterTbIpuCed() {

        assertNotNull(tabImptes.getTbIpuCed());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tabImptes.setTbIpuCed(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, tabImptes.getTbIpuCed().toBigDecimal());

        int vint = 123456789;
        tabImptes.setTbIpuCed(vint); // int
        assertEquals(asInt(vint), asInt(tabImptes.getTbIpuCed()));

        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tabImptes.setTbIpuCed(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tabImptes.getTbIpuCed()));
    }
}

