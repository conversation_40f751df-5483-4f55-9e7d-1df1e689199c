package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-HORA-TIMESTAMP.
 *
 */
class WsHoraTimestampTest {

    private WsHoraTimestamp wsHoraTimestamp;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsHoraTimestamp = new WsHoraTimestamp();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsHoraTimestamp wsHoraTimestamp2 = new WsHoraTimestamp(parent);
        assertNotNull(wsHoraTimestamp2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsHoraTimestamp wsHoraTimestamp3 = new WsHoraTimestamp(parent, sibling);
        assertNotNull(wsHoraTimestamp3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsHoraTimestamp.getWsHoraTimestamp());

        assertNotNull(wsHoraTimestamp.getWsMinTimestamp());

        assertNotNull(wsHoraTimestamp.getWsSegTimestamp());

    }

    // property wsHoraTimestamp
    @Test
    void testGetterSetterWsHoraTimestamp() {

        assertNotNull(wsHoraTimestamp.getWsHoraTimestamp());

        int vint = 12;
        wsHoraTimestamp.setWsHoraTimestamp(vint); // int
        assertEquals(asInt(vint), asInt(wsHoraTimestamp.getWsHoraTimestamp()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsHoraTimestamp.setWsHoraTimestamp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHoraTimestamp.getWsHoraTimestamp()));
    }

    // property wsMinTimestamp
    @Test
    void testGetterSetterWsMinTimestamp() {

        assertNotNull(wsHoraTimestamp.getWsMinTimestamp());

        int vint = 12;
        wsHoraTimestamp.setWsMinTimestamp(vint); // int
        assertEquals(asInt(vint), asInt(wsHoraTimestamp.getWsMinTimestamp()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsHoraTimestamp.setWsMinTimestamp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHoraTimestamp.getWsMinTimestamp()));
    }

    // property wsSegTimestamp
    @Test
    void testGetterSetterWsSegTimestamp() {

        assertNotNull(wsHoraTimestamp.getWsSegTimestamp());

        int vint = 12;
        wsHoraTimestamp.setWsSegTimestamp(vint); // int
        assertEquals(asInt(vint), asInt(wsHoraTimestamp.getWsSegTimestamp()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsHoraTimestamp.setWsSegTimestamp(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHoraTimestamp.getWsSegTimestamp()));
    }
}

