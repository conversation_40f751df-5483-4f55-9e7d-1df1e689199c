package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICADORES-DE-NULOS.
 *
 */
class IndicadoresDeNulosTest {

    private IndicadoresDeNulos indicadoresDeNulos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indicadoresDeNulos = new IndicadoresDeNulos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {





    }

    // property indicadoresCna
    @Test
    void testGetterSetterIndicadoresCna() {

        assertNotNull(indicadoresDeNulos.getIndicadoresCna());
    }

    // property indicadoresDsc
    @Test
    void testGetterSetterIndicadoresDsc() {

        assertNotNull(indicadoresDeNulos.getIndicadoresDsc());
    }

    // property indicadoresDma0
    @Test
    void testGetterSetterIndicadoresDma0() {

        assertNotNull(indicadoresDeNulos.getIndicadoresDma0());
    }

    // property indicadoresCma0
    @Test
    void testGetterSetterIndicadoresCma0() {

        assertNotNull(indicadoresDeNulos.getIndicadoresCma0());
    }

    // property indicadoresAgt0
    @Test
    void testGetterSetterIndicadoresAgt0() {

        assertNotNull(indicadoresDeNulos.getIndicadoresAgt0());
    }

    // property indcnatfp
    @Test
    void testGetterSetterIndcnatfp() {

        // Property: indicadoresCna.indcnatfp -> 05 INDCNATFP S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndcnatfp());
        int vint = 1234;
        indicadoresDeNulos.setIndcnatfp(vint); // int
    }

    // property indcnamte
    @Test
    void testGetterSetterIndcnamte() {

        // Property: indicadoresCna.indcnamte -> 05 INDCNAMTE S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndcnamte());
        int vint = 1234;
        indicadoresDeNulos.setIndcnamte(vint); // int
    }

    // property inddscdsg
    @Test
    void testGetterSetterInddscdsg() {

        // Property: indicadoresDsc.inddscdsg -> 05 INDDSCDSG S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getInddscdsg());
        int vint = 1234;
        indicadoresDeNulos.setInddscdsg(vint); // int
    }

    // property inddmaagt
    @Test
    void testGetterSetterInddmaagt() {

        // Property: indicadoresDma0.inddmaagt -> 05 INDDMAAGT S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getInddmaagt());
        int vint = 1234;
        indicadoresDeNulos.setInddmaagt(vint); // int
    }

    // property inddmaidr
    @Test
    void testGetterSetterInddmaidr() {

        // Property: indicadoresDma0.inddmaidr -> 05 INDDMAIDR S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getInddmaidr());
        int vint = 1234;
        indicadoresDeNulos.setInddmaidr(vint); // int
    }

    // property inddmacol
    @Test
    void testGetterSetterInddmacol() {

        // Property: indicadoresDma0.inddmacol -> 05 INDDMACOL S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getInddmacol());
        int vint = 1234;
        indicadoresDeNulos.setInddmacol(vint); // int
    }

    // property inddmatdm
    @Test
    void testGetterSetterInddmatdm() {

        // Property: indicadoresDma0.inddmatdm -> 05 INDDMATDM S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getInddmatdm());
        int vint = 1234;
        indicadoresDeNulos.setInddmatdm(vint); // int
    }

    // property inddmacpo
    @Test
    void testGetterSetterInddmacpo() {

        // Property: indicadoresDma0.inddmacpo -> 05 INDDMACPO S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getInddmacpo());
        int vint = 1234;
        indicadoresDeNulos.setInddmacpo(vint); // int
    }

    // property indcmades
    @Test
    void testGetterSetterIndcmades() {

        // Property: indicadoresCma0.indcmades -> 05 INDCMADES S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndcmades());
        int vint = 1234;
        indicadoresDeNulos.setIndcmades(vint); // int
    }

    // property indagtnal
    @Test
    void testGetterSetterIndagtnal() {

        // Property: indicadoresAgt0.indagtnal -> 05 INDAGTNAL S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndagtnal());
        int vint = 1234;
        indicadoresDeNulos.setIndagtnal(vint); // int
    }

    // property indagtpef
    @Test
    void testGetterSetterIndagtpef() {

        // Property: indicadoresAgt0.indagtpef -> 05 INDAGTPEF S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndagtpef());
        int vint = 1234;
        indicadoresDeNulos.setIndagtpef(vint); // int
    }

    // property indagttca
    @Test
    void testGetterSetterIndagttca() {

        // Property: indicadoresAgt0.indagttca -> 05 INDAGTTCA S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndagttca());
        int vint = 1234;
        indicadoresDeNulos.setIndagttca(vint); // int
    }

    // property indagtmte
    @Test
    void testGetterSetterIndagtmte() {

        // Property: indicadoresAgt0.indagtmte -> 05 INDAGTMTE S9(4) COMPUTATIONAL
        assertNotNull(indicadoresDeNulos.getIndagtmte());
        int vint = 1234;
        indicadoresDeNulos.setIndagtmte(vint); // int
    }
}

