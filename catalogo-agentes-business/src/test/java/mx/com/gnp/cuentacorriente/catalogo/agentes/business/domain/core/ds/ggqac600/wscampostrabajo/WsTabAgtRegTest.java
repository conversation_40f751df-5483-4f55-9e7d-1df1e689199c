package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TAB-AGT-REG.
 *
 */
class WsTabAgtRegTest {

    private WsTabAgtReg wsTabAgtReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTabAgtReg = new WsTabAgtReg();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsTabAgtReg.getWsTabAgtN());


    }

    // property wsTabAgtN
    @Test
    void testGetterSetterWsTabAgtN() {

        assertNotNull(wsTabAgtReg.getWsTabAgtN());

        BigDecimal vBigDecimal = new BigDecimal("1234567890");
        wsTabAgtReg.setWsTabAgtN(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsTabAgtReg.getWsTabAgtN().toBigDecimal());

        int vint = 123456789;
        wsTabAgtReg.setWsTabAgtN(vint); // int
        assertEquals(asInt(vint), asInt(wsTabAgtReg.getWsTabAgtN()));

        NumericVar vINumericValue = new NumericVar(10, 0);;
        vINumericValue.setValue("1234567890");
        wsTabAgtReg.setWsTabAgtN(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsTabAgtReg.getWsTabAgtN()));
    }

    // property wsTabAgtR
    @Test
    void testGetterSetterWsTabAgtR() {

        assertNotNull(wsTabAgtReg.getWsTabAgtR());
    }

    // property wsTabFiller
    @Test
    void testGetterSetterWsTabFiller() {

        // Property: wsTabAgtR.wsTabFiller -> 15 WS-TAB-FILLER X(03)
        assertNotNull(wsTabAgtReg.getWsTabFiller());
        String vString = "ABC";
        wsTabAgtReg.setWsTabFiller(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsTabAgtReg.setWsTabFiller(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsTabAgt
    @Test
    void testGetterSetterWsTabAgt() {

        // Property: wsTabAgtR.wsTabAgt -> 15 WS-TAB-AGT X(07)
        assertNotNull(wsTabAgtReg.getWsTabAgt());
        String vString = "ABCabc1";
        wsTabAgtReg.setWsTabAgt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wsTabAgtReg.setWsTabAgt(vIAlphanumericValue); // IAlphanumericValue
    }
}

