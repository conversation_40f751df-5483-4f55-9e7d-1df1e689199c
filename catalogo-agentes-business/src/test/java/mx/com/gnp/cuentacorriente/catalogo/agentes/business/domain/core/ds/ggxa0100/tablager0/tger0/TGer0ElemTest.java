package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablager0.tger0;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-GER0-ELEM.
 *
 */
class TGer0ElemTest {

    private TGer0Elem tGer0Elem;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tGer0Elem = new TGer0Elem();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TGer0Elem tGer0Elem2 = new TGer0Elem(parent);
        assertNotNull(tGer0Elem2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 5);
        TGer0Elem tGer0Elem3 = new TGer0Elem(parent, sibling);
        assertNotNull(tGer0Elem3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tGer0Elem.getSCnaGerZon());

        assertNotNull(tGer0Elem.getSGerLimitad());

    }

    // property sCnaGerZon
    @Test
    void testGetterSetterSCnaGerZon() {

        assertNotNull(tGer0Elem.getSCnaGerZon());

        String vString = "ABCa";
        tGer0Elem.setSCnaGerZon(vString); // String
        assertEquals(asStr(vString), asStr(tGer0Elem.getSCnaGerZon()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tGer0Elem.setSCnaGerZon(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tGer0Elem.getSCnaGerZon()));
    }

    // property sGerLimitad
    @Test
    void testGetterSetterSGerLimitad() {

        assertNotNull(tGer0Elem.getSGerLimitad());

        String vString = "A";
        tGer0Elem.setSGerLimitad(vString); // String
        assertEquals(asStr(vString), asStr(tGer0Elem.getSGerLimitad()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tGer0Elem.setSGerLimitad(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tGer0Elem.getSGerLimitad()));
    }
}

