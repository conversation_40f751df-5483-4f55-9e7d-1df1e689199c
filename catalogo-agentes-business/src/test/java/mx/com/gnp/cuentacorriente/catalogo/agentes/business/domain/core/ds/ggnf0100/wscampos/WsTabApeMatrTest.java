package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TAB-APE-MATR.
 *
 */
class WsTabApeMatrTest {

    private WsTabApeMatr wsTabApeMatr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTabApeMatr = new WsTabApeMatr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsTabApeMatr wsTabApeMatr2 = new WsTabApeMatr(parent);
        assertNotNull(wsTabApeMatr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 30);
        WsTabApeMatr wsTabApeMatr3 = new WsTabApeMatr(parent, sibling);
        assertNotNull(wsTabApeMatr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsTabApeMatr.getEleApeMat());

    }

    // property eleApeMat
    @Test
    void testGetterSetterEleApeMat() {

        assertNotNull(wsTabApeMatr.getEleApeMat());
        assertNotNull(wsTabApeMatr.getEleApeMat(1));

        String vString = "A";
        wsTabApeMatr.getEleApeMat().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsTabApeMatr.getEleApeMat(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsTabApeMatr.getEleApeMat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsTabApeMatr.getEleApeMat(1)));
    }
}

