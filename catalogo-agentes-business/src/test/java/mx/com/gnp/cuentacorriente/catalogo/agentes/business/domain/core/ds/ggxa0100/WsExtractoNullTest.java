package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-EXTRACTO-NULL.
 *
 */
class WsExtractoNullTest {

    private WsExtractoNull wsExtractoNull;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsExtractoNull = new WsExtractoNull();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsExtractoNull.setAgtAgtIdr1(0);
        assertEquals(0, wsExtractoNull.getAgtAgtIdr1());

        wsExtractoNull.setAgtAgtNom1(0);
        assertEquals(0, wsExtractoNull.getAgtAgtNom1());

        wsExtractoNull.setAgtAgtApePat1(0);
        assertEquals(0, wsExtractoNull.getAgtAgtApePat1());

        wsExtractoNull.setAgtAgtApeMat1(0);
        assertEquals(0, wsExtractoNull.getAgtAgtApeMat1());

        wsExtractoNull.setAgtAgtNip1(0);
        assertEquals(0, wsExtractoNull.getAgtAgtNip1());

        wsExtractoNull.setCmaCmaDes1(0);
        assertEquals(0, wsExtractoNull.getCmaCmaDes1());

        wsExtractoNull.setCnaCnaNum1(0);
        assertEquals(0, wsExtractoNull.getCnaCnaNum1());

        wsExtractoNull.setCnaEmpCve1(0);
        assertEquals(0, wsExtractoNull.getCnaEmpCve1());

        wsExtractoNull.setCnaOfnCve1(0);
        assertEquals(0, wsExtractoNull.getCnaOfnCve1());

        wsExtractoNull.setCnaTfpCve1(0);
        assertEquals(0, wsExtractoNull.getCnaTfpCve1());

        wsExtractoNull.setCnaGerZonEnv1(0);
        assertEquals(0, wsExtractoNull.getCnaGerZonEnv1());

        wsExtractoNull.setFlaFlaNumN(0);
        assertEquals(0, wsExtractoNull.getFlaFlaNumN());

        wsExtractoNull.setCeoCeoCve1(0);
        assertEquals(0, wsExtractoNull.getCeoCeoCve1());

        wsExtractoNull.setCeoCeoNom1(0);
        assertEquals(0, wsExtractoNull.getCeoCeoNom1());

        wsExtractoNull.setWsOrvCve1(0);
        assertEquals(0, wsExtractoNull.getWsOrvCve1());

        wsExtractoNull.setAgtRazSoc1(0);
        assertEquals(0, wsExtractoNull.getAgtRazSoc1());

        wsExtractoNull.setAgtPefCve1(0);
        assertEquals(0, wsExtractoNull.getAgtPefCve1());

    }

    // property agtAgtIdr1
    @Test
    void testGetterSetterAgtAgtIdr1() {

        assertNotNull(wsExtractoNull.getAgtAgtIdr1());

        int vint = 1234;
        wsExtractoNull.setAgtAgtIdr1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getAgtAgtIdr1()));
    }

    // property agtAgtNom1
    @Test
    void testGetterSetterAgtAgtNom1() {

        assertNotNull(wsExtractoNull.getAgtAgtNom1());

        int vint = 1234;
        wsExtractoNull.setAgtAgtNom1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getAgtAgtNom1()));
    }

    // property agtAgtApePat1
    @Test
    void testGetterSetterAgtAgtApePat1() {

        assertNotNull(wsExtractoNull.getAgtAgtApePat1());

        int vint = 1234;
        wsExtractoNull.setAgtAgtApePat1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getAgtAgtApePat1()));
    }

    // property agtAgtApeMat1
    @Test
    void testGetterSetterAgtAgtApeMat1() {

        assertNotNull(wsExtractoNull.getAgtAgtApeMat1());

        int vint = 1234;
        wsExtractoNull.setAgtAgtApeMat1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getAgtAgtApeMat1()));
    }

    // property agtAgtNip1
    @Test
    void testGetterSetterAgtAgtNip1() {

        assertNotNull(wsExtractoNull.getAgtAgtNip1());

        int vint = 1234;
        wsExtractoNull.setAgtAgtNip1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getAgtAgtNip1()));
    }

    // property cmaCmaDes1
    @Test
    void testGetterSetterCmaCmaDes1() {

        assertNotNull(wsExtractoNull.getCmaCmaDes1());

        int vint = 1234;
        wsExtractoNull.setCmaCmaDes1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCmaCmaDes1()));
    }

    // property cnaCnaNum1
    @Test
    void testGetterSetterCnaCnaNum1() {

        assertNotNull(wsExtractoNull.getCnaCnaNum1());

        int vint = 1234;
        wsExtractoNull.setCnaCnaNum1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCnaCnaNum1()));
    }

    // property cnaEmpCve1
    @Test
    void testGetterSetterCnaEmpCve1() {

        assertNotNull(wsExtractoNull.getCnaEmpCve1());

        int vint = 1234;
        wsExtractoNull.setCnaEmpCve1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCnaEmpCve1()));
    }

    // property cnaOfnCve1
    @Test
    void testGetterSetterCnaOfnCve1() {

        assertNotNull(wsExtractoNull.getCnaOfnCve1());

        int vint = 1234;
        wsExtractoNull.setCnaOfnCve1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCnaOfnCve1()));
    }

    // property cnaTfpCve1
    @Test
    void testGetterSetterCnaTfpCve1() {

        assertNotNull(wsExtractoNull.getCnaTfpCve1());

        int vint = 1234;
        wsExtractoNull.setCnaTfpCve1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCnaTfpCve1()));
    }

    // property cnaGerZonEnv1
    @Test
    void testGetterSetterCnaGerZonEnv1() {

        assertNotNull(wsExtractoNull.getCnaGerZonEnv1());

        int vint = 1234;
        wsExtractoNull.setCnaGerZonEnv1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCnaGerZonEnv1()));
    }

    // property flaFlaNumN
    @Test
    void testGetterSetterFlaFlaNumN() {

        assertNotNull(wsExtractoNull.getFlaFlaNumN());

        int vint = 1234;
        wsExtractoNull.setFlaFlaNumN(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getFlaFlaNumN()));
    }

    // property ceoCeoCve1
    @Test
    void testGetterSetterCeoCeoCve1() {

        assertNotNull(wsExtractoNull.getCeoCeoCve1());

        int vint = 1234;
        wsExtractoNull.setCeoCeoCve1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCeoCeoCve1()));
    }

    // property ceoCeoNom1
    @Test
    void testGetterSetterCeoCeoNom1() {

        assertNotNull(wsExtractoNull.getCeoCeoNom1());

        int vint = 1234;
        wsExtractoNull.setCeoCeoNom1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getCeoCeoNom1()));
    }

    // property wsOrvCve1
    @Test
    void testGetterSetterWsOrvCve1() {

        assertNotNull(wsExtractoNull.getWsOrvCve1());

        int vint = 1234;
        wsExtractoNull.setWsOrvCve1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getWsOrvCve1()));
    }

    // property agtRazSoc1
    @Test
    void testGetterSetterAgtRazSoc1() {

        assertNotNull(wsExtractoNull.getAgtRazSoc1());

        int vint = 1234;
        wsExtractoNull.setAgtRazSoc1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getAgtRazSoc1()));
    }

    // property agtPefCve1
    @Test
    void testGetterSetterAgtPefCve1() {

        assertNotNull(wsExtractoNull.getAgtPefCve1());

        int vint = 1234;
        wsExtractoNull.setAgtPefCve1(vint); // int
        assertEquals(asInt(vint), asInt(wsExtractoNull.getAgtPefCve1()));
    }
}

