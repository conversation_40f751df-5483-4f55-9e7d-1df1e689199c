package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: L-PARAMS-SAL.
 *
 */
class LParamsSalTest {

    private LParamsSal lParamsSal;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        lParamsSal = new LParamsSal();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        LParamsSal lParamsSal2 = new LParamsSal(parent);
        assertNotNull(lParamsSal2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 313);
        LParamsSal lParamsSal3 = new LParamsSal(parent, sibling);
        assertNotNull(lParamsSal3);
    }

    @Test
    void testInitialize() {
        assertNotNull(lParamsSal.getLRegfis());

        assertNotNull(lParamsSal.getLNombre());

        assertNotNull(lParamsSal.getLPcenvi());

        assertNotNull(lParamsSal.getLPcevid());

    }

    // property lRegfis
    @Test
    void testGetterSetterLRegfis() {

        assertNotNull(lParamsSal.getLRegfis());

        String vString = "ABC";
        lParamsSal.setLRegfis(vString); // String
        assertEquals(asStr(vString), asStr(lParamsSal.getLRegfis()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        lParamsSal.setLRegfis(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(lParamsSal.getLRegfis()));
    }

    // property lNombre
    @Test
    void testGetterSetterLNombre() {

        assertNotNull(lParamsSal.getLNombre());

        String vString = "ABCabc1230";
        lParamsSal.setLNombre(vString); // String
        assertEquals(asStr(vString), asStr(lParamsSal.getLNombre()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        lParamsSal.setLNombre(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(lParamsSal.getLNombre()));
    }

    // property lPcenvi
    @Test
    void testGetterSetterLPcenvi() {

        assertNotNull(lParamsSal.getLPcenvi());

        String vString = "ABCab";
        lParamsSal.setLPcenvi(vString); // String
        assertEquals(asStr(vString), asStr(lParamsSal.getLPcenvi()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        lParamsSal.setLPcenvi(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(lParamsSal.getLPcenvi()));
    }

    // property lPcevid
    @Test
    void testGetterSetterLPcevid() {

        assertNotNull(lParamsSal.getLPcevid());

        String vString = "ABCab";
        lParamsSal.setLPcevid(vString); // String
        assertEquals(asStr(vString), asStr(lParamsSal.getLPcevid()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        lParamsSal.setLPcevid(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(lParamsSal.getLPcevid()));
    }
}

