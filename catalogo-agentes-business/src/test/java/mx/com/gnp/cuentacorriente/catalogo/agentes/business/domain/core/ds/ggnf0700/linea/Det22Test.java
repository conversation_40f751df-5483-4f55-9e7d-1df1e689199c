package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DET22.
 *
 */
class Det22Test {

    private Det22 det22;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        det22 = new Det22();
    }


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Det22 det222 = new Det22(parent);
        assertNotNull(det222);
        AlphanumericVar sibling = new AlphanumericVar(parent, 132);
        Det22 det223 = new Det22(parent, sibling);
        assertNotNull(det223);
    }

    @Test
    void testInitialize() {
        assertNotNull(det22.getD3FlaDes());

        assertNotNull(det22.getD3AgtTraEsp());

        assertNotNull(det22.getD3CnaFecCnx());

        assertNotNull(det22.getD3GnaCve());

        assertNotNull(det22.getD3EsaCveFla());

        assertNotNull(det22.getD3FlaGerZon());

        assertNotNull(det22.getD3OfnCveCna());

        assertNotNull(det22.getD3AgtIdrGte());

    }

    // property d3FlaDes
    @Test
    void testGetterSetterD3FlaDes() {

        assertNotNull(det22.getD3FlaDes());

        String vString = "ABCabc1230";
        det22.setD3FlaDes(vString); // String
        assertEquals(asStr(vString), asStr(det22.getD3FlaDes()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det22.setD3FlaDes(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det22.getD3FlaDes()));
    }

    // property d3AgtTraEsp
    @Test
    void testGetterSetterD3AgtTraEsp() {

        assertNotNull(det22.getD3AgtTraEsp());

        String vString = "A";
        det22.setD3AgtTraEsp(vString); // String
        assertEquals(asStr(vString), asStr(det22.getD3AgtTraEsp()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        det22.setD3AgtTraEsp(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det22.getD3AgtTraEsp()));
    }

    // property d3CnaFecCnx
    @Test
    void testGetterSetterD3CnaFecCnx() {

        assertNotNull(det22.getD3CnaFecCnx());

        String vString = "ABCabc1230";
        det22.setD3CnaFecCnx(vString); // String
        assertEquals(asStr(vString), asStr(det22.getD3CnaFecCnx()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        det22.setD3CnaFecCnx(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(det22.getD3CnaFecCnx()));
    }

    // property d3GnaCve
    @Test
    void testGetterSetterD3GnaCve() {

        assertNotNull(det22.getD3GnaCve());

        String vString = "   0";
        det22.setD3GnaCve(vString); // String
        assertEquals(asStr(vString), asStr(det22.getD3GnaCve()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det22.setD3GnaCve(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det22.getD3GnaCve()));
    }

    // property d3EsaCveFla
    @Test
    void testGetterSetterD3EsaCveFla() {

        assertNotNull(det22.getD3EsaCveFla());

        String vString = "   0";
        det22.setD3EsaCveFla(vString); // String
        assertEquals(asStr(vString), asStr(det22.getD3EsaCveFla()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det22.setD3EsaCveFla(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(det22.getD3EsaCveFla()));
    }

    // property d3FlaGerZon
    @Test
    void testGetterSetterD3FlaGerZon() {

        assertNotNull(det22.getD3FlaGerZon());

        String vString = " 000";
        det22.setD3FlaGerZon(vString); // String
        assertEquals(asStr(vString), asStr(det22.getD3FlaGerZon()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det22.setD3FlaGerZon(vIValue); // IValue
        // format("1234", "Z999")
        assertNotNull(asStr(det22.getD3FlaGerZon()));
    }

    // property d3OfnCveCna
    @Test
    void testGetterSetterD3OfnCveCna() {

        assertNotNull(det22.getD3OfnCveCna());

        String vString = "  00";
        det22.setD3OfnCveCna(vString); // String
        assertEquals(asStr(vString), asStr(det22.getD3OfnCveCna()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        det22.setD3OfnCveCna(vIValue); // IValue
        // format("1234", "ZZ99")
        assertNotNull(asStr(det22.getD3OfnCveCna()));
    }

    // property d3AgtIdrGte
    @Test
    void testGetterSetterD3AgtIdrGte() {

        assertNotNull(det22.getD3AgtIdrGte());

        int vint = 12345;
        det22.setD3AgtIdrGte(vint); // int
        assertEquals(asInt(vint), asInt(det22.getD3AgtIdrGte()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        det22.setD3AgtIdrGte(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(det22.getD3AgtIdrGte()));
    }
}
