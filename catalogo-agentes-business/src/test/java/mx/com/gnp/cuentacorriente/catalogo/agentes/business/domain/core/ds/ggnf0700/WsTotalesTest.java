package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-TOTALES.
 *
 */
class WsTotalesTest {

    private WsTotales wsTotales;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsTotales = new WsTotales();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsTotales.setTotLeidos(0);
        assertEquals(0, wsTotales.getTotLeidos());

        wsTotales.setTotGrabados(0);
        assertEquals(0, wsTotales.getTotGrabados());

        wsTotales.setTotCambios(0);
        assertEquals(0, wsTotales.getTotCambios());

        wsTotales.setTotDuplicados(0);
        assertEquals(0, wsTotales.getTotDuplicados());

    }

    // property totLeidos
    @Test
    void testGetterSetterTotLeidos() {

        assertNotNull(wsTotales.getTotLeidos());

        int vint = 123456;
        wsTotales.setTotLeidos(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotLeidos()));
    }

    // property totGrabados
    @Test
    void testGetterSetterTotGrabados() {

        assertNotNull(wsTotales.getTotGrabados());

        int vint = 123456;
        wsTotales.setTotGrabados(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotGrabados()));
    }

    // property totCambios
    @Test
    void testGetterSetterTotCambios() {

        assertNotNull(wsTotales.getTotCambios());

        int vint = 123456;
        wsTotales.setTotCambios(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotCambios()));
    }

    // property totDuplicados
    @Test
    void testGetterSetterTotDuplicados() {

        assertNotNull(wsTotales.getTotDuplicados());

        int vint = 123456;
        wsTotales.setTotDuplicados(vint); // int
        assertEquals(asInt(vint), asInt(wsTotales.getTotDuplicados()));
    }
}

