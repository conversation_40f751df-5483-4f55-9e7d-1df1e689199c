package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICADORES-DSC.
 *
 */
class IndicadoresDscTest {

    private IndicadoresDsc indicadoresDsc;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indicadoresDsc = new IndicadoresDsc();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        indicadoresDsc.setInddscdsg(0);
        assertEquals(0, indicadoresDsc.getInddscdsg());

    }

    // property inddscdsg
    @Test
    void testGetterSetterInddscdsg() {

        assertNotNull(indicadoresDsc.getInddscdsg());

        int vint = 1234;
        indicadoresDsc.setInddscdsg(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresDsc.getInddscdsg()));
    }
}

