package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.common.cbl.exception.GobackException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;
import com.base100.caravel.support.common.cbl.context.IProgramCblContext;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa002qcpy.LcevLinkReg;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

class Ggnf0100Test {
    Ggnf0100 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggnf0100.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);

        // Mock program context to break infinite loops
        IProgramCblContext programCblContext = pgm.getProgramCblContext();
        doAnswer(invocation -> {
            String programName = invocation.getArgument(0);
            if (programName.equals("GGNF0100")){
                LcevLinkReg lbdeLinkReg = invocation.getArgument(1);
                lbdeLinkReg.setLcevSwErr("1");
            }
            return null;
        }).when(programCblContext).runProgram(anyString(), any(LcevLinkReg.class));
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(GobackException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p2100InicioGfvtagt0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p2100InicioGfvtagt0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p2100InicioGfvtagt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2100InicioGfvtagt0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p2100InicioGfvtagt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2100LeerGfvtagt0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p2100LeerGfvtagt0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p2100LeerGfvtagt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2100LeerGfvtagt0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p2100LeerGfvtagt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2100CerrarGfvtagt0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p2100CerrarGfvtagt0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p2100CerrarGfvtagt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2100CerrarGfvtagt0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p2100CerrarGfvtagt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3200ProcesoGfvtcna0 test conditions:
        wsFlaNum0 : ["N", "XXN", "P", "XXP"]
        wsContador : [500, 501]
        wsCuantosCna : [0, 1]
    */
    @Test
    void p3200ProcesoGfvtcna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsCampos().setWsFlaNum0("N");
        pgm.getWsContadores().setWsContador(500);
        pgm.getWsCampos().setWsCuantosCna(0);
        try {
            pgm.p3200ProcesoGfvtcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3200ProcesoGfvtcna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsCampos().setWsFlaNum0("XXN");
            pgm.getWsContadores().setWsContador(501);
            pgm.getWsCampos().setWsCuantosCna(1);
            try {
                pgm.p3200ProcesoGfvtcna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p3200ProcesoGfvtcna0Test3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getWsCampos().setWsFlaNum0("P");
                pgm.getWsContadores().setWsContador(501);
                pgm.getWsCampos().setWsCuantosCna(1);
                try {
                    pgm.p3200ProcesoGfvtcna0();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p3200ProcesoGfvtcna0Test4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getWsCampos().setWsFlaNum0("XXP");
                    pgm.getWsContadores().setWsContador(501);
                    pgm.getWsCampos().setWsCuantosCna(1);
                    try {
                        pgm.p3200ProcesoGfvtcna0();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p3300ProcesoGfvtfla0 test conditions:
        wsFlaNum0 : ["N", "XXN", "P", "XXP"]
        wsContador : [500, 501]
        esaCve : [1, 2]
        wsFinMte : [0, 1]
        wsFinAfo : [0, 1]
        taaCve : ["1", "XX1"]
        agtIdr : [50000, 50001]
        flaDes : ["10", "XX10"]
        agtIdrRcl : [99999, 100000]
        wsFinFla1 : [0, 1]
        wsFinFla3 : [0, 1]
        pkEsGerente : ["S", "XXS"]
        wsFinDma : [0, 1]
        pefCve : [2, 3]
        eciCve : [1, 2, 3, 4]
        agtIdr : [50000, 50001]
        gnaCve : [1, 2]
        nalCve : ["MEX", "XXMEX"]
        wsFinDrc : [0, 1]
        wsCuantosFla : [0, 1]
    */
    @Test
    void p3300ProcesoGfvtfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsCampos().setWsFlaNum0("N");
        pgm.getWsContadores().setWsContador(500);
        pgm.getDclgfvtmte0().setEsaCve(1);
        pgm.getWsIndicadores().setWsFinMte(0);
        pgm.getWsIndicadores().setWsFinAfo(0);
        pgm.getDclgfvtcna0().setTaaCve("1");
        pgm.getDclgfvtcna0().setAgtIdr(50000);
        pgm.getDclgfvtfla0().setFlaDes("10");
        pgm.getDclgfvtcna0().setAgtIdrRcl(99999);
        pgm.getWsIndicadores().setWsFinFla1(0);
        pgm.getWsIndicadores().setWsFinFla3(0);
        pgm.getRegMaestro().setPkEsGerente("S");
        pgm.getWsIndicadores().setWsFinDma(0);
        pgm.getDclgfvtagt0().setPefCve(2);
        pgm.getDclgfvtpra0().setEciCve(1);
        pgm.getDclgfvtfla0().setAgtIdr(50000);
        pgm.getDclgfvtcna0().setGnaCve(1);
        pgm.getDclgfvtagt0().setNalCve("MEX");
        pgm.getWsIndicadores().setWsFinDrc(0);
        pgm.getWsCampos().setWsCuantosFla(0);
        try {
            pgm.p3300ProcesoGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3300ProcesoGfvtfla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsCampos().setWsFlaNum0("XXN");
            pgm.getWsContadores().setWsContador(501);
            pgm.getDclgfvtmte0().setEsaCve(2);
            pgm.getWsIndicadores().setWsFinMte(1);
            pgm.getWsIndicadores().setWsFinAfo(1);
            pgm.getDclgfvtcna0().setTaaCve("XX1");
            pgm.getDclgfvtcna0().setAgtIdr(50001);
            pgm.getDclgfvtfla0().setFlaDes("XX10");
            pgm.getDclgfvtcna0().setAgtIdrRcl(100000);
            pgm.getWsIndicadores().setWsFinFla1(1);
            pgm.getWsIndicadores().setWsFinFla3(1);
            pgm.getRegMaestro().setPkEsGerente("XXS");
            pgm.getWsIndicadores().setWsFinDma(1);
            pgm.getDclgfvtagt0().setPefCve(3);
            pgm.getDclgfvtpra0().setEciCve(2);
            pgm.getDclgfvtfla0().setAgtIdr(50001);
            pgm.getDclgfvtcna0().setGnaCve(2);
            pgm.getDclgfvtagt0().setNalCve("XXMEX");
            pgm.getWsIndicadores().setWsFinDrc(1);
            pgm.getWsCampos().setWsCuantosFla(1);
            try {
                pgm.p3300ProcesoGfvtfla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p3300ProcesoGfvtfla0Test3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getWsCampos().setWsFlaNum0("P");
                pgm.getWsContadores().setWsContador(501);
                pgm.getDclgfvtmte0().setEsaCve(2);
                pgm.getWsIndicadores().setWsFinMte(1);
                pgm.getWsIndicadores().setWsFinAfo(1);
                pgm.getDclgfvtcna0().setTaaCve("XX1");
                pgm.getDclgfvtcna0().setAgtIdr(50001);
                pgm.getDclgfvtfla0().setFlaDes("XX10");
                pgm.getDclgfvtcna0().setAgtIdrRcl(100000);
                pgm.getWsIndicadores().setWsFinFla1(1);
                pgm.getWsIndicadores().setWsFinFla3(1);
                pgm.getRegMaestro().setPkEsGerente("XXS");
                pgm.getWsIndicadores().setWsFinDma(1);
                pgm.getDclgfvtagt0().setPefCve(3);
                pgm.getDclgfvtpra0().setEciCve(3);
                pgm.getDclgfvtfla0().setAgtIdr(50001);
                pgm.getDclgfvtcna0().setGnaCve(2);
                pgm.getDclgfvtagt0().setNalCve("XXMEX");
                pgm.getWsIndicadores().setWsFinDrc(1);
                pgm.getWsCampos().setWsCuantosFla(1);
                try {
                    pgm.p3300ProcesoGfvtfla0();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p3300ProcesoGfvtfla0Test4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getWsCampos().setWsFlaNum0("XXP");
                    pgm.getWsContadores().setWsContador(501);
                    pgm.getDclgfvtmte0().setEsaCve(2);
                    pgm.getWsIndicadores().setWsFinMte(1);
                    pgm.getWsIndicadores().setWsFinAfo(1);
                    pgm.getDclgfvtcna0().setTaaCve("XX1");
                    pgm.getDclgfvtcna0().setAgtIdr(50001);
                    pgm.getDclgfvtfla0().setFlaDes("XX10");
                    pgm.getDclgfvtcna0().setAgtIdrRcl(100000);
                    pgm.getWsIndicadores().setWsFinFla1(1);
                    pgm.getWsIndicadores().setWsFinFla3(1);
                    pgm.getRegMaestro().setPkEsGerente("XXS");
                    pgm.getWsIndicadores().setWsFinDma(1);
                    pgm.getDclgfvtagt0().setPefCve(3);
                    pgm.getDclgfvtpra0().setEciCve(4);
                    pgm.getDclgfvtfla0().setAgtIdr(50001);
                    pgm.getDclgfvtcna0().setGnaCve(2);
                    pgm.getDclgfvtagt0().setNalCve("XXMEX");
                    pgm.getWsIndicadores().setWsFinDrc(1);
                    pgm.getWsCampos().setWsCuantosFla(1);
                    try {
                        pgm.p3300ProcesoGfvtfla0();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p3400RegresoCamposNac test conditions:
        esaCve : [1, 2]
        wsFinMte : [0, 1]
        wsFinAfo : [0, 1]
    */
    @Test
    void p3400RegresoCamposNacTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtmte0().setEsaCve(1);
        pgm.getWsIndicadores().setWsFinMte(0);
        pgm.getWsIndicadores().setWsFinAfo(0);
        try {
            pgm.p3400RegresoCamposNac();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3400RegresoCamposNacTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getDclgfvtmte0().setEsaCve(2);
            pgm.getWsIndicadores().setWsFinMte(1);
            pgm.getWsIndicadores().setWsFinAfo(1);
            try {
                pgm.p3400RegresoCamposNac();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3400RegresoCamposProv test conditions:
        esaCve : [1, 2]
        wsFinMte : [0, 1]
        wsFinAfo : [0, 1]
    */
    @Test
    void p3400RegresoCamposProvTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtmte0().setEsaCve(1);
        pgm.getWsIndicadores().setWsFinMte(0);
        pgm.getWsIndicadores().setWsFinAfo(0);
        try {
            pgm.p3400RegresoCamposProv();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3400RegresoCamposProvTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getDclgfvtmte0().setEsaCve(2);
            pgm.getWsIndicadores().setWsFinMte(1);
            pgm.getWsIndicadores().setWsFinAfo(1);
            try {
                pgm.p3400RegresoCamposProv();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3410ObtieneJefes test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p3410ObtieneJefesTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p3410ObtieneJefes();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3410ObtieneJefesTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p3410ObtieneJefes();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      cnaYFlaConIdrGte test conditions:
        wsFinCna1 : [0, 1]
        wsIndNal : [0, 1]
        wsFolioxx1 : ["N", "XXN", "P", "XXP"]
        wsIndProv : [0, 1]
    */
    @Test
    void cnaYFlaConIdrGteTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinCna1(0);
        pgm.getWsIndicadores().setWsIndNal(0);
        pgm.getWsCampos().setWsFolioxx1("N");
        pgm.getWsIndicadores().setWsIndProv(0);
        try {
            pgm.cnaYFlaConIdrGte();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void cnaYFlaConIdrGteTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsFinCna1(1);
            pgm.getWsIndicadores().setWsIndNal(1);
            pgm.getWsCampos().setWsFolioxx1("XXN");
            pgm.getWsIndicadores().setWsIndProv(1);
            try {
                pgm.cnaYFlaConIdrGte();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void cnaYFlaConIdrGteTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getWsIndicadores().setWsFinCna1(1);
                pgm.getWsIndicadores().setWsIndNal(1);
                pgm.getWsCampos().setWsFolioxx1("P");
                pgm.getWsIndicadores().setWsIndProv(1);
                try {
                    pgm.cnaYFlaConIdrGte();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void cnaYFlaConIdrGteTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getWsIndicadores().setWsFinCna1(1);
                    pgm.getWsIndicadores().setWsIndNal(1);
                    pgm.getWsCampos().setWsFolioxx1("XXP");
                    pgm.getWsIndicadores().setWsIndProv(1);
                    try {
                        pgm.cnaYFlaConIdrGte();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      zonaDcmOEnv test conditions:
        wsFinDrc : [0, 1]
        wsFinCna1 : [0, 1]
    */
    @Test
    void zonaDcmOEnvTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinDrc(0);
        pgm.getWsIndicadores().setWsFinCna1(0);
        try {
            pgm.zonaDcmOEnv();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void zonaDcmOEnvTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsFinDrc(1);
            pgm.getWsIndicadores().setWsFinCna1(1);
            try {
                pgm.zonaDcmOEnv();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3400AccesaGfvtfla2 test conditions:
        wsIndNal : [0, 1]
        wsFolioxx1 : ["N", "XXN", "P", "XXP"]
        wsIndProv : [0, 1]
    */
    @Test
    void p3400AccesaGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsIndNal(0);
        pgm.getWsCampos().setWsFolioxx1("N");
        pgm.getWsIndicadores().setWsIndProv(0);
        try {
            pgm.p3400AccesaGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3400AccesaGfvtfla2Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsIndNal(1);
            pgm.getWsCampos().setWsFolioxx1("XXN");
            pgm.getWsIndicadores().setWsIndProv(1);
            try {
                pgm.p3400AccesaGfvtfla2();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p3400AccesaGfvtfla2Test3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getWsIndicadores().setWsIndNal(1);
                pgm.getWsCampos().setWsFolioxx1("P");
                pgm.getWsIndicadores().setWsIndProv(1);
                try {
                    pgm.p3400AccesaGfvtfla2();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p3400AccesaGfvtfla2Test4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getWsIndicadores().setWsIndNal(1);
                    pgm.getWsCampos().setWsFolioxx1("XXP");
                    pgm.getWsIndicadores().setWsIndProv(1);
                    try {
                        pgm.p3400AccesaGfvtfla2();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      tratarClaveAdicional test conditions:
        wsFinCna1 : [0, 1]
    */
    @Test
    void tratarClaveAdicionalTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinCna1(0);
        try {
            pgm.tratarClaveAdicional();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void tratarClaveAdicionalTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsFinCna1(1);
            try {
                pgm.tratarClaveAdicional();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3450RegresoResto test conditions:
        taaCve : ["1", "XX1"]
        agtIdr : [50000, 50001]
        flaDes : ["10", "XX10"]
        agtIdrRcl : [99999, 100000]
        wsFinFla1 : [0, 1]
        wsFinFla3 : [0, 1]
        pkEsGerente : ["S", "XXS"]
        wsFinDma : [0, 1]
        pefCve : [2, 3]
        eciCve : [1, 2, 3, 4]
        agtIdr : [50000, 50001]
        gnaCve : [1, 2]
        nalCve : ["MEX", "XXMEX"]
        wsFinDrc : [0, 1]
        wsFinCna1 : [0, 1]
        wsFinMnd : [0, 1]
        wsFinPob : [0, 1]
        wsFinDmz : [0, 1]
        paiCve : ["MEX", "XXMEX"]
        wsFinCma : [0, 1]
    */
    @Test
    void p3450RegresoRestoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getDclgfvtcna0().setTaaCve("1");
        pgm.getDclgfvtcna0().setAgtIdr(50000);
        pgm.getDclgfvtfla0().setFlaDes("10");
        pgm.getDclgfvtcna0().setAgtIdrRcl(99999);
        pgm.getWsIndicadores().setWsFinFla1(0);
        pgm.getWsIndicadores().setWsFinFla3(0);
        pgm.getRegMaestro().setPkEsGerente("S");
        pgm.getWsIndicadores().setWsFinDma(0);
        pgm.getDclgfvtagt0().setPefCve(2);
        pgm.getDclgfvtpra0().setEciCve(1);
        pgm.getDclgfvtfla0().setAgtIdr(50000);
        pgm.getDclgfvtcna0().setGnaCve(1);
        pgm.getDclgfvtagt0().setNalCve("MEX");
        pgm.getWsIndicadores().setWsFinDrc(0);
        pgm.getWsIndicadores().setWsFinCna1(0);
        pgm.getWsIndicadores().setWsFinMnd(0);
        pgm.getWsIndicadores().setWsFinPob(0);
        pgm.getWsIndicadores().setWsFinDmz(0);
        pgm.getDclgcctedo0().setPaiCve("MEX");
        pgm.getWsIndicadores().setWsFinCma(0);
        try {
            pgm.p3450RegresoResto();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3450RegresoRestoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getDclgfvtcna0().setTaaCve("XX1");
            pgm.getDclgfvtcna0().setAgtIdr(50001);
            pgm.getDclgfvtfla0().setFlaDes("XX10");
            pgm.getDclgfvtcna0().setAgtIdrRcl(100000);
            pgm.getWsIndicadores().setWsFinFla1(1);
            pgm.getWsIndicadores().setWsFinFla3(1);
            pgm.getRegMaestro().setPkEsGerente("XXS");
            pgm.getWsIndicadores().setWsFinDma(1);
            pgm.getDclgfvtagt0().setPefCve(3);
            pgm.getDclgfvtpra0().setEciCve(2);
            pgm.getDclgfvtfla0().setAgtIdr(50001);
            pgm.getDclgfvtcna0().setGnaCve(2);
            pgm.getDclgfvtagt0().setNalCve("XXMEX");
            pgm.getWsIndicadores().setWsFinDrc(1);
            pgm.getWsIndicadores().setWsFinCna1(1);
            pgm.getWsIndicadores().setWsFinMnd(1);
            pgm.getWsIndicadores().setWsFinPob(1);
            pgm.getWsIndicadores().setWsFinDmz(1);
            pgm.getDclgcctedo0().setPaiCve("XXMEX");
            pgm.getWsIndicadores().setWsFinCma(1);
            try {
                pgm.p3450RegresoResto();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p3450RegresoRestoTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getDclgfvtcna0().setTaaCve("XX1");
                pgm.getDclgfvtcna0().setAgtIdr(50001);
                pgm.getDclgfvtfla0().setFlaDes("XX10");
                pgm.getDclgfvtcna0().setAgtIdrRcl(100000);
                pgm.getWsIndicadores().setWsFinFla1(1);
                pgm.getWsIndicadores().setWsFinFla3(1);
                pgm.getRegMaestro().setPkEsGerente("XXS");
                pgm.getWsIndicadores().setWsFinDma(1);
                pgm.getDclgfvtagt0().setPefCve(3);
                pgm.getDclgfvtpra0().setEciCve(3);
                pgm.getDclgfvtfla0().setAgtIdr(50001);
                pgm.getDclgfvtcna0().setGnaCve(2);
                pgm.getDclgfvtagt0().setNalCve("XXMEX");
                pgm.getWsIndicadores().setWsFinDrc(1);
                pgm.getWsIndicadores().setWsFinCna1(1);
                pgm.getWsIndicadores().setWsFinMnd(1);
                pgm.getWsIndicadores().setWsFinPob(1);
                pgm.getWsIndicadores().setWsFinDmz(1);
                pgm.getDclgcctedo0().setPaiCve("XXMEX");
                pgm.getWsIndicadores().setWsFinCma(1);
                try {
                    pgm.p3450RegresoResto();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p3450RegresoRestoTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getDclgfvtcna0().setTaaCve("XX1");
                    pgm.getDclgfvtcna0().setAgtIdr(50001);
                    pgm.getDclgfvtfla0().setFlaDes("XX10");
                    pgm.getDclgfvtcna0().setAgtIdrRcl(100000);
                    pgm.getWsIndicadores().setWsFinFla1(1);
                    pgm.getWsIndicadores().setWsFinFla3(1);
                    pgm.getRegMaestro().setPkEsGerente("XXS");
                    pgm.getWsIndicadores().setWsFinDma(1);
                    pgm.getDclgfvtagt0().setPefCve(3);
                    pgm.getDclgfvtpra0().setEciCve(4);
                    pgm.getDclgfvtfla0().setAgtIdr(50001);
                    pgm.getDclgfvtcna0().setGnaCve(2);
                    pgm.getDclgfvtagt0().setNalCve("XXMEX");
                    pgm.getWsIndicadores().setWsFinDrc(1);
                    pgm.getWsIndicadores().setWsFinCna1(1);
                    pgm.getWsIndicadores().setWsFinMnd(1);
                    pgm.getWsIndicadores().setWsFinPob(1);
                    pgm.getWsIndicadores().setWsFinDmz(1);
                    pgm.getDclgcctedo0().setPaiCve("XXMEX");
                    pgm.getWsIndicadores().setWsFinCma(1);
                    try {
                        pgm.p3450RegresoResto();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p3500ArmaDmaPobMndEdoP test conditions:
        wsFinMnd : [0, 1]
        wsFinPob : [0, 1]
        paiCve : ["MEX", "XXMEX"]
        wsFinCma : [0, 1]
    */
    @Test
    void p3500ArmaDmaPobMndEdoPTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinMnd(0);
        pgm.getWsIndicadores().setWsFinPob(0);
        pgm.getDclgcctedo0().setPaiCve("MEX");
        pgm.getWsIndicadores().setWsFinCma(0);
        try {
            pgm.p3500ArmaDmaPobMndEdoP();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3500ArmaDmaPobMndEdoPTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsFinMnd(1);
            pgm.getWsIndicadores().setWsFinPob(1);
            pgm.getDclgcctedo0().setPaiCve("XXMEX");
            pgm.getWsIndicadores().setWsFinCma(1);
            try {
                pgm.p3500ArmaDmaPobMndEdoP();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3500ArmaDmaPobMndEdoC test conditions:
        wsFinMnd : [0, 1]
        wsFinPob : [0, 1]
        paiCve : ["MEX", "XXMEX"]
        wsFinCma : [0, 1]
    */
    @Test
    void p3500ArmaDmaPobMndEdoCTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinMnd(0);
        pgm.getWsIndicadores().setWsFinPob(0);
        pgm.getDclgcctedo0().setPaiCve("MEX");
        pgm.getWsIndicadores().setWsFinCma(0);
        try {
            pgm.p3500ArmaDmaPobMndEdoC();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3500ArmaDmaPobMndEdoCTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsFinMnd(1);
            pgm.getWsIndicadores().setWsFinPob(1);
            pgm.getDclgcctedo0().setPaiCve("XXMEX");
            pgm.getWsIndicadores().setWsFinCma(1);
            try {
                pgm.p3500ArmaDmaPobMndEdoC();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3500ArmaDmaPobMndEdoE test conditions:
        wsFinMnd : [0, 1]
        wsFinPob : [0, 1]
        wsFinDmz : [0, 1]
        paiCve : ["MEX", "XXMEX"]
    */
    @Test
    void p3500ArmaDmaPobMndEdoETest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsIndicadores().setWsFinMnd(0);
        pgm.getWsIndicadores().setWsFinPob(0);
        pgm.getWsIndicadores().setWsFinDmz(0);
        pgm.getDclgcctedo0().setPaiCve("MEX");
        try {
            pgm.p3500ArmaDmaPobMndEdoE();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3500ArmaDmaPobMndEdoETest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsIndicadores().setWsFinMnd(1);
            pgm.getWsIndicadores().setWsFinPob(1);
            pgm.getWsIndicadores().setWsFinDmz(1);
            pgm.getDclgcctedo0().setPaiCve("XXMEX");
            try {
                pgm.p3500ArmaDmaPobMndEdoE();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7100LeerGfvtpra0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7100LeerGfvtpra0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7100LeerGfvtpra0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7100LeerGfvtpra0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7100LeerGfvtpra0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7200InicioGfvtdma0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7200InicioGfvtdma0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7200InicioGfvtdma0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7200InicioGfvtdma0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7200InicioGfvtdma0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7200LeerGfvtdma0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7200LeerGfvtdma0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7200LeerGfvtdma0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7200LeerGfvtdma0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7200LeerGfvtdma0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7200CerrarGfvtdma0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7200CerrarGfvtdma0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7200CerrarGfvtdma0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7200CerrarGfvtdma0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7200CerrarGfvtdma0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7250LeerGfvtdmz0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7250LeerGfvtdmz0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7250LeerGfvtdmz0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7250LeerGfvtdmz0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7250LeerGfvtdmz0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7300LeerGcctpob0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7300LeerGcctpob0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7300LeerGcctpob0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7300LeerGcctpob0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7300LeerGcctpob0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7400LeerGcctmnd0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7400LeerGcctmnd0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7400LeerGcctmnd0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7400LeerGcctmnd0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7400LeerGcctmnd0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7500LeerGcctedo0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7500LeerGcctedo0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7500LeerGcctedo0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7500LeerGcctedo0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7500LeerGcctedo0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7600LeerGfvtcma0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7600LeerGfvtcma0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7600LeerGfvtcma0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7600LeerGfvtcma0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7600LeerGfvtcma0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7700InicioGfvtcna0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
        wsCuantosCna : [0, 1]
    */
    @Test
    void p7700InicioGfvtcna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        pgm.getWsCampos().setWsCuantosCna(0);
        try {
            pgm.p7700InicioGfvtcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7700InicioGfvtcna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            pgm.getWsCampos().setWsCuantosCna(1);
            try {
                pgm.p7700InicioGfvtcna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7700LeerGfvtcna0 test conditions:
        wsCuantosCna : [0, 1]
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7700LeerGfvtcna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsCampos().setWsCuantosCna(0);
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7700LeerGfvtcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7700LeerGfvtcna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsCampos().setWsCuantosCna(1);
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7700LeerGfvtcna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7700CerrarGfvtcna0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7700CerrarGfvtcna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7700CerrarGfvtcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7700CerrarGfvtcna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7700CerrarGfvtcna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7800InicioGfvtfla0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
        wsCuantosFla : [0, 1]
    */
    @Test
    void p7800InicioGfvtfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        pgm.getWsCampos().setWsCuantosFla(0);
        try {
            pgm.p7800InicioGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7800InicioGfvtfla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            pgm.getWsCampos().setWsCuantosFla(1);
            try {
                pgm.p7800InicioGfvtfla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7800LeerGfvtfla0 test conditions:
        wsCuantosFla : [0, 1]
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7800LeerGfvtfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsCampos().setWsCuantosFla(0);
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7800LeerGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7800LeerGfvtfla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsCampos().setWsCuantosFla(1);
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7800LeerGfvtfla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7800CerrarGfvtfla0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7800CerrarGfvtfla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7800CerrarGfvtfla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7800CerrarGfvtfla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7800CerrarGfvtfla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850InicioGfvcfla1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850InicioGfvcfla1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850InicioGfvcfla1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850InicioGfvcfla1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850InicioGfvcfla1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850InicioGfvcfla3 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850InicioGfvcfla3Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850InicioGfvcfla3();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850InicioGfvcfla3Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850InicioGfvcfla3();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850LeerGfvtfla1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850LeerGfvtfla1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850LeerGfvtfla1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850LeerGfvtfla1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850LeerGfvtfla1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850LeerGfvtfla3 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850LeerGfvtfla3Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850LeerGfvtfla3();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850LeerGfvtfla3Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850LeerGfvtfla3();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850CierroGfvcfla1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850CierroGfvcfla1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850CierroGfvcfla1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850CierroGfvcfla1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850CierroGfvcfla1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7850CierroGfvcfla3 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7850CierroGfvcfla3Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7850CierroGfvcfla3();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7850CierroGfvcfla3Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7850CierroGfvcfla3();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7900LeerGfvtmte0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7900LeerGfvtmte0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7900LeerGfvtmte0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7900LeerGfvtmte0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7900LeerGfvtmte0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7950LeerGcctorv0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7950LeerGcctorv0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7950LeerGcctorv0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7950LeerGcctorv0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7950LeerGcctorv0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7950LeerGfvtdrc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7950LeerGfvtdrc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7950LeerGfvtdrc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7950LeerGfvtdrc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7950LeerGfvtdrc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7950InicioGfvcdrc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7950InicioGfvcdrc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7950InicioGfvcdrc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7950InicioGfvcdrc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7950InicioGfvcdrc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7950LeerGfvcdrc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7950LeerGfvcdrc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7950LeerGfvcdrc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7950LeerGfvcdrc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7950LeerGfvcdrc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7950CerrarGfvcdrc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7950CerrarGfvcdrc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7950CerrarGfvcdrc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7950CerrarGfvcdrc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7950CerrarGfvcdrc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7960LeerGfvtcna0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7960LeerGfvtcna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7960LeerGfvtcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7960LeerGfvtcna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7960LeerGfvtcna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7960InicioGfvtcna1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7960InicioGfvtcna1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7960InicioGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7960InicioGfvtcna1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7960InicioGfvtcna1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7960LeerGfvtcna1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7960LeerGfvtcna1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7960LeerGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7960LeerGfvtcna1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7960LeerGfvtcna1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7960CerrarGfvtcna1 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7960CerrarGfvtcna1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7960CerrarGfvtcna1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7960CerrarGfvtcna1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7960CerrarGfvtcna1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7970InicioGfvtfla2 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7970InicioGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7970InicioGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7970InicioGfvtfla2Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7970InicioGfvtfla2();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7970LeerGfvtfla2 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7970LeerGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7970LeerGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7970LeerGfvtfla2Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7970LeerGfvtfla2();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7970CerrarGfvtfla2 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p7970CerrarGfvtfla2Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p7970CerrarGfvtfla2();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7970CerrarGfvtfla2Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p7970CerrarGfvtfla2();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p8000LeerGfvtcma0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p8000LeerGfvtcma0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p8000LeerGfvtcma0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p8000LeerGfvtcma0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p8000LeerGfvtcma0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p8100LeerGfvtdrc0 test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p8100LeerGfvtdrc0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p8100LeerGfvtdrc0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p8100LeerGfvtdrc0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p8100LeerGfvtdrc0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      db2check test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void db2checkTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.db2check();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void db2checkTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.db2check();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p9999AnalizaSql test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
        sqlwarn5 : ["W", "XXW"]
    */
    @Test
    void p9999AnalizaSqlTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        pgm.getSqlca().setSqlwarn5("W");
        try {
            pgm.p9999AnalizaSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p9999AnalizaSqlTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            pgm.getSqlca().setSqlwarn5("XXW");
            try {
                pgm.p9999AnalizaSql();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
}
