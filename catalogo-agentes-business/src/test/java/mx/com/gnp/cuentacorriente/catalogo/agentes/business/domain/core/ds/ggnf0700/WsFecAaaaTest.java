package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FEC-AAAA.
 *
 */
class WsFecAaaaTest {

    private WsFecAaaa wsFecAaaa;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFecAaaa = new WsFecAaaa();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {


    }

    // property wsAaaa
    @Test
    void testGetterSetterWsAaaa() {

        assertNotNull(wsFecAaaa.getWsAaaa());
    }

    // property wsAaaar
    @Test
    void testGetterSetterWsAaaar() {

        assertNotNull(wsFecAaaa.getWsAaaar());

        int vint = 1234;
        wsFecAaaa.setWsAaaar(vint); // int
        assertEquals(asInt(vint), asInt(wsFecAaaa.getWsAaaar()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsFecAaaa.setWsAaaar(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFecAaaa.getWsAaaar()));
    }

    // property wsAa1
    @Test
    void testGetterSetterWsAa1() {

        // Property: wsAaaa.wsAa1 -> 05 WS-AA1 99
        assertNotNull(wsFecAaaa.getWsAa1());
        int vint = 12;
        wsFecAaaa.setWsAa1(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFecAaaa.setWsAa1(vINumericValue); // INumericValue
    }

    // property wsAa2
    @Test
    void testGetterSetterWsAa2() {

        // Property: wsAaaa.wsAa2 -> 05 WS-AA2 99
        assertNotNull(wsFecAaaa.getWsAa2());
        int vint = 12;
        wsFecAaaa.setWsAa2(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFecAaaa.setWsAa2(vINumericValue); // INumericValue
    }
}

