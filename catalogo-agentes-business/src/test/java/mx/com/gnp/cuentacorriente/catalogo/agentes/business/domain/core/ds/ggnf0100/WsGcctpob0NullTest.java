package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GCCTPOB0-NULL.
 *
 */
class WsGcctpob0NullTest {

    private WsGcctpob0Null wsGcctpob0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGcctpob0Null = new WsGcctpob0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGcctpob0Null.setPob01(0);
        assertEquals(0, wsGcctpob0Null.getPob01());

        wsGcctpob0Null.setPob02(0);
        assertEquals(0, wsGcctpob0Null.getPob02());

        wsGcctpob0Null.setPob03(0);
        assertEquals(0, wsGcctpob0Null.getPob03());

        wsGcctpob0Null.setPob04(0);
        assertEquals(0, wsGcctpob0Null.getPob04());

    }

    // property pob01
    @Test
    void testGetterSetterPob01() {

        assertNotNull(wsGcctpob0Null.getPob01());

        int vint = 1234;
        wsGcctpob0Null.setPob01(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctpob0Null.getPob01()));
    }

    // property pob02
    @Test
    void testGetterSetterPob02() {

        assertNotNull(wsGcctpob0Null.getPob02());

        int vint = 1234;
        wsGcctpob0Null.setPob02(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctpob0Null.getPob02()));
    }

    // property pob03
    @Test
    void testGetterSetterPob03() {

        assertNotNull(wsGcctpob0Null.getPob03());

        int vint = 1234;
        wsGcctpob0Null.setPob03(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctpob0Null.getPob03()));
    }

    // property pob04
    @Test
    void testGetterSetterPob04() {

        assertNotNull(wsGcctpob0Null.getPob04());

        int vint = 1234;
        wsGcctpob0Null.setPob04(vint); // int
        assertEquals(asInt(vint), asInt(wsGcctpob0Null.getPob04()));
    }
}

