package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TIT11.
 *
 */
class Tit11Test {

    private Tit11 tit11;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tit11 = new Tit11();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Tit11 tit112 = new Tit11(parent);
        assertNotNull(tit112);
        AlphanumericVar sibling = new AlphanumericVar(parent, 125);
        Tit11 tit113 = new Tit11(parent, sibling);
        assertNotNull(tit113);
    }

    @Test
    void testInitialize() {
        assertNotNull(tit11.getTit11Pgm());

        assertNotNull(tit11.getTit11Hoja());

    }

    // property tit11Pgm
    @Test
    void testGetterSetterTit11Pgm() {

        assertNotNull(tit11.getTit11Pgm());

        String vString = "ABCabc12";
        tit11.setTit11Pgm(vString); // String
        assertEquals(asStr(vString), asStr(tit11.getTit11Pgm()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tit11.setTit11Pgm(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tit11.getTit11Pgm()));
    }

    // property tit11Hoja
    @Test
    void testGetterSetterTit11Hoja() {

        assertNotNull(tit11.getTit11Hoja());

        String vString = "   0";
        tit11.setTit11Hoja(vString); // String
        assertEquals(asStr(vString), asStr(tit11.getTit11Hoja()));

        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        tit11.setTit11Hoja(vIValue); // IValue
        // format("1234", "ZZZ9")
        assertNotNull(asStr(tit11.getTit11Hoja()));
    }
}

