package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-A-PAT.
 *
 */
class WsAuxAPatTest {

    private WsAuxAPat wsAuxAPat;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxAPat = new WsAuxAPat();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAuxAPat wsAuxAPat2 = new WsAuxAPat(parent);
        assertNotNull(wsAuxAPat2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 40);
        WsAuxAPat wsAuxAPat3 = new WsAuxAPat(parent, sibling);
        assertNotNull(wsAuxAPat3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAuxAPat.getWsAuxApPat());

    }

    // property wsAuxApPat
    @Test
    void testGetterSetterWsAuxApPat() {

        assertNotNull(wsAuxAPat.getWsAuxApPat());
        assertNotNull(wsAuxAPat.getWsAuxApPat(1));

        String vString = "A";
        wsAuxAPat.getWsAuxApPat().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxAPat.getWsAuxApPat(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxAPat.getWsAuxApPat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsAuxAPat.getWsAuxApPat(1)));
    }
}

