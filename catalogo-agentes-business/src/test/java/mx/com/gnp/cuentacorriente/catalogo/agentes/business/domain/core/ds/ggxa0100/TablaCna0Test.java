package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-CNA0.
 *
 */
class TablaCna0Test {

    private TablaCna0 tablaCna0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaCna0 = new TablaCna0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tCna0
    @Test
    void testGetterSetterTCna0() {

        assertNotNull(tablaCna0.getTCna0());
        assertNotNull(tablaCna0.getTCna0(1));
    }

    // property tCna0Elem
    @Test
    void testGetterSetterTCna0Elem() {

        // Property: tCna0.tCna0Elem -> 15 T-CNA0-ELEM UsedAsParameter
        assertNotNull(tablaCna0.getTCna0Elem());
    }

    // property sCnaCnaNum
    @Test
    void testGetterSetterSCnaCnaNum() {

        // Property: tCna0.tCna0Elem.sCnaCnaNum -> 20 S-CNA-CNA-NUM X(02)
        assertNotNull(tablaCna0.getSCnaCnaNum());
        String vString = "AB";
        tablaCna0.setSCnaCnaNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        tablaCna0.setSCnaCnaNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sCnaLimitad
    @Test
    void testGetterSetterSCnaLimitad() {

        // Property: tCna0.tCna0Elem.sCnaLimitad -> 20 S-CNA-LIMITAD X(01)
        assertNotNull(tablaCna0.getSCnaLimitad());
        String vString = "A";
        tablaCna0.setSCnaLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tablaCna0.setSCnaLimitad(vIAlphanumericValue); // IAlphanumericValue
    }
}

