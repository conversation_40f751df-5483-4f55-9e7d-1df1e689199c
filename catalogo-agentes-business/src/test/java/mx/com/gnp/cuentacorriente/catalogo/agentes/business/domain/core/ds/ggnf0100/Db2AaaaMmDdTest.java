package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: DB2-AAAA-MM-DD.
 *
 */
class Db2AaaaMmDdTest {

    private Db2AaaaMmDd db2AaaaMmDd;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        db2AaaaMmDd = new Db2AaaaMmDd();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Db2AaaaMmDd db2AaaaMmDd2 = new Db2AaaaMmDd(parent);
        assertNotNull(db2AaaaMmDd2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        Db2AaaaMmDd db2AaaaMmDd3 = new Db2AaaaMmDd(parent, sibling);
        assertNotNull(db2AaaaMmDd3);
    }

    @Test
    void testInitialize() {
        assertNotNull(db2AaaaMmDd.getDb2Aaaa());


        assertNotNull(db2AaaaMmDd.getDb2Mm());

        assertNotNull(db2AaaaMmDd.getDb2Dd());

    }

    // property db2Aaaa
    @Test
    void testGetterSetterDb2Aaaa() {

        assertNotNull(db2AaaaMmDd.getDb2Aaaa());

        int vint = 1234;
        db2AaaaMmDd.setDb2Aaaa(vint); // int
        assertEquals(asInt(vint), asInt(db2AaaaMmDd.getDb2Aaaa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        db2AaaaMmDd.setDb2Aaaa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(db2AaaaMmDd.getDb2Aaaa()));
    }

    // property db2Aaaar
    @Test
    void testGetterSetterDb2Aaaar() {

        assertNotNull(db2AaaaMmDd.getDb2Aaaar());
    }

    // property db2Mm
    @Test
    void testGetterSetterDb2Mm() {

        assertNotNull(db2AaaaMmDd.getDb2Mm());

        int vint = 12;
        db2AaaaMmDd.setDb2Mm(vint); // int
        assertEquals(asInt(vint), asInt(db2AaaaMmDd.getDb2Mm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        db2AaaaMmDd.setDb2Mm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(db2AaaaMmDd.getDb2Mm()));
    }

    // property db2Dd
    @Test
    void testGetterSetterDb2Dd() {

        assertNotNull(db2AaaaMmDd.getDb2Dd());

        int vint = 12;
        db2AaaaMmDd.setDb2Dd(vint); // int
        assertEquals(asInt(vint), asInt(db2AaaaMmDd.getDb2Dd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        db2AaaaMmDd.setDb2Dd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(db2AaaaMmDd.getDb2Dd()));
    }

    // property db2Aa1
    @Test
    void testGetterSetterDb2Aa1() {

        // Property: db2Aaaar.db2Aa1 -> 05 DB2-AA1 99
        assertNotNull(db2AaaaMmDd.getDb2Aa1());
        int vint = 12;
        db2AaaaMmDd.setDb2Aa1(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        db2AaaaMmDd.setDb2Aa1(vINumericValue); // INumericValue
    }

    // property db2Aa2
    @Test
    void testGetterSetterDb2Aa2() {

        // Property: db2Aaaar.db2Aa2 -> 05 DB2-AA2 99
        assertNotNull(db2AaaaMmDd.getDb2Aa2());
        int vint = 12;
        db2AaaaMmDd.setDb2Aa2(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        db2AaaaMmDd.setDb2Aa2(vINumericValue); // INumericValue
    }
}

