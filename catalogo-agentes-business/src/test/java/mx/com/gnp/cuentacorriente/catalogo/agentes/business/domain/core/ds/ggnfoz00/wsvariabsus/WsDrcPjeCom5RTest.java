package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DRC-PJE-COM5-R.
 *
 */
class WsDrcPjeCom5RTest {

    private WsDrcPjeCom5R wsDrcPjeCom5R;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDrcPjeCom5R = new WsDrcPjeCom5R();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsDrcPjeCom5R wsDrcPjeCom5R2 = new WsDrcPjeCom5R(parent);
        assertNotNull(wsDrcPjeCom5R2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsDrcPjeCom5R wsDrcPjeCom5R3 = new WsDrcPjeCom5R(parent, sibling);
        assertNotNull(wsDrcPjeCom5R3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsDrcPjeCom5R.getRsDrcPjeCom53());

        assertNotNull(wsDrcPjeCom5R.getRsDrcPjeCom57());

    }

    // property rsDrcPjeCom53
    @Test
    void testGetterSetterRsDrcPjeCom53() {

        assertNotNull(wsDrcPjeCom5R.getRsDrcPjeCom53());

        String vString = "ABC";
        wsDrcPjeCom5R.setRsDrcPjeCom53(vString); // String
        assertEquals(asStr(vString), asStr(wsDrcPjeCom5R.getRsDrcPjeCom53()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsDrcPjeCom5R.setRsDrcPjeCom53(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDrcPjeCom5R.getRsDrcPjeCom53()));
    }

    // property rsDrcPjeCom57
    @Test
    void testGetterSetterRsDrcPjeCom57() {

        assertNotNull(wsDrcPjeCom5R.getRsDrcPjeCom57());

        String vString = "ABCabc1";
        wsDrcPjeCom5R.setRsDrcPjeCom57(vString); // String
        assertEquals(asStr(vString), asStr(wsDrcPjeCom5R.getRsDrcPjeCom57()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wsDrcPjeCom5R.setRsDrcPjeCom57(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDrcPjeCom5R.getRsDrcPjeCom57()));
    }
}

