package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;
@Disabled
class Ggqac600Test {
    Ggqac600 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggqac600.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p100Inicio test conditions:
        s999Err : ["1", "XX1"]
        lagtSwErr : ["XX0", "0"]
    */
    @Test
    void p100InicioTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsCamposTrabajo().setS999Err("1");
        pgm.getWsLinkReg().setLagtSwErr("XX0");
        try {
            pgm.p100Inicio();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p100InicioTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsCamposTrabajo().setS999Err("XX1");
            pgm.getWsLinkReg().setLagtSwErr("0");
            try {
                pgm.p100Inicio();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p120LeeParametroP01 test conditions:
        s999Err : ["1", "XX1"]
    */
    @Test
    void p120LeeParametroP01Test1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsCamposTrabajo().setS999Err("1");
        try {
            pgm.p120LeeParametroP01();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p120LeeParametroP01Test2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsCamposTrabajo().setS999Err("XX1");
            try {
                pgm.p120LeeParametroP01();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p130AbreCursorAgt0 test conditions:
        lagtSwErr : ["XX0", "0"]
    */
    @Test
    void p130AbreCursorAgt0Test1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLagtSwErr("XX0");
        try {
            pgm.p130AbreCursorAgt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p130AbreCursorAgt0Test2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLagtSwErr("0");
            try {
                pgm.p130AbreCursorAgt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p200Proceso test conditions:
        lagtSwErr : ["0", "XX0", "1", "XX1"]
    */
    @Test
    void p200ProcesoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLagtSwErr("0");
        try {
            pgm.p200Proceso();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p200ProcesoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLagtSwErr("XX0");
            try {
                pgm.p200Proceso();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p200ProcesoTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getWsLinkReg().setLagtSwErr("1");
                try {
                    pgm.p200Proceso();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p200ProcesoTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getWsLinkReg().setLagtSwErr("XX1");
                    try {
                        pgm.p200Proceso();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p300FetchAgt0 test conditions:
        lagtSwErr : ["0", "XX0", "1", "XX1"]
        lgralSwErr : ["0", "XX0", "1", "XX1"]
    */
    @Test
    void p300FetchAgt0Test1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLagtSwErr("0");
        pgm.getLgralLinkReg().setLgralSwErr("0");
        try {
            pgm.p300FetchAgt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p300FetchAgt0Test2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLagtSwErr("XX0");
            pgm.getLgralLinkReg().setLgralSwErr("XX0");
            try {
                pgm.p300FetchAgt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p300FetchAgt0Test3() {
                //TODO setup test
                // Set test conditions
                pgm.getWsLinkReg().setLagtSwErr("1");
                pgm.getLgralLinkReg().setLgralSwErr("1");
                try {
                    pgm.p300FetchAgt0();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p300FetchAgt0Test4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getWsLinkReg().setLagtSwErr("XX1");
                    pgm.getLgralLinkReg().setLgralSwErr("XX1");
                    try {
                        pgm.p300FetchAgt0();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p400LlamadoRutinaGral test conditions:
        lgralSwErr : ["0", "XX0", "1", "XX1"]
        lcnaSwErr : ["0", "XX0", "1", "XX1"]
    */
    @Test
    void p400LlamadoRutinaGralTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getLgralLinkReg().setLgralSwErr("0");
        pgm.getLcnaLinkReg().setLcnaSwErr("0");
        try {
            pgm.p400LlamadoRutinaGral();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p400LlamadoRutinaGralTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getLgralLinkReg().setLgralSwErr("XX0");
            pgm.getLcnaLinkReg().setLcnaSwErr("XX0");
            try {
                pgm.p400LlamadoRutinaGral();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p400LlamadoRutinaGralTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getLgralLinkReg().setLgralSwErr("1");
                pgm.getLcnaLinkReg().setLcnaSwErr("1");
                try {
                    pgm.p400LlamadoRutinaGral();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p400LlamadoRutinaGralTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getLgralLinkReg().setLgralSwErr("XX1");
                    pgm.getLcnaLinkReg().setLcnaSwErr("XX1");
                    try {
                        pgm.p400LlamadoRutinaGral();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p500LlamadoRutinaCna test conditions:
        lcnaSwErr : ["0", "XX0", "1", "XX1"]
        bdErr : ["0", "XX0"]
        lhfaSwErr : ["0", "XX0", "1", "XX1"]
    */
    @Test
    void p500LlamadoRutinaCnaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getLcnaLinkReg().setLcnaSwErr("0");
        pgm.getGgqa0053cpy().setBdErr("0");
        pgm.getLhfaLinkReg().setLhfaSwErr("0");
        try {
            pgm.p500LlamadoRutinaCna();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p500LlamadoRutinaCnaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getLcnaLinkReg().setLcnaSwErr("XX0");
            pgm.getGgqa0053cpy().setBdErr("XX0");
            pgm.getLhfaLinkReg().setLhfaSwErr("XX0");
            try {
                pgm.p500LlamadoRutinaCna();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p500LlamadoRutinaCnaTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getLcnaLinkReg().setLcnaSwErr("1");
                pgm.getGgqa0053cpy().setBdErr("XX0");
                pgm.getLhfaLinkReg().setLhfaSwErr("1");
                try {
                    pgm.p500LlamadoRutinaCna();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p500LlamadoRutinaCnaTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getLcnaLinkReg().setLcnaSwErr("XX1");
                    pgm.getGgqa0053cpy().setBdErr("XX0");
                    pgm.getLhfaLinkReg().setLhfaSwErr("XX1");
                    try {
                        pgm.p500LlamadoRutinaCna();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p520ObtieneIndiExc test conditions:
        wsHfaIndExc : ["", "XX"]
    */
    @Test
    void p520ObtieneIndiExcTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsCamposTrabajo().setWsHfaIndExc("");
        try {
            pgm.p520ObtieneIndiExc();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p520ObtieneIndiExcTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsCamposTrabajo().setWsHfaIndExc("XX");
            try {
                pgm.p520ObtieneIndiExc();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p530ValidarEstatusAgt test conditions:
        bdErr : ["0", "XX0"]
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p530ValidarEstatusAgtTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getGgqa0053cpy().setBdErr("0");
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p530ValidarEstatusAgt();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p530ValidarEstatusAgtTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getGgqa0053cpy().setBdErr("XX0");
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p530ValidarEstatusAgt();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p530ValidarEstatusAgtTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getGgqa0053cpy().setBdErr("XX0");
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p530ValidarEstatusAgt();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p530ValidarEstatusAgtTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getGgqa0053cpy().setBdErr("XX0");
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p530ValidarEstatusAgt();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p540ObtenerIndPago test conditions:
        bdErr : ["0", "XX0"]
    */
    @Test
    void p540ObtenerIndPagoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getGgqa0053cpy().setBdErr("0");
        try {
            pgm.p540ObtenerIndPago();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p540ObtenerIndPagoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getGgqa0053cpy().setBdErr("XX0");
            try {
                pgm.p540ObtenerIndPago();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p600LlamadoRutinaHfa test conditions:
        lhfaSwErr : ["0", "XX0", "1", "XX1"]
        wsHfaIndExc : ["", "XX"]
        wsGralPefCve : [1, 2]
    */
    @Test
    void p600LlamadoRutinaHfaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getLhfaLinkReg().setLhfaSwErr("0");
        pgm.getWsCamposTrabajo().setWsHfaIndExc("");
        pgm.getWsCamposTrabajo().setWsGralPefCve(1);
        try {
            pgm.p600LlamadoRutinaHfa();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p600LlamadoRutinaHfaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getLhfaLinkReg().setLhfaSwErr("XX0");
            pgm.getWsCamposTrabajo().setWsHfaIndExc("XX");
            pgm.getWsCamposTrabajo().setWsGralPefCve(2);
            try {
                pgm.p600LlamadoRutinaHfa();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p600LlamadoRutinaHfaTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getLhfaLinkReg().setLhfaSwErr("1");
                pgm.getWsCamposTrabajo().setWsHfaIndExc("XX");
                pgm.getWsCamposTrabajo().setWsGralPefCve(2);
                try {
                    pgm.p600LlamadoRutinaHfa();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p600LlamadoRutinaHfaTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getLhfaLinkReg().setLhfaSwErr("XX1");
                    pgm.getWsCamposTrabajo().setWsHfaIndExc("XX");
                    pgm.getWsCamposTrabajo().setWsGralPefCve(2);
                    try {
                        pgm.p600LlamadoRutinaHfa();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p620ComparaAgtHfa test conditions:
        wsGralPefCve : [1, 2]
    */
    @Test
    void p620ComparaAgtHfaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsCamposTrabajo().setWsGralPefCve(1);
        try {
            pgm.p620ComparaAgtHfa();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p620ComparaAgtHfaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsCamposTrabajo().setWsGralPefCve(2);
            try {
                pgm.p620ComparaAgtHfa();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p630MueveValorInsert test conditions:
        wsGralPefCve : [1, 2]
    */
    @Test
    void p630MueveValorInsertTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsCamposTrabajo().setWsGralPefCve(1);
        try {
            pgm.p630MueveValorInsert();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p630MueveValorInsertTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsCamposTrabajo().setWsGralPefCve(2);
            try {
                pgm.p630MueveValorInsert();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p650InsertHfa test conditions:
        lhfaSwErr : ["1", "XX1"]
    */
    @Test
    void p650InsertHfaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getLhfaLinkReg().setLhfaSwErr("1");
        try {
            pgm.p650InsertHfa();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p650InsertHfaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getLhfaLinkReg().setLhfaSwErr("XX1");
            try {
                pgm.p650InsertHfa();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p670UpdateHfa test conditions:
        lhfaSwErr : ["1", "XX1"]
    */
    @Test
    void p670UpdateHfaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getLhfaLinkReg().setLhfaSwErr("1");
        try {
            pgm.p670UpdateHfa();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p670UpdateHfaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getLhfaLinkReg().setLhfaSwErr("XX1");
            try {
                pgm.p670UpdateHfa();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p999CloseCursorAgt0 test conditions:
        lagtSwErr : ["XX0", "0"]
    */
    @Test
    void p999CloseCursorAgt0Test1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLagtSwErr("XX0");
        try {
            pgm.p999CloseCursorAgt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p999CloseCursorAgt0Test2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLagtSwErr("0");
            try {
                pgm.p999CloseCursorAgt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p999Termina test conditions:
        stExcAgt : ["XX00", "00"]
    */
    @Test
    void p999TerminaTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getBanderas().setStExcAgt("XX00");
        try {
            pgm.p999Termina();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p999TerminaTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getBanderas().setStExcAgt("00");
            try {
                pgm.p999Termina();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p991CloseArchivos test conditions:
        stExcAgt : ["XX00", "00"]
    */
    @Test
    void p991CloseArchivosTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getBanderas().setStExcAgt("XX00");
        try {
            pgm.p991CloseArchivos();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p991CloseArchivosTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getBanderas().setStExcAgt("00");
            try {
                pgm.p991CloseArchivos();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p999VerErrSql test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p999VerErrSqlTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p999VerErrSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p999VerErrSqlTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p999VerErrSql();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p999VerErrSqlTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p999VerErrSql();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p999VerErrSqlTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p999VerErrSql();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
}
