package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-VARIABSUS.
 *
 */
class WsVariabsusTest {

    private WsVariabsus wsVariabsus;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsVariabsus = new WsVariabsus();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsVariabsus.getWsSqlcode());

        wsVariabsus.setFsAgtIdr("");
        assertEquals("", wsVariabsus.getFsAgtIdr());

        wsVariabsus.setFsTfpCvev("");
        assertEquals("", wsVariabsus.getFsTfpCvev());

        wsVariabsus.setFsTfpCven("");
        assertEquals("", wsVariabsus.getFsTfpCven());

        wsVariabsus.setFsAgtFecCamRazSo("");
        assertEquals("", wsVariabsus.getFsAgtFecCamRazSo());

        wsVariabsus.setFsFlaNum("");
        assertEquals("", wsVariabsus.getFsFlaNum());

        wsVariabsus.setWsDraCve(0);
        assertEquals(0, wsVariabsus.getWsDraCve());

        wsVariabsus.setWsAgtIdr(0);
        assertEquals(0, wsVariabsus.getWsAgtIdr());

        wsVariabsus.setWsAgtFecCamRazSo("");
        assertEquals("", wsVariabsus.getWsAgtFecCamRazSo());

        wsVariabsus.setWsAgtClfLmr(0);
        assertEquals(0, wsVariabsus.getWsAgtClfLmr());

        wsVariabsus.setWsPefCve(0);
        assertEquals(0, wsVariabsus.getWsPefCve());

        wsVariabsus.setWsMteCve(0);
        assertEquals(0, wsVariabsus.getWsMteCve());

        wsVariabsus.setWsAgtNumFolInv(0);
        assertEquals(0, wsVariabsus.getWsAgtNumFolInv());

        wsVariabsus.setWsCnaNum(0);
        assertEquals(0, wsVariabsus.getWsCnaNum());

        assertNotNull(wsVariabsus.getWsDrcPjeCom());

        assertNotNull(wsVariabsus.getWsDrcPjeCom5());


        assertNotNull(wsVariabsus.getWsCnaPjeIva());

        assertNotNull(wsVariabsus.getWsPjeIva());


        assertNotNull(wsVariabsus.getWsCnaPjeIsr());

        assertNotNull(wsVariabsus.getWsPjeIsr());


        assertNotNull(wsVariabsus.getWsCnaImpComNiv());

        assertNotNull(wsVariabsus.getWsComNiv());


        assertNotNull(wsVariabsus.getWsCnaImpFza());

        assertNotNull(wsVariabsus.getWsImpFza());


        assertNotNull(wsVariabsus.getWsCnaPjeFav());

        assertNotNull(wsVariabsus.getWsPjeFav());


        wsVariabsus.setWsPpgCve(0);
        assertEquals(0, wsVariabsus.getWsPpgCve());

        wsVariabsus.setWsCpgCve(0);
        assertEquals(0, wsVariabsus.getWsCpgCve());

        wsVariabsus.setWsCdeCve(0);
        assertEquals(0, wsVariabsus.getWsCdeCve());

        wsVariabsus.setWsOfnCve(0);
        assertEquals(0, wsVariabsus.getWsOfnCve());

        wsVariabsus.setWsGnaCve(0);
        assertEquals(0, wsVariabsus.getWsGnaCve());

        wsVariabsus.setWsTfpCve(0);
        assertEquals(0, wsVariabsus.getWsTfpCve());

        wsVariabsus.setWsAgtIdrRcl(0);
        assertEquals(0, wsVariabsus.getWsAgtIdrRcl());

        wsVariabsus.setWsMdeCve(0);
        assertEquals(0, wsVariabsus.getWsMdeCve());

        wsVariabsus.setWsZecCve(0);
        assertEquals(0, wsVariabsus.getWsZecCve());

        wsVariabsus.setWsCnaGerZonEnv(0);
        assertEquals(0, wsVariabsus.getWsCnaGerZonEnv());

        wsVariabsus.setWsCnaGerZonDcm(0);
        assertEquals(0, wsVariabsus.getWsCnaGerZonDcm());

        wsVariabsus.setWsDmaIdr(0);
        assertEquals(0, wsVariabsus.getWsDmaIdr());

        wsVariabsus.setWsTdmCve(0);
        assertEquals(0, wsVariabsus.getWsTdmCve());

        wsVariabsus.setWsPobCve(0);
        assertEquals(0, wsVariabsus.getWsPobCve());

        wsVariabsus.setWsCpoCve(0);
        assertEquals(0, wsVariabsus.getWsCpoCve());

        wsVariabsus.setWsMndCve(0);
        assertEquals(0, wsVariabsus.getWsMndCve());

        wsVariabsus.setWsCcmCve(0);
        assertEquals(0, wsVariabsus.getWsCcmCve());

        wsVariabsus.setWsEssRek("");
        assertEquals("", wsVariabsus.getWsEssRek());

    }

    // property wsSqlcode
    @Test
    void testGetterSetterWsSqlcode() {

        assertNotNull(wsVariabsus.getWsSqlcode());

        String vString = "ABCab";
        wsVariabsus.setWsSqlcode(vString); // String
        assertEquals(asStr("    0"), asStr(wsVariabsus.getWsSqlcode()));

        NumericVar vIValue = new NumericVar(5, 0);;
        vIValue.setValue("12345");
        wsVariabsus.setWsSqlcode(vIValue); // IValue
        // format("12345", "-ZZZ9")
        assertNotNull(asStr(wsVariabsus.getWsSqlcode()));
    }

    // property fsAgtIdr
    @Test
    void testGetterSetterFsAgtIdr() {

        assertNotNull(wsVariabsus.getFsAgtIdr());

        String vString = "ABCab";
        wsVariabsus.setFsAgtIdr(vString); // String
        assertEquals(asStr(vString), asStr(wsVariabsus.getFsAgtIdr()));
    }

    // property fsTfpCvev
    @Test
    void testGetterSetterFsTfpCvev() {

        assertNotNull(wsVariabsus.getFsTfpCvev());

        String vString = "ABC";
        wsVariabsus.setFsTfpCvev(vString); // String
        assertEquals(asStr(vString), asStr(wsVariabsus.getFsTfpCvev()));
    }

    // property fsTfpCven
    @Test
    void testGetterSetterFsTfpCven() {

        assertNotNull(wsVariabsus.getFsTfpCven());

        String vString = "ABC";
        wsVariabsus.setFsTfpCven(vString); // String
        assertEquals(asStr(vString), asStr(wsVariabsus.getFsTfpCven()));
    }

    // property fsAgtFecCamRazSo
    @Test
    void testGetterSetterFsAgtFecCamRazSo() {

        assertNotNull(wsVariabsus.getFsAgtFecCamRazSo());

        String vString = "ABCabc1230";
        wsVariabsus.setFsAgtFecCamRazSo(vString); // String
        assertEquals(asStr(vString), asStr(wsVariabsus.getFsAgtFecCamRazSo()));
    }

    // property fsFlaNum
    @Test
    void testGetterSetterFsFlaNum() {

        assertNotNull(wsVariabsus.getFsFlaNum());

        String vString = "ABCabc1";
        wsVariabsus.setFsFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(wsVariabsus.getFsFlaNum()));
    }

    // property wsDraCve
    @Test
    void testGetterSetterWsDraCve() {

        assertNotNull(wsVariabsus.getWsDraCve());

        int vint = 1234;
        wsVariabsus.setWsDraCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsDraCve()));
    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        assertNotNull(wsVariabsus.getWsAgtIdr());

        int vint = 123456789;
        wsVariabsus.setWsAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsAgtIdr()));
    }

    // property wsAgtFecCamRazSo
    @Test
    void testGetterSetterWsAgtFecCamRazSo() {

        assertNotNull(wsVariabsus.getWsAgtFecCamRazSo());

        String vString = "ABCabc1230";
        wsVariabsus.setWsAgtFecCamRazSo(vString); // String
        assertEquals(asStr(vString), asStr(wsVariabsus.getWsAgtFecCamRazSo()));
    }

    // property wsAgtClfLmr
    @Test
    void testGetterSetterWsAgtClfLmr() {

        assertNotNull(wsVariabsus.getWsAgtClfLmr());

        int vint = 1234;
        wsVariabsus.setWsAgtClfLmr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsAgtClfLmr()));
    }

    // property wsPefCve
    @Test
    void testGetterSetterWsPefCve() {

        assertNotNull(wsVariabsus.getWsPefCve());

        int vint = 1234;
        wsVariabsus.setWsPefCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsPefCve()));
    }

    // property wsMteCve
    @Test
    void testGetterSetterWsMteCve() {

        assertNotNull(wsVariabsus.getWsMteCve());

        int vint = 1234;
        wsVariabsus.setWsMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsMteCve()));
    }

    // property wsAgtNumFolInv
    @Test
    void testGetterSetterWsAgtNumFolInv() {

        assertNotNull(wsVariabsus.getWsAgtNumFolInv());

        int vint = 123456789;
        wsVariabsus.setWsAgtNumFolInv(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsAgtNumFolInv()));
    }

    // property wsCnaNum
    @Test
    void testGetterSetterWsCnaNum() {

        assertNotNull(wsVariabsus.getWsCnaNum());

        int vint = 1234;
        wsVariabsus.setWsCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaNum()));
    }

    // property wsDrcPjeCom
    @Test
    void testGetterSetterWsDrcPjeCom() {

        assertNotNull(wsVariabsus.getWsDrcPjeCom());

        BigDecimal vBigDecimal = new BigDecimal("123.1234567");
        wsVariabsus.setWsDrcPjeCom(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsDrcPjeCom().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsDrcPjeCom(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsDrcPjeCom()));

        NumericVar vINumericValue = new NumericVar(10, 7);;
        vINumericValue.setValue("123.1234567");
        wsVariabsus.setWsDrcPjeCom(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsDrcPjeCom()));
    }

    // property wsDrcPjeCom5
    @Test
    void testGetterSetterWsDrcPjeCom5() {

        assertNotNull(wsVariabsus.getWsDrcPjeCom5());

        BigDecimal vBigDecimal = new BigDecimal("123.1234567");
        wsVariabsus.setWsDrcPjeCom5(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsDrcPjeCom5().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsDrcPjeCom5(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsDrcPjeCom5()));

        NumericVar vINumericValue = new NumericVar(10, 7);;
        vINumericValue.setValue("123.1234567");
        wsVariabsus.setWsDrcPjeCom5(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsDrcPjeCom5()));
    }

    // property wsDrcPjeCom5R
    @Test
    void testGetterSetterWsDrcPjeCom5R() {

        assertNotNull(wsVariabsus.getWsDrcPjeCom5R());
    }

    // property wsCnaPjeIva
    @Test
    void testGetterSetterWsCnaPjeIva() {

        assertNotNull(wsVariabsus.getWsCnaPjeIva());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsVariabsus.setWsCnaPjeIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsCnaPjeIva().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsCnaPjeIva(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaPjeIva()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsVariabsus.setWsCnaPjeIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsCnaPjeIva()));
    }

    // property wsPjeIva
    @Test
    void testGetterSetterWsPjeIva() {

        assertNotNull(wsVariabsus.getWsPjeIva());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsVariabsus.setWsPjeIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsPjeIva().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsPjeIva(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsPjeIva()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsVariabsus.setWsPjeIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsPjeIva()));
    }

    // property wsPjeIvaR
    @Test
    void testGetterSetterWsPjeIvaR() {

        assertNotNull(wsVariabsus.getWsPjeIvaR());
    }

    // property wsCnaPjeIsr
    @Test
    void testGetterSetterWsCnaPjeIsr() {

        assertNotNull(wsVariabsus.getWsCnaPjeIsr());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsVariabsus.setWsCnaPjeIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsCnaPjeIsr().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsCnaPjeIsr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaPjeIsr()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsVariabsus.setWsCnaPjeIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsCnaPjeIsr()));
    }

    // property wsPjeIsr
    @Test
    void testGetterSetterWsPjeIsr() {

        assertNotNull(wsVariabsus.getWsPjeIsr());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsVariabsus.setWsPjeIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsPjeIsr().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsPjeIsr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsPjeIsr()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsVariabsus.setWsPjeIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsPjeIsr()));
    }

    // property wsPjeIsrR
    @Test
    void testGetterSetterWsPjeIsrR() {

        assertNotNull(wsVariabsus.getWsPjeIsrR());
    }

    // property wsCnaImpComNiv
    @Test
    void testGetterSetterWsCnaImpComNiv() {

        assertNotNull(wsVariabsus.getWsCnaImpComNiv());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariabsus.setWsCnaImpComNiv(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsCnaImpComNiv().toBigDecimal());

        int vint = 123456789;
        wsVariabsus.setWsCnaImpComNiv(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaImpComNiv()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariabsus.setWsCnaImpComNiv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsCnaImpComNiv()));
    }

    // property wsComNiv
    @Test
    void testGetterSetterWsComNiv() {

        assertNotNull(wsVariabsus.getWsComNiv());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariabsus.setWsComNiv(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsComNiv().toBigDecimal());

        int vint = 123456789;
        wsVariabsus.setWsComNiv(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsComNiv()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariabsus.setWsComNiv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsComNiv()));
    }

    // property wsComNivR
    @Test
    void testGetterSetterWsComNivR() {

        assertNotNull(wsVariabsus.getWsComNivR());
    }

    // property wsCnaImpFza
    @Test
    void testGetterSetterWsCnaImpFza() {

        assertNotNull(wsVariabsus.getWsCnaImpFza());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariabsus.setWsCnaImpFza(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsCnaImpFza().toBigDecimal());

        int vint = 123456789;
        wsVariabsus.setWsCnaImpFza(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaImpFza()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariabsus.setWsCnaImpFza(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsCnaImpFza()));
    }

    // property wsImpFza
    @Test
    void testGetterSetterWsImpFza() {

        assertNotNull(wsVariabsus.getWsImpFza());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariabsus.setWsImpFza(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsImpFza().toBigDecimal());

        int vint = 123456789;
        wsVariabsus.setWsImpFza(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsImpFza()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariabsus.setWsImpFza(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsImpFza()));
    }

    // property wsImpFzaR
    @Test
    void testGetterSetterWsImpFzaR() {

        assertNotNull(wsVariabsus.getWsImpFzaR());
    }

    // property wsCnaPjeFav
    @Test
    void testGetterSetterWsCnaPjeFav() {

        assertNotNull(wsVariabsus.getWsCnaPjeFav());

        BigDecimal vBigDecimal = new BigDecimal("123.1234");
        wsVariabsus.setWsCnaPjeFav(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsCnaPjeFav().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsCnaPjeFav(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaPjeFav()));

        NumericVar vINumericValue = new NumericVar(7, 4);;
        vINumericValue.setValue("123.1234");
        wsVariabsus.setWsCnaPjeFav(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsCnaPjeFav()));
    }

    // property wsPjeFav
    @Test
    void testGetterSetterWsPjeFav() {

        assertNotNull(wsVariabsus.getWsPjeFav());

        BigDecimal vBigDecimal = new BigDecimal("123.1234");
        wsVariabsus.setWsPjeFav(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariabsus.getWsPjeFav().toBigDecimal());

        int vint = 123;
        wsVariabsus.setWsPjeFav(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsPjeFav()));

        NumericVar vINumericValue = new NumericVar(7, 4);;
        vINumericValue.setValue("123.1234");
        wsVariabsus.setWsPjeFav(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariabsus.getWsPjeFav()));
    }

    // property wsPjeFavR
    @Test
    void testGetterSetterWsPjeFavR() {

        assertNotNull(wsVariabsus.getWsPjeFavR());
    }

    // property wsPpgCve
    @Test
    void testGetterSetterWsPpgCve() {

        assertNotNull(wsVariabsus.getWsPpgCve());

        int vint = 1234;
        wsVariabsus.setWsPpgCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsPpgCve()));
    }

    // property wsCpgCve
    @Test
    void testGetterSetterWsCpgCve() {

        assertNotNull(wsVariabsus.getWsCpgCve());

        int vint = 1234;
        wsVariabsus.setWsCpgCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCpgCve()));
    }

    // property wsCdeCve
    @Test
    void testGetterSetterWsCdeCve() {

        assertNotNull(wsVariabsus.getWsCdeCve());

        int vint = 1234;
        wsVariabsus.setWsCdeCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCdeCve()));
    }

    // property wsOfnCve
    @Test
    void testGetterSetterWsOfnCve() {

        assertNotNull(wsVariabsus.getWsOfnCve());

        int vint = 1234;
        wsVariabsus.setWsOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsOfnCve()));
    }

    // property wsGnaCve
    @Test
    void testGetterSetterWsGnaCve() {

        assertNotNull(wsVariabsus.getWsGnaCve());

        int vint = 1234;
        wsVariabsus.setWsGnaCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsGnaCve()));
    }

    // property wsTfpCve
    @Test
    void testGetterSetterWsTfpCve() {

        assertNotNull(wsVariabsus.getWsTfpCve());

        int vint = 1234;
        wsVariabsus.setWsTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsTfpCve()));
    }

    // property wsAgtIdrRcl
    @Test
    void testGetterSetterWsAgtIdrRcl() {

        assertNotNull(wsVariabsus.getWsAgtIdrRcl());

        int vint = 123456789;
        wsVariabsus.setWsAgtIdrRcl(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsAgtIdrRcl()));
    }

    // property wsMdeCve
    @Test
    void testGetterSetterWsMdeCve() {

        assertNotNull(wsVariabsus.getWsMdeCve());

        int vint = 1234;
        wsVariabsus.setWsMdeCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsMdeCve()));
    }

    // property wsZecCve
    @Test
    void testGetterSetterWsZecCve() {

        assertNotNull(wsVariabsus.getWsZecCve());

        int vint = 1234;
        wsVariabsus.setWsZecCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsZecCve()));
    }

    // property wsCnaGerZonEnv
    @Test
    void testGetterSetterWsCnaGerZonEnv() {

        assertNotNull(wsVariabsus.getWsCnaGerZonEnv());

        int vint = 1234;
        wsVariabsus.setWsCnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaGerZonEnv()));
    }

    // property wsCnaGerZonDcm
    @Test
    void testGetterSetterWsCnaGerZonDcm() {

        assertNotNull(wsVariabsus.getWsCnaGerZonDcm());

        int vint = 1234;
        wsVariabsus.setWsCnaGerZonDcm(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCnaGerZonDcm()));
    }

    // property wsDmaIdr
    @Test
    void testGetterSetterWsDmaIdr() {

        assertNotNull(wsVariabsus.getWsDmaIdr());

        int vint = 1234;
        wsVariabsus.setWsDmaIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsDmaIdr()));
    }

    // property wsTdmCve
    @Test
    void testGetterSetterWsTdmCve() {

        assertNotNull(wsVariabsus.getWsTdmCve());

        int vint = 1234;
        wsVariabsus.setWsTdmCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsTdmCve()));
    }

    // property wsPobCve
    @Test
    void testGetterSetterWsPobCve() {

        assertNotNull(wsVariabsus.getWsPobCve());

        int vint = 123456789;
        wsVariabsus.setWsPobCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsPobCve()));
    }

    // property wsCpoCve
    @Test
    void testGetterSetterWsCpoCve() {

        assertNotNull(wsVariabsus.getWsCpoCve());

        int vint = 123456789;
        wsVariabsus.setWsCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCpoCve()));
    }

    // property wsMndCve
    @Test
    void testGetterSetterWsMndCve() {

        assertNotNull(wsVariabsus.getWsMndCve());

        int vint = 123456789;
        wsVariabsus.setWsMndCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsMndCve()));
    }

    // property wsCcmCve
    @Test
    void testGetterSetterWsCcmCve() {

        assertNotNull(wsVariabsus.getWsCcmCve());

        int vint = 1234;
        wsVariabsus.setWsCcmCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariabsus.getWsCcmCve()));
    }

    // property wsEssRek
    @Test
    void testGetterSetterWsEssRek() {

        assertNotNull(wsVariabsus.getWsEssRek());

        String vString = "A";
        wsVariabsus.setWsEssRek(vString); // String
        assertEquals(asStr(vString), asStr(wsVariabsus.getWsEssRek()));
    }

    // property rsDrcPjeCom53
    @Test
    void testGetterSetterRsDrcPjeCom53() {

        // Property: wsDrcPjeCom5R.rsDrcPjeCom53 -> 10 RS-DRC-PJE-COM53 X(03)
        assertNotNull(wsVariabsus.getRsDrcPjeCom53());
        String vString = "ABC";
        wsVariabsus.setRsDrcPjeCom53(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsVariabsus.setRsDrcPjeCom53(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsDrcPjeCom57
    @Test
    void testGetterSetterRsDrcPjeCom57() {

        // Property: wsDrcPjeCom5R.rsDrcPjeCom57 -> 10 RS-DRC-PJE-COM57 X(07)
        assertNotNull(wsVariabsus.getRsDrcPjeCom57());
        String vString = "ABCabc1";
        wsVariabsus.setRsDrcPjeCom57(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wsVariabsus.setRsDrcPjeCom57(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsPjeIva3
    @Test
    void testGetterSetterRsPjeIva3() {

        // Property: wsPjeIvaR.rsPjeIva3 -> 10 RS-PJE-IVA3 X(03)
        assertNotNull(wsVariabsus.getRsPjeIva3());
        String vString = "ABC";
        wsVariabsus.setRsPjeIva3(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsVariabsus.setRsPjeIva3(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsPjeIva2
    @Test
    void testGetterSetterRsPjeIva2() {

        // Property: wsPjeIvaR.rsPjeIva2 -> 10 RS-PJE-IVA2 X(02)
        assertNotNull(wsVariabsus.getRsPjeIva2());
        String vString = "AB";
        wsVariabsus.setRsPjeIva2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsVariabsus.setRsPjeIva2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsPjeIsr3
    @Test
    void testGetterSetterRsPjeIsr3() {

        // Property: wsPjeIsrR.rsPjeIsr3 -> 10 RS-PJE-ISR3 X(03)
        assertNotNull(wsVariabsus.getRsPjeIsr3());
        String vString = "ABC";
        wsVariabsus.setRsPjeIsr3(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsVariabsus.setRsPjeIsr3(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsPjeIsr2
    @Test
    void testGetterSetterRsPjeIsr2() {

        // Property: wsPjeIsrR.rsPjeIsr2 -> 10 RS-PJE-ISR2 X(02)
        assertNotNull(wsVariabsus.getRsPjeIsr2());
        String vString = "AB";
        wsVariabsus.setRsPjeIsr2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsVariabsus.setRsPjeIsr2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsComNiv11
    @Test
    void testGetterSetterRsComNiv11() {

        // Property: wsComNivR.rsComNiv11 -> 10 RS-COM-NIV11 X(11)
        assertNotNull(wsVariabsus.getRsComNiv11());
        String vString = "ABCabc1230";
        wsVariabsus.setRsComNiv11(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsVariabsus.setRsComNiv11(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsComNiv2
    @Test
    void testGetterSetterRsComNiv2() {

        // Property: wsComNivR.rsComNiv2 -> 10 RS-COM-NIV2 X(02)
        assertNotNull(wsVariabsus.getRsComNiv2());
        String vString = "AB";
        wsVariabsus.setRsComNiv2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsVariabsus.setRsComNiv2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsImpFza11
    @Test
    void testGetterSetterRsImpFza11() {

        // Property: wsImpFzaR.rsImpFza11 -> 10 RS-IMP-FZA11 X(11)
        assertNotNull(wsVariabsus.getRsImpFza11());
        String vString = "ABCabc1230";
        wsVariabsus.setRsImpFza11(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsVariabsus.setRsImpFza11(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsImpFza2
    @Test
    void testGetterSetterRsImpFza2() {

        // Property: wsImpFzaR.rsImpFza2 -> 10 RS-IMP-FZA2 X(02)
        assertNotNull(wsVariabsus.getRsImpFza2());
        String vString = "AB";
        wsVariabsus.setRsImpFza2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsVariabsus.setRsImpFza2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsPjeFav3
    @Test
    void testGetterSetterRsPjeFav3() {

        // Property: wsPjeFavR.rsPjeFav3 -> 10 RS-PJE-FAV3 X(03)
        assertNotNull(wsVariabsus.getRsPjeFav3());
        String vString = "ABC";
        wsVariabsus.setRsPjeFav3(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsVariabsus.setRsPjeFav3(vIAlphanumericValue); // IAlphanumericValue
    }

    // property rsPjeFav4
    @Test
    void testGetterSetterRsPjeFav4() {

        // Property: wsPjeFavR.rsPjeFav4 -> 10 RS-PJE-FAV4 X(04)
        assertNotNull(wsVariabsus.getRsPjeFav4());
        String vString = "ABCa";
        wsVariabsus.setRsPjeFav4(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsVariabsus.setRsPjeFav4(vIAlphanumericValue); // IAlphanumericValue
    }
}

