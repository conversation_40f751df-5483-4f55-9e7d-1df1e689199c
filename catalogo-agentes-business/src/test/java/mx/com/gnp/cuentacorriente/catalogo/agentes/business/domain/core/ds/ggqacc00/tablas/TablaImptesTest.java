package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablas;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-IMPTES.
 *
 */
class TablaImptesTest {

    private TablaImptes tablaImptes;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaImptes = new TablaImptes();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tabImptes
    @Test
    void testGetterSetterTabImptes() {

        assertNotNull(tablaImptes.getTabImptes());
        assertNotNull(tablaImptes.getTabImptes(1));
    }

    // property tbPercGravab
    @Test
    void testGetterSetterTbPercGravab() {

        // Property: tabImptes.tbPercGravab -> 05 TB-PERC-GRAVAB S9(14)V99
        assertNotNull(tablaImptes.getTbPercGravab());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbPercGravab(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbPercGravab(vINumericValue); // INumericValue
    }

    // property tbPercExenta
    @Test
    void testGetterSetterTbPercExenta() {

        // Property: tabImptes.tbPercExenta -> 05 TB-PERC-EXENTA S9(14)V99
        assertNotNull(tablaImptes.getTbPercExenta());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbPercExenta(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbPercExenta(vINumericValue); // INumericValue
    }

    // property tbIva
    @Test
    void testGetterSetterTbIva() {

        // Property: tabImptes.tbIva -> 05 TB-IVA S9(14)V99
        assertNotNull(tablaImptes.getTbIva());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbIva(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbIva(vINumericValue); // INumericValue
    }

    // property tbIsr
    @Test
    void testGetterSetterTbIsr() {

        // Property: tabImptes.tbIsr -> 05 TB-ISR S9(14)V99
        assertNotNull(tablaImptes.getTbIsr());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbIsr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbIsr(vINumericValue); // INumericValue
    }

    // property tbIvaAcr
    @Test
    void testGetterSetterTbIvaAcr() {

        // Property: tabImptes.tbIvaAcr -> 05 TB-IVA-ACR S9(14)V99
        assertNotNull(tablaImptes.getTbIvaAcr());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbIvaAcr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbIvaAcr(vINumericValue); // INumericValue
    }

    // property tbIvaRtn
    @Test
    void testGetterSetterTbIvaRtn() {

        // Property: tabImptes.tbIvaRtn -> 05 TB-IVA-RTN S9(14)V99
        assertNotNull(tablaImptes.getTbIvaRtn());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbIvaRtn(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbIvaRtn(vINumericValue); // INumericValue
    }

    // property tbIsrRet
    @Test
    void testGetterSetterTbIsrRet() {

        // Property: tabImptes.tbIsrRet -> 05 TB-ISR-RET S9(14)V99
        assertNotNull(tablaImptes.getTbIsrRet());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbIsrRet(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbIsrRet(vINumericValue); // INumericValue
    }

    // property tbIpuCed
    @Test
    void testGetterSetterTbIpuCed() {

        // Property: tabImptes.tbIpuCed -> 05 TB-IPU-CED S9(14)V99
        assertNotNull(tablaImptes.getTbIpuCed());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablaImptes.setTbIpuCed(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablaImptes.setTbIpuCed(vINumericValue); // INumericValue
    }
}

