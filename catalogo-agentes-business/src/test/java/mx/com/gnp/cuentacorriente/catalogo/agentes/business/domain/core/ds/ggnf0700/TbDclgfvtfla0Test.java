package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TB-DCLGFVTFLA0.
 *
 */
class TbDclgfvtfla0Test {

    private TbDclgfvtfla0 tbDclgfvtfla0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tbDclgfvtfla0 = new TbDclgfvtfla0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TbDclgfvtfla0 tbDclgfvtfla02 = new TbDclgfvtfla0(parent);
        assertNotNull(tbDclgfvtfla02);
        AlphanumericVar sibling = new AlphanumericVar(parent, 106);
        TbDclgfvtfla0 tbDclgfvtfla03 = new TbDclgfvtfla0(parent, sibling);
        assertNotNull(tbDclgfvtfla03);
    }

    @Test
    void testInitialize() {
        assertNotNull(tbDclgfvtfla0.getTbFlaNum());

        assertNotNull(tbDclgfvtfla0.getTbFlaDes());

        assertNotNull(tbDclgfvtfla0.getTbFlaFecIniAsg());

        assertNotNull(tbDclgfvtfla0.getTbFla03());

        assertNotNull(tbDclgfvtfla0.getTbFlaFecFinAsg());

        assertNotNull(tbDclgfvtfla0.getTbFla04());

        assertNotNull(tbDclgfvtfla0.getTbOfnCve());

        assertNotNull(tbDclgfvtfla0.getTbFla05());

        assertNotNull(tbDclgfvtfla0.getTbAgtIdr());

        assertNotNull(tbDclgfvtfla0.getTbEmpCve());

        assertNotNull(tbDclgfvtfla0.getTbCnaNum());

        assertNotNull(tbDclgfvtfla0.getTbMteCve());

        assertNotNull(tbDclgfvtfla0.getTbOrvCve());

        assertNotNull(tbDclgfvtfla0.getTbFlaGerZon());

        assertNotNull(tbDclgfvtfla0.getTbFla11());

        assertNotNull(tbDclgfvtfla0.getTbFlaNegCve());

        assertNotNull(tbDclgfvtfla0.getTbFla12());

        assertNotNull(tbDclgfvtfla0.getTbFlaFecMotEss());

        assertNotNull(tbDclgfvtfla0.getTbFla13());

    }

    // property tbFlaNum
    @Test
    void testGetterSetterTbFlaNum() {

        assertNotNull(tbDclgfvtfla0.getTbFlaNum());

        String vString = "ABCabc12";
        tbDclgfvtfla0.setTbFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFlaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tbDclgfvtfla0.setTbFlaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFlaNum()));
    }

    // property tbFlaDes
    @Test
    void testGetterSetterTbFlaDes() {

        assertNotNull(tbDclgfvtfla0.getTbFlaDes());

        String vString = "ABCabc1230";
        tbDclgfvtfla0.setTbFlaDes(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFlaDes()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtfla0.setTbFlaDes(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFlaDes()));
    }

    // property tbFlaFecIniAsg
    @Test
    void testGetterSetterTbFlaFecIniAsg() {

        assertNotNull(tbDclgfvtfla0.getTbFlaFecIniAsg());

        String vString = "ABCabc1230";
        tbDclgfvtfla0.setTbFlaFecIniAsg(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFlaFecIniAsg()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtfla0.setTbFlaFecIniAsg(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFlaFecIniAsg()));
    }

    // property tbFla03
    @Test
    void testGetterSetterTbFla03() {

        assertNotNull(tbDclgfvtfla0.getTbFla03());

        String vString = "A";
        tbDclgfvtfla0.setTbFla03(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFla03()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtfla0.setTbFla03(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFla03()));
    }

    // property tbFlaFecFinAsg
    @Test
    void testGetterSetterTbFlaFecFinAsg() {

        assertNotNull(tbDclgfvtfla0.getTbFlaFecFinAsg());

        String vString = "ABCabc1230";
        tbDclgfvtfla0.setTbFlaFecFinAsg(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFlaFecFinAsg()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtfla0.setTbFlaFecFinAsg(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFlaFecFinAsg()));
    }

    // property tbFla04
    @Test
    void testGetterSetterTbFla04() {

        assertNotNull(tbDclgfvtfla0.getTbFla04());

        String vString = "A";
        tbDclgfvtfla0.setTbFla04(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFla04()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtfla0.setTbFla04(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFla04()));
    }

    // property tbOfnCve
    @Test
    void testGetterSetterTbOfnCve() {

        assertNotNull(tbDclgfvtfla0.getTbOfnCve());

        int vint = 1234;
        tbDclgfvtfla0.setTbOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtfla0.getTbOfnCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtfla0.setTbOfnCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtfla0.getTbOfnCve()));
    }

    // property tbFla05
    @Test
    void testGetterSetterTbFla05() {

        assertNotNull(tbDclgfvtfla0.getTbFla05());

        String vString = "A";
        tbDclgfvtfla0.setTbFla05(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFla05()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtfla0.setTbFla05(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFla05()));
    }

    // property tbAgtIdr
    @Test
    void testGetterSetterTbAgtIdr() {

        assertNotNull(tbDclgfvtfla0.getTbAgtIdr());

        int vint = 123456789;
        tbDclgfvtfla0.setTbAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtfla0.getTbAgtIdr()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        tbDclgfvtfla0.setTbAgtIdr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtfla0.getTbAgtIdr()));
    }

    // property tbEmpCve
    @Test
    void testGetterSetterTbEmpCve() {

        assertNotNull(tbDclgfvtfla0.getTbEmpCve());

        String vString = "ABCab";
        tbDclgfvtfla0.setTbEmpCve(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbEmpCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tbDclgfvtfla0.setTbEmpCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbEmpCve()));
    }

    // property tbCnaNum
    @Test
    void testGetterSetterTbCnaNum() {

        assertNotNull(tbDclgfvtfla0.getTbCnaNum());

        int vint = 1234;
        tbDclgfvtfla0.setTbCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtfla0.getTbCnaNum()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtfla0.setTbCnaNum(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtfla0.getTbCnaNum()));
    }

    // property tbMteCve
    @Test
    void testGetterSetterTbMteCve() {

        assertNotNull(tbDclgfvtfla0.getTbMteCve());

        int vint = 1234;
        tbDclgfvtfla0.setTbMteCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtfla0.getTbMteCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtfla0.setTbMteCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtfla0.getTbMteCve()));
    }

    // property tbOrvCve
    @Test
    void testGetterSetterTbOrvCve() {

        assertNotNull(tbDclgfvtfla0.getTbOrvCve());

        int vint = 1234;
        tbDclgfvtfla0.setTbOrvCve(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtfla0.getTbOrvCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtfla0.setTbOrvCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtfla0.getTbOrvCve()));
    }

    // property tbFlaGerZon
    @Test
    void testGetterSetterTbFlaGerZon() {

        assertNotNull(tbDclgfvtfla0.getTbFlaGerZon());

        int vint = 1234;
        tbDclgfvtfla0.setTbFlaGerZon(vint); // int
        assertEquals(asInt(vint), asInt(tbDclgfvtfla0.getTbFlaGerZon()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbDclgfvtfla0.setTbFlaGerZon(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tbDclgfvtfla0.getTbFlaGerZon()));
    }

    // property tbFla11
    @Test
    void testGetterSetterTbFla11() {

        assertNotNull(tbDclgfvtfla0.getTbFla11());

        String vString = "A";
        tbDclgfvtfla0.setTbFla11(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFla11()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtfla0.setTbFla11(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFla11()));
    }

    // property tbFlaNegCve
    @Test
    void testGetterSetterTbFlaNegCve() {

        assertNotNull(tbDclgfvtfla0.getTbFlaNegCve());

        String vString = "ABC";
        tbDclgfvtfla0.setTbFlaNegCve(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFlaNegCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        tbDclgfvtfla0.setTbFlaNegCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFlaNegCve()));
    }

    // property tbFla12
    @Test
    void testGetterSetterTbFla12() {

        assertNotNull(tbDclgfvtfla0.getTbFla12());

        String vString = "A";
        tbDclgfvtfla0.setTbFla12(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFla12()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtfla0.setTbFla12(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFla12()));
    }

    // property tbFlaFecMotEss
    @Test
    void testGetterSetterTbFlaFecMotEss() {

        assertNotNull(tbDclgfvtfla0.getTbFlaFecMotEss());

        String vString = "ABCabc1230";
        tbDclgfvtfla0.setTbFlaFecMotEss(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFlaFecMotEss()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tbDclgfvtfla0.setTbFlaFecMotEss(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFlaFecMotEss()));
    }

    // property tbFla13
    @Test
    void testGetterSetterTbFla13() {

        assertNotNull(tbDclgfvtfla0.getTbFla13());

        String vString = "A";
        tbDclgfvtfla0.setTbFla13(vString); // String
        assertEquals(asStr(vString), asStr(tbDclgfvtfla0.getTbFla13()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tbDclgfvtfla0.setTbFla13(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tbDclgfvtfla0.getTbFla13()));
    }
}

