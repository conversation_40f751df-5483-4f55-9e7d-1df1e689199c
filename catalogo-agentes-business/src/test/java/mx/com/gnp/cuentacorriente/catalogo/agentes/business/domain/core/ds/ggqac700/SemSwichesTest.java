package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: SEM-SWICHES.
 *
 */
class SemSwichesTest {

    private SemSwiches semSwiches;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        semSwiches = new SemSwiches();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        semSwiches.setSemArchivoOfn("N");
        assertEquals("N", semSwiches.getSemArchivoOfn());

        semSwiches.setSemPrimeraVez("N");
        assertEquals("N", semSwiches.getSemPrimeraVez());

        semSwiches.setSemCampoDifer("N");
        assertEquals("N", semSwiches.getSemCampoDifer());

    }

    // property semArchivoOfn
    @Test
    void testGetterSetterSemArchivoOfn() {

        assertNotNull(semSwiches.getSemArchivoOfn());

        String vString = "A";
        semSwiches.setSemArchivoOfn(vString); // String
        assertEquals(asStr(vString), asStr(semSwiches.getSemArchivoOfn()));
        // Conditionals (88) - SEM-ARCHIVO-OFN
        semSwiches.setSwSiFinArchOfn();
        assertTrue(semSwiches.isSwSiFinArchOfn());
        semSwiches.setSwNoFinArchOfn();
        assertTrue(semSwiches.isSwNoFinArchOfn());

    }

    // property semPrimeraVez
    @Test
    void testGetterSetterSemPrimeraVez() {

        assertNotNull(semSwiches.getSemPrimeraVez());

        String vString = "A";
        semSwiches.setSemPrimeraVez(vString); // String
        assertEquals(asStr(vString), asStr(semSwiches.getSemPrimeraVez()));
        // Conditionals (88) - SEM-PRIMERA-VEZ
        semSwiches.setSwSiPrimeraVez();
        assertTrue(semSwiches.isSwSiPrimeraVez());
        semSwiches.setSwNoPrimeraVez();
        assertTrue(semSwiches.isSwNoPrimeraVez());

    }

    // property semCampoDifer
    @Test
    void testGetterSetterSemCampoDifer() {

        assertNotNull(semSwiches.getSemCampoDifer());

        String vString = "A";
        semSwiches.setSemCampoDifer(vString); // String
        assertEquals(asStr(vString), asStr(semSwiches.getSemCampoDifer()));
        // Conditionals (88) - SEM-CAMPO-DIFER
        semSwiches.setSwSiCampoDifer();
        assertTrue(semSwiches.isSwSiCampoDifer());
        semSwiches.setSwNoCampoDifer();
        assertTrue(semSwiches.isSwNoCampoDifer());

    }
}

