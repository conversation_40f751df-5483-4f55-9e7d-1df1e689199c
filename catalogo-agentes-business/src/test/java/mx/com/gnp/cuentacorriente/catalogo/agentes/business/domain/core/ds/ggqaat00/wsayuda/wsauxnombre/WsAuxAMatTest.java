package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-A-MAT.
 *
 */
class WsAuxAMatTest {

    private WsAuxAMat wsAuxAMat;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxAMat = new WsAuxAMat();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAuxAMat wsAuxAMat2 = new WsAuxAMat(parent);
        assertNotNull(wsAuxAMat2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 30);
        WsAuxAMat wsAuxAMat3 = new WsAuxAMat(parent, sibling);
        assertNotNull(wsAuxAMat3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAuxAMat.getWsAuxApMat());

    }

    // property wsAuxApMat
    @Test
    void testGetterSetterWsAuxApMat() {

        assertNotNull(wsAuxAMat.getWsAuxApMat());
        assertNotNull(wsAuxAMat.getWsAuxApMat(1));

        String vString = "A";
        wsAuxAMat.getWsAuxApMat().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxAMat.getWsAuxApMat(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxAMat.getWsAuxApMat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsAuxAMat.getWsAuxApMat(1)));
    }
}

