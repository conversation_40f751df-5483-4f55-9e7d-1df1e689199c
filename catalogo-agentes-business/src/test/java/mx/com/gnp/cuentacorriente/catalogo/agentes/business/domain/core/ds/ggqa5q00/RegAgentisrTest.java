package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-AGENTISR.
 *
 */
class RegAgentisrTest {

    private RegAgentisr regAgentisr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regAgentisr = new RegAgentisr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegAgentisr regAgentisr2 = new RegAgentisr(parent);
        assertNotNull(regAgentisr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 328);
        RegAgentisr regAgentisr3 = new RegAgentisr(parent, sibling);
        assertNotNull(regAgentisr3);
    }

    @Test
    void testInitialize() {

        assertNotNull(regAgentisr.getVsContra());

        assertNotNull(regAgentisr.getVsRegfis());

        assertNotNull(regAgentisr.getVsNombre());

        assertNotNull(regAgentisr.getVsClave());

        assertNotNull(regAgentisr.getVsCedNovi());

        assertNotNull(regAgentisr.getVsCedVida());

    }

    // property vsLlaveAgt
    @Test
    void testGetterSetterVsLlaveAgt() {

        assertNotNull(regAgentisr.getVsLlaveAgt());
    }

    // property vsContra
    @Test
    void testGetterSetterVsContra() {

        assertNotNull(regAgentisr.getVsContra());

        String vString = "ABC";
        regAgentisr.setVsContra(vString); // String
        assertEquals(asStr(vString), asStr(regAgentisr.getVsContra()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regAgentisr.setVsContra(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regAgentisr.getVsContra()));
    }

    // property vsRegfis
    @Test
    void testGetterSetterVsRegfis() {

        assertNotNull(regAgentisr.getVsRegfis());

        String vString = "ABC";
        regAgentisr.setVsRegfis(vString); // String
        assertEquals(asStr(vString), asStr(regAgentisr.getVsRegfis()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regAgentisr.setVsRegfis(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regAgentisr.getVsRegfis()));
    }

    // property vsNombre
    @Test
    void testGetterSetterVsNombre() {

        assertNotNull(regAgentisr.getVsNombre());

        String vString = "ABCabc1230";
        regAgentisr.setVsNombre(vString); // String
        assertEquals(asStr(vString), asStr(regAgentisr.getVsNombre()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regAgentisr.setVsNombre(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regAgentisr.getVsNombre()));
    }

    // property vsClave
    @Test
    void testGetterSetterVsClave() {

        assertNotNull(regAgentisr.getVsClave());

        String vString = "ABCab";
        regAgentisr.setVsClave(vString); // String
        assertEquals(asStr(vString), asStr(regAgentisr.getVsClave()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regAgentisr.setVsClave(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regAgentisr.getVsClave()));
    }

    // property vsCedNovi
    @Test
    void testGetterSetterVsCedNovi() {

        assertNotNull(regAgentisr.getVsCedNovi());

        String vString = "ABCab";
        regAgentisr.setVsCedNovi(vString); // String
        assertEquals(asStr(vString), asStr(regAgentisr.getVsCedNovi()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regAgentisr.setVsCedNovi(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regAgentisr.getVsCedNovi()));
    }

    // property vsCedVida
    @Test
    void testGetterSetterVsCedVida() {

        assertNotNull(regAgentisr.getVsCedVida());

        String vString = "ABCab";
        regAgentisr.setVsCedVida(vString); // String
        assertEquals(asStr(vString), asStr(regAgentisr.getVsCedVida()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regAgentisr.setVsCedVida(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regAgentisr.getVsCedVida()));
    }

    // property vsAgente
    @Test
    void testGetterSetterVsAgente() {

        // Property: vsLlaveAgt.vsAgente -> 07 VS-AGENTE X(07)
        assertNotNull(regAgentisr.getVsAgente());
        String vString = "ABCabc1";
        regAgentisr.setVsAgente(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        regAgentisr.setVsAgente(vIAlphanumericValue); // IAlphanumericValue
    }
}

