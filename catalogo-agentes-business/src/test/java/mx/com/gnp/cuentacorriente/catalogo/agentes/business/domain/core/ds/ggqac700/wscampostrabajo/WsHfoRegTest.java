package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.wscampostrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-HFO-REG.
 *
 */
class WsHfoRegTest {

    private WsHfoReg wsHfoReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsHfoReg = new WsHfoReg();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsHfoReg wsHfoReg2 = new WsHfoReg(parent);
        assertNotNull(wsHfoReg2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 371);
        WsHfoReg wsHfoReg3 = new WsHfoReg(parent, sibling);
        assertNotNull(wsHfoReg3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsHfoReg.getWsHfoOfnCve());

        assertNotNull(wsHfoReg.getWsHfoFecIniPdo());

        assertNotNull(wsHfoReg.getWsHfoFecFinPdo());

        assertNotNull(wsHfoReg.getWsHfoRfcRcp());

        assertNotNull(wsHfoReg.getWsHfoNomRcp());

        assertNotNull(wsHfoReg.getWsHfoOfnCae());

        assertNotNull(wsHfoReg.getWsHfoOfnNum());

        assertNotNull(wsHfoReg.getWsHfoOfnInt());

        assertNotNull(wsHfoReg.getWsHfoOfnCol());

        assertNotNull(wsHfoReg.getWsHfoMndNom());

        assertNotNull(wsHfoReg.getWsHfoEdoNom());

        assertNotNull(wsHfoReg.getWsHfoPaiNom());

        assertNotNull(wsHfoReg.getWsHfoCpoCve());

        assertNotNull(wsHfoReg.getWsHfoUsuCve());

        assertNotNull(wsHfoReg.getWsHfoNomPrg());

        assertNotNull(wsHfoReg.getWsHfoFecUltAct());

    }

    // property wsHfoOfnCve
    @Test
    void testGetterSetterWsHfoOfnCve() {

        assertNotNull(wsHfoReg.getWsHfoOfnCve());

        int vint = 1234;
        wsHfoReg.setWsHfoOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfoReg.getWsHfoOfnCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsHfoReg.setWsHfoOfnCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfoReg.getWsHfoOfnCve()));
    }

    // property wsHfoFecIniPdo
    @Test
    void testGetterSetterWsHfoFecIniPdo() {

        assertNotNull(wsHfoReg.getWsHfoFecIniPdo());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoFecIniPdo(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoFecIniPdo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoFecIniPdo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoFecIniPdo()));
    }

    // property wsHfoFecFinPdo
    @Test
    void testGetterSetterWsHfoFecFinPdo() {

        assertNotNull(wsHfoReg.getWsHfoFecFinPdo());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoFecFinPdo(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoFecFinPdo()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoFecFinPdo(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoFecFinPdo()));
    }

    // property wsHfoRfcRcp
    @Test
    void testGetterSetterWsHfoRfcRcp() {

        assertNotNull(wsHfoReg.getWsHfoRfcRcp());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoRfcRcp(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoRfcRcp()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoRfcRcp(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoRfcRcp()));
    }

    // property wsHfoNomRcp
    @Test
    void testGetterSetterWsHfoNomRcp() {

        assertNotNull(wsHfoReg.getWsHfoNomRcp());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoNomRcp(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoNomRcp()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoNomRcp(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoNomRcp()));
    }

    // property wsHfoOfnCae
    @Test
    void testGetterSetterWsHfoOfnCae() {

        assertNotNull(wsHfoReg.getWsHfoOfnCae());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoOfnCae(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoOfnCae()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoOfnCae(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoOfnCae()));
    }

    // property wsHfoOfnNum
    @Test
    void testGetterSetterWsHfoOfnNum() {

        assertNotNull(wsHfoReg.getWsHfoOfnNum());

        String vString = "ABCab";
        wsHfoReg.setWsHfoOfnNum(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoOfnNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsHfoReg.setWsHfoOfnNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoOfnNum()));
    }

    // property wsHfoOfnInt
    @Test
    void testGetterSetterWsHfoOfnInt() {

        assertNotNull(wsHfoReg.getWsHfoOfnInt());

        String vString = "ABCab";
        wsHfoReg.setWsHfoOfnInt(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoOfnInt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsHfoReg.setWsHfoOfnInt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoOfnInt()));
    }

    // property wsHfoOfnCol
    @Test
    void testGetterSetterWsHfoOfnCol() {

        assertNotNull(wsHfoReg.getWsHfoOfnCol());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoOfnCol(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoOfnCol()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoOfnCol(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoOfnCol()));
    }

    // property wsHfoMndNom
    @Test
    void testGetterSetterWsHfoMndNom() {

        assertNotNull(wsHfoReg.getWsHfoMndNom());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoMndNom(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoMndNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoMndNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoMndNom()));
    }

    // property wsHfoEdoNom
    @Test
    void testGetterSetterWsHfoEdoNom() {

        assertNotNull(wsHfoReg.getWsHfoEdoNom());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoEdoNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoEdoNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoEdoNom()));
    }

    // property wsHfoPaiNom
    @Test
    void testGetterSetterWsHfoPaiNom() {

        assertNotNull(wsHfoReg.getWsHfoPaiNom());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoPaiNom(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoPaiNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoPaiNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoPaiNom()));
    }

    // property wsHfoCpoCve
    @Test
    void testGetterSetterWsHfoCpoCve() {

        assertNotNull(wsHfoReg.getWsHfoCpoCve());

        int vint = 123456789;
        wsHfoReg.setWsHfoCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(wsHfoReg.getWsHfoCpoCve()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsHfoReg.setWsHfoCpoCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsHfoReg.getWsHfoCpoCve()));
    }

    // property wsHfoUsuCve
    @Test
    void testGetterSetterWsHfoUsuCve() {

        assertNotNull(wsHfoReg.getWsHfoUsuCve());

        String vString = "ABCabc12";
        wsHfoReg.setWsHfoUsuCve(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoUsuCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsHfoReg.setWsHfoUsuCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoUsuCve()));
    }

    // property wsHfoNomPrg
    @Test
    void testGetterSetterWsHfoNomPrg() {

        assertNotNull(wsHfoReg.getWsHfoNomPrg());

        String vString = "ABCabc12";
        wsHfoReg.setWsHfoNomPrg(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoNomPrg()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsHfoReg.setWsHfoNomPrg(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoNomPrg()));
    }

    // property wsHfoFecUltAct
    @Test
    void testGetterSetterWsHfoFecUltAct() {

        assertNotNull(wsHfoReg.getWsHfoFecUltAct());

        String vString = "ABCabc1230";
        wsHfoReg.setWsHfoFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsHfoReg.getWsHfoFecUltAct()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsHfoReg.setWsHfoFecUltAct(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsHfoReg.getWsHfoFecUltAct()));
    }
}

