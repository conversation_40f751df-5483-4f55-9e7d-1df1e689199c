package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablameses;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-MES-R.
 *
 */
class TablaMesRTest {

    private TablaMesR tablaMesR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaMesR = new TablaMesR(1);
        tablaMesR.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TablaMesR tablaMesR2 = new TablaMesR(parent, 1);
        assertNotNull(tablaMesR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TablaMesR tablaMesR3 = new TablaMesR(parent, sibling, 1);
        assertNotNull(tablaMesR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tablaMesR.getTbDia());

    }

    // property tbDia
    @Test
    void testGetterSetterTbDia() {

        assertNotNull(tablaMesR.getTbDia());

        int vint = 12;
        tablaMesR.setTbDia(vint); // int
        assertEquals(asInt(vint), asInt(tablaMesR.getTbDia()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        tablaMesR.setTbDia(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(tablaMesR.getTbDia()));
    }
}

