package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.tpar;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-PAR-REG.
 *
 */
class TParRegTest {

    private TParReg tParReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tParReg = new TParReg(1);
        tParReg.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TParReg tParReg2 = new TParReg(parent, 1);
        assertNotNull(tParReg2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TParReg tParReg3 = new TParReg(parent, sibling, 1);
        assertNotNull(tParReg3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tParReg.getTParTxt());

    }

    // property tParTxt
    @Test
    void testGetterSetterTParTxt() {

        assertNotNull(tParReg.getTParTxt());

        String vString = "ABCabc12";
        tParReg.setTParTxt(vString); // String
        assertEquals(asStr(vString), asStr(tParReg.getTParTxt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tParReg.setTParTxt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tParReg.getTParTxt()));
    }
}

