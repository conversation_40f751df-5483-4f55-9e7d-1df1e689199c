package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: SW-BANDERAS.
 *
 */
class SwBanderasTest {

    private SwBanderas swBanderas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        swBanderas = new SwBanderas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        swBanderas.setSwVsam("N");
        assertEquals("N", swBanderas.getSwVsam());

    }

    // property swVsam
    @Test
    void testGetterSetterSwVsam() {

        assertNotNull(swBanderas.getSwVsam());

        String vString = "A";
        swBanderas.setSwVsam(vString); // String
        assertEquals(asStr(vString), asStr(swBanderas.getSwVsam()));
        // Conditionals (88) - SW-VSAM
        swBanderas.setSwVsamNo();
        assertTrue(swBanderas.isSwVsamNo());
        swBanderas.setSwVsamSi();
        assertTrue(swBanderas.isSwVsamSi());

    }
}

