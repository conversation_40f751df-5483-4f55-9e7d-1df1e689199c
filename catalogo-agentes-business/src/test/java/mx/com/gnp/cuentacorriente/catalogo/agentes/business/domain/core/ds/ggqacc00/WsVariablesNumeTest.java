package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-VARIABLES-NUME.
 *
 */
class WsVariablesNumeTest {

    private WsVariablesNume wsVariablesNume;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsVariablesNume = new WsVariablesNume();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsVariablesNume.setWsDscDsgCve(0);
        assertEquals(0, wsVariablesNume.getWsDscDsgCve());

        wsVariablesNume.setWsCnaMteCve(0);
        assertEquals(0, wsVariablesNume.getWsCnaMteCve());

        wsVariablesNume.setWsCnaTfpCve(0);
        assertEquals(0, wsVariablesNume.getWsCnaTfpCve());

        wsVariablesNume.setWsAgtMteCve(0);
        assertEquals(0, wsVariablesNume.getWsAgtMteCve());

        assertNotNull(wsVariablesNume.getWsImpIvaRtn());

        assertNotNull(wsVariablesNume.getWsImpIvaAcr());

        assertNotNull(wsVariablesNume.getWsImpIsr());

        assertNotNull(wsVariablesNume.getWsImpIsrRtn());

        assertNotNull(wsVariablesNume.getWsTasa());

    }

    // property wsDscDsgCve
    @Test
    void testGetterSetterWsDscDsgCve() {

        assertNotNull(wsVariablesNume.getWsDscDsgCve());

        int vint = 1234;
        wsVariablesNume.setWsDscDsgCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsDscDsgCve()));
    }

    // property wsCnaMteCve
    @Test
    void testGetterSetterWsCnaMteCve() {

        assertNotNull(wsVariablesNume.getWsCnaMteCve());

        int vint = 1234;
        wsVariablesNume.setWsCnaMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsCnaMteCve()));
    }

    // property wsCnaTfpCve
    @Test
    void testGetterSetterWsCnaTfpCve() {

        assertNotNull(wsVariablesNume.getWsCnaTfpCve());

        int vint = 12;
        wsVariablesNume.setWsCnaTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsCnaTfpCve()));
        // Conditionals (88) - WS-CNA-TFP-CVE
        wsVariablesNume.setEmpleados();
        assertTrue(wsVariablesNume.isEmpleados());

    }

    // property wsAgtMteCve
    @Test
    void testGetterSetterWsAgtMteCve() {

        assertNotNull(wsVariablesNume.getWsAgtMteCve());

        int vint = 1234;
        wsVariablesNume.setWsAgtMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsAgtMteCve()));
    }

    // property wsImpIvaRtn
    @Test
    void testGetterSetterWsImpIvaRtn() {

        assertNotNull(wsVariablesNume.getWsImpIvaRtn());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariablesNume.setWsImpIvaRtn(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariablesNume.getWsImpIvaRtn().toBigDecimal());

        int vint = 123456789;
        wsVariablesNume.setWsImpIvaRtn(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsImpIvaRtn()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariablesNume.setWsImpIvaRtn(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariablesNume.getWsImpIvaRtn()));
    }

    // property wsImpIvaAcr
    @Test
    void testGetterSetterWsImpIvaAcr() {

        assertNotNull(wsVariablesNume.getWsImpIvaAcr());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariablesNume.setWsImpIvaAcr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariablesNume.getWsImpIvaAcr().toBigDecimal());

        int vint = 123456789;
        wsVariablesNume.setWsImpIvaAcr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsImpIvaAcr()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariablesNume.setWsImpIvaAcr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariablesNume.getWsImpIvaAcr()));
    }

    // property wsImpIsr
    @Test
    void testGetterSetterWsImpIsr() {

        assertNotNull(wsVariablesNume.getWsImpIsr());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariablesNume.setWsImpIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariablesNume.getWsImpIsr().toBigDecimal());

        int vint = 123456789;
        wsVariablesNume.setWsImpIsr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsImpIsr()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariablesNume.setWsImpIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariablesNume.getWsImpIsr()));
    }

    // property wsImpIsrRtn
    @Test
    void testGetterSetterWsImpIsrRtn() {

        assertNotNull(wsVariablesNume.getWsImpIsrRtn());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsVariablesNume.setWsImpIsrRtn(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariablesNume.getWsImpIsrRtn().toBigDecimal());

        int vint = 123456789;
        wsVariablesNume.setWsImpIsrRtn(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsImpIsrRtn()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsVariablesNume.setWsImpIsrRtn(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariablesNume.getWsImpIsrRtn()));
    }

    // property wsTasa
    @Test
    void testGetterSetterWsTasa() {

        assertNotNull(wsVariablesNume.getWsTasa());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsVariablesNume.setWsTasa(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsVariablesNume.getWsTasa().toBigDecimal());

        int vint = 123;
        wsVariablesNume.setWsTasa(vint); // int
        assertEquals(asInt(vint), asInt(wsVariablesNume.getWsTasa()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsVariablesNume.setWsTasa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsVariablesNume.getWsTasa()));
    }
}

