package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-ARCHCMAH.
 *
 */
class RegArchcmahTest {

    private RegArchcmah regArchcmah;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regArchcmah = new RegArchcmah();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegArchcmah regArchcmah2 = new RegArchcmah(parent);
        assertNotNull(regArchcmah2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 90);
        RegArchcmah regArchcmah3 = new RegArchcmah(parent, sibling);
        assertNotNull(regArchcmah3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regArchcmah.getCcmCve4());

        assertNotNull(regArchcmah.getAgtIdr4());

        assertNotNull(regArchcmah.getAgtFecCamRazSo4());

        assertNotNull(regArchcmah.getDmaIdr4());

        assertNotNull(regArchcmah.getCmaDes4());

        assertNotNull(regArchcmah.getEssRek4());

    }

    // property ccmCve4
    @Test
    void testGetterSetterCcmCve4() {

        assertNotNull(regArchcmah.getCcmCve4());

        int vint = 12345;
        regArchcmah.setCcmCve4(vint); // int
        assertEquals(asInt(vint), asInt(regArchcmah.getCcmCve4()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcmah.setCcmCve4(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcmah.getCcmCve4()));
    }

    // property agtIdr4
    @Test
    void testGetterSetterAgtIdr4() {

        assertNotNull(regArchcmah.getAgtIdr4());

        int vint = 123456789;
        regArchcmah.setAgtIdr4(vint); // int
        assertEquals(asInt(vint), asInt(regArchcmah.getAgtIdr4()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchcmah.setAgtIdr4(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcmah.getAgtIdr4()));
    }

    // property agtFecCamRazSo4
    @Test
    void testGetterSetterAgtFecCamRazSo4() {

        assertNotNull(regArchcmah.getAgtFecCamRazSo4());

        String vString = "ABCabc1230";
        regArchcmah.setAgtFecCamRazSo4(vString); // String
        assertEquals(asStr(vString), asStr(regArchcmah.getAgtFecCamRazSo4()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcmah.setAgtFecCamRazSo4(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcmah.getAgtFecCamRazSo4()));
    }

    // property dmaIdr4
    @Test
    void testGetterSetterDmaIdr4() {

        assertNotNull(regArchcmah.getDmaIdr4());

        int vint = 12345;
        regArchcmah.setDmaIdr4(vint); // int
        assertEquals(asInt(vint), asInt(regArchcmah.getDmaIdr4()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcmah.setDmaIdr4(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcmah.getDmaIdr4()));
    }

    // property cmaDes4
    @Test
    void testGetterSetterCmaDes4() {

        assertNotNull(regArchcmah.getCmaDes4());

        String vString = "ABCabc1230";
        regArchcmah.setCmaDes4(vString); // String
        assertEquals(asStr(vString), asStr(regArchcmah.getCmaDes4()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcmah.setCmaDes4(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcmah.getCmaDes4()));
    }

    // property essRek4
    @Test
    void testGetterSetterEssRek4() {

        assertNotNull(regArchcmah.getEssRek4());

        String vString = "A";
        regArchcmah.setEssRek4(vString); // String
        assertEquals(asStr(vString), asStr(regArchcmah.getEssRek4()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcmah.setEssRek4(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcmah.getEssRek4()));
    }
}

