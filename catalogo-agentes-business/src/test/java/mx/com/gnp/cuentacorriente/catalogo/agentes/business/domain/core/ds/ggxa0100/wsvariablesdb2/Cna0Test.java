package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: CNA0.
 *
 */
class Cna0Test {

    private Cna0 cna0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        cna0 = new Cna0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        cna0.setCnaCnaNum(0);
        assertEquals(0, cna0.getCnaCnaNum());

        cna0.setWsCnaCnaNum(0);
        assertEquals(0, cna0.getWsCnaCnaNum());

        cna0.setCnaGerZonEnv(0);
        assertEquals(0, cna0.getCnaGerZonEnv());

        cna0.setWsCnaGerZon(0);
        assertEquals(0, cna0.getWsCnaGerZon());

        cna0.setCnaOfnCve(0);
        assertEquals(0, cna0.getCnaOfnCve());

        cna0.setWsCnaOfnCve(0);
        assertEquals(0, cna0.getWsCnaOfnCve());

        cna0.setCnaEmpCve("");
        assertEquals("", cna0.getCnaEmpCve());

        cna0.setCnaTfpCve(0);
        assertEquals(0, cna0.getCnaTfpCve());

        cna0.setCeoCeoCve(0);
        assertEquals(0, cna0.getCeoCeoCve());

        cna0.setWsCeoCeoCve(0);
        assertEquals(0, cna0.getWsCeoCeoCve());

        cna0.setCeoCeoNom("");
        assertEquals("", cna0.getCeoCeoNom());

        cna0.setWsCeoCeoNom("");
        assertEquals("", cna0.getWsCeoCeoNom());

    }

    // property cnaCnaNum
    @Test
    void testGetterSetterCnaCnaNum() {

        assertNotNull(cna0.getCnaCnaNum());

        int vint = 1234;
        cna0.setCnaCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getCnaCnaNum()));
    }

    // property wsCnaCnaNum
    @Test
    void testGetterSetterWsCnaCnaNum() {

        assertNotNull(cna0.getWsCnaCnaNum());

        int vint = 12;
        cna0.setWsCnaCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getWsCnaCnaNum()));
    }

    // property cnaGerZonEnv
    @Test
    void testGetterSetterCnaGerZonEnv() {

        assertNotNull(cna0.getCnaGerZonEnv());

        int vint = 1234;
        cna0.setCnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getCnaGerZonEnv()));
    }

    // property wsCnaGerZon
    @Test
    void testGetterSetterWsCnaGerZon() {

        assertNotNull(cna0.getWsCnaGerZon());

        int vint = 1234;
        cna0.setWsCnaGerZon(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getWsCnaGerZon()));
    }

    // property cnaOfnCve
    @Test
    void testGetterSetterCnaOfnCve() {

        assertNotNull(cna0.getCnaOfnCve());

        int vint = 1234;
        cna0.setCnaOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getCnaOfnCve()));
    }

    // property wsCnaOfnCve
    @Test
    void testGetterSetterWsCnaOfnCve() {

        assertNotNull(cna0.getWsCnaOfnCve());

        int vint = 1234;
        cna0.setWsCnaOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getWsCnaOfnCve()));
    }

    // property cnaEmpCve
    @Test
    void testGetterSetterCnaEmpCve() {

        assertNotNull(cna0.getCnaEmpCve());

        String vString = "ABCab";
        cna0.setCnaEmpCve(vString); // String
        assertEquals(asStr(vString), asStr(cna0.getCnaEmpCve()));
    }

    // property cnaTfpCve
    @Test
    void testGetterSetterCnaTfpCve() {

        assertNotNull(cna0.getCnaTfpCve());

        int vint = 1234;
        cna0.setCnaTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getCnaTfpCve()));
    }

    // property ceoCeoCve
    @Test
    void testGetterSetterCeoCeoCve() {

        assertNotNull(cna0.getCeoCeoCve());

        int vint = 1234;
        cna0.setCeoCeoCve(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getCeoCeoCve()));
    }

    // property wsCeoCeoCve
    @Test
    void testGetterSetterWsCeoCeoCve() {

        assertNotNull(cna0.getWsCeoCeoCve());

        int vint = 1234;
        cna0.setWsCeoCeoCve(vint); // int
        assertEquals(asInt(vint), asInt(cna0.getWsCeoCeoCve()));
    }

    // property ceoCeoNom
    @Test
    void testGetterSetterCeoCeoNom() {

        assertNotNull(cna0.getCeoCeoNom());

        String vString = "ABCabc1230";
        cna0.setCeoCeoNom(vString); // String
        assertEquals(asStr(vString), asStr(cna0.getCeoCeoNom()));
    }

    // property wsCeoCeoNom
    @Test
    void testGetterSetterWsCeoCeoNom() {

        assertNotNull(cna0.getWsCeoCeoNom());

        String vString = "ABCabc1230";
        cna0.setWsCeoCeoNom(vString); // String
        assertEquals(asStr(vString), asStr(cna0.getWsCeoCeoNom()));
    }
}

