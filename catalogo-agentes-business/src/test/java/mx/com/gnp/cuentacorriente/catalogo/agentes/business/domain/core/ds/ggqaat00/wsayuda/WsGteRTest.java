package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GTE-R.
 *
 */
class WsGteRTest {

    private WsGteR wsGteR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGteR = new WsGteR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsGteR wsGteR2 = new WsGteR(parent);
        assertNotNull(wsGteR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 10);
        WsGteR wsGteR3 = new WsGteR(parent, sibling);
        assertNotNull(wsGteR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsGteR.getWsGteX());

        assertNotNull(wsGteR.getWsGteN());

    }

    // property wsGteX
    @Test
    void testGetterSetterWsGteX() {

        assertNotNull(wsGteR.getWsGteX());

        String vString = "ABC";
        wsGteR.setWsGteX(vString); // String
        assertEquals(asStr(vString), asStr(wsGteR.getWsGteX()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsGteR.setWsGteX(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsGteR.getWsGteX()));
    }

    // property wsGteN
    @Test
    void testGetterSetterWsGteN() {

        assertNotNull(wsGteR.getWsGteN());

        int vint = 1234567;
        wsGteR.setWsGteN(vint); // int
        assertEquals(asInt(vint), asInt(wsGteR.getWsGteN()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsGteR.setWsGteN(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGteR.getWsGteN()));
    }
}

