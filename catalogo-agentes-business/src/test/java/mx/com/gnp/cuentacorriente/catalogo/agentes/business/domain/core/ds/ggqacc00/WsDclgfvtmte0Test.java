package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGFVTMTE0.
 *
 */
class WsDclgfvtmte0Test {

    private WsDclgfvtmte0 wsDclgfvtmte0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgfvtmte0 = new WsDclgfvtmte0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgfvtmte0.setMteMteCve(0);
        assertEquals(0, wsDclgfvtmte0.getMteMteCve());

        wsDclgfvtmte0.setMteMteDes("");
        assertEquals("", wsDclgfvtmte0.getMteMteDes());

        wsDclgfvtmte0.setMteEsaCve(0);
        assertEquals(0, wsDclgfvtmte0.getMteEsaCve());

        wsDclgfvtmte0.setMteFecUltAct("");
        assertEquals("", wsDclgfvtmte0.getMteFecUltAct());

        wsDclgfvtmte0.setMteUsuCveAct("");
        assertEquals("", wsDclgfvtmte0.getMteUsuCveAct());

    }

    // property mteMteCve
    @Test
    void testGetterSetterMteMteCve() {

        assertNotNull(wsDclgfvtmte0.getMteMteCve());

        int vint = 1234;
        wsDclgfvtmte0.setMteMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtmte0.getMteMteCve()));
    }

    // property mteMteDes
    @Test
    void testGetterSetterMteMteDes() {

        assertNotNull(wsDclgfvtmte0.getMteMteDes());

        String vString = "ABCabc1230";
        wsDclgfvtmte0.setMteMteDes(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtmte0.getMteMteDes()));
    }

    // property mteEsaCve
    @Test
    void testGetterSetterMteEsaCve() {

        assertNotNull(wsDclgfvtmte0.getMteEsaCve());

        int vint = 1234;
        wsDclgfvtmte0.setMteEsaCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtmte0.getMteEsaCve()));
    }

    // property mteFecUltAct
    @Test
    void testGetterSetterMteFecUltAct() {

        assertNotNull(wsDclgfvtmte0.getMteFecUltAct());

        String vString = "ABCabc1230";
        wsDclgfvtmte0.setMteFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtmte0.getMteFecUltAct()));
    }

    // property mteUsuCveAct
    @Test
    void testGetterSetterMteUsuCveAct() {

        assertNotNull(wsDclgfvtmte0.getMteUsuCveAct());

        String vString = "ABCabc12";
        wsDclgfvtmte0.setMteUsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgfvtmte0.getMteUsuCveAct()));
    }
}

