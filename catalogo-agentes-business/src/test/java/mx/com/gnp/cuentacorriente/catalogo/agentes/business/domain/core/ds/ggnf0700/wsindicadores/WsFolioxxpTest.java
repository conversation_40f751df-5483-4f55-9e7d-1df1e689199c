package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FOLIOXXP.
 *
 */
class WsFolioxxpTest {

    private WsFolioxxp wsFolioxxp;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFolioxxp = new WsFolioxxp();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFolioxxp wsFolioxxp2 = new WsFolioxxp(parent);
        assertNotNull(wsFolioxxp2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFolioxxp wsFolioxxp3 = new WsFolioxxp(parent, sibling);
        assertNotNull(wsFolioxxp3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFolioxxp.getWsFolioxx1p());

        assertNotNull(wsFolioxxp.getWsFolioxx2p());

    }

    // property wsFolioxx1p
    @Test
    void testGetterSetterWsFolioxx1p() {

        assertNotNull(wsFolioxxp.getWsFolioxx1p());

        String vString = "A";
        wsFolioxxp.setWsFolioxx1p(vString); // String
        assertEquals(asStr(vString), asStr(wsFolioxxp.getWsFolioxx1p()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFolioxxp.setWsFolioxx1p(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFolioxxp.getWsFolioxx1p()));
    }

    // property wsFolioxx2p
    @Test
    void testGetterSetterWsFolioxx2p() {

        assertNotNull(wsFolioxxp.getWsFolioxx2p());

        int vint = 1234567;
        wsFolioxxp.setWsFolioxx2p(vint); // int
        assertEquals(asInt(vint), asInt(wsFolioxxp.getWsFolioxx2p()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsFolioxxp.setWsFolioxx2p(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFolioxxp.getWsFolioxx2p()));
    }
}

