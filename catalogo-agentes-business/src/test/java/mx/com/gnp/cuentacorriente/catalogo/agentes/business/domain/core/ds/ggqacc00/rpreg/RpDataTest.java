package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-DATA.
 *
 */
class RpDataTest {

    private RpData rpData;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpData = new RpData();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        rpData.setRpDsgCve(0);
        assertEquals(0, rpData.getRpDsgCve());

        rpData.setRpFecPrxFac("");
        assertEquals("", rpData.getRpFecPrxFac());

        rpData.setRpEsaCve(0);
        assertEquals(0, rpData.getRpEsaCve());

        rpData.setRpEssPrx("");
        assertEquals("", rpData.getRpEssPrx());

        rpData.setRpCf5CfbCveDseCmp("");
        assertEquals("", rpData.getRpCf5CfbCveDseCmp());

        assertNotNull(rpData.getRpCf5ImpCtoDse());

        assertNotNull(rpData.getRpCf5PjeCtoDse());

    }

    // property rpDsgCve
    @Test
    void testGetterSetterRpDsgCve() {

        assertNotNull(rpData.getRpDsgCve());

        int vint = 1234;
        rpData.setRpDsgCve(vint); // int
        assertEquals(asInt(vint), asInt(rpData.getRpDsgCve()));
    }

    // property rpFecPrxFac
    @Test
    void testGetterSetterRpFecPrxFac() {

        assertNotNull(rpData.getRpFecPrxFac());

        String vString = "ABCabc1230";
        rpData.setRpFecPrxFac(vString); // String
        assertEquals(asStr(vString), asStr(rpData.getRpFecPrxFac()));
    }

    // property rpEsaCve
    @Test
    void testGetterSetterRpEsaCve() {

        assertNotNull(rpData.getRpEsaCve());

        int vint = 1234;
        rpData.setRpEsaCve(vint); // int
        assertEquals(asInt(vint), asInt(rpData.getRpEsaCve()));
    }

    // property rpEssPrx
    @Test
    void testGetterSetterRpEssPrx() {

        assertNotNull(rpData.getRpEssPrx());

        String vString = "A";
        rpData.setRpEssPrx(vString); // String
        assertEquals(asStr(vString), asStr(rpData.getRpEssPrx()));
    }

    // property rpCf5CfbCveDseCmp
    @Test
    void testGetterSetterRpCf5CfbCveDseCmp() {

        assertNotNull(rpData.getRpCf5CfbCveDseCmp());

        String vString = "ABCa";
        rpData.setRpCf5CfbCveDseCmp(vString); // String
        assertEquals(asStr(vString), asStr(rpData.getRpCf5CfbCveDseCmp()));
    }

    // property rpCf5ImpCtoDse
    @Test
    void testGetterSetterRpCf5ImpCtoDse() {

        assertNotNull(rpData.getRpCf5ImpCtoDse());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpData.setRpCf5ImpCtoDse(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpData.getRpCf5ImpCtoDse().toBigDecimal());

        int vint = 123456789;
        rpData.setRpCf5ImpCtoDse(vint); // int
        assertEquals(asInt(vint), asInt(rpData.getRpCf5ImpCtoDse()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpData.setRpCf5ImpCtoDse(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpData.getRpCf5ImpCtoDse()));
    }

    // property rpCf5PjeCtoDse
    @Test
    void testGetterSetterRpCf5PjeCtoDse() {

        assertNotNull(rpData.getRpCf5PjeCtoDse());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        rpData.setRpCf5PjeCtoDse(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpData.getRpCf5PjeCtoDse().toBigDecimal());

        int vint = 123;
        rpData.setRpCf5PjeCtoDse(vint); // int
        assertEquals(asInt(vint), asInt(rpData.getRpCf5PjeCtoDse()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        rpData.setRpCf5PjeCtoDse(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpData.getRpCf5PjeCtoDse()));
    }
}

