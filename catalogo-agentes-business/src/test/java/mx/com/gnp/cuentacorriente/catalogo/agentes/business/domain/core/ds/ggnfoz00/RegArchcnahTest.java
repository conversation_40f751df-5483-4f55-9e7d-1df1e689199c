package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-ARCHCNAH.
 *
 */
class RegArchcnahTest {

    private RegArchcnah regArchcnah;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regArchcnah = new RegArchcnah();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegArchcnah regArchcnah2 = new RegArchcnah(parent);
        assertNotNull(regArchcnah2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 264);
        RegArchcnah regArchcnah3 = new RegArchcnah(parent, sibling);
        assertNotNull(regArchcnah3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regArchcnah.getAgtIdr2());

        assertNotNull(regArchcnah.getAgtFecCamRazSo2());

        assertNotNull(regArchcnah.getEmpCve2());

        assertNotNull(regArchcnah.getCnaNum2());

        assertNotNull(regArchcnah.getCnaTpoCnr2());

        assertNotNull(regArchcnah.getCnaUltCia2());

        assertNotNull(regArchcnah.getCnaFecCnx2());

        assertNotNull(regArchcnah.getCnaCodIpu2());

        assertNotNull(regArchcnah.getFiller01());

        assertNotNull(regArchcnah.getCnaPjeIva23());

        assertNotNull(regArchcnah.getFiller02());

        assertNotNull(regArchcnah.getCnaPjeIva22());

        assertNotNull(regArchcnah.getFiller03());

        assertNotNull(regArchcnah.getCnaPjeIsr23());

        assertNotNull(regArchcnah.getFiller04());

        assertNotNull(regArchcnah.getCnaPjeIsr22());

        assertNotNull(regArchcnah.getCnaComNiv2());

        assertNotNull(regArchcnah.getFiller05());

        assertNotNull(regArchcnah.getCnaImpComNiv211());

        assertNotNull(regArchcnah.getFiller06());

        assertNotNull(regArchcnah.getCnaImpComNiv22());

        assertNotNull(regArchcnah.getCnaNumFza2());

        assertNotNull(regArchcnah.getCnaFecIniFza2());

        assertNotNull(regArchcnah.getCnaFecFinFza2());

        assertNotNull(regArchcnah.getFiller07());

        assertNotNull(regArchcnah.getCnaImpFza211());

        assertNotNull(regArchcnah.getFiller08());

        assertNotNull(regArchcnah.getCnaImpFza22());

        assertNotNull(regArchcnah.getCnaIdrFav2());

        assertNotNull(regArchcnah.getFiller09());

        assertNotNull(regArchcnah.getCnaPjeFav23());

        assertNotNull(regArchcnah.getFiller10());

        assertNotNull(regArchcnah.getCnaPjeFav24());

        assertNotNull(regArchcnah.getPpgCve2());

        assertNotNull(regArchcnah.getCpgCve2());

        assertNotNull(regArchcnah.getCdeCve2());

        assertNotNull(regArchcnah.getOfnCve2());

        assertNotNull(regArchcnah.getTaaCve2());

        assertNotNull(regArchcnah.getGnaCveAgt2());

        assertNotNull(regArchcnah.getGnaCve2());

        assertNotNull(regArchcnah.getTfpCve2());

        assertNotNull(regArchcnah.getAgtIdrRcl2());

        assertNotNull(regArchcnah.getCtbNumCta2());

        assertNotNull(regArchcnah.getMteCve2());

        assertNotNull(regArchcnah.getMdeCve2());

        assertNotNull(regArchcnah.getZecCve2());

        assertNotNull(regArchcnah.getCnaCodPag2());

        assertNotNull(regArchcnah.getCnaFecMotEss2());

        assertNotNull(regArchcnah.getCnaGerZonEnv2());

        assertNotNull(regArchcnah.getCnaGerZonDcm2());

        assertNotNull(regArchcnah.getCnaIndGer2());

        assertNotNull(regArchcnah.getCnaFecAsgGer2());

        assertNotNull(regArchcnah.getEssRek2());

    }

    // property agtIdr2
    @Test
    void testGetterSetterAgtIdr2() {

        assertNotNull(regArchcnah.getAgtIdr2());

        int vint = 123456789;
        regArchcnah.setAgtIdr2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getAgtIdr2()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchcnah.setAgtIdr2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getAgtIdr2()));
    }

    // property agtFecCamRazSo2
    @Test
    void testGetterSetterAgtFecCamRazSo2() {

        assertNotNull(regArchcnah.getAgtFecCamRazSo2());

        String vString = "ABCabc1230";
        regArchcnah.setAgtFecCamRazSo2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getAgtFecCamRazSo2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setAgtFecCamRazSo2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getAgtFecCamRazSo2()));
    }

    // property empCve2
    @Test
    void testGetterSetterEmpCve2() {

        assertNotNull(regArchcnah.getEmpCve2());

        String vString = "ABCab";
        regArchcnah.setEmpCve2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getEmpCve2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regArchcnah.setEmpCve2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getEmpCve2()));
    }

    // property cnaNum2
    @Test
    void testGetterSetterCnaNum2() {

        assertNotNull(regArchcnah.getCnaNum2());

        int vint = 12345;
        regArchcnah.setCnaNum2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaNum2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setCnaNum2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaNum2()));
    }

    // property cnaTpoCnr2
    @Test
    void testGetterSetterCnaTpoCnr2() {

        assertNotNull(regArchcnah.getCnaTpoCnr2());

        String vString = "AB";
        regArchcnah.setCnaTpoCnr2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaTpoCnr2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regArchcnah.setCnaTpoCnr2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaTpoCnr2()));
    }

    // property cnaUltCia2
    @Test
    void testGetterSetterCnaUltCia2() {

        assertNotNull(regArchcnah.getCnaUltCia2());

        String vString = "ABCabc1230";
        regArchcnah.setCnaUltCia2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaUltCia2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCnaUltCia2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaUltCia2()));
    }

    // property cnaFecCnx2
    @Test
    void testGetterSetterCnaFecCnx2() {

        assertNotNull(regArchcnah.getCnaFecCnx2());

        String vString = "ABCabc1230";
        regArchcnah.setCnaFecCnx2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaFecCnx2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCnaFecCnx2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaFecCnx2()));
    }

    // property cnaCodIpu2
    @Test
    void testGetterSetterCnaCodIpu2() {

        assertNotNull(regArchcnah.getCnaCodIpu2());

        String vString = "A";
        regArchcnah.setCnaCodIpu2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaCodIpu2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setCnaCodIpu2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaCodIpu2()));
    }

    // property filler01
    @Test
    void testGetterSetterFiller01() {

        assertNotNull(regArchcnah.getFiller01());

        String vString = "A";
        regArchcnah.setFiller01(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller01()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller01(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller01()));
    }

    // property cnaPjeIva23
    @Test
    void testGetterSetterCnaPjeIva23() {

        assertNotNull(regArchcnah.getCnaPjeIva23());

        int vint = 123;
        regArchcnah.setCnaPjeIva23(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaPjeIva23()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regArchcnah.setCnaPjeIva23(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaPjeIva23()));
    }

    // property filler02
    @Test
    void testGetterSetterFiller02() {

        assertNotNull(regArchcnah.getFiller02());

        String vString = "A";
        regArchcnah.setFiller02(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller02()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller02(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller02()));
    }

    // property cnaPjeIva22
    @Test
    void testGetterSetterCnaPjeIva22() {

        assertNotNull(regArchcnah.getCnaPjeIva22());

        int vint = 12;
        regArchcnah.setCnaPjeIva22(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaPjeIva22()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regArchcnah.setCnaPjeIva22(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaPjeIva22()));
    }

    // property filler03
    @Test
    void testGetterSetterFiller03() {

        assertNotNull(regArchcnah.getFiller03());

        String vString = "A";
        regArchcnah.setFiller03(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller03()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller03(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller03()));
    }

    // property cnaPjeIsr23
    @Test
    void testGetterSetterCnaPjeIsr23() {

        assertNotNull(regArchcnah.getCnaPjeIsr23());

        int vint = 123;
        regArchcnah.setCnaPjeIsr23(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaPjeIsr23()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regArchcnah.setCnaPjeIsr23(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaPjeIsr23()));
    }

    // property filler04
    @Test
    void testGetterSetterFiller04() {

        assertNotNull(regArchcnah.getFiller04());

        String vString = "A";
        regArchcnah.setFiller04(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller04()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller04(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller04()));
    }

    // property cnaPjeIsr22
    @Test
    void testGetterSetterCnaPjeIsr22() {

        assertNotNull(regArchcnah.getCnaPjeIsr22());

        int vint = 12;
        regArchcnah.setCnaPjeIsr22(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaPjeIsr22()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regArchcnah.setCnaPjeIsr22(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaPjeIsr22()));
    }

    // property cnaComNiv2
    @Test
    void testGetterSetterCnaComNiv2() {

        assertNotNull(regArchcnah.getCnaComNiv2());

        String vString = "A";
        regArchcnah.setCnaComNiv2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaComNiv2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setCnaComNiv2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaComNiv2()));
    }

    // property filler05
    @Test
    void testGetterSetterFiller05() {

        assertNotNull(regArchcnah.getFiller05());

        String vString = "A";
        regArchcnah.setFiller05(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller05()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller05(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller05()));
    }

    // property cnaImpComNiv211
    @Test
    void testGetterSetterCnaImpComNiv211() {

        assertNotNull(regArchcnah.getCnaImpComNiv211());

        BigDecimal vBigDecimal = new BigDecimal("12345678901");
        regArchcnah.setCnaImpComNiv211(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, regArchcnah.getCnaImpComNiv211().toBigDecimal());

        int vint = 123456789;
        regArchcnah.setCnaImpComNiv211(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaImpComNiv211()));

        NumericVar vINumericValue = new NumericVar(11, 0);;
        vINumericValue.setValue("12345678901");
        regArchcnah.setCnaImpComNiv211(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaImpComNiv211()));
    }

    // property filler06
    @Test
    void testGetterSetterFiller06() {

        assertNotNull(regArchcnah.getFiller06());

        String vString = "A";
        regArchcnah.setFiller06(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller06()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller06(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller06()));
    }

    // property cnaImpComNiv22
    @Test
    void testGetterSetterCnaImpComNiv22() {

        assertNotNull(regArchcnah.getCnaImpComNiv22());

        int vint = 12;
        regArchcnah.setCnaImpComNiv22(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaImpComNiv22()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regArchcnah.setCnaImpComNiv22(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaImpComNiv22()));
    }

    // property cnaNumFza2
    @Test
    void testGetterSetterCnaNumFza2() {

        assertNotNull(regArchcnah.getCnaNumFza2());

        String vString = "ABCabc1230";
        regArchcnah.setCnaNumFza2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaNumFza2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCnaNumFza2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaNumFza2()));
    }

    // property cnaFecIniFza2
    @Test
    void testGetterSetterCnaFecIniFza2() {

        assertNotNull(regArchcnah.getCnaFecIniFza2());

        String vString = "ABCabc1230";
        regArchcnah.setCnaFecIniFza2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaFecIniFza2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCnaFecIniFza2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaFecIniFza2()));
    }

    // property cnaFecFinFza2
    @Test
    void testGetterSetterCnaFecFinFza2() {

        assertNotNull(regArchcnah.getCnaFecFinFza2());

        String vString = "ABCabc1230";
        regArchcnah.setCnaFecFinFza2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaFecFinFza2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCnaFecFinFza2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaFecFinFza2()));
    }

    // property filler07
    @Test
    void testGetterSetterFiller07() {

        assertNotNull(regArchcnah.getFiller07());

        String vString = "A";
        regArchcnah.setFiller07(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller07()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller07(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller07()));
    }

    // property cnaImpFza211
    @Test
    void testGetterSetterCnaImpFza211() {

        assertNotNull(regArchcnah.getCnaImpFza211());

        BigDecimal vBigDecimal = new BigDecimal("12345678901");
        regArchcnah.setCnaImpFza211(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, regArchcnah.getCnaImpFza211().toBigDecimal());

        int vint = 123456789;
        regArchcnah.setCnaImpFza211(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaImpFza211()));

        NumericVar vINumericValue = new NumericVar(11, 0);;
        vINumericValue.setValue("12345678901");
        regArchcnah.setCnaImpFza211(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaImpFza211()));
    }

    // property filler08
    @Test
    void testGetterSetterFiller08() {

        assertNotNull(regArchcnah.getFiller08());

        String vString = "A";
        regArchcnah.setFiller08(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller08()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller08(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller08()));
    }

    // property cnaImpFza22
    @Test
    void testGetterSetterCnaImpFza22() {

        assertNotNull(regArchcnah.getCnaImpFza22());

        int vint = 12;
        regArchcnah.setCnaImpFza22(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaImpFza22()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regArchcnah.setCnaImpFza22(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaImpFza22()));
    }

    // property cnaIdrFav2
    @Test
    void testGetterSetterCnaIdrFav2() {

        assertNotNull(regArchcnah.getCnaIdrFav2());

        String vString = "A";
        regArchcnah.setCnaIdrFav2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaIdrFav2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setCnaIdrFav2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaIdrFav2()));
    }

    // property filler09
    @Test
    void testGetterSetterFiller09() {

        assertNotNull(regArchcnah.getFiller09());

        String vString = "A";
        regArchcnah.setFiller09(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller09()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller09(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller09()));
    }

    // property cnaPjeFav23
    @Test
    void testGetterSetterCnaPjeFav23() {

        assertNotNull(regArchcnah.getCnaPjeFav23());

        int vint = 123;
        regArchcnah.setCnaPjeFav23(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaPjeFav23()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regArchcnah.setCnaPjeFav23(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaPjeFav23()));
    }

    // property filler10
    @Test
    void testGetterSetterFiller10() {

        assertNotNull(regArchcnah.getFiller10());

        String vString = "A";
        regArchcnah.setFiller10(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getFiller10()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setFiller10(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getFiller10()));
    }

    // property cnaPjeFav24
    @Test
    void testGetterSetterCnaPjeFav24() {

        assertNotNull(regArchcnah.getCnaPjeFav24());

        int vint = 1234;
        regArchcnah.setCnaPjeFav24(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaPjeFav24()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        regArchcnah.setCnaPjeFav24(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaPjeFav24()));
    }

    // property ppgCve2
    @Test
    void testGetterSetterPpgCve2() {

        assertNotNull(regArchcnah.getPpgCve2());

        int vint = 12345;
        regArchcnah.setPpgCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getPpgCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setPpgCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getPpgCve2()));
    }

    // property cpgCve2
    @Test
    void testGetterSetterCpgCve2() {

        assertNotNull(regArchcnah.getCpgCve2());

        int vint = 12345;
        regArchcnah.setCpgCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCpgCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setCpgCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCpgCve2()));
    }

    // property cdeCve2
    @Test
    void testGetterSetterCdeCve2() {

        assertNotNull(regArchcnah.getCdeCve2());

        int vint = 12345;
        regArchcnah.setCdeCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCdeCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setCdeCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCdeCve2()));
    }

    // property ofnCve2
    @Test
    void testGetterSetterOfnCve2() {

        assertNotNull(regArchcnah.getOfnCve2());

        int vint = 12345;
        regArchcnah.setOfnCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getOfnCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setOfnCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getOfnCve2()));
    }

    // property taaCve2
    @Test
    void testGetterSetterTaaCve2() {

        assertNotNull(regArchcnah.getTaaCve2());

        String vString = "A";
        regArchcnah.setTaaCve2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getTaaCve2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setTaaCve2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getTaaCve2()));
    }

    // property gnaCveAgt2
    @Test
    void testGetterSetterGnaCveAgt2() {

        assertNotNull(regArchcnah.getGnaCveAgt2());

        String vString = "A";
        regArchcnah.setGnaCveAgt2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getGnaCveAgt2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setGnaCveAgt2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getGnaCveAgt2()));
    }

    // property gnaCve2
    @Test
    void testGetterSetterGnaCve2() {

        assertNotNull(regArchcnah.getGnaCve2());

        int vint = 12345;
        regArchcnah.setGnaCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getGnaCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setGnaCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getGnaCve2()));
    }

    // property tfpCve2
    @Test
    void testGetterSetterTfpCve2() {

        assertNotNull(regArchcnah.getTfpCve2());

        int vint = 12345;
        regArchcnah.setTfpCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getTfpCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setTfpCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getTfpCve2()));
    }

    // property agtIdrRcl2
    @Test
    void testGetterSetterAgtIdrRcl2() {

        assertNotNull(regArchcnah.getAgtIdrRcl2());

        int vint = 123456789;
        regArchcnah.setAgtIdrRcl2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getAgtIdrRcl2()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchcnah.setAgtIdrRcl2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getAgtIdrRcl2()));
    }

    // property ctbNumCta2
    @Test
    void testGetterSetterCtbNumCta2() {

        assertNotNull(regArchcnah.getCtbNumCta2());

        String vString = "ABCabc1230";
        regArchcnah.setCtbNumCta2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCtbNumCta2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCtbNumCta2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCtbNumCta2()));
    }

    // property mteCve2
    @Test
    void testGetterSetterMteCve2() {

        assertNotNull(regArchcnah.getMteCve2());

        int vint = 12345;
        regArchcnah.setMteCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getMteCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setMteCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getMteCve2()));
    }

    // property mdeCve2
    @Test
    void testGetterSetterMdeCve2() {

        assertNotNull(regArchcnah.getMdeCve2());

        int vint = 12345;
        regArchcnah.setMdeCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getMdeCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setMdeCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getMdeCve2()));
    }

    // property zecCve2
    @Test
    void testGetterSetterZecCve2() {

        assertNotNull(regArchcnah.getZecCve2());

        int vint = 12345;
        regArchcnah.setZecCve2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getZecCve2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setZecCve2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getZecCve2()));
    }

    // property cnaCodPag2
    @Test
    void testGetterSetterCnaCodPag2() {

        assertNotNull(regArchcnah.getCnaCodPag2());

        String vString = "A";
        regArchcnah.setCnaCodPag2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaCodPag2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setCnaCodPag2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaCodPag2()));
    }

    // property cnaFecMotEss2
    @Test
    void testGetterSetterCnaFecMotEss2() {

        assertNotNull(regArchcnah.getCnaFecMotEss2());

        String vString = "ABCabc1230";
        regArchcnah.setCnaFecMotEss2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaFecMotEss2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCnaFecMotEss2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaFecMotEss2()));
    }

    // property cnaGerZonEnv2
    @Test
    void testGetterSetterCnaGerZonEnv2() {

        assertNotNull(regArchcnah.getCnaGerZonEnv2());

        int vint = 12345;
        regArchcnah.setCnaGerZonEnv2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaGerZonEnv2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setCnaGerZonEnv2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaGerZonEnv2()));
    }

    // property cnaGerZonDcm2
    @Test
    void testGetterSetterCnaGerZonDcm2() {

        assertNotNull(regArchcnah.getCnaGerZonDcm2());

        int vint = 12345;
        regArchcnah.setCnaGerZonDcm2(vint); // int
        assertEquals(asInt(vint), asInt(regArchcnah.getCnaGerZonDcm2()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchcnah.setCnaGerZonDcm2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchcnah.getCnaGerZonDcm2()));
    }

    // property cnaIndGer2
    @Test
    void testGetterSetterCnaIndGer2() {

        assertNotNull(regArchcnah.getCnaIndGer2());

        String vString = "A";
        regArchcnah.setCnaIndGer2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaIndGer2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setCnaIndGer2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaIndGer2()));
    }

    // property cnaFecAsgGer2
    @Test
    void testGetterSetterCnaFecAsgGer2() {

        assertNotNull(regArchcnah.getCnaFecAsgGer2());

        String vString = "ABCabc1230";
        regArchcnah.setCnaFecAsgGer2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getCnaFecAsgGer2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchcnah.setCnaFecAsgGer2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getCnaFecAsgGer2()));
    }

    // property essRek2
    @Test
    void testGetterSetterEssRek2() {

        assertNotNull(regArchcnah.getEssRek2());

        String vString = "A";
        regArchcnah.setEssRek2(vString); // String
        assertEquals(asStr(vString), asStr(regArchcnah.getEssRek2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchcnah.setEssRek2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchcnah.getEssRek2()));
    }
}

