package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTDRC0-NULL.
 *
 */
class WsGfvtdrc0NullTest {

    private WsGfvtdrc0Null wsGfvtdrc0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtdrc0Null = new WsGfvtdrc0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtdrc0Null.setDrc01(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc01());

        wsGfvtdrc0Null.setDrc02(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc02());

        wsGfvtdrc0Null.setDrc03(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc03());

        wsGfvtdrc0Null.setDrc04(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc04());

        wsGfvtdrc0Null.setDrc05(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc05());

        wsGfvtdrc0Null.setDrc06(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc06());

        wsGfvtdrc0Null.setDrc07(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc07());

        wsGfvtdrc0Null.setDrc08(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc08());

        wsGfvtdrc0Null.setDrc09(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc09());

        wsGfvtdrc0Null.setDrc10(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc10());

        wsGfvtdrc0Null.setDrc11(0);
        assertEquals(0, wsGfvtdrc0Null.getDrc11());

    }

    // property drc01
    @Test
    void testGetterSetterDrc01() {

        assertNotNull(wsGfvtdrc0Null.getDrc01());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc01()));
    }

    // property drc02
    @Test
    void testGetterSetterDrc02() {

        assertNotNull(wsGfvtdrc0Null.getDrc02());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc02()));
    }

    // property drc03
    @Test
    void testGetterSetterDrc03() {

        assertNotNull(wsGfvtdrc0Null.getDrc03());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc03()));
    }

    // property drc04
    @Test
    void testGetterSetterDrc04() {

        assertNotNull(wsGfvtdrc0Null.getDrc04());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc04()));
    }

    // property drc05
    @Test
    void testGetterSetterDrc05() {

        assertNotNull(wsGfvtdrc0Null.getDrc05());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc05()));
    }

    // property drc06
    @Test
    void testGetterSetterDrc06() {

        assertNotNull(wsGfvtdrc0Null.getDrc06());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc06()));
    }

    // property drc07
    @Test
    void testGetterSetterDrc07() {

        assertNotNull(wsGfvtdrc0Null.getDrc07());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc07()));
    }

    // property drc08
    @Test
    void testGetterSetterDrc08() {

        assertNotNull(wsGfvtdrc0Null.getDrc08());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc08()));
    }

    // property drc09
    @Test
    void testGetterSetterDrc09() {

        assertNotNull(wsGfvtdrc0Null.getDrc09());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc09()));
    }

    // property drc10
    @Test
    void testGetterSetterDrc10() {

        assertNotNull(wsGfvtdrc0Null.getDrc10());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc10()));
    }

    // property drc11
    @Test
    void testGetterSetterDrc11() {

        assertNotNull(wsGfvtdrc0Null.getDrc11());

        int vint = 1234;
        wsGfvtdrc0Null.setDrc11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtdrc0Null.getDrc11()));
    }
}

