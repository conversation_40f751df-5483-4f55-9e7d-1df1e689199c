package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-CNA0-1.
 *
 */
class TCna0_1Test {

    private TCna0_1 tCna0_1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tCna0_1 = new TCna0_1(1);
        tCna0_1.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TCna0_1 tCna0_12 = new TCna0_1(parent, 1);
        assertNotNull(tCna0_12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TCna0_1 tCna0_13 = new TCna0_1(parent, sibling, 1);
        assertNotNull(tCna0_13);
    }

    @Test
    void testInitialize() {

    }

    // property tCna0Elem_1
    @Test
    void testGetterSetterTCna0Elem_1() {

        assertNotNull(tCna0_1.getTCna0Elem_1());
    }

    // property sCnaCnaNumElem
    @Test
    void testGetterSetterSCnaCnaNumElem() {

        // Property: tCna0Elem_1.sCnaCnaNumElem -> 20 S-CNA-CNA-NUM-ELEM X(01)
        assertNotNull(tCna0_1.getSCnaCnaNumElem());
        String vString = "A";
        tCna0_1.setSCnaCnaNumElem(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tCna0_1.setSCnaCnaNumElem(vIAlphanumericValue); // IAlphanumericValue
    }
}

