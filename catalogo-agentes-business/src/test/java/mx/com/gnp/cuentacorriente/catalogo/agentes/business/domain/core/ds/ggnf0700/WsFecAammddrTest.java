package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FEC-AAMMDDR.
 *
 */
class WsFecAammddrTest {

    private WsFecAammddr wsFecAammddr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFecAammddr = new WsFecAammddr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFecAammddr wsFecAammddr2 = new WsFecAammddr(parent);
        assertNotNull(wsFecAammddr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 6);
        WsFecAammddr wsFecAammddr3 = new WsFecAammddr(parent, sibling);
        assertNotNull(wsFecAammddr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFecAammddr.getWsAa());

        assertNotNull(wsFecAammddr.getWsMm());

        assertNotNull(wsFecAammddr.getWsDd());

    }

    // property wsAa
    @Test
    void testGetterSetterWsAa() {

        assertNotNull(wsFecAammddr.getWsAa());

        int vint = 12;
        wsFecAammddr.setWsAa(vint); // int
        assertEquals(asInt(vint), asInt(wsFecAammddr.getWsAa()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFecAammddr.setWsAa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFecAammddr.getWsAa()));
    }

    // property wsMm
    @Test
    void testGetterSetterWsMm() {

        assertNotNull(wsFecAammddr.getWsMm());

        int vint = 12;
        wsFecAammddr.setWsMm(vint); // int
        assertEquals(asInt(vint), asInt(wsFecAammddr.getWsMm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFecAammddr.setWsMm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFecAammddr.getWsMm()));
    }

    // property wsDd
    @Test
    void testGetterSetterWsDd() {

        assertNotNull(wsFecAammddr.getWsDd());

        int vint = 12;
        wsFecAammddr.setWsDd(vint); // int
        assertEquals(asInt(vint), asInt(wsFecAammddr.getWsDd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFecAammddr.setWsDd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFecAammddr.getWsDd()));
    }
}

