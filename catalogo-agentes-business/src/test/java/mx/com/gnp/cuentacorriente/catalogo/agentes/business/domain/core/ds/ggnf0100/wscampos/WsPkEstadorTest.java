package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-PK-ESTADOR.
 *
 */
class WsPkEstadorTest {

    private WsPkEstador wsPkEstador;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsPkEstador = new WsPkEstador();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsPkEstador wsPkEstador2 = new WsPkEstador(parent);
        assertNotNull(wsPkEstador2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 4);
        WsPkEstador wsPkEstador3 = new WsPkEstador(parent, sibling);
        assertNotNull(wsPkEstador3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsPkEstador.getWsPkEstado1());

        assertNotNull(wsPkEstador.getWsPkEstado2());

    }

    // property wsPkEstado1
    @Test
    void testGetterSetterWsPkEstado1() {

        assertNotNull(wsPkEstador.getWsPkEstado1());

        int vint = 12;
        wsPkEstador.setWsPkEstado1(vint); // int
        assertEquals(asInt(vint), asInt(wsPkEstador.getWsPkEstado1()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsPkEstador.setWsPkEstado1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsPkEstador.getWsPkEstado1()));
    }

    // property wsPkEstado2
    @Test
    void testGetterSetterWsPkEstado2() {

        assertNotNull(wsPkEstador.getWsPkEstado2());

        String vString = "AB";
        wsPkEstador.setWsPkEstado2(vString); // String
        assertEquals(asStr(vString), asStr(wsPkEstador.getWsPkEstado2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsPkEstador.setWsPkEstado2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPkEstador.getWsPkEstado2()));
    }
}

