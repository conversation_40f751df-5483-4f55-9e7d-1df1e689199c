package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTEDO0-NULL.
 *
 */
class WsGfvtedo0NullTest {

    private WsGfvtedo0Null wsGfvtedo0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtedo0Null = new WsGfvtedo0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtedo0Null.setEdo01(0);
        assertEquals(0, wsGfvtedo0Null.getEdo01());

        wsGfvtedo0Null.setEdo02(0);
        assertEquals(0, wsGfvtedo0Null.getEdo02());

        wsGfvtedo0Null.setEdo03(0);
        assertEquals(0, wsGfvtedo0Null.getEdo03());

        wsGfvtedo0Null.setEdo04(0);
        assertEquals(0, wsGfvtedo0Null.getEdo04());

        wsGfvtedo0Null.setEdo05(0);
        assertEquals(0, wsGfvtedo0Null.getEdo05());

        wsGfvtedo0Null.setEdo06(0);
        assertEquals(0, wsGfvtedo0Null.getEdo06());

    }

    // property edo01
    @Test
    void testGetterSetterEdo01() {

        assertNotNull(wsGfvtedo0Null.getEdo01());

        int vint = 1234;
        wsGfvtedo0Null.setEdo01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtedo0Null.getEdo01()));
    }

    // property edo02
    @Test
    void testGetterSetterEdo02() {

        assertNotNull(wsGfvtedo0Null.getEdo02());

        int vint = 1234;
        wsGfvtedo0Null.setEdo02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtedo0Null.getEdo02()));
    }

    // property edo03
    @Test
    void testGetterSetterEdo03() {

        assertNotNull(wsGfvtedo0Null.getEdo03());

        int vint = 1234;
        wsGfvtedo0Null.setEdo03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtedo0Null.getEdo03()));
    }

    // property edo04
    @Test
    void testGetterSetterEdo04() {

        assertNotNull(wsGfvtedo0Null.getEdo04());

        int vint = 1234;
        wsGfvtedo0Null.setEdo04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtedo0Null.getEdo04()));
    }

    // property edo05
    @Test
    void testGetterSetterEdo05() {

        assertNotNull(wsGfvtedo0Null.getEdo05());

        int vint = 1234;
        wsGfvtedo0Null.setEdo05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtedo0Null.getEdo05()));
    }

    // property edo06
    @Test
    void testGetterSetterEdo06() {

        assertNotNull(wsGfvtedo0Null.getEdo06());

        int vint = 1234;
        wsGfvtedo0Null.setEdo06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtedo0Null.getEdo06()));
    }
}

