package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-CIFRAS-CONTROL.
 *
 */
class RegCifrasControlTest {

    private RegCifrasControl regCifrasControl;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regCifrasControl = new RegCifrasControl();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegCifrasControl regCifrasControl2 = new RegCifrasControl(parent);
        assertNotNull(regCifrasControl2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 45);
        RegCifrasControl regCifrasControl3 = new RegCifrasControl(parent, sibling);
        assertNotNull(regCifrasControl3);
    }

    @Test
    void testInitialize() {
        assertNotNull(regCifrasControl.getRegCcDescripcion());

        assertNotNull(regCifrasControl.getRegCcCantidad());

    }

    // property regCcDescripcion
    @Test
    void testGetterSetterRegCcDescripcion() {

        assertNotNull(regCifrasControl.getRegCcDescripcion());

        String vString = "ABCabc1230";
        regCifrasControl.setRegCcDescripcion(vString); // String
        assertEquals(asStr(vString), asStr(regCifrasControl.getRegCcDescripcion()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regCifrasControl.setRegCcDescripcion(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regCifrasControl.getRegCcDescripcion()));
    }

    // property regCcCantidad
    @Test
    void testGetterSetterRegCcCantidad() {

        assertNotNull(regCifrasControl.getRegCcCantidad());

        String vString = "ABCabc1230";
        regCifrasControl.setRegCcCantidad(vString); // String
        assertEquals(asStr(vString), asStr(regCifrasControl.getRegCcCantidad()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regCifrasControl.setRegCcCantidad(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regCifrasControl.getRegCcCantidad()));
    }
}

