package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-GER0-1.
 *
 */
class TGer0_1Test {

    private TGer0_1 tGer0_1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tGer0_1 = new TGer0_1(1);
        tGer0_1.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TGer0_1 tGer0_12 = new TGer0_1(parent, 1);
        assertNotNull(tGer0_12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TGer0_1 tGer0_13 = new TGer0_1(parent, sibling, 1);
        assertNotNull(tGer0_13);
    }

    @Test
    void testInitialize() {

    }

    // property tGer0Elem_1
    @Test
    void testGetterSetterTGer0Elem_1() {

        assertNotNull(tGer0_1.getTGer0Elem_1());
    }

    // property sCnaGerZonElem
    @Test
    void testGetterSetterSCnaGerZonElem() {

        // Property: tGer0Elem_1.sCnaGerZonElem -> 20 S-CNA-GER-ZON-ELEM X(01)
        assertNotNull(tGer0_1.getSCnaGerZonElem());
        String vString = "A";
        tGer0_1.setSCnaGerZonElem(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tGer0_1.setSCnaGerZonElem(vIAlphanumericValue); // IAlphanumericValue
    }
}

