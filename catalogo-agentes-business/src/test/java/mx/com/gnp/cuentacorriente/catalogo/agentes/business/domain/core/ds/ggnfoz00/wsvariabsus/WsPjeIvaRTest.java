package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-PJE-IVA-R.
 *
 */
class WsPjeIvaRTest {

    private WsPjeIvaR wsPjeIvaR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsPjeIvaR = new WsPjeIvaR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsPjeIvaR wsPjeIvaR2 = new WsPjeIvaR(parent);
        assertNotNull(wsPjeIvaR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 5);
        WsPjeIvaR wsPjeIvaR3 = new WsPjeIvaR(parent, sibling);
        assertNotNull(wsPjeIvaR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsPjeIvaR.getRsPjeIva3());

        assertNotNull(wsPjeIvaR.getRsPjeIva2());

    }

    // property rsPjeIva3
    @Test
    void testGetterSetterRsPjeIva3() {

        assertNotNull(wsPjeIvaR.getRsPjeIva3());

        String vString = "ABC";
        wsPjeIvaR.setRsPjeIva3(vString); // String
        assertEquals(asStr(vString), asStr(wsPjeIvaR.getRsPjeIva3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsPjeIvaR.setRsPjeIva3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPjeIvaR.getRsPjeIva3()));
    }

    // property rsPjeIva2
    @Test
    void testGetterSetterRsPjeIva2() {

        assertNotNull(wsPjeIvaR.getRsPjeIva2());

        String vString = "AB";
        wsPjeIvaR.setRsPjeIva2(vString); // String
        assertEquals(asStr(vString), asStr(wsPjeIvaR.getRsPjeIva2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsPjeIvaR.setRsPjeIva2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPjeIvaR.getRsPjeIva2()));
    }
}

