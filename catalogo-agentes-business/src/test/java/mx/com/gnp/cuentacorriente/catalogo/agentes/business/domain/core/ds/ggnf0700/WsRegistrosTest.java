package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-REGISTROS.
 *
 */
class WsRegistrosTest {

    private WsRegistros wsRegistros;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsRegistros = new WsRegistros();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsRegistros.setWsDclgfvtfla00("");
        assertEquals("", wsRegistros.getWsDclgfvtfla00());

        wsRegistros.setWsDclgfvtfla01("");
        assertEquals("", wsRegistros.getWsDclgfvtfla01());

        wsRegistros.setWsDclgfvtfla02("");
        assertEquals("", wsRegistros.getWsDclgfvtfla02());

        wsRegistros.setWsDclgfvtcna00("");
        assertEquals("", wsRegistros.getWsDclgfvtcna00());

        wsRegistros.setWsDclgfvtcna01("");
        assertEquals("", wsRegistros.getWsDclgfvtcna01());

        wsRegistros.setWsIndNal(0);
        assertEquals(0, wsRegistros.getWsIndNal());

        wsRegistros.setWsIndProv(0);
        assertEquals(0, wsRegistros.getWsIndProv());

    }

    // property wsDclgfvtfla00
    @Test
    void testGetterSetterWsDclgfvtfla00() {

        assertNotNull(wsRegistros.getWsDclgfvtfla00());

        String vString = "ABCabc1230";
        wsRegistros.setWsDclgfvtfla00(vString); // String
        assertEquals(asStr(vString), asStr(wsRegistros.getWsDclgfvtfla00()));
    }

    // property wsDclgfvtfla01
    @Test
    void testGetterSetterWsDclgfvtfla01() {

        assertNotNull(wsRegistros.getWsDclgfvtfla01());

        String vString = "ABCabc1230";
        wsRegistros.setWsDclgfvtfla01(vString); // String
        assertEquals(asStr(vString), asStr(wsRegistros.getWsDclgfvtfla01()));
    }

    // property wsDclgfvtfla02
    @Test
    void testGetterSetterWsDclgfvtfla02() {

        assertNotNull(wsRegistros.getWsDclgfvtfla02());

        String vString = "ABCabc1230";
        wsRegistros.setWsDclgfvtfla02(vString); // String
        assertEquals(asStr(vString), asStr(wsRegistros.getWsDclgfvtfla02()));
    }

    // property wsDclgfvtcna00
    @Test
    void testGetterSetterWsDclgfvtcna00() {

        assertNotNull(wsRegistros.getWsDclgfvtcna00());

        String vString = "ABCabc1230";
        wsRegistros.setWsDclgfvtcna00(vString); // String
        assertEquals(asStr(vString), asStr(wsRegistros.getWsDclgfvtcna00()));
    }

    // property wsDclgfvtcna01
    @Test
    void testGetterSetterWsDclgfvtcna01() {

        assertNotNull(wsRegistros.getWsDclgfvtcna01());

        String vString = "ABCabc1230";
        wsRegistros.setWsDclgfvtcna01(vString); // String
        assertEquals(asStr(vString), asStr(wsRegistros.getWsDclgfvtcna01()));
    }

    // property wsIndNal
    @Test
    void testGetterSetterWsIndNal() {

        assertNotNull(wsRegistros.getWsIndNal());

        int vint = 1;
        wsRegistros.setWsIndNal(vint); // int
        assertEquals(asInt(vint), asInt(wsRegistros.getWsIndNal()));
    }

    // property wsIndProv
    @Test
    void testGetterSetterWsIndProv() {

        assertNotNull(wsRegistros.getWsIndProv());

        int vint = 1;
        wsRegistros.setWsIndProv(vint); // int
        assertEquals(asInt(vint), asInt(wsRegistros.getWsIndProv()));
    }
}

