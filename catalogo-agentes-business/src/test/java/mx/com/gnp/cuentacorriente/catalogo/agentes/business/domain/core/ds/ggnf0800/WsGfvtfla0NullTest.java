package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTFLA0-NULL.
 *
 */
class WsGfvtfla0NullTest {

    private WsGfvtfla0Null wsGfvtfla0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtfla0Null = new WsGfvtfla0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtfla0Null.setFla01(0);
        assertEquals(0, wsGfvtfla0Null.getFla01());

        wsGfvtfla0Null.setFla02(0);
        assertEquals(0, wsGfvtfla0Null.getFla02());

        wsGfvtfla0Null.setFla03(0);
        assertEquals(0, wsGfvtfla0Null.getFla03());

        wsGfvtfla0Null.setFla04(0);
        assertEquals(0, wsGfvtfla0Null.getFla04());

        wsGfvtfla0Null.setFla05(0);
        assertEquals(0, wsGfvtfla0Null.getFla05());

        wsGfvtfla0Null.setFla06(0);
        assertEquals(0, wsGfvtfla0Null.getFla06());

        wsGfvtfla0Null.setFla07(0);
        assertEquals(0, wsGfvtfla0Null.getFla07());

        wsGfvtfla0Null.setFla08(0);
        assertEquals(0, wsGfvtfla0Null.getFla08());

        wsGfvtfla0Null.setFla09(0);
        assertEquals(0, wsGfvtfla0Null.getFla09());

        wsGfvtfla0Null.setFla10(0);
        assertEquals(0, wsGfvtfla0Null.getFla10());

        wsGfvtfla0Null.setFla11(0);
        assertEquals(0, wsGfvtfla0Null.getFla11());

        wsGfvtfla0Null.setFla12(0);
        assertEquals(0, wsGfvtfla0Null.getFla12());

        wsGfvtfla0Null.setFla13(0);
        assertEquals(0, wsGfvtfla0Null.getFla13());

    }

    // property fla01
    @Test
    void testGetterSetterFla01() {

        assertNotNull(wsGfvtfla0Null.getFla01());

        int vint = 1234;
        wsGfvtfla0Null.setFla01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla01()));
    }

    // property fla02
    @Test
    void testGetterSetterFla02() {

        assertNotNull(wsGfvtfla0Null.getFla02());

        int vint = 1234;
        wsGfvtfla0Null.setFla02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla02()));
    }

    // property fla03
    @Test
    void testGetterSetterFla03() {

        assertNotNull(wsGfvtfla0Null.getFla03());

        int vint = 1234;
        wsGfvtfla0Null.setFla03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla03()));
    }

    // property fla04
    @Test
    void testGetterSetterFla04() {

        assertNotNull(wsGfvtfla0Null.getFla04());

        int vint = 1234;
        wsGfvtfla0Null.setFla04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla04()));
    }

    // property fla05
    @Test
    void testGetterSetterFla05() {

        assertNotNull(wsGfvtfla0Null.getFla05());

        int vint = 1234;
        wsGfvtfla0Null.setFla05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla05()));
    }

    // property fla06
    @Test
    void testGetterSetterFla06() {

        assertNotNull(wsGfvtfla0Null.getFla06());

        int vint = 1234;
        wsGfvtfla0Null.setFla06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla06()));
    }

    // property fla07
    @Test
    void testGetterSetterFla07() {

        assertNotNull(wsGfvtfla0Null.getFla07());

        int vint = 1234;
        wsGfvtfla0Null.setFla07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla07()));
    }

    // property fla08
    @Test
    void testGetterSetterFla08() {

        assertNotNull(wsGfvtfla0Null.getFla08());

        int vint = 1234;
        wsGfvtfla0Null.setFla08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla08()));
    }

    // property fla09
    @Test
    void testGetterSetterFla09() {

        assertNotNull(wsGfvtfla0Null.getFla09());

        int vint = 1234;
        wsGfvtfla0Null.setFla09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla09()));
    }

    // property fla10
    @Test
    void testGetterSetterFla10() {

        assertNotNull(wsGfvtfla0Null.getFla10());

        int vint = 1234;
        wsGfvtfla0Null.setFla10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla10()));
    }

    // property fla11
    @Test
    void testGetterSetterFla11() {

        assertNotNull(wsGfvtfla0Null.getFla11());

        int vint = 1234;
        wsGfvtfla0Null.setFla11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla11()));
    }

    // property fla12
    @Test
    void testGetterSetterFla12() {

        assertNotNull(wsGfvtfla0Null.getFla12());

        int vint = 1234;
        wsGfvtfla0Null.setFla12(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla12()));
    }

    // property fla13
    @Test
    void testGetterSetterFla13() {

        assertNotNull(wsGfvtfla0Null.getFla13());

        int vint = 1234;
        wsGfvtfla0Null.setFla13(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla13()));
    }
}

