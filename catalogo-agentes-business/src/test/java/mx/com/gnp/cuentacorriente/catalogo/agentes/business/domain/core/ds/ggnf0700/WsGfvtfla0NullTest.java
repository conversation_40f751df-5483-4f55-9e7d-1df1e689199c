package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTFLA0-NULL.
 *
 */
class WsGfvtfla0NullTest {

    private WsGfvtfla0Null wsGfvtfla0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtfla0Null = new WsGfvtfla0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsGfvtfla0Null wsGfvtfla0Null2 = new WsGfvtfla0Null(parent);
        assertNotNull(wsGfvtfla0Null2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 26);
        WsGfvtfla0Null wsGfvtfla0Null3 = new WsGfvtfla0Null(parent, sibling);
        assertNotNull(wsGfvtfla0Null3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsGfvtfla0Null.getFla01());

        assertNotNull(wsGfvtfla0Null.getFla02());

        assertNotNull(wsGfvtfla0Null.getFla03());

        assertNotNull(wsGfvtfla0Null.getFla04());

        assertNotNull(wsGfvtfla0Null.getFla05());

        assertNotNull(wsGfvtfla0Null.getFla06());

        assertNotNull(wsGfvtfla0Null.getFla07());

        assertNotNull(wsGfvtfla0Null.getFla08());

        assertNotNull(wsGfvtfla0Null.getFla09());

        assertNotNull(wsGfvtfla0Null.getFla10());

        assertNotNull(wsGfvtfla0Null.getFla11());

        assertNotNull(wsGfvtfla0Null.getFla12());

        assertNotNull(wsGfvtfla0Null.getFla13());

    }

    // property fla01
    @Test
    void testGetterSetterFla01() {

        assertNotNull(wsGfvtfla0Null.getFla01());

        int vint = 1234;
        wsGfvtfla0Null.setFla01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla01()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla01(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla01()));
    }

    // property fla02
    @Test
    void testGetterSetterFla02() {

        assertNotNull(wsGfvtfla0Null.getFla02());

        int vint = 1234;
        wsGfvtfla0Null.setFla02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla02()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla02(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla02()));
    }

    // property fla03
    @Test
    void testGetterSetterFla03() {

        assertNotNull(wsGfvtfla0Null.getFla03());

        int vint = 1234;
        wsGfvtfla0Null.setFla03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla03()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla03(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla03()));
    }

    // property fla04
    @Test
    void testGetterSetterFla04() {

        assertNotNull(wsGfvtfla0Null.getFla04());

        int vint = 1234;
        wsGfvtfla0Null.setFla04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla04()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla04(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla04()));
    }

    // property fla05
    @Test
    void testGetterSetterFla05() {

        assertNotNull(wsGfvtfla0Null.getFla05());

        int vint = 1234;
        wsGfvtfla0Null.setFla05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla05()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla05(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla05()));
    }

    // property fla06
    @Test
    void testGetterSetterFla06() {

        assertNotNull(wsGfvtfla0Null.getFla06());

        int vint = 1234;
        wsGfvtfla0Null.setFla06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla06()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla06(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla06()));
    }

    // property fla07
    @Test
    void testGetterSetterFla07() {

        assertNotNull(wsGfvtfla0Null.getFla07());

        int vint = 1234;
        wsGfvtfla0Null.setFla07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla07()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla07(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla07()));
    }

    // property fla08
    @Test
    void testGetterSetterFla08() {

        assertNotNull(wsGfvtfla0Null.getFla08());

        int vint = 1234;
        wsGfvtfla0Null.setFla08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla08()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla08(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla08()));
    }

    // property fla09
    @Test
    void testGetterSetterFla09() {

        assertNotNull(wsGfvtfla0Null.getFla09());

        int vint = 1234;
        wsGfvtfla0Null.setFla09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla09()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla09(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla09()));
    }

    // property fla10
    @Test
    void testGetterSetterFla10() {

        assertNotNull(wsGfvtfla0Null.getFla10());

        int vint = 1234;
        wsGfvtfla0Null.setFla10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla10()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla10(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla10()));
    }

    // property fla11
    @Test
    void testGetterSetterFla11() {

        assertNotNull(wsGfvtfla0Null.getFla11());

        int vint = 1234;
        wsGfvtfla0Null.setFla11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla11()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla11(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla11()));
    }

    // property fla12
    @Test
    void testGetterSetterFla12() {

        assertNotNull(wsGfvtfla0Null.getFla12());

        int vint = 1234;
        wsGfvtfla0Null.setFla12(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla12()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla12(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla12()));
    }

    // property fla13
    @Test
    void testGetterSetterFla13() {

        assertNotNull(wsGfvtfla0Null.getFla13());

        int vint = 1234;
        wsGfvtfla0Null.setFla13(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0Null.getFla13()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsGfvtfla0Null.setFla13(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsGfvtfla0Null.getFla13()));
    }
}

