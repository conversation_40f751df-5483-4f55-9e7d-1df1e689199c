package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tofn0_1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-OFN0-ELEM-1.
 *
 */
class TOfn0Elem_1Test {

    private TOfn0Elem_1 tOfn0Elem_1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tOfn0Elem_1 = new TOfn0Elem_1();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TOfn0Elem_1 tOfn0Elem_12 = new TOfn0Elem_1(parent);
        assertNotNull(tOfn0Elem_12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 1);
        TOfn0Elem_1 tOfn0Elem_13 = new TOfn0Elem_1(parent, sibling);
        assertNotNull(tOfn0Elem_13);
    }

    @Test
    void testInitialize() {
        assertNotNull(tOfn0Elem_1.getSCnaOfnCveElem());

    }

    // property sCnaOfnCveElem
    @Test
    void testGetterSetterSCnaOfnCveElem() {

        assertNotNull(tOfn0Elem_1.getSCnaOfnCveElem());

        String vString = "A";
        tOfn0Elem_1.setSCnaOfnCveElem(vString); // String
        assertEquals(asStr(vString), asStr(tOfn0Elem_1.getSCnaOfnCveElem()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tOfn0Elem_1.setSCnaOfnCveElem(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tOfn0Elem_1.getSCnaOfnCveElem()));
    }
}

