package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTMTE0-NULL.
 *
 */
class WsGfvtmte0NullTest {

    private WsGfvtmte0Null wsGfvtmte0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtmte0Null = new WsGfvtmte0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtmte0Null.setMte01(0);
        assertEquals(0, wsGfvtmte0Null.getMte01());

        wsGfvtmte0Null.setMte02(0);
        assertEquals(0, wsGfvtmte0Null.getMte02());

        wsGfvtmte0Null.setMte03(0);
        assertEquals(0, wsGfvtmte0Null.getMte03());

        wsGfvtmte0Null.setMte04(0);
        assertEquals(0, wsGfvtmte0Null.getMte04());

        wsGfvtmte0Null.setMte05(0);
        assertEquals(0, wsGfvtmte0Null.getMte05());

    }

    // property mte01
    @Test
    void testGetterSetterMte01() {

        assertNotNull(wsGfvtmte0Null.getMte01());

        int vint = 1234;
        wsGfvtmte0Null.setMte01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmte0Null.getMte01()));
    }

    // property mte02
    @Test
    void testGetterSetterMte02() {

        assertNotNull(wsGfvtmte0Null.getMte02());

        int vint = 1234;
        wsGfvtmte0Null.setMte02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmte0Null.getMte02()));
    }

    // property mte03
    @Test
    void testGetterSetterMte03() {

        assertNotNull(wsGfvtmte0Null.getMte03());

        int vint = 1234;
        wsGfvtmte0Null.setMte03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmte0Null.getMte03()));
    }

    // property mte04
    @Test
    void testGetterSetterMte04() {

        assertNotNull(wsGfvtmte0Null.getMte04());

        int vint = 1234;
        wsGfvtmte0Null.setMte04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmte0Null.getMte04()));
    }

    // property mte05
    @Test
    void testGetterSetterMte05() {

        assertNotNull(wsGfvtmte0Null.getMte05());

        int vint = 1234;
        wsGfvtmte0Null.setMte05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmte0Null.getMte05()));
    }
}

