package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AYUDA.
 *
 */
class WsAyudaTest {

    private WsAyuda wsAyuda;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAyuda = new WsAyuda();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

        assertNotNull(wsAyuda.getWsGteA());


        wsAyuda.setWsCpoCveB(0);
        assertEquals(0, wsAyuda.getWsCpoCveB());

        assertNotNull(wsAyuda.getWsCpoCve());


        wsAyuda.setWsOf(0);
        assertEquals(0, wsAyuda.getWsOf());






        assertNotNull(wsAyuda.getWsTelAux());

        wsAyuda.setWsEdoInter("");
        assertEquals("", wsAyuda.getWsEdoInter());

        wsAyuda.setWsDmaIdr(0);
        assertEquals(0, wsAyuda.getWsDmaIdr());

        wsAyuda.setWsOpcion("");
        assertEquals("", wsAyuda.getWsOpcion());

        assertNotNull(wsAyuda.getWsFlaGerZon());






        assertNotNull(wsAyuda.getWsUniqueAgentCode());


        wsAyuda.setWsIndNogte("");
        assertEquals("", wsAyuda.getWsIndNogte());




    }

    // property wsNombreGz
    @Test
    void testGetterSetterWsNombreGz() {

        assertNotNull(wsAyuda.getWsNombreGz());
    }

    // property wsGteA
    @Test
    void testGetterSetterWsGteA() {

        assertNotNull(wsAyuda.getWsGteA());

        String vString = "ABCabc1230";
        wsAyuda.setWsGteA(vString); // String
        assertEquals(asStr(vString), asStr(wsAyuda.getWsGteA()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAyuda.setWsGteA(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAyuda.getWsGteA()));
    }

    // property wsGteR
    @Test
    void testGetterSetterWsGteR() {

        assertNotNull(wsAyuda.getWsGteR());
    }

    // property wsCpoCveB
    @Test
    void testGetterSetterWsCpoCveB() {

        assertNotNull(wsAyuda.getWsCpoCveB());

        int vint = 123456789;
        wsAyuda.setWsCpoCveB(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsCpoCveB()));
    }

    // property wsCpoCve
    @Test
    void testGetterSetterWsCpoCve() {

        assertNotNull(wsAyuda.getWsCpoCve());

        int vint = 123456789;
        wsAyuda.setWsCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsCpoCve()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsAyuda.setWsCpoCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAyuda.getWsCpoCve()));
    }

    // property wsCpoCveR
    @Test
    void testGetterSetterWsCpoCveR() {

        assertNotNull(wsAyuda.getWsCpoCveR());
    }

    // property wsOf
    @Test
    void testGetterSetterWsOf() {

        assertNotNull(wsAyuda.getWsOf());

        int vint = 123;
        wsAyuda.setWsOf(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsOf()));
    }

    // property wsAuxPhonen
    @Test
    void testGetterSetterWsAuxPhonen() {

        assertNotNull(wsAyuda.getWsAuxPhonen());
    }

    // property wsAuxTelefono
    @Test
    void testGetterSetterWsAuxTelefono() {

        assertNotNull(wsAyuda.getWsAuxTelefono());
    }

    // property wsAuxNombre
    @Test
    void testGetterSetterWsAuxNombre() {

        assertNotNull(wsAyuda.getWsAuxNombre());
    }

    // property wsAuxDate
    @Test
    void testGetterSetterWsAuxDate() {

        assertNotNull(wsAyuda.getWsAuxDate());
    }

    // property wsAuxDateSinG
    @Test
    void testGetterSetterWsAuxDateSinG() {

        assertNotNull(wsAyuda.getWsAuxDateSinG());
    }

    // property wsTelAux
    @Test
    void testGetterSetterWsTelAux() {

        assertNotNull(wsAyuda.getWsTelAux());

        BigDecimal vBigDecimal = new BigDecimal("12345678901234");
        wsAyuda.setWsTelAux(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAyuda.getWsTelAux().toBigDecimal());

        int vint = 123456789;
        wsAyuda.setWsTelAux(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsTelAux()));

        NumericVar vINumericValue = new NumericVar(14, 0);;
        vINumericValue.setValue("12345678901234");
        wsAyuda.setWsTelAux(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAyuda.getWsTelAux()));
    }

    // property wsEdoInter
    @Test
    void testGetterSetterWsEdoInter() {

        assertNotNull(wsAyuda.getWsEdoInter());

        String vString = "ABC";
        wsAyuda.setWsEdoInter(vString); // String
        assertEquals(asStr(vString), asStr(wsAyuda.getWsEdoInter()));
    }

    // property wsDmaIdr
    @Test
    void testGetterSetterWsDmaIdr() {

        assertNotNull(wsAyuda.getWsDmaIdr());

        int vint = 1;
        wsAyuda.setWsDmaIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsDmaIdr()));
    }

    // property wsOpcion
    @Test
    void testGetterSetterWsOpcion() {

        assertNotNull(wsAyuda.getWsOpcion());

        String vString = "A";
        wsAyuda.setWsOpcion(vString); // String
        assertEquals(asStr(vString), asStr(wsAyuda.getWsOpcion()));
        // Conditionals (88) - WS-OPCION
        wsAyuda.setWsAgteGte();
        assertTrue(wsAyuda.isWsAgteGte());
        wsAyuda.setWsOficina();
        assertTrue(wsAyuda.isWsOficina());
        wsAyuda.setWsCr();
        assertTrue(wsAyuda.isWsCr());

    }

    // property wsFlaGerZon
    @Test
    void testGetterSetterWsFlaGerZon() {

        assertNotNull(wsAyuda.getWsFlaGerZon());

        String vString = "ABC";
        wsAyuda.setWsFlaGerZon(vString); // String
        assertEquals(asStr(vString), asStr(wsAyuda.getWsFlaGerZon()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsAyuda.setWsFlaGerZon(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAyuda.getWsFlaGerZon()));
    }

    // property wsFlaGerZonN
    @Test
    void testGetterSetterWsFlaGerZonN() {

        assertNotNull(wsAyuda.getWsFlaGerZonN());

        int vint = 123;
        wsAyuda.setWsFlaGerZonN(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsFlaGerZonN()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        wsAyuda.setWsFlaGerZonN(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAyuda.getWsFlaGerZonN()));
    }

    // property wsChain
    @Test
    void testGetterSetterWsChain() {

        assertNotNull(wsAyuda.getWsChain());
    }

    // property wsChainN
    @Test
    void testGetterSetterWsChainN() {

        assertNotNull(wsAyuda.getWsChainN());

        BigDecimal vBigDecimal = new BigDecimal("1234567890");
        wsAyuda.setWsChainN(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsAyuda.getWsChainN().toBigDecimal());

        int vint = 123456789;
        wsAyuda.setWsChainN(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsChainN()));

        NumericVar vINumericValue = new NumericVar(10, 0);;
        vINumericValue.setValue("1234567890");
        wsAyuda.setWsChainN(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAyuda.getWsChainN()));
    }

    // property wsAgentId
    @Test
    void testGetterSetterWsAgentId() {

        assertNotNull(wsAyuda.getWsAgentId());
    }

    // property wsAgentIdN
    @Test
    void testGetterSetterWsAgentIdN() {

        assertNotNull(wsAyuda.getWsAgentIdN());

        int vint = 12345;
        wsAyuda.setWsAgentIdN(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsAgentIdN()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        wsAyuda.setWsAgentIdN(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAyuda.getWsAgentIdN()));
    }

    // property wsUniqueAgentCode
    @Test
    void testGetterSetterWsUniqueAgentCode() {

        assertNotNull(wsAyuda.getWsUniqueAgentCode());

        String vString = "ABCabc12";
        wsAyuda.setWsUniqueAgentCode(vString); // String
        assertEquals(asStr(vString), asStr(wsAyuda.getWsUniqueAgentCode()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsAyuda.setWsUniqueAgentCode(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAyuda.getWsUniqueAgentCode()));
    }

    // property wsUniqueAgentCodeN
    @Test
    void testGetterSetterWsUniqueAgentCodeN() {

        assertNotNull(wsAyuda.getWsUniqueAgentCodeN());

        int vint = 12345678;
        wsAyuda.setWsUniqueAgentCodeN(vint); // int
        assertEquals(asInt(vint), asInt(wsAyuda.getWsUniqueAgentCodeN()));

        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsAyuda.setWsUniqueAgentCodeN(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAyuda.getWsUniqueAgentCodeN()));
    }

    // property wsIndNogte
    @Test
    void testGetterSetterWsIndNogte() {

        assertNotNull(wsAyuda.getWsIndNogte());

        String vString = "A";
        wsAyuda.setWsIndNogte(vString); // String
        assertEquals(asStr(vString), asStr(wsAyuda.getWsIndNogte()));
    }

    // property wsFlaNum
    @Test
    void testGetterSetterWsFlaNum() {

        assertNotNull(wsAyuda.getWsFlaNum());
    }

    // property wsFlaNumGte
    @Test
    void testGetterSetterWsFlaNumGte() {

        assertNotNull(wsAyuda.getWsFlaNumGte());
    }

    // property wsFechaSys
    @Test
    void testGetterSetterWsFechaSys() {

        assertNotNull(wsAyuda.getWsFechaSys());
    }

    // property wsNombreGzk
    @Test
    void testGetterSetterWsNombreGzk() {

        // Property: wsNombreGz.wsNombreGzk -> 10 WS-NOMBRE-GZK X(24)
        assertNotNull(wsAyuda.getWsNombreGzk());
        String vString = "ABCabc1230";
        wsAyuda.setWsNombreGzk(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsAyuda.setWsNombreGzk(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsNombreGzNo
    @Test
    void testGetterSetterWsNombreGzNo() {

        // Property: wsNombreGz.wsNombreGzNo -> 10 WS-NOMBRE-GZ-NO X(03)
        assertNotNull(wsAyuda.getWsNombreGzNo());
        String vString = "ABC";
        wsAyuda.setWsNombreGzNo(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsAyuda.setWsNombreGzNo(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsNombreFil
    @Test
    void testGetterSetterWsNombreFil() {

        // Property: wsNombreGz.wsNombreFil -> 10 WS-NOMBRE-FIL X(08)
        assertNotNull(wsAyuda.getWsNombreFil());
        String vString = "ABCabc12";
        wsAyuda.setWsNombreFil(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsAyuda.setWsNombreFil(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGteX
    @Test
    void testGetterSetterWsGteX() {

        // Property: wsGteR.wsGteX -> 10 WS-GTE-X X(03)
        assertNotNull(wsAyuda.getWsGteX());
        String vString = "ABC";
        wsAyuda.setWsGteX(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsAyuda.setWsGteX(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGteN
    @Test
    void testGetterSetterWsGteN() {

        // Property: wsGteR.wsGteN -> 10 WS-GTE-N 9(07)
        assertNotNull(wsAyuda.getWsGteN());
        int vint = 1234567;
        wsAyuda.setWsGteN(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsAyuda.setWsGteN(vINumericValue); // INumericValue
    }

    // property wsCpoCve_4
    @Test
    void testGetterSetterWsCpoCve_4() {

        // Property: wsCpoCveR.wsCpoCve_4 -> 10 WS-CPO-CVE-4 X(04)
        assertNotNull(wsAyuda.getWsCpoCve_4());
        String vString = "ABCa";
        wsAyuda.setWsCpoCve_4(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsAyuda.setWsCpoCve_4(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsCpoCve_5
    @Test
    void testGetterSetterWsCpoCve_5() {

        // Property: wsCpoCveR.wsCpoCve_5 -> 10 WS-CPO-CVE-5 X(05)
        assertNotNull(wsAyuda.getWsCpoCve_5());
        String vString = "ABCab";
        wsAyuda.setWsCpoCve_5(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsAyuda.setWsCpoCve_5(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAuxPhone
    @Test
    void testGetterSetterWsAuxPhone() {

        // Property: wsAuxPhonen.wsAuxPhone -> 10 WS-AUX-PHONE X(01) [14]
        assertNotNull(wsAyuda.getWsAuxPhone());
        String vString = "A";
        wsAyuda.getWsAuxPhone().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAyuda.getWsAuxPhone().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsAuxTelX
    @Test
    void testGetterSetterWsAuxTelX() {

        // Property: wsAuxTelefono.wsAuxTelX -> 10 WS-AUX-TEL-X X(01) [60]
        assertNotNull(wsAyuda.getWsAuxTelX());
        String vString = "A";
        wsAyuda.getWsAuxTelX().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAyuda.getWsAuxTelX().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsAuxAPat
    @Test
    void testGetterSetterWsAuxAPat() {

        // Property: wsAuxNombre.wsAuxAPat -> 10 WS-AUX-A-PAT
        assertNotNull(wsAyuda.getWsAuxAPat());
    }

    // property wsAuxApPat
    @Test
    void testGetterSetterWsAuxApPat() {

        // Property: wsAuxNombre.wsAuxAPat.wsAuxApPat -> 15 WS-AUX-AP-PAT X(01) [40]
        assertNotNull(wsAyuda.getWsAuxApPat());
        String vString = "A";
        wsAyuda.getWsAuxApPat().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAyuda.getWsAuxApPat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsAuxAMat
    @Test
    void testGetterSetterWsAuxAMat() {

        // Property: wsAuxNombre.wsAuxAMat -> 10 WS-AUX-A-MAT
        assertNotNull(wsAyuda.getWsAuxAMat());
    }

    // property wsAuxApMat
    @Test
    void testGetterSetterWsAuxApMat() {

        // Property: wsAuxNombre.wsAuxAMat.wsAuxApMat -> 15 WS-AUX-AP-MAT X(01) [30]
        assertNotNull(wsAyuda.getWsAuxApMat());
        String vString = "A";
        wsAyuda.getWsAuxApMat().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAyuda.getWsAuxApMat().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsAuxLasName
    @Test
    void testGetterSetterWsAuxLasName() {

        // Property: wsAuxNombre.wsAuxLasName -> 10 WS-AUX-LAS-NAME UsedAsParameter
        assertNotNull(wsAyuda.getWsAuxLasName());
    }

    // property wsAuxLastName
    @Test
    void testGetterSetterWsAuxLastName() {

        // Property: wsAuxNombre.wsAuxLasName.wsAuxLastName -> 15 WS-AUX-LAST-NAME X(01) [40]
        assertNotNull(wsAyuda.getWsAuxLastName());
        String vString = "A";
        wsAyuda.getWsAuxLastName().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAyuda.getWsAuxLastName().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wsAuxDateAaaa
    @Test
    void testGetterSetterWsAuxDateAaaa() {

        // Property: wsAuxDate.wsAuxDateAaaa -> 10 WS-AUX-DATE-AAAA X(04)
        assertNotNull(wsAyuda.getWsAuxDateAaaa());
        String vString = "ABCa";
        wsAyuda.setWsAuxDateAaaa(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsAyuda.setWsAuxDateAaaa(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAuxDateMm
    @Test
    void testGetterSetterWsAuxDateMm() {

        // Property: wsAuxDate.wsAuxDateMm -> 10 WS-AUX-DATE-MM X(02)
        assertNotNull(wsAyuda.getWsAuxDateMm());
        String vString = "AB";
        wsAyuda.setWsAuxDateMm(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAyuda.setWsAuxDateMm(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAuxDateDd
    @Test
    void testGetterSetterWsAuxDateDd() {

        // Property: wsAuxDate.wsAuxDateDd -> 10 WS-AUX-DATE-DD X(02)
        assertNotNull(wsAyuda.getWsAuxDateDd());
        String vString = "AB";
        wsAyuda.setWsAuxDateDd(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAyuda.setWsAuxDateDd(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAuxDateSinGAaaa
    @Test
    void testGetterSetterWsAuxDateSinGAaaa() {

        // Property: wsAuxDateSinG.wsAuxDateSinGAaaa -> 10 WS-AUX-DATE-SIN-G-AAAA 9(04)
        assertNotNull(wsAyuda.getWsAuxDateSinGAaaa());
        int vint = 1234;
        wsAyuda.setWsAuxDateSinGAaaa(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsAyuda.setWsAuxDateSinGAaaa(vINumericValue); // INumericValue
    }

    // property wsAuxDateSinGMm
    @Test
    void testGetterSetterWsAuxDateSinGMm() {

        // Property: wsAuxDateSinG.wsAuxDateSinGMm -> 10 WS-AUX-DATE-SIN-G-MM 9(02)
        assertNotNull(wsAyuda.getWsAuxDateSinGMm());
        int vint = 12;
        wsAyuda.setWsAuxDateSinGMm(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAyuda.setWsAuxDateSinGMm(vINumericValue); // INumericValue
    }

    // property wsAuxDateSinGDd
    @Test
    void testGetterSetterWsAuxDateSinGDd() {

        // Property: wsAuxDateSinG.wsAuxDateSinGDd -> 10 WS-AUX-DATE-SIN-G-DD 9(02)
        assertNotNull(wsAyuda.getWsAuxDateSinGDd());
        int vint = 12;
        wsAyuda.setWsAuxDateSinGDd(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAyuda.setWsAuxDateSinGDd(vINumericValue); // INumericValue
    }

    // property wsChain1
    @Test
    void testGetterSetterWsChain1() {

        // Property: wsChain.wsChain1 -> 10 WS-CHAIN1 X(05)
        assertNotNull(wsAyuda.getWsChain1());
        String vString = "ABCab";
        wsAyuda.setWsChain1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsAyuda.setWsChain1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsChain2
    @Test
    void testGetterSetterWsChain2() {

        // Property: wsChain.wsChain2 -> 10 WS-CHAIN2 X(05)
        assertNotNull(wsAyuda.getWsChain2());
        String vString = "ABCab";
        wsAyuda.setWsChain2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsAyuda.setWsChain2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgentId2
    @Test
    void testGetterSetterWsAgentId2() {

        // Property: wsAgentId.wsAgentId2 -> 10 WS-AGENT-ID2 X(02)
        assertNotNull(wsAyuda.getWsAgentId2());
        String vString = "AB";
        wsAyuda.setWsAgentId2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAyuda.setWsAgentId2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgentId3
    @Test
    void testGetterSetterWsAgentId3() {

        // Property: wsAgentId.wsAgentId3 -> 10 WS-AGENT-ID3 X(03)
        assertNotNull(wsAyuda.getWsAgentId3());
        String vString = "ABC";
        wsAyuda.setWsAgentId3(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsAyuda.setWsAgentId3(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFlaNum_1
    @Test
    void testGetterSetterWsFlaNum_1() {

        // Property: wsFlaNum.wsFlaNum_1 -> 10 WS-FLA-NUM-1 X(01)
        assertNotNull(wsAyuda.getWsFlaNum_1());
        String vString = "A";
        wsAyuda.setWsFlaNum_1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsAyuda.setWsFlaNum_1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFlaNumX
    @Test
    void testGetterSetterWsFlaNumX() {

        // Property: wsFlaNum.wsFlaNumX -> 10 WS-FLA-NUM-X 9(02)
        assertNotNull(wsAyuda.getWsFlaNumX());
        int vint = 12;
        wsAyuda.setWsFlaNumX(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAyuda.setWsFlaNumX(vINumericValue); // INumericValue
    }

    // property wsFlaNum_2
    @Test
    void testGetterSetterWsFlaNum_2() {

        // Property: wsFlaNum.wsFlaNum_2 -> 10 WS-FLA-NUM-2 9(05)
        assertNotNull(wsAyuda.getWsFlaNum_2());
        int vint = 12345;
        wsAyuda.setWsFlaNum_2(vint); // int
        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        wsAyuda.setWsFlaNum_2(vINumericValue); // INumericValue
    }

    // property wsFlaNumGte_1
    @Test
    void testGetterSetterWsFlaNumGte_1() {

        // Property: wsFlaNumGte.wsFlaNumGte_1 -> 10 WS-FLA-NUM-GTE-1 X(01)
        assertNotNull(wsAyuda.getWsFlaNumGte_1());
        String vString = "A";
        wsAyuda.setWsFlaNumGte_1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsAyuda.setWsFlaNumGte_1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFlaNumGteX
    @Test
    void testGetterSetterWsFlaNumGteX() {

        // Property: wsFlaNumGte.wsFlaNumGteX -> 10 WS-FLA-NUM-GTE-X X(02)
        assertNotNull(wsAyuda.getWsFlaNumGteX());
        String vString = "AB";
        wsAyuda.setWsFlaNumGteX(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAyuda.setWsFlaNumGteX(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFlaNumGte_2
    @Test
    void testGetterSetterWsFlaNumGte_2() {

        // Property: wsFlaNumGte.wsFlaNumGte_2 -> 10 WS-FLA-NUM-GTE-2 9(05)
        assertNotNull(wsAyuda.getWsFlaNumGte_2());
        int vint = 12345;
        wsAyuda.setWsFlaNumGte_2(vint); // int
        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        wsAyuda.setWsFlaNumGte_2(vINumericValue); // INumericValue
    }

    // property wsAaSys
    @Test
    void testGetterSetterWsAaSys() {

        // Property: wsFechaSys.wsAaSys -> 10 WS-AA-SYS 9(04)
        assertNotNull(wsAyuda.getWsAaSys());
        int vint = 1234;
        wsAyuda.setWsAaSys(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsAyuda.setWsAaSys(vINumericValue); // INumericValue
    }

    // property wsMmSys
    @Test
    void testGetterSetterWsMmSys() {

        // Property: wsFechaSys.wsMmSys -> 10 WS-MM-SYS 9(02)
        assertNotNull(wsAyuda.getWsMmSys());
        int vint = 12;
        wsAyuda.setWsMmSys(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAyuda.setWsMmSys(vINumericValue); // INumericValue
    }

    // property wsDdSys
    @Test
    void testGetterSetterWsDdSys() {

        // Property: wsFechaSys.wsDdSys -> 10 WS-DD-SYS 9(02)
        assertNotNull(wsAyuda.getWsDdSys());
        int vint = 12;
        wsAyuda.setWsDdSys(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAyuda.setWsDdSys(vINumericValue); // INumericValue
    }
}

