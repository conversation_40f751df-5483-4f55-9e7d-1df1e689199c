package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-MESES.
 *
 */
class TablaMesesTest {

    private TablaMeses tablaMeses;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaMeses = new TablaMeses();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(tablaMeses.getTablaMes());


    }

    // property tablaMes
    @Test
    void testGetterSetterTablaMes() {

        assertNotNull(tablaMeses.getTablaMes());

        String vString = "ABCabc1230";
        tablaMeses.setTablaMes(vString); // String
        assertEquals(asStr(vString), asStr(tablaMeses.getTablaMes()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaMeses.setTablaMes(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tablaMeses.getTablaMes()));
    }

    // property tablaMesR
    @Test
    void testGetterSetterTablaMesR() {

        assertNotNull(tablaMeses.getTablaMesR());
        assertNotNull(tablaMeses.getTablaMesR(1));
    }

    // property tbDia
    @Test
    void testGetterSetterTbDia() {

        // Property: tablaMesR.tbDia -> 07 TB-DIA 9(02)
        assertNotNull(tablaMeses.getTbDia());
        int vint = 12;
        tablaMeses.setTbDia(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        tablaMeses.setTbDia(vINumericValue); // INumericValue
    }
}

