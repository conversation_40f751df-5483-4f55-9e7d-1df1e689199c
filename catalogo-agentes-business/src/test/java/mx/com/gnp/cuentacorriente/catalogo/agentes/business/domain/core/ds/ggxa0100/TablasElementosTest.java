package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLAS-ELEMENTOS.
 *
 */
class TablasElementosTest {

    private TablasElementos tablasElementos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablasElementos = new TablasElementos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {


    }

    // property wwTabDato
    @Test
    void testGetterSetterWwTabDato() {

        assertNotNull(tablasElementos.getWwTabDato());
    }

    // property wwTabDato5
    @Test
    void testGetterSetterWwTabDato5() {

        assertNotNull(tablasElementos.getWwTabDato5());
    }

    // property wwCampo
    @Test
    void testGetterSetterWwCampo() {

        // Property: wwTabDato.wwCampo -> 15 WW-CAMPO X(01) [3]
        assertNotNull(tablasElementos.getWwCampo());
        String vString = "A";
        tablasElementos.getWwCampo().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        tablasElementos.getWwCampo().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }

    // property wwCampo5
    @Test
    void testGetterSetterWwCampo5() {

        // Property: wwTabDato5.wwCampo5 -> 15 WW-CAMPO5 X(01) [5]
        assertNotNull(tablasElementos.getWwCampo5());
        String vString = "A";
        tablasElementos.getWwCampo5().at(1).setValue(vString); // String
        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        tablasElementos.getWwCampo5().at(1).setValue(vAlphanumericVar); // AlphanumericVar
    }
}

