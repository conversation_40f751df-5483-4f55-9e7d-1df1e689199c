package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-DATA-SAT.
 *
 */
class RpDataSatTest {

    private RpDataSat rpDataSat;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpDataSat = new RpDataSat();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        rpDataSat.setRpFolioFiscal("");
        assertEquals("", rpDataSat.getRpFolioFiscal());

        assertNotNull(rpDataSat.getRpPercepGrav());

        assertNotNull(rpDataSat.getRpPercepExen());

        assertNotNull(rpDataSat.getRpIva());

        assertNotNull(rpDataSat.getRpIsr());

        assertNotNull(rpDataSat.getRpIvaRtn());

        assertNotNull(rpDataSat.getRpIvaAcr());

        assertNotNull(rpDataSat.getRpIsrRet());

        assertNotNull(rpDataSat.getRpIpuRtnCed());

        assertNotNull(rpDataSat.getRpTasaIva());

        assertNotNull(rpDataSat.getRpTasaCed());

    }

    // property rpFolioFiscal
    @Test
    void testGetterSetterRpFolioFiscal() {

        assertNotNull(rpDataSat.getRpFolioFiscal());

        String vString = "ABCabc1230";
        rpDataSat.setRpFolioFiscal(vString); // String
        assertEquals(asStr(vString), asStr(rpDataSat.getRpFolioFiscal()));
    }

    // property rpPercepGrav
    @Test
    void testGetterSetterRpPercepGrav() {

        assertNotNull(rpDataSat.getRpPercepGrav());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpPercepGrav(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpPercepGrav().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpPercepGrav(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpPercepGrav()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpPercepGrav(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpPercepGrav()));
    }

    // property rpPercepExen
    @Test
    void testGetterSetterRpPercepExen() {

        assertNotNull(rpDataSat.getRpPercepExen());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpPercepExen(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpPercepExen().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpPercepExen(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpPercepExen()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpPercepExen(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpPercepExen()));
    }

    // property rpIva
    @Test
    void testGetterSetterRpIva() {

        assertNotNull(rpDataSat.getRpIva());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpIva().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpIva(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpIva()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpIva()));
    }

    // property rpIsr
    @Test
    void testGetterSetterRpIsr() {

        assertNotNull(rpDataSat.getRpIsr());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpIsr().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpIsr(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpIsr()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpIsr()));
    }

    // property rpIvaRtn
    @Test
    void testGetterSetterRpIvaRtn() {

        assertNotNull(rpDataSat.getRpIvaRtn());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpIvaRtn(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpIvaRtn().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpIvaRtn(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpIvaRtn()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpIvaRtn(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpIvaRtn()));
    }

    // property rpIvaAcr
    @Test
    void testGetterSetterRpIvaAcr() {

        assertNotNull(rpDataSat.getRpIvaAcr());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpIvaAcr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpIvaAcr().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpIvaAcr(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpIvaAcr()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpIvaAcr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpIvaAcr()));
    }

    // property rpIsrRet
    @Test
    void testGetterSetterRpIsrRet() {

        assertNotNull(rpDataSat.getRpIsrRet());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpIsrRet(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpIsrRet().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpIsrRet(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpIsrRet()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpIsrRet(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpIsrRet()));
    }

    // property rpIpuRtnCed
    @Test
    void testGetterSetterRpIpuRtnCed() {

        assertNotNull(rpDataSat.getRpIpuRtnCed());

        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        rpDataSat.setRpIpuRtnCed(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpIpuRtnCed().toBigDecimal());

        int vint = 123456789;
        rpDataSat.setRpIpuRtnCed(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpIpuRtnCed()));

        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        rpDataSat.setRpIpuRtnCed(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpIpuRtnCed()));
    }

    // property rpTasaIva
    @Test
    void testGetterSetterRpTasaIva() {

        assertNotNull(rpDataSat.getRpTasaIva());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        rpDataSat.setRpTasaIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpTasaIva().toBigDecimal());

        int vint = 123;
        rpDataSat.setRpTasaIva(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpTasaIva()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        rpDataSat.setRpTasaIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpTasaIva()));
    }

    // property rpTasaCed
    @Test
    void testGetterSetterRpTasaCed() {

        assertNotNull(rpDataSat.getRpTasaCed());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        rpDataSat.setRpTasaCed(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, rpDataSat.getRpTasaCed().toBigDecimal());

        int vint = 123;
        rpDataSat.setRpTasaCed(vint); // int
        assertEquals(asInt(vint), asInt(rpDataSat.getRpTasaCed()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        rpDataSat.setRpTasaCed(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(rpDataSat.getRpTasaCed()));
    }
}

