package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: PK-NUM-CEDULAP-RR.
 *
 */
class PkNumCedulapRrTest {

    private PkNumCedulapRr pkNumCedulapRr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        pkNumCedulapRr = new PkNumCedulapRr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        PkNumCedulapRr pkNumCedulapRr2 = new PkNumCedulapRr(parent);
        assertNotNull(pkNumCedulapRr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 20);
        PkNumCedulapRr pkNumCedulapRr3 = new PkNumCedulapRr(parent, sibling);
        assertNotNull(pkNumCedulapRr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(pkNumCedulapRr.getPkNumCedulaPTres());

        assertNotNull(pkNumCedulapRr.getPkNumCedulaPR17());

    }

    // property pkNumCedulaPTres
    @Test
    void testGetterSetterPkNumCedulaPTres() {

        assertNotNull(pkNumCedulapRr.getPkNumCedulaPTres());

        String vString = "ABC";
        pkNumCedulapRr.setPkNumCedulaPTres(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedulapRr.getPkNumCedulaPTres()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        pkNumCedulapRr.setPkNumCedulaPTres(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedulapRr.getPkNumCedulaPTres()));
    }

    // property pkNumCedulaPR17
    @Test
    void testGetterSetterPkNumCedulaPR17() {

        assertNotNull(pkNumCedulapRr.getPkNumCedulaPR17());

        String vString = "ABCabc1230";
        pkNumCedulapRr.setPkNumCedulaPR17(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedulapRr.getPkNumCedulaPR17()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        pkNumCedulapRr.setPkNumCedulaPR17(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedulapRr.getPkNumCedulaPR17()));
    }
}

