package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WT-CLAVE-POS.
 *
 */
class WtClavePosTest {

    private WtClavePos wtClavePos;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wtClavePos = new WtClavePos();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WtClavePos wtClavePos2 = new WtClavePos(parent);
        assertNotNull(wtClavePos2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 4);
        WtClavePos wtClavePos3 = new WtClavePos(parent, sibling);
        assertNotNull(wtClavePos3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wtClavePos.getWtDigito());

    }

    // property wtDigito
    @Test
    void testGetterSetterWtDigito() {

        assertNotNull(wtClavePos.getWtDigito());
        assertNotNull(wtClavePos.getWtDigito(1));

        String vString = "A";
        wtClavePos.getWtDigito().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wtClavePos.getWtDigito(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wtClavePos.getWtDigito().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wtClavePos.getWtDigito(1)));
    }
}

