package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGFVTCNA0.
 *
 */
class WsDclgfvtcna0Test {

    private WsDclgfvtcna0 wsDclgfvtcna0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgfvtcna0 = new WsDclgfvtcna0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgfvtcna0.setCnaMteCve(0);
        assertEquals(0, wsDclgfvtcna0.getCnaMteCve());

        wsDclgfvtcna0.setCnaTfpCve(0);
        assertEquals(0, wsDclgfvtcna0.getCnaTfpCve());

        wsDclgfvtcna0.setCnaOfnCve(0);
        assertEquals(0, wsDclgfvtcna0.getCnaOfnCve());

        wsDclgfvtcna0.setCnaGerZonEnv(0);
        assertEquals(0, wsDclgfvtcna0.getCnaGerZonEnv());

    }

    // property cnaMteCve
    @Test
    void testGetterSetterCnaMteCve() {

        assertNotNull(wsDclgfvtcna0.getCnaMteCve());

        int vint = 1234;
        wsDclgfvtcna0.setCnaMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtcna0.getCnaMteCve()));
    }

    // property cnaTfpCve
    @Test
    void testGetterSetterCnaTfpCve() {

        assertNotNull(wsDclgfvtcna0.getCnaTfpCve());

        int vint = 1234;
        wsDclgfvtcna0.setCnaTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtcna0.getCnaTfpCve()));
    }

    // property cnaOfnCve
    @Test
    void testGetterSetterCnaOfnCve() {

        assertNotNull(wsDclgfvtcna0.getCnaOfnCve());

        int vint = 1234;
        wsDclgfvtcna0.setCnaOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtcna0.getCnaOfnCve()));
    }

    // property cnaGerZonEnv
    @Test
    void testGetterSetterCnaGerZonEnv() {

        assertNotNull(wsDclgfvtcna0.getCnaGerZonEnv());

        int vint = 1234;
        wsDclgfvtcna0.setCnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgfvtcna0.getCnaGerZonEnv()));
    }
}

