package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datoscocoa;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: PK-FOLIO-INVESR.
 *
 */
class PkFolioInvesrTest {

    private PkFolioInvesr pkFolioInvesr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        pkFolioInvesr = new PkFolioInvesr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        PkFolioInvesr pkFolioInvesr2 = new PkFolioInvesr(parent);
        assertNotNull(pkFolioInvesr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 7);
        PkFolioInvesr pkFolioInvesr3 = new PkFolioInvesr(parent, sibling);
        assertNotNull(pkFolioInvesr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(pkFolioInvesr.getPkFolio1());

        assertNotNull(pkFolioInvesr.getPkFolio2());

    }

    // property pkFolio1
    @Test
    void testGetterSetterPkFolio1() {

        assertNotNull(pkFolioInvesr.getPkFolio1());

        int vint = 123;
        pkFolioInvesr.setPkFolio1(vint); // int
        assertEquals(asInt(vint), asInt(pkFolioInvesr.getPkFolio1()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        pkFolioInvesr.setPkFolio1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(pkFolioInvesr.getPkFolio1()));
    }

    // property pkFolio2
    @Test
    void testGetterSetterPkFolio2() {

        assertNotNull(pkFolioInvesr.getPkFolio2());

        int vint = 1234;
        pkFolioInvesr.setPkFolio2(vint); // int
        assertEquals(asInt(vint), asInt(pkFolioInvesr.getPkFolio2()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        pkFolioInvesr.setPkFolio2(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(pkFolioInvesr.getPkFolio2()));
    }
}

