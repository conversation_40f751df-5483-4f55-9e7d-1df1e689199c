package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-VARIABLES.
 *
 */
class WsVariablesTest {

    private WsVariables wsVariables;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsVariables = new WsVariables();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsVariables.setSwEofagth(0);
        assertEquals(0, wsVariables.getSwEofagth());

        wsVariables.setSwEofcnah(0);
        assertEquals(0, wsVariables.getSwEofcnah());

        wsVariables.setSwEofdmah(0);
        assertEquals(0, wsVariables.getSwEofdmah());

        wsVariables.setSwEofcmah(0);
        assertEquals(0, wsVariables.getSwEofcmah());

        wsVariables.setSwEofdrc0(0);
        assertEquals(0, wsVariables.getSwEofdrc0());

        wsVariables.setSwEofintr(0);
        assertEquals(0, wsVariables.getSwEofintr());

        wsVariables.setWsConagth(0);
        assertEquals(0, wsVariables.getWsConagth());

        wsVariables.setWsConcnah(0);
        assertEquals(0, wsVariables.getWsConcnah());

        wsVariables.setWsCondmah(0);
        assertEquals(0, wsVariables.getWsCondmah());

        wsVariables.setWsConcmah(0);
        assertEquals(0, wsVariables.getWsConcmah());

        wsVariables.setWsCondrc0(0);
        assertEquals(0, wsVariables.getWsCondrc0());

        wsVariables.setWsConintr(0);
        assertEquals(0, wsVariables.getWsConintr());

        wsVariables.setWsAgtidraux(0);
        assertEquals(0, wsVariables.getWsAgtidraux());

        wsVariables.setWsCnanumaux(0);
        assertEquals(0, wsVariables.getWsCnanumaux());

    }

    // property swEofagth
    @Test
    void testGetterSetterSwEofagth() {

        assertNotNull(wsVariables.getSwEofagth());

        int vint = 1;
        wsVariables.setSwEofagth(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getSwEofagth()));
    }

    // property swEofcnah
    @Test
    void testGetterSetterSwEofcnah() {

        assertNotNull(wsVariables.getSwEofcnah());

        int vint = 1;
        wsVariables.setSwEofcnah(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getSwEofcnah()));
    }

    // property swEofdmah
    @Test
    void testGetterSetterSwEofdmah() {

        assertNotNull(wsVariables.getSwEofdmah());

        int vint = 1;
        wsVariables.setSwEofdmah(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getSwEofdmah()));
    }

    // property swEofcmah
    @Test
    void testGetterSetterSwEofcmah() {

        assertNotNull(wsVariables.getSwEofcmah());

        int vint = 1;
        wsVariables.setSwEofcmah(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getSwEofcmah()));
    }

    // property swEofdrc0
    @Test
    void testGetterSetterSwEofdrc0() {

        assertNotNull(wsVariables.getSwEofdrc0());

        int vint = 1;
        wsVariables.setSwEofdrc0(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getSwEofdrc0()));
    }

    // property swEofintr
    @Test
    void testGetterSetterSwEofintr() {

        assertNotNull(wsVariables.getSwEofintr());

        int vint = 1;
        wsVariables.setSwEofintr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getSwEofintr()));
    }

    // property wsConagth
    @Test
    void testGetterSetterWsConagth() {

        assertNotNull(wsVariables.getWsConagth());

        int vint = 123456789;
        wsVariables.setWsConagth(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsConagth()));
    }

    // property wsConcnah
    @Test
    void testGetterSetterWsConcnah() {

        assertNotNull(wsVariables.getWsConcnah());

        int vint = 123456789;
        wsVariables.setWsConcnah(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsConcnah()));
    }

    // property wsCondmah
    @Test
    void testGetterSetterWsCondmah() {

        assertNotNull(wsVariables.getWsCondmah());

        int vint = 123456789;
        wsVariables.setWsCondmah(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsCondmah()));
    }

    // property wsConcmah
    @Test
    void testGetterSetterWsConcmah() {

        assertNotNull(wsVariables.getWsConcmah());

        int vint = 123456789;
        wsVariables.setWsConcmah(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsConcmah()));
    }

    // property wsCondrc0
    @Test
    void testGetterSetterWsCondrc0() {

        assertNotNull(wsVariables.getWsCondrc0());

        int vint = 123456789;
        wsVariables.setWsCondrc0(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsCondrc0()));
    }

    // property wsConintr
    @Test
    void testGetterSetterWsConintr() {

        assertNotNull(wsVariables.getWsConintr());

        int vint = 123456789;
        wsVariables.setWsConintr(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsConintr()));
    }

    // property wsAgtidraux
    @Test
    void testGetterSetterWsAgtidraux() {

        assertNotNull(wsVariables.getWsAgtidraux());

        int vint = 123456789;
        wsVariables.setWsAgtidraux(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsAgtidraux()));
    }

    // property wsCnanumaux
    @Test
    void testGetterSetterWsCnanumaux() {

        assertNotNull(wsVariables.getWsCnanumaux());

        int vint = 12345;
        wsVariables.setWsCnanumaux(vint); // int
        assertEquals(asInt(vint), asInt(wsVariables.getWsCnanumaux()));
    }
}

