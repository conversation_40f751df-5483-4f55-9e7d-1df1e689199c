package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-PJE-FAV-R.
 *
 */
class WsPjeFavRTest {

    private WsPjeFavR wsPjeFavR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsPjeFavR = new WsPjeFavR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsPjeFavR wsPjeFavR2 = new WsPjeFavR(parent);
        assertNotNull(wsPjeFavR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 7);
        WsPjeFavR wsPjeFavR3 = new WsPjeFavR(parent, sibling);
        assertNotNull(wsPjeFavR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsPjeFavR.getRsPjeFav3());

        assertNotNull(wsPjeFavR.getRsPjeFav4());

    }

    // property rsPjeFav3
    @Test
    void testGetterSetterRsPjeFav3() {

        assertNotNull(wsPjeFavR.getRsPjeFav3());

        String vString = "ABC";
        wsPjeFavR.setRsPjeFav3(vString); // String
        assertEquals(asStr(vString), asStr(wsPjeFavR.getRsPjeFav3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsPjeFavR.setRsPjeFav3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPjeFavR.getRsPjeFav3()));
    }

    // property rsPjeFav4
    @Test
    void testGetterSetterRsPjeFav4() {

        assertNotNull(wsPjeFavR.getRsPjeFav4());

        String vString = "ABCa";
        wsPjeFavR.setRsPjeFav4(vString); // String
        assertEquals(asStr(vString), asStr(wsPjeFavR.getRsPjeFav4()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsPjeFavR.setRsPjeFav4(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsPjeFavR.getRsPjeFav4()));
    }
}

