package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-INTERFAZ.
 *
 */
class WsInterfazTest {

    private WsInterfaz wsInterfaz;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsInterfaz = new WsInterfaz();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsInterfaz wsInterfaz2 = new WsInterfaz(parent);
        assertNotNull(wsInterfaz2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        WsInterfaz wsInterfaz3 = new WsInterfaz(parent, sibling);
        assertNotNull(wsInterfaz3);
    }

    @Test
    void testInitialize() {

    }

    // property ggqa00adcpy
    @Test
    void testGetterSetterGgqa00adcpy() {

        assertNotNull(wsInterfaz.getGgqa00adcpy());
    }

    // property plhcMktHeader
    @Test
    void testGetterSetterPlhcMktHeader() {

        // Property: ggqa00adcpy.plhcMktHeader -> 05 PLHC-MKT-HEADER
        assertNotNull(wsInterfaz.getPlhcMktHeader());
    }

    // property plhcMktRecordIdh
    @Test
    void testGetterSetterPlhcMktRecordIdh() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktRecordIdh -> 10 PLHC-MKT-RECORD-IDH X(05)
        assertNotNull(wsInterfaz.getPlhcMktRecordIdh());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktRecordIdh(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktRecordIdh(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktCompany
    @Test
    void testGetterSetterPlhcMktCompany() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktCompany -> 10 PLHC-MKT-COMPANY
        assertNotNull(wsInterfaz.getPlhcMktCompany());
    }

    // property plhcMktCompanyId
    @Test
    void testGetterSetterPlhcMktCompanyId() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktCompany.plhcMktCompanyId -> 15 PLHC-MKT-COMPANY-ID X(07)
        assertNotNull(wsInterfaz.getPlhcMktCompanyId());
        String vString = "ABCabc1";
        wsInterfaz.setPlhcMktCompanyId(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wsInterfaz.setPlhcMktCompanyId(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktProcessDt
    @Test
    void testGetterSetterPlhcMktProcessDt() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktProcessDt -> 10 PLHC-MKT-PROCESS-DT 9(08)
        assertNotNull(wsInterfaz.getPlhcMktProcessDt());
        int vint = 12345678;
        wsInterfaz.setPlhcMktProcessDt(vint); // int
        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsInterfaz.setPlhcMktProcessDt(vINumericValue); // INumericValue
    }

    // property plhcMktCreationDt
    @Test
    void testGetterSetterPlhcMktCreationDt() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktCreationDt -> 10 PLHC-MKT-CREATION-DT 9(08)
        assertNotNull(wsInterfaz.getPlhcMktCreationDt());
        int vint = 12345678;
        wsInterfaz.setPlhcMktCreationDt(vint); // int
        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsInterfaz.setPlhcMktCreationDt(vINumericValue); // INumericValue
    }

    // property plhcMktRecordCt
    @Test
    void testGetterSetterPlhcMktRecordCt() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktRecordCt -> 10 PLHC-MKT-RECORD-CT 9(06)
        assertNotNull(wsInterfaz.getPlhcMktRecordCt());
        int vint = 123456;
        wsInterfaz.setPlhcMktRecordCt(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        wsInterfaz.setPlhcMktRecordCt(vINumericValue); // INumericValue
    }

    // property plhcMktSortIndh
    @Test
    void testGetterSetterPlhcMktSortIndh() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktSortIndh -> 10 PLHC-MKT-SORT-INDH X(0001)
        assertNotNull(wsInterfaz.getPlhcMktSortIndh());
        String vString = "A";
        wsInterfaz.setPlhcMktSortIndh(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktSortIndh(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktEndrech
    @Test
    void testGetterSetterPlhcMktEndrech() {

        // Property: ggqa00adcpy.plhcMktHeader.plhcMktEndrech -> 10 PLHC-MKT-ENDRECH X(0001)
        assertNotNull(wsInterfaz.getPlhcMktEndrech());
        String vString = "A";
        wsInterfaz.setPlhcMktEndrech(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktEndrech(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktDetail
    @Test
    void testGetterSetterPlhcMktDetail() {

        // Property: ggqa00adcpy.plhcMktDetail -> 05 PLHC-MKT-DETAIL
        assertNotNull(wsInterfaz.getPlhcMktDetail());
    }

    // property plhcMktCompanyD
    @Test
    void testGetterSetterPlhcMktCompanyD() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktCompanyD -> 10 PLHC-MKT-COMPANY-D X(05)
        assertNotNull(wsInterfaz.getPlhcMktCompanyD());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktCompanyD(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktCompanyD(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAgentId
    @Test
    void testGetterSetterPlhcMktAgentId() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAgentId -> 10 PLHC-MKT-AGENT-ID X(05)
        assertNotNull(wsInterfaz.getPlhcMktAgentId());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktAgentId(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktAgentId(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAgencyId
    @Test
    void testGetterSetterPlhcMktAgencyId() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAgencyId -> 10 PLHC-MKT-AGENCY-ID X(03)
        assertNotNull(wsInterfaz.getPlhcMktAgencyId());
        String vString = "ABC";
        wsInterfaz.setPlhcMktAgencyId(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsInterfaz.setPlhcMktAgencyId(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktSsn
    @Test
    void testGetterSetterPlhcMktSsn() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktSsn -> 10 PLHC-MKT-SSN X(13)
        assertNotNull(wsInterfaz.getPlhcMktSsn());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktSsn(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktSsn(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktNasd
    @Test
    void testGetterSetterPlhcMktNasd() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktNasd -> 10 PLHC-MKT-NASD X(10)
        assertNotNull(wsInterfaz.getPlhcMktNasd());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktNasd(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktNasd(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktSex
    @Test
    void testGetterSetterPlhcMktSex() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktSex -> 10 PLHC-MKT-SEX X(01)
        assertNotNull(wsInterfaz.getPlhcMktSex());
        String vString = "A";
        wsInterfaz.setPlhcMktSex(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktSex(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktHireDate
    @Test
    void testGetterSetterPlhcMktHireDate() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktHireDate -> 10 PLHC-MKT-HIRE-DATE 9(08)
        assertNotNull(wsInterfaz.getPlhcMktHireDate());
        int vint = 12345678;
        wsInterfaz.setPlhcMktHireDate(vint); // int
        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsInterfaz.setPlhcMktHireDate(vINumericValue); // INumericValue
    }

    // property plhcMktStatus
    @Test
    void testGetterSetterPlhcMktStatus() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktStatus -> 10 PLHC-MKT-STATUS X(01)
        assertNotNull(wsInterfaz.getPlhcMktStatus());
        String vString = "A";
        wsInterfaz.setPlhcMktStatus(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktStatus(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktTermDate
    @Test
    void testGetterSetterPlhcMktTermDate() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktTermDate -> 10 PLHC-MKT-TERM-DATE 9(08)
        assertNotNull(wsInterfaz.getPlhcMktTermDate());
        int vint = 12345678;
        wsInterfaz.setPlhcMktTermDate(vint); // int
        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsInterfaz.setPlhcMktTermDate(vINumericValue); // INumericValue
    }

    // property plhcMktDob
    @Test
    void testGetterSetterPlhcMktDob() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktDob -> 10 PLHC-MKT-DOB 9(08)
        assertNotNull(wsInterfaz.getPlhcMktDob());
        int vint = 12345678;
        wsInterfaz.setPlhcMktDob(vint); // int
        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsInterfaz.setPlhcMktDob(vINumericValue); // INumericValue
    }

    // property plhcMktCorpName
    @Test
    void testGetterSetterPlhcMktCorpName() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktCorpName -> 10 PLHC-MKT-CORP-NAME X(60)
        assertNotNull(wsInterfaz.getPlhcMktCorpName());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktCorpName(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktCorpName(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktNameLast
    @Test
    void testGetterSetterPlhcMktNameLast() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameLast -> 10 PLHC-MKT-NAME-LAST X(40)
        assertNotNull(wsInterfaz.getPlhcMktNameLast());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktNameLast(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktNameLast(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktNameFirst
    @Test
    void testGetterSetterPlhcMktNameFirst() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameFirst -> 10 PLHC-MKT-NAME-FIRST X(20)
        assertNotNull(wsInterfaz.getPlhcMktNameFirst());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktNameFirst(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktNameFirst(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressOne
    @Test
    void testGetterSetterPlhcMktAddressOne() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressOne -> 10 PLHC-MKT-ADDRESS-ONE X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressOne());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressOne(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressOne(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressTwo
    @Test
    void testGetterSetterPlhcMktAddressTwo() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressTwo -> 10 PLHC-MKT-ADDRESS-TWO X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressTwo());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressTwo(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressTwo(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressThree
    @Test
    void testGetterSetterPlhcMktAddressThree() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressThree -> 10 PLHC-MKT-ADDRESS-THREE X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressThree());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressThree(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressThree(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressFour
    @Test
    void testGetterSetterPlhcMktAddressFour() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressFour -> 10 PLHC-MKT-ADDRESS-FOUR X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressFour());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressFour(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressFour(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressState
    @Test
    void testGetterSetterPlhcMktAddressState() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressState -> 10 PLHC-MKT-ADDRESS-STATE X(03)
        assertNotNull(wsInterfaz.getPlhcMktAddressState());
        String vString = "ABC";
        wsInterfaz.setPlhcMktAddressState(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsInterfaz.setPlhcMktAddressState(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktZipFive
    @Test
    void testGetterSetterPlhcMktZipFive() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktZipFive -> 10 PLHC-MKT-ZIP-FIVE X(05)
        assertNotNull(wsInterfaz.getPlhcMktZipFive());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktZipFive(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktZipFive(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktPdcFive
    @Test
    void testGetterSetterPlhcMktPdcFive() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktPdcFive -> 10 PLHC-MKT-PDC-FIVE X(05)
        assertNotNull(wsInterfaz.getPlhcMktPdcFive());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktPdcFive(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktPdcFive(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktPhone
    @Test
    void testGetterSetterPlhcMktPhone() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktPhone -> 10 PLHC-MKT-PHONE X(14)
        assertNotNull(wsInterfaz.getPlhcMktPhone());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktPhone(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktPhone(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktCorpNamea
    @Test
    void testGetterSetterPlhcMktCorpNamea() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktCorpNamea -> 10 PLHC-MKT-CORP-NAMEA X(60)
        assertNotNull(wsInterfaz.getPlhcMktCorpNamea());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktCorpNamea(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktCorpNamea(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktNameLasta
    @Test
    void testGetterSetterPlhcMktNameLasta() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameLasta -> 10 PLHC-MKT-NAME-LASTA X(40)
        assertNotNull(wsInterfaz.getPlhcMktNameLasta());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktNameLasta(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktNameLasta(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktNameFirsta
    @Test
    void testGetterSetterPlhcMktNameFirsta() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameFirsta -> 10 PLHC-MKT-NAME-FIRSTA X(20)
        assertNotNull(wsInterfaz.getPlhcMktNameFirsta());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktNameFirsta(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktNameFirsta(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressOnea
    @Test
    void testGetterSetterPlhcMktAddressOnea() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressOnea -> 10 PLHC-MKT-ADDRESS-ONEA X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressOnea());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressOnea(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressOnea(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressTwoa
    @Test
    void testGetterSetterPlhcMktAddressTwoa() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressTwoa -> 10 PLHC-MKT-ADDRESS-TWOA X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressTwoa());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressTwoa(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressTwoa(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressThreea
    @Test
    void testGetterSetterPlhcMktAddressThreea() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressThreea -> 10 PLHC-MKT-ADDRESS-THREEA X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressThreea());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressThreea(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressThreea(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressFoura
    @Test
    void testGetterSetterPlhcMktAddressFoura() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressFoura -> 10 PLHC-MKT-ADDRESS-FOURA X(35)
        assertNotNull(wsInterfaz.getPlhcMktAddressFoura());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktAddressFoura(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktAddressFoura(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktAddressStatea
    @Test
    void testGetterSetterPlhcMktAddressStatea() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressStatea -> 10 PLHC-MKT-ADDRESS-STATEA X(03)
        assertNotNull(wsInterfaz.getPlhcMktAddressStatea());
        String vString = "ABC";
        wsInterfaz.setPlhcMktAddressStatea(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsInterfaz.setPlhcMktAddressStatea(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktZipFivea
    @Test
    void testGetterSetterPlhcMktZipFivea() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktZipFivea -> 10 PLHC-MKT-ZIP-FIVEA X(05)
        assertNotNull(wsInterfaz.getPlhcMktZipFivea());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktZipFivea(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktZipFivea(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktPdcFivea
    @Test
    void testGetterSetterPlhcMktPdcFivea() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktPdcFivea -> 10 PLHC-MKT-PDC-FIVEA X(05)
        assertNotNull(wsInterfaz.getPlhcMktPdcFivea());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktPdcFivea(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktPdcFivea(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktPhonea
    @Test
    void testGetterSetterPlhcMktPhonea() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktPhonea -> 10 PLHC-MKT-PHONEA X(14)
        assertNotNull(wsInterfaz.getPlhcMktPhonea());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktPhonea(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktPhonea(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktNasdEffDate
    @Test
    void testGetterSetterPlhcMktNasdEffDate() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktNasdEffDate -> 10 PLHC-MKT-NASD-EFF-DATE 9(08)
        assertNotNull(wsInterfaz.getPlhcMktNasdEffDate());
        int vint = 12345678;
        wsInterfaz.setPlhcMktNasdEffDate(vint); // int
        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsInterfaz.setPlhcMktNasdEffDate(vINumericValue); // INumericValue
    }

    // property plhcMktNasdRenDate
    @Test
    void testGetterSetterPlhcMktNasdRenDate() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktNasdRenDate -> 10 PLHC-MKT-NASD-REN-DATE 9(08)
        assertNotNull(wsInterfaz.getPlhcMktNasdRenDate());
        int vint = 12345678;
        wsInterfaz.setPlhcMktNasdRenDate(vint); // int
        NumericVar vINumericValue = new NumericVar(8, 0);;
        vINumericValue.setValue("12345678");
        wsInterfaz.setPlhcMktNasdRenDate(vINumericValue); // INumericValue
    }

    // property plhcMktLicName
    @Test
    void testGetterSetterPlhcMktLicName() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktLicName -> 10 PLHC-MKT-LIC-NAME X(50)
        assertNotNull(wsInterfaz.getPlhcMktLicName());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktLicName(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktLicName(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktLevel
    @Test
    void testGetterSetterPlhcMktLevel() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktLevel -> 10 PLHC-MKT-LEVEL X(02)
        assertNotNull(wsInterfaz.getPlhcMktLevel());
        String vString = "AB";
        wsInterfaz.setPlhcMktLevel(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsInterfaz.setPlhcMktLevel(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktPayOption
    @Test
    void testGetterSetterPlhcMktPayOption() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktPayOption -> 10 PLHC-MKT-PAY-OPTION X(05)
        assertNotNull(wsInterfaz.getPlhcMktPayOption());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktPayOption(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktPayOption(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktChain
    @Test
    void testGetterSetterPlhcMktChain() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktChain -> 10 PLHC-MKT-CHAIN X(10)
        assertNotNull(wsInterfaz.getPlhcMktChain());
        String vString = "ABCabc1230";
        wsInterfaz.setPlhcMktChain(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsInterfaz.setPlhcMktChain(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktCommIndex
    @Test
    void testGetterSetterPlhcMktCommIndex() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktCommIndex -> 10 PLHC-MKT-COMM-INDEX X(06)
        assertNotNull(wsInterfaz.getPlhcMktCommIndex());
        String vString = "ABCabc";
        wsInterfaz.setPlhcMktCommIndex(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        wsInterfaz.setPlhcMktCommIndex(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktPayMethod
    @Test
    void testGetterSetterPlhcMktPayMethod() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktPayMethod -> 10 PLHC-MKT-PAY-METHOD X(01)
        assertNotNull(wsInterfaz.getPlhcMktPayMethod());
        String vString = "A";
        wsInterfaz.setPlhcMktPayMethod(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktPayMethod(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktPayFreq
    @Test
    void testGetterSetterPlhcMktPayFreq() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktPayFreq -> 10 PLHC-MKT-PAY-FREQ X(02)
        assertNotNull(wsInterfaz.getPlhcMktPayFreq());
        String vString = "AB";
        wsInterfaz.setPlhcMktPayFreq(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsInterfaz.setPlhcMktPayFreq(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktUniqueAgentCode
    @Test
    void testGetterSetterPlhcMktUniqueAgentCode() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktUniqueAgentCode -> 10 PLHC-MKT-UNIQUE-AGENT-CODE X(08)
        assertNotNull(wsInterfaz.getPlhcMktUniqueAgentCode());
        String vString = "ABCabc12";
        wsInterfaz.setPlhcMktUniqueAgentCode(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsInterfaz.setPlhcMktUniqueAgentCode(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktSortInd
    @Test
    void testGetterSetterPlhcMktSortInd() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktSortInd -> 10 PLHC-MKT-SORT-IND X(01)
        assertNotNull(wsInterfaz.getPlhcMktSortInd());
        String vString = "A";
        wsInterfaz.setPlhcMktSortInd(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktSortInd(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktEndrec
    @Test
    void testGetterSetterPlhcMktEndrec() {

        // Property: ggqa00adcpy.plhcMktDetail.plhcMktEndrec -> 10 PLHC-MKT-ENDREC X(01)
        assertNotNull(wsInterfaz.getPlhcMktEndrec());
        String vString = "A";
        wsInterfaz.setPlhcMktEndrec(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktEndrec(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktTrailer
    @Test
    void testGetterSetterPlhcMktTrailer() {

        // Property: ggqa00adcpy.plhcMktTrailer -> 05 PLHC-MKT-TRAILER
        assertNotNull(wsInterfaz.getPlhcMktTrailer());
    }

    // property plhcMktRecordIdt
    @Test
    void testGetterSetterPlhcMktRecordIdt() {

        // Property: ggqa00adcpy.plhcMktTrailer.plhcMktRecordIdt -> 10 PLHC-MKT-RECORD-IDT X(05)
        assertNotNull(wsInterfaz.getPlhcMktRecordIdt());
        String vString = "ABCab";
        wsInterfaz.setPlhcMktRecordIdt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsInterfaz.setPlhcMktRecordIdt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktCompanyT
    @Test
    void testGetterSetterPlhcMktCompanyT() {

        // Property: ggqa00adcpy.plhcMktTrailer.plhcMktCompanyT -> 10 PLHC-MKT-COMPANY-T
        assertNotNull(wsInterfaz.getPlhcMktCompanyT());
    }

    // property plhcMktCompanyIdT
    @Test
    void testGetterSetterPlhcMktCompanyIdT() {

        // Property: ggqa00adcpy.plhcMktTrailer.plhcMktCompanyT.plhcMktCompanyIdT -> 15 PLHC-MKT-COMPANY-ID-T X(03)
        assertNotNull(wsInterfaz.getPlhcMktCompanyIdT());
        String vString = "ABC";
        wsInterfaz.setPlhcMktCompanyIdT(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsInterfaz.setPlhcMktCompanyIdT(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktRecordCount
    @Test
    void testGetterSetterPlhcMktRecordCount() {

        // Property: ggqa00adcpy.plhcMktTrailer.plhcMktRecordCount -> 10 PLHC-MKT-RECORD-COUNT 9(06)
        assertNotNull(wsInterfaz.getPlhcMktRecordCount());
        int vint = 123456;
        wsInterfaz.setPlhcMktRecordCount(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        wsInterfaz.setPlhcMktRecordCount(vINumericValue); // INumericValue
    }

    // property plhcMktSortIndt
    @Test
    void testGetterSetterPlhcMktSortIndt() {

        // Property: ggqa00adcpy.plhcMktTrailer.plhcMktSortIndt -> 10 PLHC-MKT-SORT-INDT X(0001)
        assertNotNull(wsInterfaz.getPlhcMktSortIndt());
        String vString = "A";
        wsInterfaz.setPlhcMktSortIndt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktSortIndt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property plhcMktEndrect
    @Test
    void testGetterSetterPlhcMktEndrect() {

        // Property: ggqa00adcpy.plhcMktTrailer.plhcMktEndrect -> 10 PLHC-MKT-ENDRECT X(0001)
        assertNotNull(wsInterfaz.getPlhcMktEndrect());
        String vString = "A";
        wsInterfaz.setPlhcMktEndrect(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsInterfaz.setPlhcMktEndrect(vIAlphanumericValue); // IAlphanumericValue
    }
}

