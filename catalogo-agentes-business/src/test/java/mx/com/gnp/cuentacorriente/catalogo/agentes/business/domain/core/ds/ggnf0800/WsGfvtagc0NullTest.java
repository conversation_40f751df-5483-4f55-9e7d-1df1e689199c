package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTAGC0-NULL.
 *
 */
class WsGfvtagc0NullTest {

    private WsGfvtagc0Null wsGfvtagc0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtagc0Null = new WsGfvtagc0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtagc0Null.setAgc01(0);
        assertEquals(0, wsGfvtagc0Null.getAgc01());

        wsGfvtagc0Null.setAgc02(0);
        assertEquals(0, wsGfvtagc0Null.getAgc02());

        wsGfvtagc0Null.setAgc03(0);
        assertEquals(0, wsGfvtagc0Null.getAgc03());

        wsGfvtagc0Null.setAgc04(0);
        assertEquals(0, wsGfvtagc0Null.getAgc04());

        wsGfvtagc0Null.setAgc05(0);
        assertEquals(0, wsGfvtagc0Null.getAgc05());

        wsGfvtagc0Null.setAgc06(0);
        assertEquals(0, wsGfvtagc0Null.getAgc06());

        wsGfvtagc0Null.setAgc07(0);
        assertEquals(0, wsGfvtagc0Null.getAgc07());

        wsGfvtagc0Null.setAgc08(0);
        assertEquals(0, wsGfvtagc0Null.getAgc08());

        wsGfvtagc0Null.setAgc09(0);
        assertEquals(0, wsGfvtagc0Null.getAgc09());

        wsGfvtagc0Null.setAgc10(0);
        assertEquals(0, wsGfvtagc0Null.getAgc10());

        wsGfvtagc0Null.setAgc11(0);
        assertEquals(0, wsGfvtagc0Null.getAgc11());

        wsGfvtagc0Null.setAgc12(0);
        assertEquals(0, wsGfvtagc0Null.getAgc12());

    }

    // property agc01
    @Test
    void testGetterSetterAgc01() {

        assertNotNull(wsGfvtagc0Null.getAgc01());

        int vint = 1234;
        wsGfvtagc0Null.setAgc01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc01()));
    }

    // property agc02
    @Test
    void testGetterSetterAgc02() {

        assertNotNull(wsGfvtagc0Null.getAgc02());

        int vint = 1234;
        wsGfvtagc0Null.setAgc02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc02()));
    }

    // property agc03
    @Test
    void testGetterSetterAgc03() {

        assertNotNull(wsGfvtagc0Null.getAgc03());

        int vint = 1234;
        wsGfvtagc0Null.setAgc03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc03()));
    }

    // property agc04
    @Test
    void testGetterSetterAgc04() {

        assertNotNull(wsGfvtagc0Null.getAgc04());

        int vint = 1234;
        wsGfvtagc0Null.setAgc04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc04()));
    }

    // property agc05
    @Test
    void testGetterSetterAgc05() {

        assertNotNull(wsGfvtagc0Null.getAgc05());

        int vint = 1234;
        wsGfvtagc0Null.setAgc05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc05()));
    }

    // property agc06
    @Test
    void testGetterSetterAgc06() {

        assertNotNull(wsGfvtagc0Null.getAgc06());

        int vint = 1234;
        wsGfvtagc0Null.setAgc06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc06()));
    }

    // property agc07
    @Test
    void testGetterSetterAgc07() {

        assertNotNull(wsGfvtagc0Null.getAgc07());

        int vint = 1234;
        wsGfvtagc0Null.setAgc07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc07()));
    }

    // property agc08
    @Test
    void testGetterSetterAgc08() {

        assertNotNull(wsGfvtagc0Null.getAgc08());

        int vint = 1234;
        wsGfvtagc0Null.setAgc08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc08()));
    }

    // property agc09
    @Test
    void testGetterSetterAgc09() {

        assertNotNull(wsGfvtagc0Null.getAgc09());

        int vint = 1234;
        wsGfvtagc0Null.setAgc09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc09()));
    }

    // property agc10
    @Test
    void testGetterSetterAgc10() {

        assertNotNull(wsGfvtagc0Null.getAgc10());

        int vint = 1234;
        wsGfvtagc0Null.setAgc10(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc10()));
    }

    // property agc11
    @Test
    void testGetterSetterAgc11() {

        assertNotNull(wsGfvtagc0Null.getAgc11());

        int vint = 1234;
        wsGfvtagc0Null.setAgc11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc11()));
    }

    // property agc12
    @Test
    void testGetterSetterAgc12() {

        assertNotNull(wsGfvtagc0Null.getAgc12());

        int vint = 1234;
        wsGfvtagc0Null.setAgc12(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagc0Null.getAgc12()));
    }
}

