package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

class Ggqacb00Test {
    Ggqacb00 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggqacb00.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p2000Proceso test conditions:
        indcnactbnum : [-1, 0]
        sqlcode : [100, 101]
    */
    @Test
    void p2000ProcesoTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getIndicadoresDeNulos().setIndcnactbnum(-1);
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p2000Proceso();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2000ProcesoTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getIndicadoresDeNulos().setIndcnactbnum(0);
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p2000Proceso();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2120FetchCurCna0 test conditions:
        indcnactbnum : [-1, 0]
        sqlcode : [100, 101]
    */
    @Test
    void p2120FetchCurCna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getIndicadoresDeNulos().setIndcnactbnum(-1);
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p2120FetchCurCna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2120FetchCurCna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getIndicadoresDeNulos().setIndcnactbnum(0);
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p2120FetchCurCna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2150CloseCurCna0 test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p2150CloseCurCna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p2150CloseCurCna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2150CloseCurCna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p2150CloseCurCna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p2150CloseCurCna0Test3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p2150CloseCurCna0();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p2150CloseCurCna0Test4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p2150CloseCurCna0();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p999VerErrSql test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p999VerErrSqlTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p999VerErrSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p999VerErrSqlTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p999VerErrSql();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p999VerErrSqlTest3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p999VerErrSql();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p999VerErrSqlTest4() {
                    //TODO setup test
                    pgm.sqlDelayedParagraph();
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p999VerErrSql();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
}
