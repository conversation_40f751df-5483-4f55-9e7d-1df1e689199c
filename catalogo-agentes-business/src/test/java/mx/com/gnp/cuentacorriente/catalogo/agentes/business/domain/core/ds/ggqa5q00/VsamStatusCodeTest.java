package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: VSAM-STATUS-CODE.
 *
 */
class VsamStatusCodeTest {

    private VsamStatusCode vsamStatusCode;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        vsamStatusCode = new VsamStatusCode();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        vsamStatusCode.setVsamReturnCode(0);
        assertEquals(0, vsamStatusCode.getVsamReturnCode());

        vsamStatusCode.setVsamFunctionCode(0);
        assertEquals(0, vsamStatusCode.getVsamFunctionCode());

        vsamStatusCode.setVsamFeedbackCode(0);
        assertEquals(0, vsamStatusCode.getVsamFeedbackCode());

    }

    // property vsamReturnCode
    @Test
    void testGetterSetterVsamReturnCode() {

        assertNotNull(vsamStatusCode.getVsamReturnCode());

        int vint = 1234;
        vsamStatusCode.setVsamReturnCode(vint); // int
        assertEquals(asInt(vint), asInt(vsamStatusCode.getVsamReturnCode()));
    }

    // property vsamFunctionCode
    @Test
    void testGetterSetterVsamFunctionCode() {

        assertNotNull(vsamStatusCode.getVsamFunctionCode());

        int vint = 1234;
        vsamStatusCode.setVsamFunctionCode(vint); // int
        assertEquals(asInt(vint), asInt(vsamStatusCode.getVsamFunctionCode()));
    }

    // property vsamFeedbackCode
    @Test
    void testGetterSetterVsamFeedbackCode() {

        assertNotNull(vsamStatusCode.getVsamFeedbackCode());

        int vint = 1234;
        vsamStatusCode.setVsamFeedbackCode(vint); // int
        assertEquals(asInt(vint), asInt(vsamStatusCode.getVsamFeedbackCode()));
    }
}

