package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTFLA1-NULL.
 *
 */
class WsGfvtfla1NullTest {

    private WsGfvtfla1Null wsGfvtfla1Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtfla1Null = new WsGfvtfla1Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtfla1Null.setFla011(0);
        assertEquals(0, wsGfvtfla1Null.getFla011());

        wsGfvtfla1Null.setFla022(0);
        assertEquals(0, wsGfvtfla1Null.getFla022());

        wsGfvtfla1Null.setFla033(0);
        assertEquals(0, wsGfvtfla1Null.getFla033());

        wsGfvtfla1Null.setFla044(0);
        assertEquals(0, wsGfvtfla1Null.getFla044());

        wsGfvtfla1Null.setFla055(0);
        assertEquals(0, wsGfvtfla1Null.getFla055());

        wsGfvtfla1Null.setFla066(0);
        assertEquals(0, wsGfvtfla1Null.getFla066());

        wsGfvtfla1Null.setFla077(0);
        assertEquals(0, wsGfvtfla1Null.getFla077());

        wsGfvtfla1Null.setFla088(0);
        assertEquals(0, wsGfvtfla1Null.getFla088());

        wsGfvtfla1Null.setFla099(0);
        assertEquals(0, wsGfvtfla1Null.getFla099());

        wsGfvtfla1Null.setFla100(0);
        assertEquals(0, wsGfvtfla1Null.getFla100());

        wsGfvtfla1Null.setFla111(0);
        assertEquals(0, wsGfvtfla1Null.getFla111());

        wsGfvtfla1Null.setFla122(0);
        assertEquals(0, wsGfvtfla1Null.getFla122());

        wsGfvtfla1Null.setFla133(0);
        assertEquals(0, wsGfvtfla1Null.getFla133());

    }

    // property fla011
    @Test
    void testGetterSetterFla011() {

        assertNotNull(wsGfvtfla1Null.getFla011());

        int vint = 1234;
        wsGfvtfla1Null.setFla011(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla011()));
    }

    // property fla022
    @Test
    void testGetterSetterFla022() {

        assertNotNull(wsGfvtfla1Null.getFla022());

        int vint = 1234;
        wsGfvtfla1Null.setFla022(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla022()));
    }

    // property fla033
    @Test
    void testGetterSetterFla033() {

        assertNotNull(wsGfvtfla1Null.getFla033());

        int vint = 1234;
        wsGfvtfla1Null.setFla033(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla033()));
    }

    // property fla044
    @Test
    void testGetterSetterFla044() {

        assertNotNull(wsGfvtfla1Null.getFla044());

        int vint = 1234;
        wsGfvtfla1Null.setFla044(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla044()));
    }

    // property fla055
    @Test
    void testGetterSetterFla055() {

        assertNotNull(wsGfvtfla1Null.getFla055());

        int vint = 1234;
        wsGfvtfla1Null.setFla055(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla055()));
    }

    // property fla066
    @Test
    void testGetterSetterFla066() {

        assertNotNull(wsGfvtfla1Null.getFla066());

        int vint = 1234;
        wsGfvtfla1Null.setFla066(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla066()));
    }

    // property fla077
    @Test
    void testGetterSetterFla077() {

        assertNotNull(wsGfvtfla1Null.getFla077());

        int vint = 1234;
        wsGfvtfla1Null.setFla077(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla077()));
    }

    // property fla088
    @Test
    void testGetterSetterFla088() {

        assertNotNull(wsGfvtfla1Null.getFla088());

        int vint = 1234;
        wsGfvtfla1Null.setFla088(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla088()));
    }

    // property fla099
    @Test
    void testGetterSetterFla099() {

        assertNotNull(wsGfvtfla1Null.getFla099());

        int vint = 1234;
        wsGfvtfla1Null.setFla099(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla099()));
    }

    // property fla100
    @Test
    void testGetterSetterFla100() {

        assertNotNull(wsGfvtfla1Null.getFla100());

        int vint = 1234;
        wsGfvtfla1Null.setFla100(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla100()));
    }

    // property fla111
    @Test
    void testGetterSetterFla111() {

        assertNotNull(wsGfvtfla1Null.getFla111());

        int vint = 1234;
        wsGfvtfla1Null.setFla111(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla111()));
    }

    // property fla122
    @Test
    void testGetterSetterFla122() {

        assertNotNull(wsGfvtfla1Null.getFla122());

        int vint = 1234;
        wsGfvtfla1Null.setFla122(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla122()));
    }

    // property fla133
    @Test
    void testGetterSetterFla133() {

        assertNotNull(wsGfvtfla1Null.getFla133());

        int vint = 1234;
        wsGfvtfla1Null.setFla133(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla1Null.getFla133()));
    }
}

