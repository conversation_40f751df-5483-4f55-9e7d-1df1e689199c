package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-VARIABLES-DB2.
 *
 */
class WsVariablesDb2Test {

    private WsVariablesDb2 wsVariablesDb2;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsVariablesDb2 = new WsVariablesDb2();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {



    }

    // property agt0
    @Test
    void testGetterSetterAgt0() {

        assertNotNull(wsVariablesDb2.getAgt0());
    }

    // property cna0
    @Test
    void testGetterSetterCna0() {

        assertNotNull(wsVariablesDb2.getCna0());
    }

    // property fla0
    @Test
    void testGetterSetterFla0() {

        assertNotNull(wsVariablesDb2.getFla0());
    }

    // property wsAgtAnt
    @Test
    void testGetterSetterWsAgtAnt() {

        // Property: agt0.wsAgtAnt -> 10 WS-AGT-ANT S9(9) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getWsAgtAnt());
        int vint = 123456789;
        wsVariablesDb2.setWsAgtAnt(vint); // int
    }

    // property agtAgtIdr
    @Test
    void testGetterSetterAgtAgtIdr() {

        // Property: agt0.agtAgtIdr -> 10 AGT-AGT-IDR S9(9) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getAgtAgtIdr());
        int vint = 123456789;
        wsVariablesDb2.setAgtAgtIdr(vint); // int
    }

    // property agtAgtNom
    @Test
    void testGetterSetterAgtAgtNom() {

        // Property: agt0.agtAgtNom -> 10 AGT-AGT-NOM X(40)
        assertNotNull(wsVariablesDb2.getAgtAgtNom());
        String vString = "ABCabc1230";
        wsVariablesDb2.setAgtAgtNom(vString); // String
    }

    // property agtAgtApePat
    @Test
    void testGetterSetterAgtAgtApePat() {

        // Property: agt0.agtAgtApePat -> 10 AGT-AGT-APE-PAT X(40)
        assertNotNull(wsVariablesDb2.getAgtAgtApePat());
        String vString = "ABCabc1230";
        wsVariablesDb2.setAgtAgtApePat(vString); // String
    }

    // property agtAgtApeMat
    @Test
    void testGetterSetterAgtAgtApeMat() {

        // Property: agt0.agtAgtApeMat -> 10 AGT-AGT-APE-MAT X(30)
        assertNotNull(wsVariablesDb2.getAgtAgtApeMat());
        String vString = "ABCabc1230";
        wsVariablesDb2.setAgtAgtApeMat(vString); // String
    }

    // property agtAgtNip
    @Test
    void testGetterSetterAgtAgtNip() {

        // Property: agt0.agtAgtNip -> 10 AGT-AGT-NIP S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getAgtAgtNip());
        int vint = 1234;
        wsVariablesDb2.setAgtAgtNip(vint); // int
    }

    // property cmaCmaDes
    @Test
    void testGetterSetterCmaCmaDes() {

        // Property: agt0.cmaCmaDes -> 10 CMA-CMA-DES X(60)
        assertNotNull(wsVariablesDb2.getCmaCmaDes());
        String vString = "ABCabc1230";
        wsVariablesDb2.setCmaCmaDes(vString); // String
    }

    // property wsIndGerente
    @Test
    void testGetterSetterWsIndGerente() {

        // Property: agt0.wsIndGerente -> 10 WS-IND-GERENTE X(07)
        assertNotNull(wsVariablesDb2.getWsIndGerente());
        String vString = "ABCabc1";
        wsVariablesDb2.setWsIndGerente(vString); // String
    }

    // property wsOrvCve
    @Test
    void testGetterSetterWsOrvCve() {

        // Property: agt0.wsOrvCve -> 10 WS-ORV-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getWsOrvCve());
        int vint = 1234;
        wsVariablesDb2.setWsOrvCve(vint); // int
    }

    // property agtRazSoc
    @Test
    void testGetterSetterAgtRazSoc() {

        // Property: agt0.agtRazSoc -> 10 AGT-RAZ-SOC X(60)
        assertNotNull(wsVariablesDb2.getAgtRazSoc());
        String vString = "ABCabc1230";
        wsVariablesDb2.setAgtRazSoc(vString); // String
    }

    // property agtPefCve
    @Test
    void testGetterSetterAgtPefCve() {

        // Property: agt0.agtPefCve -> 10 AGT-PEF-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getAgtPefCve());
        int vint = 1234;
        wsVariablesDb2.setAgtPefCve(vint); // int
    }

    // property cnaCnaNum
    @Test
    void testGetterSetterCnaCnaNum() {

        // Property: cna0.cnaCnaNum -> 10 CNA-CNA-NUM S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getCnaCnaNum());
        int vint = 1234;
        wsVariablesDb2.setCnaCnaNum(vint); // int
    }

    // property wsCnaCnaNum
    @Test
    void testGetterSetterWsCnaCnaNum() {

        // Property: cna0.wsCnaCnaNum -> 10 WS-CNA-CNA-NUM 9(02)
        assertNotNull(wsVariablesDb2.getWsCnaCnaNum());
        int vint = 12;
        wsVariablesDb2.setWsCnaCnaNum(vint); // int
    }

    // property cnaGerZonEnv
    @Test
    void testGetterSetterCnaGerZonEnv() {

        // Property: cna0.cnaGerZonEnv -> 10 CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getCnaGerZonEnv());
        int vint = 1234;
        wsVariablesDb2.setCnaGerZonEnv(vint); // int
    }

    // property wsCnaGerZon
    @Test
    void testGetterSetterWsCnaGerZon() {

        // Property: cna0.wsCnaGerZon -> 10 WS-CNA-GER-ZON 9(04)
        assertNotNull(wsVariablesDb2.getWsCnaGerZon());
        int vint = 1234;
        wsVariablesDb2.setWsCnaGerZon(vint); // int
    }

    // property cnaOfnCve
    @Test
    void testGetterSetterCnaOfnCve() {

        // Property: cna0.cnaOfnCve -> 10 CNA-OFN-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getCnaOfnCve());
        int vint = 1234;
        wsVariablesDb2.setCnaOfnCve(vint); // int
    }

    // property wsCnaOfnCve
    @Test
    void testGetterSetterWsCnaOfnCve() {

        // Property: cna0.wsCnaOfnCve -> 10 WS-CNA-OFN-CVE 9(04)
        assertNotNull(wsVariablesDb2.getWsCnaOfnCve());
        int vint = 1234;
        wsVariablesDb2.setWsCnaOfnCve(vint); // int
    }

    // property cnaEmpCve
    @Test
    void testGetterSetterCnaEmpCve() {

        // Property: cna0.cnaEmpCve -> 10 CNA-EMP-CVE X(05)
        assertNotNull(wsVariablesDb2.getCnaEmpCve());
        String vString = "ABCab";
        wsVariablesDb2.setCnaEmpCve(vString); // String
    }

    // property cnaTfpCve
    @Test
    void testGetterSetterCnaTfpCve() {

        // Property: cna0.cnaTfpCve -> 10 CNA-TFP-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getCnaTfpCve());
        int vint = 1234;
        wsVariablesDb2.setCnaTfpCve(vint); // int
    }

    // property ceoCeoCve
    @Test
    void testGetterSetterCeoCeoCve() {

        // Property: cna0.ceoCeoCve -> 10 CEO-CEO-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsVariablesDb2.getCeoCeoCve());
        int vint = 1234;
        wsVariablesDb2.setCeoCeoCve(vint); // int
    }

    // property wsCeoCeoCve
    @Test
    void testGetterSetterWsCeoCeoCve() {

        // Property: cna0.wsCeoCeoCve -> 10 WS-CEO-CEO-CVE 9(04)
        assertNotNull(wsVariablesDb2.getWsCeoCeoCve());
        int vint = 1234;
        wsVariablesDb2.setWsCeoCeoCve(vint); // int
    }

    // property ceoCeoNom
    @Test
    void testGetterSetterCeoCeoNom() {

        // Property: cna0.ceoCeoNom -> 10 CEO-CEO-NOM X(30)
        assertNotNull(wsVariablesDb2.getCeoCeoNom());
        String vString = "ABCabc1230";
        wsVariablesDb2.setCeoCeoNom(vString); // String
    }

    // property wsCeoCeoNom
    @Test
    void testGetterSetterWsCeoCeoNom() {

        // Property: cna0.wsCeoCeoNom -> 10 WS-CEO-CEO-NOM X(30)
        assertNotNull(wsVariablesDb2.getWsCeoCeoNom());
        String vString = "ABCabc1230";
        wsVariablesDb2.setWsCeoCeoNom(vString); // String
    }

    // property flaFlaNum
    @Test
    void testGetterSetterFlaFlaNum() {

        // Property: fla0.flaFlaNum -> 10 FLA-FLA-NUM X(08)
        assertNotNull(wsVariablesDb2.getFlaFlaNum());
        String vString = "ABCabc12";
        wsVariablesDb2.setFlaFlaNum(vString); // String
    }
}

