package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.string.StringUtils;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CAMPOS-TRABAJO.
 *
 */
class WsCamposTrabajoTest {

    private WsCamposTrabajo wsCamposTrabajo;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCamposTrabajo = new WsCamposTrabajo();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsCamposTrabajo.getS999Err());

        wsCamposTrabajo.setWsAgtIdrAux(0);
        assertEquals(0, wsCamposTrabajo.getWsAgtIdrAux());

        wsCamposTrabajo.setWsAgtIdrAux3(0);
        assertEquals(0, wsCamposTrabajo.getWsAgtIdrAux3());

        wsCamposTrabajo.setWsAgtIdrAux2(0);
        assertEquals(0, wsCamposTrabajo.getWsAgtIdrAux2());

        wsCamposTrabajo.setWsIndPag("");
        assertEquals("", wsCamposTrabajo.getWsIndPag());

        wsCamposTrabajo.setWnNro(0);
        assertEquals(0, wsCamposTrabajo.getWnNro());

        wsCamposTrabajo.setWnNull(0);
        assertEquals(0, wsCamposTrabajo.getWnNull());

        wsCamposTrabajo.setWsDraCve(0);
        assertEquals(0, wsCamposTrabajo.getWsDraCve());

        assertTrue(wsCamposTrabajo.getWsAgtInsert().isZero());

        assertTrue(wsCamposTrabajo.getWsAgtUpdate().isZero());

        wsCamposTrabajo.setWsEsaCve(0);
        assertEquals(0, wsCamposTrabajo.getWsEsaCve());

        wsCamposTrabajo.setWsEsaDes("");
        assertEquals("", wsCamposTrabajo.getWsEsaDes());

        wsCamposTrabajo.setWsFecMas_1("");
        assertEquals("", wsCamposTrabajo.getWsFecMas_1());

        wsCamposTrabajo.setWsFecHfa(StringUtils.all("0", 10));
        assertEquals(StringUtils.all("0", 10), wsCamposTrabajo.getWsFecHfa());

        wsCamposTrabajo.setWsIndExcAgt("");
        assertEquals("", wsCamposTrabajo.getWsIndExcAgt());

        assertNotNull(wsCamposTrabajo.getWsPjeCedNovi());

        assertNotNull(wsCamposTrabajo.getWsPjeCedVida());






    }

    // property s999Err
    @Test
    void testGetterSetterS999Err() {

        assertNotNull(wsCamposTrabajo.getS999Err());

        String vString = "A";
        wsCamposTrabajo.setS999Err(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getS999Err()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCamposTrabajo.setS999Err(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCamposTrabajo.getS999Err()));
    }

    // property wsAgtIdrAux
    @Test
    void testGetterSetterWsAgtIdrAux() {

        assertNotNull(wsCamposTrabajo.getWsAgtIdrAux());

        int vint = 12345;
        wsCamposTrabajo.setWsAgtIdrAux(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsAgtIdrAux()));
    }

    // property wsAgtIdrAux3
    @Test
    void testGetterSetterWsAgtIdrAux3() {

        assertNotNull(wsCamposTrabajo.getWsAgtIdrAux3());

        int vint = 12345;
        wsCamposTrabajo.setWsAgtIdrAux3(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsAgtIdrAux3()));
    }

    // property wsAgtIdrAux2
    @Test
    void testGetterSetterWsAgtIdrAux2() {

        assertNotNull(wsCamposTrabajo.getWsAgtIdrAux2());

        int vint = 123456789;
        wsCamposTrabajo.setWsAgtIdrAux2(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsAgtIdrAux2()));
    }

    // property wsIndPag
    @Test
    void testGetterSetterWsIndPag() {

        assertNotNull(wsCamposTrabajo.getWsIndPag());

        String vString = "A";
        wsCamposTrabajo.setWsIndPag(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsIndPag()));
    }

    // property wnNro
    @Test
    void testGetterSetterWnNro() {

        assertNotNull(wsCamposTrabajo.getWnNro());

        int vint = 1234;
        wsCamposTrabajo.setWnNro(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWnNro()));
    }

    // property wnNull
    @Test
    void testGetterSetterWnNull() {

        assertNotNull(wsCamposTrabajo.getWnNull());

        int vint = 1234;
        wsCamposTrabajo.setWnNull(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWnNull()));
    }

    // property wsDraCve
    @Test
    void testGetterSetterWsDraCve() {

        assertNotNull(wsCamposTrabajo.getWsDraCve());

        int vint = 1234;
        wsCamposTrabajo.setWsDraCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsDraCve()));
    }

    // property wsAgtInsert
    @Test
    void testGetterSetterWsAgtInsert() {

        assertNotNull(wsCamposTrabajo.getWsAgtInsert());

        BigDecimal vBigDecimal = new BigDecimal("1234567890");
        wsCamposTrabajo.setWsAgtInsert(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsCamposTrabajo.getWsAgtInsert().toBigDecimal());

        int vint = 123456789;
        wsCamposTrabajo.setWsAgtInsert(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsAgtInsert()));

        NumericVar vINumericValue = new NumericVar(10, 0);;
        vINumericValue.setValue("1234567890");
        wsCamposTrabajo.setWsAgtInsert(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCamposTrabajo.getWsAgtInsert()));
    }

    // property wsAgtUpdate
    @Test
    void testGetterSetterWsAgtUpdate() {

        assertNotNull(wsCamposTrabajo.getWsAgtUpdate());

        BigDecimal vBigDecimal = new BigDecimal("1234567890");
        wsCamposTrabajo.setWsAgtUpdate(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsCamposTrabajo.getWsAgtUpdate().toBigDecimal());

        int vint = 123456789;
        wsCamposTrabajo.setWsAgtUpdate(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsAgtUpdate()));

        NumericVar vINumericValue = new NumericVar(10, 0);;
        vINumericValue.setValue("1234567890");
        wsCamposTrabajo.setWsAgtUpdate(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCamposTrabajo.getWsAgtUpdate()));
    }

    // property wsEsaCve
    @Test
    void testGetterSetterWsEsaCve() {

        assertNotNull(wsCamposTrabajo.getWsEsaCve());

        int vint = 1234;
        wsCamposTrabajo.setWsEsaCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsEsaCve()));
    }

    // property wsEsaDes
    @Test
    void testGetterSetterWsEsaDes() {

        assertNotNull(wsCamposTrabajo.getWsEsaDes());

        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsEsaDes(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsEsaDes()));
    }

    // property wsFecMas_1
    @Test
    void testGetterSetterWsFecMas_1() {

        assertNotNull(wsCamposTrabajo.getWsFecMas_1());

        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsFecMas_1(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsFecMas_1()));
    }

    // property wsFecHfa
    @Test
    void testGetterSetterWsFecHfa() {

        assertNotNull(wsCamposTrabajo.getWsFecHfa());

        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsFecHfa(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsFecHfa()));
    }

    // property wsIndExcAgt
    @Test
    void testGetterSetterWsIndExcAgt() {

        assertNotNull(wsCamposTrabajo.getWsIndExcAgt());

        String vString = "A";
        wsCamposTrabajo.setWsIndExcAgt(vString); // String
        assertEquals(asStr(vString), asStr(wsCamposTrabajo.getWsIndExcAgt()));
    }

    // property wsPjeCedNovi
    @Test
    void testGetterSetterWsPjeCedNovi() {

        assertNotNull(wsCamposTrabajo.getWsPjeCedNovi());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsPjeCedNovi(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsCamposTrabajo.getWsPjeCedNovi().toBigDecimal());

        int vint = 123;
        wsCamposTrabajo.setWsPjeCedNovi(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsPjeCedNovi()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsPjeCedNovi(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCamposTrabajo.getWsPjeCedNovi()));
    }

    // property wsPjeCedVida
    @Test
    void testGetterSetterWsPjeCedVida() {

        assertNotNull(wsCamposTrabajo.getWsPjeCedVida());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsPjeCedVida(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsCamposTrabajo.getWsPjeCedVida().toBigDecimal());

        int vint = 123;
        wsCamposTrabajo.setWsPjeCedVida(vint); // int
        assertEquals(asInt(vint), asInt(wsCamposTrabajo.getWsPjeCedVida()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsPjeCedVida(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCamposTrabajo.getWsPjeCedVida()));
    }

    // property wsHfaReg
    @Test
    void testGetterSetterWsHfaReg() {

        assertNotNull(wsCamposTrabajo.getWsHfaReg());
    }

    // property wsAgtReg
    @Test
    void testGetterSetterWsAgtReg() {

        assertNotNull(wsCamposTrabajo.getWsAgtReg());
    }

    // property wsDatosGralAgt
    @Test
    void testGetterSetterWsDatosGralAgt() {

        assertNotNull(wsCamposTrabajo.getWsDatosGralAgt());
    }

    // property wsCnaAgtReg
    @Test
    void testGetterSetterWsCnaAgtReg() {

        assertNotNull(wsCamposTrabajo.getWsCnaAgtReg());
    }

    // property wsTabAgtReg
    @Test
    void testGetterSetterWsTabAgtReg() {

        assertNotNull(wsCamposTrabajo.getWsTabAgtReg());
    }

    // property wsHfaAgtIdr
    @Test
    void testGetterSetterWsHfaAgtIdr() {

        // Property: wsHfaReg.wsHfaAgtIdr -> 10 WS-HFA-AGT-IDR S9(09) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsHfaAgtIdr());
        int vint = 123456789;
        wsCamposTrabajo.setWsHfaAgtIdr(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCamposTrabajo.setWsHfaAgtIdr(vINumericValue); // INumericValue
    }

    // property wsHfaFecIniPdo
    @Test
    void testGetterSetterWsHfaFecIniPdo() {

        // Property: wsHfaReg.wsHfaFecIniPdo -> 10 WS-HFA-FEC-INI-PDO X(10)
        assertNotNull(wsCamposTrabajo.getWsHfaFecIniPdo());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaFecIniPdo(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaFecIniPdo(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaFecFinPdo
    @Test
    void testGetterSetterWsHfaFecFinPdo() {

        // Property: wsHfaReg.wsHfaFecFinPdo -> 10 WS-HFA-FEC-FIN-PDO X(10)
        assertNotNull(wsCamposTrabajo.getWsHfaFecFinPdo());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaFecFinPdo(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaFecFinPdo(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaEsaCve
    @Test
    void testGetterSetterWsHfaEsaCve() {

        // Property: wsHfaReg.wsHfaEsaCve -> 10 WS-HFA-ESA-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaEsaCve());
        int vint = 1234;
        wsCamposTrabajo.setWsHfaEsaCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfaEsaCve(vINumericValue); // INumericValue
    }

    // property wsHfaPefCve
    @Test
    void testGetterSetterWsHfaPefCve() {

        // Property: wsHfaReg.wsHfaPefCve -> 10 WS-HFA-PEF-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaPefCve());
        int vint = 1234;
        wsCamposTrabajo.setWsHfaPefCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfaPefCve(vINumericValue); // INumericValue
    }

    // property wsHfaOfnCve
    @Test
    void testGetterSetterWsHfaOfnCve() {

        // Property: wsHfaReg.wsHfaOfnCve -> 10 WS-HFA-OFN-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaOfnCve());
        int vint = 1234;
        wsCamposTrabajo.setWsHfaOfnCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfaOfnCve(vINumericValue); // INumericValue
    }

    // property wsHfaCnaGerZonEnv
    @Test
    void testGetterSetterWsHfaCnaGerZonEnv() {

        // Property: wsHfaReg.wsHfaCnaGerZonEnv -> 10 WS-HFA-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaCnaGerZonEnv());
        int vint = 1234;
        wsCamposTrabajo.setWsHfaCnaGerZonEnv(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfaCnaGerZonEnv(vINumericValue); // INumericValue
    }

    // property wsHfaDsgCve
    @Test
    void testGetterSetterWsHfaDsgCve() {

        // Property: wsHfaReg.wsHfaDsgCve -> 10 WS-HFA-DSG-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaDsgCve());
        int vint = 1234;
        wsCamposTrabajo.setWsHfaDsgCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfaDsgCve(vINumericValue); // INumericValue
    }

    // property wsHfaAgtRfc
    @Test
    void testGetterSetterWsHfaAgtRfc() {

        // Property: wsHfaReg.wsHfaAgtRfc -> 10 WS-HFA-AGT-RFC X(13)
        assertNotNull(wsCamposTrabajo.getWsHfaAgtRfc());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaAgtRfc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaAgtRfc(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaNomEmi
    @Test
    void testGetterSetterWsHfaNomEmi() {

        // Property: wsHfaReg.wsHfaNomEmi -> 10 WS-HFA-NOM-EMI X(100)
        assertNotNull(wsCamposTrabajo.getWsHfaNomEmi());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaNomEmi(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaNomEmi(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaDmaCae
    @Test
    void testGetterSetterWsHfaDmaCae() {

        // Property: wsHfaReg.wsHfaDmaCae -> 10 WS-HFA-DMA-CAE X(40)
        assertNotNull(wsCamposTrabajo.getWsHfaDmaCae());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaDmaCae(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaDmaCae(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaDmaNum
    @Test
    void testGetterSetterWsHfaDmaNum() {

        // Property: wsHfaReg.wsHfaDmaNum -> 10 WS-HFA-DMA-NUM X(05)
        assertNotNull(wsCamposTrabajo.getWsHfaDmaNum());
        String vString = "ABCab";
        wsCamposTrabajo.setWsHfaDmaNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCamposTrabajo.setWsHfaDmaNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaDmaInt
    @Test
    void testGetterSetterWsHfaDmaInt() {

        // Property: wsHfaReg.wsHfaDmaInt -> 10 WS-HFA-DMA-INT X(05)
        assertNotNull(wsCamposTrabajo.getWsHfaDmaInt());
        String vString = "ABCab";
        wsCamposTrabajo.setWsHfaDmaInt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCamposTrabajo.setWsHfaDmaInt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaDmaCol
    @Test
    void testGetterSetterWsHfaDmaCol() {

        // Property: wsHfaReg.wsHfaDmaCol -> 10 WS-HFA-DMA-COL X(40)
        assertNotNull(wsCamposTrabajo.getWsHfaDmaCol());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaDmaCol(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaDmaCol(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaMndNom
    @Test
    void testGetterSetterWsHfaMndNom() {

        // Property: wsHfaReg.wsHfaMndNom -> 10 WS-HFA-MND-NOM X(50)
        assertNotNull(wsCamposTrabajo.getWsHfaMndNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaMndNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaMndNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaEdoNom
    @Test
    void testGetterSetterWsHfaEdoNom() {

        // Property: wsHfaReg.wsHfaEdoNom -> 10 WS-HFA-EDO-NOM X(30)
        assertNotNull(wsCamposTrabajo.getWsHfaEdoNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaEdoNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaEdoNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaPaiNom
    @Test
    void testGetterSetterWsHfaPaiNom() {

        // Property: wsHfaReg.wsHfaPaiNom -> 10 WS-HFA-PAI-NOM X(30)
        assertNotNull(wsCamposTrabajo.getWsHfaPaiNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaPaiNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaPaiNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaCpoCve
    @Test
    void testGetterSetterWsHfaCpoCve() {

        // Property: wsHfaReg.wsHfaCpoCve -> 10 WS-HFA-CPO-CVE S9(9) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaCpoCve());
        int vint = 123456789;
        wsCamposTrabajo.setWsHfaCpoCve(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCamposTrabajo.setWsHfaCpoCve(vINumericValue); // INumericValue
    }

    // property wsHfaMteCve
    @Test
    void testGetterSetterWsHfaMteCve() {

        // Property: wsHfaReg.wsHfaMteCve -> 10 WS-HFA-MTE-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaMteCve());
        int vint = 1234;
        wsCamposTrabajo.setWsHfaMteCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfaMteCve(vINumericValue); // INumericValue
    }

    // property wsHfaTfpCve
    @Test
    void testGetterSetterWsHfaTfpCve() {

        // Property: wsHfaReg.wsHfaTfpCve -> 10 WS-HFA-TFP-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsHfaTfpCve());
        int vint = 1234;
        wsCamposTrabajo.setWsHfaTfpCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsHfaTfpCve(vINumericValue); // INumericValue
    }

    // property wsHfaCtbNumCta
    @Test
    void testGetterSetterWsHfaCtbNumCta() {

        // Property: wsHfaReg.wsHfaCtbNumCta -> 10 WS-HFA-CTB-NUM-CTA X(18)
        assertNotNull(wsCamposTrabajo.getWsHfaCtbNumCta());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaCtbNumCta(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaCtbNumCta(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaIndPag
    @Test
    void testGetterSetterWsHfaIndPag() {

        // Property: wsHfaReg.wsHfaIndPag -> 10 WS-HFA-IND-PAG X(01)
        assertNotNull(wsCamposTrabajo.getWsHfaIndPag());
        String vString = "A";
        wsCamposTrabajo.setWsHfaIndPag(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCamposTrabajo.setWsHfaIndPag(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaIndExc
    @Test
    void testGetterSetterWsHfaIndExc() {

        // Property: wsHfaReg.wsHfaIndExc -> 10 WS-HFA-IND-EXC X(01)
        assertNotNull(wsCamposTrabajo.getWsHfaIndExc());
        String vString = "A";
        wsCamposTrabajo.setWsHfaIndExc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCamposTrabajo.setWsHfaIndExc(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaUsuCve
    @Test
    void testGetterSetterWsHfaUsuCve() {

        // Property: wsHfaReg.wsHfaUsuCve -> 10 WS-HFA-USU-CVE X(08)
        assertNotNull(wsCamposTrabajo.getWsHfaUsuCve());
        String vString = "ABCabc12";
        wsCamposTrabajo.setWsHfaUsuCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsCamposTrabajo.setWsHfaUsuCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaNomPrg
    @Test
    void testGetterSetterWsHfaNomPrg() {

        // Property: wsHfaReg.wsHfaNomPrg -> 10 WS-HFA-NOM-PRG X(08)
        assertNotNull(wsCamposTrabajo.getWsHfaNomPrg());
        String vString = "ABCabc12";
        wsCamposTrabajo.setWsHfaNomPrg(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsCamposTrabajo.setWsHfaNomPrg(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaFecUltAct
    @Test
    void testGetterSetterWsHfaFecUltAct() {

        // Property: wsHfaReg.wsHfaFecUltAct -> 10 WS-HFA-FEC-ULT-ACT X(26)
        assertNotNull(wsCamposTrabajo.getWsHfaFecUltAct());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaFecUltAct(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaFecUltAct(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaPjeIva
    @Test
    void testGetterSetterWsHfaPjeIva() {

        // Property: wsHfaReg.wsHfaPjeIva -> 10 WS-HFA-PJE-IVA S9(3)V9(2) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsHfaPjeIva());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsHfaPjeIva(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsHfaPjeIva(vINumericValue); // INumericValue
    }

    // property wsHfaPjeIsr
    @Test
    void testGetterSetterWsHfaPjeIsr() {

        // Property: wsHfaReg.wsHfaPjeIsr -> 10 WS-HFA-PJE-ISR S9(3)V9(2) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsHfaPjeIsr());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsHfaPjeIsr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsHfaPjeIsr(vINumericValue); // INumericValue
    }

    // property wsHfaCdregfis
    @Test
    void testGetterSetterWsHfaCdregfis() {

        // Property: wsHfaReg.wsHfaCdregfis -> 10 WS-HFA-CDREGFIS X(03)
        assertNotNull(wsCamposTrabajo.getWsHfaCdregfis());
        String vString = "ABC";
        wsCamposTrabajo.setWsHfaCdregfis(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsCamposTrabajo.setWsHfaCdregfis(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaNomrznso
    @Test
    void testGetterSetterWsHfaNomrznso() {

        // Property: wsHfaReg.wsHfaNomrznso -> 10 WS-HFA-NOMRZNSO X(300)
        assertNotNull(wsCamposTrabajo.getWsHfaNomrznso());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsHfaNomrznso(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsHfaNomrznso(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsHfaCedNovi
    @Test
    void testGetterSetterWsHfaCedNovi() {

        // Property: wsHfaReg.wsHfaCedNovi -> 10 WS-HFA-CED-NOVI S9(3)V9(2) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsHfaCedNovi());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsHfaCedNovi(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsHfaCedNovi(vINumericValue); // INumericValue
    }

    // property wsHfaCedVida
    @Test
    void testGetterSetterWsHfaCedVida() {

        // Property: wsHfaReg.wsHfaCedVida -> 10 WS-HFA-CED-VIDA S9(3)V9(2) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsHfaCedVida());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsHfaCedVida(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsHfaCedVida(vINumericValue); // INumericValue
    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        // Property: wsAgtReg.wsAgtIdr -> 10 WS-AGT-IDR S9(9) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsAgtIdr());
        int vint = 123456789;
        wsCamposTrabajo.setWsAgtIdr(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCamposTrabajo.setWsAgtIdr(vINumericValue); // INumericValue
    }

    // property wsAgtRfc
    @Test
    void testGetterSetterWsAgtRfc() {

        // Property: wsAgtReg.wsAgtRfc -> 10 WS-AGT-RFC X(13)
        assertNotNull(wsCamposTrabajo.getWsAgtRfc());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtRfc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtRfc(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtNom
    @Test
    void testGetterSetterWsAgtNom() {

        // Property: wsAgtReg.wsAgtNom -> 10 WS-AGT-NOM X(40)
        assertNotNull(wsCamposTrabajo.getWsAgtNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtApePat
    @Test
    void testGetterSetterWsAgtApePat() {

        // Property: wsAgtReg.wsAgtApePat -> 10 WS-AGT-APE-PAT X(40)
        assertNotNull(wsCamposTrabajo.getWsAgtApePat());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtApePat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtApePat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtApeMat
    @Test
    void testGetterSetterWsAgtApeMat() {

        // Property: wsAgtReg.wsAgtApeMat -> 10 WS-AGT-APE-MAT X(30)
        assertNotNull(wsCamposTrabajo.getWsAgtApeMat());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtApeMat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtApeMat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecNac
    @Test
    void testGetterSetterWsAgtFecNac() {

        // Property: wsAgtReg.wsAgtFecNac -> 10 WS-AGT-FEC-NAC X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecNac());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecNac(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecNac(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtNumCed
    @Test
    void testGetterSetterWsAgtNumCed() {

        // Property: wsAgtReg.wsAgtNumCed -> 10 WS-AGT-NUM-CED X(20)
        assertNotNull(wsCamposTrabajo.getWsAgtNumCed());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtNumCed(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtNumCed(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecIniCed
    @Test
    void testGetterSetterWsAgtFecIniCed() {

        // Property: wsAgtReg.wsAgtFecIniCed -> 10 WS-AGT-FEC-INI-CED X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecIniCed());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecIniCed(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecIniCed(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecFinCed
    @Test
    void testGetterSetterWsAgtFecFinCed() {

        // Property: wsAgtReg.wsAgtFecFinCed -> 10 WS-AGT-FEC-FIN-CED X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecFinCed());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecFinCed(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecFinCed(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtExpSeg
    @Test
    void testGetterSetterWsAgtExpSeg() {

        // Property: wsAgtReg.wsAgtExpSeg -> 10 WS-AGT-EXP-SEG X(01)
        assertNotNull(wsCamposTrabajo.getWsAgtExpSeg());
        String vString = "A";
        wsCamposTrabajo.setWsAgtExpSeg(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCamposTrabajo.setWsAgtExpSeg(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtTraEsp
    @Test
    void testGetterSetterWsAgtTraEsp() {

        // Property: wsAgtReg.wsAgtTraEsp -> 10 WS-AGT-TRA-ESP X(01)
        assertNotNull(wsCamposTrabajo.getWsAgtTraEsp());
        String vString = "A";
        wsCamposTrabajo.setWsAgtTraEsp(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsCamposTrabajo.setWsAgtTraEsp(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtClfLmr
    @Test
    void testGetterSetterWsAgtClfLmr() {

        // Property: wsAgtReg.wsAgtClfLmr -> 10 WS-AGT-CLF-LMR S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsAgtClfLmr());
        int vint = 1234;
        wsCamposTrabajo.setWsAgtClfLmr(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsAgtClfLmr(vINumericValue); // INumericValue
    }

    // property wsFecUltAct
    @Test
    void testGetterSetterWsFecUltAct() {

        // Property: wsAgtReg.wsFecUltAct -> 10 WS-FEC-ULT-ACT X(10)
        assertNotNull(wsCamposTrabajo.getWsFecUltAct());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsFecUltAct(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsFecUltAct(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtRazSoc
    @Test
    void testGetterSetterWsAgtRazSoc() {

        // Property: wsAgtReg.wsAgtRazSoc -> 10 WS-AGT-RAZ-SOC X(60)
        assertNotNull(wsCamposTrabajo.getWsAgtRazSoc());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtRazSoc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtRazSoc(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsUsuCveAct
    @Test
    void testGetterSetterWsUsuCveAct() {

        // Property: wsAgtReg.wsUsuCveAct -> 10 WS-USU-CVE-ACT X(08)
        assertNotNull(wsCamposTrabajo.getWsUsuCveAct());
        String vString = "ABCabc12";
        wsCamposTrabajo.setWsUsuCveAct(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        wsCamposTrabajo.setWsUsuCveAct(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsNalCve
    @Test
    void testGetterSetterWsNalCve() {

        // Property: wsAgtReg.wsNalCve -> 10 WS-NAL-CVE X(03)
        assertNotNull(wsCamposTrabajo.getWsNalCve());
        String vString = "ABC";
        wsCamposTrabajo.setWsNalCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsCamposTrabajo.setWsNalCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsPefCve
    @Test
    void testGetterSetterWsPefCve() {

        // Property: wsAgtReg.wsPefCve -> 10 WS-PEF-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsPefCve());
        int vint = 1234;
        wsCamposTrabajo.setWsPefCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsPefCve(vINumericValue); // INumericValue
    }

    // property wsTcaCve
    @Test
    void testGetterSetterWsTcaCve() {

        // Property: wsAgtReg.wsTcaCve -> 10 WS-TCA-CVE X(02)
        assertNotNull(wsCamposTrabajo.getWsTcaCve());
        String vString = "AB";
        wsCamposTrabajo.setWsTcaCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsCamposTrabajo.setWsTcaCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsMteCve
    @Test
    void testGetterSetterWsMteCve() {

        // Property: wsAgtReg.wsMteCve -> 10 WS-MTE-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsMteCve());
        int vint = 1234;
        wsCamposTrabajo.setWsMteCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsMteCve(vINumericValue); // INumericValue
    }

    // property wsAgtNumFolInv
    @Test
    void testGetterSetterWsAgtNumFolInv() {

        // Property: wsAgtReg.wsAgtNumFolInv -> 10 WS-AGT-NUM-FOL-INV S9(9) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsAgtNumFolInv());
        int vint = 123456789;
        wsCamposTrabajo.setWsAgtNumFolInv(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCamposTrabajo.setWsAgtNumFolInv(vINumericValue); // INumericValue
    }

    // property wsAgtNomExi
    @Test
    void testGetterSetterWsAgtNomExi() {

        // Property: wsAgtReg.wsAgtNomExi -> 10 WS-AGT-NOM-EXI X(40)
        assertNotNull(wsCamposTrabajo.getWsAgtNomExi());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtNomExi(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtNomExi(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtRstInv
    @Test
    void testGetterSetterWsAgtRstInv() {

        // Property: wsAgtReg.wsAgtRstInv -> 10 WS-AGT-RST-INV X(05)
        assertNotNull(wsCamposTrabajo.getWsAgtRstInv());
        String vString = "ABCab";
        wsCamposTrabajo.setWsAgtRstInv(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCamposTrabajo.setWsAgtRstInv(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecIniInv
    @Test
    void testGetterSetterWsAgtFecIniInv() {

        // Property: wsAgtReg.wsAgtFecIniInv -> 10 WS-AGT-FEC-INI-INV X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecIniInv());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecIniInv(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecIniInv(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecFinInv
    @Test
    void testGetterSetterWsAgtFecFinInv() {

        // Property: wsAgtReg.wsAgtFecFinInv -> 10 WS-AGT-FEC-FIN-INV X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecFinInv());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecFinInv(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecFinInv(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtNomDphInv
    @Test
    void testGetterSetterWsAgtNomDphInv() {

        // Property: wsAgtReg.wsAgtNomDphInv -> 10 WS-AGT-NOM-DPH-INV X(40)
        assertNotNull(wsCamposTrabajo.getWsAgtNomDphInv());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtNomDphInv(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtNomDphInv(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecReh
    @Test
    void testGetterSetterWsAgtFecReh() {

        // Property: wsAgtReg.wsAgtFecReh -> 10 WS-AGT-FEC-REH X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecReh());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecReh(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecReh(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecMotEss
    @Test
    void testGetterSetterWsAgtFecMotEss() {

        // Property: wsAgtReg.wsAgtFecMotEss -> 10 WS-AGT-FEC-MOT-ESS X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecMotEss());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecMotEss(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecMotEss(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtFecMotAsgIn
    @Test
    void testGetterSetterWsAgtFecMotAsgIn() {

        // Property: wsAgtReg.wsAgtFecMotAsgIn -> 10 WS-AGT-FEC-MOT-ASG-IN X(10)
        assertNotNull(wsCamposTrabajo.getWsAgtFecMotAsgIn());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtFecMotAsgIn(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtFecMotAsgIn(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtObs
    @Test
    void testGetterSetterWsAgtObs() {

        // Property: wsAgtReg.wsAgtObs -> 10 WS-AGT-OBS X(60)
        assertNotNull(wsCamposTrabajo.getWsAgtObs());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsAgtObs(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsAgtObs(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsAgtNip
    @Test
    void testGetterSetterWsAgtNip() {

        // Property: wsAgtReg.wsAgtNip -> 10 WS-AGT-NIP S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsAgtNip());
        int vint = 1234;
        wsCamposTrabajo.setWsAgtNip(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsAgtNip(vINumericValue); // INumericValue
    }

    // property wsAgtNumFacIso
    @Test
    void testGetterSetterWsAgtNumFacIso() {

        // Property: wsAgtReg.wsAgtNumFacIso -> 10 WS-AGT-NUM-FAC-ISO S9(9) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsAgtNumFacIso());
        int vint = 123456789;
        wsCamposTrabajo.setWsAgtNumFacIso(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCamposTrabajo.setWsAgtNumFacIso(vINumericValue); // INumericValue
    }

    // property wsAgtImpFacIso
    @Test
    void testGetterSetterWsAgtImpFacIso() {

        // Property: wsAgtReg.wsAgtImpFacIso -> 10 WS-AGT-IMP-FAC-ISO S9(11)V9(2) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsAgtImpFacIso());
        BigDecimal vBigDecimal = new BigDecimal("12345678901.12");
        wsCamposTrabajo.setWsAgtImpFacIso(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(13, 2);;
        vINumericValue.setValue("12345678901.12");
        wsCamposTrabajo.setWsAgtImpFacIso(vINumericValue); // INumericValue
    }

    // property wsGralAgtRfc
    @Test
    void testGetterSetterWsGralAgtRfc() {

        // Property: wsDatosGralAgt.wsGralAgtRfc -> 10 WS-GRAL-AGT-RFC X(13)
        assertNotNull(wsCamposTrabajo.getWsGralAgtRfc());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralAgtRfc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralAgtRfc(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralAgtNom
    @Test
    void testGetterSetterWsGralAgtNom() {

        // Property: wsDatosGralAgt.wsGralAgtNom -> 10 WS-GRAL-AGT-NOM X(40)
        assertNotNull(wsCamposTrabajo.getWsGralAgtNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralAgtNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralAgtNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralAgtApePat
    @Test
    void testGetterSetterWsGralAgtApePat() {

        // Property: wsDatosGralAgt.wsGralAgtApePat -> 10 WS-GRAL-AGT-APE-PAT X(40)
        assertNotNull(wsCamposTrabajo.getWsGralAgtApePat());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralAgtApePat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralAgtApePat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralAgtApeMat
    @Test
    void testGetterSetterWsGralAgtApeMat() {

        // Property: wsDatosGralAgt.wsGralAgtApeMat -> 10 WS-GRAL-AGT-APE-MAT X(30)
        assertNotNull(wsCamposTrabajo.getWsGralAgtApeMat());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralAgtApeMat(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralAgtApeMat(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralAgtRazSoc
    @Test
    void testGetterSetterWsGralAgtRazSoc() {

        // Property: wsDatosGralAgt.wsGralAgtRazSoc -> 10 WS-GRAL-AGT-RAZ-SOC X(60)
        assertNotNull(wsCamposTrabajo.getWsGralAgtRazSoc());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralAgtRazSoc(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralAgtRazSoc(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralAgtNomConcatenado
    @Test
    void testGetterSetterWsGralAgtNomConcatenado() {

        // Property: wsDatosGralAgt.wsGralAgtNomConcatenado -> 10 WS-GRAL-AGT-NOM-CONCATENADO X(100)
        assertNotNull(wsCamposTrabajo.getWsGralAgtNomConcatenado());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralAgtNomConcatenado(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralAgtNomConcatenado(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralPraAgtCxxCurp
    @Test
    void testGetterSetterWsGralPraAgtCxxCurp() {

        // Property: wsDatosGralAgt.wsGralPraAgtCxxCurp -> 10 WS-GRAL-PRA-AGT-CXX-CURP X(20)
        assertNotNull(wsCamposTrabajo.getWsGralPraAgtCxxCurp());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralPraAgtCxxCurp(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralPraAgtCxxCurp(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralAgtNalCve
    @Test
    void testGetterSetterWsGralAgtNalCve() {

        // Property: wsDatosGralAgt.wsGralAgtNalCve -> 10 WS-GRAL-AGT-NAL-CVE X(03)
        assertNotNull(wsCamposTrabajo.getWsGralAgtNalCve());
        String vString = "ABC";
        wsCamposTrabajo.setWsGralAgtNalCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsCamposTrabajo.setWsGralAgtNalCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralExtranjero
    @Test
    void testGetterSetterWsGralExtranjero() {

        // Property: wsDatosGralAgt.wsGralExtranjero -> 10 WS-GRAL-EXTRANJERO X(03)
        assertNotNull(wsCamposTrabajo.getWsGralExtranjero());
        String vString = "ABC";
        wsCamposTrabajo.setWsGralExtranjero(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsCamposTrabajo.setWsGralExtranjero(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralEsaCve
    @Test
    void testGetterSetterWsGralEsaCve() {

        // Property: wsDatosGralAgt.wsGralEsaCve -> 10 WS-GRAL-ESA-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsGralEsaCve());
        int vint = 1234;
        wsCamposTrabajo.setWsGralEsaCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsGralEsaCve(vINumericValue); // INumericValue
    }

    // property wsGralPefCve
    @Test
    void testGetterSetterWsGralPefCve() {

        // Property: wsDatosGralAgt.wsGralPefCve -> 10 WS-GRAL-PEF-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsGralPefCve());
        int vint = 1234;
        wsCamposTrabajo.setWsGralPefCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsGralPefCve(vINumericValue); // INumericValue
    }

    // property wsGralDmaCae
    @Test
    void testGetterSetterWsGralDmaCae() {

        // Property: wsDatosGralAgt.wsGralDmaCae -> 10 WS-GRAL-DMA-CAE X(40)
        assertNotNull(wsCamposTrabajo.getWsGralDmaCae());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralDmaCae(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralDmaCae(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralDmaNum
    @Test
    void testGetterSetterWsGralDmaNum() {

        // Property: wsDatosGralAgt.wsGralDmaNum -> 10 WS-GRAL-DMA-NUM X(05)
        assertNotNull(wsCamposTrabajo.getWsGralDmaNum());
        String vString = "ABCab";
        wsCamposTrabajo.setWsGralDmaNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCamposTrabajo.setWsGralDmaNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralDmaInt
    @Test
    void testGetterSetterWsGralDmaInt() {

        // Property: wsDatosGralAgt.wsGralDmaInt -> 10 WS-GRAL-DMA-INT X(05)
        assertNotNull(wsCamposTrabajo.getWsGralDmaInt());
        String vString = "ABCab";
        wsCamposTrabajo.setWsGralDmaInt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCamposTrabajo.setWsGralDmaInt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralDmaCol
    @Test
    void testGetterSetterWsGralDmaCol() {

        // Property: wsDatosGralAgt.wsGralDmaCol -> 10 WS-GRAL-DMA-COL X(40)
        assertNotNull(wsCamposTrabajo.getWsGralDmaCol());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralDmaCol(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralDmaCol(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralMndNom
    @Test
    void testGetterSetterWsGralMndNom() {

        // Property: wsDatosGralAgt.wsGralMndNom -> 10 WS-GRAL-MND-NOM X(50)
        assertNotNull(wsCamposTrabajo.getWsGralMndNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralMndNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralMndNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralDmaCpoCve
    @Test
    void testGetterSetterWsGralDmaCpoCve() {

        // Property: wsDatosGralAgt.wsGralDmaCpoCve -> 10 WS-GRAL-DMA-CPO-CVE S9(9) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsGralDmaCpoCve());
        int vint = 123456789;
        wsCamposTrabajo.setWsGralDmaCpoCve(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsCamposTrabajo.setWsGralDmaCpoCve(vINumericValue); // INumericValue
    }

    // property wsGralPobNom
    @Test
    void testGetterSetterWsGralPobNom() {

        // Property: wsDatosGralAgt.wsGralPobNom -> 10 WS-GRAL-POB-NOM X(40)
        assertNotNull(wsCamposTrabajo.getWsGralPobNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralPobNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralPobNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralMndEdoCve
    @Test
    void testGetterSetterWsGralMndEdoCve() {

        // Property: wsDatosGralAgt.wsGralMndEdoCve -> 10 WS-GRAL-MND-EDO-CVE X(04)
        assertNotNull(wsCamposTrabajo.getWsGralMndEdoCve());
        String vString = "ABCa";
        wsCamposTrabajo.setWsGralMndEdoCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsCamposTrabajo.setWsGralMndEdoCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralEdoNom
    @Test
    void testGetterSetterWsGralEdoNom() {

        // Property: wsDatosGralAgt.wsGralEdoNom -> 10 WS-GRAL-EDO-NOM X(30)
        assertNotNull(wsCamposTrabajo.getWsGralEdoNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralEdoNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralEdoNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralEdoPaiCve
    @Test
    void testGetterSetterWsGralEdoPaiCve() {

        // Property: wsDatosGralAgt.wsGralEdoPaiCve -> 10 WS-GRAL-EDO-PAI-CVE X(03)
        assertNotNull(wsCamposTrabajo.getWsGralEdoPaiCve());
        String vString = "ABC";
        wsCamposTrabajo.setWsGralEdoPaiCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsCamposTrabajo.setWsGralEdoPaiCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralPaiPaiNom
    @Test
    void testGetterSetterWsGralPaiPaiNom() {

        // Property: wsDatosGralAgt.wsGralPaiPaiNom -> 10 WS-GRAL-PAI-PAI-NOM X(30)
        assertNotNull(wsCamposTrabajo.getWsGralPaiPaiNom());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralPaiPaiNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralPaiPaiNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsGralCmaDesTelefon
    @Test
    void testGetterSetterWsGralCmaDesTelefon() {

        // Property: wsDatosGralAgt.wsGralCmaDesTelefon -> 10 WS-GRAL-CMA-DES-TELEFON X(60)
        assertNotNull(wsCamposTrabajo.getWsGralCmaDesTelefon());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsGralCmaDesTelefon(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsGralCmaDesTelefon(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsCnaOfnCve
    @Test
    void testGetterSetterWsCnaOfnCve() {

        // Property: wsCnaAgtReg.wsCnaOfnCve -> 10 WS-CNA-OFN-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsCnaOfnCve());
        int vint = 1234;
        wsCamposTrabajo.setWsCnaOfnCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsCnaOfnCve(vINumericValue); // INumericValue
    }

    // property wsCnaTfpCve
    @Test
    void testGetterSetterWsCnaTfpCve() {

        // Property: wsCnaAgtReg.wsCnaTfpCve -> 10 WS-CNA-TFP-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsCnaTfpCve());
        int vint = 1234;
        wsCamposTrabajo.setWsCnaTfpCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsCnaTfpCve(vINumericValue); // INumericValue
    }

    // property wsCnaMteCve
    @Test
    void testGetterSetterWsCnaMteCve() {

        // Property: wsCnaAgtReg.wsCnaMteCve -> 10 WS-CNA-MTE-CVE S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsCnaMteCve());
        int vint = 1234;
        wsCamposTrabajo.setWsCnaMteCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsCnaMteCve(vINumericValue); // INumericValue
    }

    // property wsCnaGerZonEnv
    @Test
    void testGetterSetterWsCnaGerZonEnv() {

        // Property: wsCnaAgtReg.wsCnaGerZonEnv -> 10 WS-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsCnaGerZonEnv());
        int vint = 1234;
        wsCamposTrabajo.setWsCnaGerZonEnv(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsCnaGerZonEnv(vINumericValue); // INumericValue
    }

    // property wsCnaCtbNumCta
    @Test
    void testGetterSetterWsCnaCtbNumCta() {

        // Property: wsCnaAgtReg.wsCnaCtbNumCta -> 10 WS-CNA-CTB-NUM-CTA X(18)
        assertNotNull(wsCamposTrabajo.getWsCnaCtbNumCta());
        String vString = "ABCabc1230";
        wsCamposTrabajo.setWsCnaCtbNumCta(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCamposTrabajo.setWsCnaCtbNumCta(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsCnaPjeIva
    @Test
    void testGetterSetterWsCnaPjeIva() {

        // Property: wsCnaAgtReg.wsCnaPjeIva -> 10 WS-CNA-PJE-IVA S9(3)V9(2) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsCnaPjeIva());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsCnaPjeIva(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsCnaPjeIva(vINumericValue); // INumericValue
    }

    // property wsCnaPjeIsr
    @Test
    void testGetterSetterWsCnaPjeIsr() {

        // Property: wsCnaAgtReg.wsCnaPjeIsr -> 10 WS-CNA-PJE-ISR S9(3)V9(2) COMPUTATIONAL-3
        assertNotNull(wsCamposTrabajo.getWsCnaPjeIsr());
        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCamposTrabajo.setWsCnaPjeIsr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCamposTrabajo.setWsCnaPjeIsr(vINumericValue); // INumericValue
    }

    // property wsCnaDsgCve
    @Test
    void testGetterSetterWsCnaDsgCve() {

        // Property: wsCnaAgtReg.wsCnaDsgCve -> 10 WS-CNA-DSG-CVE S9(04) COMPUTATIONAL
        assertNotNull(wsCamposTrabajo.getWsCnaDsgCve());
        int vint = 1234;
        wsCamposTrabajo.setWsCnaDsgCve(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCamposTrabajo.setWsCnaDsgCve(vINumericValue); // INumericValue
    }

    // property wsTabAgtN
    @Test
    void testGetterSetterWsTabAgtN() {

        // Property: wsTabAgtReg.wsTabAgtN -> 10 WS-TAB-AGT-N 9(10) REDEFINED BY WS-TAB-AGT-R
        assertNotNull(wsCamposTrabajo.getWsTabAgtN());
        BigDecimal vBigDecimal = new BigDecimal("1234567890");
        wsCamposTrabajo.setWsTabAgtN(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(10, 0);;
        vINumericValue.setValue("1234567890");
        wsCamposTrabajo.setWsTabAgtN(vINumericValue); // INumericValue
    }

    // property wsTabAgtR
    @Test
    void testGetterSetterWsTabAgtR() {

        // Property: wsTabAgtReg.wsTabAgtR -> 10 WS-TAB-AGT-R REDEFINES WS-TAB-AGT-N
        assertNotNull(wsCamposTrabajo.getWsTabAgtR());
    }

    // property wsTabFiller
    @Test
    void testGetterSetterWsTabFiller() {

        // Property: wsTabAgtReg.wsTabAgtR.wsTabFiller -> 15 WS-TAB-FILLER X(03)
        assertNotNull(wsCamposTrabajo.getWsTabFiller());
        String vString = "ABC";
        wsCamposTrabajo.setWsTabFiller(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsCamposTrabajo.setWsTabFiller(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsTabAgt
    @Test
    void testGetterSetterWsTabAgt() {

        // Property: wsTabAgtReg.wsTabAgtR.wsTabAgt -> 15 WS-TAB-AGT X(07)
        assertNotNull(wsCamposTrabajo.getWsTabAgt());
        String vString = "ABCabc1";
        wsCamposTrabajo.setWsTabAgt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1");
        wsCamposTrabajo.setWsTabAgt(vIAlphanumericValue); // IAlphanumericValue
    }
}

