package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: FECHA-FORMAT-III.
 *
 */
class FechaFormatIiiTest {

    private FechaFormatIii fechaFormatIii;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        fechaFormatIii = new FechaFormatIii();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

        fechaFormatIii.setFechaFormatDdIii(0);
        assertEquals(0, fechaFormatIii.getFechaFormatDdIii());

    }

    // property fechaFormatMmaa
    @Test
    void testGetterSetterFechaFormatMmaa() {

        assertNotNull(fechaFormatIii.getFechaFormatMmaa());
    }

    // property fechaFormatDdIii
    @Test
    void testGetterSetterFechaFormatDdIii() {

        assertNotNull(fechaFormatIii.getFechaFormatDdIii());

        int vint = 12;
        fechaFormatIii.setFechaFormatDdIii(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatIii.getFechaFormatDdIii()));
    }

    // property fechaFormatFiller
    @Test
    void testGetterSetterFechaFormatFiller() {

        // Property: fechaFormatMmaa.fechaFormatFiller -> 10 FECHA-FORMAT-FILLER 9(02)
        assertNotNull(fechaFormatIii.getFechaFormatFiller());
        int vint = 12;
        fechaFormatIii.setFechaFormatFiller(vint); // int
    }

    // property fechaFormatAaaa
    @Test
    void testGetterSetterFechaFormatAaaa() {

        // Property: fechaFormatMmaa.fechaFormatAaaa -> 10 FECHA-FORMAT-AAAA 9(04)
        assertNotNull(fechaFormatIii.getFechaFormatAaaa());
        int vint = 1234;
        fechaFormatIii.setFechaFormatAaaa(vint); // int
    }

    // property fechaFormatMmIii
    @Test
    void testGetterSetterFechaFormatMmIii() {

        // Property: fechaFormatMmaa.fechaFormatMmIii -> 10 FECHA-FORMAT-MM-III 9(02)
        assertNotNull(fechaFormatIii.getFechaFormatMmIii());
        int vint = 12;
        fechaFormatIii.setFechaFormatMmIii(vint); // int
    }
}

