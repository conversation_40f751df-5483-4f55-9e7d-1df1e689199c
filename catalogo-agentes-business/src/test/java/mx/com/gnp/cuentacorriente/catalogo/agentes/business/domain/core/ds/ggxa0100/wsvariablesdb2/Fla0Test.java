package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: FLA0.
 *
 */
class Fla0Test {

    private Fla0 fla0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        fla0 = new Fla0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        fla0.setFlaFlaNum("");
        assertEquals("", fla0.getFlaFlaNum());

    }

    // property flaFlaNum
    @Test
    void testGetterSetterFlaFlaNum() {

        assertNotNull(fla0.getFlaFlaNum());

        String vString = "ABCabc12";
        fla0.setFlaFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(fla0.getFlaFlaNum()));
    }
}

