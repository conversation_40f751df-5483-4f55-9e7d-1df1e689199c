package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTFLA0-NULL-AUX.
 *
 */
class WsGfvtfla0NullAuxTest {

    private WsGfvtfla0NullAux wsGfvtfla0NullAux;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtfla0NullAux = new WsGfvtfla0NullAux();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsGfvtfla0NullAux wsGfvtfla0NullAux2 = new WsGfvtfla0NullAux(parent);
        assertNotNull(wsGfvtfla0NullAux2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 26);
        WsGfvtfla0NullAux wsGfvtfla0NullAux3 = new WsGfvtfla0NullAux(parent, sibling);
        assertNotNull(wsGfvtfla0NullAux3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsGfvtfla0NullAux.getWsGfvtfla0Ele());

    }

    // property wsGfvtfla0Ele
    @Test
    void testGetterSetterWsGfvtfla0Ele() {

        assertNotNull(wsGfvtfla0NullAux.getWsGfvtfla0Ele());
        assertNotNull(wsGfvtfla0NullAux.getWsGfvtfla0Ele(1));

        int vint = 1234;
        wsGfvtfla0NullAux.getWsGfvtfla0Ele().at(1).setValue(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtfla0NullAux.getWsGfvtfla0Ele().at(1)));

        BinaryVar vBinaryVar = new BinaryVar(4, 0);;
        vBinaryVar.setValue("1234");
        wsGfvtfla0NullAux.getWsGfvtfla0Ele().at(1).setValue(vBinaryVar); // BinaryVar
        assertEquals(asInt(vBinaryVar), asInt(wsGfvtfla0NullAux.getWsGfvtfla0Ele().at(1)));
    }
}

