package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CPO-CVE-R.
 *
 */
class WsCpoCveRTest {

    private WsCpoCveR wsCpoCveR;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCpoCveR = new WsCpoCveR();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsCpoCveR wsCpoCveR2 = new WsCpoCveR(parent);
        assertNotNull(wsCpoCveR2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 9);
        WsCpoCveR wsCpoCveR3 = new WsCpoCveR(parent, sibling);
        assertNotNull(wsCpoCveR3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsCpoCveR.getWsCpoCve_4());

        assertNotNull(wsCpoCveR.getWsCpoCve_5());

    }

    // property wsCpoCve_4
    @Test
    void testGetterSetterWsCpoCve_4() {

        assertNotNull(wsCpoCveR.getWsCpoCve_4());

        String vString = "ABCa";
        wsCpoCveR.setWsCpoCve_4(vString); // String
        assertEquals(asStr(vString), asStr(wsCpoCveR.getWsCpoCve_4()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsCpoCveR.setWsCpoCve_4(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCpoCveR.getWsCpoCve_4()));
    }

    // property wsCpoCve_5
    @Test
    void testGetterSetterWsCpoCve_5() {

        assertNotNull(wsCpoCveR.getWsCpoCve_5());

        String vString = "ABCab";
        wsCpoCveR.setWsCpoCve_5(vString); // String
        assertEquals(asStr(vString), asStr(wsCpoCveR.getWsCpoCve_5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsCpoCveR.setWsCpoCve_5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCpoCveR.getWsCpoCve_5()));
    }
}

