package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DATOS-GRAL-AGT.
 *
 */
class WsDatosGralAgtTest {

    private WsDatosGralAgt wsDatosGralAgt;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDatosGralAgt = new WsDatosGralAgt();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsDatosGralAgt wsDatosGralAgt2 = new WsDatosGralAgt(parent);
        assertNotNull(wsDatosGralAgt2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 624);
        WsDatosGralAgt wsDatosGralAgt3 = new WsDatosGralAgt(parent, sibling);
        assertNotNull(wsDatosGralAgt3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsDatosGralAgt.getWsGralAgtRfc());

        assertNotNull(wsDatosGralAgt.getWsGralAgtNom());

        assertNotNull(wsDatosGralAgt.getWsGralAgtApePat());

        assertNotNull(wsDatosGralAgt.getWsGralAgtApeMat());

        assertNotNull(wsDatosGralAgt.getWsGralAgtRazSoc());

        assertNotNull(wsDatosGralAgt.getWsGralAgtNomConcatenado());

        assertNotNull(wsDatosGralAgt.getWsGralPraAgtCxxCurp());

        assertNotNull(wsDatosGralAgt.getWsGralAgtNalCve());

        assertNotNull(wsDatosGralAgt.getWsGralExtranjero());

        assertNotNull(wsDatosGralAgt.getWsGralEsaCve());

        assertNotNull(wsDatosGralAgt.getWsGralPefCve());

        assertNotNull(wsDatosGralAgt.getWsGralDmaCae());

        assertNotNull(wsDatosGralAgt.getWsGralDmaNum());

        assertNotNull(wsDatosGralAgt.getWsGralDmaInt());

        assertNotNull(wsDatosGralAgt.getWsGralDmaCol());

        assertNotNull(wsDatosGralAgt.getWsGralMndNom());

        assertNotNull(wsDatosGralAgt.getWsGralDmaCpoCve());

        assertNotNull(wsDatosGralAgt.getWsGralPobNom());

        assertNotNull(wsDatosGralAgt.getWsGralMndEdoCve());

        assertNotNull(wsDatosGralAgt.getWsGralEdoNom());

        assertNotNull(wsDatosGralAgt.getWsGralEdoPaiCve());

        assertNotNull(wsDatosGralAgt.getWsGralPaiPaiNom());

        assertNotNull(wsDatosGralAgt.getWsGralCmaDesTelefon());

    }

    // property wsGralAgtRfc
    @Test
    void testGetterSetterWsGralAgtRfc() {

        assertNotNull(wsDatosGralAgt.getWsGralAgtRfc());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralAgtRfc(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralAgtRfc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralAgtRfc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralAgtRfc()));
    }

    // property wsGralAgtNom
    @Test
    void testGetterSetterWsGralAgtNom() {

        assertNotNull(wsDatosGralAgt.getWsGralAgtNom());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralAgtNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralAgtNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralAgtNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralAgtNom()));
    }

    // property wsGralAgtApePat
    @Test
    void testGetterSetterWsGralAgtApePat() {

        assertNotNull(wsDatosGralAgt.getWsGralAgtApePat());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralAgtApePat(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralAgtApePat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralAgtApePat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralAgtApePat()));
    }

    // property wsGralAgtApeMat
    @Test
    void testGetterSetterWsGralAgtApeMat() {

        assertNotNull(wsDatosGralAgt.getWsGralAgtApeMat());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralAgtApeMat(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralAgtApeMat()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralAgtApeMat(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralAgtApeMat()));
    }

    // property wsGralAgtRazSoc
    @Test
    void testGetterSetterWsGralAgtRazSoc() {

        assertNotNull(wsDatosGralAgt.getWsGralAgtRazSoc());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralAgtRazSoc(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralAgtRazSoc()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralAgtRazSoc(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralAgtRazSoc()));
    }

    // property wsGralAgtNomConcatenado
    @Test
    void testGetterSetterWsGralAgtNomConcatenado() {

        assertNotNull(wsDatosGralAgt.getWsGralAgtNomConcatenado());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralAgtNomConcatenado(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralAgtNomConcatenado()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralAgtNomConcatenado(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralAgtNomConcatenado()));
    }

    // property wsGralPraAgtCxxCurp
    @Test
    void testGetterSetterWsGralPraAgtCxxCurp() {

        assertNotNull(wsDatosGralAgt.getWsGralPraAgtCxxCurp());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralPraAgtCxxCurp(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralPraAgtCxxCurp()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralPraAgtCxxCurp(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralPraAgtCxxCurp()));
    }

    // property wsGralAgtNalCve
    @Test
    void testGetterSetterWsGralAgtNalCve() {

        assertNotNull(wsDatosGralAgt.getWsGralAgtNalCve());

        String vString = "ABC";
        wsDatosGralAgt.setWsGralAgtNalCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralAgtNalCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsDatosGralAgt.setWsGralAgtNalCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralAgtNalCve()));
    }

    // property wsGralExtranjero
    @Test
    void testGetterSetterWsGralExtranjero() {

        assertNotNull(wsDatosGralAgt.getWsGralExtranjero());

        String vString = "ABC";
        wsDatosGralAgt.setWsGralExtranjero(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralExtranjero()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsDatosGralAgt.setWsGralExtranjero(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralExtranjero()));
    }

    // property wsGralEsaCve
    @Test
    void testGetterSetterWsGralEsaCve() {

        assertNotNull(wsDatosGralAgt.getWsGralEsaCve());

        int vint = 1234;
        wsDatosGralAgt.setWsGralEsaCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDatosGralAgt.getWsGralEsaCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsDatosGralAgt.setWsGralEsaCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsDatosGralAgt.getWsGralEsaCve()));
    }

    // property wsGralPefCve
    @Test
    void testGetterSetterWsGralPefCve() {

        assertNotNull(wsDatosGralAgt.getWsGralPefCve());

        int vint = 1234;
        wsDatosGralAgt.setWsGralPefCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDatosGralAgt.getWsGralPefCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsDatosGralAgt.setWsGralPefCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsDatosGralAgt.getWsGralPefCve()));
    }

    // property wsGralDmaCae
    @Test
    void testGetterSetterWsGralDmaCae() {

        assertNotNull(wsDatosGralAgt.getWsGralDmaCae());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralDmaCae(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralDmaCae()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralDmaCae(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralDmaCae()));
    }

    // property wsGralDmaNum
    @Test
    void testGetterSetterWsGralDmaNum() {

        assertNotNull(wsDatosGralAgt.getWsGralDmaNum());

        String vString = "ABCab";
        wsDatosGralAgt.setWsGralDmaNum(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralDmaNum()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsDatosGralAgt.setWsGralDmaNum(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralDmaNum()));
    }

    // property wsGralDmaInt
    @Test
    void testGetterSetterWsGralDmaInt() {

        assertNotNull(wsDatosGralAgt.getWsGralDmaInt());

        String vString = "ABCab";
        wsDatosGralAgt.setWsGralDmaInt(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralDmaInt()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wsDatosGralAgt.setWsGralDmaInt(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralDmaInt()));
    }

    // property wsGralDmaCol
    @Test
    void testGetterSetterWsGralDmaCol() {

        assertNotNull(wsDatosGralAgt.getWsGralDmaCol());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralDmaCol(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralDmaCol()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralDmaCol(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralDmaCol()));
    }

    // property wsGralMndNom
    @Test
    void testGetterSetterWsGralMndNom() {

        assertNotNull(wsDatosGralAgt.getWsGralMndNom());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralMndNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralMndNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralMndNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralMndNom()));
    }

    // property wsGralDmaCpoCve
    @Test
    void testGetterSetterWsGralDmaCpoCve() {

        assertNotNull(wsDatosGralAgt.getWsGralDmaCpoCve());

        int vint = 123456789;
        wsDatosGralAgt.setWsGralDmaCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDatosGralAgt.getWsGralDmaCpoCve()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        wsDatosGralAgt.setWsGralDmaCpoCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsDatosGralAgt.getWsGralDmaCpoCve()));
    }

    // property wsGralPobNom
    @Test
    void testGetterSetterWsGralPobNom() {

        assertNotNull(wsDatosGralAgt.getWsGralPobNom());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralPobNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralPobNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralPobNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralPobNom()));
    }

    // property wsGralMndEdoCve
    @Test
    void testGetterSetterWsGralMndEdoCve() {

        assertNotNull(wsDatosGralAgt.getWsGralMndEdoCve());

        String vString = "ABCa";
        wsDatosGralAgt.setWsGralMndEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralMndEdoCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        wsDatosGralAgt.setWsGralMndEdoCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralMndEdoCve()));
    }

    // property wsGralEdoNom
    @Test
    void testGetterSetterWsGralEdoNom() {

        assertNotNull(wsDatosGralAgt.getWsGralEdoNom());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralEdoNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralEdoNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralEdoNom()));
    }

    // property wsGralEdoPaiCve
    @Test
    void testGetterSetterWsGralEdoPaiCve() {

        assertNotNull(wsDatosGralAgt.getWsGralEdoPaiCve());

        String vString = "ABC";
        wsDatosGralAgt.setWsGralEdoPaiCve(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralEdoPaiCve()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsDatosGralAgt.setWsGralEdoPaiCve(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralEdoPaiCve()));
    }

    // property wsGralPaiPaiNom
    @Test
    void testGetterSetterWsGralPaiPaiNom() {

        assertNotNull(wsDatosGralAgt.getWsGralPaiPaiNom());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralPaiPaiNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralPaiPaiNom()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralPaiPaiNom(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralPaiPaiNom()));
    }

    // property wsGralCmaDesTelefon
    @Test
    void testGetterSetterWsGralCmaDesTelefon() {

        assertNotNull(wsDatosGralAgt.getWsGralCmaDesTelefon());

        String vString = "ABCabc1230";
        wsDatosGralAgt.setWsGralCmaDesTelefon(vString); // String
        assertEquals(asStr(vString), asStr(wsDatosGralAgt.getWsGralCmaDesTelefon()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsDatosGralAgt.setWsGralCmaDesTelefon(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsDatosGralAgt.getWsGralCmaDesTelefon()));
    }
}

