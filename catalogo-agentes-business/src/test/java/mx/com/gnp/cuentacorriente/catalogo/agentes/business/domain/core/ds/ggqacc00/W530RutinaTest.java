package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: W530-RUTINA.
 *
 */
class W530RutinaTest {

    private W530Rutina w530Rutina;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        w530Rutina = new W530Rutina();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        w530Rutina.setW530RutinaTabla("");
        assertEquals("", w530Rutina.getW530RutinaTabla());

    }

    // property w530RutinaTabla
    @Test
    void testGetterSetterW530RutinaTabla() {

        assertNotNull(w530Rutina.getW530RutinaTabla());

        String vString = "ABC";
        w530Rutina.setW530RutinaTabla(vString); // String
        assertEquals(asStr(vString), asStr(w530Rutina.getW530RutinaTabla()));
    }
}

