package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WL-PARAMS-SAL.
 *
 */
class WlParamsSalTest {

    private WlParamsSal wlParamsSal;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wlParamsSal = new WlParamsSal();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WlParamsSal wlParamsSal2 = new WlParamsSal(parent);
        assertNotNull(wlParamsSal2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 313);
        WlParamsSal wlParamsSal3 = new WlParamsSal(parent, sibling);
        assertNotNull(wlParamsSal3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wlParamsSal.getWlRegfis());

        assertNotNull(wlParamsSal.getWlNombre());

        assertNotNull(wlParamsSal.getWlPcenvi());

        assertNotNull(wlParamsSal.getWlPcevid());

    }

    // property wlRegfis
    @Test
    void testGetterSetterWlRegfis() {

        assertNotNull(wlParamsSal.getWlRegfis());

        String vString = "ABC";
        wlParamsSal.setWlRegfis(vString); // String
        assertEquals(asStr(vString), asStr(wlParamsSal.getWlRegfis()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wlParamsSal.setWlRegfis(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wlParamsSal.getWlRegfis()));
    }

    // property wlNombre
    @Test
    void testGetterSetterWlNombre() {

        assertNotNull(wlParamsSal.getWlNombre());

        String vString = "ABCabc1230";
        wlParamsSal.setWlNombre(vString); // String
        assertEquals(asStr(vString), asStr(wlParamsSal.getWlNombre()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wlParamsSal.setWlNombre(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wlParamsSal.getWlNombre()));
    }

    // property wlPcenvi
    @Test
    void testGetterSetterWlPcenvi() {

        assertNotNull(wlParamsSal.getWlPcenvi());

        String vString = "ABCab";
        wlParamsSal.setWlPcenvi(vString); // String
        assertEquals(asStr(vString), asStr(wlParamsSal.getWlPcenvi()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wlParamsSal.setWlPcenvi(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wlParamsSal.getWlPcenvi()));
    }

    // property wlPcevid
    @Test
    void testGetterSetterWlPcevid() {

        assertNotNull(wlParamsSal.getWlPcevid());

        String vString = "ABCab";
        wlParamsSal.setWlPcevid(vString); // String
        assertEquals(asStr(vString), asStr(wlParamsSal.getWlPcevid()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        wlParamsSal.setWlPcevid(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wlParamsSal.getWlPcevid()));
    }
}

