package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AGENT-ID.
 *
 */
class WsAgentIdTest {

    private WsAgentId wsAgentId;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAgentId = new WsAgentId();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAgentId wsAgentId2 = new WsAgentId(parent);
        assertNotNull(wsAgentId2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 5);
        WsAgentId wsAgentId3 = new WsAgentId(parent, sibling);
        assertNotNull(wsAgentId3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAgentId.getWsAgentId2());

        assertNotNull(wsAgentId.getWsAgentId3());

    }

    // property wsAgentId2
    @Test
    void testGetterSetterWsAgentId2() {

        assertNotNull(wsAgentId.getWsAgentId2());

        String vString = "AB";
        wsAgentId.setWsAgentId2(vString); // String
        assertEquals(asStr(vString), asStr(wsAgentId.getWsAgentId2()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        wsAgentId.setWsAgentId2(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgentId.getWsAgentId2()));
    }

    // property wsAgentId3
    @Test
    void testGetterSetterWsAgentId3() {

        assertNotNull(wsAgentId.getWsAgentId3());

        String vString = "ABC";
        wsAgentId.setWsAgentId3(vString); // String
        assertEquals(asStr(vString), asStr(wsAgentId.getWsAgentId3()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        wsAgentId.setWsAgentId3(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsAgentId.getWsAgentId3()));
    }
}

