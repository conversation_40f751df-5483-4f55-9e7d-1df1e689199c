package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tcna0_1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-CNA0-ELEM-1.
 *
 */
class TCna0Elem_1Test {

    private TCna0Elem_1 tCna0Elem_1;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tCna0Elem_1 = new TCna0Elem_1();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TCna0Elem_1 tCna0Elem_12 = new TCna0Elem_1(parent);
        assertNotNull(tCna0Elem_12);
        AlphanumericVar sibling = new AlphanumericVar(parent, 1);
        TCna0Elem_1 tCna0Elem_13 = new TCna0Elem_1(parent, sibling);
        assertNotNull(tCna0Elem_13);
    }

    @Test
    void testInitialize() {
        assertNotNull(tCna0Elem_1.getSCnaCnaNumElem());

    }

    // property sCnaCnaNumElem
    @Test
    void testGetterSetterSCnaCnaNumElem() {

        assertNotNull(tCna0Elem_1.getSCnaCnaNumElem());

        String vString = "A";
        tCna0Elem_1.setSCnaCnaNumElem(vString); // String
        assertEquals(asStr(vString), asStr(tCna0Elem_1.getSCnaCnaNumElem()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tCna0Elem_1.setSCnaCnaNumElem(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tCna0Elem_1.getSCnaCnaNumElem()));
    }
}

