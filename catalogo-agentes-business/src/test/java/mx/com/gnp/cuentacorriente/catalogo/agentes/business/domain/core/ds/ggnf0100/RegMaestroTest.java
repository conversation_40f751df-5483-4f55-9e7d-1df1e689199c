package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-MAESTRO.
 *
 */
class RegMaestroTest {

    private RegMaestro regMaestro;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regMaestro = new RegMaestro();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegMaestro regMaestro2 = new RegMaestro(parent);
        assertNotNull(regMaestro2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 800);
        RegMaestro regMaestro3 = new RegMaestro(parent, sibling);
        assertNotNull(regMaestro3);
    }

    @Test
    void testInitialize() {


    }

    // property datosSaeta
    @Test
    void testGetterSetterDatosSaeta() {

        assertNotNull(regMaestro.getDatosSaeta());
    }

    // property datosCocoa
    @Test
    void testGetterSetterDatosCocoa() {

        assertNotNull(regMaestro.getDatosCocoa());
    }

    // property datosCons
    @Test
    void testGetterSetterDatosCons() {

        // Property: datosSaeta.datosCons -> 05 DATOS-CONS 9(7)
        assertNotNull(regMaestro.getDatosCons());
        int vint = 1234567;
        regMaestro.setDatosCons(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setDatosCons(vINumericValue); // INumericValue
    }

    // property pkRfc
    @Test
    void testGetterSetterPkRfc() {

        // Property: datosSaeta.pkRfc -> 05 PK-RFC
        assertNotNull(regMaestro.getPkRfc());
    }

    // property pkRfc1
    @Test
    void testGetterSetterPkRfc1() {

        // Property: datosSaeta.pkRfc.pkRfc1 -> 07 PK-RFC1 X(13)
        assertNotNull(regMaestro.getPkRfc1());
        String vString = "ABCabc1230";
        regMaestro.setPkRfc1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkRfc1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRfc2
    @Test
    void testGetterSetterPkRfc2() {

        // Property: datosSaeta.pkRfc.pkRfc2 -> 07 PK-RFC2 XX
        assertNotNull(regMaestro.getPkRfc2());
        String vString = "AB";
        regMaestro.setPkRfc2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRfc2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkClaveNal
    @Test
    void testGetterSetterPkClaveNal() {

        // Property: datosSaeta.pkClaveNal -> 05 PK-CLAVE-NAL 9(07)
        assertNotNull(regMaestro.getPkClaveNal());
        int vint = 1234567;
        regMaestro.setPkClaveNal(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkClaveNal(vINumericValue); // INumericValue
    }

    // property pkClaveProv
    @Test
    void testGetterSetterPkClaveProv() {

        // Property: datosSaeta.pkClaveProv -> 05 PK-CLAVE-PROV 9(07)
        assertNotNull(regMaestro.getPkClaveProv());
        int vint = 1234567;
        regMaestro.setPkClaveProv(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkClaveProv(vINumericValue); // INumericValue
    }

    // property pkNombre
    @Test
    void testGetterSetterPkNombre() {

        // Property: datosSaeta.pkNombre -> 05 PK-NOMBRE X(35)
        assertNotNull(regMaestro.getPkNombre());
        String vString = "ABCabc1230";
        regMaestro.setPkNombre(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNombre(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCompania
    @Test
    void testGetterSetterPkCompania() {

        // Property: datosSaeta.pkCompania -> 05 PK-COMPANIA X(01)
        assertNotNull(regMaestro.getPkCompania());
        String vString = "A";
        regMaestro.setPkCompania(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCompania(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFecConexion
    @Test
    void testGetterSetterPkFecConexion() {

        // Property: datosSaeta.pkFecConexion -> 05 PK-FEC-CONEXION 9(06) REDEFINED BY PK-FEC-CONEXIONR
        assertNotNull(regMaestro.getPkFecConexion());
        int vint = 123456;
        regMaestro.setPkFecConexion(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecConexion(vINumericValue); // INumericValue
    }

    // property pkFecConexionr
    @Test
    void testGetterSetterPkFecConexionr() {

        // Property: datosSaeta.pkFecConexionr -> 05 PK-FEC-CONEXIONR REDEFINES PK-FEC-CONEXION X(6)
        assertNotNull(regMaestro.getPkFecConexionr());
        String vString = "ABCabc";
        regMaestro.setPkFecConexionr(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regMaestro.setPkFecConexionr(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCalleP
    @Test
    void testGetterSetterPkCalleP() {

        // Property: datosSaeta.pkCalleP -> 05 PK-CALLE-P X(35)
        assertNotNull(regMaestro.getPkCalleP());
        String vString = "ABCabc1230";
        regMaestro.setPkCalleP(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkCalleP(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkColoniaP
    @Test
    void testGetterSetterPkColoniaP() {

        // Property: datosSaeta.pkColoniaP -> 05 PK-COLONIA-P X(20)
        assertNotNull(regMaestro.getPkColoniaP());
        String vString = "ABCabc1230";
        regMaestro.setPkColoniaP(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkColoniaP(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCodPostP
    @Test
    void testGetterSetterPkCodPostP() {

        // Property: datosSaeta.pkCodPostP -> 05 PK-COD-POST-P 9(05)
        assertNotNull(regMaestro.getPkCodPostP());
        int vint = 12345;
        regMaestro.setPkCodPostP(vint); // int
        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regMaestro.setPkCodPostP(vINumericValue); // INumericValue
    }

    // property pkPoblP
    @Test
    void testGetterSetterPkPoblP() {

        // Property: datosSaeta.pkPoblP -> 05 PK-POBL-P X(25)
        assertNotNull(regMaestro.getPkPoblP());
        String vString = "ABCabc1230";
        regMaestro.setPkPoblP(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkPoblP(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkEstadoP
    @Test
    void testGetterSetterPkEstadoP() {

        // Property: datosSaeta.pkEstadoP -> 05 PK-ESTADO-P 99
        assertNotNull(regMaestro.getPkEstadoP());
        int vint = 12;
        regMaestro.setPkEstadoP(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkEstadoP(vINumericValue); // INumericValue
    }

    // property pkPaisP
    @Test
    void testGetterSetterPkPaisP() {

        // Property: datosSaeta.pkPaisP -> 05 PK-PAIS-P 99
        assertNotNull(regMaestro.getPkPaisP());
        int vint = 12;
        regMaestro.setPkPaisP(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkPaisP(vINumericValue); // INumericValue
    }

    // property pkTelefP
    @Test
    void testGetterSetterPkTelefP() {

        // Property: datosSaeta.pkTelefP -> 05 PK-TELEF-P 9(12) REDEFINED BY PK-TELEF-PR
        assertNotNull(regMaestro.getPkTelefP());
        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        regMaestro.setPkTelefP(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        regMaestro.setPkTelefP(vINumericValue); // INumericValue
    }

    // property pkTelefPr
    @Test
    void testGetterSetterPkTelefPr() {

        // Property: datosSaeta.pkTelefPr -> 05 PK-TELEF-PR REDEFINES PK-TELEF-P X(12)
        assertNotNull(regMaestro.getPkTelefPr());
        String vString = "ABCabc1230";
        regMaestro.setPkTelefPr(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkTelefPr(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCalleC
    @Test
    void testGetterSetterPkCalleC() {

        // Property: datosSaeta.pkCalleC -> 05 PK-CALLE-C X(35)
        assertNotNull(regMaestro.getPkCalleC());
        String vString = "ABCabc1230";
        regMaestro.setPkCalleC(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkCalleC(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkColoniaC
    @Test
    void testGetterSetterPkColoniaC() {

        // Property: datosSaeta.pkColoniaC -> 05 PK-COLONIA-C X(20)
        assertNotNull(regMaestro.getPkColoniaC());
        String vString = "ABCabc1230";
        regMaestro.setPkColoniaC(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkColoniaC(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCodPostC
    @Test
    void testGetterSetterPkCodPostC() {

        // Property: datosSaeta.pkCodPostC -> 05 PK-COD-POST-C 9(05)
        assertNotNull(regMaestro.getPkCodPostC());
        int vint = 12345;
        regMaestro.setPkCodPostC(vint); // int
        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regMaestro.setPkCodPostC(vINumericValue); // INumericValue
    }

    // property pkPoblC
    @Test
    void testGetterSetterPkPoblC() {

        // Property: datosSaeta.pkPoblC -> 05 PK-POBL-C X(20)
        assertNotNull(regMaestro.getPkPoblC());
        String vString = "ABCabc1230";
        regMaestro.setPkPoblC(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkPoblC(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkEstadoC
    @Test
    void testGetterSetterPkEstadoC() {

        // Property: datosSaeta.pkEstadoC -> 05 PK-ESTADO-C 99
        assertNotNull(regMaestro.getPkEstadoC());
        int vint = 12;
        regMaestro.setPkEstadoC(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkEstadoC(vINumericValue); // INumericValue
    }

    // property pkPaisC
    @Test
    void testGetterSetterPkPaisC() {

        // Property: datosSaeta.pkPaisC -> 05 PK-PAIS-C 99
        assertNotNull(regMaestro.getPkPaisC());
        int vint = 12;
        regMaestro.setPkPaisC(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkPaisC(vINumericValue); // INumericValue
    }

    // property pkTelefC
    @Test
    void testGetterSetterPkTelefC() {

        // Property: datosSaeta.pkTelefC -> 05 PK-TELEF-C 9(12) REDEFINED BY PK-TELEF-CR
        assertNotNull(regMaestro.getPkTelefC());
        BigDecimal vBigDecimal = new BigDecimal("123456789012");
        regMaestro.setPkTelefC(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(12, 0);;
        vINumericValue.setValue("123456789012");
        regMaestro.setPkTelefC(vINumericValue); // INumericValue
    }

    // property pkTelefCr
    @Test
    void testGetterSetterPkTelefCr() {

        // Property: datosSaeta.pkTelefCr -> 05 PK-TELEF-CR REDEFINES PK-TELEF-C X(12)
        assertNotNull(regMaestro.getPkTelefCr());
        String vString = "ABCabc1230";
        regMaestro.setPkTelefCr(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkTelefCr(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFecNacim
    @Test
    void testGetterSetterPkFecNacim() {

        // Property: datosSaeta.pkFecNacim -> 05 PK-FEC-NACIM 9(6)
        assertNotNull(regMaestro.getPkFecNacim());
        int vint = 123456;
        regMaestro.setPkFecNacim(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecNacim(vINumericValue); // INumericValue
    }

    // property pkNacionalidad
    @Test
    void testGetterSetterPkNacionalidad() {

        // Property: datosSaeta.pkNacionalidad -> 05 PK-NACIONALIDAD 99
        assertNotNull(regMaestro.getPkNacionalidad());
        int vint = 12;
        regMaestro.setPkNacionalidad(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkNacionalidad(vINumericValue); // INumericValue
    }

    // property pkSexo
    @Test
    void testGetterSetterPkSexo() {

        // Property: datosSaeta.pkSexo -> 05 PK-SEXO X
        assertNotNull(regMaestro.getPkSexo());
        String vString = "A";
        regMaestro.setPkSexo(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkSexo(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkEdoCivil
    @Test
    void testGetterSetterPkEdoCivil() {

        // Property: datosSaeta.pkEdoCivil -> 05 PK-EDO-CIVIL X
        assertNotNull(regMaestro.getPkEdoCivil());
        String vString = "A";
        regMaestro.setPkEdoCivil(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkEdoCivil(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkEscolaridad
    @Test
    void testGetterSetterPkEscolaridad() {

        // Property: datosSaeta.pkEscolaridad -> 05 PK-ESCOLARIDAD 9
        assertNotNull(regMaestro.getPkEscolaridad());
        int vint = 1;
        regMaestro.setPkEscolaridad(vint); // int
        NumericVar vINumericValue = new NumericVar(1, 0);;
        vINumericValue.setValue("1");
        regMaestro.setPkEscolaridad(vINumericValue); // INumericValue
    }

    // property pkNomConyuge
    @Test
    void testGetterSetterPkNomConyuge() {

        // Property: datosSaeta.pkNomConyuge -> 05 PK-NOM-CONYUGE X(35)
        assertNotNull(regMaestro.getPkNomConyuge());
        String vString = "ABCabc1230";
        regMaestro.setPkNomConyuge(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNomConyuge(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFecCasam
    @Test
    void testGetterSetterPkFecCasam() {

        // Property: datosSaeta.pkFecCasam -> 05 PK-FEC-CASAM 9(6)
        assertNotNull(regMaestro.getPkFecCasam());
        int vint = 123456;
        regMaestro.setPkFecCasam(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecCasam(vINumericValue); // INumericValue
    }

    // property pkStatus
    @Test
    void testGetterSetterPkStatus() {

        // Property: datosSaeta.pkStatus -> 05 PK-STATUS X
        assertNotNull(regMaestro.getPkStatus());
        String vString = "A";
        regMaestro.setPkStatus(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkStatus(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkMotStatus
    @Test
    void testGetterSetterPkMotStatus() {

        // Property: datosSaeta.pkMotStatus -> 05 PK-MOT-STATUS 99
        assertNotNull(regMaestro.getPkMotStatus());
        int vint = 12;
        regMaestro.setPkMotStatus(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkMotStatus(vINumericValue); // INumericValue
    }

    // property pkFecStatus
    @Test
    void testGetterSetterPkFecStatus() {

        // Property: datosSaeta.pkFecStatus -> 05 PK-FEC-STATUS 9(6)
        assertNotNull(regMaestro.getPkFecStatus());
        int vint = 123456;
        regMaestro.setPkFecStatus(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecStatus(vINumericValue); // INumericValue
    }

    // property pkFecAlta
    @Test
    void testGetterSetterPkFecAlta() {

        // Property: datosSaeta.pkFecAlta -> 05 PK-FEC-ALTA 9(6)
        assertNotNull(regMaestro.getPkFecAlta());
        int vint = 123456;
        regMaestro.setPkFecAlta(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecAlta(vINumericValue); // INumericValue
    }

    // property pkFecTerm
    @Test
    void testGetterSetterPkFecTerm() {

        // Property: datosSaeta.pkFecTerm -> 05 PK-FEC-TERM 9(6)
        assertNotNull(regMaestro.getPkFecTerm());
        int vint = 123456;
        regMaestro.setPkFecTerm(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecTerm(vINumericValue); // INumericValue
    }

    // property pkTipCedula
    @Test
    void testGetterSetterPkTipCedula() {

        // Property: datosSaeta.pkTipCedula -> 05 PK-TIP-CEDULA X
        assertNotNull(regMaestro.getPkTipCedula());
        String vString = "A";
        regMaestro.setPkTipCedula(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkTipCedula(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedula
    @Test
    void testGetterSetterPkNumCedula() {

        // Property: datosSaeta.pkNumCedula -> 05 PK-NUM-CEDULA X(20) REDEFINED BY PK-NUM-CEDULARRR
        assertNotNull(regMaestro.getPkNumCedula());
        String vString = "ABCabc1230";
        regMaestro.setPkNumCedula(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNumCedula(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedular
    @Test
    void testGetterSetterPkNumCedular() {

        // Property: datosSaeta.pkNumCedular -> 05 PK-NUM-CEDULAR REDEFINES PK-NUM-CEDULA
        assertNotNull(regMaestro.getPkNumCedular());
    }

    // property pkNumCedulaDos
    @Test
    void testGetterSetterPkNumCedulaDos() {

        // Property: datosSaeta.pkNumCedular.pkNumCedulaDos -> 07 PK-NUM-CEDULA-DOS XX
        assertNotNull(regMaestro.getPkNumCedulaDos());
        String vString = "AB";
        regMaestro.setPkNumCedulaDos(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkNumCedulaDos(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaR18
    @Test
    void testGetterSetterPkNumCedulaR18() {

        // Property: datosSaeta.pkNumCedular.pkNumCedulaR18 -> 07 PK-NUM-CEDULA-R18 X(18)
        assertNotNull(regMaestro.getPkNumCedulaR18());
        String vString = "ABCabc1230";
        regMaestro.setPkNumCedulaR18(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNumCedulaR18(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedularr
    @Test
    void testGetterSetterPkNumCedularr() {

        // Property: datosSaeta.pkNumCedularr -> 05 PK-NUM-CEDULARR REDEFINES PK-NUM-CEDULA
        assertNotNull(regMaestro.getPkNumCedularr());
    }

    // property pkNumCedulaTres
    @Test
    void testGetterSetterPkNumCedulaTres() {

        // Property: datosSaeta.pkNumCedularr.pkNumCedulaTres -> 07 PK-NUM-CEDULA-TRES XXX
        assertNotNull(regMaestro.getPkNumCedulaTres());
        String vString = "ABC";
        regMaestro.setPkNumCedulaTres(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regMaestro.setPkNumCedulaTres(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedularrr
    @Test
    void testGetterSetterPkNumCedularrr() {

        // Property: datosSaeta.pkNumCedularrr -> 05 PK-NUM-CEDULARRR REDEFINES PK-NUM-CEDULA
        assertNotNull(regMaestro.getPkNumCedularrr());
    }

    // property pkNumCedulaXx
    @Test
    void testGetterSetterPkNumCedulaXx() {

        // Property: datosSaeta.pkNumCedularrr.pkNumCedulaXx -> 07 PK-NUM-CEDULA-XX XX
        assertNotNull(regMaestro.getPkNumCedulaXx());
        String vString = "AB";
        regMaestro.setPkNumCedulaXx(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkNumCedulaXx(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaX
    @Test
    void testGetterSetterPkNumCedulaX() {

        // Property: datosSaeta.pkNumCedularrr.pkNumCedulaX -> 07 PK-NUM-CEDULA-X X
        assertNotNull(regMaestro.getPkNumCedulaX());
        String vString = "A";
        regMaestro.setPkNumCedulaX(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkNumCedulaX(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkIniCedula
    @Test
    void testGetterSetterPkIniCedula() {

        // Property: datosSaeta.pkIniCedula -> 05 PK-INI-CEDULA 9(6)
        assertNotNull(regMaestro.getPkIniCedula());
        int vint = 123456;
        regMaestro.setPkIniCedula(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkIniCedula(vINumericValue); // INumericValue
    }

    // property pkFinCedula
    @Test
    void testGetterSetterPkFinCedula() {

        // Property: datosSaeta.pkFinCedula -> 05 PK-FIN-CEDULA 9(6)
        assertNotNull(regMaestro.getPkFinCedula());
        int vint = 123456;
        regMaestro.setPkFinCedula(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFinCedula(vINumericValue); // INumericValue
    }

    // property pkRamoP_1
    @Test
    void testGetterSetterPkRamoP_1() {

        // Property: datosSaeta.pkRamoP_1 -> 05 PK-RAMO-P-1 X(02)
        assertNotNull(regMaestro.getPkRamoP_1());
        String vString = "AB";
        regMaestro.setPkRamoP_1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_2
    @Test
    void testGetterSetterPkRamoP_2() {

        // Property: datosSaeta.pkRamoP_2 -> 05 PK-RAMO-P-2 X(02)
        assertNotNull(regMaestro.getPkRamoP_2());
        String vString = "AB";
        regMaestro.setPkRamoP_2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_3
    @Test
    void testGetterSetterPkRamoP_3() {

        // Property: datosSaeta.pkRamoP_3 -> 05 PK-RAMO-P-3 X(02)
        assertNotNull(regMaestro.getPkRamoP_3());
        String vString = "AB";
        regMaestro.setPkRamoP_3(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_3(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_4
    @Test
    void testGetterSetterPkRamoP_4() {

        // Property: datosSaeta.pkRamoP_4 -> 05 PK-RAMO-P-4 X(02)
        assertNotNull(regMaestro.getPkRamoP_4());
        String vString = "AB";
        regMaestro.setPkRamoP_4(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_4(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_5
    @Test
    void testGetterSetterPkRamoP_5() {

        // Property: datosSaeta.pkRamoP_5 -> 05 PK-RAMO-P-5 X(02)
        assertNotNull(regMaestro.getPkRamoP_5());
        String vString = "AB";
        regMaestro.setPkRamoP_5(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_5(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_6
    @Test
    void testGetterSetterPkRamoP_6() {

        // Property: datosSaeta.pkRamoP_6 -> 05 PK-RAMO-P-6 X(02)
        assertNotNull(regMaestro.getPkRamoP_6());
        String vString = "AB";
        regMaestro.setPkRamoP_6(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_6(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_7
    @Test
    void testGetterSetterPkRamoP_7() {

        // Property: datosSaeta.pkRamoP_7 -> 05 PK-RAMO-P-7 X(02)
        assertNotNull(regMaestro.getPkRamoP_7());
        String vString = "AB";
        regMaestro.setPkRamoP_7(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_7(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_8
    @Test
    void testGetterSetterPkRamoP_8() {

        // Property: datosSaeta.pkRamoP_8 -> 05 PK-RAMO-P-8 X(02)
        assertNotNull(regMaestro.getPkRamoP_8());
        String vString = "AB";
        regMaestro.setPkRamoP_8(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_8(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_9
    @Test
    void testGetterSetterPkRamoP_9() {

        // Property: datosSaeta.pkRamoP_9 -> 05 PK-RAMO-P-9 X(02)
        assertNotNull(regMaestro.getPkRamoP_9());
        String vString = "AB";
        regMaestro.setPkRamoP_9(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_9(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRamoP_10
    @Test
    void testGetterSetterPkRamoP_10() {

        // Property: datosSaeta.pkRamoP_10 -> 05 PK-RAMO-P-10 X(02)
        assertNotNull(regMaestro.getPkRamoP_10());
        String vString = "AB";
        regMaestro.setPkRamoP_10(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkRamoP_10(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkObservOficS
    @Test
    void testGetterSetterPkObservOficS() {

        // Property: datosSaeta.pkObservOficS -> 05 PK-OBSERV-OFIC-S X(60)
        assertNotNull(regMaestro.getPkObservOficS());
        String vString = "ABCabc1230";
        regMaestro.setPkObservOficS(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkObservOficS(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkRegIva
    @Test
    void testGetterSetterPkRegIva() {

        // Property: datosSaeta.pkRegIva -> 05 PK-REG-IVA X(10)
        assertNotNull(regMaestro.getPkRegIva());
        String vString = "ABCabc1230";
        regMaestro.setPkRegIva(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkRegIva(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkTipo
    @Test
    void testGetterSetterPkTipo() {

        // Property: datosSaeta.pkTipo -> 05 PK-TIPO 99
        assertNotNull(regMaestro.getPkTipo());
        int vint = 12;
        regMaestro.setPkTipo(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkTipo(vINumericValue); // INumericValue
    }

    // property pkCodPago
    @Test
    void testGetterSetterPkCodPago() {

        // Property: datosSaeta.pkCodPago -> 05 PK-COD-PAGO 99
        assertNotNull(regMaestro.getPkCodPago());
        int vint = 12;
        regMaestro.setPkCodPago(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkCodPago(vINumericValue); // INumericValue
    }

    // property pkCvePagoImp
    @Test
    void testGetterSetterPkCvePagoImp() {

        // Property: datosSaeta.pkCvePagoImp -> 05 PK-CVE-PAGO-IMP 99
        assertNotNull(regMaestro.getPkCvePagoImp());
        int vint = 12;
        regMaestro.setPkCvePagoImp(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkCvePagoImp(vINumericValue); // INumericValue
    }

    // property pkPorIva
    @Test
    void testGetterSetterPkPorIva() {

        // Property: datosSaeta.pkPorIva -> 05 PK-POR-IVA 99V99
        assertNotNull(regMaestro.getPkPorIva());
        BigDecimal vBigDecimal = new BigDecimal("12.12");
        regMaestro.setPkPorIva(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(4, 2);;
        vINumericValue.setValue("12.12");
        regMaestro.setPkPorIva(vINumericValue); // INumericValue
    }

    // property pkPorIsr
    @Test
    void testGetterSetterPkPorIsr() {

        // Property: datosSaeta.pkPorIsr -> 05 PK-POR-ISR 99V99
        assertNotNull(regMaestro.getPkPorIsr());
        BigDecimal vBigDecimal = new BigDecimal("12.12");
        regMaestro.setPkPorIsr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(4, 2);;
        vINumericValue.setValue("12.12");
        regMaestro.setPkPorIsr(vINumericValue); // INumericValue
    }

    // property pkNumFianza
    @Test
    void testGetterSetterPkNumFianza() {

        // Property: datosSaeta.pkNumFianza -> 05 PK-NUM-FIANZA X(12)
        assertNotNull(regMaestro.getPkNumFianza());
        String vString = "ABCabc1230";
        regMaestro.setPkNumFianza(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNumFianza(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCanFianza
    @Test
    void testGetterSetterPkCanFianza() {

        // Property: datosSaeta.pkCanFianza -> 05 PK-CAN-FIANZA 9(7)V99
        assertNotNull(regMaestro.getPkCanFianza());
        BigDecimal vBigDecimal = new BigDecimal("1234567.12");
        regMaestro.setPkCanFianza(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(9, 2);;
        vINumericValue.setValue("1234567.12");
        regMaestro.setPkCanFianza(vINumericValue); // INumericValue
    }

    // property pkIniFianza
    @Test
    void testGetterSetterPkIniFianza() {

        // Property: datosSaeta.pkIniFianza -> 05 PK-INI-FIANZA 9(6)
        assertNotNull(regMaestro.getPkIniFianza());
        int vint = 123456;
        regMaestro.setPkIniFianza(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkIniFianza(vINumericValue); // INumericValue
    }

    // property pkFinFianza
    @Test
    void testGetterSetterPkFinFianza() {

        // Property: datosSaeta.pkFinFianza -> 05 PK-FIN-FIANZA 9(6)
        assertNotNull(regMaestro.getPkFinFianza());
        int vint = 123456;
        regMaestro.setPkFinFianza(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFinFianza(vINumericValue); // INumericValue
    }

    // property pkAgenteRec
    @Test
    void testGetterSetterPkAgenteRec() {

        // Property: datosSaeta.pkAgenteRec -> 05 PK-AGENTE-REC 9(7)
        assertNotNull(regMaestro.getPkAgenteRec());
        int vint = 1234567;
        regMaestro.setPkAgenteRec(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkAgenteRec(vINumericValue); // INumericValue
    }

    // property pkOtrasCias
    @Test
    void testGetterSetterPkOtrasCias() {

        // Property: datosSaeta.pkOtrasCias -> 05 PK-OTRAS-CIAS X(1)
        assertNotNull(regMaestro.getPkOtrasCias());
        String vString = "A";
        regMaestro.setPkOtrasCias(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkOtrasCias(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkEsGerente
    @Test
    void testGetterSetterPkEsGerente() {

        // Property: datosSaeta.pkEsGerente -> 05 PK-ES-GERENTE X(1)
        assertNotNull(regMaestro.getPkEsGerente());
        String vString = "A";
        regMaestro.setPkEsGerente(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkEsGerente(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkZonaNal
    @Test
    void testGetterSetterPkZonaNal() {

        // Property: datosSaeta.pkZonaNal -> 05 PK-ZONA-NAL 9(4)
        assertNotNull(regMaestro.getPkZonaNal());
        int vint = 1234;
        regMaestro.setPkZonaNal(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        regMaestro.setPkZonaNal(vINumericValue); // INumericValue
    }

    // property pkZonaProv
    @Test
    void testGetterSetterPkZonaProv() {

        // Property: datosSaeta.pkZonaProv -> 05 PK-ZONA-PROV 9(4)
        assertNotNull(regMaestro.getPkZonaProv());
        int vint = 1234;
        regMaestro.setPkZonaProv(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        regMaestro.setPkZonaProv(vINumericValue); // INumericValue
    }

    // property pkZonaNalGte
    @Test
    void testGetterSetterPkZonaNalGte() {

        // Property: datosSaeta.pkZonaNalGte -> 05 PK-ZONA-NAL-GTE 9(04)
        assertNotNull(regMaestro.getPkZonaNalGte());
        int vint = 1234;
        regMaestro.setPkZonaNalGte(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        regMaestro.setPkZonaNalGte(vINumericValue); // INumericValue
    }

    // property pkZonaProvGte
    @Test
    void testGetterSetterPkZonaProvGte() {

        // Property: datosSaeta.pkZonaProvGte -> 05 PK-ZONA-PROV-GTE 9(04)
        assertNotNull(regMaestro.getPkZonaProvGte());
        int vint = 1234;
        regMaestro.setPkZonaProvGte(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        regMaestro.setPkZonaProvGte(vINumericValue); // INumericValue
    }

    // property pkOficinaNal
    @Test
    void testGetterSetterPkOficinaNal() {

        // Property: datosSaeta.pkOficinaNal -> 05 PK-OFICINA-NAL 999
        assertNotNull(regMaestro.getPkOficinaNal());
        int vint = 123;
        regMaestro.setPkOficinaNal(vint); // int
        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regMaestro.setPkOficinaNal(vINumericValue); // INumericValue
    }

    // property pkOficinaProv
    @Test
    void testGetterSetterPkOficinaProv() {

        // Property: datosSaeta.pkOficinaProv -> 05 PK-OFICINA-PROV 999
        assertNotNull(regMaestro.getPkOficinaProv());
        int vint = 123;
        regMaestro.setPkOficinaProv(vint); // int
        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regMaestro.setPkOficinaProv(vINumericValue); // INumericValue
    }

    // property pkFecZonaNal
    @Test
    void testGetterSetterPkFecZonaNal() {

        // Property: datosSaeta.pkFecZonaNal -> 05 PK-FEC-ZONA-NAL 9(06)
        assertNotNull(regMaestro.getPkFecZonaNal());
        int vint = 123456;
        regMaestro.setPkFecZonaNal(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecZonaNal(vINumericValue); // INumericValue
    }

    // property pkFecZonaProv
    @Test
    void testGetterSetterPkFecZonaProv() {

        // Property: datosSaeta.pkFecZonaProv -> 05 PK-FEC-ZONA-PROV 9(06)
        assertNotNull(regMaestro.getPkFecZonaProv());
        int vint = 123456;
        regMaestro.setPkFecZonaProv(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecZonaProv(vINumericValue); // INumericValue
    }

    // property pkFecZonaNalGte
    @Test
    void testGetterSetterPkFecZonaNalGte() {

        // Property: datosSaeta.pkFecZonaNalGte -> 05 PK-FEC-ZONA-NAL-GTE 9(06)
        assertNotNull(regMaestro.getPkFecZonaNalGte());
        int vint = 123456;
        regMaestro.setPkFecZonaNalGte(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecZonaNalGte(vINumericValue); // INumericValue
    }

    // property pkFecZonaProvGte
    @Test
    void testGetterSetterPkFecZonaProvGte() {

        // Property: datosSaeta.pkFecZonaProvGte -> 05 PK-FEC-ZONA-PROV-GTE 9(06)
        assertNotNull(regMaestro.getPkFecZonaProvGte());
        int vint = 123456;
        regMaestro.setPkFecZonaProvGte(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecZonaProvGte(vINumericValue); // INumericValue
    }

    // property pkFecOficinaNal
    @Test
    void testGetterSetterPkFecOficinaNal() {

        // Property: datosSaeta.pkFecOficinaNal -> 05 PK-FEC-OFICINA-NAL 9(06)
        assertNotNull(regMaestro.getPkFecOficinaNal());
        int vint = 123456;
        regMaestro.setPkFecOficinaNal(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecOficinaNal(vINumericValue); // INumericValue
    }

    // property pkFecOficinaProv
    @Test
    void testGetterSetterPkFecOficinaProv() {

        // Property: datosSaeta.pkFecOficinaProv -> 05 PK-FEC-OFICINA-PROV 9(06)
        assertNotNull(regMaestro.getPkFecOficinaProv());
        int vint = 123456;
        regMaestro.setPkFecOficinaProv(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecOficinaProv(vINumericValue); // INumericValue
    }

    // property pkCveAgrup
    @Test
    void testGetterSetterPkCveAgrup() {

        // Property: datosSaeta.pkCveAgrup -> 05 PK-CVE-AGRUP 9
        assertNotNull(regMaestro.getPkCveAgrup());
        int vint = 1;
        regMaestro.setPkCveAgrup(vint); // int
        NumericVar vINumericValue = new NumericVar(1, 0);;
        vINumericValue.setValue("1");
        regMaestro.setPkCveAgrup(vINumericValue); // INumericValue
    }

    // property pkCveEnvio
    @Test
    void testGetterSetterPkCveEnvio() {

        // Property: datosSaeta.pkCveEnvio -> 05 PK-CVE-ENVIO 99
        assertNotNull(regMaestro.getPkCveEnvio());
        int vint = 12;
        regMaestro.setPkCveEnvio(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkCveEnvio(vINumericValue); // INumericValue
    }

    // property pkCalidad
    @Test
    void testGetterSetterPkCalidad() {

        // Property: datosSaeta.pkCalidad -> 05 PK-CALIDAD 99
        assertNotNull(regMaestro.getPkCalidad());
        int vint = 12;
        regMaestro.setPkCalidad(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkCalidad(vINumericValue); // INumericValue
    }

    // property pkJefeGpoNal
    @Test
    void testGetterSetterPkJefeGpoNal() {

        // Property: datosSaeta.pkJefeGpoNal -> 05 PK-JEFE-GPO-NAL 9(7)
        assertNotNull(regMaestro.getPkJefeGpoNal());
        int vint = 1234567;
        regMaestro.setPkJefeGpoNal(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkJefeGpoNal(vINumericValue); // INumericValue
    }

    // property pkJefeGpoProv
    @Test
    void testGetterSetterPkJefeGpoProv() {

        // Property: datosSaeta.pkJefeGpoProv -> 05 PK-JEFE-GPO-PROV 9(7)
        assertNotNull(regMaestro.getPkJefeGpoProv());
        int vint = 1234567;
        regMaestro.setPkJefeGpoProv(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkJefeGpoProv(vINumericValue); // INumericValue
    }

    // property pkFecConexionP
    @Test
    void testGetterSetterPkFecConexionP() {

        // Property: datosSaeta.pkFecConexionP -> 05 PK-FEC-CONEXION-P 9(6) REDEFINED BY PK-FEC-CONEXION-PR
        assertNotNull(regMaestro.getPkFecConexionP());
        int vint = 123456;
        regMaestro.setPkFecConexionP(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecConexionP(vINumericValue); // INumericValue
    }

    // property pkFecConexionPr
    @Test
    void testGetterSetterPkFecConexionPr() {

        // Property: datosSaeta.pkFecConexionPr -> 05 PK-FEC-CONEXION-PR REDEFINES PK-FEC-CONEXION-P X(6)
        assertNotNull(regMaestro.getPkFecConexionPr());
        String vString = "ABCabc";
        regMaestro.setPkFecConexionPr(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc");
        regMaestro.setPkFecConexionPr(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaP
    @Test
    void testGetterSetterPkNumCedulaP() {

        // Property: datosSaeta.pkNumCedulaP -> 05 PK-NUM-CEDULA-P X(20) REDEFINED BY PK-NUM-CEDULAP-RR
        assertNotNull(regMaestro.getPkNumCedulaP());
        String vString = "ABCabc1230";
        regMaestro.setPkNumCedulaP(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNumCedulaP(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaPr
    @Test
    void testGetterSetterPkNumCedulaPr() {

        // Property: datosSaeta.pkNumCedulaPr -> 05 PK-NUM-CEDULA-PR REDEFINES PK-NUM-CEDULA-P
        assertNotNull(regMaestro.getPkNumCedulaPr());
    }

    // property pkNumCedulaPDos
    @Test
    void testGetterSetterPkNumCedulaPDos() {

        // Property: datosSaeta.pkNumCedulaPr.pkNumCedulaPDos -> 07 PK-NUM-CEDULA-P-DOS XX
        assertNotNull(regMaestro.getPkNumCedulaPDos());
        String vString = "AB";
        regMaestro.setPkNumCedulaPDos(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        regMaestro.setPkNumCedulaPDos(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaPR18
    @Test
    void testGetterSetterPkNumCedulaPR18() {

        // Property: datosSaeta.pkNumCedulaPr.pkNumCedulaPR18 -> 07 PK-NUM-CEDULA-P-R18 X(18)
        assertNotNull(regMaestro.getPkNumCedulaPR18());
        String vString = "ABCabc1230";
        regMaestro.setPkNumCedulaPR18(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNumCedulaPR18(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulapRr
    @Test
    void testGetterSetterPkNumCedulapRr() {

        // Property: datosSaeta.pkNumCedulapRr -> 05 PK-NUM-CEDULAP-RR REDEFINES PK-NUM-CEDULA-P
        assertNotNull(regMaestro.getPkNumCedulapRr());
    }

    // property pkNumCedulaPTres
    @Test
    void testGetterSetterPkNumCedulaPTres() {

        // Property: datosSaeta.pkNumCedulapRr.pkNumCedulaPTres -> 07 PK-NUM-CEDULA-P-TRES XXX
        assertNotNull(regMaestro.getPkNumCedulaPTres());
        String vString = "ABC";
        regMaestro.setPkNumCedulaPTres(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        regMaestro.setPkNumCedulaPTres(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNumCedulaPR17
    @Test
    void testGetterSetterPkNumCedulaPR17() {

        // Property: datosSaeta.pkNumCedulapRr.pkNumCedulaPR17 -> 07 PK-NUM-CEDULA-P-R17 X(17)
        assertNotNull(regMaestro.getPkNumCedulaPR17());
        String vString = "ABCabc1230";
        regMaestro.setPkNumCedulaPR17(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkNumCedulaPR17(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFinCedulaP
    @Test
    void testGetterSetterPkFinCedulaP() {

        // Property: datosSaeta.pkFinCedulaP -> 05 PK-FIN-CEDULA-P 9(6)
        assertNotNull(regMaestro.getPkFinCedulaP());
        int vint = 123456;
        regMaestro.setPkFinCedulaP(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFinCedulaP(vINumericValue); // INumericValue
    }

    // property pkStatusProv
    @Test
    void testGetterSetterPkStatusProv() {

        // Property: datosSaeta.pkStatusProv -> 05 PK-STATUS-PROV X
        assertNotNull(regMaestro.getPkStatusProv());
        String vString = "A";
        regMaestro.setPkStatusProv(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkStatusProv(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkMotStatusProv
    @Test
    void testGetterSetterPkMotStatusProv() {

        // Property: datosSaeta.pkMotStatusProv -> 05 PK-MOT-STATUS-PROV 99
        assertNotNull(regMaestro.getPkMotStatusProv());
        int vint = 12;
        regMaestro.setPkMotStatusProv(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkMotStatusProv(vINumericValue); // INumericValue
    }

    // property pkGteZonaNal
    @Test
    void testGetterSetterPkGteZonaNal() {

        // Property: datosSaeta.pkGteZonaNal -> 05 PK-GTE-ZONA-NAL 9(07)
        assertNotNull(regMaestro.getPkGteZonaNal());
        int vint = 1234567;
        regMaestro.setPkGteZonaNal(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkGteZonaNal(vINumericValue); // INumericValue
    }

    // property pkGteZonaProv
    @Test
    void testGetterSetterPkGteZonaProv() {

        // Property: datosSaeta.pkGteZonaProv -> 05 PK-GTE-ZONA-PROV 9(07)
        assertNotNull(regMaestro.getPkGteZonaProv());
        int vint = 1234567;
        regMaestro.setPkGteZonaProv(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkGteZonaProv(vINumericValue); // INumericValue
    }

    // property pkFuente
    @Test
    void testGetterSetterPkFuente() {

        // Property: datosSaeta.pkFuente -> 05 PK-FUENTE 99
        assertNotNull(regMaestro.getPkFuente());
        int vint = 12;
        regMaestro.setPkFuente(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkFuente(vINumericValue); // INumericValue
    }

    // property pkGrupo
    @Test
    void testGetterSetterPkGrupo() {

        // Property: datosSaeta.pkGrupo -> 05 PK-GRUPO 99
        assertNotNull(regMaestro.getPkGrupo());
        int vint = 12;
        regMaestro.setPkGrupo(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkGrupo(vINumericValue); // INumericValue
    }

    // property pkSubgrupo
    @Test
    void testGetterSetterPkSubgrupo() {

        // Property: datosSaeta.pkSubgrupo -> 05 PK-SUBGRUPO 999
        assertNotNull(regMaestro.getPkSubgrupo());
        int vint = 123;
        regMaestro.setPkSubgrupo(vint); // int
        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regMaestro.setPkSubgrupo(vINumericValue); // INumericValue
    }

    // property pkExiste
    @Test
    void testGetterSetterPkExiste() {

        // Property: datosCocoa.pkExiste -> 05 PK-EXISTE X
        assertNotNull(regMaestro.getPkExiste());
        String vString = "A";
        regMaestro.setPkExiste(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkExiste(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCveSist
    @Test
    void testGetterSetterPkCveSist() {

        // Property: datosCocoa.pkCveSist -> 05 PK-CVE-SIST X
        assertNotNull(regMaestro.getPkCveSist());
        String vString = "A";
        regMaestro.setPkCveSist(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCveSist(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFecRehab
    @Test
    void testGetterSetterPkFecRehab() {

        // Property: datosCocoa.pkFecRehab -> 05 PK-FEC-REHAB 9(06)
        assertNotNull(regMaestro.getPkFecRehab());
        int vint = 123456;
        regMaestro.setPkFecRehab(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFecRehab(vINumericValue); // INumericValue
    }

    // property pkQuienExime
    @Test
    void testGetterSetterPkQuienExime() {

        // Property: datosCocoa.pkQuienExime -> 05 PK-QUIEN-EXIME X(35)
        assertNotNull(regMaestro.getPkQuienExime());
        String vString = "ABCabc1230";
        regMaestro.setPkQuienExime(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkQuienExime(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFolioInves
    @Test
    void testGetterSetterPkFolioInves() {

        // Property: datosCocoa.pkFolioInves -> 05 PK-FOLIO-INVES 9(07) REDEFINED BY PK-FOLIO-INVESR
        assertNotNull(regMaestro.getPkFolioInves());
        int vint = 1234567;
        regMaestro.setPkFolioInves(vint); // int
        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regMaestro.setPkFolioInves(vINumericValue); // INumericValue
    }

    // property pkFolioInvesr
    @Test
    void testGetterSetterPkFolioInvesr() {

        // Property: datosCocoa.pkFolioInvesr -> 05 PK-FOLIO-INVESR REDEFINES PK-FOLIO-INVES
        assertNotNull(regMaestro.getPkFolioInvesr());
    }

    // property pkFolio1
    @Test
    void testGetterSetterPkFolio1() {

        // Property: datosCocoa.pkFolioInvesr.pkFolio1 -> 07 PK-FOLIO1 999
        assertNotNull(regMaestro.getPkFolio1());
        int vint = 123;
        regMaestro.setPkFolio1(vint); // int
        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regMaestro.setPkFolio1(vINumericValue); // INumericValue
    }

    // property pkFolio2
    @Test
    void testGetterSetterPkFolio2() {

        // Property: datosCocoa.pkFolioInvesr.pkFolio2 -> 07 PK-FOLIO2 9999
        assertNotNull(regMaestro.getPkFolio2());
        int vint = 1234;
        regMaestro.setPkFolio2(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        regMaestro.setPkFolio2(vINumericValue); // INumericValue
    }

    // property pkAdicionales
    @Test
    void testGetterSetterPkAdicionales() {

        // Property: datosCocoa.pkAdicionales -> 05 PK-ADICIONALES X(60)
        assertNotNull(regMaestro.getPkAdicionales());
        String vString = "ABCabc1230";
        regMaestro.setPkAdicionales(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regMaestro.setPkAdicionales(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFotos
    @Test
    void testGetterSetterPkFotos() {

        // Property: datosCocoa.pkFotos -> 05 PK-FOTOS X(01)
        assertNotNull(regMaestro.getPkFotos());
        String vString = "A";
        regMaestro.setPkFotos(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkFotos(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkActaNac
    @Test
    void testGetterSetterPkActaNac() {

        // Property: datosCocoa.pkActaNac -> 05 PK-ACTA-NAC X(01)
        assertNotNull(regMaestro.getPkActaNac());
        String vString = "A";
        regMaestro.setPkActaNac(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkActaNac(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCertificado
    @Test
    void testGetterSetterPkCertificado() {

        // Property: datosCocoa.pkCertificado -> 05 PK-CERTIFICADO X(01)
        assertNotNull(regMaestro.getPkCertificado());
        String vString = "A";
        regMaestro.setPkCertificado(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCertificado(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCartaRecom
    @Test
    void testGetterSetterPkCartaRecom() {

        // Property: datosCocoa.pkCartaRecom -> 05 PK-CARTA-RECOM X(01)
        assertNotNull(regMaestro.getPkCartaRecom());
        String vString = "A";
        regMaestro.setPkCartaRecom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCartaRecom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkAltaShcp
    @Test
    void testGetterSetterPkAltaShcp() {

        // Property: datosCocoa.pkAltaShcp -> 05 PK-ALTA-SHCP X(01)
        assertNotNull(regMaestro.getPkAltaShcp());
        String vString = "A";
        regMaestro.setPkAltaShcp(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkAltaShcp(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkHomoclave
    @Test
    void testGetterSetterPkHomoclave() {

        // Property: datosCocoa.pkHomoclave -> 05 PK-HOMOCLAVE X(01)
        assertNotNull(regMaestro.getPkHomoclave());
        String vString = "A";
        regMaestro.setPkHomoclave(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkHomoclave(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCartaBaja
    @Test
    void testGetterSetterPkCartaBaja() {

        // Property: datosCocoa.pkCartaBaja -> 05 PK-CARTA-BAJA X(01)
        assertNotNull(regMaestro.getPkCartaBaja());
        String vString = "A";
        regMaestro.setPkCartaBaja(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCartaBaja(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCedulaDef
    @Test
    void testGetterSetterPkCedulaDef() {

        // Property: datosCocoa.pkCedulaDef -> 05 PK-CEDULA-DEF X(01)
        assertNotNull(regMaestro.getPkCedulaDef());
        String vString = "A";
        regMaestro.setPkCedulaDef(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCedulaDef(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkEdoCta
    @Test
    void testGetterSetterPkEdoCta() {

        // Property: datosCocoa.pkEdoCta -> 05 PK-EDO-CTA X(01)
        assertNotNull(regMaestro.getPkEdoCta());
        String vString = "A";
        regMaestro.setPkEdoCta(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkEdoCta(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFm2
    @Test
    void testGetterSetterPkFm2() {

        // Property: datosCocoa.pkFm2 -> 05 PK-FM2 X(01)
        assertNotNull(regMaestro.getPkFm2());
        String vString = "A";
        regMaestro.setPkFm2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkFm2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkSolicitud
    @Test
    void testGetterSetterPkSolicitud() {

        // Property: datosCocoa.pkSolicitud -> 05 PK-SOLICITUD X(01)
        assertNotNull(regMaestro.getPkSolicitud());
        String vString = "A";
        regMaestro.setPkSolicitud(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkSolicitud(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFas2
    @Test
    void testGetterSetterPkFas2() {

        // Property: datosCocoa.pkFas2 -> 05 PK-FAS2 X(01)
        assertNotNull(regMaestro.getPkFas2());
        String vString = "A";
        regMaestro.setPkFas2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkFas2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCartaArt10
    @Test
    void testGetterSetterPkCartaArt10() {

        // Property: datosCocoa.pkCartaArt10 -> 05 PK-CARTA-ART10 X(01)
        assertNotNull(regMaestro.getPkCartaArt10());
        String vString = "A";
        regMaestro.setPkCartaArt10(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCartaArt10(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkSeguroCol
    @Test
    void testGetterSetterPkSeguroCol() {

        // Property: datosCocoa.pkSeguroCol -> 05 PK-SEGURO-COL X(01)
        assertNotNull(regMaestro.getPkSeguroCol());
        String vString = "A";
        regMaestro.setPkSeguroCol(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkSeguroCol(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkContrato
    @Test
    void testGetterSetterPkContrato() {

        // Property: datosCocoa.pkContrato -> 05 PK-CONTRATO X(01)
        assertNotNull(regMaestro.getPkContrato());
        String vString = "A";
        regMaestro.setPkContrato(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkContrato(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkChequePagoDer
    @Test
    void testGetterSetterPkChequePagoDer() {

        // Property: datosCocoa.pkChequePagoDer -> 05 PK-CHEQUE-PAGO-DER X(01)
        assertNotNull(regMaestro.getPkChequePagoDer());
        String vString = "A";
        regMaestro.setPkChequePagoDer(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkChequePagoDer(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCopiaCedDef
    @Test
    void testGetterSetterPkCopiaCedDef() {

        // Property: datosCocoa.pkCopiaCedDef -> 05 PK-COPIA-CED-DEF X(01)
        assertNotNull(regMaestro.getPkCopiaCedDef());
        String vString = "A";
        regMaestro.setPkCopiaCedDef(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCopiaCedDef(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkCursoPade
    @Test
    void testGetterSetterPkCursoPade() {

        // Property: datosCocoa.pkCursoPade -> 05 PK-CURSO-PADE X(01)
        assertNotNull(regMaestro.getPkCursoPade());
        String vString = "A";
        regMaestro.setPkCursoPade(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkCursoPade(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkInvestigacion
    @Test
    void testGetterSetterPkInvestigacion() {

        // Property: datosCocoa.pkInvestigacion -> 05 PK-INVESTIGACION X(01)
        assertNotNull(regMaestro.getPkInvestigacion());
        String vString = "A";
        regMaestro.setPkInvestigacion(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkInvestigacion(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkFechaPlazo
    @Test
    void testGetterSetterPkFechaPlazo() {

        // Property: datosCocoa.pkFechaPlazo -> 05 PK-FECHA-PLAZO 9(6)
        assertNotNull(regMaestro.getPkFechaPlazo());
        int vint = 123456;
        regMaestro.setPkFechaPlazo(vint); // int
        NumericVar vINumericValue = new NumericVar(6, 0);;
        vINumericValue.setValue("123456");
        regMaestro.setPkFechaPlazo(vINumericValue); // INumericValue
    }

    // property pkTipCedulaAux
    @Test
    void testGetterSetterPkTipCedulaAux() {

        // Property: datosCocoa.pkTipCedulaAux -> 05 PK-TIP-CEDULA-AUX X
        assertNotNull(regMaestro.getPkTipCedulaAux());
        String vString = "A";
        regMaestro.setPkTipCedulaAux(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regMaestro.setPkTipCedulaAux(vIAlphanumericValue); // IAlphanumericValue
    }

    // property pkNivelNal
    @Test
    void testGetterSetterPkNivelNal() {

        // Property: datosCocoa.pkNivelNal -> 05 PK-NIVEL-NAL 99
        assertNotNull(regMaestro.getPkNivelNal());
        int vint = 12;
        regMaestro.setPkNivelNal(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkNivelNal(vINumericValue); // INumericValue
    }

    // property pkNivelProv
    @Test
    void testGetterSetterPkNivelProv() {

        // Property: datosCocoa.pkNivelProv -> 05 PK-NIVEL-PROV 99
        assertNotNull(regMaestro.getPkNivelProv());
        int vint = 12;
        regMaestro.setPkNivelProv(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        regMaestro.setPkNivelProv(vINumericValue); // INumericValue
    }
}

