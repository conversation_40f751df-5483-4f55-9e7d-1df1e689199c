package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-DCLGCCTPOB0.
 *
 */
class WsDclgcctpob0Test {

    private WsDclgcctpob0 wsDclgcctpob0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsDclgcctpob0 = new WsDclgcctpob0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsDclgcctpob0.setPobPobCve(0);
        assertEquals(0, wsDclgcctpob0.getPobPobCve());

        wsDclgcctpob0.setPobPobNom("");
        assertEquals("", wsDclgcctpob0.getPobPobNom());

        wsDclgcctpob0.setPobFecUltAct("");
        assertEquals("", wsDclgcctpob0.getPobFecUltAct());

        wsDclgcctpob0.setPobUsuCveAct("");
        assertEquals("", wsDclgcctpob0.getPobUsuCveAct());

    }

    // property pobPobCve
    @Test
    void testGetterSetterPobPobCve() {

        assertNotNull(wsDclgcctpob0.getPobPobCve());

        int vint = 123456789;
        wsDclgcctpob0.setPobPobCve(vint); // int
        assertEquals(asInt(vint), asInt(wsDclgcctpob0.getPobPobCve()));
    }

    // property pobPobNom
    @Test
    void testGetterSetterPobPobNom() {

        assertNotNull(wsDclgcctpob0.getPobPobNom());

        String vString = "ABCabc1230";
        wsDclgcctpob0.setPobPobNom(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctpob0.getPobPobNom()));
    }

    // property pobFecUltAct
    @Test
    void testGetterSetterPobFecUltAct() {

        assertNotNull(wsDclgcctpob0.getPobFecUltAct());

        String vString = "ABCabc1230";
        wsDclgcctpob0.setPobFecUltAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctpob0.getPobFecUltAct()));
    }

    // property pobUsuCveAct
    @Test
    void testGetterSetterPobUsuCveAct() {

        assertNotNull(wsDclgcctpob0.getPobUsuCveAct());

        String vString = "ABCabc12";
        wsDclgcctpob0.setPobUsuCveAct(vString); // String
        assertEquals(asStr(vString), asStr(wsDclgcctpob0.getPobUsuCveAct()));
    }
}

