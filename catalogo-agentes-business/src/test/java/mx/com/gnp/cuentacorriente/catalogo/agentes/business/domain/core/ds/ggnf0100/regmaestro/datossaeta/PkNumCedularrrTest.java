package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: PK-NUM-CEDULARRR.
 *
 */
class PkNumCedularrrTest {

    private PkNumCedularrr pkNumCedularrr;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        pkNumCedularrr = new PkNumCedularrr();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        PkNumCedularrr pkNumCedularrr2 = new PkNumCedularrr(parent);
        assertNotNull(pkNumCedularrr2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 20);
        PkNumCedularrr pkNumCedularrr3 = new PkNumCedularrr(parent, sibling);
        assertNotNull(pkNumCedularrr3);
    }

    @Test
    void testInitialize() {
        assertNotNull(pkNumCedularrr.getPkNumCedulaXx());

        assertNotNull(pkNumCedularrr.getPkNumCedulaX());

        assertNotNull(pkNumCedularrr.getPkNumCedulaR17());

    }

    // property pkNumCedulaXx
    @Test
    void testGetterSetterPkNumCedulaXx() {

        assertNotNull(pkNumCedularrr.getPkNumCedulaXx());

        String vString = "AB";
        pkNumCedularrr.setPkNumCedulaXx(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedularrr.getPkNumCedulaXx()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        pkNumCedularrr.setPkNumCedulaXx(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedularrr.getPkNumCedulaXx()));
    }

    // property pkNumCedulaX
    @Test
    void testGetterSetterPkNumCedulaX() {

        assertNotNull(pkNumCedularrr.getPkNumCedulaX());

        String vString = "A";
        pkNumCedularrr.setPkNumCedulaX(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedularrr.getPkNumCedulaX()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        pkNumCedularrr.setPkNumCedulaX(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedularrr.getPkNumCedulaX()));
    }

    // property pkNumCedulaR17
    @Test
    void testGetterSetterPkNumCedulaR17() {

        assertNotNull(pkNumCedularrr.getPkNumCedulaR17());

        String vString = "ABCabc1230";
        pkNumCedularrr.setPkNumCedulaR17(vString); // String
        assertEquals(asStr(vString), asStr(pkNumCedularrr.getPkNumCedulaR17()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        pkNumCedularrr.setPkNumCedulaR17(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(pkNumCedularrr.getPkNumCedulaR17()));
    }
}

