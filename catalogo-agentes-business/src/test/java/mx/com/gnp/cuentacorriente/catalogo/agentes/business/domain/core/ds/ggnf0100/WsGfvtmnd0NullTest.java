package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTMND0-NULL.
 *
 */
class WsGfvtmnd0NullTest {

    private WsGfvtmnd0Null wsGfvtmnd0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtmnd0Null = new WsGfvtmnd0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtmnd0Null.setMnd01(0);
        assertEquals(0, wsGfvtmnd0Null.getMnd01());

        wsGfvtmnd0Null.setMnd02(0);
        assertEquals(0, wsGfvtmnd0Null.getMnd02());

        wsGfvtmnd0Null.setMnd03(0);
        assertEquals(0, wsGfvtmnd0Null.getMnd03());

        wsGfvtmnd0Null.setMnd04(0);
        assertEquals(0, wsGfvtmnd0Null.getMnd04());

        wsGfvtmnd0Null.setMnd05(0);
        assertEquals(0, wsGfvtmnd0Null.getMnd05());

    }

    // property mnd01
    @Test
    void testGetterSetterMnd01() {

        assertNotNull(wsGfvtmnd0Null.getMnd01());

        int vint = 1234;
        wsGfvtmnd0Null.setMnd01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmnd0Null.getMnd01()));
    }

    // property mnd02
    @Test
    void testGetterSetterMnd02() {

        assertNotNull(wsGfvtmnd0Null.getMnd02());

        int vint = 1234;
        wsGfvtmnd0Null.setMnd02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmnd0Null.getMnd02()));
    }

    // property mnd03
    @Test
    void testGetterSetterMnd03() {

        assertNotNull(wsGfvtmnd0Null.getMnd03());

        int vint = 1234;
        wsGfvtmnd0Null.setMnd03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmnd0Null.getMnd03()));
    }

    // property mnd04
    @Test
    void testGetterSetterMnd04() {

        assertNotNull(wsGfvtmnd0Null.getMnd04());

        int vint = 1234;
        wsGfvtmnd0Null.setMnd04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmnd0Null.getMnd04()));
    }

    // property mnd05
    @Test
    void testGetterSetterMnd05() {

        assertNotNull(wsGfvtmnd0Null.getMnd05());

        int vint = 1234;
        wsGfvtmnd0Null.setMnd05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtmnd0Null.getMnd05()));
    }
}

