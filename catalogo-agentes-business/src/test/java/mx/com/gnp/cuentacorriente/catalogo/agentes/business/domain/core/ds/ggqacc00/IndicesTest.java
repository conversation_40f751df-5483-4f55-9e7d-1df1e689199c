package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICES.
 *
 */
class IndicesTest {

    private Indices indices;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indices = new Indices();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        indices.setWsI(0);
        assertEquals(0, indices.getWsI());

        indices.setWsJ(0);
        assertEquals(0, indices.getWsJ());

        indices.setWsX(0);
        assertEquals(0, indices.getWsX());

        indices.setWsY(0);
        assertEquals(0, indices.getWsY());

        indices.setWsInd(0);
        assertEquals(0, indices.getWsInd());

    }

    // property wsI
    @Test
    void testGetterSetterWsI() {

        assertNotNull(indices.getWsI());

        int vint = 1234;
        indices.setWsI(vint); // int
        assertEquals(asInt(vint), asInt(indices.getWsI()));
    }

    // property wsJ
    @Test
    void testGetterSetterWsJ() {

        assertNotNull(indices.getWsJ());

        int vint = 1234;
        indices.setWsJ(vint); // int
        assertEquals(asInt(vint), asInt(indices.getWsJ()));
    }

    // property wsX
    @Test
    void testGetterSetterWsX() {

        assertNotNull(indices.getWsX());

        int vint = 1234;
        indices.setWsX(vint); // int
        assertEquals(asInt(vint), asInt(indices.getWsX()));
    }

    // property wsY
    @Test
    void testGetterSetterWsY() {

        assertNotNull(indices.getWsY());

        int vint = 1234;
        indices.setWsY(vint); // int
        assertEquals(asInt(vint), asInt(indices.getWsY()));
    }

    // property wsInd
    @Test
    void testGetterSetterWsInd() {

        assertNotNull(indices.getWsInd());

        int vint = 1234;
        indices.setWsInd(vint); // int
        assertEquals(asInt(vint), asInt(indices.getWsInd()));
    }
}

