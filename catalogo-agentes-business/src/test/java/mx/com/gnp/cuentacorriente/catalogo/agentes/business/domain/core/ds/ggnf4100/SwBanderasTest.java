package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: SW-BANDERAS.
 *
 */
class SwBanderasTest {

    private SwBanderas swBanderas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        swBanderas = new SwBanderas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        swBanderas.setFiller1("");
        assertEquals("", swBanderas.getFiller1());

    }

    // property filler1
    @Test
    void testGetterSetterFiller1() {

        assertNotNull(swBanderas.getFiller1());

        String vString = "A";
        swBanderas.setFiller1(vString); // String
        assertEquals(asStr(vString), asStr(swBanderas.getFiller1()));
        // Conditionals (88) - FILLER1
        swBanderas.setSwFinCursor();
        assertTrue(swBanderas.isSwFinCursor());

    }
}

