package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-DATA-RECEPTOR.
 *
 */
class RpDataReceptorTest {

    private RpDataReceptor rpDataReceptor;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpDataReceptor = new RpDataReceptor();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        rpDataReceptor.setRpRecCae("");
        assertEquals("", rpDataReceptor.getRpRecCae());

        rpDataReceptor.setRpRecNum("");
        assertEquals("", rpDataReceptor.getRpRecNum());

        rpDataReceptor.setRpRecInt("");
        assertEquals("", rpDataReceptor.getRpRecInt());

        rpDataReceptor.setRpRecCol("");
        assertEquals("", rpDataReceptor.getRpRecCol());

        rpDataReceptor.setRpRecMndNom("");
        assertEquals("", rpDataReceptor.getRpRecMndNom());

        rpDataReceptor.setRpRecPobNom("");
        assertEquals("", rpDataReceptor.getRpRecPobNom());

        rpDataReceptor.setRpRecMndEdoCve("");
        assertEquals("", rpDataReceptor.getRpRecMndEdoCve());

        rpDataReceptor.setRpRecMndEdoNom("");
        assertEquals("", rpDataReceptor.getRpRecMndEdoNom());

        rpDataReceptor.setRpRecMndPaiCve("");
        assertEquals("", rpDataReceptor.getRpRecMndPaiCve());

        rpDataReceptor.setRpRecMndPaiNom("");
        assertEquals("", rpDataReceptor.getRpRecMndPaiNom());

        rpDataReceptor.setRpRecCpoCve(0);
        assertEquals(0, rpDataReceptor.getRpRecCpoCve());

        rpDataReceptor.setRpRecGerZonEnv(0);
        assertEquals(0, rpDataReceptor.getRpRecGerZonEnv());

    }

    // property rpRecCae
    @Test
    void testGetterSetterRpRecCae() {

        assertNotNull(rpDataReceptor.getRpRecCae());

        String vString = "ABCabc1230";
        rpDataReceptor.setRpRecCae(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecCae()));
    }

    // property rpRecNum
    @Test
    void testGetterSetterRpRecNum() {

        assertNotNull(rpDataReceptor.getRpRecNum());

        String vString = "ABCab";
        rpDataReceptor.setRpRecNum(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecNum()));
    }

    // property rpRecInt
    @Test
    void testGetterSetterRpRecInt() {

        assertNotNull(rpDataReceptor.getRpRecInt());

        String vString = "ABCab";
        rpDataReceptor.setRpRecInt(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecInt()));
    }

    // property rpRecCol
    @Test
    void testGetterSetterRpRecCol() {

        assertNotNull(rpDataReceptor.getRpRecCol());

        String vString = "ABCabc1230";
        rpDataReceptor.setRpRecCol(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecCol()));
    }

    // property rpRecMndNom
    @Test
    void testGetterSetterRpRecMndNom() {

        assertNotNull(rpDataReceptor.getRpRecMndNom());

        String vString = "ABCabc1230";
        rpDataReceptor.setRpRecMndNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecMndNom()));
    }

    // property rpRecPobNom
    @Test
    void testGetterSetterRpRecPobNom() {

        assertNotNull(rpDataReceptor.getRpRecPobNom());

        String vString = "ABCabc1230";
        rpDataReceptor.setRpRecPobNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecPobNom()));
    }

    // property rpRecMndEdoCve
    @Test
    void testGetterSetterRpRecMndEdoCve() {

        assertNotNull(rpDataReceptor.getRpRecMndEdoCve());

        String vString = "ABCa";
        rpDataReceptor.setRpRecMndEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecMndEdoCve()));
    }

    // property rpRecMndEdoNom
    @Test
    void testGetterSetterRpRecMndEdoNom() {

        assertNotNull(rpDataReceptor.getRpRecMndEdoNom());

        String vString = "ABCabc1230";
        rpDataReceptor.setRpRecMndEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecMndEdoNom()));
    }

    // property rpRecMndPaiCve
    @Test
    void testGetterSetterRpRecMndPaiCve() {

        assertNotNull(rpDataReceptor.getRpRecMndPaiCve());

        String vString = "ABC";
        rpDataReceptor.setRpRecMndPaiCve(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecMndPaiCve()));
    }

    // property rpRecMndPaiNom
    @Test
    void testGetterSetterRpRecMndPaiNom() {

        assertNotNull(rpDataReceptor.getRpRecMndPaiNom());

        String vString = "ABCabc1230";
        rpDataReceptor.setRpRecMndPaiNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataReceptor.getRpRecMndPaiNom()));
    }

    // property rpRecCpoCve
    @Test
    void testGetterSetterRpRecCpoCve() {

        assertNotNull(rpDataReceptor.getRpRecCpoCve());

        int vint = 12345;
        rpDataReceptor.setRpRecCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(rpDataReceptor.getRpRecCpoCve()));
    }

    // property rpRecGerZonEnv
    @Test
    void testGetterSetterRpRecGerZonEnv() {

        assertNotNull(rpDataReceptor.getRpRecGerZonEnv());

        int vint = 1234;
        rpDataReceptor.setRpRecGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(rpDataReceptor.getRpRecGerZonEnv()));
    }
}

