package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-DIRECCIONES.
 *
 */
class TablaDireccionesTest {

    private TablaDirecciones tablaDirecciones;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaDirecciones = new TablaDirecciones();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tabDirecciones
    @Test
    void testGetterSetterTabDirecciones() {

        assertNotNull(tablaDirecciones.getTabDirecciones());
        assertNotNull(tablaDirecciones.getTabDirecciones(1));
    }

    // property tbDirOfna
    @Test
    void testGetterSetterTbDirOfna() {

        // Property: tabDirecciones.tbDirOfna -> 07 TB-DIR-OFNA 9(04)
        assertNotNull(tablaDirecciones.getTbDirOfna());
        int vint = 1234;
        tablaDirecciones.setTbDirOfna(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tablaDirecciones.setTbDirOfna(vINumericValue); // INumericValue
    }

    // property tbDirCae
    @Test
    void testGetterSetterTbDirCae() {

        // Property: tabDirecciones.tbDirCae -> 07 TB-DIR-CAE X(40)
        assertNotNull(tablaDirecciones.getTbDirCae());
        String vString = "ABCabc1230";
        tablaDirecciones.setTbDirCae(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaDirecciones.setTbDirCae(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirNum
    @Test
    void testGetterSetterTbDirNum() {

        // Property: tabDirecciones.tbDirNum -> 07 TB-DIR-NUM X(05)
        assertNotNull(tablaDirecciones.getTbDirNum());
        String vString = "ABCab";
        tablaDirecciones.setTbDirNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tablaDirecciones.setTbDirNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirInt
    @Test
    void testGetterSetterTbDirInt() {

        // Property: tabDirecciones.tbDirInt -> 07 TB-DIR-INT X(05)
        assertNotNull(tablaDirecciones.getTbDirInt());
        String vString = "ABCab";
        tablaDirecciones.setTbDirInt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tablaDirecciones.setTbDirInt(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirCol
    @Test
    void testGetterSetterTbDirCol() {

        // Property: tabDirecciones.tbDirCol -> 07 TB-DIR-COL X(40)
        assertNotNull(tablaDirecciones.getTbDirCol());
        String vString = "ABCabc1230";
        tablaDirecciones.setTbDirCol(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaDirecciones.setTbDirCol(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirMndNom
    @Test
    void testGetterSetterTbDirMndNom() {

        // Property: tabDirecciones.tbDirMndNom -> 07 TB-DIR-MND-NOM X(50)
        assertNotNull(tablaDirecciones.getTbDirMndNom());
        String vString = "ABCabc1230";
        tablaDirecciones.setTbDirMndNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaDirecciones.setTbDirMndNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirPobNom
    @Test
    void testGetterSetterTbDirPobNom() {

        // Property: tabDirecciones.tbDirPobNom -> 07 TB-DIR-POB-NOM X(40)
        assertNotNull(tablaDirecciones.getTbDirPobNom());
        String vString = "ABCabc1230";
        tablaDirecciones.setTbDirPobNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaDirecciones.setTbDirPobNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirMndEdoCve
    @Test
    void testGetterSetterTbDirMndEdoCve() {

        // Property: tabDirecciones.tbDirMndEdoCve -> 07 TB-DIR-MND-EDO-CVE X(04)
        assertNotNull(tablaDirecciones.getTbDirMndEdoCve());
        String vString = "ABCa";
        tablaDirecciones.setTbDirMndEdoCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tablaDirecciones.setTbDirMndEdoCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirMndEdoNom
    @Test
    void testGetterSetterTbDirMndEdoNom() {

        // Property: tabDirecciones.tbDirMndEdoNom -> 07 TB-DIR-MND-EDO-NOM X(30)
        assertNotNull(tablaDirecciones.getTbDirMndEdoNom());
        String vString = "ABCabc1230";
        tablaDirecciones.setTbDirMndEdoNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaDirecciones.setTbDirMndEdoNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirMndPaiCve
    @Test
    void testGetterSetterTbDirMndPaiCve() {

        // Property: tabDirecciones.tbDirMndPaiCve -> 07 TB-DIR-MND-PAI-CVE X(03)
        assertNotNull(tablaDirecciones.getTbDirMndPaiCve());
        String vString = "ABC";
        tablaDirecciones.setTbDirMndPaiCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABC");
        tablaDirecciones.setTbDirMndPaiCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirMndPaiNom
    @Test
    void testGetterSetterTbDirMndPaiNom() {

        // Property: tabDirecciones.tbDirMndPaiNom -> 07 TB-DIR-MND-PAI-NOM X(30)
        assertNotNull(tablaDirecciones.getTbDirMndPaiNom());
        String vString = "ABCabc1230";
        tablaDirecciones.setTbDirMndPaiNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tablaDirecciones.setTbDirMndPaiNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbDirCpoCve
    @Test
    void testGetterSetterTbDirCpoCve() {

        // Property: tabDirecciones.tbDirCpoCve -> 07 TB-DIR-CPO-CVE 9(05)
        assertNotNull(tablaDirecciones.getTbDirCpoCve());
        int vint = 12345;
        tablaDirecciones.setTbDirCpoCve(vint); // int
        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        tablaDirecciones.setTbDirCpoCve(vINumericValue); // INumericValue
    }

    // property tbDirGerZonEnv
    @Test
    void testGetterSetterTbDirGerZonEnv() {

        // Property: tabDirecciones.tbDirGerZonEnv -> 07 TB-DIR-GER-ZON-ENV 9(04)
        assertNotNull(tablaDirecciones.getTbDirGerZonEnv());
        int vint = 1234;
        tablaDirecciones.setTbDirGerZonEnv(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tablaDirecciones.setTbDirGerZonEnv(vINumericValue); // INumericValue
    }
}

