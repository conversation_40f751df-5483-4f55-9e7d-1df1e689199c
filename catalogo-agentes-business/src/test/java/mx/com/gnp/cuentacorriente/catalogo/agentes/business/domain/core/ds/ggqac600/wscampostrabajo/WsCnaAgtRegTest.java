package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-CNA-AGT-REG.
 *
 */
class WsCnaAgtRegTest {

    private WsCnaAgtReg wsCnaAgtReg;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsCnaAgtReg = new WsCnaAgtReg();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsCnaAgtReg wsCnaAgtReg2 = new WsCnaAgtReg(parent);
        assertNotNull(wsCnaAgtReg2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 34);
        WsCnaAgtReg wsCnaAgtReg3 = new WsCnaAgtReg(parent, sibling);
        assertNotNull(wsCnaAgtReg3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsCnaAgtReg.getWsCnaOfnCve());

        assertNotNull(wsCnaAgtReg.getWsCnaTfpCve());

        assertNotNull(wsCnaAgtReg.getWsCnaMteCve());

        assertNotNull(wsCnaAgtReg.getWsCnaGerZonEnv());

        assertNotNull(wsCnaAgtReg.getWsCnaCtbNumCta());

        assertNotNull(wsCnaAgtReg.getWsCnaPjeIva());

        assertNotNull(wsCnaAgtReg.getWsCnaPjeIsr());

        assertNotNull(wsCnaAgtReg.getWsCnaDsgCve());

    }

    // property wsCnaOfnCve
    @Test
    void testGetterSetterWsCnaOfnCve() {

        assertNotNull(wsCnaAgtReg.getWsCnaOfnCve());

        int vint = 1234;
        wsCnaAgtReg.setWsCnaOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCnaAgtReg.getWsCnaOfnCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCnaAgtReg.setWsCnaOfnCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCnaAgtReg.getWsCnaOfnCve()));
    }

    // property wsCnaTfpCve
    @Test
    void testGetterSetterWsCnaTfpCve() {

        assertNotNull(wsCnaAgtReg.getWsCnaTfpCve());

        int vint = 1234;
        wsCnaAgtReg.setWsCnaTfpCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCnaAgtReg.getWsCnaTfpCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCnaAgtReg.setWsCnaTfpCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCnaAgtReg.getWsCnaTfpCve()));
    }

    // property wsCnaMteCve
    @Test
    void testGetterSetterWsCnaMteCve() {

        assertNotNull(wsCnaAgtReg.getWsCnaMteCve());

        int vint = 1234;
        wsCnaAgtReg.setWsCnaMteCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCnaAgtReg.getWsCnaMteCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCnaAgtReg.setWsCnaMteCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCnaAgtReg.getWsCnaMteCve()));
    }

    // property wsCnaGerZonEnv
    @Test
    void testGetterSetterWsCnaGerZonEnv() {

        assertNotNull(wsCnaAgtReg.getWsCnaGerZonEnv());

        int vint = 1234;
        wsCnaAgtReg.setWsCnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(wsCnaAgtReg.getWsCnaGerZonEnv()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCnaAgtReg.setWsCnaGerZonEnv(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCnaAgtReg.getWsCnaGerZonEnv()));
    }

    // property wsCnaCtbNumCta
    @Test
    void testGetterSetterWsCnaCtbNumCta() {

        assertNotNull(wsCnaAgtReg.getWsCnaCtbNumCta());

        String vString = "ABCabc1230";
        wsCnaAgtReg.setWsCnaCtbNumCta(vString); // String
        assertEquals(asStr(vString), asStr(wsCnaAgtReg.getWsCnaCtbNumCta()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsCnaAgtReg.setWsCnaCtbNumCta(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsCnaAgtReg.getWsCnaCtbNumCta()));
    }

    // property wsCnaPjeIva
    @Test
    void testGetterSetterWsCnaPjeIva() {

        assertNotNull(wsCnaAgtReg.getWsCnaPjeIva());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCnaAgtReg.setWsCnaPjeIva(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsCnaAgtReg.getWsCnaPjeIva().toBigDecimal());

        int vint = 123;
        wsCnaAgtReg.setWsCnaPjeIva(vint); // int
        assertEquals(asInt(vint), asInt(wsCnaAgtReg.getWsCnaPjeIva()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCnaAgtReg.setWsCnaPjeIva(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCnaAgtReg.getWsCnaPjeIva()));
    }

    // property wsCnaPjeIsr
    @Test
    void testGetterSetterWsCnaPjeIsr() {

        assertNotNull(wsCnaAgtReg.getWsCnaPjeIsr());

        BigDecimal vBigDecimal = new BigDecimal("123.12");
        wsCnaAgtReg.setWsCnaPjeIsr(vBigDecimal); // BigDecimal
        assertEquals(vBigDecimal, wsCnaAgtReg.getWsCnaPjeIsr().toBigDecimal());

        int vint = 123;
        wsCnaAgtReg.setWsCnaPjeIsr(vint); // int
        assertEquals(asInt(vint), asInt(wsCnaAgtReg.getWsCnaPjeIsr()));

        NumericVar vINumericValue = new NumericVar(5, 2);;
        vINumericValue.setValue("123.12");
        wsCnaAgtReg.setWsCnaPjeIsr(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCnaAgtReg.getWsCnaPjeIsr()));
    }

    // property wsCnaDsgCve
    @Test
    void testGetterSetterWsCnaDsgCve() {

        assertNotNull(wsCnaAgtReg.getWsCnaDsgCve());

        int vint = 1234;
        wsCnaAgtReg.setWsCnaDsgCve(vint); // int
        assertEquals(asInt(vint), asInt(wsCnaAgtReg.getWsCnaDsgCve()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsCnaAgtReg.setWsCnaDsgCve(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsCnaAgtReg.getWsCnaDsgCve()));
    }
}

