package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-FIN.
 *
 */
class TFinTest {

    private TFin tFin;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tFin = new TFin(1);
        tFin.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TFin tFin2 = new TFin(parent, 1);
        assertNotNull(tFin2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TFin tFin3 = new TFin(parent, sibling, 1);
        assertNotNull(tFin3);
    }

    @Test
    void testInitialize() {

    }

    // property tFinElem
    @Test
    void testGetterSetterTFinElem() {

        assertNotNull(tFin.getTFinElem());
    }

    // property sTablaFin
    @Test
    void testGetterSetterSTablaFin() {

        // Property: tFinElem.sTablaFin -> 20 S-TABLA-FIN X(09)
        assertNotNull(tFin.getSTablaFin());
        String vString = "ABCabc123";
        tFin.setSTablaFin(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc123");
        tFin.setSTablaFin(vIAlphanumericValue); // IAlphanumericValue
    }
}

