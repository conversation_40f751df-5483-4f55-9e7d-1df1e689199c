package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-DATE-SIN-G.
 *
 */
class WsAuxDateSinGTest {

    private WsAuxDateSinG wsAuxDateSinG;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxDateSinG = new WsAuxDateSinG();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAuxDateSinG wsAuxDateSinG2 = new WsAuxDateSinG(parent);
        assertNotNull(wsAuxDateSinG2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsAuxDateSinG wsAuxDateSinG3 = new WsAuxDateSinG(parent, sibling);
        assertNotNull(wsAuxDateSinG3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAuxDateSinG.getWsAuxDateSinGAaaa());

        assertNotNull(wsAuxDateSinG.getWsAuxDateSinGMm());

        assertNotNull(wsAuxDateSinG.getWsAuxDateSinGDd());

    }

    // property wsAuxDateSinGAaaa
    @Test
    void testGetterSetterWsAuxDateSinGAaaa() {

        assertNotNull(wsAuxDateSinG.getWsAuxDateSinGAaaa());

        int vint = 1234;
        wsAuxDateSinG.setWsAuxDateSinGAaaa(vint); // int
        assertEquals(asInt(vint), asInt(wsAuxDateSinG.getWsAuxDateSinGAaaa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsAuxDateSinG.setWsAuxDateSinGAaaa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAuxDateSinG.getWsAuxDateSinGAaaa()));
    }

    // property wsAuxDateSinGMm
    @Test
    void testGetterSetterWsAuxDateSinGMm() {

        assertNotNull(wsAuxDateSinG.getWsAuxDateSinGMm());

        int vint = 12;
        wsAuxDateSinG.setWsAuxDateSinGMm(vint); // int
        assertEquals(asInt(vint), asInt(wsAuxDateSinG.getWsAuxDateSinGMm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAuxDateSinG.setWsAuxDateSinGMm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAuxDateSinG.getWsAuxDateSinGMm()));
    }

    // property wsAuxDateSinGDd
    @Test
    void testGetterSetterWsAuxDateSinGDd() {

        assertNotNull(wsAuxDateSinG.getWsAuxDateSinGDd());

        int vint = 12;
        wsAuxDateSinG.setWsAuxDateSinGDd(vint); // int
        assertEquals(asInt(vint), asInt(wsAuxDateSinG.getWsAuxDateSinGDd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsAuxDateSinG.setWsAuxDateSinGDd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsAuxDateSinG.getWsAuxDateSinGDd()));
    }
}

