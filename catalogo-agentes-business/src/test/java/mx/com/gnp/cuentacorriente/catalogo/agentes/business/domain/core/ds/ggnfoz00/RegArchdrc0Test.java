package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: REG-ARCHDRC0.
 *
 */
class RegArchdrc0Test {

    private RegArchdrc0 regArchdrc0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        regArchdrc0 = new RegArchdrc0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        RegArchdrc0 regArchdrc02 = new RegArchdrc0(parent);
        assertNotNull(regArchdrc02);
        AlphanumericVar sibling = new AlphanumericVar(parent, 109);
        RegArchdrc0 regArchdrc03 = new RegArchdrc0(parent, sibling);
        assertNotNull(regArchdrc03);
    }

    @Test
    void testInitialize() {
        assertNotNull(regArchdrc0.getDraCve5());

        assertNotNull(regArchdrc0.getAgtIdr5());

        assertNotNull(regArchdrc0.getEmpCve5());

        assertNotNull(regArchdrc0.getCnaNum5());

        assertNotNull(regArchdrc0.getDrcNomAut5());

        assertNotNull(regArchdrc0.getDrcFecAut5());

        assertNotNull(regArchdrc0.getDrcFecIniCio5());

        assertNotNull(regArchdrc0.getDrcFecFinCio5());

        assertNotNull(regArchdrc0.getFiller11());

        assertNotNull(regArchdrc0.getDrcPjeCom53());

        assertNotNull(regArchdrc0.getFiller12());

        assertNotNull(regArchdrc0.getDrcPjeCom57());

        assertNotNull(regArchdrc0.getDrcIdrAgt5());

        assertNotNull(regArchdrc0.getDrcIdrGte5());

        assertNotNull(regArchdrc0.getEssRek5());

    }

    // property draCve5
    @Test
    void testGetterSetterDraCve5() {

        assertNotNull(regArchdrc0.getDraCve5());

        int vint = 12345;
        regArchdrc0.setDraCve5(vint); // int
        assertEquals(asInt(vint), asInt(regArchdrc0.getDraCve5()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchdrc0.setDraCve5(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdrc0.getDraCve5()));
    }

    // property agtIdr5
    @Test
    void testGetterSetterAgtIdr5() {

        assertNotNull(regArchdrc0.getAgtIdr5());

        int vint = 123456789;
        regArchdrc0.setAgtIdr5(vint); // int
        assertEquals(asInt(vint), asInt(regArchdrc0.getAgtIdr5()));

        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        regArchdrc0.setAgtIdr5(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdrc0.getAgtIdr5()));
    }

    // property empCve5
    @Test
    void testGetterSetterEmpCve5() {

        assertNotNull(regArchdrc0.getEmpCve5());

        String vString = "ABCab";
        regArchdrc0.setEmpCve5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getEmpCve5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        regArchdrc0.setEmpCve5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getEmpCve5()));
    }

    // property cnaNum5
    @Test
    void testGetterSetterCnaNum5() {

        assertNotNull(regArchdrc0.getCnaNum5());

        int vint = 12345;
        regArchdrc0.setCnaNum5(vint); // int
        assertEquals(asInt(vint), asInt(regArchdrc0.getCnaNum5()));

        NumericVar vINumericValue = new NumericVar(5, 0);;
        vINumericValue.setValue("12345");
        regArchdrc0.setCnaNum5(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdrc0.getCnaNum5()));
    }

    // property drcNomAut5
    @Test
    void testGetterSetterDrcNomAut5() {

        assertNotNull(regArchdrc0.getDrcNomAut5());

        String vString = "ABCabc1230";
        regArchdrc0.setDrcNomAut5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getDrcNomAut5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdrc0.setDrcNomAut5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getDrcNomAut5()));
    }

    // property drcFecAut5
    @Test
    void testGetterSetterDrcFecAut5() {

        assertNotNull(regArchdrc0.getDrcFecAut5());

        String vString = "ABCabc1230";
        regArchdrc0.setDrcFecAut5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getDrcFecAut5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdrc0.setDrcFecAut5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getDrcFecAut5()));
    }

    // property drcFecIniCio5
    @Test
    void testGetterSetterDrcFecIniCio5() {

        assertNotNull(regArchdrc0.getDrcFecIniCio5());

        String vString = "ABCabc1230";
        regArchdrc0.setDrcFecIniCio5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getDrcFecIniCio5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdrc0.setDrcFecIniCio5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getDrcFecIniCio5()));
    }

    // property drcFecFinCio5
    @Test
    void testGetterSetterDrcFecFinCio5() {

        assertNotNull(regArchdrc0.getDrcFecFinCio5());

        String vString = "ABCabc1230";
        regArchdrc0.setDrcFecFinCio5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getDrcFecFinCio5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        regArchdrc0.setDrcFecFinCio5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getDrcFecFinCio5()));
    }

    // property filler11
    @Test
    void testGetterSetterFiller11() {

        assertNotNull(regArchdrc0.getFiller11());

        String vString = "A";
        regArchdrc0.setFiller11(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getFiller11()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchdrc0.setFiller11(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getFiller11()));
    }

    // property drcPjeCom53
    @Test
    void testGetterSetterDrcPjeCom53() {

        assertNotNull(regArchdrc0.getDrcPjeCom53());

        int vint = 123;
        regArchdrc0.setDrcPjeCom53(vint); // int
        assertEquals(asInt(vint), asInt(regArchdrc0.getDrcPjeCom53()));

        NumericVar vINumericValue = new NumericVar(3, 0);;
        vINumericValue.setValue("123");
        regArchdrc0.setDrcPjeCom53(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdrc0.getDrcPjeCom53()));
    }

    // property filler12
    @Test
    void testGetterSetterFiller12() {

        assertNotNull(regArchdrc0.getFiller12());

        String vString = "A";
        regArchdrc0.setFiller12(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getFiller12()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchdrc0.setFiller12(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getFiller12()));
    }

    // property drcPjeCom57
    @Test
    void testGetterSetterDrcPjeCom57() {

        assertNotNull(regArchdrc0.getDrcPjeCom57());

        int vint = 1234567;
        regArchdrc0.setDrcPjeCom57(vint); // int
        assertEquals(asInt(vint), asInt(regArchdrc0.getDrcPjeCom57()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        regArchdrc0.setDrcPjeCom57(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(regArchdrc0.getDrcPjeCom57()));
    }

    // property drcIdrAgt5
    @Test
    void testGetterSetterDrcIdrAgt5() {

        assertNotNull(regArchdrc0.getDrcIdrAgt5());

        String vString = "A";
        regArchdrc0.setDrcIdrAgt5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getDrcIdrAgt5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchdrc0.setDrcIdrAgt5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getDrcIdrAgt5()));
    }

    // property drcIdrGte5
    @Test
    void testGetterSetterDrcIdrGte5() {

        assertNotNull(regArchdrc0.getDrcIdrGte5());

        String vString = "A";
        regArchdrc0.setDrcIdrGte5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getDrcIdrGte5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchdrc0.setDrcIdrGte5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getDrcIdrGte5()));
    }

    // property essRek5
    @Test
    void testGetterSetterEssRek5() {

        assertNotNull(regArchdrc0.getEssRek5());

        String vString = "A";
        regArchdrc0.setEssRek5(vString); // String
        assertEquals(asStr(vString), asStr(regArchdrc0.getEssRek5()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        regArchdrc0.setEssRek5(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(regArchdrc0.getEssRek5()));
    }
}

