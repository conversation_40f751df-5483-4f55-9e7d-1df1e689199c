package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: A990-CIF-CTL.
 *
 */
class A990CifCtlTest {

    private A990CifCtl a990CifCtl;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        a990CifCtl = new A990CifCtl();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        a990CifCtl.setA990RegLeiOfnA1(0);
        assertEquals(0, a990CifCtl.getA990RegLeiOfnA1());

        a990CifCtl.setA990RegLeiInsHfo(0);
        assertEquals(0, a990CifCtl.getA990RegLeiInsHfo());

        a990CifCtl.setA990RegLeiUpdHfo(0);
        assertEquals(0, a990CifCtl.getA990RegLeiUpdHfo());

    }

    // property a990RegLeiOfnA1
    @Test
    void testGetterSetterA990RegLeiOfnA1() {

        assertNotNull(a990CifCtl.getA990RegLeiOfnA1());

        int vint = 123456789;
        a990CifCtl.setA990RegLeiOfnA1(vint); // int
        assertEquals(asInt(vint), asInt(a990CifCtl.getA990RegLeiOfnA1()));
    }

    // property a990RegLeiInsHfo
    @Test
    void testGetterSetterA990RegLeiInsHfo() {

        assertNotNull(a990CifCtl.getA990RegLeiInsHfo());

        int vint = 123456789;
        a990CifCtl.setA990RegLeiInsHfo(vint); // int
        assertEquals(asInt(vint), asInt(a990CifCtl.getA990RegLeiInsHfo()));
    }

    // property a990RegLeiUpdHfo
    @Test
    void testGetterSetterA990RegLeiUpdHfo() {

        assertNotNull(a990CifCtl.getA990RegLeiUpdHfo());

        int vint = 123456789;
        a990CifCtl.setA990RegLeiUpdHfo(vint); // int
        assertEquals(asInt(vint), asInt(a990CifCtl.getA990RegLeiUpdHfo()));
    }
}

