package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTCNA0-NULL00.
 *
 */
class WsGfvtcna0Null00Test {

    private WsGfvtcna0Null00 wsGfvtcna0Null00;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtcna0Null00 = new WsGfvtcna0Null00();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsGfvtcna0Null00 wsGfvtcna0Null002 = new WsGfvtcna0Null00(parent);
        assertNotNull(wsGfvtcna0Null002);
        AlphanumericVar sibling = new AlphanumericVar(parent, 72);
        WsGfvtcna0Null00 wsGfvtcna0Null003 = new WsGfvtcna0Null00(parent, sibling);
        assertNotNull(wsGfvtcna0Null003);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsGfvtcna0Null00.getCna0100());

    }

    // property cna0100
    @Test
    void testGetterSetterCna0100() {

        assertNotNull(wsGfvtcna0Null00.getCna0100());
        assertNotNull(wsGfvtcna0Null00.getCna0100(1));

        int vint = 1234;
        wsGfvtcna0Null00.getCna0100().at(1).setValue(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtcna0Null00.getCna0100().at(1)));

        BinaryVar vBinaryVar = new BinaryVar(4, 0);;
        vBinaryVar.setValue("1234");
        wsGfvtcna0Null00.getCna0100().at(1).setValue(vBinaryVar); // BinaryVar
        assertEquals(asInt(vBinaryVar), asInt(wsGfvtcna0Null00.getCna0100().at(1)));
    }
}

