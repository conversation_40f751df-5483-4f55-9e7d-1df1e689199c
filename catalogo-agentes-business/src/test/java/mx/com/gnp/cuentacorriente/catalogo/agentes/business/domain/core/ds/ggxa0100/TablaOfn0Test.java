package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLA-OFN0.
 *
 */
class TablaOfn0Test {

    private TablaOfn0 tablaOfn0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablaOfn0 = new TablaOfn0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tOfn0
    @Test
    void testGetterSetterTOfn0() {

        assertNotNull(tablaOfn0.getTOfn0());
        assertNotNull(tablaOfn0.getTOfn0(1));
    }

    // property tOfn0Elem
    @Test
    void testGetterSetterTOfn0Elem() {

        // Property: tOfn0.tOfn0Elem -> 15 T-OFN0-ELEM UsedAsParameter
        assertNotNull(tablaOfn0.getTOfn0Elem());
    }

    // property sCnaOfnCve
    @Test
    void testGetterSetterSCnaOfnCve() {

        // Property: tOfn0.tOfn0Elem.sCnaOfnCve -> 20 S-CNA-OFN-CVE X(04)
        assertNotNull(tablaOfn0.getSCnaOfnCve());
        String vString = "ABCa";
        tablaOfn0.setSCnaOfnCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tablaOfn0.setSCnaOfnCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sOfnLimitad
    @Test
    void testGetterSetterSOfnLimitad() {

        // Property: tOfn0.tOfn0Elem.sOfnLimitad -> 20 S-OFN-LIMITAD X(01)
        assertNotNull(tablaOfn0.getSOfnLimitad());
        String vString = "A";
        tablaOfn0.setSOfnLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tablaOfn0.setSOfnLimitad(vIAlphanumericValue); // IAlphanumericValue
    }
}

