package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-DATA-AGENTE.
 *
 */
class RpDataAgenteTest {

    private RpDataAgente rpDataAgente;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpDataAgente = new RpDataAgente();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        rpDataAgente.setRpAgtRfc("");
        assertEquals("", rpDataAgente.getRpAgtRfc());

        rpDataAgente.setRpAgtNom("");
        assertEquals("", rpDataAgente.getRpAgtNom());

        rpDataAgente.setRpAgtApePat("");
        assertEquals("", rpDataAgente.getRpAgtApePat());

        rpDataAgente.setRpAgtApeMat("");
        assertEquals("", rpDataAgente.getRpAgtApeMat());

        rpDataAgente.setRpAgtRazSoc("");
        assertEquals("", rpDataAgente.getRpAgtRazSoc());

        rpDataAgente.setRpAgtNomConcatenado("");
        assertEquals("", rpDataAgente.getRpAgtNomConcatenado());

        rpDataAgente.setRpPraAgtCxxCurp("");
        assertEquals("", rpDataAgente.getRpPraAgtCxxCurp());

        rpDataAgente.setRpAgtNalCve("");
        assertEquals("", rpDataAgente.getRpAgtNalCve());

        rpDataAgente.setRpMteCve(0);
        assertEquals(0, rpDataAgente.getRpMteCve());

        rpDataAgente.setRpPefCve(0);
        assertEquals(0, rpDataAgente.getRpPefCve());

        rpDataAgente.setRpDmaCae("");
        assertEquals("", rpDataAgente.getRpDmaCae());

        rpDataAgente.setRpDmaNum("");
        assertEquals("", rpDataAgente.getRpDmaNum());

        rpDataAgente.setRpDmaInt("");
        assertEquals("", rpDataAgente.getRpDmaInt());

        rpDataAgente.setRpDmaCol("");
        assertEquals("", rpDataAgente.getRpDmaCol());

        rpDataAgente.setRpMndNom("");
        assertEquals("", rpDataAgente.getRpMndNom());

        rpDataAgente.setRpDmaCpoCve(0);
        assertEquals(0, rpDataAgente.getRpDmaCpoCve());

        rpDataAgente.setRpPobNom("");
        assertEquals("", rpDataAgente.getRpPobNom());

        rpDataAgente.setRpMndEdoCve("");
        assertEquals("", rpDataAgente.getRpMndEdoCve());

        rpDataAgente.setRpEdoEdoNom("");
        assertEquals("", rpDataAgente.getRpEdoEdoNom());

        rpDataAgente.setRpEdoPaiCve("");
        assertEquals("", rpDataAgente.getRpEdoPaiCve());

        rpDataAgente.setRpCmaDesTelefon("");
        assertEquals("", rpDataAgente.getRpCmaDesTelefon());

        rpDataAgente.setRpCnaGerZonEnv(0);
        assertEquals(0, rpDataAgente.getRpCnaGerZonEnv());

        rpDataAgente.setRpCnaOfnCve(0);
        assertEquals(0, rpDataAgente.getRpCnaOfnCve());

    }

    // property rpAgtRfc
    @Test
    void testGetterSetterRpAgtRfc() {

        assertNotNull(rpDataAgente.getRpAgtRfc());

        String vString = "ABCabc1230";
        rpDataAgente.setRpAgtRfc(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpAgtRfc()));
    }

    // property rpAgtNom
    @Test
    void testGetterSetterRpAgtNom() {

        assertNotNull(rpDataAgente.getRpAgtNom());

        String vString = "ABCabc1230";
        rpDataAgente.setRpAgtNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpAgtNom()));
    }

    // property rpAgtApePat
    @Test
    void testGetterSetterRpAgtApePat() {

        assertNotNull(rpDataAgente.getRpAgtApePat());

        String vString = "ABCabc1230";
        rpDataAgente.setRpAgtApePat(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpAgtApePat()));
    }

    // property rpAgtApeMat
    @Test
    void testGetterSetterRpAgtApeMat() {

        assertNotNull(rpDataAgente.getRpAgtApeMat());

        String vString = "ABCabc1230";
        rpDataAgente.setRpAgtApeMat(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpAgtApeMat()));
    }

    // property rpAgtRazSoc
    @Test
    void testGetterSetterRpAgtRazSoc() {

        assertNotNull(rpDataAgente.getRpAgtRazSoc());

        String vString = "ABCabc1230";
        rpDataAgente.setRpAgtRazSoc(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpAgtRazSoc()));
    }

    // property rpAgtNomConcatenado
    @Test
    void testGetterSetterRpAgtNomConcatenado() {

        assertNotNull(rpDataAgente.getRpAgtNomConcatenado());

        String vString = "ABCabc1230";
        rpDataAgente.setRpAgtNomConcatenado(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpAgtNomConcatenado()));
    }

    // property rpPraAgtCxxCurp
    @Test
    void testGetterSetterRpPraAgtCxxCurp() {

        assertNotNull(rpDataAgente.getRpPraAgtCxxCurp());

        String vString = "ABCabc1230";
        rpDataAgente.setRpPraAgtCxxCurp(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpPraAgtCxxCurp()));
    }

    // property rpAgtNalCve
    @Test
    void testGetterSetterRpAgtNalCve() {

        assertNotNull(rpDataAgente.getRpAgtNalCve());

        String vString = "ABC";
        rpDataAgente.setRpAgtNalCve(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpAgtNalCve()));
        // Conditionals (88) - RP-AGT-NAL-CVE
        rpDataAgente.setRpExtranjero();
        assertTrue(rpDataAgente.isRpExtranjero());

    }

    // property rpMteCve
    @Test
    void testGetterSetterRpMteCve() {

        assertNotNull(rpDataAgente.getRpMteCve());

        int vint = 1234;
        rpDataAgente.setRpMteCve(vint); // int
        assertEquals(asInt(vint), asInt(rpDataAgente.getRpMteCve()));
    }

    // property rpPefCve
    @Test
    void testGetterSetterRpPefCve() {

        assertNotNull(rpDataAgente.getRpPefCve());

        int vint = 1234;
        rpDataAgente.setRpPefCve(vint); // int
        assertEquals(asInt(vint), asInt(rpDataAgente.getRpPefCve()));
    }

    // property rpDmaCae
    @Test
    void testGetterSetterRpDmaCae() {

        assertNotNull(rpDataAgente.getRpDmaCae());

        String vString = "ABCabc1230";
        rpDataAgente.setRpDmaCae(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpDmaCae()));
    }

    // property rpDmaNum
    @Test
    void testGetterSetterRpDmaNum() {

        assertNotNull(rpDataAgente.getRpDmaNum());

        String vString = "ABCab";
        rpDataAgente.setRpDmaNum(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpDmaNum()));
    }

    // property rpDmaInt
    @Test
    void testGetterSetterRpDmaInt() {

        assertNotNull(rpDataAgente.getRpDmaInt());

        String vString = "ABCab";
        rpDataAgente.setRpDmaInt(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpDmaInt()));
    }

    // property rpDmaCol
    @Test
    void testGetterSetterRpDmaCol() {

        assertNotNull(rpDataAgente.getRpDmaCol());

        String vString = "ABCabc1230";
        rpDataAgente.setRpDmaCol(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpDmaCol()));
    }

    // property rpMndNom
    @Test
    void testGetterSetterRpMndNom() {

        assertNotNull(rpDataAgente.getRpMndNom());

        String vString = "ABCabc1230";
        rpDataAgente.setRpMndNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpMndNom()));
    }

    // property rpDmaCpoCve
    @Test
    void testGetterSetterRpDmaCpoCve() {

        assertNotNull(rpDataAgente.getRpDmaCpoCve());

        int vint = 12345;
        rpDataAgente.setRpDmaCpoCve(vint); // int
        assertEquals(asInt(vint), asInt(rpDataAgente.getRpDmaCpoCve()));
    }

    // property rpPobNom
    @Test
    void testGetterSetterRpPobNom() {

        assertNotNull(rpDataAgente.getRpPobNom());

        String vString = "ABCabc1230";
        rpDataAgente.setRpPobNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpPobNom()));
    }

    // property rpMndEdoCve
    @Test
    void testGetterSetterRpMndEdoCve() {

        assertNotNull(rpDataAgente.getRpMndEdoCve());

        String vString = "ABCa";
        rpDataAgente.setRpMndEdoCve(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpMndEdoCve()));
    }

    // property rpEdoEdoNom
    @Test
    void testGetterSetterRpEdoEdoNom() {

        assertNotNull(rpDataAgente.getRpEdoEdoNom());

        String vString = "ABCabc1230";
        rpDataAgente.setRpEdoEdoNom(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpEdoEdoNom()));
    }

    // property rpEdoPaiCve
    @Test
    void testGetterSetterRpEdoPaiCve() {

        assertNotNull(rpDataAgente.getRpEdoPaiCve());

        String vString = "ABC";
        rpDataAgente.setRpEdoPaiCve(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpEdoPaiCve()));
    }

    // property rpCmaDesTelefon
    @Test
    void testGetterSetterRpCmaDesTelefon() {

        assertNotNull(rpDataAgente.getRpCmaDesTelefon());

        String vString = "ABCabc1230";
        rpDataAgente.setRpCmaDesTelefon(vString); // String
        assertEquals(asStr(vString), asStr(rpDataAgente.getRpCmaDesTelefon()));
    }

    // property rpCnaGerZonEnv
    @Test
    void testGetterSetterRpCnaGerZonEnv() {

        assertNotNull(rpDataAgente.getRpCnaGerZonEnv());

        int vint = 1234;
        rpDataAgente.setRpCnaGerZonEnv(vint); // int
        assertEquals(asInt(vint), asInt(rpDataAgente.getRpCnaGerZonEnv()));
    }

    // property rpCnaOfnCve
    @Test
    void testGetterSetterRpCnaOfnCve() {

        assertNotNull(rpDataAgente.getRpCnaOfnCve());

        int vint = 1234;
        rpDataAgente.setRpCnaOfnCve(vint); // int
        assertEquals(asInt(vint), asInt(rpDataAgente.getRpCnaOfnCve()));
    }
}

