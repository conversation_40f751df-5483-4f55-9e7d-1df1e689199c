package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FECHAS.
 *
 */
class WsFechasTest {

    private WsFechas wsFechas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFechas = new WsFechas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsFechas.setWsProcesoAaaa(0);
        assertEquals(0, wsFechas.getWsProcesoAaaa());

        wsFechas.setWsProcesoMm(0);
        assertEquals(0, wsFechas.getWsProcesoMm());

        wsFechas.setWsFechaPaso("");
        assertEquals("", wsFechas.getWsFechaPaso());

        assertNotNull(wsFechas.getWsFechaMov());


        wsFechas.setWsFechaMovCev("");
        assertEquals("", wsFechas.getWsFechaMovCev());

        assertNotNull(wsFechas.getWsFechaAlta());


    }

    // property wsProcesoAaaa
    @Test
    void testGetterSetterWsProcesoAaaa() {

        assertNotNull(wsFechas.getWsProcesoAaaa());

        int vint = 1234;
        wsFechas.setWsProcesoAaaa(vint); // int
        assertEquals(asInt(vint), asInt(wsFechas.getWsProcesoAaaa()));
    }

    // property wsProcesoMm
    @Test
    void testGetterSetterWsProcesoMm() {

        assertNotNull(wsFechas.getWsProcesoMm());

        int vint = 12;
        wsFechas.setWsProcesoMm(vint); // int
        assertEquals(asInt(vint), asInt(wsFechas.getWsProcesoMm()));
    }

    // property wsFechaPaso
    @Test
    void testGetterSetterWsFechaPaso() {

        assertNotNull(wsFechas.getWsFechaPaso());

        String vString = "ABCabc12";
        wsFechas.setWsFechaPaso(vString); // String
        assertEquals(asStr(vString), asStr(wsFechas.getWsFechaPaso()));
    }

    // property wsFechaMov
    @Test
    void testGetterSetterWsFechaMov() {

        assertNotNull(wsFechas.getWsFechaMov());

        String vString = "ABCabc1230";
        wsFechas.setWsFechaMov(vString); // String
        assertEquals(asStr(vString), asStr(wsFechas.getWsFechaMov()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsFechas.setWsFechaMov(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFechas.getWsFechaMov()));
    }

    // property wsFechaMovR
    @Test
    void testGetterSetterWsFechaMovR() {

        assertNotNull(wsFechas.getWsFechaMovR());
    }

    // property wsFechaMovCev
    @Test
    void testGetterSetterWsFechaMovCev() {

        assertNotNull(wsFechas.getWsFechaMovCev());

        String vString = "ABCabc1230";
        wsFechas.setWsFechaMovCev(vString); // String
        assertEquals(asStr(vString), asStr(wsFechas.getWsFechaMovCev()));
    }

    // property wsFechaAlta
    @Test
    void testGetterSetterWsFechaAlta() {

        assertNotNull(wsFechas.getWsFechaAlta());

        String vString = "ABCabc1230";
        wsFechas.setWsFechaAlta(vString); // String
        assertEquals(asStr(vString), asStr(wsFechas.getWsFechaAlta()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        wsFechas.setWsFechaAlta(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFechas.getWsFechaAlta()));
    }

    // property wsFechaAltaR
    @Test
    void testGetterSetterWsFechaAltaR() {

        assertNotNull(wsFechas.getWsFechaAltaR());
    }

    // property wsFecMovAa
    @Test
    void testGetterSetterWsFecMovAa() {

        // Property: wsFechaMovR.wsFecMovAa -> 05 WS-FEC-MOV-AA 9(04)
        assertNotNull(wsFechas.getWsFecMovAa());
        int vint = 1234;
        wsFechas.setWsFecMovAa(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsFechas.setWsFecMovAa(vINumericValue); // INumericValue
    }

    // property wsFecMovGuion_1
    @Test
    void testGetterSetterWsFecMovGuion_1() {

        // Property: wsFechaMovR.wsFecMovGuion_1 -> 05 WS-FEC-MOV-GUION-1 X(01)
        assertNotNull(wsFechas.getWsFecMovGuion_1());
        String vString = "A";
        wsFechas.setWsFecMovGuion_1(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFechas.setWsFecMovGuion_1(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFecMovMm
    @Test
    void testGetterSetterWsFecMovMm() {

        // Property: wsFechaMovR.wsFecMovMm -> 05 WS-FEC-MOV-MM 9(02)
        assertNotNull(wsFechas.getWsFecMovMm());
        int vint = 12;
        wsFechas.setWsFecMovMm(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechas.setWsFecMovMm(vINumericValue); // INumericValue
    }

    // property wsFecMovGuion_2
    @Test
    void testGetterSetterWsFecMovGuion_2() {

        // Property: wsFechaMovR.wsFecMovGuion_2 -> 05 WS-FEC-MOV-GUION-2 X(01)
        assertNotNull(wsFechas.getWsFecMovGuion_2());
        String vString = "A";
        wsFechas.setWsFecMovGuion_2(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFechas.setWsFecMovGuion_2(vIAlphanumericValue); // IAlphanumericValue
    }

    // property wsFecMovDd
    @Test
    void testGetterSetterWsFecMovDd() {

        // Property: wsFechaMovR.wsFecMovDd -> 05 WS-FEC-MOV-DD 9(02)
        assertNotNull(wsFechas.getWsFecMovDd());
        int vint = 12;
        wsFechas.setWsFecMovDd(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechas.setWsFecMovDd(vINumericValue); // INumericValue
    }

    // property wsFecAltAa
    @Test
    void testGetterSetterWsFecAltAa() {

        // Property: wsFechaAltaR.wsFecAltAa -> 05 WS-FEC-ALT-AA 9(04)
        assertNotNull(wsFechas.getWsFecAltAa());
        int vint = 1234;
        wsFechas.setWsFecAltAa(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        wsFechas.setWsFecAltAa(vINumericValue); // INumericValue
    }

    // property wsFecAltMm
    @Test
    void testGetterSetterWsFecAltMm() {

        // Property: wsFechaAltaR.wsFecAltMm -> 05 WS-FEC-ALT-MM 9(02)
        assertNotNull(wsFechas.getWsFecAltMm());
        int vint = 12;
        wsFechas.setWsFecAltMm(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechas.setWsFecAltMm(vINumericValue); // INumericValue
    }

    // property wsFecAltDd
    @Test
    void testGetterSetterWsFecAltDd() {

        // Property: wsFechaAltaR.wsFecAltDd -> 05 WS-FEC-ALT-DD 9(02)
        assertNotNull(wsFechas.getWsFecAltDd());
        int vint = 12;
        wsFechas.setWsFecAltDd(vint); // int
        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        wsFechas.setWsFecAltDd(vINumericValue); // INumericValue
    }
}

