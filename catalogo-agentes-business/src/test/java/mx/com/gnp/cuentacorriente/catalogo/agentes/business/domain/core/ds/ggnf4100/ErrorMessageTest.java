package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: ERROR-MESSAGE.
 *
 */
class ErrorMessageTest {

    private ErrorMessage errorMessage;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        errorMessage = new ErrorMessage();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        ErrorMessage errorMessage2 = new ErrorMessage(parent);
        assertNotNull(errorMessage2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 722);
        ErrorMessage errorMessage3 = new ErrorMessage(parent, sibling);
        assertNotNull(errorMessage3);
    }

    @Test
    void testInitialize() {
        assertNotNull(errorMessage.getErrorLen());

        assertNotNull(errorMessage.getErrorText());

    }

    // property errorLen
    @Test
    void testGetterSetterErrorLen() {

        assertNotNull(errorMessage.getErrorLen());

        int vint = 1234;
        errorMessage.setErrorLen(vint); // int
        assertEquals(asInt(vint), asInt(errorMessage.getErrorLen()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        errorMessage.setErrorLen(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(errorMessage.getErrorLen()));
    }

    // property errorText
    @Test
    void testGetterSetterErrorText() {

        assertNotNull(errorMessage.getErrorText());
        assertNotNull(errorMessage.getErrorText(1));

        String vString = "ABCabc1230";
        errorMessage.getErrorText().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(errorMessage.getErrorText(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("ABCabc1230");
        errorMessage.getErrorText().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(errorMessage.getErrorText(1)));
    }
}

