package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-PAR.
 *
 */
class TParTest {

    private TPar tPar;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tPar = new TPar();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tParReg
    @Test
    void testGetterSetterTParReg() {

        assertNotNull(tPar.getTParReg());
        assertNotNull(tPar.getTParReg(1));
    }

    // property tParTxt
    @Test
    void testGetterSetterTParTxt() {

        // Property: tParReg.tParTxt -> 10 T-PAR-TXT X(08)
        assertNotNull(tPar.getTParTxt());
        String vString = "ABCabc12";
        tPar.setTParTxt(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tPar.setTParTxt(vIAlphanumericValue); // IAlphanumericValue
    }
}

