package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

class Ggxa0100Test {
    Ggxa0100 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggxa0100.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      testRun test conditions:
        swContratos : [1, 2]
    */
    @Test
    void testRun1() {
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(1);

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    @Test
    void testRun2() {
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(2);

        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      start test conditions:
        swContratos : [1, 2]
    */
    @Test
    void startTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(1);
        try {
            pgm.start();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void startTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwContratos(2);
            try {
                pgm.start();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p1000General test conditions:
        swContratos : [1, 2]
    */
    @Test
    void p1000GeneralTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(1);
        try {
            pgm.p1000General();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p1000GeneralTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwContratos(2);
            try {
                pgm.p1000General();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2000ProcesaAgt0 test conditions:
        swContratos : [1, 2, 0]
        agtPefCve : [2, 3]
        swFolios : [0, 1]
        wsContTfla : [57, 56]
        swFla2 : [1, 2]
        swFla3 : [1, 2]
        swGerencias : [0, 1]
        swRegiones : [0, 1]
        swOficinas : [0, 1]
        contadorProc : [1000, 1001]
    */
    @Test
    void p2000ProcesaAgt0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(1);
        pgm.getWsVariablesDb2().setAgtPefCve(2);
        pgm.getAreasDeTrabajo().setSwFolios(0);
        pgm.getAreasDeTrabajo().setWsContTfla(57);
        pgm.getAreasDeTrabajo().setSwFla2(1);
        pgm.getAreasDeTrabajo().setSwFla3(1);
        pgm.getAreasDeTrabajo().setSwGerencias(0);
        pgm.getAreasDeTrabajo().setSwRegiones(0);
        pgm.getAreasDeTrabajo().setSwOficinas(0);
        pgm.getAreasDeTrabajo().setContadorProc(1000);
        try {
            pgm.p2000ProcesaAgt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2000ProcesaAgt0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwContratos(2);
            pgm.getWsVariablesDb2().setAgtPefCve(3);
            pgm.getAreasDeTrabajo().setSwFolios(1);
            pgm.getAreasDeTrabajo().setWsContTfla(56);
            pgm.getAreasDeTrabajo().setSwFla2(2);
            pgm.getAreasDeTrabajo().setSwFla3(2);
            pgm.getAreasDeTrabajo().setSwGerencias(1);
            pgm.getAreasDeTrabajo().setSwRegiones(1);
            pgm.getAreasDeTrabajo().setSwOficinas(1);
            pgm.getAreasDeTrabajo().setContadorProc(1001);
            try {
                pgm.p2000ProcesaAgt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p2000ProcesaAgt0Test3() {
                //TODO setup test
                pgm.sqlDelayedParagraph();
                // Set test conditions
                pgm.getAreasDeTrabajo().setSwContratos(0);
                pgm.getWsVariablesDb2().setAgtPefCve(3);
                pgm.getAreasDeTrabajo().setSwFolios(1);
                pgm.getAreasDeTrabajo().setWsContTfla(56);
                pgm.getAreasDeTrabajo().setSwFla2(2);
                pgm.getAreasDeTrabajo().setSwFla3(2);
                pgm.getAreasDeTrabajo().setSwGerencias(1);
                pgm.getAreasDeTrabajo().setSwRegiones(1);
                pgm.getAreasDeTrabajo().setSwOficinas(1);
                pgm.getAreasDeTrabajo().setContadorProc(1001);
                try {
                    pgm.p2000ProcesaAgt0();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
    /*
      p2100CompactaCna test conditions:
        swContratos : [0, 1]
    */
    @Test
    void p2100CompactaCnaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(0);
        try {
            pgm.p2100CompactaCna();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2100CompactaCnaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwContratos(1);
            try {
                pgm.p2100CompactaCna();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2012FetchCurAgt0 test conditions:
        contadorProc : [1000, 1001]
    */
    @Test
    void p2012FetchCurAgt0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setContadorProc(1000);
        try {
            pgm.p2012FetchCurAgt0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2012FetchCurAgt0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setContadorProc(1001);
            try {
                pgm.p2012FetchCurAgt0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2003MueveExtagtSal test conditions:
        agtPefCve : [2, 3]
    */
    @Test
    void p2003MueveExtagtSalTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsVariablesDb2().setAgtPefCve(2);
        try {
            pgm.p2003MueveExtagtSal();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2003MueveExtagtSalTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsVariablesDb2().setAgtPefCve(3);
            try {
                pgm.p2003MueveExtagtSal();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3000ProcesaCna0 test conditions:
        swContratos : [0, 1]
    */
    @Test
    void p3000ProcesaCna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(0);
        try {
            pgm.p3000ProcesaCna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3000ProcesaCna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwContratos(1);
            try {
                pgm.p3000ProcesaCna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3010LlenaTablaTcna0 test conditions:
        wsCnaCnaNum : [9, 10]
    */
    @Test
    void p3010LlenaTablaTcna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsVariablesDb2().setWsCnaCnaNum(9);
        try {
            pgm.p3010LlenaTablaTcna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3010LlenaTablaTcna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsVariablesDb2().setWsCnaCnaNum(10);
            try {
                pgm.p3010LlenaTablaTcna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p3011MueveBlancos test conditions:
        wsCnaCnaNum : [9, 10]
    */
    @Test
    void p3011MueveBlancosTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getWsVariablesDb2().setWsCnaCnaNum(9);
        try {
            pgm.p3011MueveBlancos();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p3011MueveBlancosTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getWsVariablesDb2().setWsCnaCnaNum(10);
            try {
                pgm.p3011MueveBlancos();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2005FetchCurCna0 test conditions:
        swContratos : [0, 1]
    */
    @Test
    void p2005FetchCurCna0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwContratos(0);
        try {
            pgm.p2005FetchCurCna0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2005FetchCurCna0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwContratos(1);
            try {
                pgm.p2005FetchCurCna0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p4000ProcesaFla1 test conditions:
        swFolios : [0, 1]
    */
    @Test
    void p4000ProcesaFla1Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwFolios(0);
        try {
            pgm.p4000ProcesaFla1();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p4000ProcesaFla1Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwFolios(1);
            try {
                pgm.p4000ProcesaFla1();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2200CompactaFla test conditions:
        swFolios : [0, 1]
        wsContTfla : [57, 56]
        swFla2 : [1, 2]
        swFla3 : [1, 2]
    */
    @Test
    void p2200CompactaFlaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwFolios(0);
        pgm.getAreasDeTrabajo().setWsContTfla(57);
        pgm.getAreasDeTrabajo().setSwFla2(1);
        pgm.getAreasDeTrabajo().setSwFla3(1);
        try {
            pgm.p2200CompactaFla();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2200CompactaFlaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwFolios(1);
            pgm.getAreasDeTrabajo().setWsContTfla(56);
            pgm.getAreasDeTrabajo().setSwFla2(2);
            pgm.getAreasDeTrabajo().setSwFla3(2);
            try {
                pgm.p2200CompactaFla();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2009FetchCurFla0 test conditions:
        swFolios : [0, 1]
    */
    @Test
    void p2009FetchCurFla0Test1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwFolios(0);
        try {
            pgm.p2009FetchCurFla0();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2009FetchCurFla0Test2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwFolios(1);
            try {
                pgm.p2009FetchCurFla0();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2300CompactaGer test conditions:
        swGerencias : [0, 1]
    */
    @Test
    void p2300CompactaGerTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwGerencias(0);
        try {
            pgm.p2300CompactaGer();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2300CompactaGerTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwGerencias(1);
            try {
                pgm.p2300CompactaGer();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p7000Inicial test conditions:
        contadorProc : [1000, 1001]
    */
    @Test
    void p7000InicialTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setContadorProc(1000);
        try {
            pgm.p7000Inicial();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p7000InicialTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setContadorProc(1001);
            try {
                pgm.p7000Inicial();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2400CompactaRegion test conditions:
        swRegiones : [0, 1]
    */
    @Test
    void p2400CompactaRegionTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwRegiones(0);
        try {
            pgm.p2400CompactaRegion();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2400CompactaRegionTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwRegiones(1);
            try {
                pgm.p2400CompactaRegion();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p2500CompactaOficina test conditions:
        swOficinas : [0, 1]
    */
    @Test
    void p2500CompactaOficinaTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getAreasDeTrabajo().setSwOficinas(0);
        try {
            pgm.p2500CompactaOficina();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p2500CompactaOficinaTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getAreasDeTrabajo().setSwOficinas(1);
            try {
                pgm.p2500CompactaOficina();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      db2check test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn5 : ["W", "XXW"]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
    */
    @Test
    void db2checkTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn5("W");
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        try {
            pgm.db2check();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void db2checkTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn5("XXW");
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            try {
                pgm.db2check();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      db2checkEmail test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn5 : ["W", "XXW"]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
    */
    @Test
    void db2checkEmailTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn5("W");
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        try {
            pgm.db2checkEmail();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void db2checkEmailTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn5("XXW");
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            try {
                pgm.db2checkEmail();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p9000Abend test conditions:
        sqlcode : [-1, 0]
        sqlerrml : [1, 0]
        sqlwarn5 : ["W", "XXW"]
        sqlwarn6 : ["W", "XXW"]
        sqlwarn9 : ["W", "XXW"]
    */
    @Test
    void p9000AbendTest1() {
        //TODO setup test
        pgm.sqlDelayedParagraph();
        // Set test conditions
        pgm.getSqlca().setSqlcode(-1);
        pgm.getSqlca().setSqlerrml(1);
        pgm.getSqlca().setSqlwarn5("W");
        pgm.getSqlca().setSqlwarn6("W");
        pgm.getSqlca().setSqlwarn9("W");
        try {
            pgm.p9000Abend();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p9000AbendTest2() {
            //TODO setup test
            pgm.sqlDelayedParagraph();
            // Set test conditions
            pgm.getSqlca().setSqlcode(0);
            pgm.getSqlca().setSqlerrml(0);
            pgm.getSqlca().setSqlwarn5("XXW");
            pgm.getSqlca().setSqlwarn6("XXW");
            pgm.getSqlca().setSqlwarn9("XXW");
            try {
                pgm.p9000Abend();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
}
