package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TABLAS.
 *
 */
class TablasTest {

    private Tablas tablas;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tablas = new Tablas();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tablaImptes
    @Test
    void testGetterSetterTablaImptes() {

        assertNotNull(tablas.getTablaImptes());
    }

    // property tabImptes
    @Test
    void testGetterSetterTabImptes() {

        // Property: tablaImptes.tabImptes -> 03 TAB-IMPTES [2]
        assertNotNull(tablas.getTabImptes());
    }

    // property tbPercGravab
    @Test
    void testGetterSetterTbPercGravab() {

        // Property: tablaImptes.tabImptes.tbPercGravab -> 05 TB-PERC-GRAVAB S9(14)V99
        assertNotNull(tablas.getTbPercGravab());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbPercGravab(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbPercGravab(vINumericValue); // INumericValue
    }

    // property tbPercExenta
    @Test
    void testGetterSetterTbPercExenta() {

        // Property: tablaImptes.tabImptes.tbPercExenta -> 05 TB-PERC-EXENTA S9(14)V99
        assertNotNull(tablas.getTbPercExenta());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbPercExenta(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbPercExenta(vINumericValue); // INumericValue
    }

    // property tbIva
    @Test
    void testGetterSetterTbIva() {

        // Property: tablaImptes.tabImptes.tbIva -> 05 TB-IVA S9(14)V99
        assertNotNull(tablas.getTbIva());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbIva(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbIva(vINumericValue); // INumericValue
    }

    // property tbIsr
    @Test
    void testGetterSetterTbIsr() {

        // Property: tablaImptes.tabImptes.tbIsr -> 05 TB-ISR S9(14)V99
        assertNotNull(tablas.getTbIsr());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbIsr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbIsr(vINumericValue); // INumericValue
    }

    // property tbIvaAcr
    @Test
    void testGetterSetterTbIvaAcr() {

        // Property: tablaImptes.tabImptes.tbIvaAcr -> 05 TB-IVA-ACR S9(14)V99
        assertNotNull(tablas.getTbIvaAcr());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbIvaAcr(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbIvaAcr(vINumericValue); // INumericValue
    }

    // property tbIvaRtn
    @Test
    void testGetterSetterTbIvaRtn() {

        // Property: tablaImptes.tabImptes.tbIvaRtn -> 05 TB-IVA-RTN S9(14)V99
        assertNotNull(tablas.getTbIvaRtn());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbIvaRtn(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbIvaRtn(vINumericValue); // INumericValue
    }

    // property tbIsrRet
    @Test
    void testGetterSetterTbIsrRet() {

        // Property: tablaImptes.tabImptes.tbIsrRet -> 05 TB-ISR-RET S9(14)V99
        assertNotNull(tablas.getTbIsrRet());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbIsrRet(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbIsrRet(vINumericValue); // INumericValue
    }

    // property tbIpuCed
    @Test
    void testGetterSetterTbIpuCed() {

        // Property: tablaImptes.tabImptes.tbIpuCed -> 05 TB-IPU-CED S9(14)V99
        assertNotNull(tablas.getTbIpuCed());
        BigDecimal vBigDecimal = new BigDecimal("12345678901234.12");
        tablas.setTbIpuCed(vBigDecimal); // BigDecimal
        NumericVar vINumericValue = new NumericVar(16, 2);;
        vINumericValue.setValue("12345678901234.12");
        tablas.setTbIpuCed(vINumericValue); // INumericValue
    }
}

