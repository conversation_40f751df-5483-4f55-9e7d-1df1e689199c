package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-INDICADORES.
 *
 */
class WsIndicadoresTest {

    private WsIndicadores wsIndicadores;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsIndicadores = new WsIndicadores();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsIndicadores.setWsAgtIdr(0);
        assertEquals(0, wsIndicadores.getWsAgtIdr());

        wsIndicadores.setWsFlaNum("");
        assertEquals("", wsIndicadores.getWsFlaNum());

        wsIndicadores.setWsMesAux(0);
        assertEquals(0, wsIndicadores.getWsMesAux());

        wsIndicadores.setWsFinFcn(0);
        assertEquals(0, wsIndicadores.getWsFinFcn());

        wsIndicadores.setWsFinFdc(0);
        assertEquals(0, wsIndicadores.getWsFinFdc());

        wsIndicadores.setWsFinAgt(0);
        assertEquals(0, wsIndicadores.getWsFinAgt());

        wsIndicadores.setWsFinCna(0);
        assertEquals(0, wsIndicadores.getWsFinCna());

        wsIndicadores.setWsFinFla(0);
        assertEquals(0, wsIndicadores.getWsFinFla());

        wsIndicadores.setWsFinMte(0);
        assertEquals(0, wsIndicadores.getWsFinMte());

        wsIndicadores.setWsFinCaf(0);
        assertEquals(0, wsIndicadores.getWsFinCaf());

        wsIndicadores.setI(0);
        assertEquals(0, wsIndicadores.getI());

        wsIndicadores.setJ(0);
        assertEquals(0, wsIndicadores.getJ());

        wsIndicadores.setContador(0);
        assertEquals(0, wsIndicadores.getContador());

        wsIndicadores.setCuantos(0);
        assertEquals(0, wsIndicadores.getCuantos());

        wsIndicadores.setContLineas(0);
        assertEquals(0, wsIndicadores.getContLineas());

        wsIndicadores.setContLineas1(0);
        assertEquals(0, wsIndicadores.getContLineas1());

        wsIndicadores.setContHojas(0);
        assertEquals(0, wsIndicadores.getContHojas());

        wsIndicadores.setContHojas1(0);
        assertEquals(0, wsIndicadores.getContHojas1());

    }

    // property wsAgtIdr
    @Test
    void testGetterSetterWsAgtIdr() {

        assertNotNull(wsIndicadores.getWsAgtIdr());

        int vint = 123456789;
        wsIndicadores.setWsAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsAgtIdr()));
    }

    // property wsFlaNum
    @Test
    void testGetterSetterWsFlaNum() {

        assertNotNull(wsIndicadores.getWsFlaNum());

        String vString = "ABCabc12";
        wsIndicadores.setWsFlaNum(vString); // String
        assertEquals(asStr(vString), asStr(wsIndicadores.getWsFlaNum()));
    }

    // property wsMesAux
    @Test
    void testGetterSetterWsMesAux() {

        assertNotNull(wsIndicadores.getWsMesAux());

        int vint = 12;
        wsIndicadores.setWsMesAux(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsMesAux()));
    }

    // property wsFinFcn
    @Test
    void testGetterSetterWsFinFcn() {

        assertNotNull(wsIndicadores.getWsFinFcn());

        int vint = 1;
        wsIndicadores.setWsFinFcn(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFcn()));
    }

    // property wsFinFdc
    @Test
    void testGetterSetterWsFinFdc() {

        assertNotNull(wsIndicadores.getWsFinFdc());

        int vint = 1;
        wsIndicadores.setWsFinFdc(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFdc()));
    }

    // property wsFinAgt
    @Test
    void testGetterSetterWsFinAgt() {

        assertNotNull(wsIndicadores.getWsFinAgt());

        int vint = 1;
        wsIndicadores.setWsFinAgt(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinAgt()));
    }

    // property wsFinCna
    @Test
    void testGetterSetterWsFinCna() {

        assertNotNull(wsIndicadores.getWsFinCna());

        int vint = 1;
        wsIndicadores.setWsFinCna(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinCna()));
    }

    // property wsFinFla
    @Test
    void testGetterSetterWsFinFla() {

        assertNotNull(wsIndicadores.getWsFinFla());

        int vint = 1;
        wsIndicadores.setWsFinFla(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinFla()));
    }

    // property wsFinMte
    @Test
    void testGetterSetterWsFinMte() {

        assertNotNull(wsIndicadores.getWsFinMte());

        int vint = 1;
        wsIndicadores.setWsFinMte(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinMte()));
    }

    // property wsFinCaf
    @Test
    void testGetterSetterWsFinCaf() {

        assertNotNull(wsIndicadores.getWsFinCaf());

        int vint = 1;
        wsIndicadores.setWsFinCaf(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getWsFinCaf()));
    }

    // property i
    @Test
    void testGetterSetterI() {

        assertNotNull(wsIndicadores.getI());

        int vint = 123;
        wsIndicadores.setI(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getI()));
    }

    // property j
    @Test
    void testGetterSetterJ() {

        assertNotNull(wsIndicadores.getJ());

        int vint = 123;
        wsIndicadores.setJ(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getJ()));
    }

    // property contador
    @Test
    void testGetterSetterContador() {

        assertNotNull(wsIndicadores.getContador());

        int vint = 12345;
        wsIndicadores.setContador(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContador()));
    }

    // property cuantos
    @Test
    void testGetterSetterCuantos() {

        assertNotNull(wsIndicadores.getCuantos());

        int vint = 12345;
        wsIndicadores.setCuantos(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getCuantos()));
    }

    // property contLineas
    @Test
    void testGetterSetterContLineas() {

        assertNotNull(wsIndicadores.getContLineas());

        int vint = 123;
        wsIndicadores.setContLineas(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContLineas()));
    }

    // property contLineas1
    @Test
    void testGetterSetterContLineas1() {

        assertNotNull(wsIndicadores.getContLineas1());

        int vint = 123;
        wsIndicadores.setContLineas1(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContLineas1()));
    }

    // property contHojas
    @Test
    void testGetterSetterContHojas() {

        assertNotNull(wsIndicadores.getContHojas());

        int vint = 123;
        wsIndicadores.setContHojas(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContHojas()));
    }

    // property contHojas1
    @Test
    void testGetterSetterContHojas1() {

        assertNotNull(wsIndicadores.getContHojas1());

        int vint = 123;
        wsIndicadores.setContHojas1(vint); // int
        assertEquals(asInt(vint), asInt(wsIndicadores.getContHojas1()));
    }
}

