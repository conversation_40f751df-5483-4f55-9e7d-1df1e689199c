package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: TBD-DCLGFVTFDC0.
 *
 */
class TbdDclgfvtfdc0Test {

    private TbdDclgfvtfdc0 tbdDclgfvtfdc0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tbdDclgfvtfdc0 = new TbdDclgfvtfdc0();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {

    }

    // property tbdTabla
    @Test
    void testGetterSetterTbdTabla() {

        assertNotNull(tbdDclgfvtfdc0.getTbdTabla());
        assertNotNull(tbdDclgfvtfdc0.getTbdTabla(1));
    }

    // property tbdAgtIdr
    @Test
    void testGetterSetterTbdAgtIdr() {

        // Property: tbdTabla.tbdAgtIdr -> 10 TBD-AGT-IDR S9(9) COMPUTATIONAL
        assertNotNull(tbdDclgfvtfdc0.getTbdAgtIdr());
        int vint = 123456789;
        tbdDclgfvtfdc0.setTbdAgtIdr(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        tbdDclgfvtfdc0.setTbdAgtIdr(vINumericValue); // INumericValue
    }

    // property tbdEmpCve
    @Test
    void testGetterSetterTbdEmpCve() {

        // Property: tbdTabla.tbdEmpCve -> 10 TBD-EMP-CVE X(5)
        assertNotNull(tbdDclgfvtfdc0.getTbdEmpCve());
        String vString = "ABCab";
        tbdDclgfvtfdc0.setTbdEmpCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        tbdDclgfvtfdc0.setTbdEmpCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbdCnaNum
    @Test
    void testGetterSetterTbdCnaNum() {

        // Property: tbdTabla.tbdCnaNum -> 10 TBD-CNA-NUM S9(4) COMPUTATIONAL
        assertNotNull(tbdDclgfvtfdc0.getTbdCnaNum());
        int vint = 1234;
        tbdDclgfvtfdc0.setTbdCnaNum(vint); // int
        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        tbdDclgfvtfdc0.setTbdCnaNum(vINumericValue); // INumericValue
    }

    // property tbdRamCve
    @Test
    void testGetterSetterTbdRamCve() {

        // Property: tbdTabla.tbdRamCve -> 10 TBD-RAM-CVE X(2)
        assertNotNull(tbdDclgfvtfdc0.getTbdRamCve());
        String vString = "AB";
        tbdDclgfvtfdc0.setTbdRamCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("AB");
        tbdDclgfvtfdc0.setTbdRamCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property tbdSraCve
    @Test
    void testGetterSetterTbdSraCve() {

        // Property: tbdTabla.tbdSraCve -> 10 TBD-SRA-CVE X(4)
        assertNotNull(tbdDclgfvtfdc0.getTbdSraCve());
        String vString = "ABCa";
        tbdDclgfvtfdc0.setTbdSraCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        tbdDclgfvtfdc0.setTbdSraCve(vIAlphanumericValue); // IAlphanumericValue
    }
}

