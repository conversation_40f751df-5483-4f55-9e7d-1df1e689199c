package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: CONSTANTES.
 *
 */
class ConstantesTest {

    private Constantes constantes;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        constantes = new Constantes();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        constantes.setWsAgenteIdr(71099);
        assertEquals(71099, constantes.getWsAgenteIdr());

        constantes.setWsMinDsg(101);
        assertEquals(101, constantes.getWsMinDsg());

        constantes.setWsMaxDsg(104);
        assertEquals(104, constantes.getWsMaxDsg());

        constantes.setWsLimiteCatEsa(10);
        assertEquals(10, constantes.getWsLimiteCatEsa());

        constantes.setWsLimiteConcepto(50);
        assertEquals(50, constantes.getWsLimiteConcepto());

        constantes.setWsLimiteEstatus(100);
        assertEquals(100, constantes.getWsLimiteEstatus());

        constantes.setWsLimiteDirecc(150);
        assertEquals(150, constantes.getWsLimiteDirecc());

        constantes.setW000Prog("GGQACC00");
        assertEquals("GGQACC00", constantes.getW000Prog());

        constantes.setW000Asteriscos("********");
        assertEquals("********", constantes.getW000Asteriscos());

        constantes.setW000OpcionSelect("SELECT");
        assertEquals("SELECT", constantes.getW000OpcionSelect());

        constantes.setW000OpcionUpdate("UPDATE");
        assertEquals("UPDATE", constantes.getW000OpcionUpdate());

        constantes.setW000OpcionInsert("INSERT");
        assertEquals("INSERT", constantes.getW000OpcionInsert());

    }

    // property wsAgenteIdr
    @Test
    void testGetterSetterWsAgenteIdr() {

        assertNotNull(constantes.getWsAgenteIdr());

        int vint = 1234567;
        constantes.setWsAgenteIdr(vint); // int
        assertEquals(asInt(vint), asInt(constantes.getWsAgenteIdr()));
    }

    // property wsMinDsg
    @Test
    void testGetterSetterWsMinDsg() {

        assertNotNull(constantes.getWsMinDsg());

        int vint = 1234;
        constantes.setWsMinDsg(vint); // int
        assertEquals(asInt(vint), asInt(constantes.getWsMinDsg()));
    }

    // property wsMaxDsg
    @Test
    void testGetterSetterWsMaxDsg() {

        assertNotNull(constantes.getWsMaxDsg());

        int vint = 1234;
        constantes.setWsMaxDsg(vint); // int
        assertEquals(asInt(vint), asInt(constantes.getWsMaxDsg()));
    }

    // property wsLimiteCatEsa
    @Test
    void testGetterSetterWsLimiteCatEsa() {

        assertNotNull(constantes.getWsLimiteCatEsa());

        int vint = 1234;
        constantes.setWsLimiteCatEsa(vint); // int
        assertEquals(asInt(vint), asInt(constantes.getWsLimiteCatEsa()));
    }

    // property wsLimiteConcepto
    @Test
    void testGetterSetterWsLimiteConcepto() {

        assertNotNull(constantes.getWsLimiteConcepto());

        int vint = 1234;
        constantes.setWsLimiteConcepto(vint); // int
        assertEquals(asInt(vint), asInt(constantes.getWsLimiteConcepto()));
    }

    // property wsLimiteEstatus
    @Test
    void testGetterSetterWsLimiteEstatus() {

        assertNotNull(constantes.getWsLimiteEstatus());

        int vint = 1234;
        constantes.setWsLimiteEstatus(vint); // int
        assertEquals(asInt(vint), asInt(constantes.getWsLimiteEstatus()));
    }

    // property wsLimiteDirecc
    @Test
    void testGetterSetterWsLimiteDirecc() {

        assertNotNull(constantes.getWsLimiteDirecc());

        int vint = 1234;
        constantes.setWsLimiteDirecc(vint); // int
        assertEquals(asInt(vint), asInt(constantes.getWsLimiteDirecc()));
    }

    // property w000Prog
    @Test
    void testGetterSetterW000Prog() {

        assertNotNull(constantes.getW000Prog());

        String vString = "ABCabc12";
        constantes.setW000Prog(vString); // String
        assertEquals(asStr(vString), asStr(constantes.getW000Prog()));
    }

    // property w000Asteriscos
    @Test
    void testGetterSetterW000Asteriscos() {

        assertNotNull(constantes.getW000Asteriscos());

        String vString = "ABCabc12";
        constantes.setW000Asteriscos(vString); // String
        assertEquals(asStr(vString), asStr(constantes.getW000Asteriscos()));
    }

    // property w000OpcionSelect
    @Test
    void testGetterSetterW000OpcionSelect() {

        assertNotNull(constantes.getW000OpcionSelect());

        String vString = "ABCabc12";
        constantes.setW000OpcionSelect(vString); // String
        assertEquals(asStr(vString), asStr(constantes.getW000OpcionSelect()));
    }

    // property w000OpcionUpdate
    @Test
    void testGetterSetterW000OpcionUpdate() {

        assertNotNull(constantes.getW000OpcionUpdate());

        String vString = "ABCabc12";
        constantes.setW000OpcionUpdate(vString); // String
        assertEquals(asStr(vString), asStr(constantes.getW000OpcionUpdate()));
    }

    // property w000OpcionInsert
    @Test
    void testGetterSetterW000OpcionInsert() {

        assertNotNull(constantes.getW000OpcionInsert());

        String vString = "ABCabc12";
        constantes.setW000OpcionInsert(vString); // String
        assertEquals(asStr(vString), asStr(constantes.getW000OpcionInsert()));
    }
}

