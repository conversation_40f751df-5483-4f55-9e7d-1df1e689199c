package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: FECHA-FORMAT-I.
 *
 */
class FechaFormatITest {

    private FechaFormatI fechaFormatI;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        fechaFormatI = new FechaFormatI();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        FechaFormatI fechaFormatI2 = new FechaFormatI(parent);
        assertNotNull(fechaFormatI2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        FechaFormatI fechaFormatI3 = new FechaFormatI(parent, sibling);
        assertNotNull(fechaFormatI3);
    }

    @Test
    void testInitialize() {
        assertNotNull(fechaFormatI.getFechaFormatAa());

        assertNotNull(fechaFormatI.getFechaFormatMm());

        assertNotNull(fechaFormatI.getFechaFormatDd());

    }

    // property fechaFormatAa
    @Test
    void testGetterSetterFechaFormatAa() {

        assertNotNull(fechaFormatI.getFechaFormatAa());

        int vint = 1234;
        fechaFormatI.setFechaFormatAa(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatI.getFechaFormatAa()));

        NumericVar vINumericValue = new NumericVar(4, 0);;
        vINumericValue.setValue("1234");
        fechaFormatI.setFechaFormatAa(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(fechaFormatI.getFechaFormatAa()));
    }

    // property fechaFormatMm
    @Test
    void testGetterSetterFechaFormatMm() {

        assertNotNull(fechaFormatI.getFechaFormatMm());

        int vint = 12;
        fechaFormatI.setFechaFormatMm(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatI.getFechaFormatMm()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        fechaFormatI.setFechaFormatMm(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(fechaFormatI.getFechaFormatMm()));
    }

    // property fechaFormatDd
    @Test
    void testGetterSetterFechaFormatDd() {

        assertNotNull(fechaFormatI.getFechaFormatDd());

        int vint = 12;
        fechaFormatI.setFechaFormatDd(vint); // int
        assertEquals(asInt(vint), asInt(fechaFormatI.getFechaFormatDd()));

        NumericVar vINumericValue = new NumericVar(2, 0);;
        vINumericValue.setValue("12");
        fechaFormatI.setFechaFormatDd(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(fechaFormatI.getFechaFormatDd()));
    }
}

