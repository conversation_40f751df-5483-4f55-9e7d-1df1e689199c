package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static com.base100.caravel.support.os390.test.support.AssertionsUtil.assertExceptionInstanceOf;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.base100.caravel.support.common.cbl.exception.CobolEndExecutionException;
import com.base100.caravel.support.os390.test.support.CobolProgramMockUtil;
import com.base100.caravel.support.os390.test.support.PortAdapterMockUtil;

class Ggqac700Test {
    Ggqac700 pgm;

    @BeforeEach
    void setUp() throws Exception {
        pgm = CobolProgramMockUtil.mockProgram(Ggqac700.class);
        PortAdapterMockUtil.mockAllPortAdapters(pgm);
    }

    @AfterEach
    void tearDown() throws Exception {
    }

    @Test
    void testInitialize() {
        pgm.initialize();
        assertNotNull(pgm.getReturnCode());
    }

    @Test
    void testRun() {
        Exception exception = assertThrows(CobolEndExecutionException.class, () -> pgm.run());
        assertNotNull(exception);
    }
    /*
      p100Inicio test conditions:
        s999Err : ["1", "XX1"]
    */
    @Test
    void p100InicioTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsCamposTrabajo().setS999Err("1");
        try {
            pgm.p100Inicio();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p100InicioTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsCamposTrabajo().setS999Err("XX1");
            try {
                pgm.p100Inicio();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p120LeeParametroP01 test conditions:
        s999Err : ["1", "XX1"]
    */
    @Test
    void p120LeeParametroP01Test1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsCamposTrabajo().setS999Err("1");
        try {
            pgm.p120LeeParametroP01();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p120LeeParametroP01Test2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsCamposTrabajo().setS999Err("XX1");
            try {
                pgm.p120LeeParametroP01();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p200Proceso test conditions:
        lhfoSwErr : ["0", "XX0", "1", "XX1"]
    */
    @Test
    void p200ProcesoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLhfoSwErr("0");
        try {
            pgm.p200Proceso();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p200ProcesoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLhfoSwErr("XX0");
            try {
                pgm.p200Proceso();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p200ProcesoTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getWsLinkReg().setLhfoSwErr("1");
                try {
                    pgm.p200Proceso();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p200ProcesoTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getWsLinkReg().setLhfoSwErr("XX1");
                    try {
                        pgm.p200Proceso();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p300BuscaRegHfo test conditions:
        lhfoSwErr : ["0", "XX0", "1", "XX1"]
    */
    @Test
    void p300BuscaRegHfoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLhfoSwErr("0");
        try {
            pgm.p300BuscaRegHfo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p300BuscaRegHfoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLhfoSwErr("XX0");
            try {
                pgm.p300BuscaRegHfo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p300BuscaRegHfoTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getWsLinkReg().setLhfoSwErr("1");
                try {
                    pgm.p300BuscaRegHfo();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p300BuscaRegHfoTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getWsLinkReg().setLhfoSwErr("XX1");
                    try {
                        pgm.p300BuscaRegHfo();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
    /*
      p350InsertHfo test conditions:
        lhfoSwErr : ["1", "XX1"]
    */
    @Test
    void p350InsertHfoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLhfoSwErr("1");
        try {
            pgm.p350InsertHfo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p350InsertHfoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLhfoSwErr("XX1");
            try {
                pgm.p350InsertHfo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p370UpdateHfo test conditions:
        lhfoSwErr : ["1", "XX1"]
    */
    @Test
    void p370UpdateHfoTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getWsLinkReg().setLhfoSwErr("1");
        try {
            pgm.p370UpdateHfo();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p370UpdateHfoTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getWsLinkReg().setLhfoSwErr("XX1");
            try {
                pgm.p370UpdateHfo();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
    /*
      p999VerErrSql test conditions:
        sqlcode : [100, 101, 1, 0]
    */
    @Test
    void p999VerErrSqlTest1() {
        //TODO setup test
        // Set test conditions
        pgm.getSqlca().setSqlcode(100);
        try {
            pgm.p999VerErrSql();
        } catch (Exception e) {
            assertExceptionInstanceOf(CobolEndExecutionException.class, e);
        }
        assertNotNull(pgm.getReturnCode());
    }
        @Test
        void p999VerErrSqlTest2() {
            //TODO setup test
            // Set test conditions
            pgm.getSqlca().setSqlcode(101);
            try {
                pgm.p999VerErrSql();
            } catch (Exception e) {
                assertExceptionInstanceOf(CobolEndExecutionException.class, e);
            }
            assertNotNull(pgm.getReturnCode());
        }
            @Test
            void p999VerErrSqlTest3() {
                //TODO setup test
                // Set test conditions
                pgm.getSqlca().setSqlcode(1);
                try {
                    pgm.p999VerErrSql();
                } catch (Exception e) {
                    assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                }
                assertNotNull(pgm.getReturnCode());
            }
                @Test
                void p999VerErrSqlTest4() {
                    //TODO setup test
                    // Set test conditions
                    pgm.getSqlca().setSqlcode(0);
                    try {
                        pgm.p999VerErrSql();
                    } catch (Exception e) {
                        assertExceptionInstanceOf(CobolEndExecutionException.class, e);
                    }
                    assertNotNull(pgm.getReturnCode());
                }
}
