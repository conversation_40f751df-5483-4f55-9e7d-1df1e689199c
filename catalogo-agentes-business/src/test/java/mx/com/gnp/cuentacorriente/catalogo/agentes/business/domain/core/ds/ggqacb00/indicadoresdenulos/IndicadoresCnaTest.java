package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.indicadoresdenulos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: INDICADORES-CNA.
 *
 */
class IndicadoresCnaTest {

    private IndicadoresCna indicadoresCna;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        indicadoresCna = new IndicadoresCna();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        indicadoresCna.setIndcnatfp(0);
        assertEquals(0, indicadoresCna.getIndcnatfp());

        indicadoresCna.setIndcnamte(0);
        assertEquals(0, indicadoresCna.getIndcnamte());

        indicadoresCna.setIndcnactbnum(0);
        assertEquals(0, indicadoresCna.getIndcnactbnum());

    }

    // property indcnatfp
    @Test
    void testGetterSetterIndcnatfp() {

        assertNotNull(indicadoresCna.getIndcnatfp());

        int vint = 1234;
        indicadoresCna.setIndcnatfp(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresCna.getIndcnatfp()));
    }

    // property indcnamte
    @Test
    void testGetterSetterIndcnamte() {

        assertNotNull(indicadoresCna.getIndcnamte());

        int vint = 1234;
        indicadoresCna.setIndcnamte(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresCna.getIndcnamte()));
    }

    // property indcnactbnum
    @Test
    void testGetterSetterIndcnactbnum() {

        assertNotNull(indicadoresCna.getIndcnactbnum());

        int vint = 1234;
        indicadoresCna.setIndcnactbnum(vint); // int
        assertEquals(asInt(vint), asInt(indicadoresCna.getIndcnactbnum()));
    }
}

