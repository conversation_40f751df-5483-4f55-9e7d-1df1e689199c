package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfla1;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-FLA0-ELEM.
 *
 */
class TFla0ElemTest {

    private TFla0Elem tFla0Elem;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tFla0Elem = new TFla0Elem();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TFla0Elem tFla0Elem2 = new TFla0Elem(parent);
        assertNotNull(tFla0Elem2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 9);
        TFla0Elem tFla0Elem3 = new TFla0Elem(parent, sibling);
        assertNotNull(tFla0Elem3);
    }

    @Test
    void testInitialize() {
        assertNotNull(tFla0Elem.getSFlaFlaNum1());

        assertNotNull(tFla0Elem.getSFlaLimitad1());

    }

    // property sFlaFlaNum1
    @Test
    void testGetterSetterSFlaFlaNum1() {

        assertNotNull(tFla0Elem.getSFlaFlaNum1());

        String vString = "ABCabc12";
        tFla0Elem.setSFlaFlaNum1(vString); // String
        assertEquals(asStr(vString), asStr(tFla0Elem.getSFlaFlaNum1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        tFla0Elem.setSFlaFlaNum1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tFla0Elem.getSFlaFlaNum1()));
    }

    // property sFlaLimitad1
    @Test
    void testGetterSetterSFlaLimitad1() {

        assertNotNull(tFla0Elem.getSFlaLimitad1());

        String vString = "A";
        tFla0Elem.setSFlaLimitad1(vString); // String
        assertEquals(asStr(vString), asStr(tFla0Elem.getSFlaLimitad1()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tFla0Elem.setSFlaLimitad1(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(tFla0Elem.getSFlaLimitad1()));
    }
}

