package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: RP-LLAVE.
 *
 */
class RpLlaveTest {

    private RpLlave rpLlave;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        rpLlave = new RpLlave();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        rpLlave.setRpAgtIdr(0);
        assertEquals(0, rpLlave.getRpAgtIdr());

        rpLlave.setRpEmpCve("");
        assertEquals("", rpLlave.getRpEmpCve());

        rpLlave.setRpCnaNum(0);
        assertEquals(0, rpLlave.getRpCnaNum());

        rpLlave.setRpFecPrxAgt("");
        assertEquals("", rpLlave.getRpFecPrxAgt());

    }

    // property rpAgtIdr
    @Test
    void testGetterSetterRpAgtIdr() {

        assertNotNull(rpLlave.getRpAgtIdr());

        int vint = 123456789;
        rpLlave.setRpAgtIdr(vint); // int
        assertEquals(asInt(vint), asInt(rpLlave.getRpAgtIdr()));
    }

    // property rpEmpCve
    @Test
    void testGetterSetterRpEmpCve() {

        assertNotNull(rpLlave.getRpEmpCve());

        String vString = "ABCab";
        rpLlave.setRpEmpCve(vString); // String
        assertEquals(asStr(vString), asStr(rpLlave.getRpEmpCve()));
    }

    // property rpCnaNum
    @Test
    void testGetterSetterRpCnaNum() {

        assertNotNull(rpLlave.getRpCnaNum());

        int vint = 1234;
        rpLlave.setRpCnaNum(vint); // int
        assertEquals(asInt(vint), asInt(rpLlave.getRpCnaNum()));
    }

    // property rpFecPrxAgt
    @Test
    void testGetterSetterRpFecPrxAgt() {

        assertNotNull(rpLlave.getRpFecPrxAgt());

        String vString = "ABCabc1230";
        rpLlave.setRpFecPrxAgt(vString); // String
        assertEquals(asStr(vString), asStr(rpLlave.getRpFecPrxAgt()));
    }
}

