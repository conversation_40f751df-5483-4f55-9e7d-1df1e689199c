package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-FOL-LID.
 *
 */
class WsFolLidTest {

    private WsFolLid wsFolLid;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsFolLid = new WsFolLid();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsFolLid wsFolLid2 = new WsFolLid(parent);
        assertNotNull(wsFolLid2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 8);
        WsFolLid wsFolLid3 = new WsFolLid(parent, sibling);
        assertNotNull(wsFolLid3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsFolLid.getWsFolLid0());

        assertNotNull(wsFolLid.getWsFolLid1());

    }

    // property wsFolLid0
    @Test
    void testGetterSetterWsFolLid0() {

        assertNotNull(wsFolLid.getWsFolLid0());

        String vString = "A";
        wsFolLid.setWsFolLid0(vString); // String
        assertEquals(asStr(vString), asStr(wsFolLid.getWsFolLid0()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        wsFolLid.setWsFolLid0(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(wsFolLid.getWsFolLid0()));
    }

    // property wsFolLid1
    @Test
    void testGetterSetterWsFolLid1() {

        assertNotNull(wsFolLid.getWsFolLid1());

        int vint = 1234567;
        wsFolLid.setWsFolLid1(vint); // int
        assertEquals(asInt(vint), asInt(wsFolLid.getWsFolLid1()));

        NumericVar vINumericValue = new NumericVar(7, 0);;
        vINumericValue.setValue("1234567");
        wsFolLid.setWsFolLid1(vINumericValue); // INumericValue
        assertEquals(asStr(vINumericValue), asStr(wsFolLid.getWsFolLid1()));
    }
}

