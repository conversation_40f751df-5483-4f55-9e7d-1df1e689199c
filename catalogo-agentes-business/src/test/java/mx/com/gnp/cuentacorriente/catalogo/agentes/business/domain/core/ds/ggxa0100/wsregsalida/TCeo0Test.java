package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: T-CEO0.
 *
 */
class TCeo0Test {

    private TCeo0 tCeo0;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        tCeo0 = new TCeo0(1);
        tCeo0.initialize();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        TCeo0 tCeo02 = new TCeo0(parent, 1);
        assertNotNull(tCeo02);
        AlphanumericVar sibling = new AlphanumericVar(parent, 0);
        TCeo0 tCeo03 = new TCeo0(parent, sibling, 1);
        assertNotNull(tCeo03);
    }

    @Test
    void testInitialize() {

    }

    // property tCeo0Elem
    @Test
    void testGetterSetterTCeo0Elem() {

        assertNotNull(tCeo0.getTCeo0Elem());
    }

    // property sCeoCeoNom
    @Test
    void testGetterSetterSCeoCeoNom() {

        // Property: tCeo0Elem.sCeoCeoNom -> 20 S-CEO-CEO-NOM X(30)
        assertNotNull(tCeo0.getSCeoCeoNom());
        String vString = "ABCabc1230";
        tCeo0.setSCeoCeoNom(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        tCeo0.setSCeoCeoNom(vIAlphanumericValue); // IAlphanumericValue
    }

    // property sCeoLimitad
    @Test
    void testGetterSetterSCeoLimitad() {

        // Property: tCeo0Elem.sCeoLimitad -> 20 S-CEO-LIMITAD X(01)
        assertNotNull(tCeo0.getSCeoLimitad());
        String vString = "A";
        tCeo0.setSCeoLimitad(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        tCeo0.setSCeoLimitad(vIAlphanumericValue); // IAlphanumericValue
    }
}

