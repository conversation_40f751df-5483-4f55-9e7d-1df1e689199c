package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-GFVTAGT0-NULL.
 *
 */
class WsGfvtagt0NullTest {

    private WsGfvtagt0Null wsGfvtagt0Null;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsGfvtagt0Null = new WsGfvtagt0Null();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testInitialize() {
        wsGfvtagt0Null.setAgt01(0);
        assertEquals(0, wsGfvtagt0Null.getAgt01());

        wsGfvtagt0Null.setAgt02(0);
        assertEquals(0, wsGfvtagt0Null.getAgt02());

        wsGfvtagt0Null.setAgt03(0);
        assertEquals(0, wsGfvtagt0Null.getAgt03());

        wsGfvtagt0Null.setAgt04(0);
        assertEquals(0, wsGfvtagt0Null.getAgt04());

        wsGfvtagt0Null.setAgt05(0);
        assertEquals(0, wsGfvtagt0Null.getAgt05());

        wsGfvtagt0Null.setAgt06(0);
        assertEquals(0, wsGfvtagt0Null.getAgt06());

        wsGfvtagt0Null.setAgt07(0);
        assertEquals(0, wsGfvtagt0Null.getAgt07());

        wsGfvtagt0Null.setAgt08(0);
        assertEquals(0, wsGfvtagt0Null.getAgt08());

        wsGfvtagt0Null.setAgt09(0);
        assertEquals(0, wsGfvtagt0Null.getAgt09());

        wsGfvtagt0Null.setAgt11(0);
        assertEquals(0, wsGfvtagt0Null.getAgt11());

        wsGfvtagt0Null.setAgt12(0);
        assertEquals(0, wsGfvtagt0Null.getAgt12());

        wsGfvtagt0Null.setAgt13(0);
        assertEquals(0, wsGfvtagt0Null.getAgt13());

        wsGfvtagt0Null.setAgt14(0);
        assertEquals(0, wsGfvtagt0Null.getAgt14());

        wsGfvtagt0Null.setAgt15(0);
        assertEquals(0, wsGfvtagt0Null.getAgt15());

        wsGfvtagt0Null.setAgt16(0);
        assertEquals(0, wsGfvtagt0Null.getAgt16());

        wsGfvtagt0Null.setAgt17(0);
        assertEquals(0, wsGfvtagt0Null.getAgt17());

        wsGfvtagt0Null.setAgt18(0);
        assertEquals(0, wsGfvtagt0Null.getAgt18());

        wsGfvtagt0Null.setAgt19(0);
        assertEquals(0, wsGfvtagt0Null.getAgt19());

        wsGfvtagt0Null.setAgt20(0);
        assertEquals(0, wsGfvtagt0Null.getAgt20());

        wsGfvtagt0Null.setAgt21(0);
        assertEquals(0, wsGfvtagt0Null.getAgt21());

        wsGfvtagt0Null.setAgt22(0);
        assertEquals(0, wsGfvtagt0Null.getAgt22());

        wsGfvtagt0Null.setAgt23(0);
        assertEquals(0, wsGfvtagt0Null.getAgt23());

        wsGfvtagt0Null.setAgt24(0);
        assertEquals(0, wsGfvtagt0Null.getAgt24());

        wsGfvtagt0Null.setAgt25(0);
        assertEquals(0, wsGfvtagt0Null.getAgt25());

        wsGfvtagt0Null.setAgt26(0);
        assertEquals(0, wsGfvtagt0Null.getAgt26());

        wsGfvtagt0Null.setAgt27(0);
        assertEquals(0, wsGfvtagt0Null.getAgt27());

        wsGfvtagt0Null.setAgt28(0);
        assertEquals(0, wsGfvtagt0Null.getAgt28());

        wsGfvtagt0Null.setAgt29(0);
        assertEquals(0, wsGfvtagt0Null.getAgt29());

        wsGfvtagt0Null.setAgt30(0);
        assertEquals(0, wsGfvtagt0Null.getAgt30());

        wsGfvtagt0Null.setAgt31(0);
        assertEquals(0, wsGfvtagt0Null.getAgt31());

        wsGfvtagt0Null.setAgt32(0);
        assertEquals(0, wsGfvtagt0Null.getAgt32());

    }

    // property agt01
    @Test
    void testGetterSetterAgt01() {

        assertNotNull(wsGfvtagt0Null.getAgt01());

        int vint = 1234;
        wsGfvtagt0Null.setAgt01(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt01()));
    }

    // property agt02
    @Test
    void testGetterSetterAgt02() {

        assertNotNull(wsGfvtagt0Null.getAgt02());

        int vint = 1234;
        wsGfvtagt0Null.setAgt02(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt02()));
    }

    // property agt03
    @Test
    void testGetterSetterAgt03() {

        assertNotNull(wsGfvtagt0Null.getAgt03());

        int vint = 1234;
        wsGfvtagt0Null.setAgt03(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt03()));
    }

    // property agt04
    @Test
    void testGetterSetterAgt04() {

        assertNotNull(wsGfvtagt0Null.getAgt04());

        int vint = 1234;
        wsGfvtagt0Null.setAgt04(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt04()));
    }

    // property agt05
    @Test
    void testGetterSetterAgt05() {

        assertNotNull(wsGfvtagt0Null.getAgt05());

        int vint = 1234;
        wsGfvtagt0Null.setAgt05(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt05()));
    }

    // property agt06
    @Test
    void testGetterSetterAgt06() {

        assertNotNull(wsGfvtagt0Null.getAgt06());

        int vint = 1234;
        wsGfvtagt0Null.setAgt06(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt06()));
    }

    // property agt07
    @Test
    void testGetterSetterAgt07() {

        assertNotNull(wsGfvtagt0Null.getAgt07());

        int vint = 1234;
        wsGfvtagt0Null.setAgt07(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt07()));
    }

    // property agt08
    @Test
    void testGetterSetterAgt08() {

        assertNotNull(wsGfvtagt0Null.getAgt08());

        int vint = 1234;
        wsGfvtagt0Null.setAgt08(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt08()));
    }

    // property agt09
    @Test
    void testGetterSetterAgt09() {

        assertNotNull(wsGfvtagt0Null.getAgt09());

        int vint = 1234;
        wsGfvtagt0Null.setAgt09(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt09()));
    }

    // property agt11
    @Test
    void testGetterSetterAgt11() {

        assertNotNull(wsGfvtagt0Null.getAgt11());

        int vint = 1234;
        wsGfvtagt0Null.setAgt11(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt11()));
    }

    // property agt12
    @Test
    void testGetterSetterAgt12() {

        assertNotNull(wsGfvtagt0Null.getAgt12());

        int vint = 1234;
        wsGfvtagt0Null.setAgt12(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt12()));
    }

    // property agt13
    @Test
    void testGetterSetterAgt13() {

        assertNotNull(wsGfvtagt0Null.getAgt13());

        int vint = 1234;
        wsGfvtagt0Null.setAgt13(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt13()));
    }

    // property agt14
    @Test
    void testGetterSetterAgt14() {

        assertNotNull(wsGfvtagt0Null.getAgt14());

        int vint = 1234;
        wsGfvtagt0Null.setAgt14(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt14()));
    }

    // property agt15
    @Test
    void testGetterSetterAgt15() {

        assertNotNull(wsGfvtagt0Null.getAgt15());

        int vint = 1234;
        wsGfvtagt0Null.setAgt15(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt15()));
    }

    // property agt16
    @Test
    void testGetterSetterAgt16() {

        assertNotNull(wsGfvtagt0Null.getAgt16());

        int vint = 1234;
        wsGfvtagt0Null.setAgt16(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt16()));
    }

    // property agt17
    @Test
    void testGetterSetterAgt17() {

        assertNotNull(wsGfvtagt0Null.getAgt17());

        int vint = 1234;
        wsGfvtagt0Null.setAgt17(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt17()));
    }

    // property agt18
    @Test
    void testGetterSetterAgt18() {

        assertNotNull(wsGfvtagt0Null.getAgt18());

        int vint = 1234;
        wsGfvtagt0Null.setAgt18(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt18()));
    }

    // property agt19
    @Test
    void testGetterSetterAgt19() {

        assertNotNull(wsGfvtagt0Null.getAgt19());

        int vint = 1234;
        wsGfvtagt0Null.setAgt19(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt19()));
    }

    // property agt20
    @Test
    void testGetterSetterAgt20() {

        assertNotNull(wsGfvtagt0Null.getAgt20());

        int vint = 1234;
        wsGfvtagt0Null.setAgt20(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt20()));
    }

    // property agt21
    @Test
    void testGetterSetterAgt21() {

        assertNotNull(wsGfvtagt0Null.getAgt21());

        int vint = 1234;
        wsGfvtagt0Null.setAgt21(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt21()));
    }

    // property agt22
    @Test
    void testGetterSetterAgt22() {

        assertNotNull(wsGfvtagt0Null.getAgt22());

        int vint = 1234;
        wsGfvtagt0Null.setAgt22(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt22()));
    }

    // property agt23
    @Test
    void testGetterSetterAgt23() {

        assertNotNull(wsGfvtagt0Null.getAgt23());

        int vint = 1234;
        wsGfvtagt0Null.setAgt23(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt23()));
    }

    // property agt24
    @Test
    void testGetterSetterAgt24() {

        assertNotNull(wsGfvtagt0Null.getAgt24());

        int vint = 1234;
        wsGfvtagt0Null.setAgt24(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt24()));
    }

    // property agt25
    @Test
    void testGetterSetterAgt25() {

        assertNotNull(wsGfvtagt0Null.getAgt25());

        int vint = 1234;
        wsGfvtagt0Null.setAgt25(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt25()));
    }

    // property agt26
    @Test
    void testGetterSetterAgt26() {

        assertNotNull(wsGfvtagt0Null.getAgt26());

        int vint = 1234;
        wsGfvtagt0Null.setAgt26(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt26()));
    }

    // property agt27
    @Test
    void testGetterSetterAgt27() {

        assertNotNull(wsGfvtagt0Null.getAgt27());

        int vint = 1234;
        wsGfvtagt0Null.setAgt27(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt27()));
    }

    // property agt28
    @Test
    void testGetterSetterAgt28() {

        assertNotNull(wsGfvtagt0Null.getAgt28());

        int vint = 1234;
        wsGfvtagt0Null.setAgt28(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt28()));
    }

    // property agt29
    @Test
    void testGetterSetterAgt29() {

        assertNotNull(wsGfvtagt0Null.getAgt29());

        int vint = 1234;
        wsGfvtagt0Null.setAgt29(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt29()));
    }

    // property agt30
    @Test
    void testGetterSetterAgt30() {

        assertNotNull(wsGfvtagt0Null.getAgt30());

        int vint = 1234;
        wsGfvtagt0Null.setAgt30(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt30()));
    }

    // property agt31
    @Test
    void testGetterSetterAgt31() {

        assertNotNull(wsGfvtagt0Null.getAgt31());

        int vint = 1234;
        wsGfvtagt0Null.setAgt31(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt31()));
    }

    // property agt32
    @Test
    void testGetterSetterAgt32() {

        assertNotNull(wsGfvtagt0Null.getAgt32());

        int vint = 1234;
        wsGfvtagt0Null.setAgt32(vint); // int
        assertEquals(asInt(vint), asInt(wsGfvtagt0Null.getAgt32()));
    }
}

