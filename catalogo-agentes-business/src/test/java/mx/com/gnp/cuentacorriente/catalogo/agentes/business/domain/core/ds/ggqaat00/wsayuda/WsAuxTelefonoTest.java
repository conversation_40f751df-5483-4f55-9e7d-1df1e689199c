package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: WS-AUX-TELEFONO.
 *
 */
class WsAuxTelefonoTest {

    private WsAuxTelefono wsAuxTelefono;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        wsAuxTelefono = new WsAuxTelefono();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        WsAuxTelefono wsAuxTelefono2 = new WsAuxTelefono(parent);
        assertNotNull(wsAuxTelefono2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 60);
        WsAuxTelefono wsAuxTelefono3 = new WsAuxTelefono(parent, sibling);
        assertNotNull(wsAuxTelefono3);
    }

    @Test
    void testInitialize() {
        assertNotNull(wsAuxTelefono.getWsAuxTelX());

    }

    // property wsAuxTelX
    @Test
    void testGetterSetterWsAuxTelX() {

        assertNotNull(wsAuxTelefono.getWsAuxTelX());
        assertNotNull(wsAuxTelefono.getWsAuxTelX(1));

        String vString = "A";
        wsAuxTelefono.getWsAuxTelX().at(1).setValue(vString); // String
        assertEquals(asStr(vString), asStr(wsAuxTelefono.getWsAuxTelX(1)));

        AlphanumericVar vAlphanumericVar = new AlphanumericVar("A");
        wsAuxTelefono.getWsAuxTelX().at(1).setValue(vAlphanumericVar); // AlphanumericVar
        assertEquals(asStr(vAlphanumericVar), asStr(wsAuxTelefono.getWsAuxTelX(1)));
    }
}

