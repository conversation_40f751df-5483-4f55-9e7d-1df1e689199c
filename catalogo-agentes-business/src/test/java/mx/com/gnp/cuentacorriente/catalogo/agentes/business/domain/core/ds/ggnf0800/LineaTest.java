package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.var.*;
import static com.base100.caravel.support.common.test.TestUtils.*;
/**
 *  Data Structure: LINEA.
 *
 */
class LineaTest {

    private Linea linea;

    @BeforeEach
    void setUp() throws Exception {
        DataContainer.setDecimalPointIsComma(true);
        linea = new Linea();
    };


    @AfterEach
    void tearDown() throws Exception {
        DataContainer.setDecimalPointIsComma(false);
    }

    @Test
    void testConstructors() {
        DataContainer parent = new DataContainer();
        Linea linea2 = new Linea(parent);
        assertNotNull(linea2);
        AlphanumericVar sibling = new AlphanumericVar(parent, 133);
        Linea linea3 = new Linea(parent, sibling);
        assertNotNull(linea3);
    }

    @Test
    void testInitialize() {
        assertNotNull(linea.getLinSalto());

        assertNotNull(linea.getLinResto());



    }

    // property linSalto
    @Test
    void testGetterSetterLinSalto() {

        assertNotNull(linea.getLinSalto());

        String vString = "A";
        linea.setLinSalto(vString); // String
        assertEquals(asStr(vString), asStr(linea.getLinSalto()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("A");
        linea.setLinSalto(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(linea.getLinSalto()));
    }

    // property linResto
    @Test
    void testGetterSetterLinResto() {

        assertNotNull(linea.getLinResto());

        String vString = "ABCabc1230";
        linea.setLinResto(vString); // String
        assertEquals(asStr(vString), asStr(linea.getLinResto()));

        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setLinResto(vIAlphanumericValue); // IAlphanumericValue
        assertEquals(asStr(vIAlphanumericValue), asStr(linea.getLinResto()));
    }

    // property det1
    @Test
    void testGetterSetterDet1() {

        assertNotNull(linea.getDet1());
    }

    // property det3
    @Test
    void testGetterSetterDet3() {

        assertNotNull(linea.getDet3());
    }

    // property d1AgtIdr
    @Test
    void testGetterSetterD1AgtIdr() {

        // Property: det1.d1AgtIdr -> 05 D1-AGT-IDR 9(9)
        assertNotNull(linea.getD1AgtIdr());
        int vint = 123456789;
        linea.setD1AgtIdr(vint); // int
        NumericVar vINumericValue = new NumericVar(9, 0);;
        vINumericValue.setValue("123456789");
        linea.setD1AgtIdr(vINumericValue); // INumericValue
    }

    // property d1CnaNum
    @Test
    void testGetterSetterD1CnaNum() {

        // Property: det1.d1CnaNum -> 05 D1-CNA-NUM ZZZ9
        assertNotNull(linea.getD1CnaNum());
        String vString = "ABCa";
        linea.setD1CnaNum(vString); // String
        NumericVar vIValue = new NumericVar(4, 0);;
        vIValue.setValue("1234");
        linea.setD1CnaNum(vIValue); // IValue
    }

    // property d1FlaNum
    @Test
    void testGetterSetterD1FlaNum() {

        // Property: det1.d1FlaNum -> 05 D1-FLA-NUM X(8)
        assertNotNull(linea.getD1FlaNum());
        String vString = "ABCabc12";
        linea.setD1FlaNum(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc12");
        linea.setD1FlaNum(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1RamCve
    @Test
    void testGetterSetterD1RamCve() {

        // Property: det1.d1RamCve -> 05 D1-RAM-CVE X(5)
        assertNotNull(linea.getD1RamCve());
        String vString = "ABCab";
        linea.setD1RamCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCab");
        linea.setD1RamCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1SraCve
    @Test
    void testGetterSetterD1SraCve() {

        // Property: det1.d1SraCve -> 05 D1-SRA-CVE X(4)
        assertNotNull(linea.getD1SraCve());
        String vString = "ABCa";
        linea.setD1SraCve(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCa");
        linea.setD1SraCve(vIAlphanumericValue); // IAlphanumericValue
    }

    // property d1Obs
    @Test
    void testGetterSetterD1Obs() {

        // Property: det1.d1Obs -> 05 D1-OBS X(20)
        assertNotNull(linea.getD1Obs());
        String vString = "ABCabc1230";
        linea.setD1Obs(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setD1Obs(vIAlphanumericValue); // IAlphanumericValue
    }

    // property det12Letrero
    @Test
    void testGetterSetterDet12Letrero() {

        // Property: det3.det12Letrero -> 10 DET12-LETRERO X(25)
        assertNotNull(linea.getDet12Letrero());
        String vString = "ABCabc1230";
        linea.setDet12Letrero(vString); // String
        IAlphanumericValue vIAlphanumericValue = new AlphanumericVar("ABCabc1230");
        linea.setDet12Letrero(vIAlphanumericValue); // IAlphanumericValue
    }

    // property det12Total
    @Test
    void testGetterSetterDet12Total() {

        // Property: det3.det12Total -> 10 DET12-TOTAL ZZZZZZ9
        assertNotNull(linea.getDet12Total());
        String vString = "ABCabc1";
        linea.setDet12Total(vString); // String
        NumericVar vIValue = new NumericVar(7, 0);;
        vIValue.setValue("1234567");
        linea.setDet12Total(vIValue); // IValue
    }
}

