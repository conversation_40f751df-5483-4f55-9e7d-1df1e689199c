package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-PJE-ISR-R.
 *
 */
@Setter
@Getter
public class WsPjeIsrR extends DataStruct {
    // Properties
    private AlphanumericVar rsPjeIsr3 = new AlphanumericVar(this, 3);        // 10 RS-PJE-ISR3 X(03)
    private AlphanumericVar rsPjeIsr2 = new AlphanumericVar(this, 2);        // 10 RS-PJE-ISR2 X(02)

    public WsPjeIsrR() {
        super();
        initialize();
    }

    public WsPjeIsrR(DataContainer parent) {
        super(parent);
    }

    public WsPjeIsrR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRsPjeIsr3(final String rsPjeIsr3) {
        this.rsPjeIsr3.setValue(rsPjeIsr3);
    }

    public void setRsPjeIsr3(final IAlphanumericValue rsPjeIsr3) {
        this.rsPjeIsr3.setValue(rsPjeIsr3);
    }

    public void setRsPjeIsr2(final String rsPjeIsr2) {
        this.rsPjeIsr2.setValue(rsPjeIsr2);
    }

    public void setRsPjeIsr2(final IAlphanumericValue rsPjeIsr2) {
        this.rsPjeIsr2.setValue(rsPjeIsr2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rsPjeIsr3.clear();
        this.rsPjeIsr2.clear();
    }

}

