package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGFVTCNA0.
 *
 */
// 693      *---------------------------------------------------------------* 
// 694      *  DECLARACION DE VARIABLES HOST                                * 
// 695      *---------------------------------------------------------------* 
// 696      * COBOL DECLARATION FOR TABLES                                   *
// 697      ******************************************************************
@Setter
@Getter
public class WsDclgfvtcna0 { // VStruct
    // Properties
    private int cnaMteCve;                                                  // 10 CNA-MTE-CVE S9(4) COMPUTATIONAL
    private int cnaTfpCve;                                                  // 10 CNA-TFP-CVE S9(4) COMPUTATIONAL
    private int cnaOfnCve;                                                  // 10 CNA-OFN-CVE S9(4) COMPUTATIONAL
    private int cnaGerZonEnv;                                               // 10 CNA-GER-ZON-ENV S9(4) COMPUTATIONAL

    public WsDclgfvtcna0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.cnaMteCve = 0;
        this.cnaTfpCve = 0;
        this.cnaOfnCve = 0;
        this.cnaGerZonEnv = 0;
    }

}

