package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: CONSTANTES.
 *
 */
// 159      *--------------------------------------------------------------*  
// 160      * CONSTANTES                                                   *  
// 161      *--------------------------------------------------------------*  
@Setter
@Getter
public class Constantes { // VStruct
    // Properties
    private int wsAgenteIdr = 71099;                                        // 03 WS-AGENTE-IDR 9(07)
    private int wsMinDsg = 101;                                             // 03 WS-MIN-DSG S9(04) COMPUTATIONAL
    private int wsMaxDsg = 104;                                             // 03 WS-MAX-DSG S9(04) COMPUTATIONAL
    private int wsLimiteCatEsa = 10;                                        // 03 WS-LIMITE-CAT-ESA 9(04)
    private int wsLimiteConcepto = 50;                                      // 03 WS-LIMITE-CONCEPTO 9(04)
    private int wsLimiteEstatus = 100;                                      // 03 WS-LIMITE-ESTATUS 9(04)
    private int wsLimiteDirecc = 150;                                       // 03 WS-LIMITE-DIRECC 9(04)
    private String w000Prog = "GGQACC00";                                   // 03 W000-PROG X(08)
    private String w000Asteriscos = "********";                             // 03 W000-ASTERISCOS X(08)
    private String w000OpcionSelect = "SELECT";                             // 03 W000-OPCION-SELECT X(08)
    private String w000OpcionUpdate = "UPDATE";                             // 03 W000-OPCION-UPDATE X(08)
    private String w000OpcionInsert = "INSERT";                             // 03 W000-OPCION-INSERT X(08)

    public Constantes() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsAgenteIdr = 71099;
        this.wsMinDsg = 101;
        this.wsMaxDsg = 104;
        this.wsLimiteCatEsa = 10;
        this.wsLimiteConcepto = 50;
        this.wsLimiteEstatus = 100;
        this.wsLimiteDirecc = 150;
        this.w000Prog = "GGQACC00";
        this.w000Asteriscos = "********";
        this.w000OpcionSelect = "SELECT";
        this.w000OpcionUpdate = "UPDATE";
        this.w000OpcionInsert = "INSERT";
    }

}

