package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.tpar;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-PAR-REG.
 *
 */
@Setter
@Getter
public class TParReg extends DataStructArray<TParReg> {
    // Properties
    private AlphanumericVar tParTxt = new AlphanumericVar(this, 8);          // 10 T-PAR-TXT X(08)

    public TParReg(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TParReg(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TParReg(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TParReg(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TParReg at(int idx) {
        return new TParReg(this, idx, getSizeElement());
    }

    public void setTParTxt(final String tParTxt) {
        this.tParTxt.setValue(tParTxt);
    }

    public void setTParTxt(final IAlphanumericValue tParTxt) {
        this.tParTxt.setValue(tParTxt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tParTxt.clear();
    }

}

