package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GTE-R.
 *
 */
@Setter
@Getter
public class WsGteR extends DataStruct {
    // Properties
    private AlphanumericVar wsGteX = new AlphanumericVar(this, 3);           // 10 WS-GTE-X X(03)
    private UnsignedNumericVar wsGteN = new UnsignedNumericVar(this, 7, 0);  // 10 WS-GTE-N 9(07)

    public WsGteR() {
        super();
        initialize();
    }

    public WsGteR(DataContainer parent) {
        super(parent);
    }

    public WsGteR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsGteX(final String wsGteX) {
        this.wsGteX.setValue(wsGteX);
    }

    public void setWsGteX(final IAlphanumericValue wsGteX) {
        this.wsGteX.setValue(wsGteX);
    }

    public void setWsGteN(final int wsGteN) {
        this.wsGteN.setValue(wsGteN);
    }

    public void setWsGteN(final BigDecimal wsGteN) {
        this.wsGteN.setValue(wsGteN);
    }

    public void setWsGteN(final INumericValue wsGteN) {
        this.wsGteN.setValue(wsGteN);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsGteX.clear();
        this.wsGteN.clear();
    }

}

