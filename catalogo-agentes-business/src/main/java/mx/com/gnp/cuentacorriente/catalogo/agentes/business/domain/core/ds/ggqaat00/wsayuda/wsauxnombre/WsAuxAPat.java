package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-A-PAT.
 *
 */
@Setter
@Getter
public class WsAuxAPat extends DataStruct {
    // Properties
    private AlphanumericVarArray wsAuxApPat = new AlphanumericVarArray(this, 40, 1);         // 15 WS-AUX-AP-PAT X(01) [40]

    public WsAuxAPat() {
        super();
        initialize();
    }

    public WsAuxAPat(DataContainer parent) {
        super(parent);
    }

    public WsAuxAPat(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWsAuxApPat(int index) {
        return this.wsAuxApPat.at(index);
    }

    public void setWsAuxApPat(final String wsAuxApPat) {
        this.wsAuxApPat.setValue(wsAuxApPat);
    }

    public void setWsAuxApPat(final AlphanumericVarArray wsAuxApPat) {
        this.wsAuxApPat.setValue(wsAuxApPat);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAuxApPat.clear();
    }

}

