package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaselementos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WW-TAB-DATO.
 *
 */
@Setter
@Getter
public class WwTabDato extends DataStruct {
    // Properties
    private AlphanumericVarArray wwCampo = new AlphanumericVarArray(this, 3, 1);             // 15 WW-CAMPO X(01) [3]

    public WwTabDato() {
        super();
        initialize();
    }

    public WwTabDato(DataContainer parent) {
        super(parent);
    }

    public WwTabDato(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWwCampo(int index) {
        return this.wwCampo.at(index);
    }

    public void setWwCampo(final String wwCampo) {
        this.wwCampo.setValue(wwCampo);
    }

    public void setWwCampo(final AlphanumericVarArray wwCampo) {
        this.wwCampo.setValue(wwCampo);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wwCampo.clear();
    }

}

