package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CPO-CVE-R.
 *
 */
@Setter
@Getter
public class WsCpoCveR extends DataStruct {
    // Properties
    private AlphanumericVar wsCpoCve_4 = new AlphanumericVar(this, 4);       // 10 WS-CPO-CVE-4 X(04)
    private AlphanumericVar wsCpoCve_5 = new AlphanumericVar(this, 5);       // 10 WS-CPO-CVE-5 X(05)

    public WsCpoCveR() {
        super();
        initialize();
    }

    public WsCpoCveR(DataContainer parent) {
        super(parent);
    }

    public WsCpoCveR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsCpoCve_4(final String wsCpoCve_4) {
        this.wsCpoCve_4.setValue(wsCpoCve_4);
    }

    public void setWsCpoCve_4(final IAlphanumericValue wsCpoCve_4) {
        this.wsCpoCve_4.setValue(wsCpoCve_4);
    }

    public void setWsCpoCve_5(final String wsCpoCve_5) {
        this.wsCpoCve_5.setValue(wsCpoCve_5);
    }

    public void setWsCpoCve_5(final IAlphanumericValue wsCpoCve_5) {
        this.wsCpoCve_5.setValue(wsCpoCve_5);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsCpoCve_4.clear();
        this.wsCpoCve_5.clear();
    }

}

