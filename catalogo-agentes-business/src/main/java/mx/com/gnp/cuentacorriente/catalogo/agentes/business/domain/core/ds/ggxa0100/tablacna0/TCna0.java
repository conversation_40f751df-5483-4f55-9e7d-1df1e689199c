package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablacna0;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablacna0.tcna0.TCna0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-CNA0.
 *
 */
// 216           10 T-CNA0      OCCURS 1 TO 85                                
@Setter
@Getter
public class TCna0 extends DataStructArray<TCna0> {
    // Properties
    private TCna0Elem tCna0Elem = new  TCna0Elem(this);                      // 15 T-CNA0-ELEM UsedAsParameter

    public TCna0(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TCna0(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TCna0(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TCna0(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TCna0 at(int idx) {
        return new TCna0(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tCna0Elem.sCnaCnaNum -> 20 S-CNA-CNA-NUM X(02)
    public AlphanumericVar getSCnaCnaNum() {
        return this.tCna0Elem.getSCnaCnaNum();
    }

    public void setSCnaCnaNum(final String sCnaCnaNum) {
        this.tCna0Elem.setSCnaCnaNum(sCnaCnaNum);
    }

    public void setSCnaCnaNum(final IAlphanumericValue sCnaCnaNum) {
        this.tCna0Elem.setSCnaCnaNum(sCnaCnaNum);
    }

    // Property: tCna0Elem.sCnaLimitad -> 20 S-CNA-LIMITAD X(01)
    public AlphanumericVar getSCnaLimitad() {
        return this.tCna0Elem.getSCnaLimitad();
    }

    public void setSCnaLimitad(final String sCnaLimitad) {
        this.tCna0Elem.setSCnaLimitad(sCnaLimitad);
    }

    public void setSCnaLimitad(final IAlphanumericValue sCnaLimitad) {
        this.tCna0Elem.setSCnaLimitad(sCnaLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tCna0Elem.initialize();
    }

}

