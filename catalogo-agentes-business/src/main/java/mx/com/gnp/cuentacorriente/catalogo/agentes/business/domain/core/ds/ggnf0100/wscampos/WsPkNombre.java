package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-PK-NOMBRE.
 *
 */
// 1255078300*                                                                 07830000
// 1256078400*                                                                 07840000
@Setter
@Getter
public class WsPkNombre { // VStruct
    // Properties
    private String wsPkApePat = "";                                         // 05 WS-PK-APE-PAT X(15)
    private String wsPkApeMat = "";                                         // 05 WS-PK-APE-MAT X(10)
    private String wsPkNom = "";                                            // 05 WS-PK-NOM X(10)

    public WsPkNombre() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsPkApePat = "";
        this.wsPkApeMat = "";
        this.wsPkNom = "";
    }

}

