package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-PK-CMA-DESR.
 *
 */
// 1230075800*                                                                 07580000
@Setter
@Getter
public class WsPkCmaDesr extends DataStruct {
    // Properties
    private AlphanumericVar wsPkTelr = new AlphanumericVar(this, 12);        // 05 WS-PK-TELR X(12) REDEFINED BY WS-PK-TEL
    private UnsignedNumericVar wsPkTel = new UnsignedNumericVar(this, this.wsPkTelr, 12, 0);                 // 05 WS-PK-TEL REDEFINES WS-PK-TELR 9(12)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 48);         // 05 FILLER X(48)

    public WsPkCmaDesr() {
        super();
        initialize();
    }

    public WsPkCmaDesr(DataContainer parent) {
        super(parent);
    }

    public WsPkCmaDesr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsPkTelr(final String wsPkTelr) {
        this.wsPkTelr.setValue(wsPkTelr);
    }

    public void setWsPkTelr(final IAlphanumericValue wsPkTelr) {
        this.wsPkTelr.setValue(wsPkTelr);
    }

    public void setWsPkTel(final BigDecimal wsPkTel) {
        this.wsPkTel.setValue(wsPkTel);
    }

    public void setWsPkTel(final int wsPkTel) {
        this.wsPkTel.setValue(wsPkTel);
    }

    public void setWsPkTel(final INumericValue wsPkTel) {
        this.wsPkTel.setValue(wsPkTel);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsPkTelr.clear();
        this.filler1.clear();
    }

}

