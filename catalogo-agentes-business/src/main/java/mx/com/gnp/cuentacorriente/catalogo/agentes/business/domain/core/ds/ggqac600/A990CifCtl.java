package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: A990-CIF-CTL.
 *
 */
// 724      *----                                                             
// 725      *    CIFRAS CONTROL                                               
// 726      *----                                                             
@Setter
@Getter
public class A990CifCtl { // VStruct
    // Properties
    private int a990RegLeiAgtA1;                                            // 05 A990-REG-LEI-AGT-A1 S9(09) COMPUTATIONAL
    private int a990RegLeiInsHfa;                                           // 05 A990-REG-LEI-INS-HFA S9(09) COMPUTATIONAL
    private int a990RegLeiUpdHfa;                                           // 05 A990-REG-LEI-UPD-HFA S9(09) COMPUTATIONAL
    private int a990RegLeiAgtExc;                                           // 05 A990-REG-LEI-AGT-EXC S9(09) COMPUTATIONAL

    public A990CifCtl() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.a990RegLeiAgtA1 = 0;
        this.a990RegLeiInsHfa = 0;
        this.a990RegLeiUpdHfa = 0;
        this.a990RegLeiAgtExc = 0;
    }

}

