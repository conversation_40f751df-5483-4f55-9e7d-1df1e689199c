package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.wsfechas;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FECHA-MOV-R.
 *
 */
@Setter
@Getter
public class WsFechaMovR extends DataStruct {
    // Properties
    private UnsignedNumericVar wsFecMovAa = new UnsignedNumericVar(this, 4, 0);              // 05 WS-FEC-MOV-AA 9(04)
    private AlphanumericVar wsFecMovGuion_1 = new AlphanumericVar(this, 1);  // 05 WS-FEC-MOV-GUION-1 X(01)
    private UnsignedNumericVar wsFecMovMm = new UnsignedNumericVar(this, 2, 0);              // 05 WS-FEC-MOV-MM 9(02)
    private AlphanumericVar wsFecMovGuion_2 = new AlphanumericVar(this, 1);  // 05 WS-FEC-MOV-GUION-2 X(01)
    private UnsignedNumericVar wsFecMovDd = new UnsignedNumericVar(this, 2, 0);              // 05 WS-FEC-MOV-DD 9(02)

    public WsFechaMovR() {
        super();
        initialize();
    }

    public WsFechaMovR(DataContainer parent) {
        super(parent);
    }

    public WsFechaMovR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFecMovAa(final int wsFecMovAa) {
        this.wsFecMovAa.setValue(wsFecMovAa);
    }

    public void setWsFecMovAa(final BigDecimal wsFecMovAa) {
        this.wsFecMovAa.setValue(wsFecMovAa);
    }

    public void setWsFecMovAa(final INumericValue wsFecMovAa) {
        this.wsFecMovAa.setValue(wsFecMovAa);
    }

    public void setWsFecMovGuion_1(final String wsFecMovGuion_1) {
        this.wsFecMovGuion_1.setValue(wsFecMovGuion_1);
    }

    public void setWsFecMovGuion_1(final IAlphanumericValue wsFecMovGuion_1) {
        this.wsFecMovGuion_1.setValue(wsFecMovGuion_1);
    }

    public void setWsFecMovMm(final int wsFecMovMm) {
        this.wsFecMovMm.setValue(wsFecMovMm);
    }

    public void setWsFecMovMm(final BigDecimal wsFecMovMm) {
        this.wsFecMovMm.setValue(wsFecMovMm);
    }

    public void setWsFecMovMm(final INumericValue wsFecMovMm) {
        this.wsFecMovMm.setValue(wsFecMovMm);
    }

    public void setWsFecMovGuion_2(final String wsFecMovGuion_2) {
        this.wsFecMovGuion_2.setValue(wsFecMovGuion_2);
    }

    public void setWsFecMovGuion_2(final IAlphanumericValue wsFecMovGuion_2) {
        this.wsFecMovGuion_2.setValue(wsFecMovGuion_2);
    }

    public void setWsFecMovDd(final int wsFecMovDd) {
        this.wsFecMovDd.setValue(wsFecMovDd);
    }

    public void setWsFecMovDd(final BigDecimal wsFecMovDd) {
        this.wsFecMovDd.setValue(wsFecMovDd);
    }

    public void setWsFecMovDd(final INumericValue wsFecMovDd) {
        this.wsFecMovDd.setValue(wsFecMovDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFecMovAa.clear();
        this.wsFecMovGuion_1.clear();
        this.wsFecMovMm.clear();
        this.wsFecMovGuion_2.clear();
        this.wsFecMovDd.clear();
    }

}

