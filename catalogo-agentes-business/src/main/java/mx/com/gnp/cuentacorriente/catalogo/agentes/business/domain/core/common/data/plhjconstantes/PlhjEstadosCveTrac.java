package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsconstantes.plhjconstantes;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PLHJ-ESTADOS-CVE-TRAC.
 *
 */
@Setter
@Getter
public class PlhjEstadosCveTrac extends DataStruct {
    // Properties
    private AlphanumericVar plhjAgst = new AlphanumericVar(this, 3);         // 15 PLHJ-AGST X(03)
    private AlphanumericVar plhjBcnt = new AlphanumericVar(this, 3);         // 15 PLHJ-BCNT X(03)
    private AlphanumericVar plhjBcst = new AlphanumericVar(this, 3);         // 15 PLHJ-BCST X(03)
    private AlphanumericVar plhjCampechet = new AlphanumericVar(this, 3);    // 15 PLHJ-CAMPECHET X(03)
    private AlphanumericVar plhjCoahuilat = new AlphanumericVar(this, 3);    // 15 PLHJ-COAHUILAT X(03)
    private AlphanumericVar plhjColimat = new AlphanumericVar(this, 3);      // 15 PLHJ-COLIMAT X(03)
    private AlphanumericVar plhjChiapast = new AlphanumericVar(this, 3);     // 15 PLHJ-CHIAPAST X(03)
    private AlphanumericVar plhjChihuahuat = new AlphanumericVar(this, 3);   // 15 PLHJ-CHIHUAHUAT X(03)
    private AlphanumericVar plhjDft = new AlphanumericVar(this, 3);          // 15 PLHJ-DFT X(03)
    private AlphanumericVar plhjDurangot = new AlphanumericVar(this, 3);     // 15 PLHJ-DURANGOT X(03)
    private AlphanumericVar plhjGtot = new AlphanumericVar(this, 3);         // 15 PLHJ-GTOT X(03)
    private AlphanumericVar plhjGuerrerot = new AlphanumericVar(this, 3);    // 15 PLHJ-GUERREROT X(03)
    private AlphanumericVar plhjHidalgot = new AlphanumericVar(this, 3);     // 15 PLHJ-HIDALGOT X(03)
    private AlphanumericVar plhjJaliscot = new AlphanumericVar(this, 3);     // 15 PLHJ-JALISCOT X(03)
    private AlphanumericVar plhjEdomext = new AlphanumericVar(this, 3);      // 15 PLHJ-EDOMEXT X(03)
    private AlphanumericVar plhjMichoacant = new AlphanumericVar(this, 3);   // 15 PLHJ-MICHOACANT X(03)
    private AlphanumericVar plhjMorelost = new AlphanumericVar(this, 3);     // 15 PLHJ-MORELOST X(03)
    private AlphanumericVar plhjNayaritt = new AlphanumericVar(this, 3);     // 15 PLHJ-NAYARITT X(03)
    private AlphanumericVar plhjNuevoleont = new AlphanumericVar(this, 3);   // 15 PLHJ-NUEVOLEONT X(03)
    private AlphanumericVar plhjOaxacat = new AlphanumericVar(this, 3);      // 15 PLHJ-OAXACAT X(03)
    private AlphanumericVar plhjPueblat = new AlphanumericVar(this, 3);      // 15 PLHJ-PUEBLAT X(03)
    private AlphanumericVar plhjQueretarot = new AlphanumericVar(this, 3);   // 15 PLHJ-QUERETAROT X(03)
    private AlphanumericVar plhjQroot = new AlphanumericVar(this, 3);        // 15 PLHJ-QROOT X(03)
    private AlphanumericVar plhjSlpt = new AlphanumericVar(this, 3);         // 15 PLHJ-SLPT X(03)
    private AlphanumericVar plhjSinaloat = new AlphanumericVar(this, 3);     // 15 PLHJ-SINALOAT X(03)
    private AlphanumericVar plhjSonorat = new AlphanumericVar(this, 3);      // 15 PLHJ-SONORAT X(03)
    private AlphanumericVar plhjTabascot = new AlphanumericVar(this, 3);     // 15 PLHJ-TABASCOT X(03)
    private AlphanumericVar plhjTamaulipast = new AlphanumericVar(this, 3);  // 15 PLHJ-TAMAULIPAST X(03)
    private AlphanumericVar plhjTlaxcalat = new AlphanumericVar(this, 3);    // 15 PLHJ-TLAXCALAT X(03)
    private AlphanumericVar plhjVeracruzt = new AlphanumericVar(this, 3);    // 15 PLHJ-VERACRUZT X(03)
    private AlphanumericVar plhjYucatant = new AlphanumericVar(this, 3);     // 15 PLHJ-YUCATANT X(03)
    private AlphanumericVar plhjZacatecast = new AlphanumericVar(this, 3);   // 15 PLHJ-ZACATECAST X(03)
    private AlphanumericVar plhjExtranjerot = new AlphanumericVar(this, 3);  // 15 PLHJ-EXTRANJEROT X(03)
    private AlphanumericVar plhjOtrost = new AlphanumericVar(this, 3);       // 15 PLHJ-OTROST X(03)

    public PlhjEstadosCveTrac() {
        super();
        initialize();
    }

    public PlhjEstadosCveTrac(DataContainer parent) {
        super(parent);
    }

    public PlhjEstadosCveTrac(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPlhjAgst(final String plhjAgst) {
        this.plhjAgst.setValue(plhjAgst);
    }

    public void setPlhjAgst(final IAlphanumericValue plhjAgst) {
        this.plhjAgst.setValue(plhjAgst);
    }

    public void setPlhjBcnt(final String plhjBcnt) {
        this.plhjBcnt.setValue(plhjBcnt);
    }

    public void setPlhjBcnt(final IAlphanumericValue plhjBcnt) {
        this.plhjBcnt.setValue(plhjBcnt);
    }

    public void setPlhjBcst(final String plhjBcst) {
        this.plhjBcst.setValue(plhjBcst);
    }

    public void setPlhjBcst(final IAlphanumericValue plhjBcst) {
        this.plhjBcst.setValue(plhjBcst);
    }

    public void setPlhjCampechet(final String plhjCampechet) {
        this.plhjCampechet.setValue(plhjCampechet);
    }

    public void setPlhjCampechet(final IAlphanumericValue plhjCampechet) {
        this.plhjCampechet.setValue(plhjCampechet);
    }

    public void setPlhjCoahuilat(final String plhjCoahuilat) {
        this.plhjCoahuilat.setValue(plhjCoahuilat);
    }

    public void setPlhjCoahuilat(final IAlphanumericValue plhjCoahuilat) {
        this.plhjCoahuilat.setValue(plhjCoahuilat);
    }

    public void setPlhjColimat(final String plhjColimat) {
        this.plhjColimat.setValue(plhjColimat);
    }

    public void setPlhjColimat(final IAlphanumericValue plhjColimat) {
        this.plhjColimat.setValue(plhjColimat);
    }

    public void setPlhjChiapast(final String plhjChiapast) {
        this.plhjChiapast.setValue(plhjChiapast);
    }

    public void setPlhjChiapast(final IAlphanumericValue plhjChiapast) {
        this.plhjChiapast.setValue(plhjChiapast);
    }

    public void setPlhjChihuahuat(final String plhjChihuahuat) {
        this.plhjChihuahuat.setValue(plhjChihuahuat);
    }

    public void setPlhjChihuahuat(final IAlphanumericValue plhjChihuahuat) {
        this.plhjChihuahuat.setValue(plhjChihuahuat);
    }

    public void setPlhjDft(final String plhjDft) {
        this.plhjDft.setValue(plhjDft);
    }

    public void setPlhjDft(final IAlphanumericValue plhjDft) {
        this.plhjDft.setValue(plhjDft);
    }

    public void setPlhjDurangot(final String plhjDurangot) {
        this.plhjDurangot.setValue(plhjDurangot);
    }

    public void setPlhjDurangot(final IAlphanumericValue plhjDurangot) {
        this.plhjDurangot.setValue(plhjDurangot);
    }

    public void setPlhjGtot(final String plhjGtot) {
        this.plhjGtot.setValue(plhjGtot);
    }

    public void setPlhjGtot(final IAlphanumericValue plhjGtot) {
        this.plhjGtot.setValue(plhjGtot);
    }

    public void setPlhjGuerrerot(final String plhjGuerrerot) {
        this.plhjGuerrerot.setValue(plhjGuerrerot);
    }

    public void setPlhjGuerrerot(final IAlphanumericValue plhjGuerrerot) {
        this.plhjGuerrerot.setValue(plhjGuerrerot);
    }

    public void setPlhjHidalgot(final String plhjHidalgot) {
        this.plhjHidalgot.setValue(plhjHidalgot);
    }

    public void setPlhjHidalgot(final IAlphanumericValue plhjHidalgot) {
        this.plhjHidalgot.setValue(plhjHidalgot);
    }

    public void setPlhjJaliscot(final String plhjJaliscot) {
        this.plhjJaliscot.setValue(plhjJaliscot);
    }

    public void setPlhjJaliscot(final IAlphanumericValue plhjJaliscot) {
        this.plhjJaliscot.setValue(plhjJaliscot);
    }

    public void setPlhjEdomext(final String plhjEdomext) {
        this.plhjEdomext.setValue(plhjEdomext);
    }

    public void setPlhjEdomext(final IAlphanumericValue plhjEdomext) {
        this.plhjEdomext.setValue(plhjEdomext);
    }

    public void setPlhjMichoacant(final String plhjMichoacant) {
        this.plhjMichoacant.setValue(plhjMichoacant);
    }

    public void setPlhjMichoacant(final IAlphanumericValue plhjMichoacant) {
        this.plhjMichoacant.setValue(plhjMichoacant);
    }

    public void setPlhjMorelost(final String plhjMorelost) {
        this.plhjMorelost.setValue(plhjMorelost);
    }

    public void setPlhjMorelost(final IAlphanumericValue plhjMorelost) {
        this.plhjMorelost.setValue(plhjMorelost);
    }

    public void setPlhjNayaritt(final String plhjNayaritt) {
        this.plhjNayaritt.setValue(plhjNayaritt);
    }

    public void setPlhjNayaritt(final IAlphanumericValue plhjNayaritt) {
        this.plhjNayaritt.setValue(plhjNayaritt);
    }

    public void setPlhjNuevoleont(final String plhjNuevoleont) {
        this.plhjNuevoleont.setValue(plhjNuevoleont);
    }

    public void setPlhjNuevoleont(final IAlphanumericValue plhjNuevoleont) {
        this.plhjNuevoleont.setValue(plhjNuevoleont);
    }

    public void setPlhjOaxacat(final String plhjOaxacat) {
        this.plhjOaxacat.setValue(plhjOaxacat);
    }

    public void setPlhjOaxacat(final IAlphanumericValue plhjOaxacat) {
        this.plhjOaxacat.setValue(plhjOaxacat);
    }

    public void setPlhjPueblat(final String plhjPueblat) {
        this.plhjPueblat.setValue(plhjPueblat);
    }

    public void setPlhjPueblat(final IAlphanumericValue plhjPueblat) {
        this.plhjPueblat.setValue(plhjPueblat);
    }

    public void setPlhjQueretarot(final String plhjQueretarot) {
        this.plhjQueretarot.setValue(plhjQueretarot);
    }

    public void setPlhjQueretarot(final IAlphanumericValue plhjQueretarot) {
        this.plhjQueretarot.setValue(plhjQueretarot);
    }

    public void setPlhjQroot(final String plhjQroot) {
        this.plhjQroot.setValue(plhjQroot);
    }

    public void setPlhjQroot(final IAlphanumericValue plhjQroot) {
        this.plhjQroot.setValue(plhjQroot);
    }

    public void setPlhjSlpt(final String plhjSlpt) {
        this.plhjSlpt.setValue(plhjSlpt);
    }

    public void setPlhjSlpt(final IAlphanumericValue plhjSlpt) {
        this.plhjSlpt.setValue(plhjSlpt);
    }

    public void setPlhjSinaloat(final String plhjSinaloat) {
        this.plhjSinaloat.setValue(plhjSinaloat);
    }

    public void setPlhjSinaloat(final IAlphanumericValue plhjSinaloat) {
        this.plhjSinaloat.setValue(plhjSinaloat);
    }

    public void setPlhjSonorat(final String plhjSonorat) {
        this.plhjSonorat.setValue(plhjSonorat);
    }

    public void setPlhjSonorat(final IAlphanumericValue plhjSonorat) {
        this.plhjSonorat.setValue(plhjSonorat);
    }

    public void setPlhjTabascot(final String plhjTabascot) {
        this.plhjTabascot.setValue(plhjTabascot);
    }

    public void setPlhjTabascot(final IAlphanumericValue plhjTabascot) {
        this.plhjTabascot.setValue(plhjTabascot);
    }

    public void setPlhjTamaulipast(final String plhjTamaulipast) {
        this.plhjTamaulipast.setValue(plhjTamaulipast);
    }

    public void setPlhjTamaulipast(final IAlphanumericValue plhjTamaulipast) {
        this.plhjTamaulipast.setValue(plhjTamaulipast);
    }

    public void setPlhjTlaxcalat(final String plhjTlaxcalat) {
        this.plhjTlaxcalat.setValue(plhjTlaxcalat);
    }

    public void setPlhjTlaxcalat(final IAlphanumericValue plhjTlaxcalat) {
        this.plhjTlaxcalat.setValue(plhjTlaxcalat);
    }

    public void setPlhjVeracruzt(final String plhjVeracruzt) {
        this.plhjVeracruzt.setValue(plhjVeracruzt);
    }

    public void setPlhjVeracruzt(final IAlphanumericValue plhjVeracruzt) {
        this.plhjVeracruzt.setValue(plhjVeracruzt);
    }

    public void setPlhjYucatant(final String plhjYucatant) {
        this.plhjYucatant.setValue(plhjYucatant);
    }

    public void setPlhjYucatant(final IAlphanumericValue plhjYucatant) {
        this.plhjYucatant.setValue(plhjYucatant);
    }

    public void setPlhjZacatecast(final String plhjZacatecast) {
        this.plhjZacatecast.setValue(plhjZacatecast);
    }

    public void setPlhjZacatecast(final IAlphanumericValue plhjZacatecast) {
        this.plhjZacatecast.setValue(plhjZacatecast);
    }

    public void setPlhjExtranjerot(final String plhjExtranjerot) {
        this.plhjExtranjerot.setValue(plhjExtranjerot);
    }

    public void setPlhjExtranjerot(final IAlphanumericValue plhjExtranjerot) {
        this.plhjExtranjerot.setValue(plhjExtranjerot);
    }

    public void setPlhjOtrost(final String plhjOtrost) {
        this.plhjOtrost.setValue(plhjOtrost);
    }

    public void setPlhjOtrost(final IAlphanumericValue plhjOtrost) {
        this.plhjOtrost.setValue(plhjOtrost);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhjAgst.setValue("AGS");
        this.plhjBcnt.setValue("BCN");
        this.plhjBcst.setValue("BCS");
        this.plhjCampechet.setValue("CAM");
        this.plhjCoahuilat.setValue("COA");
        this.plhjColimat.setValue("COL");
        this.plhjChiapast.setValue("CHA");
        this.plhjChihuahuat.setValue("CHI");
        this.plhjDft.setValue("D.F");
        this.plhjDurangot.setValue("DGO");
        this.plhjGtot.setValue("GTO");
        this.plhjGuerrerot.setValue("GRO");
        this.plhjHidalgot.setValue("HGO");
        this.plhjJaliscot.setValue("JAL");
        this.plhjEdomext.setValue("MEX");
        this.plhjMichoacant.setValue("MCH");
        this.plhjMorelost.setValue("MOR");
        this.plhjNayaritt.setValue("NAY");
        this.plhjNuevoleont.setValue("N.L");
        this.plhjOaxacat.setValue("OAX");
        this.plhjPueblat.setValue("PUE");
        this.plhjQueretarot.setValue("QRO");
        this.plhjQroot.setValue("Q.R");
        this.plhjSlpt.setValue("SLP");
        this.plhjSinaloat.setValue("SIN");
        this.plhjSonorat.setValue("SON");
        this.plhjTabascot.setValue("TAB");
        this.plhjTamaulipast.setValue("TAM");
        this.plhjTlaxcalat.setValue("TLX");
        this.plhjVeracruzt.setValue("VER");
        this.plhjYucatant.setValue("YUC");
        this.plhjZacatecast.setValue("ZAC");
        this.plhjExtranjerot.setValue("EXT");
        this.plhjOtrost.setValue("OTR");
    }

}

