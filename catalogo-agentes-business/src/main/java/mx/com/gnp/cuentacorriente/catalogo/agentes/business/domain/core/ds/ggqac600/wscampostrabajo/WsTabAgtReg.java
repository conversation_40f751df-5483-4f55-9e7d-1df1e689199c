package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.wstabagtreg.WsTabAgtR;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TAB-AGT-REG.
 *
 */
@Setter
@Getter
public class WsTabAgtReg { // VStruct
    // Properties
    private UnsignedNumericVar wsTabAgtN = new UnsignedNumericVar(10, 0);    // 10 WS-TAB-AGT-N 9(10) REDEFINED BY WS-TAB-AGT-R
    private WsTabAgtR wsTabAgtR = new  WsTabAgtR(null, this.wsTabAgtN);      // 10 WS-TAB-AGT-R REDEFINES WS-TAB-AGT-N

    public WsTabAgtReg() {
        initialize();
    }



    public void setWsTabAgtN(final BigDecimal wsTabAgtN) {
        this.wsTabAgtN.setValue(wsTabAgtN);
    }

    public void setWsTabAgtN(final int wsTabAgtN) {
        this.wsTabAgtN.setValue(wsTabAgtN);
    }

    public void setWsTabAgtN(final INumericValue wsTabAgtN) {
        this.wsTabAgtN.setValue(wsTabAgtN);
    }


    // Deeper properties

    // Property: wsTabAgtR.wsTabFiller -> 15 WS-TAB-FILLER X(03)
    public AlphanumericVar getWsTabFiller() {
        return this.wsTabAgtR.getWsTabFiller();
    }

    public void setWsTabFiller(final String wsTabFiller) {
        this.wsTabAgtR.setWsTabFiller(wsTabFiller);
    }

    public void setWsTabFiller(final IAlphanumericValue wsTabFiller) {
        this.wsTabAgtR.setWsTabFiller(wsTabFiller);
    }

    // Property: wsTabAgtR.wsTabAgt -> 15 WS-TAB-AGT X(07)
    public AlphanumericVar getWsTabAgt() {
        return this.wsTabAgtR.getWsTabAgt();
    }

    public void setWsTabAgt(final String wsTabAgt) {
        this.wsTabAgtR.setWsTabAgt(wsTabAgt);
    }

    public void setWsTabAgt(final IAlphanumericValue wsTabAgt) {
        this.wsTabAgtR.setWsTabAgt(wsTabAgt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsTabAgtN.clear();
    }

}

