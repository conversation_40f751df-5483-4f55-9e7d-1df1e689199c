package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2.Agt0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2.Cna0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2.Fla0;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-VARIABLES-DB2.
 *
 */
// 93      *================================================================*
// 94      *   REGISTRO DE ACCESOS A DB2.                                   *
// 95      *================================================================*
// 96                                                                  
@Setter
@Getter
public class WsVariablesDb2 { // VStruct
    // Properties
    private Agt0 agt0 = new  Agt0();                                         // 05 AGT0
    private Cna0 cna0 = new  Cna0();                                         // 05 CNA0
    private Fla0 fla0 = new  Fla0();                                         // 05 FLA0

    public WsVariablesDb2() {
        initialize();
    }




    // Deeper properties

    // Property: agt0.wsAgtAnt -> 10 WS-AGT-ANT S9(9) COMPUTATIONAL
    public int getWsAgtAnt() {
        return this.agt0.getWsAgtAnt();
    }

    public void setWsAgtAnt(final int wsAgtAnt) {
        this.agt0.setWsAgtAnt(wsAgtAnt);
    }

    // Property: agt0.agtAgtIdr -> 10 AGT-AGT-IDR S9(9) COMPUTATIONAL
    public int getAgtAgtIdr() {
        return this.agt0.getAgtAgtIdr();
    }

    public void setAgtAgtIdr(final int agtAgtIdr) {
        this.agt0.setAgtAgtIdr(agtAgtIdr);
    }

    // Property: agt0.agtAgtNom -> 10 AGT-AGT-NOM X(40)
    public String getAgtAgtNom() {
        return this.agt0.getAgtAgtNom();
    }

    public void setAgtAgtNom(final String agtAgtNom) {
        this.agt0.setAgtAgtNom(agtAgtNom);
    }

    // Property: agt0.agtAgtApePat -> 10 AGT-AGT-APE-PAT X(40)
    public String getAgtAgtApePat() {
        return this.agt0.getAgtAgtApePat();
    }

    public void setAgtAgtApePat(final String agtAgtApePat) {
        this.agt0.setAgtAgtApePat(agtAgtApePat);
    }

    // Property: agt0.agtAgtApeMat -> 10 AGT-AGT-APE-MAT X(30)
    public String getAgtAgtApeMat() {
        return this.agt0.getAgtAgtApeMat();
    }

    public void setAgtAgtApeMat(final String agtAgtApeMat) {
        this.agt0.setAgtAgtApeMat(agtAgtApeMat);
    }

    // Property: agt0.agtAgtNip -> 10 AGT-AGT-NIP S9(4) COMPUTATIONAL
    public int getAgtAgtNip() {
        return this.agt0.getAgtAgtNip();
    }

    public void setAgtAgtNip(final int agtAgtNip) {
        this.agt0.setAgtAgtNip(agtAgtNip);
    }

    // Property: agt0.cmaCmaDes -> 10 CMA-CMA-DES X(60)
    public String getCmaCmaDes() {
        return this.agt0.getCmaCmaDes();
    }

    public void setCmaCmaDes(final String cmaCmaDes) {
        this.agt0.setCmaCmaDes(cmaCmaDes);
    }

    // Property: agt0.wsIndGerente -> 10 WS-IND-GERENTE X(07)
    public String getWsIndGerente() {
        return this.agt0.getWsIndGerente();
    }

    public void setWsIndGerente(final String wsIndGerente) {
        this.agt0.setWsIndGerente(wsIndGerente);
    }

    // Property: agt0.wsOrvCve -> 10 WS-ORV-CVE S9(4) COMPUTATIONAL
    public int getWsOrvCve() {
        return this.agt0.getWsOrvCve();
    }

    public void setWsOrvCve(final int wsOrvCve) {
        this.agt0.setWsOrvCve(wsOrvCve);
    }

    // Property: agt0.agtRazSoc -> 10 AGT-RAZ-SOC X(60)
    public String getAgtRazSoc() {
        return this.agt0.getAgtRazSoc();
    }

    public void setAgtRazSoc(final String agtRazSoc) {
        this.agt0.setAgtRazSoc(agtRazSoc);
    }

    // Property: agt0.agtPefCve -> 10 AGT-PEF-CVE S9(4) COMPUTATIONAL
    public int getAgtPefCve() {
        return this.agt0.getAgtPefCve();
    }

    public void setAgtPefCve(final int agtPefCve) {
        this.agt0.setAgtPefCve(agtPefCve);
    }


    // Deeper properties

    // Property: cna0.cnaCnaNum -> 10 CNA-CNA-NUM S9(4) COMPUTATIONAL
    public int getCnaCnaNum() {
        return this.cna0.getCnaCnaNum();
    }

    public void setCnaCnaNum(final int cnaCnaNum) {
        this.cna0.setCnaCnaNum(cnaCnaNum);
    }

    // Property: cna0.wsCnaCnaNum -> 10 WS-CNA-CNA-NUM 9(02)
    public int getWsCnaCnaNum() {
        return this.cna0.getWsCnaCnaNum();
    }

    public void setWsCnaCnaNum(final int wsCnaCnaNum) {
        this.cna0.setWsCnaCnaNum(wsCnaCnaNum);
    }

    // Property: cna0.cnaGerZonEnv -> 10 CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    public int getCnaGerZonEnv() {
        return this.cna0.getCnaGerZonEnv();
    }

    public void setCnaGerZonEnv(final int cnaGerZonEnv) {
        this.cna0.setCnaGerZonEnv(cnaGerZonEnv);
    }

    // Property: cna0.wsCnaGerZon -> 10 WS-CNA-GER-ZON 9(04)
    public int getWsCnaGerZon() {
        return this.cna0.getWsCnaGerZon();
    }

    public void setWsCnaGerZon(final int wsCnaGerZon) {
        this.cna0.setWsCnaGerZon(wsCnaGerZon);
    }

    // Property: cna0.cnaOfnCve -> 10 CNA-OFN-CVE S9(4) COMPUTATIONAL
    public int getCnaOfnCve() {
        return this.cna0.getCnaOfnCve();
    }

    public void setCnaOfnCve(final int cnaOfnCve) {
        this.cna0.setCnaOfnCve(cnaOfnCve);
    }

    // Property: cna0.wsCnaOfnCve -> 10 WS-CNA-OFN-CVE 9(04)
    public int getWsCnaOfnCve() {
        return this.cna0.getWsCnaOfnCve();
    }

    public void setWsCnaOfnCve(final int wsCnaOfnCve) {
        this.cna0.setWsCnaOfnCve(wsCnaOfnCve);
    }

    // Property: cna0.cnaEmpCve -> 10 CNA-EMP-CVE X(05)
    public String getCnaEmpCve() {
        return this.cna0.getCnaEmpCve();
    }

    public void setCnaEmpCve(final String cnaEmpCve) {
        this.cna0.setCnaEmpCve(cnaEmpCve);
    }

    // Property: cna0.cnaTfpCve -> 10 CNA-TFP-CVE S9(4) COMPUTATIONAL
    public int getCnaTfpCve() {
        return this.cna0.getCnaTfpCve();
    }

    public void setCnaTfpCve(final int cnaTfpCve) {
        this.cna0.setCnaTfpCve(cnaTfpCve);
    }

    // Property: cna0.ceoCeoCve -> 10 CEO-CEO-CVE S9(4) COMPUTATIONAL
    public int getCeoCeoCve() {
        return this.cna0.getCeoCeoCve();
    }

    public void setCeoCeoCve(final int ceoCeoCve) {
        this.cna0.setCeoCeoCve(ceoCeoCve);
    }

    // Property: cna0.wsCeoCeoCve -> 10 WS-CEO-CEO-CVE 9(04)
    public int getWsCeoCeoCve() {
        return this.cna0.getWsCeoCeoCve();
    }

    public void setWsCeoCeoCve(final int wsCeoCeoCve) {
        this.cna0.setWsCeoCeoCve(wsCeoCeoCve);
    }

    // Property: cna0.ceoCeoNom -> 10 CEO-CEO-NOM X(30)
    public String getCeoCeoNom() {
        return this.cna0.getCeoCeoNom();
    }

    public void setCeoCeoNom(final String ceoCeoNom) {
        this.cna0.setCeoCeoNom(ceoCeoNom);
    }

    // Property: cna0.wsCeoCeoNom -> 10 WS-CEO-CEO-NOM X(30)
    public String getWsCeoCeoNom() {
        return this.cna0.getWsCeoCeoNom();
    }

    public void setWsCeoCeoNom(final String wsCeoCeoNom) {
        this.cna0.setWsCeoCeoNom(wsCeoCeoNom);
    }


    // Deeper properties

    // Property: fla0.flaFlaNum -> 10 FLA-FLA-NUM X(08)
    public String getFlaFlaNum() {
        return this.fla0.getFlaFlaNum();
    }

    public void setFlaFlaNum(final String flaFlaNum) {
        this.fla0.setFlaFlaNum(flaFlaNum);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.agt0.initialize();
        this.cna0.initialize();
        this.fla0.initialize();
    }

}

