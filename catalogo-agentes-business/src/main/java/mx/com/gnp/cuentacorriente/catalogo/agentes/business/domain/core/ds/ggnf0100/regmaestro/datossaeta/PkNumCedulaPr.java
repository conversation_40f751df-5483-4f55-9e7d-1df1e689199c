package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PK-NUM-CEDULA-PR.
 *
 */
@Setter
@Getter
public class PkNumCedulaPr extends DataStruct {
    // Properties
    private AlphanumericVar pkNumCedulaPDos = new AlphanumericVar(this, 2);  // 07 PK-NUM-CEDULA-P-DOS XX
    private AlphanumericVar pkNumCedulaPR18 = new AlphanumericVar(this, 18);                 // 07 PK-NUM-CEDULA-P-R18 X(18)

    public PkNumCedulaPr() {
        super();
        initialize();
    }

    public PkNumCedulaPr(DataContainer parent) {
        super(parent);
    }

    public PkNumCedulaPr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkNumCedulaPDos(final String pkNumCedulaPDos) {
        this.pkNumCedulaPDos.setValue(pkNumCedulaPDos);
    }

    public void setPkNumCedulaPDos(final IAlphanumericValue pkNumCedulaPDos) {
        this.pkNumCedulaPDos.setValue(pkNumCedulaPDos);
    }

    public void setPkNumCedulaPR18(final String pkNumCedulaPR18) {
        this.pkNumCedulaPR18.setValue(pkNumCedulaPR18);
    }

    public void setPkNumCedulaPR18(final IAlphanumericValue pkNumCedulaPR18) {
        this.pkNumCedulaPR18.setValue(pkNumCedulaPR18);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkNumCedulaPDos.clear();
        this.pkNumCedulaPR18.clear();
    }

}

