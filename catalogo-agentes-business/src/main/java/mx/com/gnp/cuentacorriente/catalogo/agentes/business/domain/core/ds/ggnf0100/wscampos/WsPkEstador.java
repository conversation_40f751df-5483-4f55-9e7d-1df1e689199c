package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-PK-ESTADOR.
 *
 */
// 1224075200*                                                                 07520000
// 1225075300*                                                                 07530000
@Setter
@Getter
public class WsPkEstador extends DataStruct {
    // Properties
    private UnsignedNumericVar wsPkEstado1 = new UnsignedNumericVar(this, 2, 0);             // 05 WS-PK-ESTADO1 99
    private AlphanumericVar wsPkEstado2 = new AlphanumericVar(this, 2);      // 05 WS-PK-ESTADO2 XX

    public WsPkEstador() {
        super();
        initialize();
    }

    public WsPkEstador(DataContainer parent) {
        super(parent);
    }

    public WsPkEstador(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsPkEstado1(final int wsPkEstado1) {
        this.wsPkEstado1.setValue(wsPkEstado1);
    }

    public void setWsPkEstado1(final BigDecimal wsPkEstado1) {
        this.wsPkEstado1.setValue(wsPkEstado1);
    }

    public void setWsPkEstado1(final INumericValue wsPkEstado1) {
        this.wsPkEstado1.setValue(wsPkEstado1);
    }

    public void setWsPkEstado2(final String wsPkEstado2) {
        this.wsPkEstado2.setValue(wsPkEstado2);
    }

    public void setWsPkEstado2(final IAlphanumericValue wsPkEstado2) {
        this.wsPkEstado2.setValue(wsPkEstado2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsPkEstado1.clear();
        this.wsPkEstado2.clear();
    }

}

