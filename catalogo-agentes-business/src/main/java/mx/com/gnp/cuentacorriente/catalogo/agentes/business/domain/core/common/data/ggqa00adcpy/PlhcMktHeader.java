package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.plhcmktheader.PlhcMktCompany;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PLHC-MKT-HEADER.
 *
 */
// 510      ******************************************************************00010000
// 511      *  COPY...... GNPL00HC                                           *00020000
// 512      *  FUNCION... DEFINICION DEL ARCHIVO INTERFAZ DE AGENTES         *00030000
// 513      *             (MARKETER INTERFACE) (GNP A CSC).                  *00040000
// 514      *  AUTOR..... GERARDO AXOTLA MU#OZ. (C.T.I.)                     *00050000
// 515      *  FECHA..... MARZO 2002                                         *00060000
// 516      *  LRECL..... 844                                                *00070000
// 517      *  MODIFICO..                                                    *00080000
// 518      ******************************************************************00090000
@Setter
@Getter
public class PlhcMktHeader extends DataStruct {
    // Properties
    private AlphanumericVar plhcMktRecordIdh = new AlphanumericVar(this, 5);                 // 10 PLHC-MKT-RECORD-IDH X(05)
    private PlhcMktCompany plhcMktCompany = new  PlhcMktCompany(this);       // 10 PLHC-MKT-COMPANY
    private AlphanumericVar filler2 = new AlphanumericVar(this, 20);         // 10 FILLER X(20)
    private UnsignedNumericVar plhcMktProcessDt = new UnsignedNumericVar(this, 8, 0);        // 10 PLHC-MKT-PROCESS-DT 9(08)
    private UnsignedNumericVar plhcMktCreationDt = new UnsignedNumericVar(this, 8, 0);       // 10 PLHC-MKT-CREATION-DT 9(08)
    private UnsignedNumericVar plhcMktRecordCt = new UnsignedNumericVar(this, 6, 0);         // 10 PLHC-MKT-RECORD-CT 9(06)
    private AlphanumericVar filler3 = new AlphanumericVar(this, 776);        // 10 FILLER X(0776)
    private AlphanumericVar plhcMktSortIndh = new AlphanumericVar(this, 1);  // 10 PLHC-MKT-SORT-INDH X(0001)
    private AlphanumericVar filler4 = new AlphanumericVar(this, 10);         // 10 FILLER X(0010)
    private AlphanumericVar plhcMktEndrech = new AlphanumericVar(this, 1);   // 10 PLHC-MKT-ENDRECH X(0001)

    public PlhcMktHeader() {
        super();
        initialize();
    }

    public PlhcMktHeader(DataContainer parent) {
        super(parent);
    }

    public PlhcMktHeader(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPlhcMktRecordIdh(final String plhcMktRecordIdh) {
        this.plhcMktRecordIdh.setValue(plhcMktRecordIdh);
    }

    public void setPlhcMktRecordIdh(final IAlphanumericValue plhcMktRecordIdh) {
        this.plhcMktRecordIdh.setValue(plhcMktRecordIdh);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setPlhcMktProcessDt(final int plhcMktProcessDt) {
        this.plhcMktProcessDt.setValue(plhcMktProcessDt);
    }

    public void setPlhcMktProcessDt(final BigDecimal plhcMktProcessDt) {
        this.plhcMktProcessDt.setValue(plhcMktProcessDt);
    }

    public void setPlhcMktProcessDt(final INumericValue plhcMktProcessDt) {
        this.plhcMktProcessDt.setValue(plhcMktProcessDt);
    }

    public void setPlhcMktCreationDt(final int plhcMktCreationDt) {
        this.plhcMktCreationDt.setValue(plhcMktCreationDt);
    }

    public void setPlhcMktCreationDt(final BigDecimal plhcMktCreationDt) {
        this.plhcMktCreationDt.setValue(plhcMktCreationDt);
    }

    public void setPlhcMktCreationDt(final INumericValue plhcMktCreationDt) {
        this.plhcMktCreationDt.setValue(plhcMktCreationDt);
    }

    public void setPlhcMktRecordCt(final int plhcMktRecordCt) {
        this.plhcMktRecordCt.setValue(plhcMktRecordCt);
    }

    public void setPlhcMktRecordCt(final BigDecimal plhcMktRecordCt) {
        this.plhcMktRecordCt.setValue(plhcMktRecordCt);
    }

    public void setPlhcMktRecordCt(final INumericValue plhcMktRecordCt) {
        this.plhcMktRecordCt.setValue(plhcMktRecordCt);
    }

    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }

    public void setPlhcMktSortIndh(final String plhcMktSortIndh) {
        this.plhcMktSortIndh.setValue(plhcMktSortIndh);
    }

    public void setPlhcMktSortIndh(final IAlphanumericValue plhcMktSortIndh) {
        this.plhcMktSortIndh.setValue(plhcMktSortIndh);
    }

    public void setFiller4(final String filler4) {
        this.filler4.setValue(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.filler4.setValue(filler4);
    }

    public void setPlhcMktEndrech(final String plhcMktEndrech) {
        this.plhcMktEndrech.setValue(plhcMktEndrech);
    }

    public void setPlhcMktEndrech(final IAlphanumericValue plhcMktEndrech) {
        this.plhcMktEndrech.setValue(plhcMktEndrech);
    }


    // Deeper properties

    // Property: plhcMktCompany.plhcMktCompanyId -> 15 PLHC-MKT-COMPANY-ID X(07)
    public AlphanumericVar getPlhcMktCompanyId() {
        return this.plhcMktCompany.getPlhcMktCompanyId();
    }

    public void setPlhcMktCompanyId(final String plhcMktCompanyId) {
        this.plhcMktCompany.setPlhcMktCompanyId(plhcMktCompanyId);
    }

    public void setPlhcMktCompanyId(final IAlphanumericValue plhcMktCompanyId) {
        this.plhcMktCompany.setPlhcMktCompanyId(plhcMktCompanyId);
    }

    // Property: plhcMktCompany.filler1 -> 15 FILLER X(02)
    public AlphanumericVar getFiller1() {
        return this.plhcMktCompany.getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.plhcMktCompany.setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.plhcMktCompany.setFiller1(filler1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhcMktRecordIdh.setValue("");
        this.plhcMktCompany.initialize();
        this.filler2.setValue("");
        this.plhcMktProcessDt.setZero();
        this.plhcMktCreationDt.setZero();
        this.plhcMktRecordCt.setZero();
        this.filler3.setValue("");
        this.plhcMktSortIndh.setValue("");
        this.filler4.setValue("");
        this.plhcMktEndrech.setValue("X");
    }

}

