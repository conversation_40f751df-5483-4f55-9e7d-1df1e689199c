package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CONTADORES.
 *
 */
@Setter
@Getter
public class WsContadores { // VStruct
    // Properties
    private UnsignedNumericVar wsContador = new UnsignedNumericVar(14, 0);   // 03 WS-CONTADOR 9(14)
    private int wsLeiExtraccion;                                            // 03 WS-LEI-EXTRACCION 9(05)
    private int wsLeiCatDirecc;                                             // 03 WS-LEI-CAT-DIRECC 9(05)
    private int wsLeiAgentes;                                               // 03 WS-LEI-AGENTES 9(05)
    private int wsLeiCna;                                                   // 03 WS-LEI-CNA 9(05)
    private int wsLeiDsc;                                                   // 03 WS-LEI-DSC 9(05)
    private int wsLeiCurMte;                                                // 03 WS-LEI-CUR-MTE 9(05)
    private int wsLeiCurEsa;                                                // 03 WS-LEI-CUR-ESA 9(05)
    private int wsLeiAgentesVigentes;                                       // 03 WS-LEI-AGENTES-VIGENTES 9(05)
    private int wsLeiAgentesCancelados;                                     // 03 WS-LEI-AGENTES-CANCELADOS 9(05)
    private int wsLeiAgentesSuspendidos;                                    // 03 WS-LEI-AGENTES-SUSPENDIDOS 9(05)
    private int wsLeiAgentesSinEstatus;                                     // 03 WS-LEI-AGENTES-SIN-ESTATUS 9(05)
    private int wsLeiCnaVigentes;                                           // 03 WS-LEI-CNA-VIGENTES 9(05)
    private int wsLeiCnaCancelados;                                         // 03 WS-LEI-CNA-CANCELADOS 9(05)
    private int wsLeiCnaSuspendidos;                                        // 03 WS-LEI-CNA-SUSPENDIDOS 9(05)
    private int wsLeiCnaSinEstatus;                                         // 03 WS-LEI-CNA-SIN-ESTATUS 9(05)
    private int wsLeiEmpleados;                                             // 03 WS-LEI-EMPLEADOS 9(05)
    private int wsLeiExtranjeros;                                           // 03 WS-LEI-EXTRANJEROS 9(05)
    private int wsLeiGfvtagt;                                               // 03 WS-LEI-GFVTAGT 9(05)
    private int wsLeiGfvtcna;                                               // 03 WS-LEI-GFVTCNA 9(05)
    private int wsLeiGfvtpra;                                               // 03 WS-LEI-GFVTPRA 9(05)
    private int wsLeiCurDma;                                                // 03 WS-LEI-CUR-DMA 9(05)
    private int wsLeiGcctmnd;                                               // 03 WS-LEI-GCCTMND 9(05)
    private int wsLeiGcctpob;                                               // 03 WS-LEI-GCCTPOB 9(05)
    private int wsLeiGfvtcma;                                               // 03 WS-LEI-GFVTCMA 9(05)
    private int wsLeiGcctedo;                                               // 03 WS-LEI-GCCTEDO 9(05)
    private int wsLeiGcctpai;                                               // 03 WS-LEI-GCCTPAI 9(05)
    private int wsSinGfvtagt;                                               // 03 WS-SIN-GFVTAGT 9(05)
    private int wsSinGfvtcna;                                               // 03 WS-SIN-GFVTCNA 9(05)
    private int wsSinGfvtdsc;                                               // 03 WS-SIN-GFVTDSC 9(05)
    private int wsSinEsquemaFact;                                           // 03 WS-SIN-ESQUEMA-FACT 9(05)
    private int wsSinGfvtpra;                                               // 03 WS-SIN-GFVTPRA 9(05)
    private int wsSinGfvtdma;                                               // 03 WS-SIN-GFVTDMA 9(05)
    private int wsLeiCurDmaNulos;                                           // 03 WS-LEI-CUR-DMA-NULOS 9(05)
    private int wsSinGcctmnd;                                               // 03 WS-SIN-GCCTMND 9(05)
    private int wsSinGcctpob;                                               // 03 WS-SIN-GCCTPOB 9(05)
    private int wsSinGfvtcma;                                               // 03 WS-SIN-GFVTCMA 9(05)
    private int wsSinGcctedo;                                               // 03 WS-SIN-GCCTEDO 9(05)
    private int wsSinGcctpai;                                               // 03 WS-SIN-GCCTPAI 9(05)
    private int wsSinRazSocial;                                             // 03 WS-SIN-RAZ-SOCIAL 9(05)
    private int wsSinDirFiscal;                                             // 03 WS-SIN-DIR-FISCAL 9(05)
    private int wsConDirFiscal;                                             // 03 WS-CON-DIR-FISCAL 9(05)
    private int wsSinDirReceptor;                                           // 03 WS-SIN-DIR-RECEPTOR 9(05)
    private int wsRegsGrabe;                                                // 03 WS-REGS-GRABE 9(05)
    private int wsCont;                                                     // 03 WS-CONT 9(05)
    private int wsExcluidos;                                                // 03 WS-EXCLUIDOS 9(05)

    public WsContadores() {
        initialize();
    }



    public void setWsContador(final BigDecimal wsContador) {
        this.wsContador.setValue(wsContador);
    }

    public void setWsContador(final int wsContador) {
        this.wsContador.setValue(wsContador);
    }

    public void setWsContador(final INumericValue wsContador) {
        this.wsContador.setValue(wsContador);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsContador.clear();
        this.wsLeiExtraccion = 0;
        this.wsLeiCatDirecc = 0;
        this.wsLeiAgentes = 0;
        this.wsLeiCna = 0;
        this.wsLeiDsc = 0;
        this.wsLeiCurMte = 0;
        this.wsLeiCurEsa = 0;
        this.wsLeiAgentesVigentes = 0;
        this.wsLeiAgentesCancelados = 0;
        this.wsLeiAgentesSuspendidos = 0;
        this.wsLeiAgentesSinEstatus = 0;
        this.wsLeiCnaVigentes = 0;
        this.wsLeiCnaCancelados = 0;
        this.wsLeiCnaSuspendidos = 0;
        this.wsLeiCnaSinEstatus = 0;
        this.wsLeiEmpleados = 0;
        this.wsLeiExtranjeros = 0;
        this.wsLeiGfvtagt = 0;
        this.wsLeiGfvtcna = 0;
        this.wsLeiGfvtpra = 0;
        this.wsLeiCurDma = 0;
        this.wsLeiGcctmnd = 0;
        this.wsLeiGcctpob = 0;
        this.wsLeiGfvtcma = 0;
        this.wsLeiGcctedo = 0;
        this.wsLeiGcctpai = 0;
        this.wsSinGfvtagt = 0;
        this.wsSinGfvtcna = 0;
        this.wsSinGfvtdsc = 0;
        this.wsSinEsquemaFact = 0;
        this.wsSinGfvtpra = 0;
        this.wsSinGfvtdma = 0;
        this.wsLeiCurDmaNulos = 0;
        this.wsSinGcctmnd = 0;
        this.wsSinGcctpob = 0;
        this.wsSinGfvtcma = 0;
        this.wsSinGcctedo = 0;
        this.wsSinGcctpai = 0;
        this.wsSinRazSocial = 0;
        this.wsSinDirFiscal = 0;
        this.wsConDirFiscal = 0;
        this.wsSinDirReceptor = 0;
        this.wsRegsGrabe = 0;
        this.wsCont = 0;
        this.wsExcluidos = 0;
    }

}

