package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-REGISTROS.
 *
 */
// 1174070700*                                                                 07070000
// 1175070800*                                                                 07080000
@Setter
@Getter
public class WsRegistros { // VStruct
    // Properties
    private String wsDclgfvtfla00 = "";                                     // 03 WS-DCLGFVTFLA00 X(100)
    private String wsDclgfvtfla01 = "";                                     // 03 WS-DCLGFVTFLA01 X(100)
    private String wsDclgfvtfla02 = "";                                     // 03 WS-DCLGFVTFLA02 X(100)
    private String wsDclgfvtfla03 = "";                                     // 03 WS-DCLGFVTFLA03 X(100)
    private String wsDclgfvtcna00 = "";                                     // 03 WS-DCLGFVTCNA00 X(178)
    private String wsDclgfvtcna01 = "";                                     // 03 WS-DCLGFVTCNA01 X(178)

    public WsRegistros() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsDclgfvtfla00 = "";
        this.wsDclgfvtfla01 = "";
        this.wsDclgfvtfla02 = "";
        this.wsDclgfvtfla03 = "";
        this.wsDclgfvtcna00 = "";
        this.wsDclgfvtcna01 = "";
    }

}

