package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICADORES-AGT0.
 *
 */
@Setter
@Getter
public class IndicadoresAgt0 { // VStruct
    // Properties
    private int indagtnal = 0;                                              // 05 INDAGTNAL S9(4) COMPUTATIONAL
    private int indagtpef = 0;                                              // 05 INDAGTPEF S9(4) COMPUTATIONAL
    private int indagttca = 0;                                              // 05 INDAGTTCA S9(4) COMPUTATIONAL
    private int indagtmte = 0;                                              // 05 INDAGTMTE S9(4) COMPUTATIONAL

    public IndicadoresAgt0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.indagtnal = 0;
        this.indagtpef = 0;
        this.indagttca = 0;
        this.indagtmte = 0;
    }

}

