package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablas;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablas.tablaimptes.TabImptes;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-IMPTES.
 *
 */
@Setter
@Getter
public class TablaImptes { // VStruct
    // Properties
    private TabImptes tabImptes = new  TabImptes(2);                         // 03 TAB-IMPTES [2]

    public TablaImptes() {
        initialize();
    }



    public TabImptes getTabImptes(int index) {
        return this.tabImptes.at(index);
    }


    // Deeper properties

    // Property: tabImptes.tbPercGravab -> 05 TB-PERC-GRAVAB S9(14)V99
    public NumericVar getTbPercGravab() {
        return this.tabImptes.getTbPercGravab();
    }

    public void setTbPercGravab(final BigDecimal tbPercGravab) {
        this.tabImptes.setTbPercGravab(tbPercGravab);
    }

    public void setTbPercGravab(final int tbPercGravab) {
        this.tabImptes.setTbPercGravab(tbPercGravab);
    }

    public void setTbPercGravab(final INumericValue tbPercGravab) {
        this.tabImptes.setTbPercGravab(tbPercGravab);
    }

    // Property: tabImptes.tbPercExenta -> 05 TB-PERC-EXENTA S9(14)V99
    public NumericVar getTbPercExenta() {
        return this.tabImptes.getTbPercExenta();
    }

    public void setTbPercExenta(final BigDecimal tbPercExenta) {
        this.tabImptes.setTbPercExenta(tbPercExenta);
    }

    public void setTbPercExenta(final int tbPercExenta) {
        this.tabImptes.setTbPercExenta(tbPercExenta);
    }

    public void setTbPercExenta(final INumericValue tbPercExenta) {
        this.tabImptes.setTbPercExenta(tbPercExenta);
    }

    // Property: tabImptes.tbIva -> 05 TB-IVA S9(14)V99
    public NumericVar getTbIva() {
        return this.tabImptes.getTbIva();
    }

    public void setTbIva(final BigDecimal tbIva) {
        this.tabImptes.setTbIva(tbIva);
    }

    public void setTbIva(final int tbIva) {
        this.tabImptes.setTbIva(tbIva);
    }

    public void setTbIva(final INumericValue tbIva) {
        this.tabImptes.setTbIva(tbIva);
    }

    // Property: tabImptes.tbIsr -> 05 TB-ISR S9(14)V99
    public NumericVar getTbIsr() {
        return this.tabImptes.getTbIsr();
    }

    public void setTbIsr(final BigDecimal tbIsr) {
        this.tabImptes.setTbIsr(tbIsr);
    }

    public void setTbIsr(final int tbIsr) {
        this.tabImptes.setTbIsr(tbIsr);
    }

    public void setTbIsr(final INumericValue tbIsr) {
        this.tabImptes.setTbIsr(tbIsr);
    }

    // Property: tabImptes.tbIvaAcr -> 05 TB-IVA-ACR S9(14)V99
    public NumericVar getTbIvaAcr() {
        return this.tabImptes.getTbIvaAcr();
    }

    public void setTbIvaAcr(final BigDecimal tbIvaAcr) {
        this.tabImptes.setTbIvaAcr(tbIvaAcr);
    }

    public void setTbIvaAcr(final int tbIvaAcr) {
        this.tabImptes.setTbIvaAcr(tbIvaAcr);
    }

    public void setTbIvaAcr(final INumericValue tbIvaAcr) {
        this.tabImptes.setTbIvaAcr(tbIvaAcr);
    }

    // Property: tabImptes.tbIvaRtn -> 05 TB-IVA-RTN S9(14)V99
    public NumericVar getTbIvaRtn() {
        return this.tabImptes.getTbIvaRtn();
    }

    public void setTbIvaRtn(final BigDecimal tbIvaRtn) {
        this.tabImptes.setTbIvaRtn(tbIvaRtn);
    }

    public void setTbIvaRtn(final int tbIvaRtn) {
        this.tabImptes.setTbIvaRtn(tbIvaRtn);
    }

    public void setTbIvaRtn(final INumericValue tbIvaRtn) {
        this.tabImptes.setTbIvaRtn(tbIvaRtn);
    }

    // Property: tabImptes.tbIsrRet -> 05 TB-ISR-RET S9(14)V99
    public NumericVar getTbIsrRet() {
        return this.tabImptes.getTbIsrRet();
    }

    public void setTbIsrRet(final BigDecimal tbIsrRet) {
        this.tabImptes.setTbIsrRet(tbIsrRet);
    }

    public void setTbIsrRet(final int tbIsrRet) {
        this.tabImptes.setTbIsrRet(tbIsrRet);
    }

    public void setTbIsrRet(final INumericValue tbIsrRet) {
        this.tabImptes.setTbIsrRet(tbIsrRet);
    }

    // Property: tabImptes.tbIpuCed -> 05 TB-IPU-CED S9(14)V99
    public NumericVar getTbIpuCed() {
        return this.tabImptes.getTbIpuCed();
    }

    public void setTbIpuCed(final BigDecimal tbIpuCed) {
        this.tabImptes.setTbIpuCed(tbIpuCed);
    }

    public void setTbIpuCed(final int tbIpuCed) {
        this.tabImptes.setTbIpuCed(tbIpuCed);
    }

    public void setTbIpuCed(final INumericValue tbIpuCed) {
        this.tabImptes.setTbIpuCed(tbIpuCed);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tabImptes.initialize();
    }

}

