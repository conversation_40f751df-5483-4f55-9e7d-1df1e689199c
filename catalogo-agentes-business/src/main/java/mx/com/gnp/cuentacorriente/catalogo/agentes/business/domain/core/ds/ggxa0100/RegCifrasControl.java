package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-CIFRAS-CONTROL.
 *
 */
// 76      *================================================================*
// 77      *   REGISTRO DE CIFRAS CONTROL                                   *
// 78      *================================================================*
// 79                                                                  
@Setter
@Getter
public class RegCifrasControl extends DataStruct {
    // Properties
    private AlphanumericVar regCcDescripcion = new AlphanumericVar(this, 30);                // 10 REG-CC-DESCRIPCION X(30)
    private AlphanumericVar regCcCantidad = new AlphanumericVar(this, 15);   // 10 REG-CC-CANTIDAD X(15)

    public RegCifrasControl() {
        super();
        initialize();
    }

    public RegCifrasControl(DataContainer parent) {
        super(parent);
    }

    public RegCifrasControl(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRegCcDescripcion(final String regCcDescripcion) {
        this.regCcDescripcion.setValue(regCcDescripcion);
    }

    public void setRegCcDescripcion(final IAlphanumericValue regCcDescripcion) {
        this.regCcDescripcion.setValue(regCcDescripcion);
    }

    public void setRegCcCantidad(final String regCcCantidad) {
        this.regCcCantidad.setValue(regCcCantidad);
    }

    public void setRegCcCantidad(final IAlphanumericValue regCcCantidad) {
        this.regCcCantidad.setValue(regCcCantidad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.regCcDescripcion.setValue("");
        this.regCcCantidad.setValue("");
    }

}

