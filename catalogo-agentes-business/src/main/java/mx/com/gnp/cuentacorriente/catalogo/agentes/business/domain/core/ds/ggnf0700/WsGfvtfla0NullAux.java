package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTFLA0-NULL-AUX.
 *
 */
// 599033100*                                                                 03310002
// 600033200*                                                                 03320002
@Setter
@Getter
public class WsGfvtfla0NullAux extends DataStruct {
    // Properties
    private BinaryVarArray wsGfvtfla0Ele = new BinaryVarArray(this, 13, 4, 0);               // 03 WS-GFVTFLA0-ELE S9(4) [13] COMPUTATIONAL

    public WsGfvtfla0NullAux() {
        super();
        initialize();
    }

    public WsGfvtfla0NullAux(DataContainer parent) {
        super(parent);
    }

    public WsGfvtfla0NullAux(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public BinaryVar getWsGfvtfla0Ele(int index) {
        return this.wsGfvtfla0Ele.at(index);
    }

    public void setWsGfvtfla0Ele(final int wsGfvtfla0Ele) {
        this.wsGfvtfla0Ele.setValue(wsGfvtfla0Ele);
    }

    public void setWsGfvtfla0Ele(final BigDecimal wsGfvtfla0Ele) {
        this.wsGfvtfla0Ele.setValue(wsGfvtfla0Ele);
    }

    public void setWsGfvtfla0Ele(final BinaryVarArray wsGfvtfla0Ele) {
        this.wsGfvtfla0Ele.setValue(wsGfvtfla0Ele);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsGfvtfla0Ele.clear();
    }

}

