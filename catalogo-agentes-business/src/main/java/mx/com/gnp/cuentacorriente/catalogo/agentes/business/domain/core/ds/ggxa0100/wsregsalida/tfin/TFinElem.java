package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfin;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-FIN-ELEM.
 *
 */
@Setter
@Getter
public class TFinElem extends DataStruct {
    // Properties
    private AlphanumericVar sTablaFin = new AlphanumericVar(this, 9);        // 20 S-TABLA-FIN X(09)

    public TFinElem() {
        super();
        initialize();
    }

    public TFinElem(DataContainer parent) {
        super(parent);
    }

    public TFinElem(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSTablaFin(final String sTablaFin) {
        this.sTablaFin.setValue(sTablaFin);
    }

    public void setSTablaFin(final IAlphanumericValue sTablaFin) {
        this.sTablaFin.setValue(sTablaFin);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sTablaFin.clear();
    }

}

