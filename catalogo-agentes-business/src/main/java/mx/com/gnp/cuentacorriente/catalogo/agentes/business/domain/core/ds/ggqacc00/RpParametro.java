package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpparametro.RpParamFechaIni_1R;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpparametro.RpParamFechaFin_1R;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-PARAMETRO.
 *
 */
// 54      *************************                                         
// 55                                                                  
// 56      *-------------------------------------------------------          
// 57      * PARAMETRO                                                       
// 58      *-------------------------------------------------------          
@Setter
@Getter
public class RpParametro { // VStruct
    // Properties
    private String rpParamOpcion = "";                                      // 05 RP-PARAM-OPCION X(01)
    // Level 88 - Conditional names
    private static final String PARAM_OPCION_OK$1 = "R";
    private static final String PARAM_OPCION_OK$2 = "A";
    private static final String PARAM_AUTOMATICO = "A";
    private static final String PARAM_REPROCESO = "R";

    private AlphanumericVar rpParamFechaIni_1 = new AlphanumericVar(10);     // 05 RP-PARAM-FECHA-INI-1 X(10) REDEFINED BY RP-PARAM-FECHA-INI-1-R
    private RpParamFechaIni_1R rpParamFechaIni_1R = new  RpParamFechaIni_1R(null, this.rpParamFechaIni_1);   // 05 RP-PARAM-FECHA-INI-1-R REDEFINES RP-PARAM-FECHA-INI-1
    private AlphanumericVar rpParamFechaFin_1 = new AlphanumericVar(10);     // 05 RP-PARAM-FECHA-FIN-1 X(10) REDEFINED BY RP-PARAM-FECHA-FIN-1-R
    private RpParamFechaFin_1R rpParamFechaFin_1R = new  RpParamFechaFin_1R(null, this.rpParamFechaFin_1);   // 05 RP-PARAM-FECHA-FIN-1-R REDEFINES RP-PARAM-FECHA-FIN-1
    private String rpResto = "";                                            // 05 RP-RESTO X(16)

    public RpParametro() {
        initialize();
    }



    // Conditionals (88) - RP-PARAM-OPCION
    public boolean isParamOpcionOk() {
        return this.rpParamOpcion.equals(PARAM_OPCION_OK$1) || this.rpParamOpcion.equals(PARAM_OPCION_OK$2);
    }

    public void setParamOpcionOk() {
        this.rpParamOpcion = PARAM_OPCION_OK$1;
    }

    public boolean isParamAutomatico() {
        return this.rpParamOpcion.equals(PARAM_AUTOMATICO);
    }

    public void setParamAutomatico() {
        this.rpParamOpcion = PARAM_AUTOMATICO;
    }

    public boolean isParamReproceso() {
        return this.rpParamOpcion.equals(PARAM_REPROCESO);
    }

    public void setParamReproceso() {
        this.rpParamOpcion = PARAM_REPROCESO;
    }


    public void setRpParamFechaIni_1(final String rpParamFechaIni_1) {
        this.rpParamFechaIni_1.setValue(rpParamFechaIni_1);
    }

    public void setRpParamFechaIni_1(final IAlphanumericValue rpParamFechaIni_1) {
        this.rpParamFechaIni_1.setValue(rpParamFechaIni_1);
    }

    public void setRpParamFechaFin_1(final String rpParamFechaFin_1) {
        this.rpParamFechaFin_1.setValue(rpParamFechaFin_1);
    }

    public void setRpParamFechaFin_1(final IAlphanumericValue rpParamFechaFin_1) {
        this.rpParamFechaFin_1.setValue(rpParamFechaFin_1);
    }


    // Deeper properties

    // Property: rpParamFechaIni_1R.rpFechaIni_1Aaaa -> 07 RP-FECHA-INI-1-AAAA 9(04)
    public UnsignedNumericVar getRpFechaIni_1Aaaa() {
        return this.rpParamFechaIni_1R.getRpFechaIni_1Aaaa();
    }

    public void setRpFechaIni_1Aaaa(final int rpFechaIni_1Aaaa) {
        this.rpParamFechaIni_1R.setRpFechaIni_1Aaaa(rpFechaIni_1Aaaa);
    }

    public void setRpFechaIni_1Aaaa(final INumericValue rpFechaIni_1Aaaa) {
        this.rpParamFechaIni_1R.setRpFechaIni_1Aaaa(rpFechaIni_1Aaaa);
    }

    // Property: rpParamFechaIni_1R : condition FECHA-INI-1-AAAA-OK
    public boolean isFechaIni_1AaaaOk() {
        return this.rpParamFechaIni_1R.isFechaIni_1AaaaOk();
    }

    public void setFechaIni_1AaaaOk() {
        this.rpParamFechaIni_1R.setFechaIni_1AaaaOk();
    }

    // Property: rpParamFechaIni_1R.rpGuionIni_1_1 -> 07 RP-GUION-INI-1-1 X(01)
    public AlphanumericVar getRpGuionIni_1_1() {
        return this.rpParamFechaIni_1R.getRpGuionIni_1_1();
    }

    public void setRpGuionIni_1_1(final String rpGuionIni_1_1) {
        this.rpParamFechaIni_1R.setRpGuionIni_1_1(rpGuionIni_1_1);
    }

    public void setRpGuionIni_1_1(final IAlphanumericValue rpGuionIni_1_1) {
        this.rpParamFechaIni_1R.setRpGuionIni_1_1(rpGuionIni_1_1);
    }

    // Property: rpParamFechaIni_1R.rpFechaIni_1Mm -> 07 RP-FECHA-INI-1-MM 9(02)
    public UnsignedNumericVar getRpFechaIni_1Mm() {
        return this.rpParamFechaIni_1R.getRpFechaIni_1Mm();
    }

    public void setRpFechaIni_1Mm(final int rpFechaIni_1Mm) {
        this.rpParamFechaIni_1R.setRpFechaIni_1Mm(rpFechaIni_1Mm);
    }

    public void setRpFechaIni_1Mm(final INumericValue rpFechaIni_1Mm) {
        this.rpParamFechaIni_1R.setRpFechaIni_1Mm(rpFechaIni_1Mm);
    }

    // Property: rpParamFechaIni_1R : condition FECHA-INI-1-MM-OK
    public boolean isFechaIni_1MmOk() {
        return this.rpParamFechaIni_1R.isFechaIni_1MmOk();
    }

    public void setFechaIni_1MmOk() {
        this.rpParamFechaIni_1R.setFechaIni_1MmOk();
    }

    // Property: rpParamFechaIni_1R.rpGuionIni_1_2 -> 07 RP-GUION-INI-1-2 X(01)
    public AlphanumericVar getRpGuionIni_1_2() {
        return this.rpParamFechaIni_1R.getRpGuionIni_1_2();
    }

    public void setRpGuionIni_1_2(final String rpGuionIni_1_2) {
        this.rpParamFechaIni_1R.setRpGuionIni_1_2(rpGuionIni_1_2);
    }

    public void setRpGuionIni_1_2(final IAlphanumericValue rpGuionIni_1_2) {
        this.rpParamFechaIni_1R.setRpGuionIni_1_2(rpGuionIni_1_2);
    }

    // Property: rpParamFechaIni_1R.rpFechaIni_1Dd -> 07 RP-FECHA-INI-1-DD 9(02)
    public UnsignedNumericVar getRpFechaIni_1Dd() {
        return this.rpParamFechaIni_1R.getRpFechaIni_1Dd();
    }

    public void setRpFechaIni_1Dd(final int rpFechaIni_1Dd) {
        this.rpParamFechaIni_1R.setRpFechaIni_1Dd(rpFechaIni_1Dd);
    }

    public void setRpFechaIni_1Dd(final INumericValue rpFechaIni_1Dd) {
        this.rpParamFechaIni_1R.setRpFechaIni_1Dd(rpFechaIni_1Dd);
    }

    // Property: rpParamFechaIni_1R : condition FECHA-INI-1-DD-OK
    public boolean isFechaIni_1DdOk() {
        return this.rpParamFechaIni_1R.isFechaIni_1DdOk();
    }

    public void setFechaIni_1DdOk() {
        this.rpParamFechaIni_1R.setFechaIni_1DdOk();
    }


    // Deeper properties

    // Property: rpParamFechaFin_1R.rpFechaFin_1Aaaa -> 07 RP-FECHA-FIN-1-AAAA 9(04)
    public UnsignedNumericVar getRpFechaFin_1Aaaa() {
        return this.rpParamFechaFin_1R.getRpFechaFin_1Aaaa();
    }

    public void setRpFechaFin_1Aaaa(final int rpFechaFin_1Aaaa) {
        this.rpParamFechaFin_1R.setRpFechaFin_1Aaaa(rpFechaFin_1Aaaa);
    }

    public void setRpFechaFin_1Aaaa(final INumericValue rpFechaFin_1Aaaa) {
        this.rpParamFechaFin_1R.setRpFechaFin_1Aaaa(rpFechaFin_1Aaaa);
    }

    // Property: rpParamFechaFin_1R : condition FECHA-FIN-1-AAAA-OK
    public boolean isFechaFin_1AaaaOk() {
        return this.rpParamFechaFin_1R.isFechaFin_1AaaaOk();
    }

    public void setFechaFin_1AaaaOk() {
        this.rpParamFechaFin_1R.setFechaFin_1AaaaOk();
    }

    // Property: rpParamFechaFin_1R.rpGuionFin_1_1 -> 07 RP-GUION-FIN-1-1 X(01)
    public AlphanumericVar getRpGuionFin_1_1() {
        return this.rpParamFechaFin_1R.getRpGuionFin_1_1();
    }

    public void setRpGuionFin_1_1(final String rpGuionFin_1_1) {
        this.rpParamFechaFin_1R.setRpGuionFin_1_1(rpGuionFin_1_1);
    }

    public void setRpGuionFin_1_1(final IAlphanumericValue rpGuionFin_1_1) {
        this.rpParamFechaFin_1R.setRpGuionFin_1_1(rpGuionFin_1_1);
    }

    // Property: rpParamFechaFin_1R.rpFechaFin_1Mm -> 07 RP-FECHA-FIN-1-MM 9(02)
    public UnsignedNumericVar getRpFechaFin_1Mm() {
        return this.rpParamFechaFin_1R.getRpFechaFin_1Mm();
    }

    public void setRpFechaFin_1Mm(final int rpFechaFin_1Mm) {
        this.rpParamFechaFin_1R.setRpFechaFin_1Mm(rpFechaFin_1Mm);
    }

    public void setRpFechaFin_1Mm(final INumericValue rpFechaFin_1Mm) {
        this.rpParamFechaFin_1R.setRpFechaFin_1Mm(rpFechaFin_1Mm);
    }

    // Property: rpParamFechaFin_1R : condition FECHA-FIN-1-MM-OK
    public boolean isFechaFin_1MmOk() {
        return this.rpParamFechaFin_1R.isFechaFin_1MmOk();
    }

    public void setFechaFin_1MmOk() {
        this.rpParamFechaFin_1R.setFechaFin_1MmOk();
    }

    // Property: rpParamFechaFin_1R.rpGuionFin_1_2 -> 07 RP-GUION-FIN-1-2 X(01)
    public AlphanumericVar getRpGuionFin_1_2() {
        return this.rpParamFechaFin_1R.getRpGuionFin_1_2();
    }

    public void setRpGuionFin_1_2(final String rpGuionFin_1_2) {
        this.rpParamFechaFin_1R.setRpGuionFin_1_2(rpGuionFin_1_2);
    }

    public void setRpGuionFin_1_2(final IAlphanumericValue rpGuionFin_1_2) {
        this.rpParamFechaFin_1R.setRpGuionFin_1_2(rpGuionFin_1_2);
    }

    // Property: rpParamFechaFin_1R.rpFechaFin_1Dd -> 07 RP-FECHA-FIN-1-DD 9(02)
    public UnsignedNumericVar getRpFechaFin_1Dd() {
        return this.rpParamFechaFin_1R.getRpFechaFin_1Dd();
    }

    public void setRpFechaFin_1Dd(final int rpFechaFin_1Dd) {
        this.rpParamFechaFin_1R.setRpFechaFin_1Dd(rpFechaFin_1Dd);
    }

    public void setRpFechaFin_1Dd(final INumericValue rpFechaFin_1Dd) {
        this.rpParamFechaFin_1R.setRpFechaFin_1Dd(rpFechaFin_1Dd);
    }

    // Property: rpParamFechaFin_1R : condition FECHA-FIN-1-DD-OK
    public boolean isFechaFin_1DdOk() {
        return this.rpParamFechaFin_1R.isFechaFin_1DdOk();
    }

    public void setFechaFin_1DdOk() {
        this.rpParamFechaFin_1R.setFechaFin_1DdOk();
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpParamOpcion = "";
        this.rpParamFechaIni_1.clear();
        this.rpParamFechaFin_1.clear();
        this.rpResto = "";
    }

}

