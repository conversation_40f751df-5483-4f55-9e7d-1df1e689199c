package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.tablaexcagt;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TAB-EXC-AGT.
 *
 */
@Setter
@Getter
public class TabExcAgt extends DataStructArray<TabExcAgt> {
    // Properties
    private UnsignedNumericVar tbAgente = new UnsignedNumericVar(this, 5, 0);                // 07 TB-AGENTE 9(05)
    private AlphanumericVar tbIndExcAgt = new AlphanumericVar(this, 1);      // 07 TB-IND-EXC-AGT X(01)

    public TabExcAgt(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TabExcAgt(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TabExcAgt(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TabExcAgt(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TabExcAgt at(int idx) {
        return new TabExcAgt(this, idx, getSizeElement());
    }

    public void setTbAgente(final int tbAgente) {
        this.tbAgente.setValue(tbAgente);
    }

    public void setTbAgente(final BigDecimal tbAgente) {
        this.tbAgente.setValue(tbAgente);
    }

    public void setTbAgente(final INumericValue tbAgente) {
        this.tbAgente.setValue(tbAgente);
    }

    public void setTbIndExcAgt(final String tbIndExcAgt) {
        this.tbIndExcAgt.setValue(tbIndExcAgt);
    }

    public void setTbIndExcAgt(final IAlphanumericValue tbIndExcAgt) {
        this.tbIndExcAgt.setValue(tbIndExcAgt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbAgente.clear();
        this.tbIndExcAgt.clear();
    }

}

