package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre.WsAuxAPat;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre.WsAuxAMat;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre.WsAuxLasName;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-NOMBRE.
 *
 */
@Setter
@Getter
public class WsAuxNombre { // VStruct
    // Properties
    private WsAuxAPat wsAuxAPat = new  WsAuxAPat();                          // 10 WS-AUX-A-PAT
    private WsAuxAMat wsAuxAMat = new  WsAuxAMat();                          // 10 WS-AUX-A-MAT
    private WsAuxLasName wsAuxLasName = new  WsAuxLasName();                 // 10 WS-AUX-LAS-NAME UsedAsParameter

    public WsAuxNombre() {
        initialize();
    }




    // Deeper properties

    // Property: wsAuxAPat.wsAuxApPat -> 15 WS-AUX-AP-PAT X(01) [40]
    public AlphanumericVarArray getWsAuxApPat() {
        return this.wsAuxAPat.getWsAuxApPat();
    }

    public void setWsAuxApPat(final String wsAuxApPat) {
        this.wsAuxAPat.setWsAuxApPat(wsAuxApPat);
    }

    public void setWsAuxApPat(final AlphanumericVarArray wsAuxApPat) {
        this.wsAuxAPat.setWsAuxApPat(wsAuxApPat);
    }


    // Deeper properties

    // Property: wsAuxAMat.wsAuxApMat -> 15 WS-AUX-AP-MAT X(01) [30]
    public AlphanumericVarArray getWsAuxApMat() {
        return this.wsAuxAMat.getWsAuxApMat();
    }

    public void setWsAuxApMat(final String wsAuxApMat) {
        this.wsAuxAMat.setWsAuxApMat(wsAuxApMat);
    }

    public void setWsAuxApMat(final AlphanumericVarArray wsAuxApMat) {
        this.wsAuxAMat.setWsAuxApMat(wsAuxApMat);
    }


    // Deeper properties

    // Property: wsAuxLasName.wsAuxLastName -> 15 WS-AUX-LAST-NAME X(01) [40]
    public AlphanumericVarArray getWsAuxLastName() {
        return this.wsAuxLasName.getWsAuxLastName();
    }

    public void setWsAuxLastName(final String wsAuxLastName) {
        this.wsAuxLasName.setWsAuxLastName(wsAuxLastName);
    }

    public void setWsAuxLastName(final AlphanumericVarArray wsAuxLastName) {
        this.wsAuxLasName.setWsAuxLastName(wsAuxLastName);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsAuxAPat.initialize();
        this.wsAuxAMat.initialize();
        this.wsAuxLasName.initialize();
    }

}

