package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TAB-PK-NOMBRER.
 *
 */
@Setter
@Getter
public class WsTabPkNombrer extends DataStruct {
    // Properties
    private AlphanumericVarArray elePkNombre = new AlphanumericVarArray(this, 35, 1);        // 05 ELE-PK-NOMBRE X [35]

    public WsTabPkNombrer() {
        super();
        initialize();
    }

    public WsTabPkNombrer(DataContainer parent) {
        super(parent);
    }

    public WsTabPkNombrer(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getElePkNombre(int index) {
        return this.elePkNombre.at(index);
    }

    public void setElePkNombre(final String elePkNombre) {
        this.elePkNombre.setValue(elePkNombre);
    }

    public void setElePkNombre(final AlphanumericVarArray elePkNombre) {
        this.elePkNombre.setValue(elePkNombre);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.elePkNombre.clear();
    }

}

