package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: SW-BANDERAS.
 *
 */
@Setter
@Getter
public class SwBanderas { // VStruct
    // Properties
    private String swVsam = "N";                                            // 05 SW-VSAM X(01)
    // Level 88 - Conditional names
    private static final String SW_VSAM_NO = "N";
    private static final String SW_VSAM_SI = "S";


    public SwBanderas() {
        initialize();
    }



    // Conditionals (88) - SW-VSAM
    public boolean isSwVsamNo() {
        return this.swVsam.equals(SW_VSAM_NO);
    }

    public void setSwVsamNo() {
        this.swVsam = SW_VSAM_NO;
    }

    public boolean isSwVsamSi() {
        return this.swVsam.equals(SW_VSAM_SI);
    }

    public void setSwVsamSi() {
        this.swVsam = SW_VSAM_SI;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.swVsam = "N";
    }

}

