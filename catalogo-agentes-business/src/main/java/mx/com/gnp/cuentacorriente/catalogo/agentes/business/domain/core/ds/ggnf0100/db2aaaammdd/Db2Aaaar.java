package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.db2aaaammdd;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DB2-AAAAR.
 *
 */
@Setter
@Getter
public class Db2Aaaar extends DataStruct {
    // Properties
    private UnsignedNumericVar db2Aa1 = new UnsignedNumericVar(this, 2, 0);  // 05 DB2-AA1 99
    private UnsignedNumericVar db2Aa2 = new UnsignedNumericVar(this, 2, 0);  // 05 DB2-AA2 99

    public Db2Aaaar() {
        super();
        initialize();
    }

    public Db2Aaaar(DataContainer parent) {
        super(parent);
    }

    public Db2Aaaar(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setDb2Aa1(final int db2Aa1) {
        this.db2Aa1.setValue(db2Aa1);
    }

    public void setDb2Aa1(final BigDecimal db2Aa1) {
        this.db2Aa1.setValue(db2Aa1);
    }

    public void setDb2Aa1(final INumericValue db2Aa1) {
        this.db2Aa1.setValue(db2Aa1);
    }

    public void setDb2Aa2(final int db2Aa2) {
        this.db2Aa2.setValue(db2Aa2);
    }

    public void setDb2Aa2(final BigDecimal db2Aa2) {
        this.db2Aa2.setValue(db2Aa2);
    }

    public void setDb2Aa2(final INumericValue db2Aa2) {
        this.db2Aa2.setValue(db2Aa2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.db2Aa1.clear();
        this.db2Aa2.clear();
    }

}

