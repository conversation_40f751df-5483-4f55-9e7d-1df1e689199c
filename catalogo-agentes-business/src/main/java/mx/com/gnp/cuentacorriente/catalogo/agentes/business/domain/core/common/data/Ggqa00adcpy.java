package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.PlhcMktHeader;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.plhcmktheader.PlhcMktCompany;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.PlhcMktDetail;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.PlhcMktTrailer;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.plhcmkttrailer.PlhcMktCompanyT;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: QA$CBLCPY$GGQA00AD.
 *
 */
@Setter
@Getter
public class Ggqa00adcpy extends DataStruct {
    // Properties
    private PlhcMktHeader plhcMktHeader = new  PlhcMktHeader(this);          // 05 PLHC-MKT-HEADER
    private PlhcMktDetail plhcMktDetail = new  PlhcMktDetail(this);          // 05 PLHC-MKT-DETAIL
    private PlhcMktTrailer plhcMktTrailer = new  PlhcMktTrailer(this);       // 05 PLHC-MKT-TRAILER

    public Ggqa00adcpy() {
        super();
        initialize();
    }

    public Ggqa00adcpy(DataContainer parent) {
        super(parent);
    }

    public Ggqa00adcpy(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }



    // Deeper properties

    // Property: plhcMktHeader.plhcMktRecordIdh -> 10 PLHC-MKT-RECORD-IDH X(05)
    public AlphanumericVar getPlhcMktRecordIdh() {
        return this.plhcMktHeader.getPlhcMktRecordIdh();
    }

    public void setPlhcMktRecordIdh(final String plhcMktRecordIdh) {
        this.plhcMktHeader.setPlhcMktRecordIdh(plhcMktRecordIdh);
    }

    public void setPlhcMktRecordIdh(final IAlphanumericValue plhcMktRecordIdh) {
        this.plhcMktHeader.setPlhcMktRecordIdh(plhcMktRecordIdh);
    }

    // Property: plhcMktHeader.plhcMktCompany -> 10 PLHC-MKT-COMPANY
    public PlhcMktCompany getPlhcMktCompany() {
        return this.plhcMktHeader.getPlhcMktCompany();
    }

    // Property: plhcMktHeader.plhcMktCompany.plhcMktCompanyId -> 15 PLHC-MKT-COMPANY-ID X(07)
    public AlphanumericVar getPlhcMktCompanyId() {
        return this.plhcMktHeader.getPlhcMktCompany().getPlhcMktCompanyId();
    }

    public void setPlhcMktCompanyId(final String plhcMktCompanyId) {
        this.plhcMktHeader.getPlhcMktCompany().setPlhcMktCompanyId(plhcMktCompanyId);
    }

    public void setPlhcMktCompanyId(final IAlphanumericValue plhcMktCompanyId) {
        this.plhcMktHeader.getPlhcMktCompany().setPlhcMktCompanyId(plhcMktCompanyId);
    }

    // Property: plhcMktHeader.plhcMktCompany.filler1 -> 15 FILLER X(02)
    public AlphanumericVar getFiller1() {
        return this.plhcMktHeader.getPlhcMktCompany().getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.plhcMktHeader.getPlhcMktCompany().setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.plhcMktHeader.getPlhcMktCompany().setFiller1(filler1);
    }

    // Property: plhcMktHeader.filler2 -> 10 FILLER X(20)
    public AlphanumericVar getFiller2() {
        return this.plhcMktHeader.getFiller2();
    }

    public void setFiller2(final String filler2) {
        this.plhcMktHeader.setFiller2(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.plhcMktHeader.setFiller2(filler2);
    }

    // Property: plhcMktHeader.plhcMktProcessDt -> 10 PLHC-MKT-PROCESS-DT 9(08)
    public UnsignedNumericVar getPlhcMktProcessDt() {
        return this.plhcMktHeader.getPlhcMktProcessDt();
    }

    public void setPlhcMktProcessDt(final int plhcMktProcessDt) {
        this.plhcMktHeader.setPlhcMktProcessDt(plhcMktProcessDt);
    }

    public void setPlhcMktProcessDt(final INumericValue plhcMktProcessDt) {
        this.plhcMktHeader.setPlhcMktProcessDt(plhcMktProcessDt);
    }

    // Property: plhcMktHeader.plhcMktCreationDt -> 10 PLHC-MKT-CREATION-DT 9(08)
    public UnsignedNumericVar getPlhcMktCreationDt() {
        return this.plhcMktHeader.getPlhcMktCreationDt();
    }

    public void setPlhcMktCreationDt(final int plhcMktCreationDt) {
        this.plhcMktHeader.setPlhcMktCreationDt(plhcMktCreationDt);
    }

    public void setPlhcMktCreationDt(final INumericValue plhcMktCreationDt) {
        this.plhcMktHeader.setPlhcMktCreationDt(plhcMktCreationDt);
    }

    // Property: plhcMktHeader.plhcMktRecordCt -> 10 PLHC-MKT-RECORD-CT 9(06)
    public UnsignedNumericVar getPlhcMktRecordCt() {
        return this.plhcMktHeader.getPlhcMktRecordCt();
    }

    public void setPlhcMktRecordCt(final int plhcMktRecordCt) {
        this.plhcMktHeader.setPlhcMktRecordCt(plhcMktRecordCt);
    }

    public void setPlhcMktRecordCt(final INumericValue plhcMktRecordCt) {
        this.plhcMktHeader.setPlhcMktRecordCt(plhcMktRecordCt);
    }

    // Property: plhcMktHeader.filler3 -> 10 FILLER X(0776)
    public AlphanumericVar getFiller3() {
        return this.plhcMktHeader.getFiller3();
    }

    public void setFiller3(final String filler3) {
        this.plhcMktHeader.setFiller3(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.plhcMktHeader.setFiller3(filler3);
    }

    // Property: plhcMktHeader.plhcMktSortIndh -> 10 PLHC-MKT-SORT-INDH X(0001)
    public AlphanumericVar getPlhcMktSortIndh() {
        return this.plhcMktHeader.getPlhcMktSortIndh();
    }

    public void setPlhcMktSortIndh(final String plhcMktSortIndh) {
        this.plhcMktHeader.setPlhcMktSortIndh(plhcMktSortIndh);
    }

    public void setPlhcMktSortIndh(final IAlphanumericValue plhcMktSortIndh) {
        this.plhcMktHeader.setPlhcMktSortIndh(plhcMktSortIndh);
    }

    // Property: plhcMktHeader.filler4 -> 10 FILLER X(0010)
    public AlphanumericVar getFiller4() {
        return this.plhcMktHeader.getFiller4();
    }

    public void setFiller4(final String filler4) {
        this.plhcMktHeader.setFiller4(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.plhcMktHeader.setFiller4(filler4);
    }

    // Property: plhcMktHeader.plhcMktEndrech -> 10 PLHC-MKT-ENDRECH X(0001)
    public AlphanumericVar getPlhcMktEndrech() {
        return this.plhcMktHeader.getPlhcMktEndrech();
    }

    public void setPlhcMktEndrech(final String plhcMktEndrech) {
        this.plhcMktHeader.setPlhcMktEndrech(plhcMktEndrech);
    }

    public void setPlhcMktEndrech(final IAlphanumericValue plhcMktEndrech) {
        this.plhcMktHeader.setPlhcMktEndrech(plhcMktEndrech);
    }


    // Deeper properties

    // Property: plhcMktDetail.plhcMktCompanyD -> 10 PLHC-MKT-COMPANY-D X(05)
    public AlphanumericVar getPlhcMktCompanyD() {
        return this.plhcMktDetail.getPlhcMktCompanyD();
    }

    public void setPlhcMktCompanyD(final String plhcMktCompanyD) {
        this.plhcMktDetail.setPlhcMktCompanyD(plhcMktCompanyD);
    }

    public void setPlhcMktCompanyD(final IAlphanumericValue plhcMktCompanyD) {
        this.plhcMktDetail.setPlhcMktCompanyD(plhcMktCompanyD);
    }

    // Property: plhcMktDetail.plhcMktAgentId -> 10 PLHC-MKT-AGENT-ID X(05)
    public AlphanumericVar getPlhcMktAgentId() {
        return this.plhcMktDetail.getPlhcMktAgentId();
    }

    public void setPlhcMktAgentId(final String plhcMktAgentId) {
        this.plhcMktDetail.setPlhcMktAgentId(plhcMktAgentId);
    }

    public void setPlhcMktAgentId(final IAlphanumericValue plhcMktAgentId) {
        this.plhcMktDetail.setPlhcMktAgentId(plhcMktAgentId);
    }

    // Property: plhcMktDetail.filler5 -> 10 FILLER X(04)
    public AlphanumericVar getFiller5() {
        return this.plhcMktDetail.getFiller5();
    }

    public void setFiller5(final String filler5) {
        this.plhcMktDetail.setFiller5(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.plhcMktDetail.setFiller5(filler5);
    }

    // Property: plhcMktDetail.plhcMktAgencyId -> 10 PLHC-MKT-AGENCY-ID X(03)
    public AlphanumericVar getPlhcMktAgencyId() {
        return this.plhcMktDetail.getPlhcMktAgencyId();
    }

    public void setPlhcMktAgencyId(final String plhcMktAgencyId) {
        this.plhcMktDetail.setPlhcMktAgencyId(plhcMktAgencyId);
    }

    public void setPlhcMktAgencyId(final IAlphanumericValue plhcMktAgencyId) {
        this.plhcMktDetail.setPlhcMktAgencyId(plhcMktAgencyId);
    }

    // Property: plhcMktDetail.filler6 -> 10 FILLER X(06)
    public AlphanumericVar getFiller6() {
        return this.plhcMktDetail.getFiller6();
    }

    public void setFiller6(final String filler6) {
        this.plhcMktDetail.setFiller6(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.plhcMktDetail.setFiller6(filler6);
    }

    // Property: plhcMktDetail.plhcMktSsn -> 10 PLHC-MKT-SSN X(13)
    public AlphanumericVar getPlhcMktSsn() {
        return this.plhcMktDetail.getPlhcMktSsn();
    }

    public void setPlhcMktSsn(final String plhcMktSsn) {
        this.plhcMktDetail.setPlhcMktSsn(plhcMktSsn);
    }

    public void setPlhcMktSsn(final IAlphanumericValue plhcMktSsn) {
        this.plhcMktDetail.setPlhcMktSsn(plhcMktSsn);
    }

    // Property: plhcMktDetail.plhcMktNasd -> 10 PLHC-MKT-NASD X(10)
    public AlphanumericVar getPlhcMktNasd() {
        return this.plhcMktDetail.getPlhcMktNasd();
    }

    public void setPlhcMktNasd(final String plhcMktNasd) {
        this.plhcMktDetail.setPlhcMktNasd(plhcMktNasd);
    }

    public void setPlhcMktNasd(final IAlphanumericValue plhcMktNasd) {
        this.plhcMktDetail.setPlhcMktNasd(plhcMktNasd);
    }

    // Property: plhcMktDetail.plhcMktSex -> 10 PLHC-MKT-SEX X(01)
    public AlphanumericVar getPlhcMktSex() {
        return this.plhcMktDetail.getPlhcMktSex();
    }

    public void setPlhcMktSex(final String plhcMktSex) {
        this.plhcMktDetail.setPlhcMktSex(plhcMktSex);
    }

    public void setPlhcMktSex(final IAlphanumericValue plhcMktSex) {
        this.plhcMktDetail.setPlhcMktSex(plhcMktSex);
    }

    // Property: plhcMktDetail.plhcMktHireDate -> 10 PLHC-MKT-HIRE-DATE 9(08)
    public UnsignedNumericVar getPlhcMktHireDate() {
        return this.plhcMktDetail.getPlhcMktHireDate();
    }

    public void setPlhcMktHireDate(final int plhcMktHireDate) {
        this.plhcMktDetail.setPlhcMktHireDate(plhcMktHireDate);
    }

    public void setPlhcMktHireDate(final INumericValue plhcMktHireDate) {
        this.plhcMktDetail.setPlhcMktHireDate(plhcMktHireDate);
    }

    // Property: plhcMktDetail.plhcMktStatus -> 10 PLHC-MKT-STATUS X(01)
    public AlphanumericVar getPlhcMktStatus() {
        return this.plhcMktDetail.getPlhcMktStatus();
    }

    public void setPlhcMktStatus(final String plhcMktStatus) {
        this.plhcMktDetail.setPlhcMktStatus(plhcMktStatus);
    }

    public void setPlhcMktStatus(final IAlphanumericValue plhcMktStatus) {
        this.plhcMktDetail.setPlhcMktStatus(plhcMktStatus);
    }

    // Property: plhcMktDetail.plhcMktTermDate -> 10 PLHC-MKT-TERM-DATE 9(08)
    public UnsignedNumericVar getPlhcMktTermDate() {
        return this.plhcMktDetail.getPlhcMktTermDate();
    }

    public void setPlhcMktTermDate(final int plhcMktTermDate) {
        this.plhcMktDetail.setPlhcMktTermDate(plhcMktTermDate);
    }

    public void setPlhcMktTermDate(final INumericValue plhcMktTermDate) {
        this.plhcMktDetail.setPlhcMktTermDate(plhcMktTermDate);
    }

    // Property: plhcMktDetail.plhcMktDob -> 10 PLHC-MKT-DOB 9(08)
    public UnsignedNumericVar getPlhcMktDob() {
        return this.plhcMktDetail.getPlhcMktDob();
    }

    public void setPlhcMktDob(final int plhcMktDob) {
        this.plhcMktDetail.setPlhcMktDob(plhcMktDob);
    }

    public void setPlhcMktDob(final INumericValue plhcMktDob) {
        this.plhcMktDetail.setPlhcMktDob(plhcMktDob);
    }

    // Property: plhcMktDetail.plhcMktCorpName -> 10 PLHC-MKT-CORP-NAME X(60)
    public AlphanumericVar getPlhcMktCorpName() {
        return this.plhcMktDetail.getPlhcMktCorpName();
    }

    public void setPlhcMktCorpName(final String plhcMktCorpName) {
        this.plhcMktDetail.setPlhcMktCorpName(plhcMktCorpName);
    }

    public void setPlhcMktCorpName(final IAlphanumericValue plhcMktCorpName) {
        this.plhcMktDetail.setPlhcMktCorpName(plhcMktCorpName);
    }

    // Property: plhcMktDetail.plhcMktNameLast -> 10 PLHC-MKT-NAME-LAST X(40)
    public AlphanumericVar getPlhcMktNameLast() {
        return this.plhcMktDetail.getPlhcMktNameLast();
    }

    public void setPlhcMktNameLast(final String plhcMktNameLast) {
        this.plhcMktDetail.setPlhcMktNameLast(plhcMktNameLast);
    }

    public void setPlhcMktNameLast(final IAlphanumericValue plhcMktNameLast) {
        this.plhcMktDetail.setPlhcMktNameLast(plhcMktNameLast);
    }

    // Property: plhcMktDetail.plhcMktNameFirst -> 10 PLHC-MKT-NAME-FIRST X(20)
    public AlphanumericVar getPlhcMktNameFirst() {
        return this.plhcMktDetail.getPlhcMktNameFirst();
    }

    public void setPlhcMktNameFirst(final String plhcMktNameFirst) {
        this.plhcMktDetail.setPlhcMktNameFirst(plhcMktNameFirst);
    }

    public void setPlhcMktNameFirst(final IAlphanumericValue plhcMktNameFirst) {
        this.plhcMktDetail.setPlhcMktNameFirst(plhcMktNameFirst);
    }

    // Property: plhcMktDetail.plhcMktAddressOne -> 10 PLHC-MKT-ADDRESS-ONE X(35)
    public AlphanumericVar getPlhcMktAddressOne() {
        return this.plhcMktDetail.getPlhcMktAddressOne();
    }

    public void setPlhcMktAddressOne(final String plhcMktAddressOne) {
        this.plhcMktDetail.setPlhcMktAddressOne(plhcMktAddressOne);
    }

    public void setPlhcMktAddressOne(final IAlphanumericValue plhcMktAddressOne) {
        this.plhcMktDetail.setPlhcMktAddressOne(plhcMktAddressOne);
    }

    // Property: plhcMktDetail.plhcMktAddressTwo -> 10 PLHC-MKT-ADDRESS-TWO X(35)
    public AlphanumericVar getPlhcMktAddressTwo() {
        return this.plhcMktDetail.getPlhcMktAddressTwo();
    }

    public void setPlhcMktAddressTwo(final String plhcMktAddressTwo) {
        this.plhcMktDetail.setPlhcMktAddressTwo(plhcMktAddressTwo);
    }

    public void setPlhcMktAddressTwo(final IAlphanumericValue plhcMktAddressTwo) {
        this.plhcMktDetail.setPlhcMktAddressTwo(plhcMktAddressTwo);
    }

    // Property: plhcMktDetail.plhcMktAddressThree -> 10 PLHC-MKT-ADDRESS-THREE X(35)
    public AlphanumericVar getPlhcMktAddressThree() {
        return this.plhcMktDetail.getPlhcMktAddressThree();
    }

    public void setPlhcMktAddressThree(final String plhcMktAddressThree) {
        this.plhcMktDetail.setPlhcMktAddressThree(plhcMktAddressThree);
    }

    public void setPlhcMktAddressThree(final IAlphanumericValue plhcMktAddressThree) {
        this.plhcMktDetail.setPlhcMktAddressThree(plhcMktAddressThree);
    }

    // Property: plhcMktDetail.plhcMktAddressFour -> 10 PLHC-MKT-ADDRESS-FOUR X(35)
    public AlphanumericVar getPlhcMktAddressFour() {
        return this.plhcMktDetail.getPlhcMktAddressFour();
    }

    public void setPlhcMktAddressFour(final String plhcMktAddressFour) {
        this.plhcMktDetail.setPlhcMktAddressFour(plhcMktAddressFour);
    }

    public void setPlhcMktAddressFour(final IAlphanumericValue plhcMktAddressFour) {
        this.plhcMktDetail.setPlhcMktAddressFour(plhcMktAddressFour);
    }

    // Property: plhcMktDetail.plhcMktAddressState -> 10 PLHC-MKT-ADDRESS-STATE X(03)
    public AlphanumericVar getPlhcMktAddressState() {
        return this.plhcMktDetail.getPlhcMktAddressState();
    }

    public void setPlhcMktAddressState(final String plhcMktAddressState) {
        this.plhcMktDetail.setPlhcMktAddressState(plhcMktAddressState);
    }

    public void setPlhcMktAddressState(final IAlphanumericValue plhcMktAddressState) {
        this.plhcMktDetail.setPlhcMktAddressState(plhcMktAddressState);
    }

    // Property: plhcMktDetail.plhcMktZipFive -> 10 PLHC-MKT-ZIP-FIVE X(05)
    public AlphanumericVar getPlhcMktZipFive() {
        return this.plhcMktDetail.getPlhcMktZipFive();
    }

    public void setPlhcMktZipFive(final String plhcMktZipFive) {
        this.plhcMktDetail.setPlhcMktZipFive(plhcMktZipFive);
    }

    public void setPlhcMktZipFive(final IAlphanumericValue plhcMktZipFive) {
        this.plhcMktDetail.setPlhcMktZipFive(plhcMktZipFive);
    }

    // Property: plhcMktDetail.plhcMktPdcFive -> 10 PLHC-MKT-PDC-FIVE X(05)
    public AlphanumericVar getPlhcMktPdcFive() {
        return this.plhcMktDetail.getPlhcMktPdcFive();
    }

    public void setPlhcMktPdcFive(final String plhcMktPdcFive) {
        this.plhcMktDetail.setPlhcMktPdcFive(plhcMktPdcFive);
    }

    public void setPlhcMktPdcFive(final IAlphanumericValue plhcMktPdcFive) {
        this.plhcMktDetail.setPlhcMktPdcFive(plhcMktPdcFive);
    }

    // Property: plhcMktDetail.plhcMktPhone -> 10 PLHC-MKT-PHONE X(14)
    public AlphanumericVar getPlhcMktPhone() {
        return this.plhcMktDetail.getPlhcMktPhone();
    }

    public void setPlhcMktPhone(final String plhcMktPhone) {
        this.plhcMktDetail.setPlhcMktPhone(plhcMktPhone);
    }

    public void setPlhcMktPhone(final IAlphanumericValue plhcMktPhone) {
        this.plhcMktDetail.setPlhcMktPhone(plhcMktPhone);
    }

    // Property: plhcMktDetail.plhcMktCorpNamea -> 10 PLHC-MKT-CORP-NAMEA X(60)
    public AlphanumericVar getPlhcMktCorpNamea() {
        return this.plhcMktDetail.getPlhcMktCorpNamea();
    }

    public void setPlhcMktCorpNamea(final String plhcMktCorpNamea) {
        this.plhcMktDetail.setPlhcMktCorpNamea(plhcMktCorpNamea);
    }

    public void setPlhcMktCorpNamea(final IAlphanumericValue plhcMktCorpNamea) {
        this.plhcMktDetail.setPlhcMktCorpNamea(plhcMktCorpNamea);
    }

    // Property: plhcMktDetail.plhcMktNameLasta -> 10 PLHC-MKT-NAME-LASTA X(40)
    public AlphanumericVar getPlhcMktNameLasta() {
        return this.plhcMktDetail.getPlhcMktNameLasta();
    }

    public void setPlhcMktNameLasta(final String plhcMktNameLasta) {
        this.plhcMktDetail.setPlhcMktNameLasta(plhcMktNameLasta);
    }

    public void setPlhcMktNameLasta(final IAlphanumericValue plhcMktNameLasta) {
        this.plhcMktDetail.setPlhcMktNameLasta(plhcMktNameLasta);
    }

    // Property: plhcMktDetail.plhcMktNameFirsta -> 10 PLHC-MKT-NAME-FIRSTA X(20)
    public AlphanumericVar getPlhcMktNameFirsta() {
        return this.plhcMktDetail.getPlhcMktNameFirsta();
    }

    public void setPlhcMktNameFirsta(final String plhcMktNameFirsta) {
        this.plhcMktDetail.setPlhcMktNameFirsta(plhcMktNameFirsta);
    }

    public void setPlhcMktNameFirsta(final IAlphanumericValue plhcMktNameFirsta) {
        this.plhcMktDetail.setPlhcMktNameFirsta(plhcMktNameFirsta);
    }

    // Property: plhcMktDetail.plhcMktAddressOnea -> 10 PLHC-MKT-ADDRESS-ONEA X(35)
    public AlphanumericVar getPlhcMktAddressOnea() {
        return this.plhcMktDetail.getPlhcMktAddressOnea();
    }

    public void setPlhcMktAddressOnea(final String plhcMktAddressOnea) {
        this.plhcMktDetail.setPlhcMktAddressOnea(plhcMktAddressOnea);
    }

    public void setPlhcMktAddressOnea(final IAlphanumericValue plhcMktAddressOnea) {
        this.plhcMktDetail.setPlhcMktAddressOnea(plhcMktAddressOnea);
    }

    // Property: plhcMktDetail.plhcMktAddressTwoa -> 10 PLHC-MKT-ADDRESS-TWOA X(35)
    public AlphanumericVar getPlhcMktAddressTwoa() {
        return this.plhcMktDetail.getPlhcMktAddressTwoa();
    }

    public void setPlhcMktAddressTwoa(final String plhcMktAddressTwoa) {
        this.plhcMktDetail.setPlhcMktAddressTwoa(plhcMktAddressTwoa);
    }

    public void setPlhcMktAddressTwoa(final IAlphanumericValue plhcMktAddressTwoa) {
        this.plhcMktDetail.setPlhcMktAddressTwoa(plhcMktAddressTwoa);
    }

    // Property: plhcMktDetail.plhcMktAddressThreea -> 10 PLHC-MKT-ADDRESS-THREEA X(35)
    public AlphanumericVar getPlhcMktAddressThreea() {
        return this.plhcMktDetail.getPlhcMktAddressThreea();
    }

    public void setPlhcMktAddressThreea(final String plhcMktAddressThreea) {
        this.plhcMktDetail.setPlhcMktAddressThreea(plhcMktAddressThreea);
    }

    public void setPlhcMktAddressThreea(final IAlphanumericValue plhcMktAddressThreea) {
        this.plhcMktDetail.setPlhcMktAddressThreea(plhcMktAddressThreea);
    }

    // Property: plhcMktDetail.plhcMktAddressFoura -> 10 PLHC-MKT-ADDRESS-FOURA X(35)
    public AlphanumericVar getPlhcMktAddressFoura() {
        return this.plhcMktDetail.getPlhcMktAddressFoura();
    }

    public void setPlhcMktAddressFoura(final String plhcMktAddressFoura) {
        this.plhcMktDetail.setPlhcMktAddressFoura(plhcMktAddressFoura);
    }

    public void setPlhcMktAddressFoura(final IAlphanumericValue plhcMktAddressFoura) {
        this.plhcMktDetail.setPlhcMktAddressFoura(plhcMktAddressFoura);
    }

    // Property: plhcMktDetail.plhcMktAddressStatea -> 10 PLHC-MKT-ADDRESS-STATEA X(03)
    public AlphanumericVar getPlhcMktAddressStatea() {
        return this.plhcMktDetail.getPlhcMktAddressStatea();
    }

    public void setPlhcMktAddressStatea(final String plhcMktAddressStatea) {
        this.plhcMktDetail.setPlhcMktAddressStatea(plhcMktAddressStatea);
    }

    public void setPlhcMktAddressStatea(final IAlphanumericValue plhcMktAddressStatea) {
        this.plhcMktDetail.setPlhcMktAddressStatea(plhcMktAddressStatea);
    }

    // Property: plhcMktDetail.plhcMktZipFivea -> 10 PLHC-MKT-ZIP-FIVEA X(05)
    public AlphanumericVar getPlhcMktZipFivea() {
        return this.plhcMktDetail.getPlhcMktZipFivea();
    }

    public void setPlhcMktZipFivea(final String plhcMktZipFivea) {
        this.plhcMktDetail.setPlhcMktZipFivea(plhcMktZipFivea);
    }

    public void setPlhcMktZipFivea(final IAlphanumericValue plhcMktZipFivea) {
        this.plhcMktDetail.setPlhcMktZipFivea(plhcMktZipFivea);
    }

    // Property: plhcMktDetail.plhcMktPdcFivea -> 10 PLHC-MKT-PDC-FIVEA X(05)
    public AlphanumericVar getPlhcMktPdcFivea() {
        return this.plhcMktDetail.getPlhcMktPdcFivea();
    }

    public void setPlhcMktPdcFivea(final String plhcMktPdcFivea) {
        this.plhcMktDetail.setPlhcMktPdcFivea(plhcMktPdcFivea);
    }

    public void setPlhcMktPdcFivea(final IAlphanumericValue plhcMktPdcFivea) {
        this.plhcMktDetail.setPlhcMktPdcFivea(plhcMktPdcFivea);
    }

    // Property: plhcMktDetail.plhcMktPhonea -> 10 PLHC-MKT-PHONEA X(14)
    public AlphanumericVar getPlhcMktPhonea() {
        return this.plhcMktDetail.getPlhcMktPhonea();
    }

    public void setPlhcMktPhonea(final String plhcMktPhonea) {
        this.plhcMktDetail.setPlhcMktPhonea(plhcMktPhonea);
    }

    public void setPlhcMktPhonea(final IAlphanumericValue plhcMktPhonea) {
        this.plhcMktDetail.setPlhcMktPhonea(plhcMktPhonea);
    }

    // Property: plhcMktDetail.plhcMktNasdEffDate -> 10 PLHC-MKT-NASD-EFF-DATE 9(08)
    public UnsignedNumericVar getPlhcMktNasdEffDate() {
        return this.plhcMktDetail.getPlhcMktNasdEffDate();
    }

    public void setPlhcMktNasdEffDate(final int plhcMktNasdEffDate) {
        this.plhcMktDetail.setPlhcMktNasdEffDate(plhcMktNasdEffDate);
    }

    public void setPlhcMktNasdEffDate(final INumericValue plhcMktNasdEffDate) {
        this.plhcMktDetail.setPlhcMktNasdEffDate(plhcMktNasdEffDate);
    }

    // Property: plhcMktDetail.plhcMktNasdRenDate -> 10 PLHC-MKT-NASD-REN-DATE 9(08)
    public UnsignedNumericVar getPlhcMktNasdRenDate() {
        return this.plhcMktDetail.getPlhcMktNasdRenDate();
    }

    public void setPlhcMktNasdRenDate(final int plhcMktNasdRenDate) {
        this.plhcMktDetail.setPlhcMktNasdRenDate(plhcMktNasdRenDate);
    }

    public void setPlhcMktNasdRenDate(final INumericValue plhcMktNasdRenDate) {
        this.plhcMktDetail.setPlhcMktNasdRenDate(plhcMktNasdRenDate);
    }

    // Property: plhcMktDetail.plhcMktLicName -> 10 PLHC-MKT-LIC-NAME X(50)
    public AlphanumericVar getPlhcMktLicName() {
        return this.plhcMktDetail.getPlhcMktLicName();
    }

    public void setPlhcMktLicName(final String plhcMktLicName) {
        this.plhcMktDetail.setPlhcMktLicName(plhcMktLicName);
    }

    public void setPlhcMktLicName(final IAlphanumericValue plhcMktLicName) {
        this.plhcMktDetail.setPlhcMktLicName(plhcMktLicName);
    }

    // Property: plhcMktDetail.plhcMktLevel -> 10 PLHC-MKT-LEVEL X(02)
    public AlphanumericVar getPlhcMktLevel() {
        return this.plhcMktDetail.getPlhcMktLevel();
    }

    public void setPlhcMktLevel(final String plhcMktLevel) {
        this.plhcMktDetail.setPlhcMktLevel(plhcMktLevel);
    }

    public void setPlhcMktLevel(final IAlphanumericValue plhcMktLevel) {
        this.plhcMktDetail.setPlhcMktLevel(plhcMktLevel);
    }

    // Property: plhcMktDetail.plhcMktPayOption -> 10 PLHC-MKT-PAY-OPTION X(05)
    public AlphanumericVar getPlhcMktPayOption() {
        return this.plhcMktDetail.getPlhcMktPayOption();
    }

    public void setPlhcMktPayOption(final String plhcMktPayOption) {
        this.plhcMktDetail.setPlhcMktPayOption(plhcMktPayOption);
    }

    public void setPlhcMktPayOption(final IAlphanumericValue plhcMktPayOption) {
        this.plhcMktDetail.setPlhcMktPayOption(plhcMktPayOption);
    }

    // Property: plhcMktDetail.plhcMktChain -> 10 PLHC-MKT-CHAIN X(10)
    public AlphanumericVar getPlhcMktChain() {
        return this.plhcMktDetail.getPlhcMktChain();
    }

    public void setPlhcMktChain(final String plhcMktChain) {
        this.plhcMktDetail.setPlhcMktChain(plhcMktChain);
    }

    public void setPlhcMktChain(final IAlphanumericValue plhcMktChain) {
        this.plhcMktDetail.setPlhcMktChain(plhcMktChain);
    }

    // Property: plhcMktDetail.plhcMktCommIndex -> 10 PLHC-MKT-COMM-INDEX X(06)
    public AlphanumericVar getPlhcMktCommIndex() {
        return this.plhcMktDetail.getPlhcMktCommIndex();
    }

    public void setPlhcMktCommIndex(final String plhcMktCommIndex) {
        this.plhcMktDetail.setPlhcMktCommIndex(plhcMktCommIndex);
    }

    public void setPlhcMktCommIndex(final IAlphanumericValue plhcMktCommIndex) {
        this.plhcMktDetail.setPlhcMktCommIndex(plhcMktCommIndex);
    }

    // Property: plhcMktDetail.plhcMktPayMethod -> 10 PLHC-MKT-PAY-METHOD X(01)
    public AlphanumericVar getPlhcMktPayMethod() {
        return this.plhcMktDetail.getPlhcMktPayMethod();
    }

    public void setPlhcMktPayMethod(final String plhcMktPayMethod) {
        this.plhcMktDetail.setPlhcMktPayMethod(plhcMktPayMethod);
    }

    public void setPlhcMktPayMethod(final IAlphanumericValue plhcMktPayMethod) {
        this.plhcMktDetail.setPlhcMktPayMethod(plhcMktPayMethod);
    }

    // Property: plhcMktDetail.plhcMktPayFreq -> 10 PLHC-MKT-PAY-FREQ X(02)
    public AlphanumericVar getPlhcMktPayFreq() {
        return this.plhcMktDetail.getPlhcMktPayFreq();
    }

    public void setPlhcMktPayFreq(final String plhcMktPayFreq) {
        this.plhcMktDetail.setPlhcMktPayFreq(plhcMktPayFreq);
    }

    public void setPlhcMktPayFreq(final IAlphanumericValue plhcMktPayFreq) {
        this.plhcMktDetail.setPlhcMktPayFreq(plhcMktPayFreq);
    }

    // Property: plhcMktDetail.filler7 -> 10 FILLER X(78)
    public AlphanumericVar getFiller7() {
        return this.plhcMktDetail.getFiller7();
    }

    public void setFiller7(final String filler7) {
        this.plhcMktDetail.setFiller7(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.plhcMktDetail.setFiller7(filler7);
    }

    // Property: plhcMktDetail.plhcMktUniqueAgentCode -> 10 PLHC-MKT-UNIQUE-AGENT-CODE X(08)
    public AlphanumericVar getPlhcMktUniqueAgentCode() {
        return this.plhcMktDetail.getPlhcMktUniqueAgentCode();
    }

    public void setPlhcMktUniqueAgentCode(final String plhcMktUniqueAgentCode) {
        this.plhcMktDetail.setPlhcMktUniqueAgentCode(plhcMktUniqueAgentCode);
    }

    public void setPlhcMktUniqueAgentCode(final IAlphanumericValue plhcMktUniqueAgentCode) {
        this.plhcMktDetail.setPlhcMktUniqueAgentCode(plhcMktUniqueAgentCode);
    }

    // Property: plhcMktDetail.filler8 -> 10 FILLER X(08)
    public AlphanumericVar getFiller8() {
        return this.plhcMktDetail.getFiller8();
    }

    public void setFiller8(final String filler8) {
        this.plhcMktDetail.setFiller8(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.plhcMktDetail.setFiller8(filler8);
    }

    // Property: plhcMktDetail.plhcMktSortInd -> 10 PLHC-MKT-SORT-IND X(01)
    public AlphanumericVar getPlhcMktSortInd() {
        return this.plhcMktDetail.getPlhcMktSortInd();
    }

    public void setPlhcMktSortInd(final String plhcMktSortInd) {
        this.plhcMktDetail.setPlhcMktSortInd(plhcMktSortInd);
    }

    public void setPlhcMktSortInd(final IAlphanumericValue plhcMktSortInd) {
        this.plhcMktDetail.setPlhcMktSortInd(plhcMktSortInd);
    }

    // Property: plhcMktDetail.filler9 -> 10 FILLER X(10)
    public AlphanumericVar getFiller9() {
        return this.plhcMktDetail.getFiller9();
    }

    public void setFiller9(final String filler9) {
        this.plhcMktDetail.setFiller9(filler9);
    }

    public void setFiller9(final IAlphanumericValue filler9) {
        this.plhcMktDetail.setFiller9(filler9);
    }

    // Property: plhcMktDetail.plhcMktEndrec -> 10 PLHC-MKT-ENDREC X(01)
    public AlphanumericVar getPlhcMktEndrec() {
        return this.plhcMktDetail.getPlhcMktEndrec();
    }

    public void setPlhcMktEndrec(final String plhcMktEndrec) {
        this.plhcMktDetail.setPlhcMktEndrec(plhcMktEndrec);
    }

    public void setPlhcMktEndrec(final IAlphanumericValue plhcMktEndrec) {
        this.plhcMktDetail.setPlhcMktEndrec(plhcMktEndrec);
    }


    // Deeper properties

    // Property: plhcMktTrailer.plhcMktRecordIdt -> 10 PLHC-MKT-RECORD-IDT X(05)
    public AlphanumericVar getPlhcMktRecordIdt() {
        return this.plhcMktTrailer.getPlhcMktRecordIdt();
    }

    public void setPlhcMktRecordIdt(final String plhcMktRecordIdt) {
        this.plhcMktTrailer.setPlhcMktRecordIdt(plhcMktRecordIdt);
    }

    public void setPlhcMktRecordIdt(final IAlphanumericValue plhcMktRecordIdt) {
        this.plhcMktTrailer.setPlhcMktRecordIdt(plhcMktRecordIdt);
    }

    // Property: plhcMktTrailer.plhcMktCompanyT -> 10 PLHC-MKT-COMPANY-T
    public PlhcMktCompanyT getPlhcMktCompanyT() {
        return this.plhcMktTrailer.getPlhcMktCompanyT();
    }

    // Property: plhcMktTrailer.plhcMktCompanyT.plhcMktCompanyIdT -> 15 PLHC-MKT-COMPANY-ID-T X(03)
    public AlphanumericVar getPlhcMktCompanyIdT() {
        return this.plhcMktTrailer.getPlhcMktCompanyT().getPlhcMktCompanyIdT();
    }

    public void setPlhcMktCompanyIdT(final String plhcMktCompanyIdT) {
        this.plhcMktTrailer.getPlhcMktCompanyT().setPlhcMktCompanyIdT(plhcMktCompanyIdT);
    }

    public void setPlhcMktCompanyIdT(final IAlphanumericValue plhcMktCompanyIdT) {
        this.plhcMktTrailer.getPlhcMktCompanyT().setPlhcMktCompanyIdT(plhcMktCompanyIdT);
    }

    // Property: plhcMktTrailer.plhcMktCompanyT.filler10 -> 15 FILLER X(03)
    public AlphanumericVar getFiller10() {
        return this.plhcMktTrailer.getPlhcMktCompanyT().getFiller10();
    }

    public void setFiller10(final String filler10) {
        this.plhcMktTrailer.getPlhcMktCompanyT().setFiller10(filler10);
    }

    public void setFiller10(final IAlphanumericValue filler10) {
        this.plhcMktTrailer.getPlhcMktCompanyT().setFiller10(filler10);
    }

    // Property: plhcMktTrailer.plhcMktRecordCount -> 10 PLHC-MKT-RECORD-COUNT 9(06)
    public UnsignedNumericVar getPlhcMktRecordCount() {
        return this.plhcMktTrailer.getPlhcMktRecordCount();
    }

    public void setPlhcMktRecordCount(final int plhcMktRecordCount) {
        this.plhcMktTrailer.setPlhcMktRecordCount(plhcMktRecordCount);
    }

    public void setPlhcMktRecordCount(final INumericValue plhcMktRecordCount) {
        this.plhcMktTrailer.setPlhcMktRecordCount(plhcMktRecordCount);
    }

    // Property: plhcMktTrailer.filler11 -> 10 FILLER X(815)
    public AlphanumericVar getFiller11() {
        return this.plhcMktTrailer.getFiller11();
    }

    public void setFiller11(final String filler11) {
        this.plhcMktTrailer.setFiller11(filler11);
    }

    public void setFiller11(final IAlphanumericValue filler11) {
        this.plhcMktTrailer.setFiller11(filler11);
    }

    // Property: plhcMktTrailer.plhcMktSortIndt -> 10 PLHC-MKT-SORT-INDT X(0001)
    public AlphanumericVar getPlhcMktSortIndt() {
        return this.plhcMktTrailer.getPlhcMktSortIndt();
    }

    public void setPlhcMktSortIndt(final String plhcMktSortIndt) {
        this.plhcMktTrailer.setPlhcMktSortIndt(plhcMktSortIndt);
    }

    public void setPlhcMktSortIndt(final IAlphanumericValue plhcMktSortIndt) {
        this.plhcMktTrailer.setPlhcMktSortIndt(plhcMktSortIndt);
    }

    // Property: plhcMktTrailer.filler12 -> 10 FILLER X(0010)
    public AlphanumericVar getFiller12() {
        return this.plhcMktTrailer.getFiller12();
    }

    public void setFiller12(final String filler12) {
        this.plhcMktTrailer.setFiller12(filler12);
    }

    public void setFiller12(final IAlphanumericValue filler12) {
        this.plhcMktTrailer.setFiller12(filler12);
    }

    // Property: plhcMktTrailer.plhcMktEndrect -> 10 PLHC-MKT-ENDRECT X(0001)
    public AlphanumericVar getPlhcMktEndrect() {
        return this.plhcMktTrailer.getPlhcMktEndrect();
    }

    public void setPlhcMktEndrect(final String plhcMktEndrect) {
        this.plhcMktTrailer.setPlhcMktEndrect(plhcMktEndrect);
    }

    public void setPlhcMktEndrect(final IAlphanumericValue plhcMktEndrect) {
        this.plhcMktTrailer.setPlhcMktEndrect(plhcMktEndrect);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhcMktHeader.initialize();
        this.plhcMktDetail.initialize();
        this.plhcMktTrailer.initialize();
    }

}

