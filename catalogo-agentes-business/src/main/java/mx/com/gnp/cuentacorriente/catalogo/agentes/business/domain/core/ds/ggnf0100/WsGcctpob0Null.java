package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GCCTPOB0-NULL.
 *
 */
// 1049058400*                                                                 05840000
// 1050058500*                                                                 05850000
@Setter
@Getter
public class WsGcctpob0Null { // VStruct
    // Properties
    private int pob01;                                                      // 03 POB01 S9(4) COMPUTATIONAL
    private int pob02;                                                      // 03 POB02 S9(4) COMPUTATIONAL
    private int pob03;                                                      // 03 POB03 S9(4) COMPUTATIONAL
    private int pob04;                                                      // 03 POB04 S9(4) COMPUTATIONAL

    public WsGcctpob0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.pob01 = 0;
        this.pob02 = 0;
        this.pob03 = 0;
        this.pob04 = 0;
    }

}

