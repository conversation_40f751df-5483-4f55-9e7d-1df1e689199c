package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: W500-FECHA-DB2.
 *
 */
@Setter
@Getter
public class W500FechaDb2 extends DataStruct {
    // Properties
    private UnsignedNumericVar w500AnoDb2 = new UnsignedNumericVar(this, 4, 0);              // 05 W500-ANO-DB2 9(04)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar w500MesDb2 = new UnsignedNumericVar(this, 2, 0);              // 05 W500-MES-DB2 9(02)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar w500DiaDb2 = new UnsignedNumericVar(this, 2, 0);              // 05 W500-DIA-DB2 9(02)

    public W500FechaDb2() {
        super();
        initialize();
    }

    public W500FechaDb2(DataContainer parent) {
        super(parent);
    }

    public W500FechaDb2(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setW500AnoDb2(final int w500AnoDb2) {
        this.w500AnoDb2.setValue(w500AnoDb2);
    }

    public void setW500AnoDb2(final BigDecimal w500AnoDb2) {
        this.w500AnoDb2.setValue(w500AnoDb2);
    }

    public void setW500AnoDb2(final INumericValue w500AnoDb2) {
        this.w500AnoDb2.setValue(w500AnoDb2);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setW500MesDb2(final int w500MesDb2) {
        this.w500MesDb2.setValue(w500MesDb2);
    }

    public void setW500MesDb2(final BigDecimal w500MesDb2) {
        this.w500MesDb2.setValue(w500MesDb2);
    }

    public void setW500MesDb2(final INumericValue w500MesDb2) {
        this.w500MesDb2.setValue(w500MesDb2);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setW500DiaDb2(final int w500DiaDb2) {
        this.w500DiaDb2.setValue(w500DiaDb2);
    }

    public void setW500DiaDb2(final BigDecimal w500DiaDb2) {
        this.w500DiaDb2.setValue(w500DiaDb2);
    }

    public void setW500DiaDb2(final INumericValue w500DiaDb2) {
        this.w500DiaDb2.setValue(w500DiaDb2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.w500AnoDb2.clear();
        this.filler1.setValue("-");
        this.w500MesDb2.clear();
        this.filler2.setValue("-");
        this.w500DiaDb2.clear();
    }

}

