package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.wstimestampr;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FECHA-TIMESTAMP.
 *
 */
@Setter
@Getter
public class WsFechaTimestamp extends DataStruct {
    // Properties
    private UnsignedNumericVar wsAaaaTimestamp = new UnsignedNumericVar(this, 4, 0);         // 05 WS-AAAA-TIMESTAMP 9(04)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar wsMmTimestamp = new UnsignedNumericVar(this, 2, 0);           // 05 WS-MM-TIMESTAMP 9(02)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar wsDdTimestamp = new UnsignedNumericVar(this, 2, 0);           // 05 WS-DD-TIMESTAMP 9(02)

    public WsFechaTimestamp() {
        super();
        initialize();
    }

    public WsFechaTimestamp(DataContainer parent) {
        super(parent);
    }

    public WsFechaTimestamp(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAaaaTimestamp(final int wsAaaaTimestamp) {
        this.wsAaaaTimestamp.setValue(wsAaaaTimestamp);
    }

    public void setWsAaaaTimestamp(final BigDecimal wsAaaaTimestamp) {
        this.wsAaaaTimestamp.setValue(wsAaaaTimestamp);
    }

    public void setWsAaaaTimestamp(final INumericValue wsAaaaTimestamp) {
        this.wsAaaaTimestamp.setValue(wsAaaaTimestamp);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setWsMmTimestamp(final int wsMmTimestamp) {
        this.wsMmTimestamp.setValue(wsMmTimestamp);
    }

    public void setWsMmTimestamp(final BigDecimal wsMmTimestamp) {
        this.wsMmTimestamp.setValue(wsMmTimestamp);
    }

    public void setWsMmTimestamp(final INumericValue wsMmTimestamp) {
        this.wsMmTimestamp.setValue(wsMmTimestamp);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setWsDdTimestamp(final int wsDdTimestamp) {
        this.wsDdTimestamp.setValue(wsDdTimestamp);
    }

    public void setWsDdTimestamp(final BigDecimal wsDdTimestamp) {
        this.wsDdTimestamp.setValue(wsDdTimestamp);
    }

    public void setWsDdTimestamp(final INumericValue wsDdTimestamp) {
        this.wsDdTimestamp.setValue(wsDdTimestamp);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAaaaTimestamp.clear();
        this.filler1.setValue("-");
        this.wsMmTimestamp.clear();
        this.filler2.setValue("-");
        this.wsDdTimestamp.clear();
    }

}

