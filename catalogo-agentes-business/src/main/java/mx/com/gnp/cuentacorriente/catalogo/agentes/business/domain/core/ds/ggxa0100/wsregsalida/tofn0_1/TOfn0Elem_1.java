package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tofn0_1;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-OFN0-ELEM-1.
 *
 */
@Setter
@Getter
public class TOfn0Elem_1 extends DataStruct {
    // Properties
    private AlphanumericVar sCnaOfnCveElem = new AlphanumericVar(this, 1);   // 20 S-CNA-OFN-CVE-ELEM X(01)

    public TOfn0Elem_1() {
        super();
        initialize();
    }

    public TOfn0Elem_1(DataContainer parent) {
        super(parent);
    }

    public TOfn0Elem_1(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSCnaOfnCveElem(final String sCnaOfnCveElem) {
        this.sCnaOfnCveElem.setValue(sCnaOfnCveElem);
    }

    public void setSCnaOfnCveElem(final IAlphanumericValue sCnaOfnCveElem) {
        this.sCnaOfnCveElem.setValue(sCnaOfnCveElem);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sCnaOfnCveElem.clear();
    }

}

