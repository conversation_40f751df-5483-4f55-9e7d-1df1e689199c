package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-LAS-NAME.
 *
 */
@Setter
@Getter
public class WsAuxLasName extends DataStruct {
    // Properties
    private AlphanumericVarArray wsAuxLastName = new AlphanumericVarArray(this, 40, 1);      // 15 WS-AUX-LAST-NAME X(01) [40]

    public WsAuxLasName() {
        super();
        initialize();
    }

    public WsAuxLasName(DataContainer parent) {
        super(parent);
    }

    public WsAuxLasName(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWsAuxLastName(int index) {
        return this.wsAuxLastName.at(index);
    }

    public void setWsAuxLastName(final String wsAuxLastName) {
        this.wsAuxLastName.setValue(wsAuxLastName);
    }

    public void setWsAuxLastName(final AlphanumericVarArray wsAuxLastName) {
        this.wsAuxLastName.setValue(wsAuxLastName);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAuxLastName.clear();
    }

}

