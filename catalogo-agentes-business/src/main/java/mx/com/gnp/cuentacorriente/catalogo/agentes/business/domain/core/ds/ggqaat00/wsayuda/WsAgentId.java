package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AGENT-ID.
 *
 */
@Setter
@Getter
public class WsAgentId extends DataStruct {
    // Properties
    private AlphanumericVar wsAgentId2 = new AlphanumericVar(this, 2);       // 10 WS-AGENT-ID2 X(02)
    private AlphanumericVar wsAgentId3 = new AlphanumericVar(this, 3);       // 10 WS-AGENT-ID3 X(03)

    public WsAgentId() {
        super();
        initialize();
    }

    public WsAgentId(DataContainer parent) {
        super(parent);
    }

    public WsAgentId(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAgentId2(final String wsAgentId2) {
        this.wsAgentId2.setValue(wsAgentId2);
    }

    public void setWsAgentId2(final IAlphanumericValue wsAgentId2) {
        this.wsAgentId2.setValue(wsAgentId2);
    }

    public void setWsAgentId3(final String wsAgentId3) {
        this.wsAgentId3.setValue(wsAgentId3);
    }

    public void setWsAgentId3(final IAlphanumericValue wsAgentId3) {
        this.wsAgentId3.setValue(wsAgentId3);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAgentId2.clear();
        this.wsAgentId3.clear();
    }

}

