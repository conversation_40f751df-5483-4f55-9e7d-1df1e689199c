package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WT-CNA-OFN-CVE.
 *
 */
@Setter
@Getter
public class WtCnaOfnCve extends DataStruct {
    // Properties
    private AlphanumericVarArray wtCnaOfnCveA = new AlphanumericVarArray(this, 4, 1);        // 15 WT-CNA-OFN-CVE-A X(01) [4]

    public WtCnaOfnCve() {
        super();
        initialize();
    }

    public WtCnaOfnCve(DataContainer parent) {
        super(parent);
    }

    public WtCnaOfnCve(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWtCnaOfnCveA(int index) {
        return this.wtCnaOfnCveA.at(index);
    }

    public void setWtCnaOfnCveA(final String wtCnaOfnCveA) {
        this.wtCnaOfnCveA.setValue(wtCnaOfnCveA);
    }

    public void setWtCnaOfnCveA(final AlphanumericVarArray wtCnaOfnCveA) {
        this.wtCnaOfnCveA.setValue(wtCnaOfnCveA);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wtCnaOfnCveA.clear();
    }

}

