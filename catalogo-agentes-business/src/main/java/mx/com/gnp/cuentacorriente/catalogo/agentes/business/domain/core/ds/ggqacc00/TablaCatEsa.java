package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablacatesa.TabCatEsa;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-CAT-ESA.
 *
 */
@Setter
@Getter
public class TablaCatEsa { // VStruct
    // Properties
    private TabCatEsa tabCatEsa = new  TabCatEsa(10);                        // 02 TAB-CAT-ESA [10]

    public TablaCatEsa() {
        initialize();
    }



    public TabCatEsa getTabCatEsa(int index) {
        return this.tabCatEsa.at(index);
    }


    // Deeper properties

    // Property: tabCatEsa.tbEsaDes -> 07 TB-ESA-DES X(10)
    public AlphanumericVar getTbEsaDes() {
        return this.tabCatEsa.getTbEsaDes();
    }

    public void setTbEsaDes(final String tbEsaDes) {
        this.tabCatEsa.setTbEsaDes(tbEsaDes);
    }

    public void setTbEsaDes(final IAlphanumericValue tbEsaDes) {
        this.tabCatEsa.setTbEsaDes(tbEsaDes);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tabCatEsa.initialize();
    }

}

