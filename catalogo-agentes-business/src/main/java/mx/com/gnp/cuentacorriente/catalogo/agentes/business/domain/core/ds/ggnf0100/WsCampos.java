package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.FlaNumAux;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsFlaNum;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsFolLid;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsFolioxx;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsFlaDes;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsPkEstador;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsPkCmaDesr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsTabApePatr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsTabApeMatr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsTabNombrer;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsTabPkNombrer;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsPkNombre;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos.WsCalleNumIn;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CAMPOS.
 *
 */
// 1184071600*                                                                 07160000
// 1185071700*                                                                 07170000
// 1186071800*                                                                 07180000
// 1187071900*                                                                 07190000
@Setter
@Getter
public class WsCampos { // VStruct
    // Properties
    private int wsStatus_1 = 0;                                             // 03 WS-STATUS-1 99
    private int wsCuantosAgt = 0;                                           // 03 WS-CUANTOS-AGT 9(6)
    private int wsCuantosCna = 0;                                           // 03 WS-CUANTOS-CNA 9(6)
    private int wsCuantosFla = 0;                                           // 03 WS-CUANTOS-FLA 9(6)
    private String wsFechaCu = "";                                          // 03 WS-FECHA-CU X(10)
    private String wsParFec = "";                                           // 03 WS-PAR-FEC X(10)
    private String wsSpaces = "";                                           // 03 WS-SPACES X(40)
    private String wsNP = "";                                               // 03 WS-N-P X
    private int wsAgtIdr;                                                   // 03 WS-AGT-IDR S9(9) COMPUTATIONAL
    private int wsTdmCve;                                                   // 03 WS-TDM-CVE S9(4) COMPUTATIONAL
    private int wsDmaPobCve;                                                // 03 WS-DMA-POB-CVE S9(9) COMPUTATIONAL
    private int wsDmaMndCve;                                                // 03 WS-DMA-MND-CVE S9(9) COMPUTATIONAL
    private String wsMndEdoCve = "";                                        // 03 WS-MND-EDO-CVE X(04)
    private int wsGerZon;                                                   // 03 WS-GER-ZON S9(04) COMPUTATIONAL
    private FlaNumAux flaNumAux = new  FlaNumAux();                          // 03 FLA-NUM-AUX
    private WsFlaNum wsFlaNum = new  WsFlaNum();                             // 03 WS-FLA-NUM
    private WsFolLid wsFolLid = new  WsFolLid();                             // 03 WS-FOL-LID
    private WsFolioxx wsFolioxx = new  WsFolioxx();                          // 03 WS-FOLIOXX
    private WsFlaDes wsFlaDes = new  WsFlaDes();                             // 03 WS-FLA-DES
    private WsPkEstador wsPkEstador = new  WsPkEstador();                    // 03 WS-PK-ESTADOR REDEFINED BY WS-PK-ESTADO
    private AlphanumericVar wsPkEstado = new AlphanumericVar(null, this.wsPkEstador, 4);     // 03 WS-PK-ESTADO REDEFINES WS-PK-ESTADOR XXXX
    private WsPkCmaDesr wsPkCmaDesr = new  WsPkCmaDesr();                    // 03 WS-PK-CMA-DESR REDEFINED BY WS-PK-CMA-DES
    private AlphanumericVar wsPkCmaDes = new AlphanumericVar(null, this.wsPkCmaDesr, 60);    // 03 WS-PK-CMA-DES REDEFINES WS-PK-CMA-DESR X(60)
    private AlphanumericVar wsCodPag = new AlphanumericVar(1);               // 03 WS-COD-PAG X REDEFINED BY WS-COD-PAGR
    private UnsignedNumericVar wsCodPagr = new UnsignedNumericVar(null, this.wsCodPag, 1, 0);                // 03 WS-COD-PAGR REDEFINES WS-COD-PAG 9
    private AlphanumericVar wsTabApePat = new AlphanumericVar(40);           // 03 WS-TAB-APE-PAT X(40) REDEFINED BY WS-TAB-APE-PATR
    private WsTabApePatr wsTabApePatr = new  WsTabApePatr(null, this.wsTabApePat);           // 03 WS-TAB-APE-PATR REDEFINES WS-TAB-APE-PAT
    private AlphanumericVar wsTabApeMat = new AlphanumericVar(30);           // 03 WS-TAB-APE-MAT X(30) REDEFINED BY WS-TAB-APE-MATR
    private WsTabApeMatr wsTabApeMatr = new  WsTabApeMatr(null, this.wsTabApeMat);           // 03 WS-TAB-APE-MATR REDEFINES WS-TAB-APE-MAT
    private AlphanumericVar wsTabNombre = new AlphanumericVar(40);           // 03 WS-TAB-NOMBRE X(40) REDEFINED BY WS-TAB-NOMBRER
    private WsTabNombrer wsTabNombrer = new  WsTabNombrer(null, this.wsTabNombre);           // 03 WS-TAB-NOMBRER REDEFINES WS-TAB-NOMBRE
    private AlphanumericVar wsTabPkNombre = new AlphanumericVar(35);         // 03 WS-TAB-PK-NOMBRE X(35) REDEFINED BY WS-TAB-PK-NOMBRER
    private WsTabPkNombrer wsTabPkNombrer = new  WsTabPkNombrer(null, this.wsTabPkNombre);   // 03 WS-TAB-PK-NOMBRER REDEFINES WS-TAB-PK-NOMBRE
    private WsPkNombre wsPkNombre = new  WsPkNombre();                       // 03 WS-PK-NOMBRE
    private WsCalleNumIn wsCalleNumIn = new  WsCalleNumIn();                 // 03 WS-CALLE-NUM-IN UsedAsParameter

    public WsCampos() {
        initialize();
    }



    public void setWsPkEstado(final String wsPkEstado) {
        this.wsPkEstado.setValue(wsPkEstado);
    }

    public void setWsPkEstado(final IAlphanumericValue wsPkEstado) {
        this.wsPkEstado.setValue(wsPkEstado);
    }

    public void setWsPkCmaDes(final String wsPkCmaDes) {
        this.wsPkCmaDes.setValue(wsPkCmaDes);
    }

    public void setWsPkCmaDes(final IAlphanumericValue wsPkCmaDes) {
        this.wsPkCmaDes.setValue(wsPkCmaDes);
    }

    public void setWsCodPag(final String wsCodPag) {
        this.wsCodPag.setValue(wsCodPag);
    }

    public void setWsCodPag(final IAlphanumericValue wsCodPag) {
        this.wsCodPag.setValue(wsCodPag);
    }

    public void setWsCodPagr(final int wsCodPagr) {
        this.wsCodPagr.setValue(wsCodPagr);
    }

    public void setWsCodPagr(final BigDecimal wsCodPagr) {
        this.wsCodPagr.setValue(wsCodPagr);
    }

    public void setWsCodPagr(final INumericValue wsCodPagr) {
        this.wsCodPagr.setValue(wsCodPagr);
    }

    public void setWsTabApePat(final String wsTabApePat) {
        this.wsTabApePat.setValue(wsTabApePat);
    }

    public void setWsTabApePat(final IAlphanumericValue wsTabApePat) {
        this.wsTabApePat.setValue(wsTabApePat);
    }

    public void setWsTabApeMat(final String wsTabApeMat) {
        this.wsTabApeMat.setValue(wsTabApeMat);
    }

    public void setWsTabApeMat(final IAlphanumericValue wsTabApeMat) {
        this.wsTabApeMat.setValue(wsTabApeMat);
    }

    public void setWsTabNombre(final String wsTabNombre) {
        this.wsTabNombre.setValue(wsTabNombre);
    }

    public void setWsTabNombre(final IAlphanumericValue wsTabNombre) {
        this.wsTabNombre.setValue(wsTabNombre);
    }

    public void setWsTabPkNombre(final String wsTabPkNombre) {
        this.wsTabPkNombre.setValue(wsTabPkNombre);
    }

    public void setWsTabPkNombre(final IAlphanumericValue wsTabPkNombre) {
        this.wsTabPkNombre.setValue(wsTabPkNombre);
    }


    // Deeper properties

    // Property: flaNumAux.flaNum0Aux -> 05 FLA-NUM0-AUX X
    public String getFlaNum0Aux() {
        return this.flaNumAux.getFlaNum0Aux();
    }

    public void setFlaNum0Aux(final String flaNum0Aux) {
        this.flaNumAux.setFlaNum0Aux(flaNum0Aux);
    }

    // Property: flaNumAux.flaNum1Aux -> 05 FLA-NUM1-AUX 9(7)
    public int getFlaNum1Aux() {
        return this.flaNumAux.getFlaNum1Aux();
    }

    public void setFlaNum1Aux(final int flaNum1Aux) {
        this.flaNumAux.setFlaNum1Aux(flaNum1Aux);
    }


    // Deeper properties

    // Property: wsFlaNum.wsFlaNum0 -> 05 WS-FLA-NUM0 X
    public AlphanumericVar getWsFlaNum0() {
        return this.wsFlaNum.getWsFlaNum0();
    }

    public void setWsFlaNum0(final String wsFlaNum0) {
        this.wsFlaNum.setWsFlaNum0(wsFlaNum0);
    }

    public void setWsFlaNum0(final IAlphanumericValue wsFlaNum0) {
        this.wsFlaNum.setWsFlaNum0(wsFlaNum0);
    }

    // Property: wsFlaNum.wsFlaNum1 -> 05 WS-FLA-NUM1 9(7)
    public UnsignedNumericVar getWsFlaNum1() {
        return this.wsFlaNum.getWsFlaNum1();
    }

    public void setWsFlaNum1(final int wsFlaNum1) {
        this.wsFlaNum.setWsFlaNum1(wsFlaNum1);
    }

    public void setWsFlaNum1(final INumericValue wsFlaNum1) {
        this.wsFlaNum.setWsFlaNum1(wsFlaNum1);
    }


    // Deeper properties

    // Property: wsFolLid.wsFolLid0 -> 05 WS-FOL-LID0 X
    public AlphanumericVar getWsFolLid0() {
        return this.wsFolLid.getWsFolLid0();
    }

    public void setWsFolLid0(final String wsFolLid0) {
        this.wsFolLid.setWsFolLid0(wsFolLid0);
    }

    public void setWsFolLid0(final IAlphanumericValue wsFolLid0) {
        this.wsFolLid.setWsFolLid0(wsFolLid0);
    }

    // Property: wsFolLid.wsFolLid1 -> 05 WS-FOL-LID1 9(7)
    public UnsignedNumericVar getWsFolLid1() {
        return this.wsFolLid.getWsFolLid1();
    }

    public void setWsFolLid1(final int wsFolLid1) {
        this.wsFolLid.setWsFolLid1(wsFolLid1);
    }

    public void setWsFolLid1(final INumericValue wsFolLid1) {
        this.wsFolLid.setWsFolLid1(wsFolLid1);
    }


    // Deeper properties

    // Property: wsFolioxx.wsFolioxx1 -> 05 WS-FOLIOXX1 X
    public AlphanumericVar getWsFolioxx1() {
        return this.wsFolioxx.getWsFolioxx1();
    }

    public void setWsFolioxx1(final String wsFolioxx1) {
        this.wsFolioxx.setWsFolioxx1(wsFolioxx1);
    }

    public void setWsFolioxx1(final IAlphanumericValue wsFolioxx1) {
        this.wsFolioxx.setWsFolioxx1(wsFolioxx1);
    }

    // Property: wsFolioxx.wsFolioxx2 -> 05 WS-FOLIOXX2 9(7)
    public UnsignedNumericVar getWsFolioxx2() {
        return this.wsFolioxx.getWsFolioxx2();
    }

    public void setWsFolioxx2(final int wsFolioxx2) {
        this.wsFolioxx.setWsFolioxx2(wsFolioxx2);
    }

    public void setWsFolioxx2(final INumericValue wsFolioxx2) {
        this.wsFolioxx.setWsFolioxx2(wsFolioxx2);
    }


    // Deeper properties

    // Property: wsFlaDes.wsFlaDesDos -> 05 WS-FLA-DES-DOS XX
    public String getWsFlaDesDos() {
        return this.wsFlaDes.getWsFlaDesDos();
    }

    public void setWsFlaDesDos(final String wsFlaDesDos) {
        this.wsFlaDes.setWsFlaDesDos(wsFlaDesDos);
    }

    // Property: wsFlaDes.wsFlaDesResto -> 05 WS-FLA-DES-RESTO X(38)
    public String getWsFlaDesResto() {
        return this.wsFlaDes.getWsFlaDesResto();
    }

    public void setWsFlaDesResto(final String wsFlaDesResto) {
        this.wsFlaDes.setWsFlaDesResto(wsFlaDesResto);
    }


    // Deeper properties

    // Property: wsPkEstador.wsPkEstado1 -> 05 WS-PK-ESTADO1 99
    public UnsignedNumericVar getWsPkEstado1() {
        return this.wsPkEstador.getWsPkEstado1();
    }

    public void setWsPkEstado1(final int wsPkEstado1) {
        this.wsPkEstador.setWsPkEstado1(wsPkEstado1);
    }

    public void setWsPkEstado1(final INumericValue wsPkEstado1) {
        this.wsPkEstador.setWsPkEstado1(wsPkEstado1);
    }

    // Property: wsPkEstador.wsPkEstado2 -> 05 WS-PK-ESTADO2 XX
    public AlphanumericVar getWsPkEstado2() {
        return this.wsPkEstador.getWsPkEstado2();
    }

    public void setWsPkEstado2(final String wsPkEstado2) {
        this.wsPkEstador.setWsPkEstado2(wsPkEstado2);
    }

    public void setWsPkEstado2(final IAlphanumericValue wsPkEstado2) {
        this.wsPkEstador.setWsPkEstado2(wsPkEstado2);
    }


    // Deeper properties

    // Property: wsPkCmaDesr.wsPkTelr -> 05 WS-PK-TELR X(12) REDEFINED BY WS-PK-TEL
    public AlphanumericVar getWsPkTelr() {
        return this.wsPkCmaDesr.getWsPkTelr();
    }

    public void setWsPkTelr(final String wsPkTelr) {
        this.wsPkCmaDesr.setWsPkTelr(wsPkTelr);
    }

    public void setWsPkTelr(final IAlphanumericValue wsPkTelr) {
        this.wsPkCmaDesr.setWsPkTelr(wsPkTelr);
    }

    // Property: wsPkCmaDesr.wsPkTel -> 05 WS-PK-TEL REDEFINES WS-PK-TELR 9(12)
    public UnsignedNumericVar getWsPkTel() {
        return this.wsPkCmaDesr.getWsPkTel();
    }

    public void setWsPkTel(final BigDecimal wsPkTel) {
        this.wsPkCmaDesr.setWsPkTel(wsPkTel);
    }

    public void setWsPkTel(final int wsPkTel) {
        this.wsPkCmaDesr.setWsPkTel(wsPkTel);
    }

    public void setWsPkTel(final INumericValue wsPkTel) {
        this.wsPkCmaDesr.setWsPkTel(wsPkTel);
    }

    // Property: wsPkCmaDesr.filler1 -> 05 FILLER X(48)
    public AlphanumericVar getFiller1() {
        return this.wsPkCmaDesr.getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.wsPkCmaDesr.setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.wsPkCmaDesr.setFiller1(filler1);
    }


    // Deeper properties

    // Property: wsTabApePatr.eleApePat -> 05 ELE-APE-PAT X [40]
    public AlphanumericVarArray getEleApePat() {
        return this.wsTabApePatr.getEleApePat();
    }

    public void setEleApePat(final String eleApePat) {
        this.wsTabApePatr.setEleApePat(eleApePat);
    }

    public void setEleApePat(final AlphanumericVarArray eleApePat) {
        this.wsTabApePatr.setEleApePat(eleApePat);
    }


    // Deeper properties

    // Property: wsTabApeMatr.eleApeMat -> 05 ELE-APE-MAT X [30]
    public AlphanumericVarArray getEleApeMat() {
        return this.wsTabApeMatr.getEleApeMat();
    }

    public void setEleApeMat(final String eleApeMat) {
        this.wsTabApeMatr.setEleApeMat(eleApeMat);
    }

    public void setEleApeMat(final AlphanumericVarArray eleApeMat) {
        this.wsTabApeMatr.setEleApeMat(eleApeMat);
    }


    // Deeper properties

    // Property: wsTabNombrer.eleNombre -> 05 ELE-NOMBRE X [40]
    public AlphanumericVarArray getEleNombre() {
        return this.wsTabNombrer.getEleNombre();
    }

    public void setEleNombre(final String eleNombre) {
        this.wsTabNombrer.setEleNombre(eleNombre);
    }

    public void setEleNombre(final AlphanumericVarArray eleNombre) {
        this.wsTabNombrer.setEleNombre(eleNombre);
    }


    // Deeper properties

    // Property: wsTabPkNombrer.elePkNombre -> 05 ELE-PK-NOMBRE X [35]
    public AlphanumericVarArray getElePkNombre() {
        return this.wsTabPkNombrer.getElePkNombre();
    }

    public void setElePkNombre(final String elePkNombre) {
        this.wsTabPkNombrer.setElePkNombre(elePkNombre);
    }

    public void setElePkNombre(final AlphanumericVarArray elePkNombre) {
        this.wsTabPkNombrer.setElePkNombre(elePkNombre);
    }


    // Deeper properties

    // Property: wsPkNombre.wsPkApePat -> 05 WS-PK-APE-PAT X(15)
    public String getWsPkApePat() {
        return this.wsPkNombre.getWsPkApePat();
    }

    public void setWsPkApePat(final String wsPkApePat) {
        this.wsPkNombre.setWsPkApePat(wsPkApePat);
    }

    // Property: wsPkNombre.wsPkApeMat -> 05 WS-PK-APE-MAT X(10)
    public String getWsPkApeMat() {
        return this.wsPkNombre.getWsPkApeMat();
    }

    public void setWsPkApeMat(final String wsPkApeMat) {
        this.wsPkNombre.setWsPkApeMat(wsPkApeMat);
    }

    // Property: wsPkNombre.wsPkNom -> 05 WS-PK-NOM X(10)
    public String getWsPkNom() {
        return this.wsPkNombre.getWsPkNom();
    }

    public void setWsPkNom(final String wsPkNom) {
        this.wsPkNombre.setWsPkNom(wsPkNom);
    }


    // Deeper properties

    // Property: wsCalleNumIn.wsPkCalle -> 05 WS-PK-CALLE X(25)
    public AlphanumericVar getWsPkCalle() {
        return this.wsCalleNumIn.getWsPkCalle();
    }

    public void setWsPkCalle(final String wsPkCalle) {
        this.wsCalleNumIn.setWsPkCalle(wsPkCalle);
    }

    public void setWsPkCalle(final IAlphanumericValue wsPkCalle) {
        this.wsCalleNumIn.setWsPkCalle(wsPkCalle);
    }

    // Property: wsCalleNumIn.wsPkNum -> 05 WS-PK-NUM X(05)
    public AlphanumericVar getWsPkNum() {
        return this.wsCalleNumIn.getWsPkNum();
    }

    public void setWsPkNum(final String wsPkNum) {
        this.wsCalleNumIn.setWsPkNum(wsPkNum);
    }

    public void setWsPkNum(final IAlphanumericValue wsPkNum) {
        this.wsCalleNumIn.setWsPkNum(wsPkNum);
    }

    // Property: wsCalleNumIn.wsPkInt -> 05 WS-PK-INT X(05)
    public AlphanumericVar getWsPkInt() {
        return this.wsCalleNumIn.getWsPkInt();
    }

    public void setWsPkInt(final String wsPkInt) {
        this.wsCalleNumIn.setWsPkInt(wsPkInt);
    }

    public void setWsPkInt(final IAlphanumericValue wsPkInt) {
        this.wsCalleNumIn.setWsPkInt(wsPkInt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsStatus_1 = 0;
        this.wsCuantosAgt = 0;
        this.wsCuantosCna = 0;
        this.wsCuantosFla = 0;
        this.wsFechaCu = "";
        this.wsParFec = "";
        this.wsSpaces = "";
        this.wsNP = "";
        this.wsAgtIdr = 0;
        this.wsTdmCve = 0;
        this.wsDmaPobCve = 0;
        this.wsDmaMndCve = 0;
        this.wsMndEdoCve = "";
        this.wsGerZon = 0;
        this.flaNumAux.initialize();
        this.wsFlaNum.initialize();
        this.wsFolLid.initialize();
        this.wsFolioxx.initialize();
        this.wsFlaDes.initialize();
        this.wsPkEstador.initialize();
        this.wsPkCmaDesr.initialize();
        this.wsCodPag.clear();
        this.wsTabApePat.clear();
        this.wsTabApeMat.clear();
        this.wsTabNombre.clear();
        this.wsTabPkNombre.clear();
        this.wsPkNombre.initialize();
        this.wsCalleNumIn.initialize();
    }

}

