/**
 *  JCL Program: NFDFJ00.
 *  Code generation type: KY<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Nfdfj00JclProc extends AbstractJclProgram {

    public Nfdfj00JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("NFDFJ00", (nfdfj00) -> {
            nfdfj00.addParameter("PROC");
            if (nfdfj00.getLastReturnCode()==8) {
                /*//****/
                /*//*                     *** NFDFJ00 ****/
                /*//*   APLICACION:  CANALES*/
                /*//*   PROCESO:     D I A R I O.*/
                /*//*   OBJETIVO:    CARGA DE INFORMACION HISTORICA CRS*/
                /*//*   COMENTARIOS:*/
                /*//*   CORRE*/
                /*//*   DESPUES DE:  PGANFDFH EXTRACCION AGENTES MEDICA*/
                /*//**/
                /*//*   ANTES DE:    NFD0100  : ACTUALIZACION DE AGENTEMOD.*/
                /*//*   ELABORO:     JL.CRUZ*/
                /*//*   MODIFICO:    ALAN CHEW LYONS - PASO NFDFJ01: SE GENERA ARCHIVO*/
                /*//*                PPCQS.NFDFJ00.EANFDFJ0 EN VOLUMEN COMPARTIDO*/
                /*//****/
                /*//****/
                /*//*                         * NFDFJ00 **/
                /*//* OBJETIVO DEL STEP:  BORRA LOS ARCHIVOS AL ARRANCAR*/
                /*//* PASO REINICIABLE*/
                /*//****/
                nfdfj00.execProgram("NFDFJ03", "IDCAMS", (nfdfj03) -> {
                    nfdfj03.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdfj03.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdfj03.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDFJ01)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdfj00.getLastReturnCode()<=4) {
                /*//****/
                /*//*                         * NFDFJ01 **/
                /*//* OBJETIVO DEL STEP:  CARGAR LA INFORMACION EN TABLAS CRS*/
                /*//* PASO REINICIABLE*/
                /*//****/
                nfdfj00.execProgram("NFDFJ02", "IKJEFT01", (nfdfj02) -> {
                    nfdfj02.addParameter("REGION","0K");
                    nfdfj02.addDataDefinition("ARCHAGTH", "PPCDS.KCDPP710.GFVTAGTH",  (archagth) -> {
                        archagth.addParameter("DISP","SHR");
                    });
                    nfdfj02.addDataDefinition("ARCHCNAH", "PPCDS.KCDPP710.GFVTCNAH",  (archcnah) -> {
                        archcnah.addParameter("DISP","SHR");
                    });
                    nfdfj02.addDataDefinition("ARCHDMAH", "PPCDS.KCDPP710.GFVTDMAH",  (archdmah) -> {
                        archdmah.addParameter("DISP","SHR");
                    });
                    nfdfj02.addDataDefinition("ARCHCMAH", "PPCDS.KCDPP710.GFVTCMAH",  (archcmah) -> {
                        archcmah.addParameter("DISP","SHR");
                    });
                    nfdfj02.addDataDefinition("ARCHDRC0", "PPCDS.KCDPP600.GFVTDRC0",  (archdrc0) -> {
                        archdrc0.addParameter("DISP","SHR");
                    });
                    nfdfj02.addDataDefinition("ARCHINTR", "PPCQS.NFDFJ00.EANFDFJ0",  (archintr) -> {
                        archintr.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        archintr.addParameter("UNIT","SHRAPL");
                        archintr.addParameter("SPACE", "(CYL,(2,2),RLSE)");
                        archintr.addParameter("DCB", "(RECFM=FB,LRECL=28,BLKSIZE=23464,BUFNO=04,DSORG=PS)");
                    });
                    nfdfj02.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdfj02.addDataDefinition("CMPRINT",  (cmprint) -> {
                        cmprint.addParameter("SYSOUT","*");
                    });
                    nfdfj02.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                        sysdbout.addParameter("SYSOUT","*");
                    });
                    nfdfj02.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdfj02.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdfj02.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    nfdfj02.addDataDefinition("SYSABOUT",  (sysabout) -> {
                        sysabout.addParameter("SYSOUT","*");
                    });
                    nfdfj02.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDFJ00)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdfj00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*  OBJETIVO:  ENVIA A BACKLOG ARCHIVO INTERFAZ                      **/
                /*//*  PASO REINICIABLE                                                 **/
                /*//**********************************************************************/
                nfdfj00.execProgram("NFDFJ01", "FTP", (nfdfj01) -> {
                    nfdfj01.addParameter("PARM","-t ANDANTIS (EXIT");
                    nfdfj01.addParameter("REGION","0M");
                    nfdfj01.addDataDefinition("INPUT", "PNCQP.BTCH.CARDS(NFDFJ02)",  (input) -> {
                        input.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
