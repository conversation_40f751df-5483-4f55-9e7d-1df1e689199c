package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tceo0;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-CEO0-ELEM.
 *
 */
@Setter
@Getter
public class TCeo0Elem extends DataStruct {
    // Properties
    private AlphanumericVar sCeoCeoNom = new AlphanumericVar(this, 30);      // 20 S-CEO-CEO-NOM X(30)
    private AlphanumericVar sCeoLimitad = new AlphanumericVar(this, 1);      // 20 S-CEO-LIMITAD X(01)

    public TCeo0Elem() {
        super();
        initialize();
    }

    public TCeo0Elem(DataContainer parent) {
        super(parent);
    }

    public TCeo0Elem(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSCeoCeoNom(final String sCeoCeoNom) {
        this.sCeoCeoNom.setValue(sCeoCeoNom);
    }

    public void setSCeoCeoNom(final IAlphanumericValue sCeoCeoNom) {
        this.sCeoCeoNom.setValue(sCeoCeoNom);
    }

    public void setSCeoLimitad(final String sCeoLimitad) {
        this.sCeoLimitad.setValue(sCeoLimitad);
    }

    public void setSCeoLimitad(final IAlphanumericValue sCeoLimitad) {
        this.sCeoLimitad.setValue(sCeoLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sCeoCeoNom.clear();
        this.sCeoLimitad.clear();
    }

}

