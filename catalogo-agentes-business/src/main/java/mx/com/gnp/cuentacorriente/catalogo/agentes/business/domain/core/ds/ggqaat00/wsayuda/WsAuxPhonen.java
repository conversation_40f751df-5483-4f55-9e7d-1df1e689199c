package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-PHONEN.
 *
 */
@Setter
@Getter
public class WsAuxPhonen extends DataStruct {
    // Properties
    private AlphanumericVarArray wsAuxPhone = new AlphanumericVarArray(this, 14, 1);         // 10 WS-AUX-PHONE X(01) [14]

    public WsAuxPhonen() {
        super();
        initialize();
    }

    public WsAuxPhonen(DataContainer parent) {
        super(parent);
    }

    public WsAuxPhonen(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWsAuxPhone(int index) {
        return this.wsAuxPhone.at(index);
    }

    public void setWsAuxPhone(final String wsAuxPhone) {
        this.wsAuxPhone.setValue(wsAuxPhone);
    }

    public void setWsAuxPhone(final AlphanumericVarArray wsAuxPhone) {
        this.wsAuxPhone.setValue(wsAuxPhone);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAuxPhone.clear();
    }

}

