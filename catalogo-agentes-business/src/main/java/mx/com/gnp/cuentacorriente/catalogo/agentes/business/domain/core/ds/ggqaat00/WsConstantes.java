package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00agcpy.PlhjConstantes;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00agcpy.plhjconstantes.PlhjEstadosCveTrac;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CONSTANTES.
 *
 */
// 769      *---------------- CONSTANTES ------------------------------------*
@Setter
@Getter
public class WsConstantes extends DataStruct {
    // Properties
    private PlhjConstantes plhjConstantes = new  PlhjConstantes(this);       // 05 PLHJ-CONSTANTES

    public WsConstantes() {
        super();
        initialize();
    }

    public WsConstantes(DataContainer parent) {
        super(parent);
    }

    public WsConstantes(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }



    // Deeper properties

    // Property: ggqa00agcpy.plhjConstantes.plhjDireccionGnp1 -> 10 PLHJ-DIRECCION-GNP1 X(35)
    public AlphanumericVar getPlhjDireccionGnp1() {
        return this.plhjConstantes.getPlhjDireccionGnp1();
    }

    public void setPlhjDireccionGnp1(final String plhjDireccionGnp1) {
        this.plhjConstantes.setPlhjDireccionGnp1(plhjDireccionGnp1);
    }

    public void setPlhjDireccionGnp1(final IAlphanumericValue plhjDireccionGnp1) {
        this.plhjConstantes.setPlhjDireccionGnp1(plhjDireccionGnp1);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjDireccionGnp2 -> 10 PLHJ-DIRECCION-GNP2 X(35)
    public AlphanumericVar getPlhjDireccionGnp2() {
        return this.plhjConstantes.getPlhjDireccionGnp2();
    }

    public void setPlhjDireccionGnp2(final String plhjDireccionGnp2) {
        this.plhjConstantes.setPlhjDireccionGnp2(plhjDireccionGnp2);
    }

    public void setPlhjDireccionGnp2(final IAlphanumericValue plhjDireccionGnp2) {
        this.plhjConstantes.setPlhjDireccionGnp2(plhjDireccionGnp2);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjDireccionGnp3 -> 10 PLHJ-DIRECCION-GNP3 X(35)
    public AlphanumericVar getPlhjDireccionGnp3() {
        return this.plhjConstantes.getPlhjDireccionGnp3();
    }

    public void setPlhjDireccionGnp3(final String plhjDireccionGnp3) {
        this.plhjConstantes.setPlhjDireccionGnp3(plhjDireccionGnp3);
    }

    public void setPlhjDireccionGnp3(final IAlphanumericValue plhjDireccionGnp3) {
        this.plhjConstantes.setPlhjDireccionGnp3(plhjDireccionGnp3);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjNombreGz -> 10 PLHJ-NOMBRE-GZ X(24)
    public AlphanumericVar getPlhjNombreGz() {
        return this.plhjConstantes.getPlhjNombreGz();
    }

    public void setPlhjNombreGz(final String plhjNombreGz) {
        this.plhjConstantes.setPlhjNombreGz(plhjNombreGz);
    }

    public void setPlhjNombreGz(final IAlphanumericValue plhjNombreGz) {
        this.plhjConstantes.setPlhjNombreGz(plhjNombreGz);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjNumeros -> 10 PLHJ-NUMEROS X(01)
    public AlphanumericVar getPlhjNumeros() {
        return this.plhjConstantes.getPlhjNumeros();
    }

    public void setPlhjNumeros(final String plhjNumeros) {
        this.plhjConstantes.setPlhjNumeros(plhjNumeros);
    }

    public void setPlhjNumeros(final IAlphanumericValue plhjNumeros) {
        this.plhjConstantes.setPlhjNumeros(plhjNumeros);
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-NUMS
    public boolean isPlhjNums() {
        return this.plhjConstantes.isPlhjNums();
    }

    public void setPlhjNums() {
        this.plhjConstantes.setPlhjNums();
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosSaga -> 10 PLHJ-ESTADOS-SAGA X(02)
    public AlphanumericVar getPlhjEstadosSaga() {
        return this.plhjConstantes.getPlhjEstadosSaga();
    }

    public void setPlhjEstadosSaga(final String plhjEstadosSaga) {
        this.plhjConstantes.setPlhjEstadosSaga(plhjEstadosSaga);
    }

    public void setPlhjEstadosSaga(final IAlphanumericValue plhjEstadosSaga) {
        this.plhjConstantes.setPlhjEstadosSaga(plhjEstadosSaga);
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-AGS
    public boolean isPlhjAgs() {
        return this.plhjConstantes.isPlhjAgs();
    }

    public void setPlhjAgs() {
        this.plhjConstantes.setPlhjAgs();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-BCN
    public boolean isPlhjBcn() {
        return this.plhjConstantes.isPlhjBcn();
    }

    public void setPlhjBcn() {
        this.plhjConstantes.setPlhjBcn();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-BCS
    public boolean isPlhjBcs() {
        return this.plhjConstantes.isPlhjBcs();
    }

    public void setPlhjBcs() {
        this.plhjConstantes.setPlhjBcs();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-CAMPECHE
    public boolean isPlhjCampeche() {
        return this.plhjConstantes.isPlhjCampeche();
    }

    public void setPlhjCampeche() {
        this.plhjConstantes.setPlhjCampeche();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-COAHUILA
    public boolean isPlhjCoahuila() {
        return this.plhjConstantes.isPlhjCoahuila();
    }

    public void setPlhjCoahuila() {
        this.plhjConstantes.setPlhjCoahuila();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-COLIMA
    public boolean isPlhjColima() {
        return this.plhjConstantes.isPlhjColima();
    }

    public void setPlhjColima() {
        this.plhjConstantes.setPlhjColima();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-CHIAPAS
    public boolean isPlhjChiapas() {
        return this.plhjConstantes.isPlhjChiapas();
    }

    public void setPlhjChiapas() {
        this.plhjConstantes.setPlhjChiapas();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-CHIHUAHUA
    public boolean isPlhjChihuahua() {
        return this.plhjConstantes.isPlhjChihuahua();
    }

    public void setPlhjChihuahua() {
        this.plhjConstantes.setPlhjChihuahua();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-DF
    public boolean isPlhjDf() {
        return this.plhjConstantes.isPlhjDf();
    }

    public void setPlhjDf() {
        this.plhjConstantes.setPlhjDf();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-DURANGO
    public boolean isPlhjDurango() {
        return this.plhjConstantes.isPlhjDurango();
    }

    public void setPlhjDurango() {
        this.plhjConstantes.setPlhjDurango();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-GTO
    public boolean isPlhjGto() {
        return this.plhjConstantes.isPlhjGto();
    }

    public void setPlhjGto() {
        this.plhjConstantes.setPlhjGto();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-GUERRERO
    public boolean isPlhjGuerrero() {
        return this.plhjConstantes.isPlhjGuerrero();
    }

    public void setPlhjGuerrero() {
        this.plhjConstantes.setPlhjGuerrero();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-HIDALGO
    public boolean isPlhjHidalgo() {
        return this.plhjConstantes.isPlhjHidalgo();
    }

    public void setPlhjHidalgo() {
        this.plhjConstantes.setPlhjHidalgo();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-JALISCO
    public boolean isPlhjJalisco() {
        return this.plhjConstantes.isPlhjJalisco();
    }

    public void setPlhjJalisco() {
        this.plhjConstantes.setPlhjJalisco();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-EDOMEX
    public boolean isPlhjEdomex() {
        return this.plhjConstantes.isPlhjEdomex();
    }

    public void setPlhjEdomex() {
        this.plhjConstantes.setPlhjEdomex();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-MICHOACAN
    public boolean isPlhjMichoacan() {
        return this.plhjConstantes.isPlhjMichoacan();
    }

    public void setPlhjMichoacan() {
        this.plhjConstantes.setPlhjMichoacan();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-MORELOS
    public boolean isPlhjMorelos() {
        return this.plhjConstantes.isPlhjMorelos();
    }

    public void setPlhjMorelos() {
        this.plhjConstantes.setPlhjMorelos();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-NAYARIT
    public boolean isPlhjNayarit() {
        return this.plhjConstantes.isPlhjNayarit();
    }

    public void setPlhjNayarit() {
        this.plhjConstantes.setPlhjNayarit();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-NUEVOLEON
    public boolean isPlhjNuevoleon() {
        return this.plhjConstantes.isPlhjNuevoleon();
    }

    public void setPlhjNuevoleon() {
        this.plhjConstantes.setPlhjNuevoleon();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-OAXACA
    public boolean isPlhjOaxaca() {
        return this.plhjConstantes.isPlhjOaxaca();
    }

    public void setPlhjOaxaca() {
        this.plhjConstantes.setPlhjOaxaca();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-PUEBLA
    public boolean isPlhjPuebla() {
        return this.plhjConstantes.isPlhjPuebla();
    }

    public void setPlhjPuebla() {
        this.plhjConstantes.setPlhjPuebla();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-QUERETARO
    public boolean isPlhjQueretaro() {
        return this.plhjConstantes.isPlhjQueretaro();
    }

    public void setPlhjQueretaro() {
        this.plhjConstantes.setPlhjQueretaro();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-QROO
    public boolean isPlhjQroo() {
        return this.plhjConstantes.isPlhjQroo();
    }

    public void setPlhjQroo() {
        this.plhjConstantes.setPlhjQroo();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-SLP
    public boolean isPlhjSlp() {
        return this.plhjConstantes.isPlhjSlp();
    }

    public void setPlhjSlp() {
        this.plhjConstantes.setPlhjSlp();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-SINALOA
    public boolean isPlhjSinaloa() {
        return this.plhjConstantes.isPlhjSinaloa();
    }

    public void setPlhjSinaloa() {
        this.plhjConstantes.setPlhjSinaloa();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-SONORA
    public boolean isPlhjSonora() {
        return this.plhjConstantes.isPlhjSonora();
    }

    public void setPlhjSonora() {
        this.plhjConstantes.setPlhjSonora();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-TABASCO
    public boolean isPlhjTabasco() {
        return this.plhjConstantes.isPlhjTabasco();
    }

    public void setPlhjTabasco() {
        this.plhjConstantes.setPlhjTabasco();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-TAMAULIPAS
    public boolean isPlhjTamaulipas() {
        return this.plhjConstantes.isPlhjTamaulipas();
    }

    public void setPlhjTamaulipas() {
        this.plhjConstantes.setPlhjTamaulipas();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-TLAXCALA
    public boolean isPlhjTlaxcala() {
        return this.plhjConstantes.isPlhjTlaxcala();
    }

    public void setPlhjTlaxcala() {
        this.plhjConstantes.setPlhjTlaxcala();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-VERACRUZ
    public boolean isPlhjVeracruz() {
        return this.plhjConstantes.isPlhjVeracruz();
    }

    public void setPlhjVeracruz() {
        this.plhjConstantes.setPlhjVeracruz();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-YUCATAN
    public boolean isPlhjYucatan() {
        return this.plhjConstantes.isPlhjYucatan();
    }

    public void setPlhjYucatan() {
        this.plhjConstantes.setPlhjYucatan();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-ZACATECAS
    public boolean isPlhjZacatecas() {
        return this.plhjConstantes.isPlhjZacatecas();
    }

    public void setPlhjZacatecas() {
        this.plhjConstantes.setPlhjZacatecas();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-EXTRANJERO
    public boolean isPlhjExtranjero() {
        return this.plhjConstantes.isPlhjExtranjero();
    }

    public void setPlhjExtranjero() {
        this.plhjConstantes.setPlhjExtranjero();
    }

    // Property: ggqa00agcpy.plhjConstantes : condition PLHJ-OTROS
    public boolean isPlhjOtros() {
        return this.plhjConstantes.isPlhjOtros();
    }

    public void setPlhjOtros() {
        this.plhjConstantes.setPlhjOtros();
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac -> 10 PLHJ-ESTADOS-CVE-TRAC
    public PlhjEstadosCveTrac getPlhjEstadosCveTrac() {
        return this.plhjConstantes.getPlhjEstadosCveTrac();
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjAgst -> 15 PLHJ-AGST X(03)
    public AlphanumericVar getPlhjAgst() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjAgst();
    }

    public void setPlhjAgst(final String plhjAgst) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjAgst(plhjAgst);
    }

    public void setPlhjAgst(final IAlphanumericValue plhjAgst) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjAgst(plhjAgst);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjBcnt -> 15 PLHJ-BCNT X(03)
    public AlphanumericVar getPlhjBcnt() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjBcnt();
    }

    public void setPlhjBcnt(final String plhjBcnt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjBcnt(plhjBcnt);
    }

    public void setPlhjBcnt(final IAlphanumericValue plhjBcnt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjBcnt(plhjBcnt);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjBcst -> 15 PLHJ-BCST X(03)
    public AlphanumericVar getPlhjBcst() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjBcst();
    }

    public void setPlhjBcst(final String plhjBcst) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjBcst(plhjBcst);
    }

    public void setPlhjBcst(final IAlphanumericValue plhjBcst) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjBcst(plhjBcst);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjCampechet -> 15 PLHJ-CAMPECHET X(03)
    public AlphanumericVar getPlhjCampechet() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjCampechet();
    }

    public void setPlhjCampechet(final String plhjCampechet) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjCampechet(plhjCampechet);
    }

    public void setPlhjCampechet(final IAlphanumericValue plhjCampechet) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjCampechet(plhjCampechet);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjCoahuilat -> 15 PLHJ-COAHUILAT X(03)
    public AlphanumericVar getPlhjCoahuilat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjCoahuilat();
    }

    public void setPlhjCoahuilat(final String plhjCoahuilat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjCoahuilat(plhjCoahuilat);
    }

    public void setPlhjCoahuilat(final IAlphanumericValue plhjCoahuilat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjCoahuilat(plhjCoahuilat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjColimat -> 15 PLHJ-COLIMAT X(03)
    public AlphanumericVar getPlhjColimat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjColimat();
    }

    public void setPlhjColimat(final String plhjColimat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjColimat(plhjColimat);
    }

    public void setPlhjColimat(final IAlphanumericValue plhjColimat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjColimat(plhjColimat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjChiapast -> 15 PLHJ-CHIAPAST X(03)
    public AlphanumericVar getPlhjChiapast() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjChiapast();
    }

    public void setPlhjChiapast(final String plhjChiapast) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjChiapast(plhjChiapast);
    }

    public void setPlhjChiapast(final IAlphanumericValue plhjChiapast) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjChiapast(plhjChiapast);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjChihuahuat -> 15 PLHJ-CHIHUAHUAT X(03)
    public AlphanumericVar getPlhjChihuahuat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjChihuahuat();
    }

    public void setPlhjChihuahuat(final String plhjChihuahuat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjChihuahuat(plhjChihuahuat);
    }

    public void setPlhjChihuahuat(final IAlphanumericValue plhjChihuahuat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjChihuahuat(plhjChihuahuat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjDft -> 15 PLHJ-DFT X(03)
    public AlphanumericVar getPlhjDft() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjDft();
    }

    public void setPlhjDft(final String plhjDft) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjDft(plhjDft);
    }

    public void setPlhjDft(final IAlphanumericValue plhjDft) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjDft(plhjDft);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjDurangot -> 15 PLHJ-DURANGOT X(03)
    public AlphanumericVar getPlhjDurangot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjDurangot();
    }

    public void setPlhjDurangot(final String plhjDurangot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjDurangot(plhjDurangot);
    }

    public void setPlhjDurangot(final IAlphanumericValue plhjDurangot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjDurangot(plhjDurangot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjGtot -> 15 PLHJ-GTOT X(03)
    public AlphanumericVar getPlhjGtot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjGtot();
    }

    public void setPlhjGtot(final String plhjGtot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjGtot(plhjGtot);
    }

    public void setPlhjGtot(final IAlphanumericValue plhjGtot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjGtot(plhjGtot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjGuerrerot -> 15 PLHJ-GUERREROT X(03)
    public AlphanumericVar getPlhjGuerrerot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjGuerrerot();
    }

    public void setPlhjGuerrerot(final String plhjGuerrerot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjGuerrerot(plhjGuerrerot);
    }

    public void setPlhjGuerrerot(final IAlphanumericValue plhjGuerrerot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjGuerrerot(plhjGuerrerot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjHidalgot -> 15 PLHJ-HIDALGOT X(03)
    public AlphanumericVar getPlhjHidalgot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjHidalgot();
    }

    public void setPlhjHidalgot(final String plhjHidalgot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjHidalgot(plhjHidalgot);
    }

    public void setPlhjHidalgot(final IAlphanumericValue plhjHidalgot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjHidalgot(plhjHidalgot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjJaliscot -> 15 PLHJ-JALISCOT X(03)
    public AlphanumericVar getPlhjJaliscot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjJaliscot();
    }

    public void setPlhjJaliscot(final String plhjJaliscot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjJaliscot(plhjJaliscot);
    }

    public void setPlhjJaliscot(final IAlphanumericValue plhjJaliscot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjJaliscot(plhjJaliscot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjEdomext -> 15 PLHJ-EDOMEXT X(03)
    public AlphanumericVar getPlhjEdomext() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjEdomext();
    }

    public void setPlhjEdomext(final String plhjEdomext) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjEdomext(plhjEdomext);
    }

    public void setPlhjEdomext(final IAlphanumericValue plhjEdomext) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjEdomext(plhjEdomext);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjMichoacant -> 15 PLHJ-MICHOACANT X(03)
    public AlphanumericVar getPlhjMichoacant() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjMichoacant();
    }

    public void setPlhjMichoacant(final String plhjMichoacant) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjMichoacant(plhjMichoacant);
    }

    public void setPlhjMichoacant(final IAlphanumericValue plhjMichoacant) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjMichoacant(plhjMichoacant);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjMorelost -> 15 PLHJ-MORELOST X(03)
    public AlphanumericVar getPlhjMorelost() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjMorelost();
    }

    public void setPlhjMorelost(final String plhjMorelost) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjMorelost(plhjMorelost);
    }

    public void setPlhjMorelost(final IAlphanumericValue plhjMorelost) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjMorelost(plhjMorelost);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjNayaritt -> 15 PLHJ-NAYARITT X(03)
    public AlphanumericVar getPlhjNayaritt() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjNayaritt();
    }

    public void setPlhjNayaritt(final String plhjNayaritt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjNayaritt(plhjNayaritt);
    }

    public void setPlhjNayaritt(final IAlphanumericValue plhjNayaritt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjNayaritt(plhjNayaritt);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjNuevoleont -> 15 PLHJ-NUEVOLEONT X(03)
    public AlphanumericVar getPlhjNuevoleont() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjNuevoleont();
    }

    public void setPlhjNuevoleont(final String plhjNuevoleont) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjNuevoleont(plhjNuevoleont);
    }

    public void setPlhjNuevoleont(final IAlphanumericValue plhjNuevoleont) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjNuevoleont(plhjNuevoleont);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjOaxacat -> 15 PLHJ-OAXACAT X(03)
    public AlphanumericVar getPlhjOaxacat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjOaxacat();
    }

    public void setPlhjOaxacat(final String plhjOaxacat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjOaxacat(plhjOaxacat);
    }

    public void setPlhjOaxacat(final IAlphanumericValue plhjOaxacat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjOaxacat(plhjOaxacat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjPueblat -> 15 PLHJ-PUEBLAT X(03)
    public AlphanumericVar getPlhjPueblat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjPueblat();
    }

    public void setPlhjPueblat(final String plhjPueblat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjPueblat(plhjPueblat);
    }

    public void setPlhjPueblat(final IAlphanumericValue plhjPueblat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjPueblat(plhjPueblat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjQueretarot -> 15 PLHJ-QUERETAROT X(03)
    public AlphanumericVar getPlhjQueretarot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjQueretarot();
    }

    public void setPlhjQueretarot(final String plhjQueretarot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjQueretarot(plhjQueretarot);
    }

    public void setPlhjQueretarot(final IAlphanumericValue plhjQueretarot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjQueretarot(plhjQueretarot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjQroot -> 15 PLHJ-QROOT X(03)
    public AlphanumericVar getPlhjQroot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjQroot();
    }

    public void setPlhjQroot(final String plhjQroot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjQroot(plhjQroot);
    }

    public void setPlhjQroot(final IAlphanumericValue plhjQroot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjQroot(plhjQroot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjSlpt -> 15 PLHJ-SLPT X(03)
    public AlphanumericVar getPlhjSlpt() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjSlpt();
    }

    public void setPlhjSlpt(final String plhjSlpt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjSlpt(plhjSlpt);
    }

    public void setPlhjSlpt(final IAlphanumericValue plhjSlpt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjSlpt(plhjSlpt);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjSinaloat -> 15 PLHJ-SINALOAT X(03)
    public AlphanumericVar getPlhjSinaloat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjSinaloat();
    }

    public void setPlhjSinaloat(final String plhjSinaloat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjSinaloat(plhjSinaloat);
    }

    public void setPlhjSinaloat(final IAlphanumericValue plhjSinaloat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjSinaloat(plhjSinaloat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjSonorat -> 15 PLHJ-SONORAT X(03)
    public AlphanumericVar getPlhjSonorat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjSonorat();
    }

    public void setPlhjSonorat(final String plhjSonorat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjSonorat(plhjSonorat);
    }

    public void setPlhjSonorat(final IAlphanumericValue plhjSonorat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjSonorat(plhjSonorat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjTabascot -> 15 PLHJ-TABASCOT X(03)
    public AlphanumericVar getPlhjTabascot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjTabascot();
    }

    public void setPlhjTabascot(final String plhjTabascot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjTabascot(plhjTabascot);
    }

    public void setPlhjTabascot(final IAlphanumericValue plhjTabascot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjTabascot(plhjTabascot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjTamaulipast -> 15 PLHJ-TAMAULIPAST X(03)
    public AlphanumericVar getPlhjTamaulipast() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjTamaulipast();
    }

    public void setPlhjTamaulipast(final String plhjTamaulipast) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjTamaulipast(plhjTamaulipast);
    }

    public void setPlhjTamaulipast(final IAlphanumericValue plhjTamaulipast) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjTamaulipast(plhjTamaulipast);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjTlaxcalat -> 15 PLHJ-TLAXCALAT X(03)
    public AlphanumericVar getPlhjTlaxcalat() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjTlaxcalat();
    }

    public void setPlhjTlaxcalat(final String plhjTlaxcalat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjTlaxcalat(plhjTlaxcalat);
    }

    public void setPlhjTlaxcalat(final IAlphanumericValue plhjTlaxcalat) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjTlaxcalat(plhjTlaxcalat);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjVeracruzt -> 15 PLHJ-VERACRUZT X(03)
    public AlphanumericVar getPlhjVeracruzt() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjVeracruzt();
    }

    public void setPlhjVeracruzt(final String plhjVeracruzt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjVeracruzt(plhjVeracruzt);
    }

    public void setPlhjVeracruzt(final IAlphanumericValue plhjVeracruzt) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjVeracruzt(plhjVeracruzt);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjYucatant -> 15 PLHJ-YUCATANT X(03)
    public AlphanumericVar getPlhjYucatant() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjYucatant();
    }

    public void setPlhjYucatant(final String plhjYucatant) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjYucatant(plhjYucatant);
    }

    public void setPlhjYucatant(final IAlphanumericValue plhjYucatant) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjYucatant(plhjYucatant);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjZacatecast -> 15 PLHJ-ZACATECAST X(03)
    public AlphanumericVar getPlhjZacatecast() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjZacatecast();
    }

    public void setPlhjZacatecast(final String plhjZacatecast) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjZacatecast(plhjZacatecast);
    }

    public void setPlhjZacatecast(final IAlphanumericValue plhjZacatecast) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjZacatecast(plhjZacatecast);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjExtranjerot -> 15 PLHJ-EXTRANJEROT X(03)
    public AlphanumericVar getPlhjExtranjerot() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjExtranjerot();
    }

    public void setPlhjExtranjerot(final String plhjExtranjerot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjExtranjerot(plhjExtranjerot);
    }

    public void setPlhjExtranjerot(final IAlphanumericValue plhjExtranjerot) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjExtranjerot(plhjExtranjerot);
    }

    // Property: ggqa00agcpy.plhjConstantes.plhjEstadosCveTrac.plhjOtrost -> 15 PLHJ-OTROST X(03)
    public AlphanumericVar getPlhjOtrost() {
        return this.plhjConstantes.getPlhjEstadosCveTrac().getPlhjOtrost();
    }

    public void setPlhjOtrost(final String plhjOtrost) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjOtrost(plhjOtrost);
    }

    public void setPlhjOtrost(final IAlphanumericValue plhjOtrost) {
        this.plhjConstantes.getPlhjEstadosCveTrac().setPlhjOtrost(plhjOtrost);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhjConstantes.initialize();
    }

}

