package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WT-CLAVE-POS.
 *
 */
@Setter
@Getter
public class WtClavePos extends DataStruct {
    // Properties
    private AlphanumericVarArray wtDigito = new AlphanumericVarArray(this, 4, 1);            // 15 WT-DIGITO X(01) [4]

    public WtClavePos() {
        super();
        initialize();
    }

    public WtClavePos(DataContainer parent) {
        super(parent);
    }

    public WtClavePos(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWtDigito(int index) {
        return this.wtDigito.at(index);
    }

    public void setWtDigito(final String wtDigito) {
        this.wtDigito.setValue(wtDigito);
    }

    public void setWtDigito(final AlphanumericVarArray wtDigito) {
        this.wtDigito.setValue(wtDigito);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wtDigito.clear();
    }

}

