package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: L-PARAMS.
 *
 */
@Setter
@Getter
public class LParams extends DataStruct {
    // Properties
    private AlphanumericVar lOption = new AlphanumericVar(this, 4);          // 05 L-OPTION X(04)
    private AlphanumericVar lAgente = new AlphanumericVar(this, 7);          // 05 L-AGENTE X(07)

    public LParams() {
        super();
        initialize();
    }

    public LParams(DataContainer parent) {
        super(parent);
    }

    public LParams(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setLOption(final String lOption) {
        this.lOption.setValue(lOption);
    }

    public void setLOption(final IAlphanumericValue lOption) {
        this.lOption.setValue(lOption);
    }

    public void setLAgente(final String lAgente) {
        this.lAgente.setValue(lAgente);
    }

    public void setLAgente(final IAlphanumericValue lAgente) {
        this.lAgente.setValue(lAgente);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.lOption.clear();
        this.lAgente.clear();
    }

}

