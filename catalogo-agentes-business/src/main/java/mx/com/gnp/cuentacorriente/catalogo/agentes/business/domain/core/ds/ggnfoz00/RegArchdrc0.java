package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-ARCHDRC0.
 *
 */
@Setter
@Getter
public class RegArchdrc0 extends DataStruct {
    // Properties
    private UnsignedNumericVar draCve5 = new UnsignedNumericVar(this, 5, 0);                 // 05 DRA-CVE5 9(05)
    private UnsignedNumericVar agtIdr5 = new UnsignedNumericVar(this, 9, 0);                 // 05 AGT-IDR5 9(09)
    private AlphanumericVar empCve5 = new AlphanumericVar(this, 5);          // 05 EMP-CVE5 X(05)
    private UnsignedNumericVar cnaNum5 = new UnsignedNumericVar(this, 5, 0);                 // 05 CNA-NUM5 9(05)
    private AlphanumericVar drcNomAut5 = new AlphanumericVar(this, 40);      // 05 DRC-NOM-AUT5 X(40)
    private AlphanumericVar drcFecAut5 = new AlphanumericVar(this, 10);      // 05 DRC-FEC-AUT5 X(10)
    private AlphanumericVar drcFecIniCio5 = new AlphanumericVar(this, 10);   // 05 DRC-FEC-INI-CIO5 X(10)
    private AlphanumericVar drcFecFinCio5 = new AlphanumericVar(this, 10);   // 05 DRC-FEC-FIN-CIO5 X(10)
    private AlphanumericVar filler11 = new AlphanumericVar(this, 1);         // 05 FILLER11 X(01)
    private UnsignedNumericVar drcPjeCom53 = new UnsignedNumericVar(this, 3, 0);             // 05 DRC-PJE-COM53 9(03)
    private AlphanumericVar filler12 = new AlphanumericVar(this, 1);         // 05 FILLER12 X(01)
    private UnsignedNumericVar drcPjeCom57 = new UnsignedNumericVar(this, 7, 0);             // 05 DRC-PJE-COM57 9(07)
    private AlphanumericVar drcIdrAgt5 = new AlphanumericVar(this, 1);       // 05 DRC-IDR-AGT5 X(01)
    private AlphanumericVar drcIdrGte5 = new AlphanumericVar(this, 1);       // 05 DRC-IDR-GTE5 X(01)
    private AlphanumericVar essRek5 = new AlphanumericVar(this, 1);          // 05 ESS-REK5 X(01)

    public RegArchdrc0() {
        super();
        initialize();
    }

    public RegArchdrc0(DataContainer parent) {
        super(parent);
    }

    public RegArchdrc0(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setDraCve5(final int draCve5) {
        this.draCve5.setValue(draCve5);
    }

    public void setDraCve5(final BigDecimal draCve5) {
        this.draCve5.setValue(draCve5);
    }

    public void setDraCve5(final INumericValue draCve5) {
        this.draCve5.setValue(draCve5);
    }

    public void setAgtIdr5(final int agtIdr5) {
        this.agtIdr5.setValue(agtIdr5);
    }

    public void setAgtIdr5(final BigDecimal agtIdr5) {
        this.agtIdr5.setValue(agtIdr5);
    }

    public void setAgtIdr5(final INumericValue agtIdr5) {
        this.agtIdr5.setValue(agtIdr5);
    }

    public void setEmpCve5(final String empCve5) {
        this.empCve5.setValue(empCve5);
    }

    public void setEmpCve5(final IAlphanumericValue empCve5) {
        this.empCve5.setValue(empCve5);
    }

    public void setCnaNum5(final int cnaNum5) {
        this.cnaNum5.setValue(cnaNum5);
    }

    public void setCnaNum5(final BigDecimal cnaNum5) {
        this.cnaNum5.setValue(cnaNum5);
    }

    public void setCnaNum5(final INumericValue cnaNum5) {
        this.cnaNum5.setValue(cnaNum5);
    }

    public void setDrcNomAut5(final String drcNomAut5) {
        this.drcNomAut5.setValue(drcNomAut5);
    }

    public void setDrcNomAut5(final IAlphanumericValue drcNomAut5) {
        this.drcNomAut5.setValue(drcNomAut5);
    }

    public void setDrcFecAut5(final String drcFecAut5) {
        this.drcFecAut5.setValue(drcFecAut5);
    }

    public void setDrcFecAut5(final IAlphanumericValue drcFecAut5) {
        this.drcFecAut5.setValue(drcFecAut5);
    }

    public void setDrcFecIniCio5(final String drcFecIniCio5) {
        this.drcFecIniCio5.setValue(drcFecIniCio5);
    }

    public void setDrcFecIniCio5(final IAlphanumericValue drcFecIniCio5) {
        this.drcFecIniCio5.setValue(drcFecIniCio5);
    }

    public void setDrcFecFinCio5(final String drcFecFinCio5) {
        this.drcFecFinCio5.setValue(drcFecFinCio5);
    }

    public void setDrcFecFinCio5(final IAlphanumericValue drcFecFinCio5) {
        this.drcFecFinCio5.setValue(drcFecFinCio5);
    }

    public void setFiller11(final String filler11) {
        this.filler11.setValue(filler11);
    }

    public void setFiller11(final IAlphanumericValue filler11) {
        this.filler11.setValue(filler11);
    }

    public void setDrcPjeCom53(final int drcPjeCom53) {
        this.drcPjeCom53.setValue(drcPjeCom53);
    }

    public void setDrcPjeCom53(final BigDecimal drcPjeCom53) {
        this.drcPjeCom53.setValue(drcPjeCom53);
    }

    public void setDrcPjeCom53(final INumericValue drcPjeCom53) {
        this.drcPjeCom53.setValue(drcPjeCom53);
    }

    public void setFiller12(final String filler12) {
        this.filler12.setValue(filler12);
    }

    public void setFiller12(final IAlphanumericValue filler12) {
        this.filler12.setValue(filler12);
    }

    public void setDrcPjeCom57(final int drcPjeCom57) {
        this.drcPjeCom57.setValue(drcPjeCom57);
    }

    public void setDrcPjeCom57(final BigDecimal drcPjeCom57) {
        this.drcPjeCom57.setValue(drcPjeCom57);
    }

    public void setDrcPjeCom57(final INumericValue drcPjeCom57) {
        this.drcPjeCom57.setValue(drcPjeCom57);
    }

    public void setDrcIdrAgt5(final String drcIdrAgt5) {
        this.drcIdrAgt5.setValue(drcIdrAgt5);
    }

    public void setDrcIdrAgt5(final IAlphanumericValue drcIdrAgt5) {
        this.drcIdrAgt5.setValue(drcIdrAgt5);
    }

    public void setDrcIdrGte5(final String drcIdrGte5) {
        this.drcIdrGte5.setValue(drcIdrGte5);
    }

    public void setDrcIdrGte5(final IAlphanumericValue drcIdrGte5) {
        this.drcIdrGte5.setValue(drcIdrGte5);
    }

    public void setEssRek5(final String essRek5) {
        this.essRek5.setValue(essRek5);
    }

    public void setEssRek5(final IAlphanumericValue essRek5) {
        this.essRek5.setValue(essRek5);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.draCve5.clear();
        this.agtIdr5.clear();
        this.empCve5.clear();
        this.cnaNum5.clear();
        this.drcNomAut5.clear();
        this.drcFecAut5.clear();
        this.drcFecIniCio5.clear();
        this.drcFecFinCio5.clear();
        this.filler11.clear();
        this.drcPjeCom53.clear();
        this.filler12.clear();
        this.drcPjeCom57.clear();
        this.drcIdrAgt5.clear();
        this.drcIdrGte5.clear();
        this.essRek5.clear();
    }

}

