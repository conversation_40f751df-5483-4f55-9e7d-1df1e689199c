package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CURRENT.
 *
 */
// 789052100*                                                                 05210002
// 790052200*                                                                 05220002
// 791052300*                                                                 05230002
@Setter
@Getter
public class WsCurrent { // VStruct
    // Properties
    private int currDd;                                                     // 05 CURR-DD 99
    private String filler1 = "";                                            // 05 FILLER X
    private int currMm;                                                     // 05 CURR-MM 99
    private String filler2 = "";                                            // 05 FILLER X
    private int currAa;                                                     // 05 CURR-AA 99

    public WsCurrent() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.currDd = 0;
        this.filler1 = "";
        this.currMm = 0;
        this.filler2 = "";
        this.currAa = 0;
    }

}

