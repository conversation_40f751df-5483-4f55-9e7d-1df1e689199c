package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TAB-APE-PATR.
 *
 */
@Setter
@Getter
public class WsTabApePatr extends DataStruct {
    // Properties
    private AlphanumericVarArray eleApePat = new AlphanumericVarArray(this, 40, 1);          // 05 ELE-APE-PAT X [40]

    public WsTabApePatr() {
        super();
        initialize();
    }

    public WsTabApePatr(DataContainer parent) {
        super(parent);
    }

    public WsTabApePatr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getEleApePat(int index) {
        return this.eleApePat.at(index);
    }

    public void setEleApePat(final String eleApePat) {
        this.eleApePat.setValue(eleApePat);
    }

    public void setEleApePat(final AlphanumericVarArray eleApePat) {
        this.eleApePat.setValue(eleApePat);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.eleApePat.clear();
    }

}

