package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: CONTADORES.
 *
 */
@Setter
@Getter
public class Contadores { // VStruct
    // Properties
    private int contadorProc = 0;                                           // 10 CONTADOR-PROC 9(07)
    private int wsContador = 0;                                             // 10 WS-CONTADOR 9(03)
    private int wsContTcna = 0;                                             // 10 WS-CONT-TCNA 9(03)
    private int wsContTcna_1 = 0;                                           // 10 WS-CONT-TCNA-1 9(03)
    private int wsContTfla = 0;                                             // 10 WS-CONT-TFLA 9(02)
    private int wsContTger = 0;                                             // 10 WS-CONT-TGER 9(02)
    private int wsContTger_1 = 0;                                           // 10 WS-CONT-TGER-1 9(03)
    private int wsContTceo = 0;                                             // 10 WS-CONT-TCEO 9(02)
    private int wsContTofn = 0;                                             // 10 WS-CONT-TOFN 9(02)
    private int wsContTofn_1 = 0;                                           // 10 WS-CONT-TOFN-1 9(03)
    private int wsContAgt0 = 0;                                             // 10 WS-CONT-AGT0 9(04)
    private int wsContTfin = 0;                                             // 10 WS-CONT-TFIN 9(04)
    private int wsContFolios = 0;                                           // 10 WS-CONT-FOLIOS 9(04)
    private int wsContGrabas = 0;                                           // 10 WS-CONT-GRABAS 9(04)
    private NumericEditedVar wsEditLeidos = new NumericEditedVar("ZZZZZZZ,ZZZ,ZZZ");         // 10 WS-EDIT-LEIDOS ZZZZZZZ,ZZZ,ZZZ
    private NumericEditedVar wsEditGrabado = new NumericEditedVar("ZZZZZZZ,ZZZ,ZZZ");        // 10 WS-EDIT-GRABADO ZZZZZZZ,ZZZ,ZZZ

    public Contadores() {
        initialize();
    }



    public void setWsEditLeidos(final String wsEditLeidos) {
        this.wsEditLeidos.setValue(wsEditLeidos);
    }

    public void setWsEditLeidos(final IValue wsEditLeidos) {
        this.wsEditLeidos.setValue(wsEditLeidos);
    }

    public void setWsEditGrabado(final String wsEditGrabado) {
        this.wsEditGrabado.setValue(wsEditGrabado);
    }

    public void setWsEditGrabado(final IValue wsEditGrabado) {
        this.wsEditGrabado.setValue(wsEditGrabado);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.contadorProc = 0;
        this.wsContador = 0;
        this.wsContTcna = 0;
        this.wsContTcna_1 = 0;
        this.wsContTfla = 0;
        this.wsContTger = 0;
        this.wsContTger_1 = 0;
        this.wsContTceo = 0;
        this.wsContTofn = 0;
        this.wsContTofn_1 = 0;
        this.wsContAgt0 = 0;
        this.wsContTfin = 0;
        this.wsContFolios = 0;
        this.wsContGrabas = 0;
        this.wsEditLeidos.clear();
        this.wsEditGrabado.clear();
    }

}

