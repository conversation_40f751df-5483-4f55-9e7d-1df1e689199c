package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.wsfecaaaa;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AAAA.
 *
 */
@Setter
@Getter
public class WsAaaa extends DataStruct {
    // Properties
    private UnsignedNumericVar wsAa1 = new UnsignedNumericVar(this, 2, 0);   // 05 WS-AA1 99
    private UnsignedNumericVar wsAa2 = new UnsignedNumericVar(this, 2, 0);   // 05 WS-AA2 99

    public WsAaaa() {
        super();
        initialize();
    }

    public WsAaaa(DataContainer parent) {
        super(parent);
    }

    public WsAaaa(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAa1(final int wsAa1) {
        this.wsAa1.setValue(wsAa1);
    }

    public void setWsAa1(final BigDecimal wsAa1) {
        this.wsAa1.setValue(wsAa1);
    }

    public void setWsAa1(final INumericValue wsAa1) {
        this.wsAa1.setValue(wsAa1);
    }

    public void setWsAa2(final int wsAa2) {
        this.wsAa2.setValue(wsAa2);
    }

    public void setWsAa2(final BigDecimal wsAa2) {
        this.wsAa2.setValue(wsAa2);
    }

    public void setWsAa2(final INumericValue wsAa2) {
        this.wsAa2.setValue(wsAa2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAa1.setValue(19);
        this.wsAa2.clear();
    }

}

