package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.indicadoresdenulos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICADORES-CNA.
 *
 */
@Setter
@Getter
public class IndicadoresCna { // VStruct
    // Properties
    private int indcnatfp = 0;                                              // 05 INDCNATFP S9(4) COMPUTATIONAL
    private int indcnamte = 0;                                              // 05 INDCNAMTE S9(4) COMPUTATIONAL
    private int indcnactbnum = 0;                                           // 05 INDCNACTBNUM S9(4) COMPUTATIONAL

    public IndicadoresCna() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.indcnatfp = 0;
        this.indcnamte = 0;
        this.indcnactbnum = 0;
    }

}

