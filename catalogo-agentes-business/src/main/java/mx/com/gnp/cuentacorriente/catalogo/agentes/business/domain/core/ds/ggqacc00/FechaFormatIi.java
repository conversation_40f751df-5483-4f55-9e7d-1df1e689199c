package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: FECHA-FORMAT-II.
 *
 */
@Setter
@Getter
public class FechaFormatIi extends DataStruct {
    // Properties
    private UnsignedNumericVar fechaFormatIiAa = new UnsignedNumericVar(this, 4, 0);         // 05 FECHA-FORMAT-II-AA 9(04)
    private AlphanumericVar fechaFormatIiF1 = new AlphanumericVar(this, 1);  // 05 FECHA-FORMAT-II-F1 X(01)
    private UnsignedNumericVar fechaFormatIiMm = new UnsignedNumericVar(this, 2, 0);         // 05 FECHA-FORMAT-II-MM 9(02)
    private AlphanumericVar fechaFormatIiF2 = new AlphanumericVar(this, 1);  // 05 FECHA-FORMAT-II-F2 X(01)
    private UnsignedNumericVar fechaFormatIiDd = new UnsignedNumericVar(this, 2, 0);         // 05 FECHA-FORMAT-II-DD 9(02)

    public FechaFormatIi() {
        super();
        initialize();
    }

    public FechaFormatIi(DataContainer parent) {
        super(parent);
    }

    public FechaFormatIi(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFechaFormatIiAa(final int fechaFormatIiAa) {
        this.fechaFormatIiAa.setValue(fechaFormatIiAa);
    }

    public void setFechaFormatIiAa(final BigDecimal fechaFormatIiAa) {
        this.fechaFormatIiAa.setValue(fechaFormatIiAa);
    }

    public void setFechaFormatIiAa(final INumericValue fechaFormatIiAa) {
        this.fechaFormatIiAa.setValue(fechaFormatIiAa);
    }

    public void setFechaFormatIiF1(final String fechaFormatIiF1) {
        this.fechaFormatIiF1.setValue(fechaFormatIiF1);
    }

    public void setFechaFormatIiF1(final IAlphanumericValue fechaFormatIiF1) {
        this.fechaFormatIiF1.setValue(fechaFormatIiF1);
    }

    public void setFechaFormatIiMm(final int fechaFormatIiMm) {
        this.fechaFormatIiMm.setValue(fechaFormatIiMm);
    }

    public void setFechaFormatIiMm(final BigDecimal fechaFormatIiMm) {
        this.fechaFormatIiMm.setValue(fechaFormatIiMm);
    }

    public void setFechaFormatIiMm(final INumericValue fechaFormatIiMm) {
        this.fechaFormatIiMm.setValue(fechaFormatIiMm);
    }

    public void setFechaFormatIiF2(final String fechaFormatIiF2) {
        this.fechaFormatIiF2.setValue(fechaFormatIiF2);
    }

    public void setFechaFormatIiF2(final IAlphanumericValue fechaFormatIiF2) {
        this.fechaFormatIiF2.setValue(fechaFormatIiF2);
    }

    public void setFechaFormatIiDd(final int fechaFormatIiDd) {
        this.fechaFormatIiDd.setValue(fechaFormatIiDd);
    }

    public void setFechaFormatIiDd(final BigDecimal fechaFormatIiDd) {
        this.fechaFormatIiDd.setValue(fechaFormatIiDd);
    }

    public void setFechaFormatIiDd(final INumericValue fechaFormatIiDd) {
        this.fechaFormatIiDd.setValue(fechaFormatIiDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.fechaFormatIiAa.clear();
        this.fechaFormatIiF1.setValue("-");
        this.fechaFormatIiMm.clear();
        this.fechaFormatIiF2.setValue("-");
        this.fechaFormatIiDd.clear();
    }

}

