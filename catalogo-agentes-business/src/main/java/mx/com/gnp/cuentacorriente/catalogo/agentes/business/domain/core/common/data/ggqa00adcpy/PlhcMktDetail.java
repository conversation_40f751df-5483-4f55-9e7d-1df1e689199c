package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.string.StringUtils;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PLHC-MKT-DETAIL.
 *
 */
// 541LUV001*=>     0844-00844                                                00300020
// 542      *                                                                 00310000
@Setter
@Getter
public class PlhcMktDetail extends DataStruct {
    // Properties
    private AlphanumericVar plhcMktCompanyD = new AlphanumericVar(this, 5);  // 10 PLHC-MKT-COMPANY-D X(05)
    private AlphanumericVar plhcMktAgentId = new AlphanumericVar(this, 5);   // 10 PLHC-MKT-AGENT-ID X(05)
    private AlphanumericVar filler5 = new AlphanumericVar(this, 4);          // 10 FILLER X(04)
    private AlphanumericVar plhcMktAgencyId = new AlphanumericVar(this, 3);  // 10 PLHC-MKT-AGENCY-ID X(03)
    private AlphanumericVar filler6 = new AlphanumericVar(this, 6);          // 10 FILLER X(06)
    private AlphanumericVar plhcMktSsn = new AlphanumericVar(this, 13);      // 10 PLHC-MKT-SSN X(13)
    private AlphanumericVar plhcMktNasd = new AlphanumericVar(this, 10);     // 10 PLHC-MKT-NASD X(10)
    private AlphanumericVar plhcMktSex = new AlphanumericVar(this, 1);       // 10 PLHC-MKT-SEX X(01)
    private UnsignedNumericVar plhcMktHireDate = new UnsignedNumericVar(this, 8, 0);         // 10 PLHC-MKT-HIRE-DATE 9(08)
    private AlphanumericVar plhcMktStatus = new AlphanumericVar(this, 1);    // 10 PLHC-MKT-STATUS X(01)
    private UnsignedNumericVar plhcMktTermDate = new UnsignedNumericVar(this, 8, 0);         // 10 PLHC-MKT-TERM-DATE 9(08)
    private UnsignedNumericVar plhcMktDob = new UnsignedNumericVar(this, 8, 0);              // 10 PLHC-MKT-DOB 9(08)
    private AlphanumericVar plhcMktCorpName = new AlphanumericVar(this, 60);                 // 10 PLHC-MKT-CORP-NAME X(60)
    private AlphanumericVar plhcMktNameLast = new AlphanumericVar(this, 40);                 // 10 PLHC-MKT-NAME-LAST X(40)
    private AlphanumericVar plhcMktNameFirst = new AlphanumericVar(this, 20);                // 10 PLHC-MKT-NAME-FIRST X(20)
    private AlphanumericVar plhcMktAddressOne = new AlphanumericVar(this, 35);               // 10 PLHC-MKT-ADDRESS-ONE X(35)
    private AlphanumericVar plhcMktAddressTwo = new AlphanumericVar(this, 35);               // 10 PLHC-MKT-ADDRESS-TWO X(35)
    private AlphanumericVar plhcMktAddressThree = new AlphanumericVar(this, 35);             // 10 PLHC-MKT-ADDRESS-THREE X(35)
    private AlphanumericVar plhcMktAddressFour = new AlphanumericVar(this, 35);              // 10 PLHC-MKT-ADDRESS-FOUR X(35)
    private AlphanumericVar plhcMktAddressState = new AlphanumericVar(this, 3);              // 10 PLHC-MKT-ADDRESS-STATE X(03)
    private AlphanumericVar plhcMktZipFive = new AlphanumericVar(this, 5);   // 10 PLHC-MKT-ZIP-FIVE X(05)
    private AlphanumericVar plhcMktPdcFive = new AlphanumericVar(this, 5);   // 10 PLHC-MKT-PDC-FIVE X(05)
    private AlphanumericVar plhcMktPhone = new AlphanumericVar(this, 14);    // 10 PLHC-MKT-PHONE X(14)
    private AlphanumericVar plhcMktCorpNamea = new AlphanumericVar(this, 60);                // 10 PLHC-MKT-CORP-NAMEA X(60)
    private AlphanumericVar plhcMktNameLasta = new AlphanumericVar(this, 40);                // 10 PLHC-MKT-NAME-LASTA X(40)
    private AlphanumericVar plhcMktNameFirsta = new AlphanumericVar(this, 20);               // 10 PLHC-MKT-NAME-FIRSTA X(20)
    private AlphanumericVar plhcMktAddressOnea = new AlphanumericVar(this, 35);              // 10 PLHC-MKT-ADDRESS-ONEA X(35)
    private AlphanumericVar plhcMktAddressTwoa = new AlphanumericVar(this, 35);              // 10 PLHC-MKT-ADDRESS-TWOA X(35)
    private AlphanumericVar plhcMktAddressThreea = new AlphanumericVar(this, 35);            // 10 PLHC-MKT-ADDRESS-THREEA X(35)
    private AlphanumericVar plhcMktAddressFoura = new AlphanumericVar(this, 35);             // 10 PLHC-MKT-ADDRESS-FOURA X(35)
    private AlphanumericVar plhcMktAddressStatea = new AlphanumericVar(this, 3);             // 10 PLHC-MKT-ADDRESS-STATEA X(03)
    private AlphanumericVar plhcMktZipFivea = new AlphanumericVar(this, 5);  // 10 PLHC-MKT-ZIP-FIVEA X(05)
    private AlphanumericVar plhcMktPdcFivea = new AlphanumericVar(this, 5);  // 10 PLHC-MKT-PDC-FIVEA X(05)
    private AlphanumericVar plhcMktPhonea = new AlphanumericVar(this, 14);   // 10 PLHC-MKT-PHONEA X(14)
    private UnsignedNumericVar plhcMktNasdEffDate = new UnsignedNumericVar(this, 8, 0);      // 10 PLHC-MKT-NASD-EFF-DATE 9(08)
    private UnsignedNumericVar plhcMktNasdRenDate = new UnsignedNumericVar(this, 8, 0);      // 10 PLHC-MKT-NASD-REN-DATE 9(08)
    private AlphanumericVar plhcMktLicName = new AlphanumericVar(this, 50);  // 10 PLHC-MKT-LIC-NAME X(50)
    private AlphanumericVar plhcMktLevel = new AlphanumericVar(this, 2);     // 10 PLHC-MKT-LEVEL X(02)
    private AlphanumericVar plhcMktPayOption = new AlphanumericVar(this, 5);                 // 10 PLHC-MKT-PAY-OPTION X(05)
    private AlphanumericVar plhcMktChain = new AlphanumericVar(this, 10);    // 10 PLHC-MKT-CHAIN X(10)
    private AlphanumericVar plhcMktCommIndex = new AlphanumericVar(this, 6);                 // 10 PLHC-MKT-COMM-INDEX X(06)
    private AlphanumericVar plhcMktPayMethod = new AlphanumericVar(this, 1);                 // 10 PLHC-MKT-PAY-METHOD X(01)
    private AlphanumericVar plhcMktPayFreq = new AlphanumericVar(this, 2);   // 10 PLHC-MKT-PAY-FREQ X(02)
    private AlphanumericVar filler7 = new AlphanumericVar(this, 78);         // 10 FILLER X(78)
    private AlphanumericVar plhcMktUniqueAgentCode = new AlphanumericVar(this, 8);           // 10 PLHC-MKT-UNIQUE-AGENT-CODE X(08)
    private AlphanumericVar filler8 = new AlphanumericVar(this, 8);          // 10 FILLER X(08)
    private AlphanumericVar plhcMktSortInd = new AlphanumericVar(this, 1);   // 10 PLHC-MKT-SORT-IND X(01)
    private AlphanumericVar filler9 = new AlphanumericVar(this, 10);         // 10 FILLER X(10)
    private AlphanumericVar plhcMktEndrec = new AlphanumericVar(this, 1);    // 10 PLHC-MKT-ENDREC X(01)

    public PlhcMktDetail() {
        super();
        initialize();
    }

    public PlhcMktDetail(DataContainer parent) {
        super(parent);
    }

    public PlhcMktDetail(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPlhcMktCompanyD(final String plhcMktCompanyD) {
        this.plhcMktCompanyD.setValue(plhcMktCompanyD);
    }

    public void setPlhcMktCompanyD(final IAlphanumericValue plhcMktCompanyD) {
        this.plhcMktCompanyD.setValue(plhcMktCompanyD);
    }

    public void setPlhcMktAgentId(final String plhcMktAgentId) {
        this.plhcMktAgentId.setValue(plhcMktAgentId);
    }

    public void setPlhcMktAgentId(final IAlphanumericValue plhcMktAgentId) {
        this.plhcMktAgentId.setValue(plhcMktAgentId);
    }

    public void setFiller5(final String filler5) {
        this.filler5.setValue(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.filler5.setValue(filler5);
    }

    public void setPlhcMktAgencyId(final String plhcMktAgencyId) {
        this.plhcMktAgencyId.setValue(plhcMktAgencyId);
    }

    public void setPlhcMktAgencyId(final IAlphanumericValue plhcMktAgencyId) {
        this.plhcMktAgencyId.setValue(plhcMktAgencyId);
    }

    public void setFiller6(final String filler6) {
        this.filler6.setValue(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.filler6.setValue(filler6);
    }

    public void setPlhcMktSsn(final String plhcMktSsn) {
        this.plhcMktSsn.setValue(plhcMktSsn);
    }

    public void setPlhcMktSsn(final IAlphanumericValue plhcMktSsn) {
        this.plhcMktSsn.setValue(plhcMktSsn);
    }

    public void setPlhcMktNasd(final String plhcMktNasd) {
        this.plhcMktNasd.setValue(plhcMktNasd);
    }

    public void setPlhcMktNasd(final IAlphanumericValue plhcMktNasd) {
        this.plhcMktNasd.setValue(plhcMktNasd);
    }

    public void setPlhcMktSex(final String plhcMktSex) {
        this.plhcMktSex.setValue(plhcMktSex);
    }

    public void setPlhcMktSex(final IAlphanumericValue plhcMktSex) {
        this.plhcMktSex.setValue(plhcMktSex);
    }

    public void setPlhcMktHireDate(final int plhcMktHireDate) {
        this.plhcMktHireDate.setValue(plhcMktHireDate);
    }

    public void setPlhcMktHireDate(final BigDecimal plhcMktHireDate) {
        this.plhcMktHireDate.setValue(plhcMktHireDate);
    }

    public void setPlhcMktHireDate(final INumericValue plhcMktHireDate) {
        this.plhcMktHireDate.setValue(plhcMktHireDate);
    }

    public void setPlhcMktStatus(final String plhcMktStatus) {
        this.plhcMktStatus.setValue(plhcMktStatus);
    }

    public void setPlhcMktStatus(final IAlphanumericValue plhcMktStatus) {
        this.plhcMktStatus.setValue(plhcMktStatus);
    }

    public void setPlhcMktTermDate(final int plhcMktTermDate) {
        this.plhcMktTermDate.setValue(plhcMktTermDate);
    }

    public void setPlhcMktTermDate(final BigDecimal plhcMktTermDate) {
        this.plhcMktTermDate.setValue(plhcMktTermDate);
    }

    public void setPlhcMktTermDate(final INumericValue plhcMktTermDate) {
        this.plhcMktTermDate.setValue(plhcMktTermDate);
    }

    public void setPlhcMktDob(final int plhcMktDob) {
        this.plhcMktDob.setValue(plhcMktDob);
    }

    public void setPlhcMktDob(final BigDecimal plhcMktDob) {
        this.plhcMktDob.setValue(plhcMktDob);
    }

    public void setPlhcMktDob(final INumericValue plhcMktDob) {
        this.plhcMktDob.setValue(plhcMktDob);
    }

    public void setPlhcMktCorpName(final String plhcMktCorpName) {
        this.plhcMktCorpName.setValue(plhcMktCorpName);
    }

    public void setPlhcMktCorpName(final IAlphanumericValue plhcMktCorpName) {
        this.plhcMktCorpName.setValue(plhcMktCorpName);
    }

    public void setPlhcMktNameLast(final String plhcMktNameLast) {
        this.plhcMktNameLast.setValue(plhcMktNameLast);
    }

    public void setPlhcMktNameLast(final IAlphanumericValue plhcMktNameLast) {
        this.plhcMktNameLast.setValue(plhcMktNameLast);
    }

    public void setPlhcMktNameFirst(final String plhcMktNameFirst) {
        this.plhcMktNameFirst.setValue(plhcMktNameFirst);
    }

    public void setPlhcMktNameFirst(final IAlphanumericValue plhcMktNameFirst) {
        this.plhcMktNameFirst.setValue(plhcMktNameFirst);
    }

    public void setPlhcMktAddressOne(final String plhcMktAddressOne) {
        this.plhcMktAddressOne.setValue(plhcMktAddressOne);
    }

    public void setPlhcMktAddressOne(final IAlphanumericValue plhcMktAddressOne) {
        this.plhcMktAddressOne.setValue(plhcMktAddressOne);
    }

    public void setPlhcMktAddressTwo(final String plhcMktAddressTwo) {
        this.plhcMktAddressTwo.setValue(plhcMktAddressTwo);
    }

    public void setPlhcMktAddressTwo(final IAlphanumericValue plhcMktAddressTwo) {
        this.plhcMktAddressTwo.setValue(plhcMktAddressTwo);
    }

    public void setPlhcMktAddressThree(final String plhcMktAddressThree) {
        this.plhcMktAddressThree.setValue(plhcMktAddressThree);
    }

    public void setPlhcMktAddressThree(final IAlphanumericValue plhcMktAddressThree) {
        this.plhcMktAddressThree.setValue(plhcMktAddressThree);
    }

    public void setPlhcMktAddressFour(final String plhcMktAddressFour) {
        this.plhcMktAddressFour.setValue(plhcMktAddressFour);
    }

    public void setPlhcMktAddressFour(final IAlphanumericValue plhcMktAddressFour) {
        this.plhcMktAddressFour.setValue(plhcMktAddressFour);
    }

    public void setPlhcMktAddressState(final String plhcMktAddressState) {
        this.plhcMktAddressState.setValue(plhcMktAddressState);
    }

    public void setPlhcMktAddressState(final IAlphanumericValue plhcMktAddressState) {
        this.plhcMktAddressState.setValue(plhcMktAddressState);
    }

    public void setPlhcMktZipFive(final String plhcMktZipFive) {
        this.plhcMktZipFive.setValue(plhcMktZipFive);
    }

    public void setPlhcMktZipFive(final IAlphanumericValue plhcMktZipFive) {
        this.plhcMktZipFive.setValue(plhcMktZipFive);
    }

    public void setPlhcMktPdcFive(final String plhcMktPdcFive) {
        this.plhcMktPdcFive.setValue(plhcMktPdcFive);
    }

    public void setPlhcMktPdcFive(final IAlphanumericValue plhcMktPdcFive) {
        this.plhcMktPdcFive.setValue(plhcMktPdcFive);
    }

    public void setPlhcMktPhone(final String plhcMktPhone) {
        this.plhcMktPhone.setValue(plhcMktPhone);
    }

    public void setPlhcMktPhone(final IAlphanumericValue plhcMktPhone) {
        this.plhcMktPhone.setValue(plhcMktPhone);
    }

    public void setPlhcMktCorpNamea(final String plhcMktCorpNamea) {
        this.plhcMktCorpNamea.setValue(plhcMktCorpNamea);
    }

    public void setPlhcMktCorpNamea(final IAlphanumericValue plhcMktCorpNamea) {
        this.plhcMktCorpNamea.setValue(plhcMktCorpNamea);
    }

    public void setPlhcMktNameLasta(final String plhcMktNameLasta) {
        this.plhcMktNameLasta.setValue(plhcMktNameLasta);
    }

    public void setPlhcMktNameLasta(final IAlphanumericValue plhcMktNameLasta) {
        this.plhcMktNameLasta.setValue(plhcMktNameLasta);
    }

    public void setPlhcMktNameFirsta(final String plhcMktNameFirsta) {
        this.plhcMktNameFirsta.setValue(plhcMktNameFirsta);
    }

    public void setPlhcMktNameFirsta(final IAlphanumericValue plhcMktNameFirsta) {
        this.plhcMktNameFirsta.setValue(plhcMktNameFirsta);
    }

    public void setPlhcMktAddressOnea(final String plhcMktAddressOnea) {
        this.plhcMktAddressOnea.setValue(plhcMktAddressOnea);
    }

    public void setPlhcMktAddressOnea(final IAlphanumericValue plhcMktAddressOnea) {
        this.plhcMktAddressOnea.setValue(plhcMktAddressOnea);
    }

    public void setPlhcMktAddressTwoa(final String plhcMktAddressTwoa) {
        this.plhcMktAddressTwoa.setValue(plhcMktAddressTwoa);
    }

    public void setPlhcMktAddressTwoa(final IAlphanumericValue plhcMktAddressTwoa) {
        this.plhcMktAddressTwoa.setValue(plhcMktAddressTwoa);
    }

    public void setPlhcMktAddressThreea(final String plhcMktAddressThreea) {
        this.plhcMktAddressThreea.setValue(plhcMktAddressThreea);
    }

    public void setPlhcMktAddressThreea(final IAlphanumericValue plhcMktAddressThreea) {
        this.plhcMktAddressThreea.setValue(plhcMktAddressThreea);
    }

    public void setPlhcMktAddressFoura(final String plhcMktAddressFoura) {
        this.plhcMktAddressFoura.setValue(plhcMktAddressFoura);
    }

    public void setPlhcMktAddressFoura(final IAlphanumericValue plhcMktAddressFoura) {
        this.plhcMktAddressFoura.setValue(plhcMktAddressFoura);
    }

    public void setPlhcMktAddressStatea(final String plhcMktAddressStatea) {
        this.plhcMktAddressStatea.setValue(plhcMktAddressStatea);
    }

    public void setPlhcMktAddressStatea(final IAlphanumericValue plhcMktAddressStatea) {
        this.plhcMktAddressStatea.setValue(plhcMktAddressStatea);
    }

    public void setPlhcMktZipFivea(final String plhcMktZipFivea) {
        this.plhcMktZipFivea.setValue(plhcMktZipFivea);
    }

    public void setPlhcMktZipFivea(final IAlphanumericValue plhcMktZipFivea) {
        this.plhcMktZipFivea.setValue(plhcMktZipFivea);
    }

    public void setPlhcMktPdcFivea(final String plhcMktPdcFivea) {
        this.plhcMktPdcFivea.setValue(plhcMktPdcFivea);
    }

    public void setPlhcMktPdcFivea(final IAlphanumericValue plhcMktPdcFivea) {
        this.plhcMktPdcFivea.setValue(plhcMktPdcFivea);
    }

    public void setPlhcMktPhonea(final String plhcMktPhonea) {
        this.plhcMktPhonea.setValue(plhcMktPhonea);
    }

    public void setPlhcMktPhonea(final IAlphanumericValue plhcMktPhonea) {
        this.plhcMktPhonea.setValue(plhcMktPhonea);
    }

    public void setPlhcMktNasdEffDate(final int plhcMktNasdEffDate) {
        this.plhcMktNasdEffDate.setValue(plhcMktNasdEffDate);
    }

    public void setPlhcMktNasdEffDate(final BigDecimal plhcMktNasdEffDate) {
        this.plhcMktNasdEffDate.setValue(plhcMktNasdEffDate);
    }

    public void setPlhcMktNasdEffDate(final INumericValue plhcMktNasdEffDate) {
        this.plhcMktNasdEffDate.setValue(plhcMktNasdEffDate);
    }

    public void setPlhcMktNasdRenDate(final int plhcMktNasdRenDate) {
        this.plhcMktNasdRenDate.setValue(plhcMktNasdRenDate);
    }

    public void setPlhcMktNasdRenDate(final BigDecimal plhcMktNasdRenDate) {
        this.plhcMktNasdRenDate.setValue(plhcMktNasdRenDate);
    }

    public void setPlhcMktNasdRenDate(final INumericValue plhcMktNasdRenDate) {
        this.plhcMktNasdRenDate.setValue(plhcMktNasdRenDate);
    }

    public void setPlhcMktLicName(final String plhcMktLicName) {
        this.plhcMktLicName.setValue(plhcMktLicName);
    }

    public void setPlhcMktLicName(final IAlphanumericValue plhcMktLicName) {
        this.plhcMktLicName.setValue(plhcMktLicName);
    }

    public void setPlhcMktLevel(final String plhcMktLevel) {
        this.plhcMktLevel.setValue(plhcMktLevel);
    }

    public void setPlhcMktLevel(final IAlphanumericValue plhcMktLevel) {
        this.plhcMktLevel.setValue(plhcMktLevel);
    }

    public void setPlhcMktPayOption(final String plhcMktPayOption) {
        this.plhcMktPayOption.setValue(plhcMktPayOption);
    }

    public void setPlhcMktPayOption(final IAlphanumericValue plhcMktPayOption) {
        this.plhcMktPayOption.setValue(plhcMktPayOption);
    }

    public void setPlhcMktChain(final String plhcMktChain) {
        this.plhcMktChain.setValue(plhcMktChain);
    }

    public void setPlhcMktChain(final IAlphanumericValue plhcMktChain) {
        this.plhcMktChain.setValue(plhcMktChain);
    }

    public void setPlhcMktCommIndex(final String plhcMktCommIndex) {
        this.plhcMktCommIndex.setValue(plhcMktCommIndex);
    }

    public void setPlhcMktCommIndex(final IAlphanumericValue plhcMktCommIndex) {
        this.plhcMktCommIndex.setValue(plhcMktCommIndex);
    }

    public void setPlhcMktPayMethod(final String plhcMktPayMethod) {
        this.plhcMktPayMethod.setValue(plhcMktPayMethod);
    }

    public void setPlhcMktPayMethod(final IAlphanumericValue plhcMktPayMethod) {
        this.plhcMktPayMethod.setValue(plhcMktPayMethod);
    }

    public void setPlhcMktPayFreq(final String plhcMktPayFreq) {
        this.plhcMktPayFreq.setValue(plhcMktPayFreq);
    }

    public void setPlhcMktPayFreq(final IAlphanumericValue plhcMktPayFreq) {
        this.plhcMktPayFreq.setValue(plhcMktPayFreq);
    }

    public void setFiller7(final String filler7) {
        this.filler7.setValue(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.filler7.setValue(filler7);
    }

    public void setPlhcMktUniqueAgentCode(final String plhcMktUniqueAgentCode) {
        this.plhcMktUniqueAgentCode.setValue(plhcMktUniqueAgentCode);
    }

    public void setPlhcMktUniqueAgentCode(final IAlphanumericValue plhcMktUniqueAgentCode) {
        this.plhcMktUniqueAgentCode.setValue(plhcMktUniqueAgentCode);
    }

    public void setFiller8(final String filler8) {
        this.filler8.setValue(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.filler8.setValue(filler8);
    }

    public void setPlhcMktSortInd(final String plhcMktSortInd) {
        this.plhcMktSortInd.setValue(plhcMktSortInd);
    }

    public void setPlhcMktSortInd(final IAlphanumericValue plhcMktSortInd) {
        this.plhcMktSortInd.setValue(plhcMktSortInd);
    }

    public void setFiller9(final String filler9) {
        this.filler9.setValue(filler9);
    }

    public void setFiller9(final IAlphanumericValue filler9) {
        this.filler9.setValue(filler9);
    }

    public void setPlhcMktEndrec(final String plhcMktEndrec) {
        this.plhcMktEndrec.setValue(plhcMktEndrec);
    }

    public void setPlhcMktEndrec(final IAlphanumericValue plhcMktEndrec) {
        this.plhcMktEndrec.setValue(plhcMktEndrec);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhcMktCompanyD.setValue("");
        this.plhcMktAgentId.setValue("");
        this.filler5.setValue("");
        this.plhcMktAgencyId.setValue("");
        this.filler6.setValue("");
        this.plhcMktSsn.setValue(StringUtils.all("0", 13));
        this.plhcMktNasd.setValue("");
        this.plhcMktSex.setValue("");
        this.plhcMktHireDate.setZero();
        this.plhcMktStatus.setValue("");
        this.plhcMktTermDate.setZero();
        this.plhcMktDob.setZero();
        this.plhcMktCorpName.setValue("");
        this.plhcMktNameLast.setValue("");
        this.plhcMktNameFirst.setValue("");
        this.plhcMktAddressOne.setValue("");
        this.plhcMktAddressTwo.setValue("");
        this.plhcMktAddressThree.setValue("");
        this.plhcMktAddressFour.setValue("");
        this.plhcMktAddressState.setValue("");
        this.plhcMktZipFive.setValue("00000");
        this.plhcMktPdcFive.setValue("");
        this.plhcMktPhone.setValue("");
        this.plhcMktCorpNamea.setValue("");
        this.plhcMktNameLasta.setValue("");
        this.plhcMktNameFirsta.setValue("");
        this.plhcMktAddressOnea.setValue("");
        this.plhcMktAddressTwoa.setValue("");
        this.plhcMktAddressThreea.setValue("");
        this.plhcMktAddressFoura.setValue("");
        this.plhcMktAddressStatea.setValue("");
        this.plhcMktZipFivea.setValue("00000");
        this.plhcMktPdcFivea.setValue("");
        this.plhcMktPhonea.setValue("");
        this.plhcMktNasdEffDate.setZero();
        this.plhcMktNasdRenDate.setZero();
        this.plhcMktLicName.setValue("");
        this.plhcMktLevel.setValue("");
        this.plhcMktPayOption.setValue("");
        this.plhcMktChain.setValue("");
        this.plhcMktCommIndex.setValue("");
        this.plhcMktPayMethod.setValue("");
        this.plhcMktPayFreq.setValue("");
        this.filler7.setValue("");
        this.plhcMktUniqueAgentCode.setValue("00000000");
        this.filler8.setValue("");
        this.plhcMktSortInd.setValue("");
        this.filler9.setValue("");
        this.plhcMktEndrec.setValue("X");
    }

}

