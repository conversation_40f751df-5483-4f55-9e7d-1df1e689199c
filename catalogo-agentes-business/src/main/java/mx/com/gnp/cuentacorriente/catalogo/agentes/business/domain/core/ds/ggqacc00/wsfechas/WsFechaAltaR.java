package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.wsfechas;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FECHA-ALTA-R.
 *
 */
@Setter
@Getter
public class WsFechaAltaR extends DataStruct {
    // Properties
    private UnsignedNumericVar wsFecAltAa = new UnsignedNumericVar(this, 4, 0);              // 05 WS-FEC-ALT-AA 9(04)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar wsFecAltMm = new UnsignedNumericVar(this, 2, 0);              // 05 WS-FEC-ALT-MM 9(02)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar wsFecAltDd = new UnsignedNumericVar(this, 2, 0);              // 05 WS-FEC-ALT-DD 9(02)

    public WsFechaAltaR() {
        super();
        initialize();
    }

    public WsFechaAltaR(DataContainer parent) {
        super(parent);
    }

    public WsFechaAltaR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFecAltAa(final int wsFecAltAa) {
        this.wsFecAltAa.setValue(wsFecAltAa);
    }

    public void setWsFecAltAa(final BigDecimal wsFecAltAa) {
        this.wsFecAltAa.setValue(wsFecAltAa);
    }

    public void setWsFecAltAa(final INumericValue wsFecAltAa) {
        this.wsFecAltAa.setValue(wsFecAltAa);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setWsFecAltMm(final int wsFecAltMm) {
        this.wsFecAltMm.setValue(wsFecAltMm);
    }

    public void setWsFecAltMm(final BigDecimal wsFecAltMm) {
        this.wsFecAltMm.setValue(wsFecAltMm);
    }

    public void setWsFecAltMm(final INumericValue wsFecAltMm) {
        this.wsFecAltMm.setValue(wsFecAltMm);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setWsFecAltDd(final int wsFecAltDd) {
        this.wsFecAltDd.setValue(wsFecAltDd);
    }

    public void setWsFecAltDd(final BigDecimal wsFecAltDd) {
        this.wsFecAltDd.setValue(wsFecAltDd);
    }

    public void setWsFecAltDd(final INumericValue wsFecAltDd) {
        this.wsFecAltDd.setValue(wsFecAltDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFecAltAa.clear();
        this.filler1.clear();
        this.wsFecAltMm.clear();
        this.filler2.clear();
        this.wsFecAltDd.clear();
    }

}

