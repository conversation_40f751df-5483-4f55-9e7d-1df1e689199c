package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.banderas;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: BANDERAS-GLOBALES.
 *
 */
@Setter
@Getter
public class BanderasGlobales { // VStruct
    // Properties
    private String stCatDirec = "00";                                       // 03 ST-CAT-DIREC X(02)
    private int wsFinCatDirecc = 0;                                         // 03 WS-FIN-CAT-DIRECC 9(01)
    // Level 88 - Conditional names
    private static final int FIN_CAT_DIRECC = 1;


    public BanderasGlobales() {
        initialize();
    }



    // Conditionals (88) - WS-FIN-CAT-DIRECC
    public boolean isFinCatDirecc() {
        return this.wsFinCatDirecc == FIN_CAT_DIRECC;
    }

    public void setFinCatDirecc() {
        this.wsFinCatDirecc = FIN_CAT_DIRECC;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.stCatDirec = "00";
        this.wsFinCatDirecc = 0;
    }

}

