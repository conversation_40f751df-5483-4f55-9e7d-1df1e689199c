package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTAGT0-NULL.
 *
 */
// 901      ******************************************************************
// 902      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 6       *
// 903      ******************************************************************
// 906044100*                                                                 04410000
// 907044200*                                                                 04420000
// 908044300*                                                                 04430000
// 909044400*                                                                 04440000
@Setter
@Getter
public class WsGfvtagt0Null { // VStruct
    // Properties
    private int agt01;                                                      // 03 AGT01 S9(4) COMPUTATIONAL
    private int agt02;                                                      // 03 AGT02 S9(4) COMPUTATIONAL
    private int agt03;                                                      // 03 AGT03 S9(4) COMPUTATIONAL
    private int agt04;                                                      // 03 AGT04 S9(4) COMPUTATIONAL
    private int agt05;                                                      // 03 AGT05 S9(4) COMPUTATIONAL
    private int agt06;                                                      // 03 AGT06 S9(4) COMPUTATIONAL
    private int agt07;                                                      // 03 AGT07 S9(4) COMPUTATIONAL
    private int agt08;                                                      // 03 AGT08 S9(4) COMPUTATIONAL
    private int agt09;                                                      // 03 AGT09 S9(4) COMPUTATIONAL
    private int agt11;                                                      // 03 AGT11 S9(4) COMPUTATIONAL
    private int agt12;                                                      // 03 AGT12 S9(4) COMPUTATIONAL
    private int agt13;                                                      // 03 AGT13 S9(4) COMPUTATIONAL
    private int agt14;                                                      // 03 AGT14 S9(4) COMPUTATIONAL
    private int agt15;                                                      // 03 AGT15 S9(4) COMPUTATIONAL
    private int agt16;                                                      // 03 AGT16 S9(4) COMPUTATIONAL
    private int agt17;                                                      // 03 AGT17 S9(4) COMPUTATIONAL
    private int agt18;                                                      // 03 AGT18 S9(4) COMPUTATIONAL
    private int agt19;                                                      // 03 AGT19 S9(4) COMPUTATIONAL
    private int agt20;                                                      // 03 AGT20 S9(4) COMPUTATIONAL
    private int agt21;                                                      // 03 AGT21 S9(4) COMPUTATIONAL
    private int agt22;                                                      // 03 AGT22 S9(4) COMPUTATIONAL
    private int agt23;                                                      // 03 AGT23 S9(4) COMPUTATIONAL
    private int agt24;                                                      // 03 AGT24 S9(4) COMPUTATIONAL
    private int agt25;                                                      // 03 AGT25 S9(4) COMPUTATIONAL
    private int agt26;                                                      // 03 AGT26 S9(4) COMPUTATIONAL
    private int agt27;                                                      // 03 AGT27 S9(4) COMPUTATIONAL
    private int agt28;                                                      // 03 AGT28 S9(4) COMPUTATIONAL
    private int agt29;                                                      // 03 AGT29 S9(4) COMPUTATIONAL
    private int agt30;                                                      // 03 AGT30 S9(4) COMPUTATIONAL
    private int agt31;                                                      // 03 AGT31 S9(4) COMPUTATIONAL
    private int agt32;                                                      // 03 AGT32 S9(4) COMPUTATIONAL

    public WsGfvtagt0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.agt01 = 0;
        this.agt02 = 0;
        this.agt03 = 0;
        this.agt04 = 0;
        this.agt05 = 0;
        this.agt06 = 0;
        this.agt07 = 0;
        this.agt08 = 0;
        this.agt09 = 0;
        this.agt11 = 0;
        this.agt12 = 0;
        this.agt13 = 0;
        this.agt14 = 0;
        this.agt15 = 0;
        this.agt16 = 0;
        this.agt17 = 0;
        this.agt18 = 0;
        this.agt19 = 0;
        this.agt20 = 0;
        this.agt21 = 0;
        this.agt22 = 0;
        this.agt23 = 0;
        this.agt24 = 0;
        this.agt25 = 0;
        this.agt26 = 0;
        this.agt27 = 0;
        this.agt28 = 0;
        this.agt29 = 0;
        this.agt30 = 0;
        this.agt31 = 0;
        this.agt32 = 0;
    }

}

