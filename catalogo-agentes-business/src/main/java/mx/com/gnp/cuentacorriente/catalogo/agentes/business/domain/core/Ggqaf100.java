
/**
 *  Program: Ggqaf100.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00olcpy.AbReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00lncpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa0053cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagt0cpy.Dclgfvtagt0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00ogcpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00l6cpy.WsLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.components.Ggqa0049cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00l6cpy.WsLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagt0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqaf100 extends AbstractCobolOs390Program implements IProgram {


    // Copy file QA$CBLCPY$GGQA00OL
    private AbReg abReg = new  AbReg();                                      // 01 AB-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00OL

    // Copy file QA$CBLCPY$GGQA00LN
    private Ggqa00lncpy ggqa00lncpy = new  Ggqa00lncpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00LN

    // Copy file QA$CBLCPY$GGQA0053
    private Ggqa0053cpy ggqa0053cpy = new  Ggqa0053cpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA0053

    // Auto variables
//  private int ccI;                                // CC-I // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GFVTAGT0
    private Dclgfvtagt0 dclgfvtagt0 = new  Dclgfvtagt0();                    // 01 DCLGFVTAGT0 UsedAsParameter
    // End copy file QA$CBLCPY$GFVTAGT0

    // Copy file QA$CBLCPY$GGQA00OG
    private Ggqa00ogcpy ggqa00ogcpy = new  Ggqa00ogcpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00OG

    // Auto variables
//  private int t999MdI;                            // T999-MD-I // [@WARNING variable NOT USED]
    private IndexVar iError = new IndexVar();        // I-ERROR
    // Level 1
    private AlphanumericVar w000Prog = new AlphanumericVar(8);               // 01 W000-PROG X(08)
//  private String w000Fs = "";                                             // 01 W000-FS XX // [@WARNING variable NOT USED]
    private String w000Insert = "INSERT";                                   // 01 W000-INSERT X(06)
    private String w000Update = "UPDATE";                                   // 01 W000-UPDATE X(06)
    private String w000Select = "SELECT";                                   // 01 W000-SELECT X(06)
    private String w000Open = "OPEN";                                       // 01 W000-OPEN X(04)
    private String w000Fetch = "FETCH";                                     // 01 W000-FETCH X(05)
    private String w000Close = "CLOSE";                                     // 01 W000-CLOSE X(05)
//  private String w000RegAct = "1";                                        // 01 W000-REG-ACT X(01) // [@WARNING variable NOT USED]
    private int wsIndNulos;                                                 // 01 WS-IND-NULOS S9(4) COMPUTATIONAL-4
    private int wsNalCve;                                                   // 01 WS-NAL-CVE S9(4) COMPUTATIONAL-4
    private int wsPefCve;                                                   // 01 WS-PEF-CVE S9(4) COMPUTATIONAL-4
    private int wsTcaCve;                                                   // 01 WS-TCA-CVE S9(4) COMPUTATIONAL-4
    private int wsMteCve;                                                   // 01 WS-MTE-CVE S9(4) COMPUTATIONAL-4
    private int wsAgtClfLmr;                                                // 01 WS-AGT-CLF-LMR S9(4) COMPUTATIONAL-4
    private int wsAgtNomExi;                                                // 01 WS-AGT-NOM-EXI S9(4) COMPUTATIONAL-4
    private int wsAgtNumFolInv;                                             // 01 WS-AGT-NUM-FOL-INV S9(4) COMPUTATIONAL-4
    private int wsAgtRstInv;                                                // 01 WS-AGT-RST-INV S9(4) COMPUTATIONAL-4
    private int wsAgtFecIniInv;                                             // 01 WS-AGT-FEC-INI-INV S9(4) COMPUTATIONAL-4
    private int wsAgtFecFinInv;                                             // 01 WS-AGT-FEC-FIN-INV S9(4) COMPUTATIONAL-4
    private int wsAgtNomDphInv;                                             // 01 WS-AGT-NOM-DPH-INV S9(4) COMPUTATIONAL-4
    private int wsAgtFecReh;                                                // 01 WS-AGT-FEC-REH S9(4) COMPUTATIONAL-4
    private int wsAgtFecMotEss;                                             // 01 WS-AGT-FEC-MOT-ESS S9(4) COMPUTATIONAL-4
    private int wsAgtFecMotAsgIn;                                           // 01 WS-AGT-FEC-MOT-ASG-IN S9(4) COMPUTATIONAL-4
    private int wsAgtNumFacIso;                                             // 01 WS-AGT-NUM-FAC-ISO S9(4) COMPUTATIONAL-4
    private int wsAgtImpFacIso;                                             // 01 WS-AGT-IMP-FAC-ISO S9(4) COMPUTATIONAL-4

    // Copy file QA$CBLCPY$GGQA00L6
    private WsLinkReg wsLinkReg = new  WsLinkReg();                          // 01 WS-LINK-REG UsedAsParameter

    private Sqlca sqlca;

    // Copy routines
    private Ggqa0049cpy ggqa0049cpy;


    // Declare Sql Ports
    private IGfvtagt0Port gfvtagt0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtagt0Model> gfvtagt0Model;

    // Declare Sql Cursors
    private ICursor<Gfvtagt0Model> cagt0Cursor;

    public Ggqaf100(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();

        // Copy routines
        ggqa0049cpy = new Ggqa0049cpy(this, ggqa00lncpy, abReg, w000Prog);

        initialize();
    }

    // Initial values
    @Override
    public void initialize() {
        this.w000Prog.setValue("GGQAF100");
        this.w000Insert = "INSERT";
        this.w000Update = "UPDATE";
        this.w000Select = "SELECT";
        this.w000Open = "OPEN";
        this.w000Fetch = "FETCH";
        this.w000Close = "CLOSE";
//        this.w000RegAct = "1"; // [@WARNING variable NOT USED]
    }


    /**
     *  Main execution entry point of the program.
     */
    public void run(AlphanumericVar wsLinkReg) {
        this.wsLinkReg.setValue(wsLinkReg);
        try {
            run();
        } finally {
            wsLinkReg.setValue(this.wsLinkReg);
        }
    }

    @Override
    public void run(Object... args) {
        assignInputArgument(args, 0, this.wsLinkReg);
        try {
            run();
        } finally {
            updateOutputArgument(args, 0, this.wsLinkReg);
        }
    }

    public void run() {
        sqlDelayedParagraph();
        p001Proceso();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(cagt0Cursor == null) {
            cagt0Cursor = gfvtagt0Port.createCursorCagt0(
                () -> null);
        }
        //
        // EXIT-SQL-DELAYED-PARAGRAPH - Last paragraph
        //
    }

    /**
     *  Section: 001-PROCESO
     *  Paragraph: unnamedProcedure.
     */
    void p001Proceso() {
        p010Inicio();
        p020Procesa();
        p100Termina();
    }

    /**
     *  Section: 010-INICIO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA INICIO DE PROGRAMA
     */
    void p010Inicio() {
        p060MueveVarLinkHost();
    }

    /**
     *  Section: 020-PROCESA
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA REGISTRO
     */
    void p020Procesa() {
        if (wsLinkReg.getLagtOpcion().isEqual(w000Select)) {
            p050ValidaSelect();
        } else if (wsLinkReg.getLagtOpcion().isEqual(w000Insert)) {
            p030ValidaInsert();
        } else if (wsLinkReg.getLagtOpcion().isEqual(w000Update)) {
            p040ValidaUpdate();
        } else if (wsLinkReg.getLagtOpcion().isEqual(w000Open)) {
            p150AbreCursorCagt0();
        } else if (wsLinkReg.getLagtOpcion().isEqual(w000Fetch)) {
            p160ValidaFetch();
            p170ValidaRegActivo();
        } else if (wsLinkReg.getLagtOpcion().isEqual(w000Close)) {
            p180CierraCursorCagt0();
        } else {
            abReg.getAbMensaje().at(1).setValue("020-PROCESA");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 050-VALIDA-SELECT
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA SELECT
     */
    void p050ValidaSelect() {
        p110SelectRes();
        if (ggqa0053cpy.getBdErr().isEqual("0")) {
            p070MueveVarHostLink();
        }
        wsLinkReg.setLagtSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 030-VALIDA-INSERT
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA INSERT
     */
    void p030ValidaInsert() {
        p120InsertRes();
        wsLinkReg.setLagtSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 040-VALIDA-UPDATE
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA PROCESO UPDATE
     */
    void p040ValidaUpdate() {
        p130UpdateRes();
        wsLinkReg.setLagtSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 060-MUEVE-VAR-LINK-HOST
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE DATOS DE LINKAGE A VARIABLES HOST
     */
    void p060MueveVarLinkHost() {
        dclgfvtagt0.setValue(wsLinkReg.getLagtReg());
    }

    /**
     *  Section: 070-MUEVE-VAR-HOST-LINK
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE DATOS DE  VARIABLES HOST  A LINKAGE
     */
    void p070MueveVarHostLink() {
        wsLinkReg.getLagtReg().setValue(dclgfvtagt0);
    }

    /**
     *  Section: 110-SELECT-RES
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA SELECT
     */
    void p110SelectRes() {
        gfvtagt0Model =
            gfvtagt0Port.findByAgtIdr(
                                    dclgfvtagt0.getAgtIdr().toInt());

        if(gfvtagt0Model.isPresent()) {
            dclgfvtagt0.setAgtIdr(fromHostInt(gfvtagt0Model.get().agtIdr()));
            dclgfvtagt0.setAgtRfc(fromHostString(gfvtagt0Model.get().agtRfc()));
            dclgfvtagt0.setAgtNom(fromHostString(gfvtagt0Model.get().agtNom()));
            dclgfvtagt0.setAgtApePat(fromHostString(gfvtagt0Model.get().agtApePat()));
            dclgfvtagt0.setAgtApeMat(fromHostString(gfvtagt0Model.get().agtApeMat()));
            dclgfvtagt0.setAgtFecNac(fromHostString(gfvtagt0Model.get().agtFecNac()));
            dclgfvtagt0.setAgtNumCed(fromHostString(gfvtagt0Model.get().agtNumCed()));
            dclgfvtagt0.setAgtFecIniCed(fromHostString(gfvtagt0Model.get().agtFecIniCed()));
            dclgfvtagt0.setAgtFecFinCed(fromHostString(gfvtagt0Model.get().agtFecFinCed()));
            dclgfvtagt0.setAgtExpSeg(fromHostString(gfvtagt0Model.get().agtExpSeg()));
            dclgfvtagt0.setAgtTraEsp(fromHostString(gfvtagt0Model.get().agtTraEsp()));
            dclgfvtagt0.setAgtClfLmr(fromHostInt(gfvtagt0Model.get().agtClfLmr()));
            wsAgtClfLmr = isNull(gfvtagt0Model.get().agtClfLmr()) ? -1 : 0;
            dclgfvtagt0.setFecUltAct(fromHostString(gfvtagt0Model.get().fecUltAct()));
            dclgfvtagt0.setAgtRazSoc(fromHostString(gfvtagt0Model.get().agtRazSoc()));
            dclgfvtagt0.setUsuCveAct(fromHostString(gfvtagt0Model.get().usuCveAct()));
            dclgfvtagt0.setNalCve(fromHostString(gfvtagt0Model.get().nalCve()));
            wsNalCve = isNull(gfvtagt0Model.get().nalCve()) ? -1 : 0;
            dclgfvtagt0.setPefCve(fromHostInt(gfvtagt0Model.get().pefCve()));
            wsPefCve = isNull(gfvtagt0Model.get().pefCve()) ? -1 : 0;
            dclgfvtagt0.setTcaCve(fromHostString(gfvtagt0Model.get().tcaCve()));
            wsTcaCve = isNull(gfvtagt0Model.get().tcaCve()) ? -1 : 0;
            dclgfvtagt0.setMteCve(fromHostInt(gfvtagt0Model.get().mteCve()));
            wsMteCve = isNull(gfvtagt0Model.get().mteCve()) ? -1 : 0;
            dclgfvtagt0.setAgtNumFolInv(fromHostInt(gfvtagt0Model.get().agtNumFolInv()));
            wsAgtNumFolInv = isNull(gfvtagt0Model.get().agtNumFolInv()) ? -1 : 0;
            dclgfvtagt0.setAgtNomExi(fromHostString(gfvtagt0Model.get().agtNomExi()));
            wsAgtNomExi = isNull(gfvtagt0Model.get().agtNomExi()) ? -1 : 0;
            dclgfvtagt0.setAgtRstInv(fromHostString(gfvtagt0Model.get().agtRstInv()));
            wsAgtRstInv = isNull(gfvtagt0Model.get().agtRstInv()) ? -1 : 0;
            dclgfvtagt0.setAgtFecIniInv(fromHostString(gfvtagt0Model.get().agtFecIniInv()));
            wsAgtFecIniInv = isNull(gfvtagt0Model.get().agtFecIniInv()) ? -1 : 0;
            dclgfvtagt0.setAgtFecFinInv(fromHostString(gfvtagt0Model.get().agtFecFinInv()));
            wsAgtFecFinInv = isNull(gfvtagt0Model.get().agtFecFinInv()) ? -1 : 0;
            dclgfvtagt0.setAgtNomDphInv(fromHostString(gfvtagt0Model.get().agtNomDphInv()));
            wsAgtNomDphInv = isNull(gfvtagt0Model.get().agtNomDphInv()) ? -1 : 0;
            dclgfvtagt0.setAgtFecReh(fromHostString(gfvtagt0Model.get().agtFecReh()));
            wsAgtFecReh = isNull(gfvtagt0Model.get().agtFecReh()) ? -1 : 0;
            dclgfvtagt0.setAgtFecMotEss(fromHostString(gfvtagt0Model.get().agtFecMotEss()));
            wsAgtFecMotEss = isNull(gfvtagt0Model.get().agtFecMotEss()) ? -1 : 0;
            dclgfvtagt0.setAgtFecMotAsgIn(fromHostString(gfvtagt0Model.get().agtFecMotAsgIn()));
            wsAgtFecMotAsgIn = isNull(gfvtagt0Model.get().agtFecMotAsgIn()) ? -1 : 0;
            dclgfvtagt0.setAgtObs(fromHostString(gfvtagt0Model.get().agtObs()));
            dclgfvtagt0.setAgtNip(fromHostInt(gfvtagt0Model.get().agtNip()));
            wsIndNulos = isNull(gfvtagt0Model.get().agtNip()) ? -1 : 0;
            dclgfvtagt0.setAgtNumFacIso(fromHostInt(gfvtagt0Model.get().agtNumFacIso()));
            wsAgtNumFacIso = isNull(gfvtagt0Model.get().agtNumFacIso()) ? -1 : 0;
            dclgfvtagt0.setAgtImpFacIso(fromHostBigDecimal(gfvtagt0Model.get().agtImpFacIso()));
            wsAgtImpFacIso = isNull(gfvtagt0Model.get().agtImpFacIso()) ? -1 : 0;
        }
        abReg.getAbMensaje().at(1).setValue("110-SELECT-RES");
        p999VerErrSql();
    }

    /**
     *  Section: 120-INSERT-RES
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA INSERT
     */
    void p120InsertRes() {
        gfvtagt0Port.insert(
            GenericModel.builder().values(List.of(
                dclgfvtagt0.getAgtIdr().toInt(),
                toHostString(dclgfvtagt0.getAgtRfc()),
                toHostString(dclgfvtagt0.getAgtNom()),
                toHostString(dclgfvtagt0.getAgtApePat()),
                toHostString(dclgfvtagt0.getAgtApeMat()),
                toHostString(dclgfvtagt0.getAgtFecNac()),
                toHostString(dclgfvtagt0.getAgtNumCed()),
                toHostString(dclgfvtagt0.getAgtFecIniCed()),
                toHostString(dclgfvtagt0.getAgtFecFinCed()),
                toHostString(dclgfvtagt0.getAgtExpSeg()),
                toHostString(dclgfvtagt0.getAgtTraEsp()),
                dclgfvtagt0.getAgtClfLmr().toInt(),
                toHostString(dclgfvtagt0.getFecUltAct()),
                toHostString(dclgfvtagt0.getAgtRazSoc()),
                toHostString(dclgfvtagt0.getUsuCveAct()),
                toHostString(dclgfvtagt0.getNalCve()),
                dclgfvtagt0.getPefCve().toInt(),
                toHostString(dclgfvtagt0.getTcaCve()),
                dclgfvtagt0.getMteCve().toInt(),
                dclgfvtagt0.getAgtNumFolInv().toInt(),
                toHostString(dclgfvtagt0.getAgtNomExi()),
                toHostString(dclgfvtagt0.getAgtRstInv()),
                toHostString(dclgfvtagt0.getAgtFecIniInv()),
                toHostString(dclgfvtagt0.getAgtFecFinInv()),
                toHostString(dclgfvtagt0.getAgtNomDphInv()),
                toHostString(dclgfvtagt0.getAgtFecReh()),
                toHostString(dclgfvtagt0.getAgtFecMotEss()),
                toHostString(dclgfvtagt0.getAgtFecMotAsgIn()),
                toHostString(dclgfvtagt0.getAgtObs()),
                dclgfvtagt0.getAgtNip().toInt(),
                dclgfvtagt0.getAgtNumFacIso().toInt(),
                dclgfvtagt0.getAgtImpFacIso().toBigDecimal()))
            .build());
        abReg.getAbMensaje().at(1).setValue("120-INSERT-RES");
        p999VerErrSql();
    }

    /**
     *  Section: 130-UPDATE-RES
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA UPDATE
     */
    void p130UpdateRes() {
        gfvtagt0Port.update(
            Gfvtagt0Model.builder()
                .agtRfc(toHostString(dclgfvtagt0.getAgtRfc()))
                .agtNom(toHostString(dclgfvtagt0.getAgtNom()))
                .agtApePat(toHostString(dclgfvtagt0.getAgtApePat()))
                .agtApeMat(toHostString(dclgfvtagt0.getAgtApeMat()))
                .agtFecNac(toHostLocalDate(dclgfvtagt0.getAgtFecNac()))
                .agtNumCed(toHostString(dclgfvtagt0.getAgtNumCed()))
                .agtFecIniCed(toHostLocalDate(dclgfvtagt0.getAgtFecIniCed()))
                .agtFecFinCed(toHostLocalDate(dclgfvtagt0.getAgtFecFinCed()))
                .agtExpSeg(toHostString(dclgfvtagt0.getAgtExpSeg()))
                .agtTraEsp(toHostString(dclgfvtagt0.getAgtTraEsp()))
                .agtClfLmr(dclgfvtagt0.getAgtClfLmr().toInt())
                .fecUltAct(toHostLocalDate(dclgfvtagt0.getFecUltAct()))
                .agtRazSoc(toHostString(dclgfvtagt0.getAgtRazSoc()))
                .usuCveAct(toHostString(dclgfvtagt0.getUsuCveAct()))
                .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
            .build());
        abReg.getAbMensaje().at(1).setValue("130-UPDATE-RES");
        p999VerErrSql();
    }

    /**
     *  Section: 150-ABRE-CURSOR-CAGT0
     *  Paragraph: unnamedProcedure.
     *
     * ABRE CURSOR CAGT0
     */
    void p150AbreCursorCagt0() {
        abReg.getAbMensaje().at(1).setValue("150-ABRE-CURSOR-CAGT0");
        p152OpenCagt0();
        wsLinkReg.setLagtSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 152-OPEN-CAGT0
     *  Paragraph: unnamedProcedure.
     *
     */
    void p152OpenCagt0() {
        cagt0Cursor.open();
        p999VerErrSql();
    }

    /**
     *  Section: 160-VALIDA-FETCH
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA PROCESO FETCH
     */
    void p160ValidaFetch() {
        p162LeeCursorCagt0();
        if (ggqa0053cpy.getBdErr().isEqual("1")) {
            wsLinkReg.setLagtSwErr(ggqa0053cpy.getBdErr());
        } else {
            ;
        }
    }

    /**
     *  Section: 162-LEE-CURSOR-CAGT0
     *  Paragraph: unnamedProcedure.
     *
     */
    void p162LeeCursorCagt0() {
        abReg.getAbMensaje().at(1).setValue("162-LEE-CURSOR-CAGT0 ");
        dclgfvtagt0.initialize();
		gfvtagt0Model = cagt0Cursor.next();

        if (gfvtagt0Model.isPresent()) {
            dclgfvtagt0.setAgtIdr(fromHostInt(gfvtagt0Model.get().agtIdr()));
            dclgfvtagt0.setAgtRfc(fromHostString(gfvtagt0Model.get().agtRfc()));
            dclgfvtagt0.setAgtNom(fromHostString(gfvtagt0Model.get().agtNom()));
            dclgfvtagt0.setAgtApePat(fromHostString(gfvtagt0Model.get().agtApePat()));
            dclgfvtagt0.setAgtApeMat(fromHostString(gfvtagt0Model.get().agtApeMat()));
            dclgfvtagt0.setAgtFecNac(fromHostString(gfvtagt0Model.get().agtFecNac()));
            dclgfvtagt0.setAgtNumCed(fromHostString(gfvtagt0Model.get().agtNumCed()));
            dclgfvtagt0.setAgtFecIniCed(fromHostString(gfvtagt0Model.get().agtFecIniCed()));
            dclgfvtagt0.setAgtFecFinCed(fromHostString(gfvtagt0Model.get().agtFecFinCed()));
            dclgfvtagt0.setAgtExpSeg(fromHostString(gfvtagt0Model.get().agtExpSeg()));
            dclgfvtagt0.setAgtTraEsp(fromHostString(gfvtagt0Model.get().agtTraEsp()));
            dclgfvtagt0.setAgtClfLmr(fromHostInt(gfvtagt0Model.get().agtClfLmr()));
            wsAgtClfLmr = isNull(gfvtagt0Model.get().agtClfLmr()) ? -1 : 0;
            dclgfvtagt0.setFecUltAct(fromHostString(gfvtagt0Model.get().fecUltAct()));
            dclgfvtagt0.setAgtRazSoc(fromHostString(gfvtagt0Model.get().agtRazSoc()));
            dclgfvtagt0.setUsuCveAct(fromHostString(gfvtagt0Model.get().usuCveAct()));
            dclgfvtagt0.setNalCve(fromHostString(gfvtagt0Model.get().nalCve()));
            wsNalCve = isNull(gfvtagt0Model.get().nalCve()) ? -1 : 0;
            dclgfvtagt0.setPefCve(fromHostInt(gfvtagt0Model.get().pefCve()));
            wsPefCve = isNull(gfvtagt0Model.get().pefCve()) ? -1 : 0;
            dclgfvtagt0.setTcaCve(fromHostString(gfvtagt0Model.get().tcaCve()));
            wsTcaCve = isNull(gfvtagt0Model.get().tcaCve()) ? -1 : 0;
            dclgfvtagt0.setMteCve(fromHostInt(gfvtagt0Model.get().mteCve()));
            wsMteCve = isNull(gfvtagt0Model.get().mteCve()) ? -1 : 0;
            dclgfvtagt0.setAgtNumFolInv(fromHostInt(gfvtagt0Model.get().agtNumFolInv()));
            wsAgtNumFolInv = isNull(gfvtagt0Model.get().agtNumFolInv()) ? -1 : 0;
            dclgfvtagt0.setAgtNomExi(fromHostString(gfvtagt0Model.get().agtNomExi()));
            wsAgtNomExi = isNull(gfvtagt0Model.get().agtNomExi()) ? -1 : 0;
            dclgfvtagt0.setAgtRstInv(fromHostString(gfvtagt0Model.get().agtRstInv()));
            wsAgtRstInv = isNull(gfvtagt0Model.get().agtRstInv()) ? -1 : 0;
            dclgfvtagt0.setAgtFecIniInv(fromHostString(gfvtagt0Model.get().agtFecIniInv()));
            wsAgtFecIniInv = isNull(gfvtagt0Model.get().agtFecIniInv()) ? -1 : 0;
            dclgfvtagt0.setAgtFecFinInv(fromHostString(gfvtagt0Model.get().agtFecFinInv()));
            wsAgtFecFinInv = isNull(gfvtagt0Model.get().agtFecFinInv()) ? -1 : 0;
            dclgfvtagt0.setAgtNomDphInv(fromHostString(gfvtagt0Model.get().agtNomDphInv()));
            wsAgtNomDphInv = isNull(gfvtagt0Model.get().agtNomDphInv()) ? -1 : 0;
            dclgfvtagt0.setAgtFecReh(fromHostString(gfvtagt0Model.get().agtFecReh()));
            wsAgtFecReh = isNull(gfvtagt0Model.get().agtFecReh()) ? -1 : 0;
            dclgfvtagt0.setAgtFecMotEss(fromHostString(gfvtagt0Model.get().agtFecMotEss()));
            wsAgtFecMotEss = isNull(gfvtagt0Model.get().agtFecMotEss()) ? -1 : 0;
            dclgfvtagt0.setAgtFecMotAsgIn(fromHostString(gfvtagt0Model.get().agtFecMotAsgIn()));
            wsAgtFecMotAsgIn = isNull(gfvtagt0Model.get().agtFecMotAsgIn()) ? -1 : 0;
            dclgfvtagt0.setAgtObs(fromHostString(gfvtagt0Model.get().agtObs()));
            dclgfvtagt0.setAgtNip(fromHostInt(gfvtagt0Model.get().agtNip()));
            wsIndNulos = isNull(gfvtagt0Model.get().agtNip()) ? -1 : 0;
            dclgfvtagt0.setAgtNumFacIso(fromHostInt(gfvtagt0Model.get().agtNumFacIso()));
            wsAgtNumFacIso = isNull(gfvtagt0Model.get().agtNumFacIso()) ? -1 : 0;
            dclgfvtagt0.setAgtImpFacIso(fromHostBigDecimal(gfvtagt0Model.get().agtImpFacIso()));
            wsAgtImpFacIso = isNull(gfvtagt0Model.get().agtImpFacIso()) ? -1 : 0;
        }
        p999VerErrSql();
        wsLinkReg.setLagtSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 170-VALIDA-REG-ACTIVO
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA REGISTRO ACTIVO
     */
    void p170ValidaRegActivo() {
        if (ggqa0053cpy.getBdErr().isEqual("0")) {
            p070MueveVarHostLink();
        }
    }

    /**
     *  Section: 180-CIERRA-CURSOR-CAGT0
     *  Paragraph: unnamedProcedure.
     *
     * CIERRA CURSOR CAGT0
     */
    void p180CierraCursorCagt0() {
        abReg.getAbMensaje().at(1).setValue("180-CIERRA-CURSOR-CAGT0");
        p999CloseCagt0();
        wsLinkReg.setLagtSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 999-CLOSE-CAGT0
     *  Paragraph: unnamedProcedure.
     *
     */
    void p999CloseCagt0() {
        if (cagt0Cursor != null) {
            cagt0Cursor.close();
        }
        p999VerErrSql();
    }

    /**
     *  Section: 100-TERMINA
     *  Paragraph: unnamedProcedure.
     *
     * TERMINA PROGRAMA
     */
    void p100Termina() {
        throw new ExitProgramException();
    }

    /**
     *  Section: 990-CIF-CTL
     *  Paragraph: unnamedProcedure.
     *
     */
    void p990CifCtl() {
    }

    /**
     *  Section: 999-VER-ERR-SQL
     *  Paragraph: unnamedProcedure.
     *
     * EXEC SQL
     * INCLUDE GGQA00OI
     * END-EXEC.
     * AHCPSQLE: RUTINA PARA EL MANEJO DEL SQL ERROR
     * AUTOR: SOFTTEK/CASE/TEN  17/NOV/95
     * TEN 1.50 CAF: 002
     * SE ADICIONAN CON EL PARRAFO 999-VER-SQL-ERR
     * (ES UN COPY FISICO, QUE SE ADICIONA POR EL CONVERTIDOR DE
     * LENGUAJE)
     * REFERENCIAS:                AHCWSQLE
     */
    void p999VerErrSql() {
        ggqa0053cpy.setBdErr("0");
        if (sqlca.getSqlcode() == 100) {
            ggqa0053cpy.setBdErr("1");
        } else if (sqlca.getSqlcode() != 0) {
            p999DespliegaSqlMsg();
            abReg.setAbCodigon(sqlca.getSqlcode());
            abReg.setAbProceso("999-VER-SQL-ERR");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 999-DESPLIEGA-SQL-MSG
     *  Paragraph: unnamedProcedure.
     *
     */
    void p999DespliegaSqlMsg() {
        display("ERROR SQL; -- <INICIO DESCRIPCION>: ");
        runProgram("DSNTIAR", sqlca, 
                ggqa00ogcpy.getW999ErrorMensaje(), 
                ggqa00ogcpy.getW999ErrorTextLong());
        ggqa00ogcpy.setW999ContadorLng(1);
        while (!(ggqa00ogcpy.getW999ContadorLng().toInt() > 10)) {
                iError.setValue(ggqa00ogcpy.getW999ContadorLng());
                display("--", ggqa00ogcpy.getW999ErrorText().at(iError));
                ggqa00ogcpy.getW999ContadorLng().add(1);
        }
        display("ERROR SQL; -- <FIN DESCRIPCION>");
    }

    @Autowired
    public void setGfvtagt0Port(IGfvtagt0Port gfvtagt0Port) {
        this.gfvtagt0Port = gfvtagt0Port;
        this.gfvtagt0Port.setProgramContext(getProgramContext());
    }
}
