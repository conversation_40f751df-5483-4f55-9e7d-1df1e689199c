package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTFLA0-NULL.
 *
 */
// 278      ******************************************************************
// 279      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 5       *
// 280      ******************************************************************
// 283053407*                                                                 05340700
// 284053408*                                                                 05340800
@Setter
@Getter
public class WsGfvtfla0Null { // VStruct
    // Properties
    private int fla01;                                                      // 03 FLA01 S9(4) COMPUTATIONAL
    private int fla02;                                                      // 03 FLA02 S9(4) COMPUTATIONAL
    private int fla03;                                                      // 03 FLA03 S9(4) COMPUTATIONAL
    private int fla04;                                                      // 03 FLA04 S9(4) COMPUTATIONAL
    private int fla05;                                                      // 03 FLA05 S9(4) COMPUTATIONAL
    private int fla06;                                                      // 03 FLA06 S9(4) COMPUTATIONAL
    private int fla07;                                                      // 03 FLA07 S9(4) COMPUTATIONAL
    private int fla08;                                                      // 03 FLA08 S9(4) COMPUTATIONAL
    private int fla09;                                                      // 03 FLA09 S9(4) COMPUTATIONAL
    private int fla10;                                                      // 03 FLA10 S9(4) COMPUTATIONAL
    private int fla11;                                                      // 03 FLA11 S9(4) COMPUTATIONAL
    private int fla12;                                                      // 03 FLA12 S9(4) COMPUTATIONAL
    private int fla13;                                                      // 03 FLA13 S9(4) COMPUTATIONAL

    public WsGfvtfla0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.fla01 = 0;
        this.fla02 = 0;
        this.fla03 = 0;
        this.fla04 = 0;
        this.fla05 = 0;
        this.fla06 = 0;
        this.fla07 = 0;
        this.fla08 = 0;
        this.fla09 = 0;
        this.fla10 = 0;
        this.fla11 = 0;
        this.fla12 = 0;
        this.fla13 = 0;
    }

}

