package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTFLA1-NULL.
 *
 */
// 1005054000*                                                                 05400000
// 1006054100*                                                                 05410000
@Setter
@Getter
public class WsGfvtfla1Null { // VStruct
    // Properties
    private int fla011;                                                     // 03 FLA011 S9(4) COMPUTATIONAL
    private int fla022;                                                     // 03 FLA022 S9(4) COMPUTATIONAL
    private int fla033;                                                     // 03 FLA033 S9(4) COMPUTATIONAL
    private int fla044;                                                     // 03 FLA044 S9(4) COMPUTATIONAL
    private int fla055;                                                     // 03 FLA055 S9(4) COMPUTATIONAL
    private int fla066;                                                     // 03 FLA066 S9(4) COMPUTATIONAL
    private int fla077;                                                     // 03 FLA077 S9(4) COMPUTATIONAL
    private int fla088;                                                     // 03 FLA088 S9(4) COMPUTATIONAL
    private int fla099;                                                     // 03 FLA099 S9(4) COMPUTATIONAL
    private int fla100;                                                     // 03 FLA100 S9(4) COMPUTATIONAL
    private int fla111;                                                     // 03 FLA111 S9(4) COMPUTATIONAL
    private int fla122;                                                     // 03 FLA122 S9(4) COMPUTATIONAL
    private int fla133;                                                     // 03 FLA133 S9(4) COMPUTATIONAL

    public WsGfvtfla1Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.fla011 = 0;
        this.fla022 = 0;
        this.fla033 = 0;
        this.fla044 = 0;
        this.fla055 = 0;
        this.fla066 = 0;
        this.fla077 = 0;
        this.fla088 = 0;
        this.fla099 = 0;
        this.fla100 = 0;
        this.fla111 = 0;
        this.fla122 = 0;
        this.fla133 = 0;
    }

}

