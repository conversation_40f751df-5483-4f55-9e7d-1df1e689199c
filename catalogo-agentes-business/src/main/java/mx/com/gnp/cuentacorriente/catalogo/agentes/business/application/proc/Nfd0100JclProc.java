/**
 *  JCL Program: NFD0100.
 *  Code generation type: K<PERSON>NDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Nfd0100JclProc extends AbstractJclProgram {

    public Nfd0100JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("NFD0100", (nfd0100) -> {
            nfd0100.addParameter("PROC");
            if (nfd0100.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                     *** NFD0100 ***                  S.A.E.T.A.   **/
                /*//*   APLICACION:  SAETA.                                             **/
                /*//*   PROCESO:     D I A R I O.                                       **/
                /*//*   OBJETIVO:    EXTRACCION DEL CATALOGO DE AGENTES (DB2)           **/
                /*//*   COMENTARIOS:                                                    **/
                /*//*   CORRE                                                           **/
                /*//*   DESPUES DE:  ACTUALIZACION DEL SERVICIO DE AGENTES EN EL NT     **/
                /*//*   ANTES DE:    NFD0200  : ACTUALIZACION DE AGENTEMOD.             **/
                /*//*   ELABORO:     MOISES OLVERA HERNANDEZ   FECHA: 11/OCT/99.        **/
                /*//*   MODIFICO:    ALAN CHEW LYONS           FECHA: 04/AGO/2017.      **/
                /*//*                SE AGREGA PASO NFD0101 PARA COPIA ARCHIVO          **/
                /*//*                PGA.EAFV0101 A VOLUMEN COMPARTIDO.                 **/
                /*//**********************************************************************/
                /*//**********************************************************************/
                /*//*                         * NFD0106 **/
                /*//* OBJETIVO DEL STEP:  EXTRAE INFORMACION DEL CATALOGO DE AGENTES(DB2)*/
                /*//*                     PARA ACTUALIZACION DE AGENTEMOD(IDMS).*/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                nfd0100.execProgram("NFD0106", "IKJEFT01", (nfd0106) -> {
                    nfd0106.addParameter("REGION","0K");
                    /*//*        ** ARCHIVO DE AGENTES EN LA BD SAETA*/
                    nfd0106.addDataDefinition("MTRO", "PGA.EAFV0101",  (mtro) -> {
                        mtro.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        mtro.addParameter("UNIT", "(SHAREDA,P)");
                        mtro.addParameter("VOL", "(,,,4)");
                        mtro.addParameter("SPACE", "(CYL,(200,50),RLSE)");
                        mtro.addParameter("DCB", "(RECFM=FB,LRECL=800,BLKSIZE=23200,BUFNO=04,DSORG=PS)");
                    });
                    /*//*        ** REPORTE DE CAMBIOS DE SAGA A SAETA*/
                    nfd0106.addDataDefinition("REP", "PGA.EAFV0201",  (rep) -> {
                        rep.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        rep.addParameter("UNIT", "(TEMPROD,P)");
                        rep.addParameter("VOL", "(,,,4)");
                        rep.addParameter("SPACE", "(CYL,(200,50),RLSE)");
                        rep.addParameter("DCB", "(RECFM=FB,LRECL=133,BLKSIZE=23408,BUFNO=04,DSORG=PS)");
                    });
                    /*//*        ** REPORTE DE ALTAS DE SAGA A SAETA*/
                    nfd0106.addDataDefinition("REP1", "PGA.EAFV0301",  (rep1) -> {
                        rep1.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        rep1.addParameter("UNIT", "(TEMPROD,P)");
                        rep1.addParameter("VOL", "(,,,4)");
                        rep1.addParameter("SPACE", "(CYL,(200,50),RLSE)");
                        rep1.addParameter("DCB", "(RECFM=FB,LRECL=133,BLKSIZE=23408,BUFNO=04,DSORG=PS)");
                    });
                    nfd0106.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfd0106.addDataDefinition("CMPRINT",  (cmprint) -> {
                        cmprint.addParameter("SYSOUT","*");
                    });
                    nfd0106.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                        sysdbout.addParameter("SYSOUT","*");
                    });
                    nfd0106.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfd0106.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfd0106.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","D");
                    });
                    nfd0106.addDataDefinition("SYSABOUT",  (sysabout) -> {
                        sysabout.addParameter("SYSOUT","*");
                    });
                    nfd0106.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFD0101)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfd0100.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                      ===* NFD0105 *===                            **/
                /*//*   OBJETIVO DEL STEP:  HACER UN BACK'UP DE ARCHIVOS                **/
                /*//*                       GENERADOS DURANTE EL PROCESO                **/
                /*//*   PASO REINICIABLE                                                **/
                /*//**********************************************************************/
                nfd0100.execProgram("NFD0105", "COPIADOR", (nfd0105) -> {
                    nfd0105.addParameter("REGION","0K");
                    nfd0105.addDataDefinition("OUTFILE1", "SYSBKW.EAFV0401(+1)",  (outfile1) -> {
                        outfile1.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        outfile1.addParameter("UNIT","DDVTAV");
                        outfile1.addParameter("LABEL", "(1,SL,EXPDT=99000)");
                        outfile1.addParameter("DCB", "(GDG.MODELO,BLKSIZE=32000,BUFNO=4,DSORG=PS)");
                    });
                    nfd0105.addDummyDataDefinition("OUTFILE2",  (outfile2) -> {
                    });
                    /*//*        ** ARCHIVO DE EXTRACCION DE DB2 DE CATALOGO DE AGENTES*/
                    nfd0105.addDataDefinition("EANF0101", "PGA.EAFV0101",  (eanf0101) -> {
                        eanf0101.addParameter("DISP","SHR");
                        eanf0101.addParameter("DCB","EROPT=SKP");
                    });
                    /*//*        ** ARCHIVO DE CAMBIOS DE SAGA A SAETA*/
                    nfd0105.addDataDefinition("EANF0201", "PGA.EAFV0201",  (eanf0201) -> {
                        eanf0201.addParameter("DISP","SHR");
                        eanf0201.addParameter("DCB","EROPT=SKP");
                    });
                    /*//*        ** REPORTE DE ALTAS DE SAGA A SAETA*/
                    nfd0105.addDataDefinition("EANF0301", "PGA.EAFV0301",  (eanf0301) -> {
                        eanf0301.addParameter("DISP","SHR");
                        eanf0301.addParameter("DCB","EROPT=SKP");
                    });
                    nfd0105.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                        sysdbout.addParameter("SYSOUT","*");
                    });
                    nfd0105.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","D");
                    });
                    nfd0105.addDataDefinition("SYSCOUNT",  (syscount) -> {
                        syscount.addParameter("SYSOUT","*");
                    });
                    nfd0105.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfd0105.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfd0105.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFD0102)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfd0100.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                    ***  NFD0104   ****/
                /*//* OBJETIVO DEL STEP : GENERACION DE REPORTES DE CAMBIOS PARA SAETA*/
                /*//**********************************************************************/
                nfd0100.execProgram("NFD0104", "REPLICA", (nfd0104) -> {
                    nfd0104.addParameter("PARM","/1");
                    /*//*        ** REPORTE DE CAMBIOS DE SAGA A SAETA*/
                    nfd0104.addDataDefinition("INFILE", "PGA.EAFV0201",  (infile) -> {
                        infile.addParameter("DISP","SHR");
                        infile.addParameter("DCB","EROPT=SKP");
                    });
                    nfd0104.addDataDefinition("SYSOUT1",  (sysout1) -> {
                        sysout1.addParameter("SYSOUT", "(L,,ST1D)");
                        sysout1.addParameter("COPIES","1");
                        sysout1.addParameter("FREE","CLOSE");
                        sysout1.addParameter("DCB", "(LRECL=137,BLKSIZE=13700,RECFM=FBM)");
                    });
                    nfd0104.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                });
            }
            if (nfd0100.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                    ***  NFD0103   ****/
                /*//* OBJETIVO DEL STEP : GENERACION DE REPORTES DE ALTAS PARA SAETA*/
                /*//**********************************************************************/
                nfd0100.execProgram("NFD0103", "REPLICA", (nfd0103) -> {
                    nfd0103.addParameter("PARM","/1");
                    /*//*        ** REPORTE DE CAMBIOS DE SAGA A SAETA*/
                    nfd0103.addDataDefinition("INFILE", "PGA.EAFV0301",  (infile) -> {
                        infile.addParameter("DISP","SHR");
                        infile.addParameter("DCB","BUFNO=4");
                    });
                    nfd0103.addDataDefinition("SYSOUT1",  (sysout1) -> {
                        sysout1.addParameter("SYSOUT", "(L,,ST1D)");
                        sysout1.addParameter("COPIES","1");
                        sysout1.addParameter("FREE","CLOSE");
                        sysout1.addParameter("DCB", "(LRECL=137,BLKSIZE=13700,RECFM=FBM)");
                    });
                    nfd0103.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                });
            }
            if (nfd0100.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                    ***  NFD0102   ****/
                /*//* OBJETIVO DEL STEP: DAR DE BAJA LOS ARCHIVOS GENERADOS EN*/
                /*//*                    ESTE PROCESO.*/
                /*//* PASO REINICIABLE*/
                /*//******************************************************************/
                nfd0100.execProgram("NFD0102", "IDCAMS", (nfd0102) -> {
                    nfd0102.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfd0102.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*            PARAMETRO PARA IDCAMS.*/
                    nfd0102.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFD0103)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfd0100.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                           * NFD0101 *                             **/
                /*//* OBJETIVO: COPIA ARCHIVO PGA.EAFV0101 A VOLUMEN COMPARTIDO.        **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfd0100.execProgram("NFD0101", "IDCAMS", (nfd0101) -> {
                    nfd0101.addDataDefinition("SYSUT1", "PGA.EAFV0101",  (sysut1) -> {
                        sysut1.addParameter("DISP","SHR");
                    });
                    nfd0101.addDataDefinition("SYSUT2", "PPCQS.NFD0100.EAFV0101",  (sysut2) -> {
                        sysut2.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysut2.addParameter("UNIT","SHRAPL");
                        sysut2.addParameter("SPACE", "(CYL,(200,50),RLSE)");
                        sysut2.addParameter("DCB", "(RECFM=FB,LRECL=800,BLKSIZE=23200,BUFNO=04,DSORG=PS)");
                    });
                    nfd0101.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfd0101.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFD0104)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfd0100.getLastReturnCode()==0) {
                /*//**/
                /*//**********************************************************************/
                /*//*                           * NFD0100 *                             **/
                /*//* OBJETIVO: ENVIA A BACKLOG ARCHIVO INTERFAZ                        **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfd0100.execProgram("NFD0100", "FTP", (_nfd0100) -> {
                    _nfd0100.addParameter("PARM","-t ANDANTIS (EXIT");
                    _nfd0100.addParameter("REGION","0M");
                    _nfd0100.addDataDefinition("INPUT", "PNCQP.BTCH.CARDS(NFD0105)",  (input) -> {
                        input.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
