package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-AGENTES.
 *
 */
@Setter
@Getter
public class RegAgentes extends DataStruct {
    // Properties
    private AlphanumericVar agFlaNum = new AlphanumericVar(this, 8);         // 03 AG-FLA-NUM X(8)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 325);        // 03 FILLER X(325)

    public RegAgentes() {
        super();
        initialize();
    }

    public RegAgentes(DataContainer parent) {
        super(parent);
    }

    public RegAgentes(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setAgFlaNum(final String agFlaNum) {
        this.agFlaNum.setValue(agFlaNum);
    }

    public void setAgFlaNum(final IAlphanumericValue agFlaNum) {
        this.agFlaNum.setValue(agFlaNum);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.agFlaNum.clear();
        this.filler1.clear();
    }

}

