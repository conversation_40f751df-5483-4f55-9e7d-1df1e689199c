package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablacna0.tcna0;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-CNA0-ELEM.
 *
 */
@Setter
@Getter
public class TCna0Elem extends DataStruct {
    // Properties
    private AlphanumericVar sCnaCnaNum = new AlphanumericVar(this, 2);       // 20 S-CNA-CNA-NUM X(02)
    private AlphanumericVar sCnaLimitad = new AlphanumericVar(this, 1);      // 20 S-CNA-LIMITAD X(01)

    public TCna0Elem() {
        super();
        initialize();
    }

    public TCna0Elem(DataContainer parent) {
        super(parent);
    }

    public TCna0Elem(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSCnaCnaNum(final String sCnaCnaNum) {
        this.sCnaCnaNum.setValue(sCnaCnaNum);
    }

    public void setSCnaCnaNum(final IAlphanumericValue sCnaCnaNum) {
        this.sCnaCnaNum.setValue(sCnaCnaNum);
    }

    public void setSCnaLimitad(final String sCnaLimitad) {
        this.sCnaLimitad.setValue(sCnaLimitad);
    }

    public void setSCnaLimitad(final IAlphanumericValue sCnaLimitad) {
        this.sCnaLimitad.setValue(sCnaLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sCnaCnaNum.clear();
        this.sCnaLimitad.clear();
    }

}

