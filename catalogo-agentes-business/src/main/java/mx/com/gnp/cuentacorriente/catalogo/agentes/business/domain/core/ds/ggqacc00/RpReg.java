package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg.RpLlave;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg.RpData;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg.RpDataImp;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg.RpDataSat;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg.RpDataAgente;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg.RpDataReceptor;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-REG.
 *
 */
// 87      *-------------------------------------------------------          
// 88      * REGISTRO DE SALIDA DE PASO                                      
// 89      *-------------------------------------------------------          
@Setter
@Getter
public class RpReg { // VStruct
    // Properties
    private RpLlave rpLlave = new  RpLlave();                                // 02 RP-LLAVE
    private RpData rpData = new  RpData();                                   // 02 RP-DATA
    private RpDataImp rpDataImp = new  RpDataImp();                          // 02 RP-DATA-IMP
    private RpDataSat rpDataSat = new  RpDataSat();                          // 02 RP-DATA-SAT
    private RpDataAgente rpDataAgente = new  RpDataAgente();                 // 02 RP-DATA-AGENTE
    private RpDataReceptor rpDataReceptor = new  RpDataReceptor();           // 02 RP-DATA-RECEPTOR

    public RpReg() {
        initialize();
    }




    // Deeper properties

    // Property: rpLlave.rpAgtIdr -> 10 RP-AGT-IDR 9(09)
    public int getRpAgtIdr() {
        return this.rpLlave.getRpAgtIdr();
    }

    public void setRpAgtIdr(final int rpAgtIdr) {
        this.rpLlave.setRpAgtIdr(rpAgtIdr);
    }

    // Property: rpLlave.rpEmpCve -> 10 RP-EMP-CVE X(05)
    public String getRpEmpCve() {
        return this.rpLlave.getRpEmpCve();
    }

    public void setRpEmpCve(final String rpEmpCve) {
        this.rpLlave.setRpEmpCve(rpEmpCve);
    }

    // Property: rpLlave.rpCnaNum -> 10 RP-CNA-NUM 9(04)
    public int getRpCnaNum() {
        return this.rpLlave.getRpCnaNum();
    }

    public void setRpCnaNum(final int rpCnaNum) {
        this.rpLlave.setRpCnaNum(rpCnaNum);
    }

    // Property: rpLlave.rpFecPrxAgt -> 10 RP-FEC-PRX-AGT X(10)
    public String getRpFecPrxAgt() {
        return this.rpLlave.getRpFecPrxAgt();
    }

    public void setRpFecPrxAgt(final String rpFecPrxAgt) {
        this.rpLlave.setRpFecPrxAgt(rpFecPrxAgt);
    }


    // Deeper properties

    // Property: rpData.rpDsgCve -> 10 RP-DSG-CVE 9(04)
    public int getRpDsgCve() {
        return this.rpData.getRpDsgCve();
    }

    public void setRpDsgCve(final int rpDsgCve) {
        this.rpData.setRpDsgCve(rpDsgCve);
    }

    // Property: rpData.rpFecPrxFac -> 10 RP-FEC-PRX-FAC X(10)
    public String getRpFecPrxFac() {
        return this.rpData.getRpFecPrxFac();
    }

    public void setRpFecPrxFac(final String rpFecPrxFac) {
        this.rpData.setRpFecPrxFac(rpFecPrxFac);
    }

    // Property: rpData.rpEsaCve -> 10 RP-ESA-CVE 9(04)
    public int getRpEsaCve() {
        return this.rpData.getRpEsaCve();
    }

    public void setRpEsaCve(final int rpEsaCve) {
        this.rpData.setRpEsaCve(rpEsaCve);
    }

    // Property: rpData.rpEssPrx -> 10 RP-ESS-PRX X(01)
    public String getRpEssPrx() {
        return this.rpData.getRpEssPrx();
    }

    public void setRpEssPrx(final String rpEssPrx) {
        this.rpData.setRpEssPrx(rpEssPrx);
    }

    // Property: rpData.rpCf5CfbCveDseCmp -> 10 RP-CF5-CFB-CVE-DSE-CMP X(04)
    public String getRpCf5CfbCveDseCmp() {
        return this.rpData.getRpCf5CfbCveDseCmp();
    }

    public void setRpCf5CfbCveDseCmp(final String rpCf5CfbCveDseCmp) {
        this.rpData.setRpCf5CfbCveDseCmp(rpCf5CfbCveDseCmp);
    }

    // Property: rpData.rpCf5ImpCtoDse -> 10 RP-CF5-IMP-CTO-DSE S9(11)V9(2)
    public NumericVar getRpCf5ImpCtoDse() {
        return this.rpData.getRpCf5ImpCtoDse();
    }

    public void setRpCf5ImpCtoDse(final BigDecimal rpCf5ImpCtoDse) {
        this.rpData.setRpCf5ImpCtoDse(rpCf5ImpCtoDse);
    }

    public void setRpCf5ImpCtoDse(final int rpCf5ImpCtoDse) {
        this.rpData.setRpCf5ImpCtoDse(rpCf5ImpCtoDse);
    }

    public void setRpCf5ImpCtoDse(final INumericValue rpCf5ImpCtoDse) {
        this.rpData.setRpCf5ImpCtoDse(rpCf5ImpCtoDse);
    }

    // Property: rpData.rpCf5PjeCtoDse -> 10 RP-CF5-PJE-CTO-DSE S9(03)V9(2)
    public NumericVar getRpCf5PjeCtoDse() {
        return this.rpData.getRpCf5PjeCtoDse();
    }

    public void setRpCf5PjeCtoDse(final BigDecimal rpCf5PjeCtoDse) {
        this.rpData.setRpCf5PjeCtoDse(rpCf5PjeCtoDse);
    }

    public void setRpCf5PjeCtoDse(final int rpCf5PjeCtoDse) {
        this.rpData.setRpCf5PjeCtoDse(rpCf5PjeCtoDse);
    }

    public void setRpCf5PjeCtoDse(final INumericValue rpCf5PjeCtoDse) {
        this.rpData.setRpCf5PjeCtoDse(rpCf5PjeCtoDse);
    }


    // Deeper properties

    // Property: rpDataImp.rpCf1ImpSbtFct -> 10 RP-CF1-IMP-SBT-FCT S9(11)V9(2)
    public NumericVar getRpCf1ImpSbtFct() {
        return this.rpDataImp.getRpCf1ImpSbtFct();
    }

    public void setRpCf1ImpSbtFct(final BigDecimal rpCf1ImpSbtFct) {
        this.rpDataImp.setRpCf1ImpSbtFct(rpCf1ImpSbtFct);
    }

    public void setRpCf1ImpSbtFct(final int rpCf1ImpSbtFct) {
        this.rpDataImp.setRpCf1ImpSbtFct(rpCf1ImpSbtFct);
    }

    public void setRpCf1ImpSbtFct(final INumericValue rpCf1ImpSbtFct) {
        this.rpDataImp.setRpCf1ImpSbtFct(rpCf1ImpSbtFct);
    }

    // Property: rpDataImp.rpCf1ImpTotFct -> 10 RP-CF1-IMP-TOT-FCT S9(11)V9(2)
    public NumericVar getRpCf1ImpTotFct() {
        return this.rpDataImp.getRpCf1ImpTotFct();
    }

    public void setRpCf1ImpTotFct(final BigDecimal rpCf1ImpTotFct) {
        this.rpDataImp.setRpCf1ImpTotFct(rpCf1ImpTotFct);
    }

    public void setRpCf1ImpTotFct(final int rpCf1ImpTotFct) {
        this.rpDataImp.setRpCf1ImpTotFct(rpCf1ImpTotFct);
    }

    public void setRpCf1ImpTotFct(final INumericValue rpCf1ImpTotFct) {
        this.rpDataImp.setRpCf1ImpTotFct(rpCf1ImpTotFct);
    }


    // Deeper properties

    // Property: rpDataSat.rpFolioFiscal -> 10 RP-FOLIO-FISCAL X(20)
    public String getRpFolioFiscal() {
        return this.rpDataSat.getRpFolioFiscal();
    }

    public void setRpFolioFiscal(final String rpFolioFiscal) {
        this.rpDataSat.setRpFolioFiscal(rpFolioFiscal);
    }

    // Property: rpDataSat.rpPercepGrav -> 10 RP-PERCEP-GRAV S9(11)V9(02)
    public NumericVar getRpPercepGrav() {
        return this.rpDataSat.getRpPercepGrav();
    }

    public void setRpPercepGrav(final BigDecimal rpPercepGrav) {
        this.rpDataSat.setRpPercepGrav(rpPercepGrav);
    }

    public void setRpPercepGrav(final int rpPercepGrav) {
        this.rpDataSat.setRpPercepGrav(rpPercepGrav);
    }

    public void setRpPercepGrav(final INumericValue rpPercepGrav) {
        this.rpDataSat.setRpPercepGrav(rpPercepGrav);
    }

    // Property: rpDataSat.rpPercepExen -> 10 RP-PERCEP-EXEN S9(11)V9(02)
    public NumericVar getRpPercepExen() {
        return this.rpDataSat.getRpPercepExen();
    }

    public void setRpPercepExen(final BigDecimal rpPercepExen) {
        this.rpDataSat.setRpPercepExen(rpPercepExen);
    }

    public void setRpPercepExen(final int rpPercepExen) {
        this.rpDataSat.setRpPercepExen(rpPercepExen);
    }

    public void setRpPercepExen(final INumericValue rpPercepExen) {
        this.rpDataSat.setRpPercepExen(rpPercepExen);
    }

    // Property: rpDataSat.rpIva -> 10 RP-IVA S9(11)V9(02)
    public NumericVar getRpIva() {
        return this.rpDataSat.getRpIva();
    }

    public void setRpIva(final BigDecimal rpIva) {
        this.rpDataSat.setRpIva(rpIva);
    }

    public void setRpIva(final int rpIva) {
        this.rpDataSat.setRpIva(rpIva);
    }

    public void setRpIva(final INumericValue rpIva) {
        this.rpDataSat.setRpIva(rpIva);
    }

    // Property: rpDataSat.rpIsr -> 10 RP-ISR S9(11)V9(02)
    public NumericVar getRpIsr() {
        return this.rpDataSat.getRpIsr();
    }

    public void setRpIsr(final BigDecimal rpIsr) {
        this.rpDataSat.setRpIsr(rpIsr);
    }

    public void setRpIsr(final int rpIsr) {
        this.rpDataSat.setRpIsr(rpIsr);
    }

    public void setRpIsr(final INumericValue rpIsr) {
        this.rpDataSat.setRpIsr(rpIsr);
    }

    // Property: rpDataSat.rpIvaRtn -> 10 RP-IVA-RTN S9(11)V9(02)
    public NumericVar getRpIvaRtn() {
        return this.rpDataSat.getRpIvaRtn();
    }

    public void setRpIvaRtn(final BigDecimal rpIvaRtn) {
        this.rpDataSat.setRpIvaRtn(rpIvaRtn);
    }

    public void setRpIvaRtn(final int rpIvaRtn) {
        this.rpDataSat.setRpIvaRtn(rpIvaRtn);
    }

    public void setRpIvaRtn(final INumericValue rpIvaRtn) {
        this.rpDataSat.setRpIvaRtn(rpIvaRtn);
    }

    // Property: rpDataSat.rpIvaAcr -> 10 RP-IVA-ACR S9(11)V9(02)
    public NumericVar getRpIvaAcr() {
        return this.rpDataSat.getRpIvaAcr();
    }

    public void setRpIvaAcr(final BigDecimal rpIvaAcr) {
        this.rpDataSat.setRpIvaAcr(rpIvaAcr);
    }

    public void setRpIvaAcr(final int rpIvaAcr) {
        this.rpDataSat.setRpIvaAcr(rpIvaAcr);
    }

    public void setRpIvaAcr(final INumericValue rpIvaAcr) {
        this.rpDataSat.setRpIvaAcr(rpIvaAcr);
    }

    // Property: rpDataSat.rpIsrRet -> 10 RP-ISR-RET S9(11)V9(02)
    public NumericVar getRpIsrRet() {
        return this.rpDataSat.getRpIsrRet();
    }

    public void setRpIsrRet(final BigDecimal rpIsrRet) {
        this.rpDataSat.setRpIsrRet(rpIsrRet);
    }

    public void setRpIsrRet(final int rpIsrRet) {
        this.rpDataSat.setRpIsrRet(rpIsrRet);
    }

    public void setRpIsrRet(final INumericValue rpIsrRet) {
        this.rpDataSat.setRpIsrRet(rpIsrRet);
    }

    // Property: rpDataSat.rpIpuRtnCed -> 10 RP-IPU-RTN-CED S9(11)V9(02)
    public NumericVar getRpIpuRtnCed() {
        return this.rpDataSat.getRpIpuRtnCed();
    }

    public void setRpIpuRtnCed(final BigDecimal rpIpuRtnCed) {
        this.rpDataSat.setRpIpuRtnCed(rpIpuRtnCed);
    }

    public void setRpIpuRtnCed(final int rpIpuRtnCed) {
        this.rpDataSat.setRpIpuRtnCed(rpIpuRtnCed);
    }

    public void setRpIpuRtnCed(final INumericValue rpIpuRtnCed) {
        this.rpDataSat.setRpIpuRtnCed(rpIpuRtnCed);
    }

    // Property: rpDataSat.rpTasaIva -> 10 RP-TASA-IVA S9(03)V9(02)
    public NumericVar getRpTasaIva() {
        return this.rpDataSat.getRpTasaIva();
    }

    public void setRpTasaIva(final BigDecimal rpTasaIva) {
        this.rpDataSat.setRpTasaIva(rpTasaIva);
    }

    public void setRpTasaIva(final int rpTasaIva) {
        this.rpDataSat.setRpTasaIva(rpTasaIva);
    }

    public void setRpTasaIva(final INumericValue rpTasaIva) {
        this.rpDataSat.setRpTasaIva(rpTasaIva);
    }

    // Property: rpDataSat.rpTasaCed -> 10 RP-TASA-CED S9(03)V9(02)
    public NumericVar getRpTasaCed() {
        return this.rpDataSat.getRpTasaCed();
    }

    public void setRpTasaCed(final BigDecimal rpTasaCed) {
        this.rpDataSat.setRpTasaCed(rpTasaCed);
    }

    public void setRpTasaCed(final int rpTasaCed) {
        this.rpDataSat.setRpTasaCed(rpTasaCed);
    }

    public void setRpTasaCed(final INumericValue rpTasaCed) {
        this.rpDataSat.setRpTasaCed(rpTasaCed);
    }


    // Deeper properties

    // Property: rpDataAgente.rpAgtRfc -> 10 RP-AGT-RFC X(13)
    public String getRpAgtRfc() {
        return this.rpDataAgente.getRpAgtRfc();
    }

    public void setRpAgtRfc(final String rpAgtRfc) {
        this.rpDataAgente.setRpAgtRfc(rpAgtRfc);
    }

    // Property: rpDataAgente.rpAgtNom -> 10 RP-AGT-NOM X(40)
    public String getRpAgtNom() {
        return this.rpDataAgente.getRpAgtNom();
    }

    public void setRpAgtNom(final String rpAgtNom) {
        this.rpDataAgente.setRpAgtNom(rpAgtNom);
    }

    // Property: rpDataAgente.rpAgtApePat -> 10 RP-AGT-APE-PAT X(40)
    public String getRpAgtApePat() {
        return this.rpDataAgente.getRpAgtApePat();
    }

    public void setRpAgtApePat(final String rpAgtApePat) {
        this.rpDataAgente.setRpAgtApePat(rpAgtApePat);
    }

    // Property: rpDataAgente.rpAgtApeMat -> 10 RP-AGT-APE-MAT X(30)
    public String getRpAgtApeMat() {
        return this.rpDataAgente.getRpAgtApeMat();
    }

    public void setRpAgtApeMat(final String rpAgtApeMat) {
        this.rpDataAgente.setRpAgtApeMat(rpAgtApeMat);
    }

    // Property: rpDataAgente.rpAgtRazSoc -> 10 RP-AGT-RAZ-SOC X(60)
    public String getRpAgtRazSoc() {
        return this.rpDataAgente.getRpAgtRazSoc();
    }

    public void setRpAgtRazSoc(final String rpAgtRazSoc) {
        this.rpDataAgente.setRpAgtRazSoc(rpAgtRazSoc);
    }

    // Property: rpDataAgente.rpAgtNomConcatenado -> 10 RP-AGT-NOM-CONCATENADO X(100)
    public String getRpAgtNomConcatenado() {
        return this.rpDataAgente.getRpAgtNomConcatenado();
    }

    public void setRpAgtNomConcatenado(final String rpAgtNomConcatenado) {
        this.rpDataAgente.setRpAgtNomConcatenado(rpAgtNomConcatenado);
    }

    // Property: rpDataAgente.rpPraAgtCxxCurp -> 10 RP-PRA-AGT-CXX-CURP X(18)
    public String getRpPraAgtCxxCurp() {
        return this.rpDataAgente.getRpPraAgtCxxCurp();
    }

    public void setRpPraAgtCxxCurp(final String rpPraAgtCxxCurp) {
        this.rpDataAgente.setRpPraAgtCxxCurp(rpPraAgtCxxCurp);
    }

    // Property: rpDataAgente.rpAgtNalCve -> 10 RP-AGT-NAL-CVE X(03)
    public String getRpAgtNalCve() {
        return this.rpDataAgente.getRpAgtNalCve();
    }

    public void setRpAgtNalCve(final String rpAgtNalCve) {
        this.rpDataAgente.setRpAgtNalCve(rpAgtNalCve);
    }

    // Property: rpDataAgente : condition RP-EXTRANJERO
    public boolean isRpExtranjero() {
        return this.rpDataAgente.isRpExtranjero();
    }

    public void setRpExtranjero() {
        this.rpDataAgente.setRpExtranjero();
    }

    // Property: rpDataAgente.rpMteCve -> 10 RP-MTE-CVE 9(04)
    public int getRpMteCve() {
        return this.rpDataAgente.getRpMteCve();
    }

    public void setRpMteCve(final int rpMteCve) {
        this.rpDataAgente.setRpMteCve(rpMteCve);
    }

    // Property: rpDataAgente.rpPefCve -> 10 RP-PEF-CVE 9(04)
    public int getRpPefCve() {
        return this.rpDataAgente.getRpPefCve();
    }

    public void setRpPefCve(final int rpPefCve) {
        this.rpDataAgente.setRpPefCve(rpPefCve);
    }

    // Property: rpDataAgente.rpDmaCae -> 10 RP-DMA-CAE X(40)
    public String getRpDmaCae() {
        return this.rpDataAgente.getRpDmaCae();
    }

    public void setRpDmaCae(final String rpDmaCae) {
        this.rpDataAgente.setRpDmaCae(rpDmaCae);
    }

    // Property: rpDataAgente.rpDmaNum -> 10 RP-DMA-NUM X(05)
    public String getRpDmaNum() {
        return this.rpDataAgente.getRpDmaNum();
    }

    public void setRpDmaNum(final String rpDmaNum) {
        this.rpDataAgente.setRpDmaNum(rpDmaNum);
    }

    // Property: rpDataAgente.rpDmaInt -> 10 RP-DMA-INT X(05)
    public String getRpDmaInt() {
        return this.rpDataAgente.getRpDmaInt();
    }

    public void setRpDmaInt(final String rpDmaInt) {
        this.rpDataAgente.setRpDmaInt(rpDmaInt);
    }

    // Property: rpDataAgente.rpDmaCol -> 10 RP-DMA-COL X(40)
    public String getRpDmaCol() {
        return this.rpDataAgente.getRpDmaCol();
    }

    public void setRpDmaCol(final String rpDmaCol) {
        this.rpDataAgente.setRpDmaCol(rpDmaCol);
    }

    // Property: rpDataAgente.rpMndNom -> 10 RP-MND-NOM X(50)
    public String getRpMndNom() {
        return this.rpDataAgente.getRpMndNom();
    }

    public void setRpMndNom(final String rpMndNom) {
        this.rpDataAgente.setRpMndNom(rpMndNom);
    }

    // Property: rpDataAgente.rpDmaCpoCve -> 10 RP-DMA-CPO-CVE 9(05)
    public int getRpDmaCpoCve() {
        return this.rpDataAgente.getRpDmaCpoCve();
    }

    public void setRpDmaCpoCve(final int rpDmaCpoCve) {
        this.rpDataAgente.setRpDmaCpoCve(rpDmaCpoCve);
    }

    // Property: rpDataAgente.rpPobNom -> 10 RP-POB-NOM X(40)
    public String getRpPobNom() {
        return this.rpDataAgente.getRpPobNom();
    }

    public void setRpPobNom(final String rpPobNom) {
        this.rpDataAgente.setRpPobNom(rpPobNom);
    }

    // Property: rpDataAgente.rpMndEdoCve -> 10 RP-MND-EDO-CVE X(04)
    public String getRpMndEdoCve() {
        return this.rpDataAgente.getRpMndEdoCve();
    }

    public void setRpMndEdoCve(final String rpMndEdoCve) {
        this.rpDataAgente.setRpMndEdoCve(rpMndEdoCve);
    }

    // Property: rpDataAgente.rpEdoEdoNom -> 10 RP-EDO-EDO-NOM X(30)
    public String getRpEdoEdoNom() {
        return this.rpDataAgente.getRpEdoEdoNom();
    }

    public void setRpEdoEdoNom(final String rpEdoEdoNom) {
        this.rpDataAgente.setRpEdoEdoNom(rpEdoEdoNom);
    }

    // Property: rpDataAgente.rpEdoPaiCve -> 10 RP-EDO-PAI-CVE X(03)
    public String getRpEdoPaiCve() {
        return this.rpDataAgente.getRpEdoPaiCve();
    }

    public void setRpEdoPaiCve(final String rpEdoPaiCve) {
        this.rpDataAgente.setRpEdoPaiCve(rpEdoPaiCve);
    }

    // Property: rpDataAgente.rpCmaDesTelefon -> 10 RP-CMA-DES-TELEFON X(60)
    public String getRpCmaDesTelefon() {
        return this.rpDataAgente.getRpCmaDesTelefon();
    }

    public void setRpCmaDesTelefon(final String rpCmaDesTelefon) {
        this.rpDataAgente.setRpCmaDesTelefon(rpCmaDesTelefon);
    }

    // Property: rpDataAgente.rpCnaGerZonEnv -> 10 RP-CNA-GER-ZON-ENV 9(04)
    public int getRpCnaGerZonEnv() {
        return this.rpDataAgente.getRpCnaGerZonEnv();
    }

    public void setRpCnaGerZonEnv(final int rpCnaGerZonEnv) {
        this.rpDataAgente.setRpCnaGerZonEnv(rpCnaGerZonEnv);
    }

    // Property: rpDataAgente.rpCnaOfnCve -> 10 RP-CNA-OFN-CVE 9(04)
    public int getRpCnaOfnCve() {
        return this.rpDataAgente.getRpCnaOfnCve();
    }

    public void setRpCnaOfnCve(final int rpCnaOfnCve) {
        this.rpDataAgente.setRpCnaOfnCve(rpCnaOfnCve);
    }


    // Deeper properties

    // Property: rpDataReceptor.rpRecCae -> 10 RP-REC-CAE X(40)
    public String getRpRecCae() {
        return this.rpDataReceptor.getRpRecCae();
    }

    public void setRpRecCae(final String rpRecCae) {
        this.rpDataReceptor.setRpRecCae(rpRecCae);
    }

    // Property: rpDataReceptor.rpRecNum -> 10 RP-REC-NUM X(05)
    public String getRpRecNum() {
        return this.rpDataReceptor.getRpRecNum();
    }

    public void setRpRecNum(final String rpRecNum) {
        this.rpDataReceptor.setRpRecNum(rpRecNum);
    }

    // Property: rpDataReceptor.rpRecInt -> 10 RP-REC-INT X(05)
    public String getRpRecInt() {
        return this.rpDataReceptor.getRpRecInt();
    }

    public void setRpRecInt(final String rpRecInt) {
        this.rpDataReceptor.setRpRecInt(rpRecInt);
    }

    // Property: rpDataReceptor.rpRecCol -> 10 RP-REC-COL X(40)
    public String getRpRecCol() {
        return this.rpDataReceptor.getRpRecCol();
    }

    public void setRpRecCol(final String rpRecCol) {
        this.rpDataReceptor.setRpRecCol(rpRecCol);
    }

    // Property: rpDataReceptor.rpRecMndNom -> 10 RP-REC-MND-NOM X(50)
    public String getRpRecMndNom() {
        return this.rpDataReceptor.getRpRecMndNom();
    }

    public void setRpRecMndNom(final String rpRecMndNom) {
        this.rpDataReceptor.setRpRecMndNom(rpRecMndNom);
    }

    // Property: rpDataReceptor.rpRecPobNom -> 10 RP-REC-POB-NOM X(40)
    public String getRpRecPobNom() {
        return this.rpDataReceptor.getRpRecPobNom();
    }

    public void setRpRecPobNom(final String rpRecPobNom) {
        this.rpDataReceptor.setRpRecPobNom(rpRecPobNom);
    }

    // Property: rpDataReceptor.rpRecMndEdoCve -> 10 RP-REC-MND-EDO-CVE X(04)
    public String getRpRecMndEdoCve() {
        return this.rpDataReceptor.getRpRecMndEdoCve();
    }

    public void setRpRecMndEdoCve(final String rpRecMndEdoCve) {
        this.rpDataReceptor.setRpRecMndEdoCve(rpRecMndEdoCve);
    }

    // Property: rpDataReceptor.rpRecMndEdoNom -> 10 RP-REC-MND-EDO-NOM X(30)
    public String getRpRecMndEdoNom() {
        return this.rpDataReceptor.getRpRecMndEdoNom();
    }

    public void setRpRecMndEdoNom(final String rpRecMndEdoNom) {
        this.rpDataReceptor.setRpRecMndEdoNom(rpRecMndEdoNom);
    }

    // Property: rpDataReceptor.rpRecMndPaiCve -> 10 RP-REC-MND-PAI-CVE X(03)
    public String getRpRecMndPaiCve() {
        return this.rpDataReceptor.getRpRecMndPaiCve();
    }

    public void setRpRecMndPaiCve(final String rpRecMndPaiCve) {
        this.rpDataReceptor.setRpRecMndPaiCve(rpRecMndPaiCve);
    }

    // Property: rpDataReceptor.rpRecMndPaiNom -> 10 RP-REC-MND-PAI-NOM X(30)
    public String getRpRecMndPaiNom() {
        return this.rpDataReceptor.getRpRecMndPaiNom();
    }

    public void setRpRecMndPaiNom(final String rpRecMndPaiNom) {
        this.rpDataReceptor.setRpRecMndPaiNom(rpRecMndPaiNom);
    }

    // Property: rpDataReceptor.rpRecCpoCve -> 10 RP-REC-CPO-CVE 9(05)
    public int getRpRecCpoCve() {
        return this.rpDataReceptor.getRpRecCpoCve();
    }

    public void setRpRecCpoCve(final int rpRecCpoCve) {
        this.rpDataReceptor.setRpRecCpoCve(rpRecCpoCve);
    }

    // Property: rpDataReceptor.rpRecGerZonEnv -> 10 RP-REC-GER-ZON-ENV 9(04)
    public int getRpRecGerZonEnv() {
        return this.rpDataReceptor.getRpRecGerZonEnv();
    }

    public void setRpRecGerZonEnv(final int rpRecGerZonEnv) {
        this.rpDataReceptor.setRpRecGerZonEnv(rpRecGerZonEnv);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpLlave.initialize();
        this.rpData.initialize();
        this.rpDataImp.initialize();
        this.rpDataSat.initialize();
        this.rpDataAgente.initialize();
        this.rpDataReceptor.initialize();
    }

}

