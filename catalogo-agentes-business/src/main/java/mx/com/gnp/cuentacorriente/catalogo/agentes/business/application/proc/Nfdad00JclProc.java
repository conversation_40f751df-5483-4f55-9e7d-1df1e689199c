/**
 *  JCL Program: NFDAD00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Nfdad00JclProc extends AbstractJclProgram {

    public Nfdad00JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("NFDAD00", (nfdad00) -> {
            nfdad00.addParameter("PROC");
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                * * *    NFDAD00    * * *                          **/
                /*//*   APLICACION:  CUENTA CORRIENTE                                   **/
                /*//*   PROCESO:     D A R I O.                                         **/
                /*//*   OBJETIVO :   GENERA VSAM CUYA FUENTE SEA ALGUN REGISTRO EN IDMS.**/
                /*//*                GNPSR00101371                                      **/
                /*//*   COMENTARIOS:                                                    **/
                /*//*   CORRE                                                           **/
                /*//*   DESPUES DE:  PGANFDAB                                           **/
                /*//*   ANTES DE:    PGANFD04, PGANFDE1 Y PGAQADIK                      **/
                /*//*   ELABORO:     RAFAEL DIAZ-MIRON CH. (HITSS)   FECHA: 20/ABR/2017 **/
                /*//**********************************************************************/
                /*//*                        * NFDAD12 *                                **/
                /*//* OBJETIVO DEL STEP:  SORT PARA ELIMINAR DUPLICADOS DEL ARCHIVO     **/
                /*//*                     DE : GLQF-ESTRUCTURA                          **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                /*//*NFDAD12  EXEC PGM=SORT,COND=(4,LT)*/
                /*//*SORTIN   DD DSN=PGA.PPCQS.NFDAB00.EAFVAB03,DISP=SHR*/
                /*//*SORTOUT  DD DSN=PGA.NFDAD00.SINDUPS,*/
                /*//*             DISP=(NEW,CATLG,DELETE),*/
                /*//*             UNIT=WORKPROD,*/
                /*//*             SPACE=(TRK,(150,15),RLSE),*/
                /*//*             DCB=(LRECL=80,RECFM=FB,BLKSIZE=0,DSORG=PS)*/
                /*//*SYSPRINT DD SYSOUT=**/
                /*//*SYSOUT   DD SYSOUT=**/
                /*//*SYSIN    DD DSN=PNCQP.BTCH.CARDS(NFDAD09),DISP=SHR*/
                /*//**********************************************************************/
                /*//*                        * NFDAD11 *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA VSAM                                   **/
                /*//*                     PARA REGISTRO: GLQF-AGENTEMOD                 **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                /*//*NFDAD11  EXEC PGM=IDCAMS,COND=(4,LT)*/
                /*//*SYSOUT   DD SYSOUT=**/
                /*//*SYSPRINT DD SYSOUT=**/
                /*//*SYSIN    DD DSN=PNCQP.BTCH.CARDS(NFDAD01),DISP=SHR*/
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD10 *                                **/
                /*//* OBJETIVO DEL STEP:  CARGA VSAM CON SECUENCIAL                     **/
                /*//*                     DEL REGISTRO: GLQF-AGENTEMOD                  **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                /*//*NFDAD10  EXEC PGM=IDCAMS,COND=(4,LT)*/
                /*//**/
                /*//* ARCHIVO CON REGISTRO GLQF-AGENTEMOD COMPLETO PARA CREAR VSAM*/
                /*//*SYSUT1   DD  DSN=PGA.PPCQS.NFDAB00.EAFVAB01,DISP=SHR*/
                /*//**/
                /*//* VSAM CON REGISTRO GLQF-AGENTEMOD COMPLETO*/
                /*//*SYSUT2   DD  DSN=PGAV.NFDAD00.EAFVADV1,DISP=SHR*/
                /*//**/
                /*//*SYSOUT   DD SYSOUT=**/
                /*//*SYSPRINT DD SYSOUT=**/
                /*//*SYSIN    DD DSN=PNCQP.BTCH.CARDS(NFDAD03),DISP=SHR*/
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD09 *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA VSAM CON LLAVE ALTERNA Y CARGA INDICE  **/
                /*//*                     PARA REGISTRO: GLQF-AGENTEMOD                 **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                /*//*NFDAD09  EXEC PGM=IDCAMS,COND=(4,LT)*/
                /*//*SYSOUT   DD SYSOUT=**/
                /*//*SYSPRINT DD SYSOUT=**/
                /*//*SYSIN    DD DSN=PNCQP.BTCH.CARDS(NFDAD02),DISP=SHR*/
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD08 *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA VSAM                                   **/
                /*//*                     PARA REGISTRO: GLQF-CONCEPTO                  **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD08", "IDCAMS", (nfdad08) -> {
                    nfdad08.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad08.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad08.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD04)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD8A *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA ARCHIVO CON NUEVES                     **/
                /*//*                                                                   **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD8A", "SORT", (nfdad8a) -> {
                    nfdad8a.addDataDefinition("SORTIN", "PNCQP.BTCH.CARDS(NFDAD0I)",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    nfdad8a.addDataDefinition("SORTOUT", "PGA.NFDAD00.EAFVAB04.NUEVES",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(1,1),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=128,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdad8a.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad8a.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad8a.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD0J)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD07 *                                **/
                /*//* OBJETIVO DEL STEP:  CARGA VSAM CON SECUENCIAL                     **/
                /*//*                     DEL REGISTRO: GLQF-CONCEPTO                   **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD07", "SORT", (nfdad07) -> {
                    /*//**/
                    /*//* ARCHIVO CON REGISTRO GLQF-CONCEPTO COMPLETO PARA CREAR VSAM*/
                    nfdad07.addDataDefinition("SORTIN", "PNCQP.BTCH.CARDS(NFDAD0A)",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//**/
                    /*//* VSAM CON REGISTRO GLQF-CONCEPTO COMPLETO*/
                    nfdad07.addDataDefinition("SORTOUT", "PGAV.NFDAD00.EAFVADV2",  (sortout) -> {
                        sortout.addParameter("DISP","SHR");
                    });
                    /*//**/
                    nfdad07.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad07.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad07.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD0B)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD06 *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA VSAM                                   **/
                /*//*                     PARA REGISTRO: GLQF-ESTRUCTURA                **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD06", "IDCAMS", (nfdad06) -> {
                    nfdad06.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad06.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad06.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD05)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD6A *                                **/
                /*//* OBJETIVO DEL STEP:  EXTRAE TABLA DB2                              **/
                /*//*                     PARA REGISTRO: GLQF-ESTRUCTURA                **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD6A", "IKJEFT01", (nfdad6a) -> {
                    nfdad6a.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdad6a.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    nfdad6a.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad6a.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad6a.addDummyDataDefinition("SYSPUNCH",  (syspunch) -> {
                    });
                    nfdad6a.addDataDefinition("SYSREC00", "PGA.NFDAD00.SINDUPS",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","TEMPROD");
                        sysrec00.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sysrec00.addParameter("DCB", "(LRECL=200,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdad6a.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD0C)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                    nfdad6a.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDAD0D)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD6B *                                **/
                /*//* OBJETIVO DEL STEP:  SORT DEL ARCHIVO GENERADO EN EL PASO ANTERIOR **/
                /*//*                     DEL REGISTRO: GLQF-ESTRUCTURA                 **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD6B", "SORT", (nfdad6b) -> {
                    /*//**/
                    /*//* ARCHIVO CON REGISTRO GLQF-ESTRUCTURA COMPLETO PARA CREAR VSAM*/
                    nfdad6b.addDataDefinition("SORTIN", "PGA.NFDAD00.SINDUPS",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//**/
                    /*//* VSAM CON REGISTRO GLQF-ESTRUCTURA COMPLETO*/
                    nfdad6b.addDataDefinition("SORTOUT", "PGA.NFDAD00.OUTREC",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=80,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    /*//**/
                    nfdad6b.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad6b.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad6b.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD0E)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                        * NFDAD6C *                                **/
                /*//* OBJETIVO DEL STEP:  SORT DEL ARCHIVO GENERADO EN EL PASO ANTERIOR **/
                /*//*                     DEL REGISTRO: GLQF-ESTRUCTURA                 **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD6C", "SORT", (nfdad6c) -> {
                    /*//**/
                    /*//* ARCHIVO CON REGISTRO GLQF-ESTRUCTURA COMPLETO PARA CREAR VSAM*/
                    nfdad6c.addDataDefinition("SORTIN", "PGA.NFDAD00.OUTREC",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//**/
                    /*//* VSAM CON REGISTRO GLQF-ESTRUCTURA COMPLETO*/
                    nfdad6c.addDataDefinition("SORTOUT", "PGA.NFDAD00.OUTREC.SORT",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=80,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    /*//**/
                    nfdad6c.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad6c.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad6c.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD09)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                        * NFDAD6D *                                **/
                /*//* OBJETIVO DEL STEP:  EXTRAE TABLA DB2                              **/
                /*//*                     PARA REGISTRO: GLQF-ESTRUCTURA                **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD6D", "IKJEFT01", (nfdad6d) -> {
                    nfdad6d.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdad6d.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    nfdad6d.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad6d.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad6d.addDummyDataDefinition("SYSPUNCH",  (syspunch) -> {
                    });
                    nfdad6d.addDataDefinition("SYSREC00", "PGA.NFDAD00.EAFVAB04",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","TEMPROD");
                        sysrec00.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sysrec00.addParameter("DCB", "(LRECL=200,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdad6d.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD0F)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                    nfdad6d.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDAD0D)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                        * NFDAD6E *                                **/
                /*//* OBJETIVO DEL STEP:  SORT DEL ARCHIVO GENERADO EN EL PASO ANTERIOR **/
                /*//*                     DEL REGISTRO: GLQF-ESTRUCTURA                 **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD6E", "SORT", (nfdad6e) -> {
                    /*//**/
                    /*//* ARCHIVO CON REGISTRO GLQF-ESTRUCTURA COMPLETO PARA CREAR VSAM*/
                    nfdad6e.addDataDefinition("SORTIN", "PGA.NFDAD00.EAFVAB04",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//**/
                    /*//* VSAM CON REGISTRO GLQF-ESTRUCTURA COMPLETO*/
                    nfdad6e.addDataDefinition("SORTOUT", "PGA.NFDAD00.EAFVAB04.OUTREC",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=128,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    /*//**/
                    nfdad6e.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad6e.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad6e.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD0G)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD6F *                                **/
                /*//* OBJETIVO DEL STEP:  SORT DEL ARCHIVO GENERADO EN EL PASO ANTERIOR **/
                /*//*                     DEL REGISTRO: GLQF-ESTRUCTURA                 **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD6F", "SORT", (nfdad6f) -> {
                    nfdad6f.addDataDefinition("SORTIN",  (sortin) -> {
                        /*//**/
                        /*//* ARCHIVO CON REGISTRO GLQF-ESTRUCTURA COMPLETO PARA CREAR VSAM*/
                        sortin.addDataDefinition("SORTIN", "PGA.NFDAD00.EAFVAB04.OUTREC",  (_concat) -> {
                            _concat.addParameter("DISP","SHR");
                        });
                        sortin.addDataDefinition("SORTIN", "PGA.NFDAD00.EAFVAB04.NUEVES",  (_concat) -> {
                            _concat.addParameter("DISP","SHR");
                        });
                    });
                    /*//**/
                    /*//* VSAM CON REGISTRO GLQF-ESTRUCTURA COMPLETO*/
                    nfdad6f.addDataDefinition("SORTOUT", "PGA.NFDAD00.EAFVAB04.OUTREC.S",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=128,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    /*//**/
                    nfdad6f.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad6f.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad6f.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD0H)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                        * NFDAD05 *                                **/
                /*//* OBJETIVO DEL STEP:  CARGA VSAM CON SECUENCIAL                     **/
                /*//*                     DEL REGISTRO: GLQF-ESTRUCTURA                 **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD05", "IDCAMS", (nfdad05) -> {
                    /*//**/
                    /*//* ARCHIVO CON REGISTRO GLQF-ESTRUCTURA COMPLETO PARA CREAR VSAM*/
                    nfdad05.addDataDefinition("SYSUT1", "PGA.NFDAD00.OUTREC.SORT",  (sysut1) -> {
                        sysut1.addParameter("DISP","SHR");
                    });
                    /*//**/
                    /*//* VSAM CON REGISTRO GLQF-ESTRUCTURA COMPLETO*/
                    nfdad05.addDataDefinition("SYSUT2", "PGAV.NFDAD00.EAFVADV3",  (sysut2) -> {
                        sysut2.addParameter("DISP","SHR");
                    });
                    /*//**/
                    nfdad05.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad05.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad05.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD03)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD04 *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA VSAM CON LLAVE ALTERNA Y CARGA INDICE  **/
                /*//*                     PARA REGISTRO: GLQF-ESTRUCTURA                **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD04", "IDCAMS", (nfdad04) -> {
                    nfdad04.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad04.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad04.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD06)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD03 *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA VSAM                                   **/
                /*//*                     PARA REGISTRO: GLQF-CATALOGO                  **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD03", "IDCAMS", (nfdad03) -> {
                    nfdad03.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad03.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad03.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD07)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD02 *                                **/
                /*//* OBJETIVO DEL STEP:  CARGA VSAM CON SECUENCIAL                     **/
                /*//*                     DEL REGISTRO: GLQF-CATALOGO                   **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD02", "IDCAMS", (nfdad02) -> {
                    /*//**/
                    /*//* ARCHIVO CON REGISTRO GLQF-CATALOGO COMPLETO PARA CREAR VSAM*/
                    nfdad02.addDataDefinition("SYSUT1", "PGA.NFDAD00.EAFVAB04.OUTREC.S",  (sysut1) -> {
                        sysut1.addParameter("DISP","SHR");
                    });
                    /*//**/
                    /*//* VSAM CON REGISTRO GLQF-CATALOGO COMPLETO*/
                    nfdad02.addDataDefinition("SYSUT2", "PGAV.NFDAD00.EAFVADV4",  (sysut2) -> {
                        sysut2.addParameter("DISP","SHR");
                    });
                    /*//**/
                    nfdad02.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad02.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad02.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD03)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdad00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDAD01 *                                **/
                /*//* OBJETIVO DEL STEP:  GENERA VSAM CON LLAVE ALTERNA Y CARGA INDICE  **/
                /*//*                     PARA REGISTRO: GLQF-CATALOGO                  **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdad00.execProgram("NFDAD01", "IDCAMS", (nfdad01) -> {
                    nfdad01.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdad01.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdad01.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDAD08)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
