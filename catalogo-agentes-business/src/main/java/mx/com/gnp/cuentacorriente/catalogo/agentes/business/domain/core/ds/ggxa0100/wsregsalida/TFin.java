package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfin.TFinElem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-FIN.
 *
 */
// 208           10 T-FIN       OCCURS 1 TO 2                                 
@Setter
@Getter
public class TFin extends DataStructArray<TFin> {
    // Properties
    private TFinElem tFinElem = new  TFinElem(this);                         // 15 T-FIN-ELEM

    public TFin(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TFin(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TFin(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TFin(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TFin at(int idx) {
        return new TFin(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tFinElem.sTablaFin -> 20 S-TABLA-FIN X(09)
    public AlphanumericVar getSTablaFin() {
        return this.tFinElem.getSTablaFin();
    }

    public void setSTablaFin(final String sTablaFin) {
        this.tFinElem.setSTablaFin(sTablaFin);
    }

    public void setSTablaFin(final IAlphanumericValue sTablaFin) {
        this.tFinElem.setSTablaFin(sTablaFin);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tFinElem.initialize();
    }

}

