package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.tmpfillerindicadoresdenulos.IndicadoresDsc;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: tmpFILLER-INDICADORES-DE-NULOS.
 *
 */
// 320      ******************************************************************
// 321      * DECLARACION DE INDICADORES NULOS FOR TABLES                    *
// 322      ******************************************************************
@Setter
@Getter
public class TmpfillerIndicadoresDeNulos { // VStruct
    // Properties
    private IndicadoresDsc indicadoresDsc = new  IndicadoresDsc();           // 03 INDICADORES-DSC

    public TmpfillerIndicadoresDeNulos() {
        initialize();
    }




    // Deeper properties

    // Property: indicadoresDsc.inddscdsg -> 05 INDDSCDSG S9(4) COMPUTATIONAL
    public int getInddscdsg() {
        return this.indicadoresDsc.getInddscdsg();
    }

    public void setInddscdsg(final int inddscdsg) {
        this.indicadoresDsc.setInddscdsg(inddscdsg);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.indicadoresDsc.initialize();
    }

}

