package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-DATE-SIN-G.
 *
 */
@Setter
@Getter
public class WsAuxDateSinG extends DataStruct {
    // Properties
    private UnsignedNumericVar wsAuxDateSinGAaaa = new UnsignedNumericVar(this, 4, 0);       // 10 WS-AUX-DATE-SIN-G-AAAA 9(04)
    private UnsignedNumericVar wsAuxDateSinGMm = new UnsignedNumericVar(this, 2, 0);         // 10 WS-AUX-DATE-SIN-G-MM 9(02)
    private UnsignedNumericVar wsAuxDateSinGDd = new UnsignedNumericVar(this, 2, 0);         // 10 WS-AUX-DATE-SIN-G-DD 9(02)

    public WsAuxDateSinG() {
        super();
        initialize();
    }

    public WsAuxDateSinG(DataContainer parent) {
        super(parent);
    }

    public WsAuxDateSinG(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAuxDateSinGAaaa(final int wsAuxDateSinGAaaa) {
        this.wsAuxDateSinGAaaa.setValue(wsAuxDateSinGAaaa);
    }

    public void setWsAuxDateSinGAaaa(final BigDecimal wsAuxDateSinGAaaa) {
        this.wsAuxDateSinGAaaa.setValue(wsAuxDateSinGAaaa);
    }

    public void setWsAuxDateSinGAaaa(final INumericValue wsAuxDateSinGAaaa) {
        this.wsAuxDateSinGAaaa.setValue(wsAuxDateSinGAaaa);
    }

    public void setWsAuxDateSinGMm(final int wsAuxDateSinGMm) {
        this.wsAuxDateSinGMm.setValue(wsAuxDateSinGMm);
    }

    public void setWsAuxDateSinGMm(final BigDecimal wsAuxDateSinGMm) {
        this.wsAuxDateSinGMm.setValue(wsAuxDateSinGMm);
    }

    public void setWsAuxDateSinGMm(final INumericValue wsAuxDateSinGMm) {
        this.wsAuxDateSinGMm.setValue(wsAuxDateSinGMm);
    }

    public void setWsAuxDateSinGDd(final int wsAuxDateSinGDd) {
        this.wsAuxDateSinGDd.setValue(wsAuxDateSinGDd);
    }

    public void setWsAuxDateSinGDd(final BigDecimal wsAuxDateSinGDd) {
        this.wsAuxDateSinGDd.setValue(wsAuxDateSinGDd);
    }

    public void setWsAuxDateSinGDd(final INumericValue wsAuxDateSinGDd) {
        this.wsAuxDateSinGDd.setValue(wsAuxDateSinGDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAuxDateSinGAaaa.clear();
        this.wsAuxDateSinGMm.clear();
        this.wsAuxDateSinGDd.clear();
    }

}

