package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DATOS-PARAM.
 *
 */
@Setter
@Getter
public class WsDatosParam { // VStruct
    // Properties
    private int wsTipoNomina;                                               // 03 WS-TIPO-NOMINA 9(01)

    public WsDatosParam() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsTipoNomina = 0;
    }

}

