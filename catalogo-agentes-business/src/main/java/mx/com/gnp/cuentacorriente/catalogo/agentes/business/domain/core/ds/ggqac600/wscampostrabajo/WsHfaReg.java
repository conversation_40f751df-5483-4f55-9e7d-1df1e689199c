package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-HFA-REG.
 *
 */
@Setter
@Getter
public class WsHfaReg extends DataStruct {
    // Properties
    private PackedVar wsHfaAgtIdr = new PackedVar(this, 9, 0);               // 10 WS-HFA-AGT-IDR S9(09) COMPUTATIONAL-3
    private AlphanumericVar wsHfaFecIniPdo = new AlphanumericVar(this, 10);  // 10 WS-HFA-FEC-INI-PDO X(10)
    private AlphanumericVar wsHfaFecFinPdo = new AlphanumericVar(this, 10);  // 10 WS-HFA-FEC-FIN-PDO X(10)
    private BinaryVar wsHfaEsaCve = new BinaryVar(this, 4, 0);               // 10 WS-HFA-ESA-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsHfaPefCve = new BinaryVar(this, 4, 0);               // 10 WS-HFA-PEF-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsHfaOfnCve = new BinaryVar(this, 4, 0);               // 10 WS-HFA-OFN-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsHfaCnaGerZonEnv = new BinaryVar(this, 4, 0);         // 10 WS-HFA-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    private BinaryVar wsHfaDsgCve = new BinaryVar(this, 4, 0);               // 10 WS-HFA-DSG-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar wsHfaAgtRfc = new AlphanumericVar(this, 13);     // 10 WS-HFA-AGT-RFC X(13)
    private AlphanumericVar wsHfaNomEmi = new AlphanumericVar(this, 100);    // 10 WS-HFA-NOM-EMI X(100)
    private AlphanumericVar wsHfaDmaCae = new AlphanumericVar(this, 40);     // 10 WS-HFA-DMA-CAE X(40)
    private AlphanumericVar wsHfaDmaNum = new AlphanumericVar(this, 5);      // 10 WS-HFA-DMA-NUM X(05)
    private AlphanumericVar wsHfaDmaInt = new AlphanumericVar(this, 5);      // 10 WS-HFA-DMA-INT X(05)
    private AlphanumericVar wsHfaDmaCol = new AlphanumericVar(this, 40);     // 10 WS-HFA-DMA-COL X(40)
    private AlphanumericVar wsHfaMndNom = new AlphanumericVar(this, 50);     // 10 WS-HFA-MND-NOM X(50)
    private AlphanumericVar wsHfaEdoNom = new AlphanumericVar(this, 30);     // 10 WS-HFA-EDO-NOM X(30)
    private AlphanumericVar wsHfaPaiNom = new AlphanumericVar(this, 30);     // 10 WS-HFA-PAI-NOM X(30)
    private BinaryVar wsHfaCpoCve = new BinaryVar(this, 9, 0);               // 10 WS-HFA-CPO-CVE S9(9) COMPUTATIONAL
    private BinaryVar wsHfaMteCve = new BinaryVar(this, 4, 0);               // 10 WS-HFA-MTE-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsHfaTfpCve = new BinaryVar(this, 4, 0);               // 10 WS-HFA-TFP-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar wsHfaCtbNumCta = new AlphanumericVar(this, 18);  // 10 WS-HFA-CTB-NUM-CTA X(18)
    private AlphanumericVar wsHfaIndPag = new AlphanumericVar(this, 1);      // 10 WS-HFA-IND-PAG X(01)
    private AlphanumericVar wsHfaIndExc = new AlphanumericVar(this, 1);      // 10 WS-HFA-IND-EXC X(01)
    private AlphanumericVar wsHfaUsuCve = new AlphanumericVar(this, 8);      // 10 WS-HFA-USU-CVE X(08)
    private AlphanumericVar wsHfaNomPrg = new AlphanumericVar(this, 8);      // 10 WS-HFA-NOM-PRG X(08)
    private AlphanumericVar wsHfaFecUltAct = new AlphanumericVar(this, 26);  // 10 WS-HFA-FEC-ULT-ACT X(26)
    private PackedVar wsHfaPjeIva = new PackedVar(this, 5, 2);               // 10 WS-HFA-PJE-IVA S9(3)V9(2) COMPUTATIONAL-3
    private PackedVar wsHfaPjeIsr = new PackedVar(this, 5, 2);               // 10 WS-HFA-PJE-ISR S9(3)V9(2) COMPUTATIONAL-3
    private AlphanumericVar wsHfaCdregfis = new AlphanumericVar(this, 3);    // 10 WS-HFA-CDREGFIS X(03)
    private AlphanumericVar wsHfaNomrznso = new AlphanumericVar(this, 300);  // 10 WS-HFA-NOMRZNSO X(300)
    private PackedVar wsHfaCedNovi = new PackedVar(this, 5, 2);              // 10 WS-HFA-CED-NOVI S9(3)V9(2) COMPUTATIONAL-3
    private PackedVar wsHfaCedVida = new PackedVar(this, 5, 2);              // 10 WS-HFA-CED-VIDA S9(3)V9(2) COMPUTATIONAL-3

    public WsHfaReg() {
        super();
        initialize();
    }

    public WsHfaReg(DataContainer parent) {
        super(parent);
    }

    public WsHfaReg(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsHfaAgtIdr(final int wsHfaAgtIdr) {
        this.wsHfaAgtIdr.setValue(wsHfaAgtIdr);
    }

    public void setWsHfaAgtIdr(final BigDecimal wsHfaAgtIdr) {
        this.wsHfaAgtIdr.setValue(wsHfaAgtIdr);
    }

    public void setWsHfaAgtIdr(final INumericValue wsHfaAgtIdr) {
        this.wsHfaAgtIdr.setValue(wsHfaAgtIdr);
    }

    public void setWsHfaFecIniPdo(final String wsHfaFecIniPdo) {
        this.wsHfaFecIniPdo.setValue(wsHfaFecIniPdo);
    }

    public void setWsHfaFecIniPdo(final IAlphanumericValue wsHfaFecIniPdo) {
        this.wsHfaFecIniPdo.setValue(wsHfaFecIniPdo);
    }

    public void setWsHfaFecFinPdo(final String wsHfaFecFinPdo) {
        this.wsHfaFecFinPdo.setValue(wsHfaFecFinPdo);
    }

    public void setWsHfaFecFinPdo(final IAlphanumericValue wsHfaFecFinPdo) {
        this.wsHfaFecFinPdo.setValue(wsHfaFecFinPdo);
    }

    public void setWsHfaEsaCve(final int wsHfaEsaCve) {
        this.wsHfaEsaCve.setValue(wsHfaEsaCve);
    }

    public void setWsHfaEsaCve(final BigDecimal wsHfaEsaCve) {
        this.wsHfaEsaCve.setValue(wsHfaEsaCve);
    }

    public void setWsHfaEsaCve(final INumericValue wsHfaEsaCve) {
        this.wsHfaEsaCve.setValue(wsHfaEsaCve);
    }

    public void setWsHfaPefCve(final int wsHfaPefCve) {
        this.wsHfaPefCve.setValue(wsHfaPefCve);
    }

    public void setWsHfaPefCve(final BigDecimal wsHfaPefCve) {
        this.wsHfaPefCve.setValue(wsHfaPefCve);
    }

    public void setWsHfaPefCve(final INumericValue wsHfaPefCve) {
        this.wsHfaPefCve.setValue(wsHfaPefCve);
    }

    public void setWsHfaOfnCve(final int wsHfaOfnCve) {
        this.wsHfaOfnCve.setValue(wsHfaOfnCve);
    }

    public void setWsHfaOfnCve(final BigDecimal wsHfaOfnCve) {
        this.wsHfaOfnCve.setValue(wsHfaOfnCve);
    }

    public void setWsHfaOfnCve(final INumericValue wsHfaOfnCve) {
        this.wsHfaOfnCve.setValue(wsHfaOfnCve);
    }

    public void setWsHfaCnaGerZonEnv(final int wsHfaCnaGerZonEnv) {
        this.wsHfaCnaGerZonEnv.setValue(wsHfaCnaGerZonEnv);
    }

    public void setWsHfaCnaGerZonEnv(final BigDecimal wsHfaCnaGerZonEnv) {
        this.wsHfaCnaGerZonEnv.setValue(wsHfaCnaGerZonEnv);
    }

    public void setWsHfaCnaGerZonEnv(final INumericValue wsHfaCnaGerZonEnv) {
        this.wsHfaCnaGerZonEnv.setValue(wsHfaCnaGerZonEnv);
    }

    public void setWsHfaDsgCve(final int wsHfaDsgCve) {
        this.wsHfaDsgCve.setValue(wsHfaDsgCve);
    }

    public void setWsHfaDsgCve(final BigDecimal wsHfaDsgCve) {
        this.wsHfaDsgCve.setValue(wsHfaDsgCve);
    }

    public void setWsHfaDsgCve(final INumericValue wsHfaDsgCve) {
        this.wsHfaDsgCve.setValue(wsHfaDsgCve);
    }

    public void setWsHfaAgtRfc(final String wsHfaAgtRfc) {
        this.wsHfaAgtRfc.setValue(wsHfaAgtRfc);
    }

    public void setWsHfaAgtRfc(final IAlphanumericValue wsHfaAgtRfc) {
        this.wsHfaAgtRfc.setValue(wsHfaAgtRfc);
    }

    public void setWsHfaNomEmi(final String wsHfaNomEmi) {
        this.wsHfaNomEmi.setValue(wsHfaNomEmi);
    }

    public void setWsHfaNomEmi(final IAlphanumericValue wsHfaNomEmi) {
        this.wsHfaNomEmi.setValue(wsHfaNomEmi);
    }

    public void setWsHfaDmaCae(final String wsHfaDmaCae) {
        this.wsHfaDmaCae.setValue(wsHfaDmaCae);
    }

    public void setWsHfaDmaCae(final IAlphanumericValue wsHfaDmaCae) {
        this.wsHfaDmaCae.setValue(wsHfaDmaCae);
    }

    public void setWsHfaDmaNum(final String wsHfaDmaNum) {
        this.wsHfaDmaNum.setValue(wsHfaDmaNum);
    }

    public void setWsHfaDmaNum(final IAlphanumericValue wsHfaDmaNum) {
        this.wsHfaDmaNum.setValue(wsHfaDmaNum);
    }

    public void setWsHfaDmaInt(final String wsHfaDmaInt) {
        this.wsHfaDmaInt.setValue(wsHfaDmaInt);
    }

    public void setWsHfaDmaInt(final IAlphanumericValue wsHfaDmaInt) {
        this.wsHfaDmaInt.setValue(wsHfaDmaInt);
    }

    public void setWsHfaDmaCol(final String wsHfaDmaCol) {
        this.wsHfaDmaCol.setValue(wsHfaDmaCol);
    }

    public void setWsHfaDmaCol(final IAlphanumericValue wsHfaDmaCol) {
        this.wsHfaDmaCol.setValue(wsHfaDmaCol);
    }

    public void setWsHfaMndNom(final String wsHfaMndNom) {
        this.wsHfaMndNom.setValue(wsHfaMndNom);
    }

    public void setWsHfaMndNom(final IAlphanumericValue wsHfaMndNom) {
        this.wsHfaMndNom.setValue(wsHfaMndNom);
    }

    public void setWsHfaEdoNom(final String wsHfaEdoNom) {
        this.wsHfaEdoNom.setValue(wsHfaEdoNom);
    }

    public void setWsHfaEdoNom(final IAlphanumericValue wsHfaEdoNom) {
        this.wsHfaEdoNom.setValue(wsHfaEdoNom);
    }

    public void setWsHfaPaiNom(final String wsHfaPaiNom) {
        this.wsHfaPaiNom.setValue(wsHfaPaiNom);
    }

    public void setWsHfaPaiNom(final IAlphanumericValue wsHfaPaiNom) {
        this.wsHfaPaiNom.setValue(wsHfaPaiNom);
    }

    public void setWsHfaCpoCve(final int wsHfaCpoCve) {
        this.wsHfaCpoCve.setValue(wsHfaCpoCve);
    }

    public void setWsHfaCpoCve(final BigDecimal wsHfaCpoCve) {
        this.wsHfaCpoCve.setValue(wsHfaCpoCve);
    }

    public void setWsHfaCpoCve(final INumericValue wsHfaCpoCve) {
        this.wsHfaCpoCve.setValue(wsHfaCpoCve);
    }

    public void setWsHfaMteCve(final int wsHfaMteCve) {
        this.wsHfaMteCve.setValue(wsHfaMteCve);
    }

    public void setWsHfaMteCve(final BigDecimal wsHfaMteCve) {
        this.wsHfaMteCve.setValue(wsHfaMteCve);
    }

    public void setWsHfaMteCve(final INumericValue wsHfaMteCve) {
        this.wsHfaMteCve.setValue(wsHfaMteCve);
    }

    public void setWsHfaTfpCve(final int wsHfaTfpCve) {
        this.wsHfaTfpCve.setValue(wsHfaTfpCve);
    }

    public void setWsHfaTfpCve(final BigDecimal wsHfaTfpCve) {
        this.wsHfaTfpCve.setValue(wsHfaTfpCve);
    }

    public void setWsHfaTfpCve(final INumericValue wsHfaTfpCve) {
        this.wsHfaTfpCve.setValue(wsHfaTfpCve);
    }

    public void setWsHfaCtbNumCta(final String wsHfaCtbNumCta) {
        this.wsHfaCtbNumCta.setValue(wsHfaCtbNumCta);
    }

    public void setWsHfaCtbNumCta(final IAlphanumericValue wsHfaCtbNumCta) {
        this.wsHfaCtbNumCta.setValue(wsHfaCtbNumCta);
    }

    public void setWsHfaIndPag(final String wsHfaIndPag) {
        this.wsHfaIndPag.setValue(wsHfaIndPag);
    }

    public void setWsHfaIndPag(final IAlphanumericValue wsHfaIndPag) {
        this.wsHfaIndPag.setValue(wsHfaIndPag);
    }

    public void setWsHfaIndExc(final String wsHfaIndExc) {
        this.wsHfaIndExc.setValue(wsHfaIndExc);
    }

    public void setWsHfaIndExc(final IAlphanumericValue wsHfaIndExc) {
        this.wsHfaIndExc.setValue(wsHfaIndExc);
    }

    public void setWsHfaUsuCve(final String wsHfaUsuCve) {
        this.wsHfaUsuCve.setValue(wsHfaUsuCve);
    }

    public void setWsHfaUsuCve(final IAlphanumericValue wsHfaUsuCve) {
        this.wsHfaUsuCve.setValue(wsHfaUsuCve);
    }

    public void setWsHfaNomPrg(final String wsHfaNomPrg) {
        this.wsHfaNomPrg.setValue(wsHfaNomPrg);
    }

    public void setWsHfaNomPrg(final IAlphanumericValue wsHfaNomPrg) {
        this.wsHfaNomPrg.setValue(wsHfaNomPrg);
    }

    public void setWsHfaFecUltAct(final String wsHfaFecUltAct) {
        this.wsHfaFecUltAct.setValue(wsHfaFecUltAct);
    }

    public void setWsHfaFecUltAct(final IAlphanumericValue wsHfaFecUltAct) {
        this.wsHfaFecUltAct.setValue(wsHfaFecUltAct);
    }

    public void setWsHfaPjeIva(final BigDecimal wsHfaPjeIva) {
        this.wsHfaPjeIva.setValue(wsHfaPjeIva);
    }

    public void setWsHfaPjeIva(final int wsHfaPjeIva) {
        this.wsHfaPjeIva.setValue(wsHfaPjeIva);
    }

    public void setWsHfaPjeIva(final INumericValue wsHfaPjeIva) {
        this.wsHfaPjeIva.setValue(wsHfaPjeIva);
    }

    public void setWsHfaPjeIsr(final BigDecimal wsHfaPjeIsr) {
        this.wsHfaPjeIsr.setValue(wsHfaPjeIsr);
    }

    public void setWsHfaPjeIsr(final int wsHfaPjeIsr) {
        this.wsHfaPjeIsr.setValue(wsHfaPjeIsr);
    }

    public void setWsHfaPjeIsr(final INumericValue wsHfaPjeIsr) {
        this.wsHfaPjeIsr.setValue(wsHfaPjeIsr);
    }

    public void setWsHfaCdregfis(final String wsHfaCdregfis) {
        this.wsHfaCdregfis.setValue(wsHfaCdregfis);
    }

    public void setWsHfaCdregfis(final IAlphanumericValue wsHfaCdregfis) {
        this.wsHfaCdregfis.setValue(wsHfaCdregfis);
    }

    public void setWsHfaNomrznso(final String wsHfaNomrznso) {
        this.wsHfaNomrznso.setValue(wsHfaNomrznso);
    }

    public void setWsHfaNomrznso(final IAlphanumericValue wsHfaNomrznso) {
        this.wsHfaNomrznso.setValue(wsHfaNomrznso);
    }

    public void setWsHfaCedNovi(final BigDecimal wsHfaCedNovi) {
        this.wsHfaCedNovi.setValue(wsHfaCedNovi);
    }

    public void setWsHfaCedNovi(final int wsHfaCedNovi) {
        this.wsHfaCedNovi.setValue(wsHfaCedNovi);
    }

    public void setWsHfaCedNovi(final INumericValue wsHfaCedNovi) {
        this.wsHfaCedNovi.setValue(wsHfaCedNovi);
    }

    public void setWsHfaCedVida(final BigDecimal wsHfaCedVida) {
        this.wsHfaCedVida.setValue(wsHfaCedVida);
    }

    public void setWsHfaCedVida(final int wsHfaCedVida) {
        this.wsHfaCedVida.setValue(wsHfaCedVida);
    }

    public void setWsHfaCedVida(final INumericValue wsHfaCedVida) {
        this.wsHfaCedVida.setValue(wsHfaCedVida);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsHfaAgtIdr.clear();
        this.wsHfaFecIniPdo.clear();
        this.wsHfaFecFinPdo.clear();
        this.wsHfaEsaCve.clear();
        this.wsHfaPefCve.clear();
        this.wsHfaOfnCve.clear();
        this.wsHfaCnaGerZonEnv.clear();
        this.wsHfaDsgCve.clear();
        this.wsHfaAgtRfc.clear();
        this.wsHfaNomEmi.clear();
        this.wsHfaDmaCae.clear();
        this.wsHfaDmaNum.clear();
        this.wsHfaDmaInt.clear();
        this.wsHfaDmaCol.clear();
        this.wsHfaMndNom.clear();
        this.wsHfaEdoNom.clear();
        this.wsHfaPaiNom.clear();
        this.wsHfaCpoCve.clear();
        this.wsHfaMteCve.clear();
        this.wsHfaTfpCve.clear();
        this.wsHfaCtbNumCta.clear();
        this.wsHfaIndPag.clear();
        this.wsHfaIndExc.clear();
        this.wsHfaUsuCve.clear();
        this.wsHfaNomPrg.clear();
        this.wsHfaFecUltAct.clear();
        this.wsHfaPjeIva.clear();
        this.wsHfaPjeIsr.clear();
        this.wsHfaCdregfis.clear();
        this.wsHfaNomrznso.clear();
        this.wsHfaCedNovi.clear();
        this.wsHfaCedVida.clear();
    }

}

