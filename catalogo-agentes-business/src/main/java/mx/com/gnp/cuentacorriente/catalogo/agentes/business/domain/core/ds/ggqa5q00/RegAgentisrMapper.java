/**
 *  Mapper: REG-AGENTISR.
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;

import mx.com.gnp.cuentacorriente.common.business.domain.model.PgavQad5300AgentisrModel;

public class RegAgentisrMapper {

    /**
     * Converts an instance of {@code PgavQad5300AgentisrModel} to an instance of {@code RegAgentisr}.);
     *
     * @param fileRecord the {@code RegAgentisr} object to be converted
     * @return a new instance of {@code PgavQad5300AgentisrModel} resulting from the conversion
     *
     */
    public PgavQad5300AgentisrModel toPgavQad5300AgentisrModel(RegAgentisr fileRecord) {
        return PgavQad5300AgentisrModel.builder()
            .vsAgente(fileRecord.getVsAgente().asString(true))
            .vsContra(fileRecord.getVsContra().asString(true))
            .vsRegfis(fileRecord.getVsRegfis().asString(true))
            .vsNombre(fileRecord.getVsNombre().asString(true))
            .vsClave(fileRecord.getVsClave().asString(true))
            .vsCedNovi(fileRecord.getVsCedNovi().asString(true))
            .vsCedVida(fileRecord.getVsCedVida().asString(true))
            .build();
    }

    /**
     * Updates an {@code RegAgentisr} with data from an {@code PgavQad5300AgentisrModel}.
     *
     * <p>This method updates the given {@code RegAgentisr} directly.</p>
     *
     * @param fileRecord the record to update
     * @param model the data source with updated information
     */
    public void updateRecordFromModel(RegAgentisr fileRecord, PgavQad5300AgentisrModel model) {
        fileRecord.setVsAgente(model.vsAgente());
        fileRecord.setVsContra(model.vsContra());
        fileRecord.setVsRegfis(model.vsRegfis());
        fileRecord.setVsNombre(model.vsNombre());
        fileRecord.setVsClave(model.vsClave());
        fileRecord.setVsCedNovi(model.vsCedNovi());
        fileRecord.setVsCedVida(model.vsCedVida());
    }

}

