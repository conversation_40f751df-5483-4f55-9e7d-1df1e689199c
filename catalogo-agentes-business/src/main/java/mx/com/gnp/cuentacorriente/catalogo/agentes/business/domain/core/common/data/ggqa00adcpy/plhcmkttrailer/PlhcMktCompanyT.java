package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.plhcmkttrailer;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PLHC-MKT-COMPANY-T.
 *
 */
// 648      *=>     0001-00005                                                01350000
@Setter
@Getter
public class PlhcMktCompanyT extends DataStruct {
    // Properties
    private AlphanumericVar plhcMktCompanyIdT = new AlphanumericVar(this, 3);                // 15 PLHC-MKT-COMPANY-ID-T X(03)
    private AlphanumericVar filler10 = new AlphanumericVar(this, 3);         // 15 FILLER X(03)

    public PlhcMktCompanyT() {
        super();
        initialize();
    }

    public PlhcMktCompanyT(DataContainer parent) {
        super(parent);
    }

    public PlhcMktCompanyT(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPlhcMktCompanyIdT(final String plhcMktCompanyIdT) {
        this.plhcMktCompanyIdT.setValue(plhcMktCompanyIdT);
    }

    public void setPlhcMktCompanyIdT(final IAlphanumericValue plhcMktCompanyIdT) {
        this.plhcMktCompanyIdT.setValue(plhcMktCompanyIdT);
    }

    public void setFiller10(final String filler10) {
        this.filler10.setValue(filler10);
    }

    public void setFiller10(final IAlphanumericValue filler10) {
        this.filler10.setValue(filler10);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhcMktCompanyIdT.setValue("GNP");
        this.filler10.setValue("");
    }

}

