package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTPRA0-NULL.
 *
 */
// 1056059100*                                                                 05910000
// 1057059200*                                                                 05920000
@Setter
@Getter
public class WsGfvtpra0Null { // VStruct
    // Properties
    private int pra01;                                                      // 03 PRA01 S9(4) COMPUTATIONAL
    private int pra02;                                                      // 03 PRA02 S9(4) COMPUTATIONAL
    private int pra03;                                                      // 03 PRA03 S9(4) COMPUTATIONAL
    private int pra04;                                                      // 03 PRA04 S9(4) COMPUTATIONAL
    private int pra05;                                                      // 03 PRA05 S9(4) COMPUTATIONAL
    private int pra06;                                                      // 03 PRA06 S9(4) COMPUTATIONAL
    private int pra07;                                                      // 03 PRA07 S9(4) COMPUTATIONAL
    private int pra08;                                                      // 03 PRA08 S9(4) COMPUTATIONAL
    private int pra09;                                                      // 03 PRA09 S9(4) COMPUTATIONAL
    private int pra10;                                                      // 03 PRA10 S9(4) COMPUTATIONAL
    private int pra11;                                                      // 03 PRA11 S9(4) COMPUTATIONAL
    private int pra12;                                                      // 03 PRA12 S9(4) COMPUTATIONAL
    private int pra13;                                                      // 03 PRA13 S9(4) COMPUTATIONAL
    private int pra14;                                                      // 03 PRA14 S9(4) COMPUTATIONAL
    private int dma01;                                                      // 03 DMA01 S9(4) COMPUTATIONAL
    private int dma02;                                                      // 03 DMA02 S9(4) COMPUTATIONAL
    private int dma03;                                                      // 03 DMA03 S9(4) COMPUTATIONAL
    private int dma04;                                                      // 03 DMA04 S9(4) COMPUTATIONAL
    private int dma05;                                                      // 03 DMA05 S9(4) COMPUTATIONAL
    private int dma06;                                                      // 03 DMA06 S9(4) COMPUTATIONAL
    private int dma07;                                                      // 03 DMA07 S9(4) COMPUTATIONAL
    private int dma08;                                                      // 03 DMA08 S9(4) COMPUTATIONAL
    private int dma09;                                                      // 03 DMA09 S9(4) COMPUTATIONAL
    private int dma10;                                                      // 03 DMA10 S9(4) COMPUTATIONAL
    private int dma11;                                                      // 03 DMA11 S9(4) COMPUTATIONAL

    public WsGfvtpra0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.pra01 = 0;
        this.pra02 = 0;
        this.pra03 = 0;
        this.pra04 = 0;
        this.pra05 = 0;
        this.pra06 = 0;
        this.pra07 = 0;
        this.pra08 = 0;
        this.pra09 = 0;
        this.pra10 = 0;
        this.pra11 = 0;
        this.pra12 = 0;
        this.pra13 = 0;
        this.pra14 = 0;
        this.dma01 = 0;
        this.dma02 = 0;
        this.dma03 = 0;
        this.dma04 = 0;
        this.dma05 = 0;
        this.dma06 = 0;
        this.dma07 = 0;
        this.dma08 = 0;
        this.dma09 = 0;
        this.dma10 = 0;
        this.dma11 = 0;
    }

}

