package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-LLAVE.
 *
 */
@Setter
@Getter
public class RpLlave { // VStruct
    // Properties
    private int rpAgtIdr;                                                   // 10 RP-AGT-IDR 9(09)
    private String rpEmpCve = "";                                           // 10 RP-EMP-CVE X(05)
    private int rpCnaNum;                                                   // 10 RP-CNA-NUM 9(04)
    private String rpFecPrxAgt = "";                                        // 10 RP-FEC-PRX-AGT X(10)

    public RpLlave() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpAgtIdr = 0;
        this.rpEmpCve = "";
        this.rpCnaNum = 0;
        this.rpFecPrxAgt = "";
    }

}

