package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GCCTORV0-NULLR.
 *
 */
// 662039400*                                                                 03940002
@Setter
@Getter
public class WsGcctorv0Nullr extends DataStruct {
    // Properties
    private NumericVarArray wsGcctorv0Ele = new NumericVarArray(this, 9, 4, 0);              // 03 WS-GCCTORV0-ELE S9(4) [9]

    public WsGcctorv0Nullr() {
        super();
        initialize();
    }

    public WsGcctorv0Nullr(DataContainer parent) {
        super(parent);
    }

    public WsGcctorv0Nullr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public NumericVar getWsGcctorv0Ele(int index) {
        return this.wsGcctorv0Ele.at(index);
    }

    public void setWsGcctorv0Ele(final int wsGcctorv0Ele) {
        this.wsGcctorv0Ele.setValue(wsGcctorv0Ele);
    }

    public void setWsGcctorv0Ele(final BigDecimal wsGcctorv0Ele) {
        this.wsGcctorv0Ele.setValue(wsGcctorv0Ele);
    }

    public void setWsGcctorv0Ele(final NumericVarArray wsGcctorv0Ele) {
        this.wsGcctorv0Ele.setValue(wsGcctorv0Ele);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsGcctorv0Ele.clear();
    }

}

