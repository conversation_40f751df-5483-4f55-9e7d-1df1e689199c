package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: SW-BANDERAS.
 *
 */
@Setter
@Getter
public class SwBanderas { // VStruct
    // Properties
    private String filler1 = "";                                            // 02 FILLER X(01)
    // Level 88 - Conditional names
    private static final String SW_FIN_CURSOR = "S";


    public SwBanderas() {
        initialize();
    }



    // Conditionals (88) - FILLER1
    public boolean isSwFinCursor() {
        return this.filler1.equals(SW_FIN_CURSOR);
    }

    public void setSwFinCursor() {
        this.filler1 = SW_FIN_CURSOR;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.filler1 = "";
    }

}

