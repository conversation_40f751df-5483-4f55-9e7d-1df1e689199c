package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FOLIOZZ.
 *
 */
// 760049200*                                                                 04920002
@Setter
@Getter
public class WsFoliozz extends DataStruct {
    // Properties
    private AlphanumericVar wsFoliozz1 = new AlphanumericVar(this, 1);       // 05 WS-FOLIOZZ1 X
    private UnsignedNumericVar wsFoliozz2 = new UnsignedNumericVar(this, 7, 0);              // 05 WS-FOLIOZZ2 9(7)

    public WsFoliozz() {
        super();
        initialize();
    }

    public WsFoliozz(DataContainer parent) {
        super(parent);
    }

    public WsFoliozz(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFoliozz1(final String wsFoliozz1) {
        this.wsFoliozz1.setValue(wsFoliozz1);
    }

    public void setWsFoliozz1(final IAlphanumericValue wsFoliozz1) {
        this.wsFoliozz1.setValue(wsFoliozz1);
    }

    public void setWsFoliozz2(final int wsFoliozz2) {
        this.wsFoliozz2.setValue(wsFoliozz2);
    }

    public void setWsFoliozz2(final BigDecimal wsFoliozz2) {
        this.wsFoliozz2.setValue(wsFoliozz2);
    }

    public void setWsFoliozz2(final INumericValue wsFoliozz2) {
        this.wsFoliozz2.setValue(wsFoliozz2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFoliozz1.clear();
        this.wsFoliozz2.clear();
    }

}

