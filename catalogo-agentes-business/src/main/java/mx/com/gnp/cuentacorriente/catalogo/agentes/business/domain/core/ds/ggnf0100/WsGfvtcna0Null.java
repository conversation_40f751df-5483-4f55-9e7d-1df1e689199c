package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTCNA0-NULL.
 *
 */
// 948048300*                                                                 04830000
// 949048400*                                                                 04840000
// 950048500*                                                                 04850000
// 951048600*                                                                 04860000
@Setter
@Getter
public class WsGfvtcna0Null extends DataStruct {
    // Properties
    private BinaryVar cna01 = new BinaryVar(this, 4, 0);                     // 03 CNA01 S9(4) COMPUTATIONAL
    private BinaryVar cna02 = new BinaryVar(this, 4, 0);                     // 03 CNA02 S9(4) COMPUTATIONAL
    private BinaryVar cna03 = new BinaryVar(this, 4, 0);                     // 03 CNA03 S9(4) COMPUTATIONAL
    private BinaryVar cna04 = new BinaryVar(this, 4, 0);                     // 03 CNA04 S9(4) COMPUTATIONAL
    private BinaryVar cna05 = new BinaryVar(this, 4, 0);                     // 03 CNA05 S9(4) COMPUTATIONAL
    private BinaryVar cna06 = new BinaryVar(this, 4, 0);                     // 03 CNA06 S9(4) COMPUTATIONAL
    private BinaryVar cna07 = new BinaryVar(this, 4, 0);                     // 03 CNA07 S9(4) COMPUTATIONAL
    private BinaryVar cna08 = new BinaryVar(this, 4, 0);                     // 03 CNA08 S9(4) COMPUTATIONAL
    private BinaryVar cna09 = new BinaryVar(this, 4, 0);                     // 03 CNA09 S9(4) COMPUTATIONAL
    private BinaryVar cna10 = new BinaryVar(this, 4, 0);                     // 03 CNA10 S9(4) COMPUTATIONAL
    private BinaryVar cna11 = new BinaryVar(this, 4, 0);                     // 03 CNA11 S9(4) COMPUTATIONAL
    private BinaryVar cna12 = new BinaryVar(this, 4, 0);                     // 03 CNA12 S9(4) COMPUTATIONAL
    private BinaryVar cna13 = new BinaryVar(this, 4, 0);                     // 03 CNA13 S9(4) COMPUTATIONAL
    private BinaryVar cna14 = new BinaryVar(this, 4, 0);                     // 03 CNA14 S9(4) COMPUTATIONAL
    private BinaryVar cna15 = new BinaryVar(this, 4, 0);                     // 03 CNA15 S9(4) COMPUTATIONAL
    private BinaryVar cna16 = new BinaryVar(this, 4, 0);                     // 03 CNA16 S9(4) COMPUTATIONAL
    private BinaryVar cna17 = new BinaryVar(this, 4, 0);                     // 03 CNA17 S9(4) COMPUTATIONAL
    private BinaryVar cna18 = new BinaryVar(this, 4, 0);                     // 03 CNA18 S9(4) COMPUTATIONAL
    private BinaryVar cna19 = new BinaryVar(this, 4, 0);                     // 03 CNA19 S9(4) COMPUTATIONAL
    private BinaryVar cna20 = new BinaryVar(this, 4, 0);                     // 03 CNA20 S9(4) COMPUTATIONAL
    private BinaryVar cna21 = new BinaryVar(this, 4, 0);                     // 03 CNA21 S9(4) COMPUTATIONAL
    private BinaryVar cna22 = new BinaryVar(this, 4, 0);                     // 03 CNA22 S9(4) COMPUTATIONAL
    private BinaryVar cna23 = new BinaryVar(this, 4, 0);                     // 03 CNA23 S9(4) COMPUTATIONAL
    private BinaryVar cna24 = new BinaryVar(this, 4, 0);                     // 03 CNA24 S9(4) COMPUTATIONAL
    private BinaryVar cna25 = new BinaryVar(this, 4, 0);                     // 03 CNA25 S9(4) COMPUTATIONAL
    private BinaryVar cna26 = new BinaryVar(this, 4, 0);                     // 03 CNA26 S9(4) COMPUTATIONAL
    private BinaryVar cna27 = new BinaryVar(this, 4, 0);                     // 03 CNA27 S9(4) COMPUTATIONAL
    private BinaryVar cna28 = new BinaryVar(this, 4, 0);                     // 03 CNA28 S9(4) COMPUTATIONAL
    private BinaryVar cna29 = new BinaryVar(this, 4, 0);                     // 03 CNA29 S9(4) COMPUTATIONAL
    private BinaryVar cna30 = new BinaryVar(this, 4, 0);                     // 03 CNA30 S9(4) COMPUTATIONAL
    private BinaryVar cna31 = new BinaryVar(this, 4, 0);                     // 03 CNA31 S9(4) COMPUTATIONAL
    private BinaryVar cna32 = new BinaryVar(this, 4, 0);                     // 03 CNA32 S9(4) COMPUTATIONAL
    private BinaryVar cna33 = new BinaryVar(this, 4, 0);                     // 03 CNA33 S9(4) COMPUTATIONAL
    private BinaryVar cna34 = new BinaryVar(this, 4, 0);                     // 03 CNA34 S9(4) COMPUTATIONAL
    private BinaryVar cna35 = new BinaryVar(this, 4, 0);                     // 03 CNA35 S9(4) COMPUTATIONAL
    private BinaryVar cna36 = new BinaryVar(this, 4, 0);                     // 03 CNA36 S9(4) COMPUTATIONAL

    public WsGfvtcna0Null() {
        super();
        initialize();
    }

    public WsGfvtcna0Null(DataContainer parent) {
        super(parent);
    }

    public WsGfvtcna0Null(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setCna01(final int cna01) {
        this.cna01.setValue(cna01);
    }

    public void setCna01(final BigDecimal cna01) {
        this.cna01.setValue(cna01);
    }

    public void setCna01(final INumericValue cna01) {
        this.cna01.setValue(cna01);
    }

    public void setCna02(final int cna02) {
        this.cna02.setValue(cna02);
    }

    public void setCna02(final BigDecimal cna02) {
        this.cna02.setValue(cna02);
    }

    public void setCna02(final INumericValue cna02) {
        this.cna02.setValue(cna02);
    }

    public void setCna03(final int cna03) {
        this.cna03.setValue(cna03);
    }

    public void setCna03(final BigDecimal cna03) {
        this.cna03.setValue(cna03);
    }

    public void setCna03(final INumericValue cna03) {
        this.cna03.setValue(cna03);
    }

    public void setCna04(final int cna04) {
        this.cna04.setValue(cna04);
    }

    public void setCna04(final BigDecimal cna04) {
        this.cna04.setValue(cna04);
    }

    public void setCna04(final INumericValue cna04) {
        this.cna04.setValue(cna04);
    }

    public void setCna05(final int cna05) {
        this.cna05.setValue(cna05);
    }

    public void setCna05(final BigDecimal cna05) {
        this.cna05.setValue(cna05);
    }

    public void setCna05(final INumericValue cna05) {
        this.cna05.setValue(cna05);
    }

    public void setCna06(final int cna06) {
        this.cna06.setValue(cna06);
    }

    public void setCna06(final BigDecimal cna06) {
        this.cna06.setValue(cna06);
    }

    public void setCna06(final INumericValue cna06) {
        this.cna06.setValue(cna06);
    }

    public void setCna07(final int cna07) {
        this.cna07.setValue(cna07);
    }

    public void setCna07(final BigDecimal cna07) {
        this.cna07.setValue(cna07);
    }

    public void setCna07(final INumericValue cna07) {
        this.cna07.setValue(cna07);
    }

    public void setCna08(final int cna08) {
        this.cna08.setValue(cna08);
    }

    public void setCna08(final BigDecimal cna08) {
        this.cna08.setValue(cna08);
    }

    public void setCna08(final INumericValue cna08) {
        this.cna08.setValue(cna08);
    }

    public void setCna09(final int cna09) {
        this.cna09.setValue(cna09);
    }

    public void setCna09(final BigDecimal cna09) {
        this.cna09.setValue(cna09);
    }

    public void setCna09(final INumericValue cna09) {
        this.cna09.setValue(cna09);
    }

    public void setCna10(final int cna10) {
        this.cna10.setValue(cna10);
    }

    public void setCna10(final BigDecimal cna10) {
        this.cna10.setValue(cna10);
    }

    public void setCna10(final INumericValue cna10) {
        this.cna10.setValue(cna10);
    }

    public void setCna11(final int cna11) {
        this.cna11.setValue(cna11);
    }

    public void setCna11(final BigDecimal cna11) {
        this.cna11.setValue(cna11);
    }

    public void setCna11(final INumericValue cna11) {
        this.cna11.setValue(cna11);
    }

    public void setCna12(final int cna12) {
        this.cna12.setValue(cna12);
    }

    public void setCna12(final BigDecimal cna12) {
        this.cna12.setValue(cna12);
    }

    public void setCna12(final INumericValue cna12) {
        this.cna12.setValue(cna12);
    }

    public void setCna13(final int cna13) {
        this.cna13.setValue(cna13);
    }

    public void setCna13(final BigDecimal cna13) {
        this.cna13.setValue(cna13);
    }

    public void setCna13(final INumericValue cna13) {
        this.cna13.setValue(cna13);
    }

    public void setCna14(final int cna14) {
        this.cna14.setValue(cna14);
    }

    public void setCna14(final BigDecimal cna14) {
        this.cna14.setValue(cna14);
    }

    public void setCna14(final INumericValue cna14) {
        this.cna14.setValue(cna14);
    }

    public void setCna15(final int cna15) {
        this.cna15.setValue(cna15);
    }

    public void setCna15(final BigDecimal cna15) {
        this.cna15.setValue(cna15);
    }

    public void setCna15(final INumericValue cna15) {
        this.cna15.setValue(cna15);
    }

    public void setCna16(final int cna16) {
        this.cna16.setValue(cna16);
    }

    public void setCna16(final BigDecimal cna16) {
        this.cna16.setValue(cna16);
    }

    public void setCna16(final INumericValue cna16) {
        this.cna16.setValue(cna16);
    }

    public void setCna17(final int cna17) {
        this.cna17.setValue(cna17);
    }

    public void setCna17(final BigDecimal cna17) {
        this.cna17.setValue(cna17);
    }

    public void setCna17(final INumericValue cna17) {
        this.cna17.setValue(cna17);
    }

    public void setCna18(final int cna18) {
        this.cna18.setValue(cna18);
    }

    public void setCna18(final BigDecimal cna18) {
        this.cna18.setValue(cna18);
    }

    public void setCna18(final INumericValue cna18) {
        this.cna18.setValue(cna18);
    }

    public void setCna19(final int cna19) {
        this.cna19.setValue(cna19);
    }

    public void setCna19(final BigDecimal cna19) {
        this.cna19.setValue(cna19);
    }

    public void setCna19(final INumericValue cna19) {
        this.cna19.setValue(cna19);
    }

    public void setCna20(final int cna20) {
        this.cna20.setValue(cna20);
    }

    public void setCna20(final BigDecimal cna20) {
        this.cna20.setValue(cna20);
    }

    public void setCna20(final INumericValue cna20) {
        this.cna20.setValue(cna20);
    }

    public void setCna21(final int cna21) {
        this.cna21.setValue(cna21);
    }

    public void setCna21(final BigDecimal cna21) {
        this.cna21.setValue(cna21);
    }

    public void setCna21(final INumericValue cna21) {
        this.cna21.setValue(cna21);
    }

    public void setCna22(final int cna22) {
        this.cna22.setValue(cna22);
    }

    public void setCna22(final BigDecimal cna22) {
        this.cna22.setValue(cna22);
    }

    public void setCna22(final INumericValue cna22) {
        this.cna22.setValue(cna22);
    }

    public void setCna23(final int cna23) {
        this.cna23.setValue(cna23);
    }

    public void setCna23(final BigDecimal cna23) {
        this.cna23.setValue(cna23);
    }

    public void setCna23(final INumericValue cna23) {
        this.cna23.setValue(cna23);
    }

    public void setCna24(final int cna24) {
        this.cna24.setValue(cna24);
    }

    public void setCna24(final BigDecimal cna24) {
        this.cna24.setValue(cna24);
    }

    public void setCna24(final INumericValue cna24) {
        this.cna24.setValue(cna24);
    }

    public void setCna25(final int cna25) {
        this.cna25.setValue(cna25);
    }

    public void setCna25(final BigDecimal cna25) {
        this.cna25.setValue(cna25);
    }

    public void setCna25(final INumericValue cna25) {
        this.cna25.setValue(cna25);
    }

    public void setCna26(final int cna26) {
        this.cna26.setValue(cna26);
    }

    public void setCna26(final BigDecimal cna26) {
        this.cna26.setValue(cna26);
    }

    public void setCna26(final INumericValue cna26) {
        this.cna26.setValue(cna26);
    }

    public void setCna27(final int cna27) {
        this.cna27.setValue(cna27);
    }

    public void setCna27(final BigDecimal cna27) {
        this.cna27.setValue(cna27);
    }

    public void setCna27(final INumericValue cna27) {
        this.cna27.setValue(cna27);
    }

    public void setCna28(final int cna28) {
        this.cna28.setValue(cna28);
    }

    public void setCna28(final BigDecimal cna28) {
        this.cna28.setValue(cna28);
    }

    public void setCna28(final INumericValue cna28) {
        this.cna28.setValue(cna28);
    }

    public void setCna29(final int cna29) {
        this.cna29.setValue(cna29);
    }

    public void setCna29(final BigDecimal cna29) {
        this.cna29.setValue(cna29);
    }

    public void setCna29(final INumericValue cna29) {
        this.cna29.setValue(cna29);
    }

    public void setCna30(final int cna30) {
        this.cna30.setValue(cna30);
    }

    public void setCna30(final BigDecimal cna30) {
        this.cna30.setValue(cna30);
    }

    public void setCna30(final INumericValue cna30) {
        this.cna30.setValue(cna30);
    }

    public void setCna31(final int cna31) {
        this.cna31.setValue(cna31);
    }

    public void setCna31(final BigDecimal cna31) {
        this.cna31.setValue(cna31);
    }

    public void setCna31(final INumericValue cna31) {
        this.cna31.setValue(cna31);
    }

    public void setCna32(final int cna32) {
        this.cna32.setValue(cna32);
    }

    public void setCna32(final BigDecimal cna32) {
        this.cna32.setValue(cna32);
    }

    public void setCna32(final INumericValue cna32) {
        this.cna32.setValue(cna32);
    }

    public void setCna33(final int cna33) {
        this.cna33.setValue(cna33);
    }

    public void setCna33(final BigDecimal cna33) {
        this.cna33.setValue(cna33);
    }

    public void setCna33(final INumericValue cna33) {
        this.cna33.setValue(cna33);
    }

    public void setCna34(final int cna34) {
        this.cna34.setValue(cna34);
    }

    public void setCna34(final BigDecimal cna34) {
        this.cna34.setValue(cna34);
    }

    public void setCna34(final INumericValue cna34) {
        this.cna34.setValue(cna34);
    }

    public void setCna35(final int cna35) {
        this.cna35.setValue(cna35);
    }

    public void setCna35(final BigDecimal cna35) {
        this.cna35.setValue(cna35);
    }

    public void setCna35(final INumericValue cna35) {
        this.cna35.setValue(cna35);
    }

    public void setCna36(final int cna36) {
        this.cna36.setValue(cna36);
    }

    public void setCna36(final BigDecimal cna36) {
        this.cna36.setValue(cna36);
    }

    public void setCna36(final INumericValue cna36) {
        this.cna36.setValue(cna36);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.cna01.clear();
        this.cna02.clear();
        this.cna03.clear();
        this.cna04.clear();
        this.cna05.clear();
        this.cna06.clear();
        this.cna07.clear();
        this.cna08.clear();
        this.cna09.clear();
        this.cna10.clear();
        this.cna11.clear();
        this.cna12.clear();
        this.cna13.clear();
        this.cna14.clear();
        this.cna15.clear();
        this.cna16.clear();
        this.cna17.clear();
        this.cna18.clear();
        this.cna19.clear();
        this.cna20.clear();
        this.cna21.clear();
        this.cna22.clear();
        this.cna23.clear();
        this.cna24.clear();
        this.cna25.clear();
        this.cna26.clear();
        this.cna27.clear();
        this.cna28.clear();
        this.cna29.clear();
        this.cna30.clear();
        this.cna31.clear();
        this.cna32.clear();
        this.cna33.clear();
        this.cna34.clear();
        this.cna35.clear();
        this.cna36.clear();
    }

}

