package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: C-CONTADORES.
 *
 */
@Setter
@Getter
public class CContadores { // VStruct
    // Properties
    private UnsignedNumericVar cFetchAgtced = new UnsignedNumericVar(12, 0);                 // 05 C-FETCH-AGTCED 9(12)

    public CContadores() {
        initialize();
    }



    public void setCFetchAgtced(final BigDecimal cFetchAgtced) {
        this.cFetchAgtced.setValue(cFetchAgtced);
    }

    public void setCFetchAgtced(final int cFetchAgtced) {
        this.cFetchAgtced.setValue(cFetchAgtced);
    }

    public void setCFetchAgtced(final INumericValue cFetchAgtced) {
        this.cFetchAgtced.setValue(cFetchAgtced);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.cFetchAgtced.setZero();
    }

}

