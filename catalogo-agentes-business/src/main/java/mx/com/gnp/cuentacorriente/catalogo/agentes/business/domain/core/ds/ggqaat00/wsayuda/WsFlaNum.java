package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FLA-NUM.
 *
 */
@Setter
@Getter
public class WsFlaNum extends DataStruct {
    // Properties
    private AlphanumericVar wsFlaNum_1 = new AlphanumericVar(this, 1);       // 10 WS-FLA-NUM-1 X(01)
    private UnsignedNumericVar wsFlaNumX = new UnsignedNumericVar(this, 2, 0);               // 10 WS-FLA-NUM-X 9(02)
    private UnsignedNumericVar wsFlaNum_2 = new UnsignedNumericVar(this, 5, 0);              // 10 WS-FLA-NUM-2 9(05)

    public WsFlaNum() {
        super();
        initialize();
    }

    public WsFlaNum(DataContainer parent) {
        super(parent);
    }

    public WsFlaNum(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFlaNum_1(final String wsFlaNum_1) {
        this.wsFlaNum_1.setValue(wsFlaNum_1);
    }

    public void setWsFlaNum_1(final IAlphanumericValue wsFlaNum_1) {
        this.wsFlaNum_1.setValue(wsFlaNum_1);
    }

    public void setWsFlaNumX(final int wsFlaNumX) {
        this.wsFlaNumX.setValue(wsFlaNumX);
    }

    public void setWsFlaNumX(final BigDecimal wsFlaNumX) {
        this.wsFlaNumX.setValue(wsFlaNumX);
    }

    public void setWsFlaNumX(final INumericValue wsFlaNumX) {
        this.wsFlaNumX.setValue(wsFlaNumX);
    }

    public void setWsFlaNum_2(final int wsFlaNum_2) {
        this.wsFlaNum_2.setValue(wsFlaNum_2);
    }

    public void setWsFlaNum_2(final BigDecimal wsFlaNum_2) {
        this.wsFlaNum_2.setValue(wsFlaNum_2);
    }

    public void setWsFlaNum_2(final INumericValue wsFlaNum_2) {
        this.wsFlaNum_2.setValue(wsFlaNum_2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFlaNum_1.clear();
        this.wsFlaNumX.clear();
        this.wsFlaNum_2.clear();
    }

}

