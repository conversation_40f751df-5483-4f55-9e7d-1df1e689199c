package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea.Det1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea.Det2;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea.Det22;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea.Det3;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: LINEA.
 *
 */
// 62006200*                                                                 00620002
@Setter
@Getter
public class Linea extends DataStruct {
    // Properties
    private AlphanumericVar linSalto = new AlphanumericVar(this, 1);         // 03 LIN-SALTO X
    private AlphanumericVar linResto = new AlphanumericVar(this, 132);       // 03 LIN-RESTO X(132) REDEFINED BY DET3
    private Det1 det1 = new  Det1(this, this.linResto);                      // 03 DET1 REDEFINES LIN-RESTO
    private Det2 det2 = new  Det2(this, this.linResto);                      // 03 DET2 REDEFINES LIN-RESTO
    private Det22 det22 = new  Det22(this, this.linResto);                   // 03 DET22 REDEFINES LIN-RESTO
    private Det3 det3 = new  Det3(this, this.linResto);                      // 03 DET3 REDEFINES LIN-RESTO

    public Linea() {
        super();
        initialize();
    }

    public Linea(DataContainer parent) {
        super(parent);
    }

    public Linea(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setLinSalto(final String linSalto) {
        this.linSalto.setValue(linSalto);
    }

    public void setLinSalto(final IAlphanumericValue linSalto) {
        this.linSalto.setValue(linSalto);
    }

    public void setLinResto(final String linResto) {
        this.linResto.setValue(linResto);
    }

    public void setLinResto(final IAlphanumericValue linResto) {
        this.linResto.setValue(linResto);
    }


    // Deeper properties

    // Property: det1.d1FlaNum -> 05 D1-FLA-NUM X(8)
    public AlphanumericVar getD1FlaNum() {
        return this.det1.getD1FlaNum();
    }

    public void setD1FlaNum(final String d1FlaNum) {
        this.det1.setD1FlaNum(d1FlaNum);
    }

    public void setD1FlaNum(final IAlphanumericValue d1FlaNum) {
        this.det1.setD1FlaNum(d1FlaNum);
    }

    // Property: det1.filler1 -> 05 FILLER X
    public AlphanumericVar getFiller1() {
        return this.det1.getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.det1.setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.det1.setFiller1(filler1);
    }

    // Property: det1.d1EmpCve -> 05 D1-EMP-CVE X(5)
    public AlphanumericVar getD1EmpCve() {
        return this.det1.getD1EmpCve();
    }

    public void setD1EmpCve(final String d1EmpCve) {
        this.det1.setD1EmpCve(d1EmpCve);
    }

    public void setD1EmpCve(final IAlphanumericValue d1EmpCve) {
        this.det1.setD1EmpCve(d1EmpCve);
    }

    // Property: det1.filler2 -> 05 FILLER X(4)
    public AlphanumericVar getFiller2() {
        return this.det1.getFiller2();
    }

    public void setFiller2(final String filler2) {
        this.det1.setFiller2(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.det1.setFiller2(filler2);
    }

    // Property: det1.d1EsaCve -> 05 D1-ESA-CVE ZZZ9
    public NumericEditedVar getD1EsaCve() {
        return this.det1.getD1EsaCve();
    }

    public void setD1EsaCve(final String d1EsaCve) {
        this.det1.setD1EsaCve(d1EsaCve);
    }

    public void setD1EsaCve(final IValue d1EsaCve) {
        this.det1.setD1EsaCve(d1EsaCve);
    }

    // Property: det1.filler3 -> 05 FILLER X(4)
    public AlphanumericVar getFiller3() {
        return this.det1.getFiller3();
    }

    public void setFiller3(final String filler3) {
        this.det1.setFiller3(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.det1.setFiller3(filler3);
    }

    // Property: det1.d1OfnCve -> 05 D1-OFN-CVE ZZZ9
    public NumericEditedVar getD1OfnCve() {
        return this.det1.getD1OfnCve();
    }

    public void setD1OfnCve(final String d1OfnCve) {
        this.det1.setD1OfnCve(d1OfnCve);
    }

    public void setD1OfnCve(final IValue d1OfnCve) {
        this.det1.setD1OfnCve(d1OfnCve);
    }

    // Property: det1.filler4 -> 05 FILLER X(4)
    public AlphanumericVar getFiller4() {
        return this.det1.getFiller4();
    }

    public void setFiller4(final String filler4) {
        this.det1.setFiller4(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.det1.setFiller4(filler4);
    }

    // Property: det1.d1OrvCve -> 05 D1-ORV-CVE ZZZ9
    public NumericEditedVar getD1OrvCve() {
        return this.det1.getD1OrvCve();
    }

    public void setD1OrvCve(final String d1OrvCve) {
        this.det1.setD1OrvCve(d1OrvCve);
    }

    public void setD1OrvCve(final IValue d1OrvCve) {
        this.det1.setD1OrvCve(d1OrvCve);
    }

    // Property: det1.filler5 -> 05 FILLER X(4)
    public AlphanumericVar getFiller5() {
        return this.det1.getFiller5();
    }

    public void setFiller5(final String filler5) {
        this.det1.setFiller5(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.det1.setFiller5(filler5);
    }

    // Property: det1.d1PefCve -> 05 D1-PEF-CVE ZZZ9
    public NumericEditedVar getD1PefCve() {
        return this.det1.getD1PefCve();
    }

    public void setD1PefCve(final String d1PefCve) {
        this.det1.setD1PefCve(d1PefCve);
    }

    public void setD1PefCve(final IValue d1PefCve) {
        this.det1.setD1PefCve(d1PefCve);
    }

    // Property: det1.filler6 -> 05 FILLER X(4)
    public AlphanumericVar getFiller6() {
        return this.det1.getFiller6();
    }

    public void setFiller6(final String filler6) {
        this.det1.setFiller6(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.det1.setFiller6(filler6);
    }

    // Property: det1.d1AgtNom -> 05 D1-AGT-NOM X(10)
    public AlphanumericVar getD1AgtNom() {
        return this.det1.getD1AgtNom();
    }

    public void setD1AgtNom(final String d1AgtNom) {
        this.det1.setD1AgtNom(d1AgtNom);
    }

    public void setD1AgtNom(final IAlphanumericValue d1AgtNom) {
        this.det1.setD1AgtNom(d1AgtNom);
    }

    // Property: det1.filler7 -> 05 FILLER XX
    public AlphanumericVar getFiller7() {
        return this.det1.getFiller7();
    }

    public void setFiller7(final String filler7) {
        this.det1.setFiller7(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.det1.setFiller7(filler7);
    }

    // Property: det1.d1AgtApePat -> 05 D1-AGT-APE-PAT X(10)
    public AlphanumericVar getD1AgtApePat() {
        return this.det1.getD1AgtApePat();
    }

    public void setD1AgtApePat(final String d1AgtApePat) {
        this.det1.setD1AgtApePat(d1AgtApePat);
    }

    public void setD1AgtApePat(final IAlphanumericValue d1AgtApePat) {
        this.det1.setD1AgtApePat(d1AgtApePat);
    }

    // Property: det1.filler8 -> 05 FILLER XX
    public AlphanumericVar getFiller8() {
        return this.det1.getFiller8();
    }

    public void setFiller8(final String filler8) {
        this.det1.setFiller8(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.det1.setFiller8(filler8);
    }

    // Property: det1.d1AgtApeMat -> 05 D1-AGT-APE-MAT X(10)
    public AlphanumericVar getD1AgtApeMat() {
        return this.det1.getD1AgtApeMat();
    }

    public void setD1AgtApeMat(final String d1AgtApeMat) {
        this.det1.setD1AgtApeMat(d1AgtApeMat);
    }

    public void setD1AgtApeMat(final IAlphanumericValue d1AgtApeMat) {
        this.det1.setD1AgtApeMat(d1AgtApeMat);
    }

    // Property: det1.filler9 -> 05 FILLER XX
    public AlphanumericVar getFiller9() {
        return this.det1.getFiller9();
    }

    public void setFiller9(final String filler9) {
        this.det1.setFiller9(filler9);
    }

    public void setFiller9(final IAlphanumericValue filler9) {
        this.det1.setFiller9(filler9);
    }

    // Property: det1.d1AgtRazSoc -> 05 D1-AGT-RAZ-SOC X(8)
    public AlphanumericVar getD1AgtRazSoc() {
        return this.det1.getD1AgtRazSoc();
    }

    public void setD1AgtRazSoc(final String d1AgtRazSoc) {
        this.det1.setD1AgtRazSoc(d1AgtRazSoc);
    }

    public void setD1AgtRazSoc(final IAlphanumericValue d1AgtRazSoc) {
        this.det1.setD1AgtRazSoc(d1AgtRazSoc);
    }

    // Property: det1.filler10 -> 05 FILLER X(6)
    public AlphanumericVar getFiller10() {
        return this.det1.getFiller10();
    }

    public void setFiller10(final String filler10) {
        this.det1.setFiller10(filler10);
    }

    public void setFiller10(final IAlphanumericValue filler10) {
        this.det1.setFiller10(filler10);
    }

    // Property: det1.d1Obs -> 05 D1-OBS X(20)
    public AlphanumericVar getD1Obs() {
        return this.det1.getD1Obs();
    }

    public void setD1Obs(final String d1Obs) {
        this.det1.setD1Obs(d1Obs);
    }

    public void setD1Obs(final IAlphanumericValue d1Obs) {
        this.det1.setD1Obs(d1Obs);
    }


    // Deeper properties

    // Property: det2.d2AgtIdr -> 05 D2-AGT-IDR ZZZZZZZZ9
    public NumericEditedVar getD2AgtIdr() {
        return this.det2.getD2AgtIdr();
    }

    public void setD2AgtIdr(final String d2AgtIdr) {
        this.det2.setD2AgtIdr(d2AgtIdr);
    }

    public void setD2AgtIdr(final IValue d2AgtIdr) {
        this.det2.setD2AgtIdr(d2AgtIdr);
    }

    // Property: det2.filler11 -> 05 FILLER XXXX
    public AlphanumericVar getFiller11() {
        return this.det2.getFiller11();
    }

    public void setFiller11(final String filler11) {
        this.det2.setFiller11(filler11);
    }

    public void setFiller11(final IAlphanumericValue filler11) {
        this.det2.setFiller11(filler11);
    }

    // Property: det2.d2CnaNum -> 05 D2-CNA-NUM ZZZ9
    public NumericEditedVar getD2CnaNum() {
        return this.det2.getD2CnaNum();
    }

    public void setD2CnaNum(final String d2CnaNum) {
        this.det2.setD2CnaNum(d2CnaNum);
    }

    public void setD2CnaNum(final IValue d2CnaNum) {
        this.det2.setD2CnaNum(d2CnaNum);
    }

    // Property: det2.filler12 -> 05 FILLER X(4)
    public AlphanumericVar getFiller12() {
        return this.det2.getFiller12();
    }

    public void setFiller12(final String filler12) {
        this.det2.setFiller12(filler12);
    }

    public void setFiller12(final IAlphanumericValue filler12) {
        this.det2.setFiller12(filler12);
    }

    // Property: det2.d2TfpCve -> 05 D2-TFP-CVE ZZZ9
    public NumericEditedVar getD2TfpCve() {
        return this.det2.getD2TfpCve();
    }

    public void setD2TfpCve(final String d2TfpCve) {
        this.det2.setD2TfpCve(d2TfpCve);
    }

    public void setD2TfpCve(final IValue d2TfpCve) {
        this.det2.setD2TfpCve(d2TfpCve);
    }

    // Property: det2.filler13 -> 05 FILLER X(4)
    public AlphanumericVar getFiller13() {
        return this.det2.getFiller13();
    }

    public void setFiller13(final String filler13) {
        this.det2.setFiller13(filler13);
    }

    public void setFiller13(final IAlphanumericValue filler13) {
        this.det2.setFiller13(filler13);
    }

    // Property: det2.d2CnaIndGer -> 05 D2-CNA-IND-GER X
    public AlphanumericVar getD2CnaIndGer() {
        return this.det2.getD2CnaIndGer();
    }

    public void setD2CnaIndGer(final String d2CnaIndGer) {
        this.det2.setD2CnaIndGer(d2CnaIndGer);
    }

    public void setD2CnaIndGer(final IAlphanumericValue d2CnaIndGer) {
        this.det2.setD2CnaIndGer(d2CnaIndGer);
    }

    // Property: det2.filler14 -> 05 FILLER X(4)
    public AlphanumericVar getFiller14() {
        return this.det2.getFiller14();
    }

    public void setFiller14(final String filler14) {
        this.det2.setFiller14(filler14);
    }

    public void setFiller14(final IAlphanumericValue filler14) {
        this.det2.setFiller14(filler14);
    }

    // Property: det2.d2MteCve -> 05 D2-MTE-CVE ZZZ9
    public NumericEditedVar getD2MteCve() {
        return this.det2.getD2MteCve();
    }

    public void setD2MteCve(final String d2MteCve) {
        this.det2.setD2MteCve(d2MteCve);
    }

    public void setD2MteCve(final IValue d2MteCve) {
        this.det2.setD2MteCve(d2MteCve);
    }

    // Property: det2.filler15 -> 05 FILLER X(4)
    public AlphanumericVar getFiller15() {
        return this.det2.getFiller15();
    }

    public void setFiller15(final String filler15) {
        this.det2.setFiller15(filler15);
    }

    public void setFiller15(final IAlphanumericValue filler15) {
        this.det2.setFiller15(filler15);
    }

    // Property: det2.d2FlaFecMotEss -> 05 D2-FLA-FEC-MOT-ESS X(10)
    public AlphanumericVar getD2FlaFecMotEss() {
        return this.det2.getD2FlaFecMotEss();
    }

    public void setD2FlaFecMotEss(final String d2FlaFecMotEss) {
        this.det2.setD2FlaFecMotEss(d2FlaFecMotEss);
    }

    public void setD2FlaFecMotEss(final IAlphanumericValue d2FlaFecMotEss) {
        this.det2.setD2FlaFecMotEss(d2FlaFecMotEss);
    }

    // Property: det2.filler16 -> 05 FILLER X(4)
    public AlphanumericVar getFiller16() {
        return this.det2.getFiller16();
    }

    public void setFiller16(final String filler16) {
        this.det2.setFiller16(filler16);
    }

    public void setFiller16(final IAlphanumericValue filler16) {
        this.det2.setFiller16(filler16);
    }

    // Property: det2.d2FlaNumGte -> 05 D2-FLA-NUM-GTE X(08)
    public AlphanumericVar getD2FlaNumGte() {
        return this.det2.getD2FlaNumGte();
    }

    public void setD2FlaNumGte(final String d2FlaNumGte) {
        this.det2.setD2FlaNumGte(d2FlaNumGte);
    }

    public void setD2FlaNumGte(final IAlphanumericValue d2FlaNumGte) {
        this.det2.setD2FlaNumGte(d2FlaNumGte);
    }

    // Property: det2.filler17 -> 05 FILLER X(02)
    public AlphanumericVar getFiller17() {
        return this.det2.getFiller17();
    }

    public void setFiller17(final String filler17) {
        this.det2.setFiller17(filler17);
    }

    public void setFiller17(final IAlphanumericValue filler17) {
        this.det2.setFiller17(filler17);
    }

    // Property: det2.d2CnaGerZonEnv -> 05 D2-CNA-GER-ZON-ENV 9999
    public UnsignedNumericVar getD2CnaGerZonEnv() {
        return this.det2.getD2CnaGerZonEnv();
    }

    public void setD2CnaGerZonEnv(final int d2CnaGerZonEnv) {
        this.det2.setD2CnaGerZonEnv(d2CnaGerZonEnv);
    }

    public void setD2CnaGerZonEnv(final INumericValue d2CnaGerZonEnv) {
        this.det2.setD2CnaGerZonEnv(d2CnaGerZonEnv);
    }

    // Property: det2.filler18 -> 05 FILLER X(02)
    public AlphanumericVar getFiller18() {
        return this.det2.getFiller18();
    }

    public void setFiller18(final String filler18) {
        this.det2.setFiller18(filler18);
    }

    public void setFiller18(final IAlphanumericValue filler18) {
        this.det2.setFiller18(filler18);
    }

    // Property: det2.d2UsuCveAct -> 05 D2-USU-CVE-ACT X(08)
    public AlphanumericVar getD2UsuCveAct() {
        return this.det2.getD2UsuCveAct();
    }

    public void setD2UsuCveAct(final String d2UsuCveAct) {
        this.det2.setD2UsuCveAct(d2UsuCveAct);
    }

    public void setD2UsuCveAct(final IAlphanumericValue d2UsuCveAct) {
        this.det2.setD2UsuCveAct(d2UsuCveAct);
    }

    // Property: det2.filler19 -> 05 FILLER X(02)
    public AlphanumericVar getFiller19() {
        return this.det2.getFiller19();
    }

    public void setFiller19(final String filler19) {
        this.det2.setFiller19(filler19);
    }

    public void setFiller19(final IAlphanumericValue filler19) {
        this.det2.setFiller19(filler19);
    }

    // Property: det2.d2FecUltAct -> 05 D2-FEC-ULT-ACT X(10)
    public AlphanumericVar getD2FecUltAct() {
        return this.det2.getD2FecUltAct();
    }

    public void setD2FecUltAct(final String d2FecUltAct) {
        this.det2.setD2FecUltAct(d2FecUltAct);
    }

    public void setD2FecUltAct(final IAlphanumericValue d2FecUltAct) {
        this.det2.setD2FecUltAct(d2FecUltAct);
    }

    // Property: det2.filler20 -> 05 FILLER X(40)
    public AlphanumericVar getFiller20() {
        return this.det2.getFiller20();
    }

    public void setFiller20(final String filler20) {
        this.det2.setFiller20(filler20);
    }

    public void setFiller20(final IAlphanumericValue filler20) {
        this.det2.setFiller20(filler20);
    }


    // Deeper properties

    // Property: det22.d3FlaDes -> 05 D3-FLA-DES X(40)
    public AlphanumericVar getD3FlaDes() {
        return this.det22.getD3FlaDes();
    }

    public void setD3FlaDes(final String d3FlaDes) {
        this.det22.setD3FlaDes(d3FlaDes);
    }

    public void setD3FlaDes(final IAlphanumericValue d3FlaDes) {
        this.det22.setD3FlaDes(d3FlaDes);
    }

    // Property: det22.filler21 -> 05 FILLER X(6)
    public AlphanumericVar getFiller21() {
        return this.det22.getFiller21();
    }

    public void setFiller21(final String filler21) {
        this.det22.setFiller21(filler21);
    }

    public void setFiller21(final IAlphanumericValue filler21) {
        this.det22.setFiller21(filler21);
    }

    // Property: det22.d3AgtTraEsp -> 05 D3-AGT-TRA-ESP X(1)
    public AlphanumericVar getD3AgtTraEsp() {
        return this.det22.getD3AgtTraEsp();
    }

    public void setD3AgtTraEsp(final String d3AgtTraEsp) {
        this.det22.setD3AgtTraEsp(d3AgtTraEsp);
    }

    public void setD3AgtTraEsp(final IAlphanumericValue d3AgtTraEsp) {
        this.det22.setD3AgtTraEsp(d3AgtTraEsp);
    }

    // Property: det22.filler22 -> 05 FILLER X(6)
    public AlphanumericVar getFiller22() {
        return this.det22.getFiller22();
    }

    public void setFiller22(final String filler22) {
        this.det22.setFiller22(filler22);
    }

    public void setFiller22(final IAlphanumericValue filler22) {
        this.det22.setFiller22(filler22);
    }

    // Property: det22.d3CnaFecCnx -> 05 D3-CNA-FEC-CNX X(10)
    public AlphanumericVar getD3CnaFecCnx() {
        return this.det22.getD3CnaFecCnx();
    }

    public void setD3CnaFecCnx(final String d3CnaFecCnx) {
        this.det22.setD3CnaFecCnx(d3CnaFecCnx);
    }

    public void setD3CnaFecCnx(final IAlphanumericValue d3CnaFecCnx) {
        this.det22.setD3CnaFecCnx(d3CnaFecCnx);
    }

    // Property: det22.filler23 -> 05 FILLER X(6)
    public AlphanumericVar getFiller23() {
        return this.det22.getFiller23();
    }

    public void setFiller23(final String filler23) {
        this.det22.setFiller23(filler23);
    }

    public void setFiller23(final IAlphanumericValue filler23) {
        this.det22.setFiller23(filler23);
    }

    // Property: det22.d3GnaCve -> 05 D3-GNA-CVE ZZZ9
    public NumericEditedVar getD3GnaCve() {
        return this.det22.getD3GnaCve();
    }

    public void setD3GnaCve(final String d3GnaCve) {
        this.det22.setD3GnaCve(d3GnaCve);
    }

    public void setD3GnaCve(final IValue d3GnaCve) {
        this.det22.setD3GnaCve(d3GnaCve);
    }

    // Property: det22.filler24 -> 05 FILLER X(6)
    public AlphanumericVar getFiller24() {
        return this.det22.getFiller24();
    }

    public void setFiller24(final String filler24) {
        this.det22.setFiller24(filler24);
    }

    public void setFiller24(final IAlphanumericValue filler24) {
        this.det22.setFiller24(filler24);
    }

    // Property: det22.d3EsaCveFla -> 05 D3-ESA-CVE-FLA ZZZ9
    public NumericEditedVar getD3EsaCveFla() {
        return this.det22.getD3EsaCveFla();
    }

    public void setD3EsaCveFla(final String d3EsaCveFla) {
        this.det22.setD3EsaCveFla(d3EsaCveFla);
    }

    public void setD3EsaCveFla(final IValue d3EsaCveFla) {
        this.det22.setD3EsaCveFla(d3EsaCveFla);
    }

    // Property: det22.filler25 -> 05 FILLER X(10)
    public AlphanumericVar getFiller25() {
        return this.det22.getFiller25();
    }

    public void setFiller25(final String filler25) {
        this.det22.setFiller25(filler25);
    }

    public void setFiller25(final IAlphanumericValue filler25) {
        this.det22.setFiller25(filler25);
    }

    // Property: det22.d3FlaGerZon -> 05 D3-FLA-GER-ZON Z999
    public NumericEditedVar getD3FlaGerZon() {
        return this.det22.getD3FlaGerZon();
    }

    public void setD3FlaGerZon(final String d3FlaGerZon) {
        this.det22.setD3FlaGerZon(d3FlaGerZon);
    }

    public void setD3FlaGerZon(final IValue d3FlaGerZon) {
        this.det22.setD3FlaGerZon(d3FlaGerZon);
    }

    // Property: det22.filler26 -> 05 FILLER X(8)
    public AlphanumericVar getFiller26() {
        return this.det22.getFiller26();
    }

    public void setFiller26(final String filler26) {
        this.det22.setFiller26(filler26);
    }

    public void setFiller26(final IAlphanumericValue filler26) {
        this.det22.setFiller26(filler26);
    }

    // Property: det22.d3OfnCveCna -> 05 D3-OFN-CVE-CNA ZZ99
    public NumericEditedVar getD3OfnCveCna() {
        return this.det22.getD3OfnCveCna();
    }

    public void setD3OfnCveCna(final String d3OfnCveCna) {
        this.det22.setD3OfnCveCna(d3OfnCveCna);
    }

    public void setD3OfnCveCna(final IValue d3OfnCveCna) {
        this.det22.setD3OfnCveCna(d3OfnCveCna);
    }

    // Property: det22.filler27 -> 05 FILLER X(6)
    public AlphanumericVar getFiller27() {
        return this.det22.getFiller27();
    }

    public void setFiller27(final String filler27) {
        this.det22.setFiller27(filler27);
    }

    public void setFiller27(final IAlphanumericValue filler27) {
        this.det22.setFiller27(filler27);
    }

    // Property: det22.d3AgtIdrGte -> 05 D3-AGT-IDR-GTE 9(05)
    public UnsignedNumericVar getD3AgtIdrGte() {
        return this.det22.getD3AgtIdrGte();
    }

    public void setD3AgtIdrGte(final int d3AgtIdrGte) {
        this.det22.setD3AgtIdrGte(d3AgtIdrGte);
    }

    public void setD3AgtIdrGte(final INumericValue d3AgtIdrGte) {
        this.det22.setD3AgtIdrGte(d3AgtIdrGte);
    }

    // Property: det22.filler28 -> 05 FILLER X(12)
    public AlphanumericVar getFiller28() {
        return this.det22.getFiller28();
    }

    public void setFiller28(final String filler28) {
        this.det22.setFiller28(filler28);
    }

    public void setFiller28(final IAlphanumericValue filler28) {
        this.det22.setFiller28(filler28);
    }


    // Deeper properties

    // Property: det3.filler29 -> 10 FILLER X(10)
    public AlphanumericVar getFiller29() {
        return this.det3.getFiller29();
    }

    public void setFiller29(final String filler29) {
        this.det3.setFiller29(filler29);
    }

    public void setFiller29(final IAlphanumericValue filler29) {
        this.det3.setFiller29(filler29);
    }

    // Property: det3.det12Letrero -> 10 DET12-LETRERO X(25)
    public AlphanumericVar getDet12Letrero() {
        return this.det3.getDet12Letrero();
    }

    public void setDet12Letrero(final String det12Letrero) {
        this.det3.setDet12Letrero(det12Letrero);
    }

    public void setDet12Letrero(final IAlphanumericValue det12Letrero) {
        this.det3.setDet12Letrero(det12Letrero);
    }

    // Property: det3.det12Total -> 10 DET12-TOTAL ZZZZZZ9
    public NumericEditedVar getDet12Total() {
        return this.det3.getDet12Total();
    }

    public void setDet12Total(final String det12Total) {
        this.det3.setDet12Total(det12Total);
    }

    public void setDet12Total(final IValue det12Total) {
        this.det3.setDet12Total(det12Total);
    }

    // Property: det3.filler30 -> 10 FILLER X(90)
    public AlphanumericVar getFiller30() {
        return this.det3.getFiller30();
    }

    public void setFiller30(final String filler30) {
        this.det3.setFiller30(filler30);
    }

    public void setFiller30(final IAlphanumericValue filler30) {
        this.det3.setFiller30(filler30);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.linSalto.clear();
        this.linResto.clear();
    }

}

