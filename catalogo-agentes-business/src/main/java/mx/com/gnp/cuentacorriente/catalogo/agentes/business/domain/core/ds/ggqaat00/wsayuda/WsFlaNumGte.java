package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FLA-NUM-GTE.
 *
 */
@Setter
@Getter
public class WsFlaNumGte extends DataStruct {
    // Properties
    private AlphanumericVar wsFlaNumGte_1 = new AlphanumericVar(this, 1);    // 10 WS-FLA-NUM-GTE-1 X(01)
    private AlphanumericVar wsFlaNumGteX = new AlphanumericVar(this, 2);     // 10 WS-FLA-NUM-GTE-X X(02)
    private UnsignedNumericVar wsFlaNumGte_2 = new UnsignedNumericVar(this, 5, 0);           // 10 WS-FLA-NUM-GTE-2 9(05)

    public WsFlaNumGte() {
        super();
        initialize();
    }

    public WsFlaNumGte(DataContainer parent) {
        super(parent);
    }

    public WsFlaNumGte(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFlaNumGte_1(final String wsFlaNumGte_1) {
        this.wsFlaNumGte_1.setValue(wsFlaNumGte_1);
    }

    public void setWsFlaNumGte_1(final IAlphanumericValue wsFlaNumGte_1) {
        this.wsFlaNumGte_1.setValue(wsFlaNumGte_1);
    }

    public void setWsFlaNumGteX(final String wsFlaNumGteX) {
        this.wsFlaNumGteX.setValue(wsFlaNumGteX);
    }

    public void setWsFlaNumGteX(final IAlphanumericValue wsFlaNumGteX) {
        this.wsFlaNumGteX.setValue(wsFlaNumGteX);
    }

    public void setWsFlaNumGte_2(final int wsFlaNumGte_2) {
        this.wsFlaNumGte_2.setValue(wsFlaNumGte_2);
    }

    public void setWsFlaNumGte_2(final BigDecimal wsFlaNumGte_2) {
        this.wsFlaNumGte_2.setValue(wsFlaNumGte_2);
    }

    public void setWsFlaNumGte_2(final INumericValue wsFlaNumGte_2) {
        this.wsFlaNumGte_2.setValue(wsFlaNumGte_2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFlaNumGte_1.clear();
        this.wsFlaNumGteX.clear();
        this.wsFlaNumGte_2.clear();
    }

}

