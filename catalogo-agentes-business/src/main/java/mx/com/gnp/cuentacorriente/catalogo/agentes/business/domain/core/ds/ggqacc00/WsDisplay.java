package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DISPLAY.
 *
 */
@Setter
@Getter
public class WsDisplay { // VStruct
    // Properties
    private NumericEditedVar wsDiferenciaEdit = new NumericEditedVar("----,---,--9");        // 03 WS-DIFERENCIA-EDIT ----,---,--9

    public WsDisplay() {
        initialize();
    }



    public void setWsDiferenciaEdit(final String wsDiferenciaEdit) {
        this.wsDiferenciaEdit.setValue(wsDiferenciaEdit);
    }

    public void setWsDiferenciaEdit(final IValue wsDiferenciaEdit) {
        this.wsDiferenciaEdit.setValue(wsDiferenciaEdit);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsDiferenciaEdit.clear();
    }

}

