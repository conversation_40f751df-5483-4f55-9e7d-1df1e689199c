package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.indicadoresdenulos.IndicadoresCna;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICADORES-DE-NULOS.
 *
 */
// 294      *---                                                              
// 295      *   DECLARACION DE VARIABLES NULLS                                
// 296      *---                                                              
@Setter
@Getter
public class IndicadoresDeNulos { // VStruct
    // Properties
    private IndicadoresCna indicadoresCna = new  IndicadoresCna();           // 03 INDICADORES-CNA

    public IndicadoresDeNulos() {
        initialize();
    }




    // Deeper properties

    // Property: indicadoresCna.indcnatfp -> 05 INDCNATFP S9(4) COMPUTATIONAL
    public int getIndcnatfp() {
        return this.indicadoresCna.getIndcnatfp();
    }

    public void setIndcnatfp(final int indcnatfp) {
        this.indicadoresCna.setIndcnatfp(indcnatfp);
    }

    // Property: indicadoresCna.indcnamte -> 05 INDCNAMTE S9(4) COMPUTATIONAL
    public int getIndcnamte() {
        return this.indicadoresCna.getIndcnamte();
    }

    public void setIndcnamte(final int indcnamte) {
        this.indicadoresCna.setIndcnamte(indcnamte);
    }

    // Property: indicadoresCna.indcnactbnum -> 05 INDCNACTBNUM S9(4) COMPUTATIONAL
    public int getIndcnactbnum() {
        return this.indicadoresCna.getIndcnactbnum();
    }

    public void setIndcnactbnum(final int indcnactbnum) {
        this.indicadoresCna.setIndcnactbnum(indcnactbnum);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.indicadoresCna.initialize();
    }

}

