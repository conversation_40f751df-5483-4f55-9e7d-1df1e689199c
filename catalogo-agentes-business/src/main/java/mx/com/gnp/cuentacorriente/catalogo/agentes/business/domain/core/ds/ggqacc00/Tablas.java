package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablas.TablaImptes;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablas.tablaimptes.TabImptes;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLAS.
 *
 */
@Setter
@Getter
public class Tablas { // VStruct
    // Properties
    private TablaImptes tablaImptes = new  TablaImptes();                    // 02 TABLA-IMPTES

    public Tablas() {
        initialize();
    }




    // Deeper properties

    // Property: tablaImptes.tabImptes -> 03 TAB-IMPTES [2]
    public TabImptes getTabImptes() {
        return this.tablaImptes.getTabImptes();
    }

    // Property: tablaImptes.tabImptes.tbPercGravab -> 05 TB-PERC-GRAVAB S9(14)V99
    public NumericVar getTbPercGravab() {
        return this.tablaImptes.getTabImptes().getTbPercGravab();
    }

    public void setTbPercGravab(final BigDecimal tbPercGravab) {
        this.tablaImptes.getTabImptes().setTbPercGravab(tbPercGravab);
    }

    public void setTbPercGravab(final int tbPercGravab) {
        this.tablaImptes.getTabImptes().setTbPercGravab(tbPercGravab);
    }

    public void setTbPercGravab(final INumericValue tbPercGravab) {
        this.tablaImptes.getTabImptes().setTbPercGravab(tbPercGravab);
    }

    // Property: tablaImptes.tabImptes.tbPercExenta -> 05 TB-PERC-EXENTA S9(14)V99
    public NumericVar getTbPercExenta() {
        return this.tablaImptes.getTabImptes().getTbPercExenta();
    }

    public void setTbPercExenta(final BigDecimal tbPercExenta) {
        this.tablaImptes.getTabImptes().setTbPercExenta(tbPercExenta);
    }

    public void setTbPercExenta(final int tbPercExenta) {
        this.tablaImptes.getTabImptes().setTbPercExenta(tbPercExenta);
    }

    public void setTbPercExenta(final INumericValue tbPercExenta) {
        this.tablaImptes.getTabImptes().setTbPercExenta(tbPercExenta);
    }

    // Property: tablaImptes.tabImptes.tbIva -> 05 TB-IVA S9(14)V99
    public NumericVar getTbIva() {
        return this.tablaImptes.getTabImptes().getTbIva();
    }

    public void setTbIva(final BigDecimal tbIva) {
        this.tablaImptes.getTabImptes().setTbIva(tbIva);
    }

    public void setTbIva(final int tbIva) {
        this.tablaImptes.getTabImptes().setTbIva(tbIva);
    }

    public void setTbIva(final INumericValue tbIva) {
        this.tablaImptes.getTabImptes().setTbIva(tbIva);
    }

    // Property: tablaImptes.tabImptes.tbIsr -> 05 TB-ISR S9(14)V99
    public NumericVar getTbIsr() {
        return this.tablaImptes.getTabImptes().getTbIsr();
    }

    public void setTbIsr(final BigDecimal tbIsr) {
        this.tablaImptes.getTabImptes().setTbIsr(tbIsr);
    }

    public void setTbIsr(final int tbIsr) {
        this.tablaImptes.getTabImptes().setTbIsr(tbIsr);
    }

    public void setTbIsr(final INumericValue tbIsr) {
        this.tablaImptes.getTabImptes().setTbIsr(tbIsr);
    }

    // Property: tablaImptes.tabImptes.tbIvaAcr -> 05 TB-IVA-ACR S9(14)V99
    public NumericVar getTbIvaAcr() {
        return this.tablaImptes.getTabImptes().getTbIvaAcr();
    }

    public void setTbIvaAcr(final BigDecimal tbIvaAcr) {
        this.tablaImptes.getTabImptes().setTbIvaAcr(tbIvaAcr);
    }

    public void setTbIvaAcr(final int tbIvaAcr) {
        this.tablaImptes.getTabImptes().setTbIvaAcr(tbIvaAcr);
    }

    public void setTbIvaAcr(final INumericValue tbIvaAcr) {
        this.tablaImptes.getTabImptes().setTbIvaAcr(tbIvaAcr);
    }

    // Property: tablaImptes.tabImptes.tbIvaRtn -> 05 TB-IVA-RTN S9(14)V99
    public NumericVar getTbIvaRtn() {
        return this.tablaImptes.getTabImptes().getTbIvaRtn();
    }

    public void setTbIvaRtn(final BigDecimal tbIvaRtn) {
        this.tablaImptes.getTabImptes().setTbIvaRtn(tbIvaRtn);
    }

    public void setTbIvaRtn(final int tbIvaRtn) {
        this.tablaImptes.getTabImptes().setTbIvaRtn(tbIvaRtn);
    }

    public void setTbIvaRtn(final INumericValue tbIvaRtn) {
        this.tablaImptes.getTabImptes().setTbIvaRtn(tbIvaRtn);
    }

    // Property: tablaImptes.tabImptes.tbIsrRet -> 05 TB-ISR-RET S9(14)V99
    public NumericVar getTbIsrRet() {
        return this.tablaImptes.getTabImptes().getTbIsrRet();
    }

    public void setTbIsrRet(final BigDecimal tbIsrRet) {
        this.tablaImptes.getTabImptes().setTbIsrRet(tbIsrRet);
    }

    public void setTbIsrRet(final int tbIsrRet) {
        this.tablaImptes.getTabImptes().setTbIsrRet(tbIsrRet);
    }

    public void setTbIsrRet(final INumericValue tbIsrRet) {
        this.tablaImptes.getTabImptes().setTbIsrRet(tbIsrRet);
    }

    // Property: tablaImptes.tabImptes.tbIpuCed -> 05 TB-IPU-CED S9(14)V99
    public NumericVar getTbIpuCed() {
        return this.tablaImptes.getTabImptes().getTbIpuCed();
    }

    public void setTbIpuCed(final BigDecimal tbIpuCed) {
        this.tablaImptes.getTabImptes().setTbIpuCed(tbIpuCed);
    }

    public void setTbIpuCed(final int tbIpuCed) {
        this.tablaImptes.getTabImptes().setTbIpuCed(tbIpuCed);
    }

    public void setTbIpuCed(final INumericValue tbIpuCed) {
        this.tablaImptes.getTabImptes().setTbIpuCed(tbIpuCed);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tablaImptes.initialize();
    }

}

