package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datoscocoa.PkFolioInvesr;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DATOS-COCOA.
 *
 */
// 276024400*=>     0654-00660                                                02440000
// 277024500*                                                                 02450000
// 278024600*         ===>    24  <===                                        02460000
// 279024700*         ===>+  636  <===                                        02470000
// 280024800*         ===>   660  <===                                        02480000
// 281024900*                                                                 02490000
// 282025000*                                                                 02500000
@Setter
@Getter
public class DatosCocoa extends DataStruct {
    // Properties
    private AlphanumericVar pkExiste = new AlphanumericVar(this, 1);         // 05 PK-EXISTE X
    private AlphanumericVar pkCveSist = new AlphanumericVar(this, 1);        // 05 PK-CVE-SIST X
    private UnsignedNumericVar pkFecRehab = new UnsignedNumericVar(this, 6, 0);              // 05 PK-FEC-REHAB 9(06)
    private AlphanumericVar pkQuienExime = new AlphanumericVar(this, 35);    // 05 PK-QUIEN-EXIME X(35)
    private UnsignedNumericVar pkFolioInves = new UnsignedNumericVar(this, 7, 0);            // 05 PK-FOLIO-INVES 9(07) REDEFINED BY PK-FOLIO-INVESR
    private PkFolioInvesr pkFolioInvesr = new  PkFolioInvesr(this, this.pkFolioInves);       // 05 PK-FOLIO-INVESR REDEFINES PK-FOLIO-INVES
    private AlphanumericVar pkAdicionales = new AlphanumericVar(this, 60);   // 05 PK-ADICIONALES X(60)
    private AlphanumericVar pkFotos = new AlphanumericVar(this, 1);          // 05 PK-FOTOS X(01)
    private AlphanumericVar pkActaNac = new AlphanumericVar(this, 1);        // 05 PK-ACTA-NAC X(01)
    private AlphanumericVar pkCertificado = new AlphanumericVar(this, 1);    // 05 PK-CERTIFICADO X(01)
    private AlphanumericVar pkCartaRecom = new AlphanumericVar(this, 1);     // 05 PK-CARTA-RECOM X(01)
    private AlphanumericVar pkAltaShcp = new AlphanumericVar(this, 1);       // 05 PK-ALTA-SHCP X(01)
    private AlphanumericVar pkHomoclave = new AlphanumericVar(this, 1);      // 05 PK-HOMOCLAVE X(01)
    private AlphanumericVar pkCartaBaja = new AlphanumericVar(this, 1);      // 05 PK-CARTA-BAJA X(01)
    private AlphanumericVar pkCedulaDef = new AlphanumericVar(this, 1);      // 05 PK-CEDULA-DEF X(01)
    private AlphanumericVar pkEdoCta = new AlphanumericVar(this, 1);         // 05 PK-EDO-CTA X(01)
    private AlphanumericVar pkFm2 = new AlphanumericVar(this, 1);            // 05 PK-FM2 X(01)
    private AlphanumericVar pkSolicitud = new AlphanumericVar(this, 1);      // 05 PK-SOLICITUD X(01)
    private AlphanumericVar pkFas2 = new AlphanumericVar(this, 1);           // 05 PK-FAS2 X(01)
    private AlphanumericVar pkCartaArt10 = new AlphanumericVar(this, 1);     // 05 PK-CARTA-ART10 X(01)
    private AlphanumericVar pkSeguroCol = new AlphanumericVar(this, 1);      // 05 PK-SEGURO-COL X(01)
    private AlphanumericVar pkContrato = new AlphanumericVar(this, 1);       // 05 PK-CONTRATO X(01)
    private AlphanumericVar pkChequePagoDer = new AlphanumericVar(this, 1);  // 05 PK-CHEQUE-PAGO-DER X(01)
    private AlphanumericVar pkCopiaCedDef = new AlphanumericVar(this, 1);    // 05 PK-COPIA-CED-DEF X(01)
    private AlphanumericVar pkCursoPade = new AlphanumericVar(this, 1);      // 05 PK-CURSO-PADE X(01)
    private AlphanumericVar pkInvestigacion = new AlphanumericVar(this, 1);  // 05 PK-INVESTIGACION X(01)
    private UnsignedNumericVar pkFechaPlazo = new UnsignedNumericVar(this, 6, 0);            // 05 PK-FECHA-PLAZO 9(6)
    private AlphanumericVar pkTipCedulaAux = new AlphanumericVar(this, 1);   // 05 PK-TIP-CEDULA-AUX X
    private UnsignedNumericVar pkNivelNal = new UnsignedNumericVar(this, 2, 0);              // 05 PK-NIVEL-NAL 99
    private UnsignedNumericVar pkNivelProv = new UnsignedNumericVar(this, 2, 0);             // 05 PK-NIVEL-PROV 99

    public DatosCocoa() {
        super();
        initialize();
    }

    public DatosCocoa(DataContainer parent) {
        super(parent);
    }

    public DatosCocoa(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkExiste(final String pkExiste) {
        this.pkExiste.setValue(pkExiste);
    }

    public void setPkExiste(final IAlphanumericValue pkExiste) {
        this.pkExiste.setValue(pkExiste);
    }

    public void setPkCveSist(final String pkCveSist) {
        this.pkCveSist.setValue(pkCveSist);
    }

    public void setPkCveSist(final IAlphanumericValue pkCveSist) {
        this.pkCveSist.setValue(pkCveSist);
    }

    public void setPkFecRehab(final int pkFecRehab) {
        this.pkFecRehab.setValue(pkFecRehab);
    }

    public void setPkFecRehab(final BigDecimal pkFecRehab) {
        this.pkFecRehab.setValue(pkFecRehab);
    }

    public void setPkFecRehab(final INumericValue pkFecRehab) {
        this.pkFecRehab.setValue(pkFecRehab);
    }

    public void setPkQuienExime(final String pkQuienExime) {
        this.pkQuienExime.setValue(pkQuienExime);
    }

    public void setPkQuienExime(final IAlphanumericValue pkQuienExime) {
        this.pkQuienExime.setValue(pkQuienExime);
    }

    public void setPkFolioInves(final int pkFolioInves) {
        this.pkFolioInves.setValue(pkFolioInves);
    }

    public void setPkFolioInves(final BigDecimal pkFolioInves) {
        this.pkFolioInves.setValue(pkFolioInves);
    }

    public void setPkFolioInves(final INumericValue pkFolioInves) {
        this.pkFolioInves.setValue(pkFolioInves);
    }

    public void setPkAdicionales(final String pkAdicionales) {
        this.pkAdicionales.setValue(pkAdicionales);
    }

    public void setPkAdicionales(final IAlphanumericValue pkAdicionales) {
        this.pkAdicionales.setValue(pkAdicionales);
    }

    public void setPkFotos(final String pkFotos) {
        this.pkFotos.setValue(pkFotos);
    }

    public void setPkFotos(final IAlphanumericValue pkFotos) {
        this.pkFotos.setValue(pkFotos);
    }

    public void setPkActaNac(final String pkActaNac) {
        this.pkActaNac.setValue(pkActaNac);
    }

    public void setPkActaNac(final IAlphanumericValue pkActaNac) {
        this.pkActaNac.setValue(pkActaNac);
    }

    public void setPkCertificado(final String pkCertificado) {
        this.pkCertificado.setValue(pkCertificado);
    }

    public void setPkCertificado(final IAlphanumericValue pkCertificado) {
        this.pkCertificado.setValue(pkCertificado);
    }

    public void setPkCartaRecom(final String pkCartaRecom) {
        this.pkCartaRecom.setValue(pkCartaRecom);
    }

    public void setPkCartaRecom(final IAlphanumericValue pkCartaRecom) {
        this.pkCartaRecom.setValue(pkCartaRecom);
    }

    public void setPkAltaShcp(final String pkAltaShcp) {
        this.pkAltaShcp.setValue(pkAltaShcp);
    }

    public void setPkAltaShcp(final IAlphanumericValue pkAltaShcp) {
        this.pkAltaShcp.setValue(pkAltaShcp);
    }

    public void setPkHomoclave(final String pkHomoclave) {
        this.pkHomoclave.setValue(pkHomoclave);
    }

    public void setPkHomoclave(final IAlphanumericValue pkHomoclave) {
        this.pkHomoclave.setValue(pkHomoclave);
    }

    public void setPkCartaBaja(final String pkCartaBaja) {
        this.pkCartaBaja.setValue(pkCartaBaja);
    }

    public void setPkCartaBaja(final IAlphanumericValue pkCartaBaja) {
        this.pkCartaBaja.setValue(pkCartaBaja);
    }

    public void setPkCedulaDef(final String pkCedulaDef) {
        this.pkCedulaDef.setValue(pkCedulaDef);
    }

    public void setPkCedulaDef(final IAlphanumericValue pkCedulaDef) {
        this.pkCedulaDef.setValue(pkCedulaDef);
    }

    public void setPkEdoCta(final String pkEdoCta) {
        this.pkEdoCta.setValue(pkEdoCta);
    }

    public void setPkEdoCta(final IAlphanumericValue pkEdoCta) {
        this.pkEdoCta.setValue(pkEdoCta);
    }

    public void setPkFm2(final String pkFm2) {
        this.pkFm2.setValue(pkFm2);
    }

    public void setPkFm2(final IAlphanumericValue pkFm2) {
        this.pkFm2.setValue(pkFm2);
    }

    public void setPkSolicitud(final String pkSolicitud) {
        this.pkSolicitud.setValue(pkSolicitud);
    }

    public void setPkSolicitud(final IAlphanumericValue pkSolicitud) {
        this.pkSolicitud.setValue(pkSolicitud);
    }

    public void setPkFas2(final String pkFas2) {
        this.pkFas2.setValue(pkFas2);
    }

    public void setPkFas2(final IAlphanumericValue pkFas2) {
        this.pkFas2.setValue(pkFas2);
    }

    public void setPkCartaArt10(final String pkCartaArt10) {
        this.pkCartaArt10.setValue(pkCartaArt10);
    }

    public void setPkCartaArt10(final IAlphanumericValue pkCartaArt10) {
        this.pkCartaArt10.setValue(pkCartaArt10);
    }

    public void setPkSeguroCol(final String pkSeguroCol) {
        this.pkSeguroCol.setValue(pkSeguroCol);
    }

    public void setPkSeguroCol(final IAlphanumericValue pkSeguroCol) {
        this.pkSeguroCol.setValue(pkSeguroCol);
    }

    public void setPkContrato(final String pkContrato) {
        this.pkContrato.setValue(pkContrato);
    }

    public void setPkContrato(final IAlphanumericValue pkContrato) {
        this.pkContrato.setValue(pkContrato);
    }

    public void setPkChequePagoDer(final String pkChequePagoDer) {
        this.pkChequePagoDer.setValue(pkChequePagoDer);
    }

    public void setPkChequePagoDer(final IAlphanumericValue pkChequePagoDer) {
        this.pkChequePagoDer.setValue(pkChequePagoDer);
    }

    public void setPkCopiaCedDef(final String pkCopiaCedDef) {
        this.pkCopiaCedDef.setValue(pkCopiaCedDef);
    }

    public void setPkCopiaCedDef(final IAlphanumericValue pkCopiaCedDef) {
        this.pkCopiaCedDef.setValue(pkCopiaCedDef);
    }

    public void setPkCursoPade(final String pkCursoPade) {
        this.pkCursoPade.setValue(pkCursoPade);
    }

    public void setPkCursoPade(final IAlphanumericValue pkCursoPade) {
        this.pkCursoPade.setValue(pkCursoPade);
    }

    public void setPkInvestigacion(final String pkInvestigacion) {
        this.pkInvestigacion.setValue(pkInvestigacion);
    }

    public void setPkInvestigacion(final IAlphanumericValue pkInvestigacion) {
        this.pkInvestigacion.setValue(pkInvestigacion);
    }

    public void setPkFechaPlazo(final int pkFechaPlazo) {
        this.pkFechaPlazo.setValue(pkFechaPlazo);
    }

    public void setPkFechaPlazo(final BigDecimal pkFechaPlazo) {
        this.pkFechaPlazo.setValue(pkFechaPlazo);
    }

    public void setPkFechaPlazo(final INumericValue pkFechaPlazo) {
        this.pkFechaPlazo.setValue(pkFechaPlazo);
    }

    public void setPkTipCedulaAux(final String pkTipCedulaAux) {
        this.pkTipCedulaAux.setValue(pkTipCedulaAux);
    }

    public void setPkTipCedulaAux(final IAlphanumericValue pkTipCedulaAux) {
        this.pkTipCedulaAux.setValue(pkTipCedulaAux);
    }

    public void setPkNivelNal(final int pkNivelNal) {
        this.pkNivelNal.setValue(pkNivelNal);
    }

    public void setPkNivelNal(final BigDecimal pkNivelNal) {
        this.pkNivelNal.setValue(pkNivelNal);
    }

    public void setPkNivelNal(final INumericValue pkNivelNal) {
        this.pkNivelNal.setValue(pkNivelNal);
    }

    public void setPkNivelProv(final int pkNivelProv) {
        this.pkNivelProv.setValue(pkNivelProv);
    }

    public void setPkNivelProv(final BigDecimal pkNivelProv) {
        this.pkNivelProv.setValue(pkNivelProv);
    }

    public void setPkNivelProv(final INumericValue pkNivelProv) {
        this.pkNivelProv.setValue(pkNivelProv);
    }


    // Deeper properties

    // Property: pkFolioInvesr.pkFolio1 -> 07 PK-FOLIO1 999
    public UnsignedNumericVar getPkFolio1() {
        return this.pkFolioInvesr.getPkFolio1();
    }

    public void setPkFolio1(final int pkFolio1) {
        this.pkFolioInvesr.setPkFolio1(pkFolio1);
    }

    public void setPkFolio1(final INumericValue pkFolio1) {
        this.pkFolioInvesr.setPkFolio1(pkFolio1);
    }

    // Property: pkFolioInvesr.pkFolio2 -> 07 PK-FOLIO2 9999
    public UnsignedNumericVar getPkFolio2() {
        return this.pkFolioInvesr.getPkFolio2();
    }

    public void setPkFolio2(final int pkFolio2) {
        this.pkFolioInvesr.setPkFolio2(pkFolio2);
    }

    public void setPkFolio2(final INumericValue pkFolio2) {
        this.pkFolioInvesr.setPkFolio2(pkFolio2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkExiste.clear();
        this.pkCveSist.clear();
        this.pkFecRehab.clear();
        this.pkQuienExime.clear();
        this.pkFolioInves.clear();
        this.pkAdicionales.clear();
        this.pkFotos.clear();
        this.pkActaNac.clear();
        this.pkCertificado.clear();
        this.pkCartaRecom.clear();
        this.pkAltaShcp.clear();
        this.pkHomoclave.clear();
        this.pkCartaBaja.clear();
        this.pkCedulaDef.clear();
        this.pkEdoCta.clear();
        this.pkFm2.clear();
        this.pkSolicitud.clear();
        this.pkFas2.clear();
        this.pkCartaArt10.clear();
        this.pkSeguroCol.clear();
        this.pkContrato.clear();
        this.pkChequePagoDer.clear();
        this.pkCopiaCedDef.clear();
        this.pkCursoPade.clear();
        this.pkInvestigacion.clear();
        this.pkFechaPlazo.clear();
        this.pkTipCedulaAux.clear();
        this.pkNivelNal.clear();
        this.pkNivelProv.clear();
    }

}

