package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.wscodigos.WsCodError;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CODIGOS.
 *
 */
// 113051501*                                                                 05150100
@Setter
@Getter
public class WsCodigos { // VStruct
    // Properties
    private int wsSqlcode;                                                  // 03 WS-SQLCODE 9(9)
    private WsCodError wsCodError = new  WsCodError();                       // 03 WS-COD-ERROR
    private NumericEditedVar wsSqlerrml = new NumericEditedVar("-------99");                 // 03 WS-SQLERRML -------99

    public WsCodigos() {
        initialize();
    }



    public void setWsSqlerrml(final String wsSqlerrml) {
        this.wsSqlerrml.setValue(wsSqlerrml);
    }

    public void setWsSqlerrml(final IValue wsSqlerrml) {
        this.wsSqlerrml.setValue(wsSqlerrml);
    }


    // Deeper properties

    // Property: wsCodError.wsDis1 -> 05 WS-DIS1 X
    public AlphanumericVar getWsDis1() {
        return this.wsCodError.getWsDis1();
    }

    public void setWsDis1(final String wsDis1) {
        this.wsCodError.setWsDis1(wsDis1);
    }

    public void setWsDis1(final IAlphanumericValue wsDis1) {
        this.wsCodError.setWsDis1(wsDis1);
    }

    // Property: wsCodError.wsDis2 -> 05 WS-DIS2 9(9)
    public UnsignedNumericVar getWsDis2() {
        return this.wsCodError.getWsDis2();
    }

    public void setWsDis2(final int wsDis2) {
        this.wsCodError.setWsDis2(wsDis2);
    }

    public void setWsDis2(final INumericValue wsDis2) {
        this.wsCodError.setWsDis2(wsDis2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsSqlcode = 0;
        this.wsCodError.initialize();
        this.wsSqlerrml.clear();
    }

}

