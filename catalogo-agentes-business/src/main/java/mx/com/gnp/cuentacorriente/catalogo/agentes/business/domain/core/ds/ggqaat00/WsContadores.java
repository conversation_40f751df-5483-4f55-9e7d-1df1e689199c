package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CONTADORES.
 *
 */
// 662LUV001*=>     0844-00844                                                01470020
// 665      *                                                                 
// 666      *------------------- CONTADORES ---------------------------------*
@Setter
@Getter
public class WsContadores { // VStruct
    // Properties
    private int wcRegistrosLeiAgt = 0;                                      // 05 WC-REGISTROS-LEI-AGT 9(09)
    private int wcRegistrosGrab = 0;                                        // 05 WC-REGISTROS-GRAB 9(09)
    private int wcC1 = 0;                                                   // 05 WC-C1 9(03)
    private int wcC2 = 0;                                                   // 05 WC-C2 9(03)
    private int wcC3 = 0;                                                   // 05 WC-C3 9(03)
    private int wcC4 = 0;                                                   // 05 WC-C4 9(03)
    private int wcC5 = 0;                                                   // 05 WC-C5 9(03)

    public WsContadores() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wcRegistrosLeiAgt = 0;
        this.wcRegistrosGrab = 0;
        this.wcC1 = 0;
        this.wcC2 = 0;
        this.wcC3 = 0;
        this.wcC4 = 0;
        this.wcC5 = 0;
    }

}

