
/**
 *  Program: Ggnfoz00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.common.cbl.functions.Functions;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.RegArchagth;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.RegArchcnah;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.RegArchdmah;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.RegArchcmah;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.RegArchdrc0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.RegArchintr;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa0055cpy.FhFechaHoraSistS;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00olcpy.AbReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa0062cpy.FhFechaHoraSist;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.WsConstants;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.WsVariables;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.WsVariabsus;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagthcpy.Dclgfvtagth;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcnahcpy.Dclgfvtcnah;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdmahcpy.Dclgfvtdmah;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcmahcpy.Dclgfvtcmah;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdrc0cpy.Dclgfvtdrc0;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdrc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagthPort;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdmahPort;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcnahPort;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcmahPort;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdrc0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.GfvtagthModel;
import mx.com.gnp.cuentacorriente.common.business.domain.model.GfvtdmahModel;
import mx.com.gnp.cuentacorriente.common.business.domain.model.GfvtcnahModel;
import mx.com.gnp.cuentacorriente.common.business.domain.model.GfvtcmahModel;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggnfoz00 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private RegArchagth regArchagth = new  RegArchagth();                    // 01 REG-ARCHAGTH UsedAsReferenceParameter
    private RegArchcnah regArchcnah = new  RegArchcnah();                    // 01 REG-ARCHCNAH UsedAsReferenceParameter
    private RegArchdmah regArchdmah = new  RegArchdmah();                    // 01 REG-ARCHDMAH UsedAsReferenceParameter
    private RegArchcmah regArchcmah = new  RegArchcmah();                    // 01 REG-ARCHCMAH UsedAsReferenceParameter
    private RegArchdrc0 regArchdrc0 = new  RegArchdrc0();                    // 01 REG-ARCHDRC0 UsedAsReferenceParameter
    private RegArchintr regArchintr = new  RegArchintr();                    // 01 REG-ARCHINTR UsedAsReferenceParameter

    // Copy file QA$CBLCPY$GGQA00LN
//  private Ggqa00lncpy ggqa00lncpy = new  Ggqa00lncpy();                // Copy file // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA00LN

    // Copy file QA$CBLCPY$GGQA0055
    private FhFechaHoraSistS fhFechaHoraSistS = new  FhFechaHoraSistS();     // 01 FH-FECHA-HORA-SIST-S
    // End copy file QA$CBLCPY$GGQA0055

    // Copy file QA$CBLCPY$GGQA0006
//  private Ggqa0006cpy ggqa0006cpy = new  Ggqa0006cpy();                // Copy file // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA0006

    // Auto variables
//  private int t999MdI;                            // T999-MD-I // [@WARNING variable NOT USED]
//  private int iError;                             // I-ERROR // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GGQA00OL
    private AbReg abReg = new  AbReg();                                      // 01 AB-REG
    // End copy file QA$CBLCPY$GGQA00OL

    // Copy file QA$CBLCPY$GGQA0062
    private FhFechaHoraSist fhFechaHoraSist = new  FhFechaHoraSist();        // 01 FH-FECHA-HORA-SIST
    // End copy file QA$CBLCPY$GGQA0062

    // Copy file QA$CBLCPY$GGQA00X3
//  private Ggqa00x3cpy ggqa00x3cpy = new  Ggqa00x3cpy();                // Copy file // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA00X3

    // Level 1
    private WsConstants wsConstants = new  WsConstants();                    // 01 WS-CONSTANTS
    private WsVariables wsVariables = new  WsVariables();                    // 01 WS-VARIABLES
    private WsVariabsus wsVariabsus = new  WsVariabsus();                    // 01 WS-VARIABSUS
    private int db2ReturnCode;                                              // 01 DB2-RETURN-CODE S9(9) COMPUTATIONAL
    // Level 88 - Conditional names
    private static final int DB2_OK = 0;
    private static final int DB2_NOTFND = 100;
    private static final int DB2_DUPREC = -803;
    private static final int DB2_NOTUNI = -811;
    private static final int DB2_DATETIME = -181;
    private static final int DB2_SYSTEM = -911;


    // Copy file QA$CBLCPY$GFVTAGTH
    private Dclgfvtagth dclgfvtagth = new  Dclgfvtagth();                    // 01 DCLGFVTAGTH
    // End copy file QA$CBLCPY$GFVTAGTH

    // Copy file QA$CBLCPY$GFVTCNAH
    private Dclgfvtcnah dclgfvtcnah = new  Dclgfvtcnah();                    // 01 DCLGFVTCNAH
    // End copy file QA$CBLCPY$GFVTCNAH

    // Copy file QA$CBLCPY$GFVTCNA0
//  private Dclgfvtcna0 dclgfvtcna0 = new  Dclgfvtcna0();                        // 01 DCLGFVTCNA0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTCNA0

    // Copy file QA$CBLCPY$GFVTDMAH
    private Dclgfvtdmah dclgfvtdmah = new  Dclgfvtdmah();                    // 01 DCLGFVTDMAH
    // End copy file QA$CBLCPY$GFVTDMAH

    // Copy file QA$CBLCPY$GFVTCMAH
    private Dclgfvtcmah dclgfvtcmah = new  Dclgfvtcmah();                    // 01 DCLGFVTCMAH
    // End copy file QA$CBLCPY$GFVTCMAH

    // Copy file QA$CBLCPY$GFVTDRC0
    private Dclgfvtdrc0 dclgfvtdrc0 = new  Dclgfvtdrc0();                    // 01 DCLGFVTDRC0
    // End copy file QA$CBLCPY$GFVTDRC0

    // Copy file QA$CBLCPY$GFVTFLA0
//  private Dclgfvtfla0 dclgfvtfla0 = new  Dclgfvtfla0();                        // 01 DCLGFVTFLA0 // [@WARNING variable NOT USED]

    private Sqlca sqlca;
    // Files
    private ISequentialFile archagth;
    private ISequentialFile archcnah;
    private ISequentialFile archdmah;
    private ISequentialFile archcmah;
    private ISequentialFile archdrc0;
    private ISequentialFile archintr;



    // Declare Sql Ports
    private IGfvtdrc0Port gfvtdrc0Port;
    private IGfvtagthPort gfvtagthPort;
    private IGfvtdmahPort gfvtdmahPort;
    private IGfvtcnahPort gfvtcnahPort;
    private IGfvtcmahPort gfvtcmahPort;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtdrc0Model> gfvtdrc0Model;
    private Optional<GfvtagthModel> gfvtagthModel;
    private Optional<GfvtdmahModel> gfvtdmahModel;
    private Optional<GfvtcnahModel> gfvtcnahModel;
    private Optional<GfvtcmahModel> gfvtcmahModel;

    // Declare Sql Cursors
    private ICursor<GenericModel> gfvtagthCursor;

    public Ggnfoz00(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initFiles();
    }

    public void initFiles() {

        this.archagth = createSequentialFile("ARCHAGTH", regArchagth);

        this.archcnah = createSequentialFile("ARCHCNAH", regArchcnah);

        this.archdmah = createSequentialFile("ARCHDMAH", regArchdmah);

        this.archcmah = createSequentialFile("ARCHCMAH", regArchcmah);

        this.archdrc0 = createSequentialFile("ARCHDRC0", regArchdrc0);

        this.archintr = createSequentialFile("ARCHINTR", regArchintr);

        this.archagth.onChangeFileStatus(status -> wsConstants.setFsAgth(status));

        this.archcnah.onChangeFileStatus(status -> wsConstants.setFsCnah(status));

        this.archdmah.onChangeFileStatus(status -> wsConstants.setFsDmah(status));

        this.archcmah.onChangeFileStatus(status -> wsConstants.setFsCmah(status));

        this.archdrc0.onChangeFileStatus(status -> wsConstants.setFsDrc0(status));

        this.archintr.onChangeFileStatus(status -> wsConstants.setFsIntr(status));

    }

    public void run() {
        sqlDelayedParagraph();
        p00000Principal();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(gfvtagthCursor == null) {
            gfvtagthCursor = gfvtcnahPort.createCursorGfvtagth(
                () -> null);
        }
        //
        // EXIT-SQL-DELAYED-PARAGRAPH - Last paragraph
        //
    }

    /**
     *  Paragraph: 00000-PRINCIPAL.
     */
    void p00000Principal() {
        p10000Inicio();
        p20000Proceso();
        p30000Termina();
        throw new StopRunException();
        /* [@WARNING Unreachable code]
        */
    }

    /**
     *  Paragraph: 10000-INICIO.
     *
     */
    void p10000Inicio() {
        // [@WARNING LOW-VALUE initialization required]
        wsVariables.initialize();
        p11000ObtnrFcha();
        p12000AbrirArchs();
        p13000LeerAgth();
        p14000LeerCnah();
        p15000LeerDmah();
        p16000LeerCmah();
        p17000LeerDrc0();
    }

    /**
     *  Paragraph: 11000-OBTNR-FCHA.
     *
     */
    void p11000ObtnrFcha() {
        fhFechaHoraSist.getFhFSist().setValue(getDate());
        fhFechaHoraSist.getFhHSist().setValue(getTime());
        if (fhFechaHoraSist.getFhAnoSist().toInt() > 80) {
            fhFechaHoraSistS.setFhSiglo(19);
        } else {
            fhFechaHoraSistS.setFhSiglo(20);
        }
        fhFechaHoraSistS.setFhDiaSistS(fhFechaHoraSist.getFhDiaSist());
        fhFechaHoraSistS.setFhMesSistS(fhFechaHoraSist.getFhMesSist());
        fhFechaHoraSistS.setFhAnoSistS(fhFechaHoraSist.getFhAnoSist());
        fhFechaHoraSistS.getFhHSistS().setValue(fhFechaHoraSist.getFhHSist());
        fhFechaHoraSistS.setFhCentSegSistS(0);
    }

    /**
     *  Paragraph: 12000-ABRIR-ARCHS.
     *
     */
    void p12000AbrirArchs() {
        abReg.getAbMensaje().at(2).setValue("NO SE GRABO RGISTROS EN HISTORICOS");
        archagth.openInput();
        if (!wsConstants.getFsAgth().equals(wsConstants.getWc_2cr())) {
            abReg.getAbMensaje().at(1).setValue("ERROR ABRIR ARCHIVO ARCHAGTH");
            p99910ChkAbnd();
        }
        //                                                                  
        archcnah.openInput();
        if (!wsConstants.getFsCnah().equals(wsConstants.getWc_2cr())) {
            abReg.getAbMensaje().at(1).setValue("ERROR ABRIR ARCHIVO ARCHCNAH");
            p99910ChkAbnd();
        }
        //                                                                  
        archdmah.openInput();
        if (!wsConstants.getFsDmah().equals(wsConstants.getWc_2cr())) {
            abReg.getAbMensaje().at(1).setValue("ERROR ABRIR ARCHIVO ARCHDMAH");
            p99910ChkAbnd();
        }
        //                                                                  
        archcmah.openInput();
        if (!wsConstants.getFsCmah().equals(wsConstants.getWc_2cr())) {
            abReg.getAbMensaje().at(1).setValue("ERROR ABRIR ARCHIVO ARCHCMAH");
            p99910ChkAbnd();
        }
        //                                                                  
        archdrc0.openInput();
        if (!wsConstants.getFsDrc0().equals(wsConstants.getWc_2cr())) {
            abReg.getAbMensaje().at(1).setValue("ERROR ABRIR ARCHIVO ARCHDRC0");
            p99910ChkAbnd();
        }
        //                                                                  
        archintr.openOutput();
        if (!wsConstants.getFsIntr().equals(wsConstants.getWc_2cr())) {
            abReg.getAbMensaje().at(1).setValue("ERROR ABRIR ARCHIVO ARCHINTR");
            p99910ChkAbnd();
        }
    }

    /**
     *  Paragraph: 13000-LEER-AGTH.
     *
     */
    void p13000LeerAgth() {
        archagth.read();
        if(archagth.isEof()) {
            wsVariables.setSwEofagth(Integer.valueOf(wsConstants.getWcUno()));

        }
    }

    /**
     *  Paragraph: 14000-LEER-CNAH.
     *
     */
    void p14000LeerCnah() {
        archcnah.read();
        if(archcnah.isEof()) {
            wsVariables.setSwEofcnah(Integer.valueOf(wsConstants.getWcUno()));

        }
    }

    /**
     *  Paragraph: 15000-LEER-DMAH.
     *
     */
    void p15000LeerDmah() {
        archdmah.read();
        if(archdmah.isEof()) {
            wsVariables.setSwEofdmah(Integer.valueOf(wsConstants.getWcUno()));

        }
    }

    /**
     *  Paragraph: 16000-LEER-CMAH.
     *
     */
    void p16000LeerCmah() {
        archcmah.read();
        if(archcmah.isEof()) {
            wsVariables.setSwEofcmah(Integer.valueOf(wsConstants.getWcUno()));

        }
    }

    /**
     *  Paragraph: 17000-LEER-DRC0.
     *
     */
    void p17000LeerDrc0() {
        archdrc0.read();
        if(archdrc0.isEof()) {
            wsVariables.setSwEofdrc0(Integer.valueOf(wsConstants.getWcUno()));

        }
    }

    /**
     *  Paragraph: 20000-PROCESO.
     *
     */
    void p20000Proceso() {
        p21000ProcsarAgth();
        p22000ProcsarCnah();
        p23000ProcsarDmah();
        p24000ProcsarCmah();
        p25000ProcsarDrc0();
        getSqlExecutor().commit(sqlca);
        p26000ProcsarIntr();
    }

    /**
     *  Paragraph: 21000-PROCSAR-AGTH.
     *
     */
    void p21000ProcsarAgth() {
        while (!(Functions.compare(wsVariables.getSwEofagth(), wsConstants.getWcUno()) == 0)) {
                wsVariables.setWsConagth(wsVariables.getWsConagth() + 1);
                wsVariabsus.initialize();
                wsVariabsus.setWsAgtIdr(regArchagth.getAgtIdr1().toInt());

                wsVariabsus.setWsAgtFecCamRazSo(regArchagth.getAgtFecCamRazSo1().toStringValue());

                dclgfvtagth.setAgtRfc(regArchagth.getAgtRfc1());
                dclgfvtagth.setAgtNom(regArchagth.getAgtNom1());
                dclgfvtagth.setAgtApePat(regArchagth.getAgtApePat1());
                dclgfvtagth.setAgtApeMat(regArchagth.getAgtApeMat1());
                dclgfvtagth.setAgtFecNac(regArchagth.getAgtFecNac1());
                dclgfvtagth.setAgtNumCed(regArchagth.getAgtNumCed1());
                dclgfvtagth.setAgtFecIniCed(regArchagth.getAgtFecIniCed1());
                dclgfvtagth.setAgtFecFinCed(regArchagth.getAgtFecFinCed1());
                dclgfvtagth.setAgtExpSeg(regArchagth.getAgtExpSeg1());
                dclgfvtagth.setAgtTraEsp(regArchagth.getAgtTraEsp1());
                wsVariabsus.setWsAgtClfLmr(regArchagth.getAgtClfLmr1().toInt());

                dclgfvtagth.setFecUltAct(regArchagth.getFecUltAct1());
                dclgfvtagth.setAgtRazSoc(regArchagth.getAgtRazSoc1());
                dclgfvtagth.setNalCve(regArchagth.getNalCve1());
                wsVariabsus.setWsPefCve(regArchagth.getPefCve1().toInt());

                dclgfvtagth.setTcaCve(regArchagth.getTcaCve1());
                wsVariabsus.setWsMteCve(regArchagth.getMteCve1().toInt());

                wsVariabsus.setWsAgtNumFolInv(regArchagth.getAgtNumFolInv1().toInt());

                dclgfvtagth.setAgtNomExi(regArchagth.getAgtNomExi1());
                dclgfvtagth.setAgtRstInv(regArchagth.getAgtRstInv1());
                dclgfvtagth.setAgtFecIniInv(regArchagth.getAgtFecIniInv1());
                dclgfvtagth.setAgtFecFinInv(regArchagth.getAgtFecFinInv1());
                dclgfvtagth.setAgtNomDphInv(regArchagth.getAgtNomDphInv1());
                dclgfvtagth.setAgtFecMotEss(regArchagth.getAgtFecMotEss1());
                dclgfvtagth.setAgtFecMotAsgIn(regArchagth.getAgtFecMotAsgIn1());
                dclgfvtagth.setAgtObs(regArchagth.getAgtObs1());
                wsVariabsus.setWsEssRek(regArchagth.getEssRek1().toStringValue());

                gfvtagthPort.insert(
                    GenericModel.builder().values(List.of(
                        wsVariabsus.getWsAgtIdr(),
                        toHostString(wsVariabsus.getWsAgtFecCamRazSo()),
                        toHostString(dclgfvtagth.getAgtRfc()),
                        toHostString(dclgfvtagth.getAgtNom()),
                        toHostString(dclgfvtagth.getAgtApePat()),
                        toHostString(dclgfvtagth.getAgtApeMat()),
                        toHostString(dclgfvtagth.getAgtFecNac()),
                        toHostString(dclgfvtagth.getAgtNumCed()),
                        toHostString(dclgfvtagth.getAgtFecIniCed()),
                        toHostString(dclgfvtagth.getAgtFecFinCed()),
                        toHostString(dclgfvtagth.getAgtExpSeg()),
                        toHostString(dclgfvtagth.getAgtTraEsp()),
                        wsVariabsus.getWsAgtClfLmr(),
                        toHostString(dclgfvtagth.getFecUltAct()),
                        toHostString(dclgfvtagth.getAgtRazSoc()),
                        toHostString(dclgfvtagth.getNalCve()),
                        wsVariabsus.getWsPefCve(),
                        toHostString(dclgfvtagth.getTcaCve()),
                        wsVariabsus.getWsMteCve(),
                        wsVariabsus.getWsAgtNumFolInv(),
                        toHostString(dclgfvtagth.getAgtNomExi()),
                        toHostString(dclgfvtagth.getAgtRstInv()),
                        toHostString(dclgfvtagth.getAgtFecIniInv()),
                        toHostString(dclgfvtagth.getAgtFecFinInv()),
                        toHostString(dclgfvtagth.getAgtNomDphInv()),
                        toHostString(dclgfvtagth.getAgtFecMotEss()),
                        toHostString(dclgfvtagth.getAgtFecMotAsgIn()),
                        toHostString(dclgfvtagth.getAgtObs()),
                        toHostString(wsVariabsus.getWsEssRek())))
                    .build());
                display("ENTRO 21000-PROCSAR-AGTH ", sqlca.getSqlcode(), wsVariabsus.getWsAgtIdr());
                p99920ChkSqlcode();
                p13000LeerAgth();
        }
    }

    /**
     *  Paragraph: 22000-PROCSAR-CNAH.
     *
     */
    void p22000ProcsarCnah() {
        while (!(Functions.compare(wsVariables.getSwEofcnah(), wsConstants.getWcUno()) == 0)) {
                wsVariables.setWsConcnah(wsVariables.getWsConcnah() + 1);
                wsVariabsus.initialize();
                wsVariabsus.setWsAgtIdr(regArchcnah.getAgtIdr2().toInt());

                wsVariabsus.setWsAgtFecCamRazSo(regArchcnah.getAgtFecCamRazSo2().toStringValue());

                dclgfvtcnah.setEmpCve(regArchcnah.getEmpCve2());
                wsVariabsus.setWsCnaNum(regArchcnah.getCnaNum2().toInt());

                dclgfvtcnah.setCnaTpoCnr(regArchcnah.getCnaTpoCnr2());
                dclgfvtcnah.setCnaUltCia(regArchcnah.getCnaUltCia2());
                dclgfvtcnah.setCnaFecCnx(regArchcnah.getCnaFecCnx2());
                dclgfvtcnah.setCnaCodIpu(regArchcnah.getCnaCodIpu2());
                wsVariabsus.getRsPjeIva3().setValue(regArchcnah.getCnaPjeIva23());
                wsVariabsus.getRsPjeIva2().setValue(regArchcnah.getCnaPjeIva22());
                wsVariabsus.setWsCnaPjeIva(wsVariabsus.getWsPjeIva());
                wsVariabsus.getRsPjeIsr3().setValue(regArchcnah.getCnaPjeIsr23());
                wsVariabsus.getRsPjeIsr2().setValue(regArchcnah.getCnaPjeIsr22());
                wsVariabsus.setWsCnaPjeIsr(wsVariabsus.getWsPjeIsr());
                dclgfvtcnah.setCnaComNiv(regArchcnah.getCnaComNiv2());
                wsVariabsus.getRsComNiv11().setValue(regArchcnah.getCnaImpComNiv211());
                wsVariabsus.getRsComNiv2().setValue(regArchcnah.getCnaImpComNiv22());
                wsVariabsus.setWsCnaImpComNiv(wsVariabsus.getWsComNiv());
                dclgfvtcnah.setCnaNumFza(regArchcnah.getCnaNumFza2());
                dclgfvtcnah.setCnaFecIniFza(regArchcnah.getCnaFecIniFza2());
                dclgfvtcnah.setCnaFecFinFza(regArchcnah.getCnaFecFinFza2());
                wsVariabsus.getRsImpFza11().setValue(regArchcnah.getCnaImpFza211());
                wsVariabsus.getRsImpFza2().setValue(regArchcnah.getCnaImpFza22());
                wsVariabsus.setWsCnaImpFza(wsVariabsus.getWsImpFza());
                dclgfvtcnah.setCnaIdrFav(regArchcnah.getCnaIdrFav2());
                wsVariabsus.getRsPjeFav3().setValue(regArchcnah.getCnaPjeFav23());
                wsVariabsus.getRsPjeFav4().setValue(regArchcnah.getCnaPjeFav24());
                wsVariabsus.setWsCnaPjeFav(wsVariabsus.getWsPjeFav());
                wsVariabsus.setWsPpgCve(regArchcnah.getPpgCve2().toInt());

                wsVariabsus.setWsCpgCve(regArchcnah.getCpgCve2().toInt());

                wsVariabsus.setWsCdeCve(regArchcnah.getCdeCve2().toInt());

                wsVariabsus.setWsOfnCve(regArchcnah.getOfnCve2().toInt());

                dclgfvtcnah.setTaaCve(regArchcnah.getTaaCve2());
                dclgfvtcnah.setGnaCveAgt(regArchcnah.getGnaCveAgt2());
                wsVariabsus.setWsGnaCve(regArchcnah.getGnaCve2().toInt());

                wsVariabsus.setWsTfpCve(regArchcnah.getTfpCve2().toInt());

                wsVariabsus.setWsAgtIdrRcl(regArchcnah.getAgtIdrRcl2().toInt());

                dclgfvtcnah.setCtbNumCta(regArchcnah.getCtbNumCta2());
                wsVariabsus.setWsMteCve(regArchcnah.getMteCve2().toInt());

                wsVariabsus.setWsMdeCve(regArchcnah.getMdeCve2().toInt());

                wsVariabsus.setWsZecCve(regArchcnah.getZecCve2().toInt());

                dclgfvtcnah.setCnaCodPag(regArchcnah.getCnaCodPag2());
                dclgfvtcnah.setCnaFecMotEss(regArchcnah.getCnaFecMotEss2());
                wsVariabsus.setWsCnaGerZonEnv(regArchcnah.getCnaGerZonEnv2().toInt());

                wsVariabsus.setWsCnaGerZonDcm(regArchcnah.getCnaGerZonDcm2().toInt());

                dclgfvtcnah.setCnaIndGer(regArchcnah.getCnaIndGer2());
                dclgfvtcnah.setCnaFecAsgGer(regArchcnah.getCnaFecAsgGer2());
                wsVariabsus.setWsEssRek(regArchcnah.getEssRek2().toStringValue());

                gfvtcnahPort.insert(
                    GenericModel.builder().values(List.of(
                        wsVariabsus.getWsAgtIdr(),
                        toHostString(wsVariabsus.getWsAgtFecCamRazSo()),
                        toHostString(dclgfvtcnah.getEmpCve()),
                        wsVariabsus.getWsCnaNum(),
                        toHostString(dclgfvtcnah.getCnaTpoCnr()),
                        toHostString(dclgfvtcnah.getCnaUltCia()),
                        toHostString(dclgfvtcnah.getCnaFecCnx()),
                        toHostString(dclgfvtcnah.getCnaCodIpu()),
                        wsVariabsus.getWsCnaPjeIva().toBigDecimal(),
                        wsVariabsus.getWsCnaPjeIsr().toBigDecimal(),
                        toHostString(dclgfvtcnah.getCnaComNiv()),
                        wsVariabsus.getWsCnaImpComNiv().toBigDecimal(),
                        toHostString(dclgfvtcnah.getCnaNumFza()),
                        toHostString(dclgfvtcnah.getCnaFecIniFza()),
                        toHostString(dclgfvtcnah.getCnaFecFinFza()),
                        wsVariabsus.getWsCnaImpFza().toBigDecimal(),
                        toHostString(dclgfvtcnah.getCnaIdrFav()),
                        wsVariabsus.getWsCnaPjeFav().toBigDecimal(),
                        wsVariabsus.getWsPpgCve(),
                        wsVariabsus.getWsCpgCve(),
                        wsVariabsus.getWsCdeCve(),
                        wsVariabsus.getWsOfnCve(),
                        toHostString(dclgfvtcnah.getTaaCve()),
                        toHostString(dclgfvtcnah.getGnaCveAgt()),
                        wsVariabsus.getWsGnaCve(),
                        wsVariabsus.getWsTfpCve(),
                        wsVariabsus.getWsAgtIdrRcl(),
                        toHostString(dclgfvtcnah.getCtbNumCta()),
                        wsVariabsus.getWsMteCve(),
                        wsVariabsus.getWsMdeCve(),
                        wsVariabsus.getWsZecCve(),
                        toHostString(dclgfvtcnah.getCnaCodPag()),
                        toHostString(dclgfvtcnah.getCnaFecMotEss()),
                        wsVariabsus.getWsCnaGerZonEnv(),
                        wsVariabsus.getWsCnaGerZonDcm(),
                        toHostString(dclgfvtcnah.getCnaIndGer()),
                        toHostString(dclgfvtcnah.getCnaFecAsgGer()),
                        toHostString(wsVariabsus.getWsEssRek())))
                    .build());
                display("ENTRO 21000-PROCSAR-CNAH ", sqlca.getSqlcode(), wsVariabsus.getWsAgtIdr());
                p99920ChkSqlcode();
                p14000LeerCnah();
        }
    }

    /**
     *  Paragraph: 23000-PROCSAR-DMAH.
     *
     */
    void p23000ProcsarDmah() {
        while (!(Functions.compare(wsVariables.getSwEofdmah(), wsConstants.getWcUno()) == 0)) {
                wsVariables.setWsCondmah(wsVariables.getWsCondmah() + 1);
                wsVariabsus.initialize();
                wsVariabsus.setWsAgtIdr(regArchdmah.getAgtIdr3().toInt());

                wsVariabsus.setWsAgtFecCamRazSo(regArchdmah.getAgtFecCamRazSo3().toStringValue());

                wsVariabsus.setWsDmaIdr(regArchdmah.getDmaIdr3().toInt());

                dclgfvtdmah.setDmaCae(regArchdmah.getDmaCae3());
                dclgfvtdmah.setDmaNum(regArchdmah.getDmaNum3());
                dclgfvtdmah.setDmaInt(regArchdmah.getDmaInt3());
                dclgfvtdmah.setDmaCol(regArchdmah.getDmaCol3());
                wsVariabsus.setWsTdmCve(regArchdmah.getTdmCve3().toInt());

                wsVariabsus.setWsPobCve(regArchdmah.getPobCve3().toInt());

                wsVariabsus.setWsCpoCve(regArchdmah.getCpoCve3().toInt());

                wsVariabsus.setWsMndCve(regArchdmah.getMndCve3().toInt());

                dclgfvtdmah.setDmaDirExt(regArchdmah.getDmaDirExt3());
                wsVariabsus.setWsEssRek(regArchdmah.getEssRek3().toStringValue());

                gfvtdmahPort.insert(
                    GenericModel.builder().values(List.of(
                        wsVariabsus.getWsAgtIdr(),
                        toHostString(wsVariabsus.getWsAgtFecCamRazSo()),
                        wsVariabsus.getWsDmaIdr(),
                        toHostString(dclgfvtdmah.getDmaCae()),
                        toHostString(dclgfvtdmah.getDmaNum()),
                        toHostString(dclgfvtdmah.getDmaInt()),
                        toHostString(dclgfvtdmah.getDmaCol()),
                        wsVariabsus.getWsTdmCve(),
                        wsVariabsus.getWsPobCve(),
                        wsVariabsus.getWsCpoCve(),
                        wsVariabsus.getWsMndCve(),
                        toHostString(dclgfvtdmah.getDmaDirExt()),
                        toHostString(wsVariabsus.getWsEssRek())))
                    .build());
                display("ENTRO 21000-PROCSAR-DMAH ", sqlca.getSqlcode(), wsVariabsus.getWsAgtIdr());
                p99920ChkSqlcode();
                p15000LeerDmah();
        }
    }

    /**
     *  Paragraph: 24000-PROCSAR-CMAH.
     *
     */
    void p24000ProcsarCmah() {
        while (!(Functions.compare(wsVariables.getSwEofcmah(), wsConstants.getWcUno()) == 0)) {
                wsVariables.setWsConcmah(wsVariables.getWsConcmah() + 1);
                wsVariabsus.initialize();
                wsVariabsus.setWsCcmCve(regArchcmah.getCcmCve4().toInt());

                wsVariabsus.setWsAgtIdr(regArchcmah.getAgtIdr4().toInt());

                wsVariabsus.setWsAgtFecCamRazSo(regArchcmah.getAgtFecCamRazSo4().toStringValue());

                wsVariabsus.setWsDmaIdr(regArchcmah.getDmaIdr4().toInt());

                dclgfvtcmah.setCmaDes(regArchcmah.getCmaDes4());
                wsVariabsus.setWsEssRek(regArchcmah.getEssRek4().toStringValue());

                gfvtcmahPort.insert(
                    GenericModel.builder().values(List.of(
                        wsVariabsus.getWsCcmCve(),
                        wsVariabsus.getWsAgtIdr(),
                        toHostString(wsVariabsus.getWsAgtFecCamRazSo()),
                        wsVariabsus.getWsDmaIdr(),
                        toHostString(dclgfvtcmah.getCmaDes()),
                        toHostString(wsVariabsus.getWsEssRek())))
                    .build());
                display("ENTRO 21000-PROCSAR-CMAH ", sqlca.getSqlcode(), wsVariabsus.getWsAgtIdr());
                p99920ChkSqlcode();
                p16000LeerCmah();
        }
    }

    /**
     *  Paragraph: 25000-PROCSAR-DRC0.
     *
     */
    void p25000ProcsarDrc0() {
        display("ENTRO A DRC0");
        while (!(Functions.compare(wsVariables.getSwEofdrc0(), wsConstants.getWcUno()) == 0)) {
                wsVariables.setWsCondrc0(wsVariables.getWsCondrc0() + 1);
                wsVariabsus.initialize();
                wsVariabsus.setWsDraCve(regArchdrc0.getDraCve5().toInt());

                wsVariabsus.setWsAgtIdr(regArchdrc0.getAgtIdr5().toInt());

                dclgfvtdrc0.setEmpCve(regArchdrc0.getEmpCve5());
                wsVariabsus.setWsCnaNum(regArchdrc0.getCnaNum5().toInt());

                dclgfvtdrc0.setDrcNomAut(regArchdrc0.getDrcNomAut5());
                dclgfvtdrc0.setDrcFecAut(regArchdrc0.getDrcFecAut5());
                dclgfvtdrc0.setDrcFecIniCio(regArchdrc0.getDrcFecIniCio5());
                dclgfvtdrc0.setDrcFecFinCio(regArchdrc0.getDrcFecFinCio5());
                wsVariabsus.getRsDrcPjeCom53().setValue(regArchdrc0.getDrcPjeCom53());
                wsVariabsus.getRsDrcPjeCom57().setValue(regArchdrc0.getDrcPjeCom57());
                wsVariabsus.setWsDrcPjeCom(wsVariabsus.getWsDrcPjeCom5());
                dclgfvtdrc0.setDrcIdrAgt(regArchdrc0.getDrcIdrAgt5());
                dclgfvtdrc0.setDrcIdrGte(regArchdrc0.getDrcIdrGte5());
                display("ANT DLT        ", wsVariables.getWsAgtidraux(), " ", wsVariabsus.getWsAgtIdr(), " ", dclgfvtdrc0.getEmpCve(), " ", wsVariabsus.getWsCnaNum(), " ", wsVariables.getWsAgtidraux(), " ", wsVariables.getWsCnanumaux(), " ", wsVariabsus.getWsDraCve());
                if (regArchdrc0.getAgtIdr5().toInt() != wsVariables.getWsAgtidraux() 
                    || regArchdrc0.getCnaNum5().toInt() != wsVariables.getWsCnanumaux()) {
                    gfvtdrc0Port.delete(
                        Gfvtdrc0Model.builder()
                            .agtIdr(wsVariabsus.getWsAgtIdr())
                            .empCve(toHostString(dclgfvtdrc0.getEmpCve()))
                            .cnaNum(wsVariabsus.getWsCnaNum())
                        .build());
                    wsVariabsus.getWsSqlcode().setValue(sqlca.getSqlcode());
                    display("DNTRO DLT DRC0 ", wsVariabsus.getWsSqlcode(), " ", wsVariabsus.getWsAgtIdr(), " ", dclgfvtdrc0.getEmpCve(), " ", wsVariabsus.getWsCnaNum(), " ", wsVariables.getWsAgtidraux(), " ", wsVariables.getWsCnanumaux());
                    wsVariables.setWsAgtidraux(regArchdrc0.getAgtIdr5().toInt());

                    wsVariables.setWsCnanumaux(regArchdrc0.getCnaNum5().toInt());

                }
                gfvtdrc0Port.insert(
                    GenericModel.builder().values(List.of(
                        wsVariabsus.getWsDraCve(),
                        wsVariabsus.getWsAgtIdr(),
                        toHostString(dclgfvtdrc0.getEmpCve()),
                        wsVariabsus.getWsCnaNum(),
                        toHostString(dclgfvtdrc0.getDrcNomAut()),
                        toHostString(dclgfvtdrc0.getDrcFecAut()),
                        toHostString(dclgfvtdrc0.getDrcFecIniCio()),
                        toHostString(dclgfvtdrc0.getDrcFecFinCio()),
                        wsVariabsus.getWsDrcPjeCom().toBigDecimal(),
                        toHostString(dclgfvtdrc0.getDrcIdrAgt()),
                        toHostString(dclgfvtdrc0.getDrcIdrGte())))
                    .build());
                display("DNTR INCR DRC0 ", wsVariabsus.getWsSqlcode(), " ", wsVariabsus.getWsAgtIdr(), " ", dclgfvtdrc0.getEmpCve(), " ", wsVariabsus.getWsCnaNum(), " ", wsVariables.getWsAgtidraux(), " ", wsVariables.getWsCnanumaux(), " ", wsVariabsus.getWsDraCve());
                p99920ChkSqlcode();
                p17000LeerDrc0();
        }
    }

    /**
     *  Paragraph: 26000-PROCSAR-INTR.
     *
     */
    void p26000ProcsarIntr() {
        gfvtagthCursor.open();
        p99920ChkSqlcode();
        display("ENTRO 26000-PROCSAR-INTR ", sqlca.getSqlcode(), wsVariabsus.getWsAgtIdr());
        if (sqlca.getSqlcode() == 0) {
            while (!(sqlca.getSqlcode() == +100
                || sqlca.getSqlcode() == -100)) {
                    genericModel = gfvtagthCursor.next();

                    if (genericModel.isPresent()) {
                        wsVariabsus.setFsAgtIdr(fromHostString(genericModel.get().values().get(0)));
                        wsVariabsus.setFsAgtFecCamRazSo(fromHostString(genericModel.get().values().get(1)));
                        wsVariabsus.setFsTfpCvev(fromHostString(genericModel.get().values().get(2)));
                        wsVariabsus.setFsTfpCven(fromHostString(genericModel.get().values().get(3)));
                        wsVariabsus.setFsFlaNum(fromHostString(genericModel.get().values().get(4)));
                    }
                    display("DNTRO 26000-PROCSAR-INTR ", sqlca.getSqlcode(), wsVariabsus.getWsAgtIdr());
                    if (sqlca.getSqlcode() != 100 
                        && sqlca.getSqlcode() != -100) {
                        wsVariables.setWsConintr(wsVariables.getWsConintr() + 1);
                        regArchintr.setSalAgtIdr(wsVariabsus.getFsAgtIdr());
                        regArchintr.setSalAgtFecCamRazSo(wsVariabsus.getFsAgtFecCamRazSo());
                        regArchintr.setSalTfpCvev(wsVariabsus.getFsTfpCvev());
                        regArchintr.setSalTfpCven(wsVariabsus.getFsTfpCven());
                        regArchintr.setSalFlaNum(wsVariabsus.getFsFlaNum());
                        archintr.write(regArchintr);
                    } else if (wsVariables.getWsConintr() == 0) {
                        regArchintr.setSalAgtIdr("REG. ");
                        regArchintr.setSalTfpCvev("VAC");
                        regArchintr.setSalTfpCven("IO ");
                        regArchintr.setSalAgtFecCamRazSo("PARA NASA");
                        archintr.write(regArchintr);
                    }
            }
            if (gfvtagthCursor != null) {
                gfvtagthCursor.close();
            }
            gfvtcnahPort.update(
                null);
        }
    }

    /**
     *  Paragraph: 30000-TERMINA.
     *
     */
    void p30000Termina() {
        getSqlExecutor().commit(sqlca);
        abReg.getAbMensaje().at(2).setValue("NO SE GRABO RGISTROS EN HISTORICOS");
        archagth.close();
        if (!wsConstants.getFsAgth().equals("00")) {
            abReg.getAbMensaje().at(1).setValue("ERROR CERRAR ARCHIVO ARCHAGTH");
            p99910ChkAbnd();
        }
        archcnah.close();
        if (!wsConstants.getFsCnah().equals("00")) {
            abReg.getAbMensaje().at(1).setValue("ERROR CERRAR ARCHIVO ARCHCNAH");
            p99910ChkAbnd();
        }
        archdmah.close();
        if (!wsConstants.getFsDmah().equals("00")) {
            abReg.getAbMensaje().at(1).setValue("ERROR CERRAR ARCHIVO ARCHDMAH");
            p99910ChkAbnd();
        }
        archcmah.close();
        if (!wsConstants.getFsCmah().equals("00")) {
            abReg.getAbMensaje().at(1).setValue("ERROR CERRAR ARCHIVO ARCHCMAH");
            p99910ChkAbnd();
        }
        archintr.close();
        if (!wsConstants.getFsIntr().equals("00")) {
            abReg.getAbMensaje().at(1).setValue("ERROR CERRAR ARCHIVO ARCHCMAH");
            p99910ChkAbnd();
        }
    }

    /**
     *  Paragraph: 99910-CHK-ABND.
     *
     */
    void p99910ChkAbnd() {
        display("+ =======================================+");
        display("+ ==         AREA  DE  ABORTA          ==+");
        display("+ =======================================+");
        display(" PROGRAMA   :   GGNFOZ00");
        display(" PROCESO    :   PGANFDFJ");
        display(" CODIGO     : ", abReg.getAbCodigo());
        display(" MENSAJE(1) : ", abReg.getAbMensaje().at(1));
        display(" MENSAJE(2) : ", abReg.getAbMensaje().at(2));
        display(" MENSAJE(3) : ", abReg.getAbMensaje().at(3));
        display("+ =======================================+");
        display("+ ==      FIN  DE  AREA  DE  ABORTA    ==+");
        display("+ =======================================+");
        returnCode.setValue(16);
        getSqlExecutor().rollback(sqlca);
        throw new StopRunException();
        /* [@WARNING Unreachable code]
        */
    }

    /**
     *  Paragraph: 99920-CHK-SQLCODE.
     *
     */
    void p99920ChkSqlcode() {
        db2ReturnCode = sqlca.getSqlcode();

        if (this.isDb2Ok()) {
            ;
        } else {
            abReg.getAbMensaje().at(2).setValue("ERROR DE DB2 ");
            abReg.getAbMensaje().at(3).setValue(lzPad(sqlca.getSqlcode(), 9));
            p99910ChkAbnd();
        }
    }

    // Conditionals (88) - DB2-RETURN-CODE
    public boolean isDb2Ok() {
        return this.db2ReturnCode == DB2_OK;
    }

    public void setDb2Ok() {
        this.db2ReturnCode = DB2_OK;
    }

    public boolean isDb2Notfnd() {
        return this.db2ReturnCode == DB2_NOTFND;
    }

    public void setDb2Notfnd() {
        this.db2ReturnCode = DB2_NOTFND;
    }

    public boolean isDb2Duprec() {
        return this.db2ReturnCode == DB2_DUPREC;
    }

    public void setDb2Duprec() {
        this.db2ReturnCode = DB2_DUPREC;
    }

    public boolean isDb2Notuni() {
        return this.db2ReturnCode == DB2_NOTUNI;
    }

    public void setDb2Notuni() {
        this.db2ReturnCode = DB2_NOTUNI;
    }

    public boolean isDb2Datetime() {
        return this.db2ReturnCode == DB2_DATETIME;
    }

    public void setDb2Datetime() {
        this.db2ReturnCode = DB2_DATETIME;
    }

    public boolean isDb2System() {
        return this.db2ReturnCode == DB2_SYSTEM;
    }

    public void setDb2System() {
        this.db2ReturnCode = DB2_SYSTEM;
    }



    @Autowired
    public void setGfvtdrc0Port(IGfvtdrc0Port gfvtdrc0Port) {
        this.gfvtdrc0Port = gfvtdrc0Port;
        this.gfvtdrc0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagthPort(IGfvtagthPort gfvtagthPort) {
        this.gfvtagthPort = gfvtagthPort;
        this.gfvtagthPort.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdmahPort(IGfvtdmahPort gfvtdmahPort) {
        this.gfvtdmahPort = gfvtdmahPort;
        this.gfvtdmahPort.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtcnahPort(IGfvtcnahPort gfvtcnahPort) {
        this.gfvtcnahPort = gfvtcnahPort;
        this.gfvtcnahPort.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtcmahPort(IGfvtcmahPort gfvtcmahPort) {
        this.gfvtcmahPort = gfvtcmahPort;
        this.gfvtcmahPort.setProgramContext(getProgramContext());
    }
}
