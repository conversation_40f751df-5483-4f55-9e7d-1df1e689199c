package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TAB-NOMBRER.
 *
 */
@Setter
@Getter
public class WsTabNombrer extends DataStruct {
    // Properties
    private AlphanumericVarArray eleNombre = new AlphanumericVarArray(this, 40, 1);          // 05 ELE-NOMBRE X [40]

    public WsTabNombrer() {
        super();
        initialize();
    }

    public WsTabNombrer(DataContainer parent) {
        super(parent);
    }

    public WsTabNombrer(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getEleNombre(int index) {
        return this.eleNombre.at(index);
    }

    public void setEleNombre(final String eleNombre) {
        this.eleNombre.setValue(eleNombre);
    }

    public void setEleNombre(final AlphanumericVarArray eleNombre) {
        this.eleNombre.setValue(eleNombre);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.eleNombre.clear();
    }

}

