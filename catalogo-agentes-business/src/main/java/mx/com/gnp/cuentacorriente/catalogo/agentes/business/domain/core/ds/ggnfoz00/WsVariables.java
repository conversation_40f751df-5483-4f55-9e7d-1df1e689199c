package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-VARIABLES.
 *
 */
// 667      ***                                                               
@Setter
@Getter
public class WsVariables { // VStruct
    // Properties
    private int swEofagth;                                                  // 05 SW-EOFAGTH 9(01)
    private int swEofcnah;                                                  // 05 SW-EOFCNAH 9(01)
    private int swEofdmah;                                                  // 05 SW-EOFDMAH 9(01)
    private int swEofcmah;                                                  // 05 SW-EOFCMAH 9(01)
    private int swEofdrc0;                                                  // 05 SW-EOFDRC0 9(01)
    private int swEofintr;                                                  // 05 SW-EOFINTR 9(01)
    private int wsConagth;                                                  // 05 WS-CONAGTH 9(09)
    private int wsConcnah;                                                  // 05 WS-CONCNAH 9(09)
    private int wsCondmah;                                                  // 05 WS-CONDMAH 9(09)
    private int wsConcmah;                                                  // 05 WS-CONCMAH 9(09)
    private int wsCondrc0;                                                  // 05 WS-CONDRC0 9(09)
    private int wsConintr;                                                  // 05 WS-CONINTR 9(09)
    private int wsAgtidraux;                                                // 05 WS-AGTIDRAUX 9(9)
    private int wsCnanumaux;                                                // 05 WS-CNANUMAUX 9(5)

    public WsVariables() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.swEofagth = 0;
        this.swEofcnah = 0;
        this.swEofdmah = 0;
        this.swEofcmah = 0;
        this.swEofdrc0 = 0;
        this.swEofintr = 0;
        this.wsConagth = 0;
        this.wsConcnah = 0;
        this.wsCondmah = 0;
        this.wsConcmah = 0;
        this.wsCondrc0 = 0;
        this.wsConintr = 0;
        this.wsAgtidraux = 0;
        this.wsCnanumaux = 0;
    }

}

