package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PK-NUM-CEDULAR.
 *
 */
@Setter
@Getter
public class PkNumCedular extends DataStruct {
    // Properties
    private AlphanumericVar pkNumCedulaDos = new AlphanumericVar(this, 2);   // 07 PK-NUM-CEDULA-DOS XX
    private AlphanumericVar pkNumCedulaR18 = new AlphanumericVar(this, 18);  // 07 PK-NUM-CEDULA-R18 X(18)

    public PkNumCedular() {
        super();
        initialize();
    }

    public PkNumCedular(DataContainer parent) {
        super(parent);
    }

    public PkNumCedular(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkNumCedulaDos(final String pkNumCedulaDos) {
        this.pkNumCedulaDos.setValue(pkNumCedulaDos);
    }

    public void setPkNumCedulaDos(final IAlphanumericValue pkNumCedulaDos) {
        this.pkNumCedulaDos.setValue(pkNumCedulaDos);
    }

    public void setPkNumCedulaR18(final String pkNumCedulaR18) {
        this.pkNumCedulaR18.setValue(pkNumCedulaR18);
    }

    public void setPkNumCedulaR18(final IAlphanumericValue pkNumCedulaR18) {
        this.pkNumCedulaR18.setValue(pkNumCedulaR18);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkNumCedulaDos.clear();
        this.pkNumCedulaR18.clear();
    }

}

