package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablacna0.TCna0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablacna0.tcna0.TCna0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-CNA0.
 *
 */
// 214      *==                     TOTAL  ===  TOTAL XXXX ==                 
@Setter
@Getter
public class TablaCna0 { // VStruct
    // Properties
    private TCna0 tCna0 = new  TCna0(85);                                    // 10 T-CNA0 [1]

    public TablaCna0() {
        initialize();
    }



    public TCna0 getTCna0(int index) {
        return this.tCna0.at(index);
    }


    // Deeper properties

    // Property: tCna0.tCna0Elem -> 15 T-CNA0-ELEM UsedAsParameter
    public TCna0Elem getTCna0Elem() {
        return this.tCna0.getTCna0Elem();
    }

    // Property: tCna0.tCna0Elem.sCnaCnaNum -> 20 S-CNA-CNA-NUM X(02)
    public AlphanumericVar getSCnaCnaNum() {
        return this.tCna0.getTCna0Elem().getSCnaCnaNum();
    }

    public void setSCnaCnaNum(final String sCnaCnaNum) {
        this.tCna0.getTCna0Elem().setSCnaCnaNum(sCnaCnaNum);
    }

    public void setSCnaCnaNum(final IAlphanumericValue sCnaCnaNum) {
        this.tCna0.getTCna0Elem().setSCnaCnaNum(sCnaCnaNum);
    }

    // Property: tCna0.tCna0Elem.sCnaLimitad -> 20 S-CNA-LIMITAD X(01)
    public AlphanumericVar getSCnaLimitad() {
        return this.tCna0.getTCna0Elem().getSCnaLimitad();
    }

    public void setSCnaLimitad(final String sCnaLimitad) {
        this.tCna0.getTCna0Elem().setSCnaLimitad(sCnaLimitad);
    }

    public void setSCnaLimitad(final IAlphanumericValue sCnaLimitad) {
        this.tCna0.getTCna0Elem().setSCnaLimitad(sCnaLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tCna0.initialize();
    }

}

