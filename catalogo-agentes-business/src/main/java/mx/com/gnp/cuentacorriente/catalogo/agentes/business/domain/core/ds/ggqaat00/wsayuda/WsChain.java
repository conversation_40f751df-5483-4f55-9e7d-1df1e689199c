package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CHAIN.
 *
 */
@Setter
@Getter
public class WsChain extends DataStruct {
    // Properties
    private AlphanumericVar wsChain1 = new AlphanumericVar(this, 5);         // 10 WS-CHAIN1 X(05)
    private AlphanumericVar wsChain2 = new AlphanumericVar(this, 5);         // 10 WS-CHAIN2 X(05)

    public WsChain() {
        super();
        initialize();
    }

    public WsChain(DataContainer parent) {
        super(parent);
    }

    public WsChain(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsChain1(final String wsChain1) {
        this.wsChain1.setValue(wsChain1);
    }

    public void setWsChain1(final IAlphanumericValue wsChain1) {
        this.wsChain1.setValue(wsChain1);
    }

    public void setWsChain2(final String wsChain2) {
        this.wsChain2.setValue(wsChain2);
    }

    public void setWsChain2(final IAlphanumericValue wsChain2) {
        this.wsChain2.setValue(wsChain2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsChain1.clear();
        this.wsChain2.clear();
    }

}

