package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-INDICADORES.
 *
 */
// 356056900*                                                                 05690000
// 357057000*                                                                 05700000
@Setter
@Getter
public class WsIndicadores { // VStruct
    // Properties
    private int wsAgtIdr;                                                   // 03 WS-AGT-IDR S9(9) COMPUTATIONAL
    private String wsFlaNum = "";                                           // 03 WS-FLA-NUM X(8)
    private int wsMesAux = 0;                                               // 03 WS-MES-AUX 99
    private int wsFinFcn = 0;                                               // 03 WS-FIN-FCN 9
    private int wsFinFdc = 0;                                               // 03 WS-FIN-FDC 9
    private int wsFinAgt = 0;                                               // 03 WS-FIN-AGT 9
    private int wsFinCna = 0;                                               // 03 WS-FIN-CNA 9
    private int wsFinFla = 0;                                               // 03 WS-FIN-FLA 9
    private int wsFinMte = 0;                                               // 03 WS-FIN-MTE 9
    private int wsFinCaf = 0;                                               // 03 WS-FIN-CAF 9
    private int i = 0;                                                      // 03 I 999
    private int j = 0;                                                      // 03 J 999
    private int contador = 0;                                               // 03 CONTADOR 99999
    private int cuantos = 0;                                                // 03 CUANTOS 99999
    private int contLineas = 0;                                             // 03 CONT-LINEAS 999
    private int contLineas1 = 0;                                            // 03 CONT-LINEAS1 999
    private int contHojas = 0;                                              // 03 CONT-HOJAS 999
    private int contHojas1 = 0;                                             // 03 CONT-HOJAS1 999

    public WsIndicadores() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsAgtIdr = 0;
        this.wsFlaNum = "";
        this.wsMesAux = 0;
        this.wsFinFcn = 0;
        this.wsFinFdc = 0;
        this.wsFinAgt = 0;
        this.wsFinCna = 0;
        this.wsFinFla = 0;
        this.wsFinMte = 0;
        this.wsFinCaf = 0;
        this.i = 0;
        this.j = 0;
        this.contador = 0;
        this.cuantos = 0;
        this.contLineas = 0;
        this.contLineas1 = 0;
        this.contHojas = 0;
        this.contHojas1 = 0;
    }

}

