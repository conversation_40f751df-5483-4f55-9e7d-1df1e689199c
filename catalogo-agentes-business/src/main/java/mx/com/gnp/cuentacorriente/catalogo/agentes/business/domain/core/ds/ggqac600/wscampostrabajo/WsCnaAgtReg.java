package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CNA-AGT-REG.
 *
 */
@Setter
@Getter
public class WsCnaAgtReg extends DataStruct {
    // Properties
    private BinaryVar wsCnaOfnCve = new BinaryVar(this, 4, 0);               // 10 WS-CNA-OFN-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsCnaTfpCve = new BinaryVar(this, 4, 0);               // 10 WS-CNA-TFP-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsCnaMteCve = new BinaryVar(this, 4, 0);               // 10 WS-CNA-MTE-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsCnaGerZonEnv = new BinaryVar(this, 4, 0);            // 10 WS-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    private AlphanumericVar wsCnaCtbNumCta = new AlphanumericVar(this, 18);  // 10 WS-CNA-CTB-NUM-CTA X(18)
    private PackedVar wsCnaPjeIva = new PackedVar(this, 5, 2);               // 10 WS-CNA-PJE-IVA S9(3)V9(2) COMPUTATIONAL-3
    private PackedVar wsCnaPjeIsr = new PackedVar(this, 5, 2);               // 10 WS-CNA-PJE-ISR S9(3)V9(2) COMPUTATIONAL-3
    private BinaryVar wsCnaDsgCve = new BinaryVar(this, 4, 0);               // 10 WS-CNA-DSG-CVE S9(04) COMPUTATIONAL

    public WsCnaAgtReg() {
        super();
        initialize();
    }

    public WsCnaAgtReg(DataContainer parent) {
        super(parent);
    }

    public WsCnaAgtReg(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsCnaOfnCve(final int wsCnaOfnCve) {
        this.wsCnaOfnCve.setValue(wsCnaOfnCve);
    }

    public void setWsCnaOfnCve(final BigDecimal wsCnaOfnCve) {
        this.wsCnaOfnCve.setValue(wsCnaOfnCve);
    }

    public void setWsCnaOfnCve(final INumericValue wsCnaOfnCve) {
        this.wsCnaOfnCve.setValue(wsCnaOfnCve);
    }

    public void setWsCnaTfpCve(final int wsCnaTfpCve) {
        this.wsCnaTfpCve.setValue(wsCnaTfpCve);
    }

    public void setWsCnaTfpCve(final BigDecimal wsCnaTfpCve) {
        this.wsCnaTfpCve.setValue(wsCnaTfpCve);
    }

    public void setWsCnaTfpCve(final INumericValue wsCnaTfpCve) {
        this.wsCnaTfpCve.setValue(wsCnaTfpCve);
    }

    public void setWsCnaMteCve(final int wsCnaMteCve) {
        this.wsCnaMteCve.setValue(wsCnaMteCve);
    }

    public void setWsCnaMteCve(final BigDecimal wsCnaMteCve) {
        this.wsCnaMteCve.setValue(wsCnaMteCve);
    }

    public void setWsCnaMteCve(final INumericValue wsCnaMteCve) {
        this.wsCnaMteCve.setValue(wsCnaMteCve);
    }

    public void setWsCnaGerZonEnv(final int wsCnaGerZonEnv) {
        this.wsCnaGerZonEnv.setValue(wsCnaGerZonEnv);
    }

    public void setWsCnaGerZonEnv(final BigDecimal wsCnaGerZonEnv) {
        this.wsCnaGerZonEnv.setValue(wsCnaGerZonEnv);
    }

    public void setWsCnaGerZonEnv(final INumericValue wsCnaGerZonEnv) {
        this.wsCnaGerZonEnv.setValue(wsCnaGerZonEnv);
    }

    public void setWsCnaCtbNumCta(final String wsCnaCtbNumCta) {
        this.wsCnaCtbNumCta.setValue(wsCnaCtbNumCta);
    }

    public void setWsCnaCtbNumCta(final IAlphanumericValue wsCnaCtbNumCta) {
        this.wsCnaCtbNumCta.setValue(wsCnaCtbNumCta);
    }

    public void setWsCnaPjeIva(final BigDecimal wsCnaPjeIva) {
        this.wsCnaPjeIva.setValue(wsCnaPjeIva);
    }

    public void setWsCnaPjeIva(final int wsCnaPjeIva) {
        this.wsCnaPjeIva.setValue(wsCnaPjeIva);
    }

    public void setWsCnaPjeIva(final INumericValue wsCnaPjeIva) {
        this.wsCnaPjeIva.setValue(wsCnaPjeIva);
    }

    public void setWsCnaPjeIsr(final BigDecimal wsCnaPjeIsr) {
        this.wsCnaPjeIsr.setValue(wsCnaPjeIsr);
    }

    public void setWsCnaPjeIsr(final int wsCnaPjeIsr) {
        this.wsCnaPjeIsr.setValue(wsCnaPjeIsr);
    }

    public void setWsCnaPjeIsr(final INumericValue wsCnaPjeIsr) {
        this.wsCnaPjeIsr.setValue(wsCnaPjeIsr);
    }

    public void setWsCnaDsgCve(final int wsCnaDsgCve) {
        this.wsCnaDsgCve.setValue(wsCnaDsgCve);
    }

    public void setWsCnaDsgCve(final BigDecimal wsCnaDsgCve) {
        this.wsCnaDsgCve.setValue(wsCnaDsgCve);
    }

    public void setWsCnaDsgCve(final INumericValue wsCnaDsgCve) {
        this.wsCnaDsgCve.setValue(wsCnaDsgCve);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsCnaOfnCve.clear();
        this.wsCnaTfpCve.clear();
        this.wsCnaMteCve.clear();
        this.wsCnaGerZonEnv.clear();
        this.wsCnaCtbNumCta.clear();
        this.wsCnaPjeIva.clear();
        this.wsCnaPjeIsr.clear();
        this.wsCnaDsgCve.clear();
    }

}

