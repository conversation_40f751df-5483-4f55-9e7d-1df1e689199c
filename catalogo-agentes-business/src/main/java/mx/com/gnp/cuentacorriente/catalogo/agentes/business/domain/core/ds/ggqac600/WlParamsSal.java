package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WL-PARAMS-SAL.
 *
 */
@Setter
@Getter
public class WlParamsSal extends DataStruct {
    // Properties
    private AlphanumericVar wlRegfis = new AlphanumericVar(this, 3);         // 05 WL-REGFIS X(03)
    private AlphanumericVar wlNombre = new AlphanumericVar(this, 300);       // 05 WL-NOMBRE X(300)
    private AlphanumericVar wlPcenvi = new AlphanumericVar(this, 5);         // 05 WL-PCENVI X(05)
    private AlphanumericVar wlPcevid = new AlphanumericVar(this, 5);         // 05 WL-PCEVID X(05)

    public WlParamsSal() {
        super();
        initialize();
    }

    public WlParamsSal(DataContainer parent) {
        super(parent);
    }

    public WlParamsSal(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWlRegfis(final String wlRegfis) {
        this.wlRegfis.setValue(wlRegfis);
    }

    public void setWlRegfis(final IAlphanumericValue wlRegfis) {
        this.wlRegfis.setValue(wlRegfis);
    }

    public void setWlNombre(final String wlNombre) {
        this.wlNombre.setValue(wlNombre);
    }

    public void setWlNombre(final IAlphanumericValue wlNombre) {
        this.wlNombre.setValue(wlNombre);
    }

    public void setWlPcenvi(final String wlPcenvi) {
        this.wlPcenvi.setValue(wlPcenvi);
    }

    public void setWlPcenvi(final IAlphanumericValue wlPcenvi) {
        this.wlPcenvi.setValue(wlPcenvi);
    }

    public void setWlPcevid(final String wlPcevid) {
        this.wlPcevid.setValue(wlPcevid);
    }

    public void setWlPcevid(final IAlphanumericValue wlPcevid) {
        this.wlPcevid.setValue(wlPcevid);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wlRegfis.clear();
        this.wlNombre.clear();
        this.wlPcenvi.clear();
        this.wlPcevid.clear();
    }

}

