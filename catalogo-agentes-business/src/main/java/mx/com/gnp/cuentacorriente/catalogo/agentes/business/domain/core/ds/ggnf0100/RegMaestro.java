package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.DatosSaeta;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkRfc;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedular;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedularr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedularrr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedulaPr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedulapRr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.DatosCocoa;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datoscocoa.PkFolioInvesr;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-MAESTRO.
 *
 */
// 74004200                                                                  00420000
@Setter
@Getter
public class RegMaestro extends DataStruct {
    // Properties
    private DatosSaeta datosSaeta = new  DatosSaeta(this);                   // 03 DATOS-SAETA
    private DatosCocoa datosCocoa = new  DatosCocoa(this);                   // 03 DATOS-COCOA

    public RegMaestro() {
        super();
        initialize();
    }

    public RegMaestro(DataContainer parent) {
        super(parent);
    }

    public RegMaestro(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }



    // Deeper properties

    // Property: datosSaeta.datosCons -> 05 DATOS-CONS 9(7)
    public UnsignedNumericVar getDatosCons() {
        return this.datosSaeta.getDatosCons();
    }

    public void setDatosCons(final int datosCons) {
        this.datosSaeta.setDatosCons(datosCons);
    }

    public void setDatosCons(final INumericValue datosCons) {
        this.datosSaeta.setDatosCons(datosCons);
    }

    // Property: datosSaeta.pkRfc -> 05 PK-RFC
    public PkRfc getPkRfc() {
        return this.datosSaeta.getPkRfc();
    }

    // Property: datosSaeta.pkRfc.pkRfc1 -> 07 PK-RFC1 X(13)
    public AlphanumericVar getPkRfc1() {
        return this.datosSaeta.getPkRfc().getPkRfc1();
    }

    public void setPkRfc1(final String pkRfc1) {
        this.datosSaeta.getPkRfc().setPkRfc1(pkRfc1);
    }

    public void setPkRfc1(final IAlphanumericValue pkRfc1) {
        this.datosSaeta.getPkRfc().setPkRfc1(pkRfc1);
    }

    // Property: datosSaeta.pkRfc.pkRfc2 -> 07 PK-RFC2 XX
    public AlphanumericVar getPkRfc2() {
        return this.datosSaeta.getPkRfc().getPkRfc2();
    }

    public void setPkRfc2(final String pkRfc2) {
        this.datosSaeta.getPkRfc().setPkRfc2(pkRfc2);
    }

    public void setPkRfc2(final IAlphanumericValue pkRfc2) {
        this.datosSaeta.getPkRfc().setPkRfc2(pkRfc2);
    }

    // Property: datosSaeta.pkClaveNal -> 05 PK-CLAVE-NAL 9(07)
    public UnsignedNumericVar getPkClaveNal() {
        return this.datosSaeta.getPkClaveNal();
    }

    public void setPkClaveNal(final int pkClaveNal) {
        this.datosSaeta.setPkClaveNal(pkClaveNal);
    }

    public void setPkClaveNal(final INumericValue pkClaveNal) {
        this.datosSaeta.setPkClaveNal(pkClaveNal);
    }

    // Property: datosSaeta.pkClaveProv -> 05 PK-CLAVE-PROV 9(07)
    public UnsignedNumericVar getPkClaveProv() {
        return this.datosSaeta.getPkClaveProv();
    }

    public void setPkClaveProv(final int pkClaveProv) {
        this.datosSaeta.setPkClaveProv(pkClaveProv);
    }

    public void setPkClaveProv(final INumericValue pkClaveProv) {
        this.datosSaeta.setPkClaveProv(pkClaveProv);
    }

    // Property: datosSaeta.pkNombre -> 05 PK-NOMBRE X(35)
    public AlphanumericVar getPkNombre() {
        return this.datosSaeta.getPkNombre();
    }

    public void setPkNombre(final String pkNombre) {
        this.datosSaeta.setPkNombre(pkNombre);
    }

    public void setPkNombre(final IAlphanumericValue pkNombre) {
        this.datosSaeta.setPkNombre(pkNombre);
    }

    // Property: datosSaeta.pkCompania -> 05 PK-COMPANIA X(01)
    public AlphanumericVar getPkCompania() {
        return this.datosSaeta.getPkCompania();
    }

    public void setPkCompania(final String pkCompania) {
        this.datosSaeta.setPkCompania(pkCompania);
    }

    public void setPkCompania(final IAlphanumericValue pkCompania) {
        this.datosSaeta.setPkCompania(pkCompania);
    }

    // Property: datosSaeta.pkFecConexion -> 05 PK-FEC-CONEXION 9(06) REDEFINED BY PK-FEC-CONEXIONR
    public UnsignedNumericVar getPkFecConexion() {
        return this.datosSaeta.getPkFecConexion();
    }

    public void setPkFecConexion(final int pkFecConexion) {
        this.datosSaeta.setPkFecConexion(pkFecConexion);
    }

    public void setPkFecConexion(final INumericValue pkFecConexion) {
        this.datosSaeta.setPkFecConexion(pkFecConexion);
    }

    // Property: datosSaeta.pkFecConexionr -> 05 PK-FEC-CONEXIONR REDEFINES PK-FEC-CONEXION X(6)
    public AlphanumericVar getPkFecConexionr() {
        return this.datosSaeta.getPkFecConexionr();
    }

    public void setPkFecConexionr(final String pkFecConexionr) {
        this.datosSaeta.setPkFecConexionr(pkFecConexionr);
    }

    public void setPkFecConexionr(final IAlphanumericValue pkFecConexionr) {
        this.datosSaeta.setPkFecConexionr(pkFecConexionr);
    }

    // Property: datosSaeta.pkCalleP -> 05 PK-CALLE-P X(35)
    public AlphanumericVar getPkCalleP() {
        return this.datosSaeta.getPkCalleP();
    }

    public void setPkCalleP(final String pkCalleP) {
        this.datosSaeta.setPkCalleP(pkCalleP);
    }

    public void setPkCalleP(final IAlphanumericValue pkCalleP) {
        this.datosSaeta.setPkCalleP(pkCalleP);
    }

    // Property: datosSaeta.pkColoniaP -> 05 PK-COLONIA-P X(20)
    public AlphanumericVar getPkColoniaP() {
        return this.datosSaeta.getPkColoniaP();
    }

    public void setPkColoniaP(final String pkColoniaP) {
        this.datosSaeta.setPkColoniaP(pkColoniaP);
    }

    public void setPkColoniaP(final IAlphanumericValue pkColoniaP) {
        this.datosSaeta.setPkColoniaP(pkColoniaP);
    }

    // Property: datosSaeta.pkCodPostP -> 05 PK-COD-POST-P 9(05)
    public UnsignedNumericVar getPkCodPostP() {
        return this.datosSaeta.getPkCodPostP();
    }

    public void setPkCodPostP(final int pkCodPostP) {
        this.datosSaeta.setPkCodPostP(pkCodPostP);
    }

    public void setPkCodPostP(final INumericValue pkCodPostP) {
        this.datosSaeta.setPkCodPostP(pkCodPostP);
    }

    // Property: datosSaeta.pkPoblP -> 05 PK-POBL-P X(25)
    public AlphanumericVar getPkPoblP() {
        return this.datosSaeta.getPkPoblP();
    }

    public void setPkPoblP(final String pkPoblP) {
        this.datosSaeta.setPkPoblP(pkPoblP);
    }

    public void setPkPoblP(final IAlphanumericValue pkPoblP) {
        this.datosSaeta.setPkPoblP(pkPoblP);
    }

    // Property: datosSaeta.pkEstadoP -> 05 PK-ESTADO-P 99
    public UnsignedNumericVar getPkEstadoP() {
        return this.datosSaeta.getPkEstadoP();
    }

    public void setPkEstadoP(final int pkEstadoP) {
        this.datosSaeta.setPkEstadoP(pkEstadoP);
    }

    public void setPkEstadoP(final INumericValue pkEstadoP) {
        this.datosSaeta.setPkEstadoP(pkEstadoP);
    }

    // Property: datosSaeta.pkPaisP -> 05 PK-PAIS-P 99
    public UnsignedNumericVar getPkPaisP() {
        return this.datosSaeta.getPkPaisP();
    }

    public void setPkPaisP(final int pkPaisP) {
        this.datosSaeta.setPkPaisP(pkPaisP);
    }

    public void setPkPaisP(final INumericValue pkPaisP) {
        this.datosSaeta.setPkPaisP(pkPaisP);
    }

    // Property: datosSaeta.pkTelefP -> 05 PK-TELEF-P 9(12) REDEFINED BY PK-TELEF-PR
    public UnsignedNumericVar getPkTelefP() {
        return this.datosSaeta.getPkTelefP();
    }

    public void setPkTelefP(final BigDecimal pkTelefP) {
        this.datosSaeta.setPkTelefP(pkTelefP);
    }

    public void setPkTelefP(final int pkTelefP) {
        this.datosSaeta.setPkTelefP(pkTelefP);
    }

    public void setPkTelefP(final INumericValue pkTelefP) {
        this.datosSaeta.setPkTelefP(pkTelefP);
    }

    // Property: datosSaeta.pkTelefPr -> 05 PK-TELEF-PR REDEFINES PK-TELEF-P X(12)
    public AlphanumericVar getPkTelefPr() {
        return this.datosSaeta.getPkTelefPr();
    }

    public void setPkTelefPr(final String pkTelefPr) {
        this.datosSaeta.setPkTelefPr(pkTelefPr);
    }

    public void setPkTelefPr(final IAlphanumericValue pkTelefPr) {
        this.datosSaeta.setPkTelefPr(pkTelefPr);
    }

    // Property: datosSaeta.pkCalleC -> 05 PK-CALLE-C X(35)
    public AlphanumericVar getPkCalleC() {
        return this.datosSaeta.getPkCalleC();
    }

    public void setPkCalleC(final String pkCalleC) {
        this.datosSaeta.setPkCalleC(pkCalleC);
    }

    public void setPkCalleC(final IAlphanumericValue pkCalleC) {
        this.datosSaeta.setPkCalleC(pkCalleC);
    }

    // Property: datosSaeta.pkColoniaC -> 05 PK-COLONIA-C X(20)
    public AlphanumericVar getPkColoniaC() {
        return this.datosSaeta.getPkColoniaC();
    }

    public void setPkColoniaC(final String pkColoniaC) {
        this.datosSaeta.setPkColoniaC(pkColoniaC);
    }

    public void setPkColoniaC(final IAlphanumericValue pkColoniaC) {
        this.datosSaeta.setPkColoniaC(pkColoniaC);
    }

    // Property: datosSaeta.pkCodPostC -> 05 PK-COD-POST-C 9(05)
    public UnsignedNumericVar getPkCodPostC() {
        return this.datosSaeta.getPkCodPostC();
    }

    public void setPkCodPostC(final int pkCodPostC) {
        this.datosSaeta.setPkCodPostC(pkCodPostC);
    }

    public void setPkCodPostC(final INumericValue pkCodPostC) {
        this.datosSaeta.setPkCodPostC(pkCodPostC);
    }

    // Property: datosSaeta.pkPoblC -> 05 PK-POBL-C X(20)
    public AlphanumericVar getPkPoblC() {
        return this.datosSaeta.getPkPoblC();
    }

    public void setPkPoblC(final String pkPoblC) {
        this.datosSaeta.setPkPoblC(pkPoblC);
    }

    public void setPkPoblC(final IAlphanumericValue pkPoblC) {
        this.datosSaeta.setPkPoblC(pkPoblC);
    }

    // Property: datosSaeta.pkEstadoC -> 05 PK-ESTADO-C 99
    public UnsignedNumericVar getPkEstadoC() {
        return this.datosSaeta.getPkEstadoC();
    }

    public void setPkEstadoC(final int pkEstadoC) {
        this.datosSaeta.setPkEstadoC(pkEstadoC);
    }

    public void setPkEstadoC(final INumericValue pkEstadoC) {
        this.datosSaeta.setPkEstadoC(pkEstadoC);
    }

    // Property: datosSaeta.pkPaisC -> 05 PK-PAIS-C 99
    public UnsignedNumericVar getPkPaisC() {
        return this.datosSaeta.getPkPaisC();
    }

    public void setPkPaisC(final int pkPaisC) {
        this.datosSaeta.setPkPaisC(pkPaisC);
    }

    public void setPkPaisC(final INumericValue pkPaisC) {
        this.datosSaeta.setPkPaisC(pkPaisC);
    }

    // Property: datosSaeta.pkTelefC -> 05 PK-TELEF-C 9(12) REDEFINED BY PK-TELEF-CR
    public UnsignedNumericVar getPkTelefC() {
        return this.datosSaeta.getPkTelefC();
    }

    public void setPkTelefC(final BigDecimal pkTelefC) {
        this.datosSaeta.setPkTelefC(pkTelefC);
    }

    public void setPkTelefC(final int pkTelefC) {
        this.datosSaeta.setPkTelefC(pkTelefC);
    }

    public void setPkTelefC(final INumericValue pkTelefC) {
        this.datosSaeta.setPkTelefC(pkTelefC);
    }

    // Property: datosSaeta.pkTelefCr -> 05 PK-TELEF-CR REDEFINES PK-TELEF-C X(12)
    public AlphanumericVar getPkTelefCr() {
        return this.datosSaeta.getPkTelefCr();
    }

    public void setPkTelefCr(final String pkTelefCr) {
        this.datosSaeta.setPkTelefCr(pkTelefCr);
    }

    public void setPkTelefCr(final IAlphanumericValue pkTelefCr) {
        this.datosSaeta.setPkTelefCr(pkTelefCr);
    }

    // Property: datosSaeta.pkFecNacim -> 05 PK-FEC-NACIM 9(6)
    public UnsignedNumericVar getPkFecNacim() {
        return this.datosSaeta.getPkFecNacim();
    }

    public void setPkFecNacim(final int pkFecNacim) {
        this.datosSaeta.setPkFecNacim(pkFecNacim);
    }

    public void setPkFecNacim(final INumericValue pkFecNacim) {
        this.datosSaeta.setPkFecNacim(pkFecNacim);
    }

    // Property: datosSaeta.pkNacionalidad -> 05 PK-NACIONALIDAD 99
    public UnsignedNumericVar getPkNacionalidad() {
        return this.datosSaeta.getPkNacionalidad();
    }

    public void setPkNacionalidad(final int pkNacionalidad) {
        this.datosSaeta.setPkNacionalidad(pkNacionalidad);
    }

    public void setPkNacionalidad(final INumericValue pkNacionalidad) {
        this.datosSaeta.setPkNacionalidad(pkNacionalidad);
    }

    // Property: datosSaeta.pkSexo -> 05 PK-SEXO X
    public AlphanumericVar getPkSexo() {
        return this.datosSaeta.getPkSexo();
    }

    public void setPkSexo(final String pkSexo) {
        this.datosSaeta.setPkSexo(pkSexo);
    }

    public void setPkSexo(final IAlphanumericValue pkSexo) {
        this.datosSaeta.setPkSexo(pkSexo);
    }

    // Property: datosSaeta.pkEdoCivil -> 05 PK-EDO-CIVIL X
    public AlphanumericVar getPkEdoCivil() {
        return this.datosSaeta.getPkEdoCivil();
    }

    public void setPkEdoCivil(final String pkEdoCivil) {
        this.datosSaeta.setPkEdoCivil(pkEdoCivil);
    }

    public void setPkEdoCivil(final IAlphanumericValue pkEdoCivil) {
        this.datosSaeta.setPkEdoCivil(pkEdoCivil);
    }

    // Property: datosSaeta.pkEscolaridad -> 05 PK-ESCOLARIDAD 9
    public UnsignedNumericVar getPkEscolaridad() {
        return this.datosSaeta.getPkEscolaridad();
    }

    public void setPkEscolaridad(final int pkEscolaridad) {
        this.datosSaeta.setPkEscolaridad(pkEscolaridad);
    }

    public void setPkEscolaridad(final INumericValue pkEscolaridad) {
        this.datosSaeta.setPkEscolaridad(pkEscolaridad);
    }

    // Property: datosSaeta.pkNomConyuge -> 05 PK-NOM-CONYUGE X(35)
    public AlphanumericVar getPkNomConyuge() {
        return this.datosSaeta.getPkNomConyuge();
    }

    public void setPkNomConyuge(final String pkNomConyuge) {
        this.datosSaeta.setPkNomConyuge(pkNomConyuge);
    }

    public void setPkNomConyuge(final IAlphanumericValue pkNomConyuge) {
        this.datosSaeta.setPkNomConyuge(pkNomConyuge);
    }

    // Property: datosSaeta.pkFecCasam -> 05 PK-FEC-CASAM 9(6)
    public UnsignedNumericVar getPkFecCasam() {
        return this.datosSaeta.getPkFecCasam();
    }

    public void setPkFecCasam(final int pkFecCasam) {
        this.datosSaeta.setPkFecCasam(pkFecCasam);
    }

    public void setPkFecCasam(final INumericValue pkFecCasam) {
        this.datosSaeta.setPkFecCasam(pkFecCasam);
    }

    // Property: datosSaeta.pkStatus -> 05 PK-STATUS X
    public AlphanumericVar getPkStatus() {
        return this.datosSaeta.getPkStatus();
    }

    public void setPkStatus(final String pkStatus) {
        this.datosSaeta.setPkStatus(pkStatus);
    }

    public void setPkStatus(final IAlphanumericValue pkStatus) {
        this.datosSaeta.setPkStatus(pkStatus);
    }

    // Property: datosSaeta.pkMotStatus -> 05 PK-MOT-STATUS 99
    public UnsignedNumericVar getPkMotStatus() {
        return this.datosSaeta.getPkMotStatus();
    }

    public void setPkMotStatus(final int pkMotStatus) {
        this.datosSaeta.setPkMotStatus(pkMotStatus);
    }

    public void setPkMotStatus(final INumericValue pkMotStatus) {
        this.datosSaeta.setPkMotStatus(pkMotStatus);
    }

    // Property: datosSaeta.pkFecStatus -> 05 PK-FEC-STATUS 9(6)
    public UnsignedNumericVar getPkFecStatus() {
        return this.datosSaeta.getPkFecStatus();
    }

    public void setPkFecStatus(final int pkFecStatus) {
        this.datosSaeta.setPkFecStatus(pkFecStatus);
    }

    public void setPkFecStatus(final INumericValue pkFecStatus) {
        this.datosSaeta.setPkFecStatus(pkFecStatus);
    }

    // Property: datosSaeta.pkFecAlta -> 05 PK-FEC-ALTA 9(6)
    public UnsignedNumericVar getPkFecAlta() {
        return this.datosSaeta.getPkFecAlta();
    }

    public void setPkFecAlta(final int pkFecAlta) {
        this.datosSaeta.setPkFecAlta(pkFecAlta);
    }

    public void setPkFecAlta(final INumericValue pkFecAlta) {
        this.datosSaeta.setPkFecAlta(pkFecAlta);
    }

    // Property: datosSaeta.pkFecTerm -> 05 PK-FEC-TERM 9(6)
    public UnsignedNumericVar getPkFecTerm() {
        return this.datosSaeta.getPkFecTerm();
    }

    public void setPkFecTerm(final int pkFecTerm) {
        this.datosSaeta.setPkFecTerm(pkFecTerm);
    }

    public void setPkFecTerm(final INumericValue pkFecTerm) {
        this.datosSaeta.setPkFecTerm(pkFecTerm);
    }

    // Property: datosSaeta.pkTipCedula -> 05 PK-TIP-CEDULA X
    public AlphanumericVar getPkTipCedula() {
        return this.datosSaeta.getPkTipCedula();
    }

    public void setPkTipCedula(final String pkTipCedula) {
        this.datosSaeta.setPkTipCedula(pkTipCedula);
    }

    public void setPkTipCedula(final IAlphanumericValue pkTipCedula) {
        this.datosSaeta.setPkTipCedula(pkTipCedula);
    }

    // Property: datosSaeta.pkNumCedula -> 05 PK-NUM-CEDULA X(20) REDEFINED BY PK-NUM-CEDULARRR
    public AlphanumericVar getPkNumCedula() {
        return this.datosSaeta.getPkNumCedula();
    }

    public void setPkNumCedula(final String pkNumCedula) {
        this.datosSaeta.setPkNumCedula(pkNumCedula);
    }

    public void setPkNumCedula(final IAlphanumericValue pkNumCedula) {
        this.datosSaeta.setPkNumCedula(pkNumCedula);
    }

    // Property: datosSaeta.pkNumCedular -> 05 PK-NUM-CEDULAR REDEFINES PK-NUM-CEDULA
    public PkNumCedular getPkNumCedular() {
        return this.datosSaeta.getPkNumCedular();
    }

    // Property: datosSaeta.pkNumCedular.pkNumCedulaDos -> 07 PK-NUM-CEDULA-DOS XX
    public AlphanumericVar getPkNumCedulaDos() {
        return this.datosSaeta.getPkNumCedular().getPkNumCedulaDos();
    }

    public void setPkNumCedulaDos(final String pkNumCedulaDos) {
        this.datosSaeta.getPkNumCedular().setPkNumCedulaDos(pkNumCedulaDos);
    }

    public void setPkNumCedulaDos(final IAlphanumericValue pkNumCedulaDos) {
        this.datosSaeta.getPkNumCedular().setPkNumCedulaDos(pkNumCedulaDos);
    }

    // Property: datosSaeta.pkNumCedular.pkNumCedulaR18 -> 07 PK-NUM-CEDULA-R18 X(18)
    public AlphanumericVar getPkNumCedulaR18() {
        return this.datosSaeta.getPkNumCedular().getPkNumCedulaR18();
    }

    public void setPkNumCedulaR18(final String pkNumCedulaR18) {
        this.datosSaeta.getPkNumCedular().setPkNumCedulaR18(pkNumCedulaR18);
    }

    public void setPkNumCedulaR18(final IAlphanumericValue pkNumCedulaR18) {
        this.datosSaeta.getPkNumCedular().setPkNumCedulaR18(pkNumCedulaR18);
    }

    // Property: datosSaeta.pkNumCedularr -> 05 PK-NUM-CEDULARR REDEFINES PK-NUM-CEDULA
    public PkNumCedularr getPkNumCedularr() {
        return this.datosSaeta.getPkNumCedularr();
    }

    // Property: datosSaeta.pkNumCedularr.pkNumCedulaTres -> 07 PK-NUM-CEDULA-TRES XXX
    public AlphanumericVar getPkNumCedulaTres() {
        return this.datosSaeta.getPkNumCedularr().getPkNumCedulaTres();
    }

    public void setPkNumCedulaTres(final String pkNumCedulaTres) {
        this.datosSaeta.getPkNumCedularr().setPkNumCedulaTres(pkNumCedulaTres);
    }

    public void setPkNumCedulaTres(final IAlphanumericValue pkNumCedulaTres) {
        this.datosSaeta.getPkNumCedularr().setPkNumCedulaTres(pkNumCedulaTres);
    }

    // Ambiguous Property: datosSaeta.pkNumCedularr.pkNumCedulaR17 -> 07 PK-NUM-CEDULA-R17 X(17)
    // Property: datosSaeta.pkNumCedularrr -> 05 PK-NUM-CEDULARRR REDEFINES PK-NUM-CEDULA
    public PkNumCedularrr getPkNumCedularrr() {
        return this.datosSaeta.getPkNumCedularrr();
    }

    // Property: datosSaeta.pkNumCedularrr.pkNumCedulaXx -> 07 PK-NUM-CEDULA-XX XX
    public AlphanumericVar getPkNumCedulaXx() {
        return this.datosSaeta.getPkNumCedularrr().getPkNumCedulaXx();
    }

    public void setPkNumCedulaXx(final String pkNumCedulaXx) {
        this.datosSaeta.getPkNumCedularrr().setPkNumCedulaXx(pkNumCedulaXx);
    }

    public void setPkNumCedulaXx(final IAlphanumericValue pkNumCedulaXx) {
        this.datosSaeta.getPkNumCedularrr().setPkNumCedulaXx(pkNumCedulaXx);
    }

    // Property: datosSaeta.pkNumCedularrr.pkNumCedulaX -> 07 PK-NUM-CEDULA-X X
    public AlphanumericVar getPkNumCedulaX() {
        return this.datosSaeta.getPkNumCedularrr().getPkNumCedulaX();
    }

    public void setPkNumCedulaX(final String pkNumCedulaX) {
        this.datosSaeta.getPkNumCedularrr().setPkNumCedulaX(pkNumCedulaX);
    }

    public void setPkNumCedulaX(final IAlphanumericValue pkNumCedulaX) {
        this.datosSaeta.getPkNumCedularrr().setPkNumCedulaX(pkNumCedulaX);
    }

    // Ambiguous Property: datosSaeta.pkNumCedularrr.pkNumCedulaR17 -> 07 PK-NUM-CEDULA-R17 X(17)
    // Property: datosSaeta.pkIniCedula -> 05 PK-INI-CEDULA 9(6)
    public UnsignedNumericVar getPkIniCedula() {
        return this.datosSaeta.getPkIniCedula();
    }

    public void setPkIniCedula(final int pkIniCedula) {
        this.datosSaeta.setPkIniCedula(pkIniCedula);
    }

    public void setPkIniCedula(final INumericValue pkIniCedula) {
        this.datosSaeta.setPkIniCedula(pkIniCedula);
    }

    // Property: datosSaeta.pkFinCedula -> 05 PK-FIN-CEDULA 9(6)
    public UnsignedNumericVar getPkFinCedula() {
        return this.datosSaeta.getPkFinCedula();
    }

    public void setPkFinCedula(final int pkFinCedula) {
        this.datosSaeta.setPkFinCedula(pkFinCedula);
    }

    public void setPkFinCedula(final INumericValue pkFinCedula) {
        this.datosSaeta.setPkFinCedula(pkFinCedula);
    }

    // Property: datosSaeta.pkRamoP_1 -> 05 PK-RAMO-P-1 X(02)
    public AlphanumericVar getPkRamoP_1() {
        return this.datosSaeta.getPkRamoP_1();
    }

    public void setPkRamoP_1(final String pkRamoP_1) {
        this.datosSaeta.setPkRamoP_1(pkRamoP_1);
    }

    public void setPkRamoP_1(final IAlphanumericValue pkRamoP_1) {
        this.datosSaeta.setPkRamoP_1(pkRamoP_1);
    }

    // Property: datosSaeta.pkRamoP_2 -> 05 PK-RAMO-P-2 X(02)
    public AlphanumericVar getPkRamoP_2() {
        return this.datosSaeta.getPkRamoP_2();
    }

    public void setPkRamoP_2(final String pkRamoP_2) {
        this.datosSaeta.setPkRamoP_2(pkRamoP_2);
    }

    public void setPkRamoP_2(final IAlphanumericValue pkRamoP_2) {
        this.datosSaeta.setPkRamoP_2(pkRamoP_2);
    }

    // Property: datosSaeta.pkRamoP_3 -> 05 PK-RAMO-P-3 X(02)
    public AlphanumericVar getPkRamoP_3() {
        return this.datosSaeta.getPkRamoP_3();
    }

    public void setPkRamoP_3(final String pkRamoP_3) {
        this.datosSaeta.setPkRamoP_3(pkRamoP_3);
    }

    public void setPkRamoP_3(final IAlphanumericValue pkRamoP_3) {
        this.datosSaeta.setPkRamoP_3(pkRamoP_3);
    }

    // Property: datosSaeta.pkRamoP_4 -> 05 PK-RAMO-P-4 X(02)
    public AlphanumericVar getPkRamoP_4() {
        return this.datosSaeta.getPkRamoP_4();
    }

    public void setPkRamoP_4(final String pkRamoP_4) {
        this.datosSaeta.setPkRamoP_4(pkRamoP_4);
    }

    public void setPkRamoP_4(final IAlphanumericValue pkRamoP_4) {
        this.datosSaeta.setPkRamoP_4(pkRamoP_4);
    }

    // Property: datosSaeta.pkRamoP_5 -> 05 PK-RAMO-P-5 X(02)
    public AlphanumericVar getPkRamoP_5() {
        return this.datosSaeta.getPkRamoP_5();
    }

    public void setPkRamoP_5(final String pkRamoP_5) {
        this.datosSaeta.setPkRamoP_5(pkRamoP_5);
    }

    public void setPkRamoP_5(final IAlphanumericValue pkRamoP_5) {
        this.datosSaeta.setPkRamoP_5(pkRamoP_5);
    }

    // Property: datosSaeta.pkRamoP_6 -> 05 PK-RAMO-P-6 X(02)
    public AlphanumericVar getPkRamoP_6() {
        return this.datosSaeta.getPkRamoP_6();
    }

    public void setPkRamoP_6(final String pkRamoP_6) {
        this.datosSaeta.setPkRamoP_6(pkRamoP_6);
    }

    public void setPkRamoP_6(final IAlphanumericValue pkRamoP_6) {
        this.datosSaeta.setPkRamoP_6(pkRamoP_6);
    }

    // Property: datosSaeta.pkRamoP_7 -> 05 PK-RAMO-P-7 X(02)
    public AlphanumericVar getPkRamoP_7() {
        return this.datosSaeta.getPkRamoP_7();
    }

    public void setPkRamoP_7(final String pkRamoP_7) {
        this.datosSaeta.setPkRamoP_7(pkRamoP_7);
    }

    public void setPkRamoP_7(final IAlphanumericValue pkRamoP_7) {
        this.datosSaeta.setPkRamoP_7(pkRamoP_7);
    }

    // Property: datosSaeta.pkRamoP_8 -> 05 PK-RAMO-P-8 X(02)
    public AlphanumericVar getPkRamoP_8() {
        return this.datosSaeta.getPkRamoP_8();
    }

    public void setPkRamoP_8(final String pkRamoP_8) {
        this.datosSaeta.setPkRamoP_8(pkRamoP_8);
    }

    public void setPkRamoP_8(final IAlphanumericValue pkRamoP_8) {
        this.datosSaeta.setPkRamoP_8(pkRamoP_8);
    }

    // Property: datosSaeta.pkRamoP_9 -> 05 PK-RAMO-P-9 X(02)
    public AlphanumericVar getPkRamoP_9() {
        return this.datosSaeta.getPkRamoP_9();
    }

    public void setPkRamoP_9(final String pkRamoP_9) {
        this.datosSaeta.setPkRamoP_9(pkRamoP_9);
    }

    public void setPkRamoP_9(final IAlphanumericValue pkRamoP_9) {
        this.datosSaeta.setPkRamoP_9(pkRamoP_9);
    }

    // Property: datosSaeta.pkRamoP_10 -> 05 PK-RAMO-P-10 X(02)
    public AlphanumericVar getPkRamoP_10() {
        return this.datosSaeta.getPkRamoP_10();
    }

    public void setPkRamoP_10(final String pkRamoP_10) {
        this.datosSaeta.setPkRamoP_10(pkRamoP_10);
    }

    public void setPkRamoP_10(final IAlphanumericValue pkRamoP_10) {
        this.datosSaeta.setPkRamoP_10(pkRamoP_10);
    }

    // Property: datosSaeta.pkObservOficS -> 05 PK-OBSERV-OFIC-S X(60)
    public AlphanumericVar getPkObservOficS() {
        return this.datosSaeta.getPkObservOficS();
    }

    public void setPkObservOficS(final String pkObservOficS) {
        this.datosSaeta.setPkObservOficS(pkObservOficS);
    }

    public void setPkObservOficS(final IAlphanumericValue pkObservOficS) {
        this.datosSaeta.setPkObservOficS(pkObservOficS);
    }

    // Property: datosSaeta.pkRegIva -> 05 PK-REG-IVA X(10)
    public AlphanumericVar getPkRegIva() {
        return this.datosSaeta.getPkRegIva();
    }

    public void setPkRegIva(final String pkRegIva) {
        this.datosSaeta.setPkRegIva(pkRegIva);
    }

    public void setPkRegIva(final IAlphanumericValue pkRegIva) {
        this.datosSaeta.setPkRegIva(pkRegIva);
    }

    // Property: datosSaeta.pkTipo -> 05 PK-TIPO 99
    public UnsignedNumericVar getPkTipo() {
        return this.datosSaeta.getPkTipo();
    }

    public void setPkTipo(final int pkTipo) {
        this.datosSaeta.setPkTipo(pkTipo);
    }

    public void setPkTipo(final INumericValue pkTipo) {
        this.datosSaeta.setPkTipo(pkTipo);
    }

    // Property: datosSaeta.pkCodPago -> 05 PK-COD-PAGO 99
    public UnsignedNumericVar getPkCodPago() {
        return this.datosSaeta.getPkCodPago();
    }

    public void setPkCodPago(final int pkCodPago) {
        this.datosSaeta.setPkCodPago(pkCodPago);
    }

    public void setPkCodPago(final INumericValue pkCodPago) {
        this.datosSaeta.setPkCodPago(pkCodPago);
    }

    // Property: datosSaeta.pkCvePagoImp -> 05 PK-CVE-PAGO-IMP 99
    public UnsignedNumericVar getPkCvePagoImp() {
        return this.datosSaeta.getPkCvePagoImp();
    }

    public void setPkCvePagoImp(final int pkCvePagoImp) {
        this.datosSaeta.setPkCvePagoImp(pkCvePagoImp);
    }

    public void setPkCvePagoImp(final INumericValue pkCvePagoImp) {
        this.datosSaeta.setPkCvePagoImp(pkCvePagoImp);
    }

    // Property: datosSaeta.pkPorIva -> 05 PK-POR-IVA 99V99
    public UnsignedNumericVar getPkPorIva() {
        return this.datosSaeta.getPkPorIva();
    }

    public void setPkPorIva(final BigDecimal pkPorIva) {
        this.datosSaeta.setPkPorIva(pkPorIva);
    }

    public void setPkPorIva(final int pkPorIva) {
        this.datosSaeta.setPkPorIva(pkPorIva);
    }

    public void setPkPorIva(final INumericValue pkPorIva) {
        this.datosSaeta.setPkPorIva(pkPorIva);
    }

    // Property: datosSaeta.pkPorIsr -> 05 PK-POR-ISR 99V99
    public UnsignedNumericVar getPkPorIsr() {
        return this.datosSaeta.getPkPorIsr();
    }

    public void setPkPorIsr(final BigDecimal pkPorIsr) {
        this.datosSaeta.setPkPorIsr(pkPorIsr);
    }

    public void setPkPorIsr(final int pkPorIsr) {
        this.datosSaeta.setPkPorIsr(pkPorIsr);
    }

    public void setPkPorIsr(final INumericValue pkPorIsr) {
        this.datosSaeta.setPkPorIsr(pkPorIsr);
    }

    // Property: datosSaeta.pkNumFianza -> 05 PK-NUM-FIANZA X(12)
    public AlphanumericVar getPkNumFianza() {
        return this.datosSaeta.getPkNumFianza();
    }

    public void setPkNumFianza(final String pkNumFianza) {
        this.datosSaeta.setPkNumFianza(pkNumFianza);
    }

    public void setPkNumFianza(final IAlphanumericValue pkNumFianza) {
        this.datosSaeta.setPkNumFianza(pkNumFianza);
    }

    // Property: datosSaeta.pkCanFianza -> 05 PK-CAN-FIANZA 9(7)V99
    public UnsignedNumericVar getPkCanFianza() {
        return this.datosSaeta.getPkCanFianza();
    }

    public void setPkCanFianza(final BigDecimal pkCanFianza) {
        this.datosSaeta.setPkCanFianza(pkCanFianza);
    }

    public void setPkCanFianza(final int pkCanFianza) {
        this.datosSaeta.setPkCanFianza(pkCanFianza);
    }

    public void setPkCanFianza(final INumericValue pkCanFianza) {
        this.datosSaeta.setPkCanFianza(pkCanFianza);
    }

    // Property: datosSaeta.pkIniFianza -> 05 PK-INI-FIANZA 9(6)
    public UnsignedNumericVar getPkIniFianza() {
        return this.datosSaeta.getPkIniFianza();
    }

    public void setPkIniFianza(final int pkIniFianza) {
        this.datosSaeta.setPkIniFianza(pkIniFianza);
    }

    public void setPkIniFianza(final INumericValue pkIniFianza) {
        this.datosSaeta.setPkIniFianza(pkIniFianza);
    }

    // Property: datosSaeta.pkFinFianza -> 05 PK-FIN-FIANZA 9(6)
    public UnsignedNumericVar getPkFinFianza() {
        return this.datosSaeta.getPkFinFianza();
    }

    public void setPkFinFianza(final int pkFinFianza) {
        this.datosSaeta.setPkFinFianza(pkFinFianza);
    }

    public void setPkFinFianza(final INumericValue pkFinFianza) {
        this.datosSaeta.setPkFinFianza(pkFinFianza);
    }

    // Property: datosSaeta.pkAgenteRec -> 05 PK-AGENTE-REC 9(7)
    public UnsignedNumericVar getPkAgenteRec() {
        return this.datosSaeta.getPkAgenteRec();
    }

    public void setPkAgenteRec(final int pkAgenteRec) {
        this.datosSaeta.setPkAgenteRec(pkAgenteRec);
    }

    public void setPkAgenteRec(final INumericValue pkAgenteRec) {
        this.datosSaeta.setPkAgenteRec(pkAgenteRec);
    }

    // Property: datosSaeta.pkOtrasCias -> 05 PK-OTRAS-CIAS X(1)
    public AlphanumericVar getPkOtrasCias() {
        return this.datosSaeta.getPkOtrasCias();
    }

    public void setPkOtrasCias(final String pkOtrasCias) {
        this.datosSaeta.setPkOtrasCias(pkOtrasCias);
    }

    public void setPkOtrasCias(final IAlphanumericValue pkOtrasCias) {
        this.datosSaeta.setPkOtrasCias(pkOtrasCias);
    }

    // Property: datosSaeta.pkEsGerente -> 05 PK-ES-GERENTE X(1)
    public AlphanumericVar getPkEsGerente() {
        return this.datosSaeta.getPkEsGerente();
    }

    public void setPkEsGerente(final String pkEsGerente) {
        this.datosSaeta.setPkEsGerente(pkEsGerente);
    }

    public void setPkEsGerente(final IAlphanumericValue pkEsGerente) {
        this.datosSaeta.setPkEsGerente(pkEsGerente);
    }

    // Property: datosSaeta.pkZonaNal -> 05 PK-ZONA-NAL 9(4)
    public UnsignedNumericVar getPkZonaNal() {
        return this.datosSaeta.getPkZonaNal();
    }

    public void setPkZonaNal(final int pkZonaNal) {
        this.datosSaeta.setPkZonaNal(pkZonaNal);
    }

    public void setPkZonaNal(final INumericValue pkZonaNal) {
        this.datosSaeta.setPkZonaNal(pkZonaNal);
    }

    // Property: datosSaeta.pkZonaProv -> 05 PK-ZONA-PROV 9(4)
    public UnsignedNumericVar getPkZonaProv() {
        return this.datosSaeta.getPkZonaProv();
    }

    public void setPkZonaProv(final int pkZonaProv) {
        this.datosSaeta.setPkZonaProv(pkZonaProv);
    }

    public void setPkZonaProv(final INumericValue pkZonaProv) {
        this.datosSaeta.setPkZonaProv(pkZonaProv);
    }

    // Property: datosSaeta.pkZonaNalGte -> 05 PK-ZONA-NAL-GTE 9(04)
    public UnsignedNumericVar getPkZonaNalGte() {
        return this.datosSaeta.getPkZonaNalGte();
    }

    public void setPkZonaNalGte(final int pkZonaNalGte) {
        this.datosSaeta.setPkZonaNalGte(pkZonaNalGte);
    }

    public void setPkZonaNalGte(final INumericValue pkZonaNalGte) {
        this.datosSaeta.setPkZonaNalGte(pkZonaNalGte);
    }

    // Property: datosSaeta.pkZonaProvGte -> 05 PK-ZONA-PROV-GTE 9(04)
    public UnsignedNumericVar getPkZonaProvGte() {
        return this.datosSaeta.getPkZonaProvGte();
    }

    public void setPkZonaProvGte(final int pkZonaProvGte) {
        this.datosSaeta.setPkZonaProvGte(pkZonaProvGte);
    }

    public void setPkZonaProvGte(final INumericValue pkZonaProvGte) {
        this.datosSaeta.setPkZonaProvGte(pkZonaProvGte);
    }

    // Property: datosSaeta.pkOficinaNal -> 05 PK-OFICINA-NAL 999
    public UnsignedNumericVar getPkOficinaNal() {
        return this.datosSaeta.getPkOficinaNal();
    }

    public void setPkOficinaNal(final int pkOficinaNal) {
        this.datosSaeta.setPkOficinaNal(pkOficinaNal);
    }

    public void setPkOficinaNal(final INumericValue pkOficinaNal) {
        this.datosSaeta.setPkOficinaNal(pkOficinaNal);
    }

    // Property: datosSaeta.pkOficinaProv -> 05 PK-OFICINA-PROV 999
    public UnsignedNumericVar getPkOficinaProv() {
        return this.datosSaeta.getPkOficinaProv();
    }

    public void setPkOficinaProv(final int pkOficinaProv) {
        this.datosSaeta.setPkOficinaProv(pkOficinaProv);
    }

    public void setPkOficinaProv(final INumericValue pkOficinaProv) {
        this.datosSaeta.setPkOficinaProv(pkOficinaProv);
    }

    // Property: datosSaeta.pkFecZonaNal -> 05 PK-FEC-ZONA-NAL 9(06)
    public UnsignedNumericVar getPkFecZonaNal() {
        return this.datosSaeta.getPkFecZonaNal();
    }

    public void setPkFecZonaNal(final int pkFecZonaNal) {
        this.datosSaeta.setPkFecZonaNal(pkFecZonaNal);
    }

    public void setPkFecZonaNal(final INumericValue pkFecZonaNal) {
        this.datosSaeta.setPkFecZonaNal(pkFecZonaNal);
    }

    // Property: datosSaeta.pkFecZonaProv -> 05 PK-FEC-ZONA-PROV 9(06)
    public UnsignedNumericVar getPkFecZonaProv() {
        return this.datosSaeta.getPkFecZonaProv();
    }

    public void setPkFecZonaProv(final int pkFecZonaProv) {
        this.datosSaeta.setPkFecZonaProv(pkFecZonaProv);
    }

    public void setPkFecZonaProv(final INumericValue pkFecZonaProv) {
        this.datosSaeta.setPkFecZonaProv(pkFecZonaProv);
    }

    // Property: datosSaeta.pkFecZonaNalGte -> 05 PK-FEC-ZONA-NAL-GTE 9(06)
    public UnsignedNumericVar getPkFecZonaNalGte() {
        return this.datosSaeta.getPkFecZonaNalGte();
    }

    public void setPkFecZonaNalGte(final int pkFecZonaNalGte) {
        this.datosSaeta.setPkFecZonaNalGte(pkFecZonaNalGte);
    }

    public void setPkFecZonaNalGte(final INumericValue pkFecZonaNalGte) {
        this.datosSaeta.setPkFecZonaNalGte(pkFecZonaNalGte);
    }

    // Property: datosSaeta.pkFecZonaProvGte -> 05 PK-FEC-ZONA-PROV-GTE 9(06)
    public UnsignedNumericVar getPkFecZonaProvGte() {
        return this.datosSaeta.getPkFecZonaProvGte();
    }

    public void setPkFecZonaProvGte(final int pkFecZonaProvGte) {
        this.datosSaeta.setPkFecZonaProvGte(pkFecZonaProvGte);
    }

    public void setPkFecZonaProvGte(final INumericValue pkFecZonaProvGte) {
        this.datosSaeta.setPkFecZonaProvGte(pkFecZonaProvGte);
    }

    // Property: datosSaeta.pkFecOficinaNal -> 05 PK-FEC-OFICINA-NAL 9(06)
    public UnsignedNumericVar getPkFecOficinaNal() {
        return this.datosSaeta.getPkFecOficinaNal();
    }

    public void setPkFecOficinaNal(final int pkFecOficinaNal) {
        this.datosSaeta.setPkFecOficinaNal(pkFecOficinaNal);
    }

    public void setPkFecOficinaNal(final INumericValue pkFecOficinaNal) {
        this.datosSaeta.setPkFecOficinaNal(pkFecOficinaNal);
    }

    // Property: datosSaeta.pkFecOficinaProv -> 05 PK-FEC-OFICINA-PROV 9(06)
    public UnsignedNumericVar getPkFecOficinaProv() {
        return this.datosSaeta.getPkFecOficinaProv();
    }

    public void setPkFecOficinaProv(final int pkFecOficinaProv) {
        this.datosSaeta.setPkFecOficinaProv(pkFecOficinaProv);
    }

    public void setPkFecOficinaProv(final INumericValue pkFecOficinaProv) {
        this.datosSaeta.setPkFecOficinaProv(pkFecOficinaProv);
    }

    // Property: datosSaeta.pkCveAgrup -> 05 PK-CVE-AGRUP 9
    public UnsignedNumericVar getPkCveAgrup() {
        return this.datosSaeta.getPkCveAgrup();
    }

    public void setPkCveAgrup(final int pkCveAgrup) {
        this.datosSaeta.setPkCveAgrup(pkCveAgrup);
    }

    public void setPkCveAgrup(final INumericValue pkCveAgrup) {
        this.datosSaeta.setPkCveAgrup(pkCveAgrup);
    }

    // Property: datosSaeta.pkCveEnvio -> 05 PK-CVE-ENVIO 99
    public UnsignedNumericVar getPkCveEnvio() {
        return this.datosSaeta.getPkCveEnvio();
    }

    public void setPkCveEnvio(final int pkCveEnvio) {
        this.datosSaeta.setPkCveEnvio(pkCveEnvio);
    }

    public void setPkCveEnvio(final INumericValue pkCveEnvio) {
        this.datosSaeta.setPkCveEnvio(pkCveEnvio);
    }

    // Property: datosSaeta.pkCalidad -> 05 PK-CALIDAD 99
    public UnsignedNumericVar getPkCalidad() {
        return this.datosSaeta.getPkCalidad();
    }

    public void setPkCalidad(final int pkCalidad) {
        this.datosSaeta.setPkCalidad(pkCalidad);
    }

    public void setPkCalidad(final INumericValue pkCalidad) {
        this.datosSaeta.setPkCalidad(pkCalidad);
    }

    // Property: datosSaeta.pkJefeGpoNal -> 05 PK-JEFE-GPO-NAL 9(7)
    public UnsignedNumericVar getPkJefeGpoNal() {
        return this.datosSaeta.getPkJefeGpoNal();
    }

    public void setPkJefeGpoNal(final int pkJefeGpoNal) {
        this.datosSaeta.setPkJefeGpoNal(pkJefeGpoNal);
    }

    public void setPkJefeGpoNal(final INumericValue pkJefeGpoNal) {
        this.datosSaeta.setPkJefeGpoNal(pkJefeGpoNal);
    }

    // Property: datosSaeta.pkJefeGpoProv -> 05 PK-JEFE-GPO-PROV 9(7)
    public UnsignedNumericVar getPkJefeGpoProv() {
        return this.datosSaeta.getPkJefeGpoProv();
    }

    public void setPkJefeGpoProv(final int pkJefeGpoProv) {
        this.datosSaeta.setPkJefeGpoProv(pkJefeGpoProv);
    }

    public void setPkJefeGpoProv(final INumericValue pkJefeGpoProv) {
        this.datosSaeta.setPkJefeGpoProv(pkJefeGpoProv);
    }

    // Property: datosSaeta.pkFecConexionP -> 05 PK-FEC-CONEXION-P 9(6) REDEFINED BY PK-FEC-CONEXION-PR
    public UnsignedNumericVar getPkFecConexionP() {
        return this.datosSaeta.getPkFecConexionP();
    }

    public void setPkFecConexionP(final int pkFecConexionP) {
        this.datosSaeta.setPkFecConexionP(pkFecConexionP);
    }

    public void setPkFecConexionP(final INumericValue pkFecConexionP) {
        this.datosSaeta.setPkFecConexionP(pkFecConexionP);
    }

    // Property: datosSaeta.pkFecConexionPr -> 05 PK-FEC-CONEXION-PR REDEFINES PK-FEC-CONEXION-P X(6)
    public AlphanumericVar getPkFecConexionPr() {
        return this.datosSaeta.getPkFecConexionPr();
    }

    public void setPkFecConexionPr(final String pkFecConexionPr) {
        this.datosSaeta.setPkFecConexionPr(pkFecConexionPr);
    }

    public void setPkFecConexionPr(final IAlphanumericValue pkFecConexionPr) {
        this.datosSaeta.setPkFecConexionPr(pkFecConexionPr);
    }

    // Property: datosSaeta.pkNumCedulaP -> 05 PK-NUM-CEDULA-P X(20) REDEFINED BY PK-NUM-CEDULAP-RR
    public AlphanumericVar getPkNumCedulaP() {
        return this.datosSaeta.getPkNumCedulaP();
    }

    public void setPkNumCedulaP(final String pkNumCedulaP) {
        this.datosSaeta.setPkNumCedulaP(pkNumCedulaP);
    }

    public void setPkNumCedulaP(final IAlphanumericValue pkNumCedulaP) {
        this.datosSaeta.setPkNumCedulaP(pkNumCedulaP);
    }

    // Property: datosSaeta.pkNumCedulaPr -> 05 PK-NUM-CEDULA-PR REDEFINES PK-NUM-CEDULA-P
    public PkNumCedulaPr getPkNumCedulaPr() {
        return this.datosSaeta.getPkNumCedulaPr();
    }

    // Property: datosSaeta.pkNumCedulaPr.pkNumCedulaPDos -> 07 PK-NUM-CEDULA-P-DOS XX
    public AlphanumericVar getPkNumCedulaPDos() {
        return this.datosSaeta.getPkNumCedulaPr().getPkNumCedulaPDos();
    }

    public void setPkNumCedulaPDos(final String pkNumCedulaPDos) {
        this.datosSaeta.getPkNumCedulaPr().setPkNumCedulaPDos(pkNumCedulaPDos);
    }

    public void setPkNumCedulaPDos(final IAlphanumericValue pkNumCedulaPDos) {
        this.datosSaeta.getPkNumCedulaPr().setPkNumCedulaPDos(pkNumCedulaPDos);
    }

    // Property: datosSaeta.pkNumCedulaPr.pkNumCedulaPR18 -> 07 PK-NUM-CEDULA-P-R18 X(18)
    public AlphanumericVar getPkNumCedulaPR18() {
        return this.datosSaeta.getPkNumCedulaPr().getPkNumCedulaPR18();
    }

    public void setPkNumCedulaPR18(final String pkNumCedulaPR18) {
        this.datosSaeta.getPkNumCedulaPr().setPkNumCedulaPR18(pkNumCedulaPR18);
    }

    public void setPkNumCedulaPR18(final IAlphanumericValue pkNumCedulaPR18) {
        this.datosSaeta.getPkNumCedulaPr().setPkNumCedulaPR18(pkNumCedulaPR18);
    }

    // Property: datosSaeta.pkNumCedulapRr -> 05 PK-NUM-CEDULAP-RR REDEFINES PK-NUM-CEDULA-P
    public PkNumCedulapRr getPkNumCedulapRr() {
        return this.datosSaeta.getPkNumCedulapRr();
    }

    // Property: datosSaeta.pkNumCedulapRr.pkNumCedulaPTres -> 07 PK-NUM-CEDULA-P-TRES XXX
    public AlphanumericVar getPkNumCedulaPTres() {
        return this.datosSaeta.getPkNumCedulapRr().getPkNumCedulaPTres();
    }

    public void setPkNumCedulaPTres(final String pkNumCedulaPTres) {
        this.datosSaeta.getPkNumCedulapRr().setPkNumCedulaPTres(pkNumCedulaPTres);
    }

    public void setPkNumCedulaPTres(final IAlphanumericValue pkNumCedulaPTres) {
        this.datosSaeta.getPkNumCedulapRr().setPkNumCedulaPTres(pkNumCedulaPTres);
    }

    // Property: datosSaeta.pkNumCedulapRr.pkNumCedulaPR17 -> 07 PK-NUM-CEDULA-P-R17 X(17)
    public AlphanumericVar getPkNumCedulaPR17() {
        return this.datosSaeta.getPkNumCedulapRr().getPkNumCedulaPR17();
    }

    public void setPkNumCedulaPR17(final String pkNumCedulaPR17) {
        this.datosSaeta.getPkNumCedulapRr().setPkNumCedulaPR17(pkNumCedulaPR17);
    }

    public void setPkNumCedulaPR17(final IAlphanumericValue pkNumCedulaPR17) {
        this.datosSaeta.getPkNumCedulapRr().setPkNumCedulaPR17(pkNumCedulaPR17);
    }

    // Property: datosSaeta.pkFinCedulaP -> 05 PK-FIN-CEDULA-P 9(6)
    public UnsignedNumericVar getPkFinCedulaP() {
        return this.datosSaeta.getPkFinCedulaP();
    }

    public void setPkFinCedulaP(final int pkFinCedulaP) {
        this.datosSaeta.setPkFinCedulaP(pkFinCedulaP);
    }

    public void setPkFinCedulaP(final INumericValue pkFinCedulaP) {
        this.datosSaeta.setPkFinCedulaP(pkFinCedulaP);
    }

    // Property: datosSaeta.pkStatusProv -> 05 PK-STATUS-PROV X
    public AlphanumericVar getPkStatusProv() {
        return this.datosSaeta.getPkStatusProv();
    }

    public void setPkStatusProv(final String pkStatusProv) {
        this.datosSaeta.setPkStatusProv(pkStatusProv);
    }

    public void setPkStatusProv(final IAlphanumericValue pkStatusProv) {
        this.datosSaeta.setPkStatusProv(pkStatusProv);
    }

    // Property: datosSaeta.pkMotStatusProv -> 05 PK-MOT-STATUS-PROV 99
    public UnsignedNumericVar getPkMotStatusProv() {
        return this.datosSaeta.getPkMotStatusProv();
    }

    public void setPkMotStatusProv(final int pkMotStatusProv) {
        this.datosSaeta.setPkMotStatusProv(pkMotStatusProv);
    }

    public void setPkMotStatusProv(final INumericValue pkMotStatusProv) {
        this.datosSaeta.setPkMotStatusProv(pkMotStatusProv);
    }

    // Property: datosSaeta.pkGteZonaNal -> 05 PK-GTE-ZONA-NAL 9(07)
    public UnsignedNumericVar getPkGteZonaNal() {
        return this.datosSaeta.getPkGteZonaNal();
    }

    public void setPkGteZonaNal(final int pkGteZonaNal) {
        this.datosSaeta.setPkGteZonaNal(pkGteZonaNal);
    }

    public void setPkGteZonaNal(final INumericValue pkGteZonaNal) {
        this.datosSaeta.setPkGteZonaNal(pkGteZonaNal);
    }

    // Property: datosSaeta.pkGteZonaProv -> 05 PK-GTE-ZONA-PROV 9(07)
    public UnsignedNumericVar getPkGteZonaProv() {
        return this.datosSaeta.getPkGteZonaProv();
    }

    public void setPkGteZonaProv(final int pkGteZonaProv) {
        this.datosSaeta.setPkGteZonaProv(pkGteZonaProv);
    }

    public void setPkGteZonaProv(final INumericValue pkGteZonaProv) {
        this.datosSaeta.setPkGteZonaProv(pkGteZonaProv);
    }

    // Property: datosSaeta.pkFuente -> 05 PK-FUENTE 99
    public UnsignedNumericVar getPkFuente() {
        return this.datosSaeta.getPkFuente();
    }

    public void setPkFuente(final int pkFuente) {
        this.datosSaeta.setPkFuente(pkFuente);
    }

    public void setPkFuente(final INumericValue pkFuente) {
        this.datosSaeta.setPkFuente(pkFuente);
    }

    // Property: datosSaeta.pkGrupo -> 05 PK-GRUPO 99
    public UnsignedNumericVar getPkGrupo() {
        return this.datosSaeta.getPkGrupo();
    }

    public void setPkGrupo(final int pkGrupo) {
        this.datosSaeta.setPkGrupo(pkGrupo);
    }

    public void setPkGrupo(final INumericValue pkGrupo) {
        this.datosSaeta.setPkGrupo(pkGrupo);
    }

    // Property: datosSaeta.pkSubgrupo -> 05 PK-SUBGRUPO 999
    public UnsignedNumericVar getPkSubgrupo() {
        return this.datosSaeta.getPkSubgrupo();
    }

    public void setPkSubgrupo(final int pkSubgrupo) {
        this.datosSaeta.setPkSubgrupo(pkSubgrupo);
    }

    public void setPkSubgrupo(final INumericValue pkSubgrupo) {
        this.datosSaeta.setPkSubgrupo(pkSubgrupo);
    }


    // Deeper properties

    // Property: datosCocoa.pkExiste -> 05 PK-EXISTE X
    public AlphanumericVar getPkExiste() {
        return this.datosCocoa.getPkExiste();
    }

    public void setPkExiste(final String pkExiste) {
        this.datosCocoa.setPkExiste(pkExiste);
    }

    public void setPkExiste(final IAlphanumericValue pkExiste) {
        this.datosCocoa.setPkExiste(pkExiste);
    }

    // Property: datosCocoa.pkCveSist -> 05 PK-CVE-SIST X
    public AlphanumericVar getPkCveSist() {
        return this.datosCocoa.getPkCveSist();
    }

    public void setPkCveSist(final String pkCveSist) {
        this.datosCocoa.setPkCveSist(pkCveSist);
    }

    public void setPkCveSist(final IAlphanumericValue pkCveSist) {
        this.datosCocoa.setPkCveSist(pkCveSist);
    }

    // Property: datosCocoa.pkFecRehab -> 05 PK-FEC-REHAB 9(06)
    public UnsignedNumericVar getPkFecRehab() {
        return this.datosCocoa.getPkFecRehab();
    }

    public void setPkFecRehab(final int pkFecRehab) {
        this.datosCocoa.setPkFecRehab(pkFecRehab);
    }

    public void setPkFecRehab(final INumericValue pkFecRehab) {
        this.datosCocoa.setPkFecRehab(pkFecRehab);
    }

    // Property: datosCocoa.pkQuienExime -> 05 PK-QUIEN-EXIME X(35)
    public AlphanumericVar getPkQuienExime() {
        return this.datosCocoa.getPkQuienExime();
    }

    public void setPkQuienExime(final String pkQuienExime) {
        this.datosCocoa.setPkQuienExime(pkQuienExime);
    }

    public void setPkQuienExime(final IAlphanumericValue pkQuienExime) {
        this.datosCocoa.setPkQuienExime(pkQuienExime);
    }

    // Property: datosCocoa.pkFolioInves -> 05 PK-FOLIO-INVES 9(07) REDEFINED BY PK-FOLIO-INVESR
    public UnsignedNumericVar getPkFolioInves() {
        return this.datosCocoa.getPkFolioInves();
    }

    public void setPkFolioInves(final int pkFolioInves) {
        this.datosCocoa.setPkFolioInves(pkFolioInves);
    }

    public void setPkFolioInves(final INumericValue pkFolioInves) {
        this.datosCocoa.setPkFolioInves(pkFolioInves);
    }

    // Property: datosCocoa.pkFolioInvesr -> 05 PK-FOLIO-INVESR REDEFINES PK-FOLIO-INVES
    public PkFolioInvesr getPkFolioInvesr() {
        return this.datosCocoa.getPkFolioInvesr();
    }

    // Property: datosCocoa.pkFolioInvesr.pkFolio1 -> 07 PK-FOLIO1 999
    public UnsignedNumericVar getPkFolio1() {
        return this.datosCocoa.getPkFolioInvesr().getPkFolio1();
    }

    public void setPkFolio1(final int pkFolio1) {
        this.datosCocoa.getPkFolioInvesr().setPkFolio1(pkFolio1);
    }

    public void setPkFolio1(final INumericValue pkFolio1) {
        this.datosCocoa.getPkFolioInvesr().setPkFolio1(pkFolio1);
    }

    // Property: datosCocoa.pkFolioInvesr.pkFolio2 -> 07 PK-FOLIO2 9999
    public UnsignedNumericVar getPkFolio2() {
        return this.datosCocoa.getPkFolioInvesr().getPkFolio2();
    }

    public void setPkFolio2(final int pkFolio2) {
        this.datosCocoa.getPkFolioInvesr().setPkFolio2(pkFolio2);
    }

    public void setPkFolio2(final INumericValue pkFolio2) {
        this.datosCocoa.getPkFolioInvesr().setPkFolio2(pkFolio2);
    }

    // Property: datosCocoa.pkAdicionales -> 05 PK-ADICIONALES X(60)
    public AlphanumericVar getPkAdicionales() {
        return this.datosCocoa.getPkAdicionales();
    }

    public void setPkAdicionales(final String pkAdicionales) {
        this.datosCocoa.setPkAdicionales(pkAdicionales);
    }

    public void setPkAdicionales(final IAlphanumericValue pkAdicionales) {
        this.datosCocoa.setPkAdicionales(pkAdicionales);
    }

    // Property: datosCocoa.pkFotos -> 05 PK-FOTOS X(01)
    public AlphanumericVar getPkFotos() {
        return this.datosCocoa.getPkFotos();
    }

    public void setPkFotos(final String pkFotos) {
        this.datosCocoa.setPkFotos(pkFotos);
    }

    public void setPkFotos(final IAlphanumericValue pkFotos) {
        this.datosCocoa.setPkFotos(pkFotos);
    }

    // Property: datosCocoa.pkActaNac -> 05 PK-ACTA-NAC X(01)
    public AlphanumericVar getPkActaNac() {
        return this.datosCocoa.getPkActaNac();
    }

    public void setPkActaNac(final String pkActaNac) {
        this.datosCocoa.setPkActaNac(pkActaNac);
    }

    public void setPkActaNac(final IAlphanumericValue pkActaNac) {
        this.datosCocoa.setPkActaNac(pkActaNac);
    }

    // Property: datosCocoa.pkCertificado -> 05 PK-CERTIFICADO X(01)
    public AlphanumericVar getPkCertificado() {
        return this.datosCocoa.getPkCertificado();
    }

    public void setPkCertificado(final String pkCertificado) {
        this.datosCocoa.setPkCertificado(pkCertificado);
    }

    public void setPkCertificado(final IAlphanumericValue pkCertificado) {
        this.datosCocoa.setPkCertificado(pkCertificado);
    }

    // Property: datosCocoa.pkCartaRecom -> 05 PK-CARTA-RECOM X(01)
    public AlphanumericVar getPkCartaRecom() {
        return this.datosCocoa.getPkCartaRecom();
    }

    public void setPkCartaRecom(final String pkCartaRecom) {
        this.datosCocoa.setPkCartaRecom(pkCartaRecom);
    }

    public void setPkCartaRecom(final IAlphanumericValue pkCartaRecom) {
        this.datosCocoa.setPkCartaRecom(pkCartaRecom);
    }

    // Property: datosCocoa.pkAltaShcp -> 05 PK-ALTA-SHCP X(01)
    public AlphanumericVar getPkAltaShcp() {
        return this.datosCocoa.getPkAltaShcp();
    }

    public void setPkAltaShcp(final String pkAltaShcp) {
        this.datosCocoa.setPkAltaShcp(pkAltaShcp);
    }

    public void setPkAltaShcp(final IAlphanumericValue pkAltaShcp) {
        this.datosCocoa.setPkAltaShcp(pkAltaShcp);
    }

    // Property: datosCocoa.pkHomoclave -> 05 PK-HOMOCLAVE X(01)
    public AlphanumericVar getPkHomoclave() {
        return this.datosCocoa.getPkHomoclave();
    }

    public void setPkHomoclave(final String pkHomoclave) {
        this.datosCocoa.setPkHomoclave(pkHomoclave);
    }

    public void setPkHomoclave(final IAlphanumericValue pkHomoclave) {
        this.datosCocoa.setPkHomoclave(pkHomoclave);
    }

    // Property: datosCocoa.pkCartaBaja -> 05 PK-CARTA-BAJA X(01)
    public AlphanumericVar getPkCartaBaja() {
        return this.datosCocoa.getPkCartaBaja();
    }

    public void setPkCartaBaja(final String pkCartaBaja) {
        this.datosCocoa.setPkCartaBaja(pkCartaBaja);
    }

    public void setPkCartaBaja(final IAlphanumericValue pkCartaBaja) {
        this.datosCocoa.setPkCartaBaja(pkCartaBaja);
    }

    // Property: datosCocoa.pkCedulaDef -> 05 PK-CEDULA-DEF X(01)
    public AlphanumericVar getPkCedulaDef() {
        return this.datosCocoa.getPkCedulaDef();
    }

    public void setPkCedulaDef(final String pkCedulaDef) {
        this.datosCocoa.setPkCedulaDef(pkCedulaDef);
    }

    public void setPkCedulaDef(final IAlphanumericValue pkCedulaDef) {
        this.datosCocoa.setPkCedulaDef(pkCedulaDef);
    }

    // Property: datosCocoa.pkEdoCta -> 05 PK-EDO-CTA X(01)
    public AlphanumericVar getPkEdoCta() {
        return this.datosCocoa.getPkEdoCta();
    }

    public void setPkEdoCta(final String pkEdoCta) {
        this.datosCocoa.setPkEdoCta(pkEdoCta);
    }

    public void setPkEdoCta(final IAlphanumericValue pkEdoCta) {
        this.datosCocoa.setPkEdoCta(pkEdoCta);
    }

    // Property: datosCocoa.pkFm2 -> 05 PK-FM2 X(01)
    public AlphanumericVar getPkFm2() {
        return this.datosCocoa.getPkFm2();
    }

    public void setPkFm2(final String pkFm2) {
        this.datosCocoa.setPkFm2(pkFm2);
    }

    public void setPkFm2(final IAlphanumericValue pkFm2) {
        this.datosCocoa.setPkFm2(pkFm2);
    }

    // Property: datosCocoa.pkSolicitud -> 05 PK-SOLICITUD X(01)
    public AlphanumericVar getPkSolicitud() {
        return this.datosCocoa.getPkSolicitud();
    }

    public void setPkSolicitud(final String pkSolicitud) {
        this.datosCocoa.setPkSolicitud(pkSolicitud);
    }

    public void setPkSolicitud(final IAlphanumericValue pkSolicitud) {
        this.datosCocoa.setPkSolicitud(pkSolicitud);
    }

    // Property: datosCocoa.pkFas2 -> 05 PK-FAS2 X(01)
    public AlphanumericVar getPkFas2() {
        return this.datosCocoa.getPkFas2();
    }

    public void setPkFas2(final String pkFas2) {
        this.datosCocoa.setPkFas2(pkFas2);
    }

    public void setPkFas2(final IAlphanumericValue pkFas2) {
        this.datosCocoa.setPkFas2(pkFas2);
    }

    // Property: datosCocoa.pkCartaArt10 -> 05 PK-CARTA-ART10 X(01)
    public AlphanumericVar getPkCartaArt10() {
        return this.datosCocoa.getPkCartaArt10();
    }

    public void setPkCartaArt10(final String pkCartaArt10) {
        this.datosCocoa.setPkCartaArt10(pkCartaArt10);
    }

    public void setPkCartaArt10(final IAlphanumericValue pkCartaArt10) {
        this.datosCocoa.setPkCartaArt10(pkCartaArt10);
    }

    // Property: datosCocoa.pkSeguroCol -> 05 PK-SEGURO-COL X(01)
    public AlphanumericVar getPkSeguroCol() {
        return this.datosCocoa.getPkSeguroCol();
    }

    public void setPkSeguroCol(final String pkSeguroCol) {
        this.datosCocoa.setPkSeguroCol(pkSeguroCol);
    }

    public void setPkSeguroCol(final IAlphanumericValue pkSeguroCol) {
        this.datosCocoa.setPkSeguroCol(pkSeguroCol);
    }

    // Property: datosCocoa.pkContrato -> 05 PK-CONTRATO X(01)
    public AlphanumericVar getPkContrato() {
        return this.datosCocoa.getPkContrato();
    }

    public void setPkContrato(final String pkContrato) {
        this.datosCocoa.setPkContrato(pkContrato);
    }

    public void setPkContrato(final IAlphanumericValue pkContrato) {
        this.datosCocoa.setPkContrato(pkContrato);
    }

    // Property: datosCocoa.pkChequePagoDer -> 05 PK-CHEQUE-PAGO-DER X(01)
    public AlphanumericVar getPkChequePagoDer() {
        return this.datosCocoa.getPkChequePagoDer();
    }

    public void setPkChequePagoDer(final String pkChequePagoDer) {
        this.datosCocoa.setPkChequePagoDer(pkChequePagoDer);
    }

    public void setPkChequePagoDer(final IAlphanumericValue pkChequePagoDer) {
        this.datosCocoa.setPkChequePagoDer(pkChequePagoDer);
    }

    // Property: datosCocoa.pkCopiaCedDef -> 05 PK-COPIA-CED-DEF X(01)
    public AlphanumericVar getPkCopiaCedDef() {
        return this.datosCocoa.getPkCopiaCedDef();
    }

    public void setPkCopiaCedDef(final String pkCopiaCedDef) {
        this.datosCocoa.setPkCopiaCedDef(pkCopiaCedDef);
    }

    public void setPkCopiaCedDef(final IAlphanumericValue pkCopiaCedDef) {
        this.datosCocoa.setPkCopiaCedDef(pkCopiaCedDef);
    }

    // Property: datosCocoa.pkCursoPade -> 05 PK-CURSO-PADE X(01)
    public AlphanumericVar getPkCursoPade() {
        return this.datosCocoa.getPkCursoPade();
    }

    public void setPkCursoPade(final String pkCursoPade) {
        this.datosCocoa.setPkCursoPade(pkCursoPade);
    }

    public void setPkCursoPade(final IAlphanumericValue pkCursoPade) {
        this.datosCocoa.setPkCursoPade(pkCursoPade);
    }

    // Property: datosCocoa.pkInvestigacion -> 05 PK-INVESTIGACION X(01)
    public AlphanumericVar getPkInvestigacion() {
        return this.datosCocoa.getPkInvestigacion();
    }

    public void setPkInvestigacion(final String pkInvestigacion) {
        this.datosCocoa.setPkInvestigacion(pkInvestigacion);
    }

    public void setPkInvestigacion(final IAlphanumericValue pkInvestigacion) {
        this.datosCocoa.setPkInvestigacion(pkInvestigacion);
    }

    // Property: datosCocoa.pkFechaPlazo -> 05 PK-FECHA-PLAZO 9(6)
    public UnsignedNumericVar getPkFechaPlazo() {
        return this.datosCocoa.getPkFechaPlazo();
    }

    public void setPkFechaPlazo(final int pkFechaPlazo) {
        this.datosCocoa.setPkFechaPlazo(pkFechaPlazo);
    }

    public void setPkFechaPlazo(final INumericValue pkFechaPlazo) {
        this.datosCocoa.setPkFechaPlazo(pkFechaPlazo);
    }

    // Property: datosCocoa.pkTipCedulaAux -> 05 PK-TIP-CEDULA-AUX X
    public AlphanumericVar getPkTipCedulaAux() {
        return this.datosCocoa.getPkTipCedulaAux();
    }

    public void setPkTipCedulaAux(final String pkTipCedulaAux) {
        this.datosCocoa.setPkTipCedulaAux(pkTipCedulaAux);
    }

    public void setPkTipCedulaAux(final IAlphanumericValue pkTipCedulaAux) {
        this.datosCocoa.setPkTipCedulaAux(pkTipCedulaAux);
    }

    // Property: datosCocoa.pkNivelNal -> 05 PK-NIVEL-NAL 99
    public UnsignedNumericVar getPkNivelNal() {
        return this.datosCocoa.getPkNivelNal();
    }

    public void setPkNivelNal(final int pkNivelNal) {
        this.datosCocoa.setPkNivelNal(pkNivelNal);
    }

    public void setPkNivelNal(final INumericValue pkNivelNal) {
        this.datosCocoa.setPkNivelNal(pkNivelNal);
    }

    // Property: datosCocoa.pkNivelProv -> 05 PK-NIVEL-PROV 99
    public UnsignedNumericVar getPkNivelProv() {
        return this.datosCocoa.getPkNivelProv();
    }

    public void setPkNivelProv(final int pkNivelProv) {
        this.datosCocoa.setPkNivelProv(pkNivelProv);
    }

    public void setPkNivelProv(final INumericValue pkNivelProv) {
        this.datosCocoa.setPkNivelProv(pkNivelProv);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.datosSaeta.initialize();
        this.datosCocoa.initialize();
    }

}

