package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tabladirecciones.TabDirecciones;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-DIRECCIONES.
 *
 */
// 455      *---------------------------------------------------------------* 
// 456      * TABLA DE DIRECCIONES DEL RECEPTOR                             * 
// 457      *---------------------------------------------------------------* 
@Setter
@Getter
public class TablaDirecciones { // VStruct
    // Properties
    private TabDirecciones tabDirecciones = new  TabDirecciones(150);        // 05 TAB-DIRECCIONES [150]

    public TablaDirecciones() {
        initialize();
    }



    public TabDirecciones getTabDirecciones(int index) {
        return this.tabDirecciones.at(index);
    }


    // Deeper properties

    // Property: tabDirecciones.tbDirOfna -> 07 TB-DIR-OFNA 9(04)
    public UnsignedNumericVar getTbDirOfna() {
        return this.tabDirecciones.getTbDirOfna();
    }

    public void setTbDirOfna(final int tbDirOfna) {
        this.tabDirecciones.setTbDirOfna(tbDirOfna);
    }

    public void setTbDirOfna(final INumericValue tbDirOfna) {
        this.tabDirecciones.setTbDirOfna(tbDirOfna);
    }

    // Property: tabDirecciones.tbDirCae -> 07 TB-DIR-CAE X(40)
    public AlphanumericVar getTbDirCae() {
        return this.tabDirecciones.getTbDirCae();
    }

    public void setTbDirCae(final String tbDirCae) {
        this.tabDirecciones.setTbDirCae(tbDirCae);
    }

    public void setTbDirCae(final IAlphanumericValue tbDirCae) {
        this.tabDirecciones.setTbDirCae(tbDirCae);
    }

    // Property: tabDirecciones.tbDirNum -> 07 TB-DIR-NUM X(05)
    public AlphanumericVar getTbDirNum() {
        return this.tabDirecciones.getTbDirNum();
    }

    public void setTbDirNum(final String tbDirNum) {
        this.tabDirecciones.setTbDirNum(tbDirNum);
    }

    public void setTbDirNum(final IAlphanumericValue tbDirNum) {
        this.tabDirecciones.setTbDirNum(tbDirNum);
    }

    // Property: tabDirecciones.tbDirInt -> 07 TB-DIR-INT X(05)
    public AlphanumericVar getTbDirInt() {
        return this.tabDirecciones.getTbDirInt();
    }

    public void setTbDirInt(final String tbDirInt) {
        this.tabDirecciones.setTbDirInt(tbDirInt);
    }

    public void setTbDirInt(final IAlphanumericValue tbDirInt) {
        this.tabDirecciones.setTbDirInt(tbDirInt);
    }

    // Property: tabDirecciones.tbDirCol -> 07 TB-DIR-COL X(40)
    public AlphanumericVar getTbDirCol() {
        return this.tabDirecciones.getTbDirCol();
    }

    public void setTbDirCol(final String tbDirCol) {
        this.tabDirecciones.setTbDirCol(tbDirCol);
    }

    public void setTbDirCol(final IAlphanumericValue tbDirCol) {
        this.tabDirecciones.setTbDirCol(tbDirCol);
    }

    // Property: tabDirecciones.tbDirMndNom -> 07 TB-DIR-MND-NOM X(50)
    public AlphanumericVar getTbDirMndNom() {
        return this.tabDirecciones.getTbDirMndNom();
    }

    public void setTbDirMndNom(final String tbDirMndNom) {
        this.tabDirecciones.setTbDirMndNom(tbDirMndNom);
    }

    public void setTbDirMndNom(final IAlphanumericValue tbDirMndNom) {
        this.tabDirecciones.setTbDirMndNom(tbDirMndNom);
    }

    // Property: tabDirecciones.tbDirPobNom -> 07 TB-DIR-POB-NOM X(40)
    public AlphanumericVar getTbDirPobNom() {
        return this.tabDirecciones.getTbDirPobNom();
    }

    public void setTbDirPobNom(final String tbDirPobNom) {
        this.tabDirecciones.setTbDirPobNom(tbDirPobNom);
    }

    public void setTbDirPobNom(final IAlphanumericValue tbDirPobNom) {
        this.tabDirecciones.setTbDirPobNom(tbDirPobNom);
    }

    // Property: tabDirecciones.tbDirMndEdoCve -> 07 TB-DIR-MND-EDO-CVE X(04)
    public AlphanumericVar getTbDirMndEdoCve() {
        return this.tabDirecciones.getTbDirMndEdoCve();
    }

    public void setTbDirMndEdoCve(final String tbDirMndEdoCve) {
        this.tabDirecciones.setTbDirMndEdoCve(tbDirMndEdoCve);
    }

    public void setTbDirMndEdoCve(final IAlphanumericValue tbDirMndEdoCve) {
        this.tabDirecciones.setTbDirMndEdoCve(tbDirMndEdoCve);
    }

    // Property: tabDirecciones.tbDirMndEdoNom -> 07 TB-DIR-MND-EDO-NOM X(30)
    public AlphanumericVar getTbDirMndEdoNom() {
        return this.tabDirecciones.getTbDirMndEdoNom();
    }

    public void setTbDirMndEdoNom(final String tbDirMndEdoNom) {
        this.tabDirecciones.setTbDirMndEdoNom(tbDirMndEdoNom);
    }

    public void setTbDirMndEdoNom(final IAlphanumericValue tbDirMndEdoNom) {
        this.tabDirecciones.setTbDirMndEdoNom(tbDirMndEdoNom);
    }

    // Property: tabDirecciones.tbDirMndPaiCve -> 07 TB-DIR-MND-PAI-CVE X(03)
    public AlphanumericVar getTbDirMndPaiCve() {
        return this.tabDirecciones.getTbDirMndPaiCve();
    }

    public void setTbDirMndPaiCve(final String tbDirMndPaiCve) {
        this.tabDirecciones.setTbDirMndPaiCve(tbDirMndPaiCve);
    }

    public void setTbDirMndPaiCve(final IAlphanumericValue tbDirMndPaiCve) {
        this.tabDirecciones.setTbDirMndPaiCve(tbDirMndPaiCve);
    }

    // Property: tabDirecciones.tbDirMndPaiNom -> 07 TB-DIR-MND-PAI-NOM X(30)
    public AlphanumericVar getTbDirMndPaiNom() {
        return this.tabDirecciones.getTbDirMndPaiNom();
    }

    public void setTbDirMndPaiNom(final String tbDirMndPaiNom) {
        this.tabDirecciones.setTbDirMndPaiNom(tbDirMndPaiNom);
    }

    public void setTbDirMndPaiNom(final IAlphanumericValue tbDirMndPaiNom) {
        this.tabDirecciones.setTbDirMndPaiNom(tbDirMndPaiNom);
    }

    // Property: tabDirecciones.tbDirCpoCve -> 07 TB-DIR-CPO-CVE 9(05)
    public UnsignedNumericVar getTbDirCpoCve() {
        return this.tabDirecciones.getTbDirCpoCve();
    }

    public void setTbDirCpoCve(final int tbDirCpoCve) {
        this.tabDirecciones.setTbDirCpoCve(tbDirCpoCve);
    }

    public void setTbDirCpoCve(final INumericValue tbDirCpoCve) {
        this.tabDirecciones.setTbDirCpoCve(tbDirCpoCve);
    }

    // Property: tabDirecciones.tbDirGerZonEnv -> 07 TB-DIR-GER-ZON-ENV 9(04)
    public UnsignedNumericVar getTbDirGerZonEnv() {
        return this.tabDirecciones.getTbDirGerZonEnv();
    }

    public void setTbDirGerZonEnv(final int tbDirGerZonEnv) {
        this.tabDirecciones.setTbDirGerZonEnv(tbDirGerZonEnv);
    }

    public void setTbDirGerZonEnv(final INumericValue tbDirGerZonEnv) {
        this.tabDirecciones.setTbDirGerZonEnv(tbDirGerZonEnv);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tabDirecciones.initialize();
    }

}

