package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: FECHA-FORMAT-I.
 *
 */
@Setter
@Getter
public class FechaFormatI extends DataStruct {
    // Properties
    private UnsignedNumericVar fechaFormatAa = new UnsignedNumericVar(this, 4, 0);           // 05 FECHA-FORMAT-AA 9(04)
    private UnsignedNumericVar fechaFormatMm = new UnsignedNumericVar(this, 2, 0);           // 05 FECHA-FORMAT-MM 9(02)
    private UnsignedNumericVar fechaFormatDd = new UnsignedNumericVar(this, 2, 0);           // 05 FECHA-FORMAT-DD 9(02)

    public FechaFormatI() {
        super();
        initialize();
    }

    public FechaFormatI(DataContainer parent) {
        super(parent);
    }

    public FechaFormatI(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFechaFormatAa(final int fechaFormatAa) {
        this.fechaFormatAa.setValue(fechaFormatAa);
    }

    public void setFechaFormatAa(final BigDecimal fechaFormatAa) {
        this.fechaFormatAa.setValue(fechaFormatAa);
    }

    public void setFechaFormatAa(final INumericValue fechaFormatAa) {
        this.fechaFormatAa.setValue(fechaFormatAa);
    }

    public void setFechaFormatMm(final int fechaFormatMm) {
        this.fechaFormatMm.setValue(fechaFormatMm);
    }

    public void setFechaFormatMm(final BigDecimal fechaFormatMm) {
        this.fechaFormatMm.setValue(fechaFormatMm);
    }

    public void setFechaFormatMm(final INumericValue fechaFormatMm) {
        this.fechaFormatMm.setValue(fechaFormatMm);
    }

    public void setFechaFormatDd(final int fechaFormatDd) {
        this.fechaFormatDd.setValue(fechaFormatDd);
    }

    public void setFechaFormatDd(final BigDecimal fechaFormatDd) {
        this.fechaFormatDd.setValue(fechaFormatDd);
    }

    public void setFechaFormatDd(final INumericValue fechaFormatDd) {
        this.fechaFormatDd.setValue(fechaFormatDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.fechaFormatAa.clear();
        this.fechaFormatMm.clear();
        this.fechaFormatDd.clear();
    }

}

