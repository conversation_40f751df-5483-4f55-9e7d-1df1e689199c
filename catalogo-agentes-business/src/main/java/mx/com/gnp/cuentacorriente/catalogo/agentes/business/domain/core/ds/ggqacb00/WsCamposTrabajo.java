package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CAMPOS-TRABAJO.
 *
 */
// 281      *---                                                              
// 282      *   DECLARACION DE VARIABLES DE TRABAJO                           
// 283      *---                                                              
@Setter
@Getter
public class WsCamposTrabajo { // VStruct
    // Properties
    private String wsCdoper = "";                                           // 05 WS-CDOPER X(10)
    private String wsOpcion = "";                                           // 05 WS-OPCION X(01)
    private int wsAgtIdr;                                                   // 05 WS-AGT-IDR S9(9) COMPUTATIONAL
    private UnsignedNumericVar wsAgtIdrAux = new UnsignedNumericVar(10, 0);  // 05 WS-AGT-IDR-AUX 9(10)
    private int wsInd_1 = 0;                                                // 05 WS-IND-1 9(05)

    public WsCamposTrabajo() {
        initialize();
    }



    public void setWsAgtIdrAux(final BigDecimal wsAgtIdrAux) {
        this.wsAgtIdrAux.setValue(wsAgtIdrAux);
    }

    public void setWsAgtIdrAux(final int wsAgtIdrAux) {
        this.wsAgtIdrAux.setValue(wsAgtIdrAux);
    }

    public void setWsAgtIdrAux(final INumericValue wsAgtIdrAux) {
        this.wsAgtIdrAux.setValue(wsAgtIdrAux);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsCdoper = "";
        this.wsOpcion = "";
        this.wsAgtIdr = 0;
        this.wsAgtIdrAux.setZero();
        this.wsInd_1 = 0;
    }

}

