package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.wstabagtreg;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TAB-AGT-R.
 *
 */
@Setter
@Getter
public class WsTabAgtR extends DataStruct {
    // Properties
    private AlphanumericVar wsTabFiller = new AlphanumericVar(this, 3);      // 15 WS-TAB-FILLER X(03)
    private AlphanumericVar wsTabAgt = new AlphanumericVar(this, 7);         // 15 WS-TAB-AGT X(07)

    public WsTabAgtR() {
        super();
        initialize();
    }

    public WsTabAgtR(DataContainer parent) {
        super(parent);
    }

    public WsTabAgtR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsTabFiller(final String wsTabFiller) {
        this.wsTabFiller.setValue(wsTabFiller);
    }

    public void setWsTabFiller(final IAlphanumericValue wsTabFiller) {
        this.wsTabFiller.setValue(wsTabFiller);
    }

    public void setWsTabAgt(final String wsTabAgt) {
        this.wsTabAgt.setValue(wsTabAgt);
    }

    public void setWsTabAgt(final IAlphanumericValue wsTabAgt) {
        this.wsTabAgt.setValue(wsTabAgt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsTabFiller.clear();
        this.wsTabAgt.clear();
    }

}

