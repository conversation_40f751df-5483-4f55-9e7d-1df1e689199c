package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: I-INDICES.
 *
 */
@Setter
@Getter
public class IIndices { // VStruct
    // Properties
    private UnsignedNumericVar i = new UnsignedNumericVar(12, 0);            // 05 I 9(12)

    public IIndices() {
        initialize();
    }



    public void setI(final BigDecimal i) {
        this.i.setValue(i);
    }

    public void setI(final int i) {
        this.i.setValue(i);
    }

    public void setI(final INumericValue i) {
        this.i.setValue(i);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.i.setZero();
    }

}

