package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tofn0_1.TOfn0Elem_1;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-OFN0-1.
 *
 */
// 204           10 T-OFN0-1    OCCURS 1 TO 255                               
@Setter
@Getter
public class TOfn0_1 extends DataStructArray<TOfn0_1> {
    // Properties
    private TOfn0Elem_1 tOfn0Elem_1 = new  TOfn0Elem_1(this);                // 15 T-OFN0-ELEM-1

    public TOfn0_1(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TOfn0_1(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TOfn0_1(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TOfn0_1(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TOfn0_1 at(int idx) {
        return new TOfn0_1(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tOfn0Elem_1.sCnaOfnCveElem -> 20 S-CNA-OFN-CVE-ELEM X(01)
    public AlphanumericVar getSCnaOfnCveElem() {
        return this.tOfn0Elem_1.getSCnaOfnCveElem();
    }

    public void setSCnaOfnCveElem(final String sCnaOfnCveElem) {
        this.tOfn0Elem_1.setSCnaOfnCveElem(sCnaOfnCveElem);
    }

    public void setSCnaOfnCveElem(final IAlphanumericValue sCnaOfnCveElem) {
        this.tOfn0Elem_1.setSCnaOfnCveElem(sCnaOfnCveElem);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tOfn0Elem_1.initialize();
    }

}

