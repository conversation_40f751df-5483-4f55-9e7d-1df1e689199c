package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: FLA0.
 *
 */
@Setter
@Getter
public class Fla0 { // VStruct
    // Properties
    private String flaFlaNum = "";                                          // 10 FLA-FLA-NUM X(08)

    public Fla0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.flaFlaNum = "";
    }

}

