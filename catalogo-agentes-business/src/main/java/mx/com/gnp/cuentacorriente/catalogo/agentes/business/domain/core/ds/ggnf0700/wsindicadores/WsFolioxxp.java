package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FOLIOXXP.
 *
 */
// 769050100*                                                                 05010002
@Setter
@Getter
public class WsFolioxxp extends DataStruct {
    // Properties
    private AlphanumericVar wsFolioxx1p = new AlphanumericVar(this, 1);      // 05 WS-FOLIOXX1P X
    private UnsignedNumericVar wsFolioxx2p = new UnsignedNumericVar(this, 7, 0);             // 05 WS-FOLIOXX2P 9(7)

    public WsFolioxxp() {
        super();
        initialize();
    }

    public WsFolioxxp(DataContainer parent) {
        super(parent);
    }

    public WsFolioxxp(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFolioxx1p(final String wsFolioxx1p) {
        this.wsFolioxx1p.setValue(wsFolioxx1p);
    }

    public void setWsFolioxx1p(final IAlphanumericValue wsFolioxx1p) {
        this.wsFolioxx1p.setValue(wsFolioxx1p);
    }

    public void setWsFolioxx2p(final int wsFolioxx2p) {
        this.wsFolioxx2p.setValue(wsFolioxx2p);
    }

    public void setWsFolioxx2p(final BigDecimal wsFolioxx2p) {
        this.wsFolioxx2p.setValue(wsFolioxx2p);
    }

    public void setWsFolioxx2p(final INumericValue wsFolioxx2p) {
        this.wsFolioxx2p.setValue(wsFolioxx2p);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFolioxx1p.clear();
        this.wsFolioxx2p.clear();
    }

}

