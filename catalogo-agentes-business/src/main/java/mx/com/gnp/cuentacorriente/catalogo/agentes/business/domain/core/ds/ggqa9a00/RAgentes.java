package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: R-AGENTES.
 *
 */
// 37      *                                                                 
@Setter
@Getter
public class RAgentes extends DataStruct {
    // Properties
    private UnsignedNumericVar rAgentesAgtIdr = new UnsignedNumericVar(this, 7, 0);          // 10 R-AGENTES-AGT-IDR 9(07)
    private NumericEditedVar rAgentesHfaPjeIva = new NumericEditedVar(this, "-9(03).9(02)");                 // 10 R-AGENTES-HFA-PJE-IVA -9(03).9(02)
    private NumericEditedVar rAgentesHfaPjeIsr = new NumericEditedVar(this, "-9(03).9(02)");                 // 10 R-AGENTES-HFA-PJE-ISR -9(03).9(02)
    private NumericEditedVar rAgentesCedNv = new NumericEditedVar(this, "-9(03).9(02)");     // 10 R-AGENTES-CED-NV -9(03).9(02)
    private NumericEditedVar rAgentesCedVi = new NumericEditedVar(this, "-9(03).9(02)");     // 10 R-AGENTES-CED-VI -9(03).9(02)
    private UnsignedNumericVar rAgentesPefCve = new UnsignedNumericVar(this, 1, 0);          // 10 R-AGENTES-PEF-CVE 9(01)
    private AlphanumericVar rAgentesCdregfis = new AlphanumericVar(this, 3);                 // 10 R-AGENTES-CDREGFIS X(03)
    private AlphanumericVar rAgentesEdoCve = new AlphanumericVar(this, 4);   // 10 R-AGENTES-EDO-CVE X(04)
    private UnsignedNumericVar rAgentesTfpCve = new UnsignedNumericVar(this, 2, 0);          // 10 R-AGENTES-TFP-CVE 9(02)
    private AlphanumericVar rAgentesFiller = new AlphanumericVar(this, 34);  // 10 R-AGENTES-FILLER X(34)

    public RAgentes() {
        super();
        initialize();
    }

    public RAgentes(DataContainer parent) {
        super(parent);
    }

    public RAgentes(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRAgentesAgtIdr(final int rAgentesAgtIdr) {
        this.rAgentesAgtIdr.setValue(rAgentesAgtIdr);
    }

    public void setRAgentesAgtIdr(final BigDecimal rAgentesAgtIdr) {
        this.rAgentesAgtIdr.setValue(rAgentesAgtIdr);
    }

    public void setRAgentesAgtIdr(final INumericValue rAgentesAgtIdr) {
        this.rAgentesAgtIdr.setValue(rAgentesAgtIdr);
    }

    public void setRAgentesHfaPjeIva(final String rAgentesHfaPjeIva) {
        this.rAgentesHfaPjeIva.setValue(rAgentesHfaPjeIva);
    }

    public void setRAgentesHfaPjeIva(final IValue rAgentesHfaPjeIva) {
        this.rAgentesHfaPjeIva.setValue(rAgentesHfaPjeIva);
    }

    public void setRAgentesHfaPjeIsr(final String rAgentesHfaPjeIsr) {
        this.rAgentesHfaPjeIsr.setValue(rAgentesHfaPjeIsr);
    }

    public void setRAgentesHfaPjeIsr(final IValue rAgentesHfaPjeIsr) {
        this.rAgentesHfaPjeIsr.setValue(rAgentesHfaPjeIsr);
    }

    public void setRAgentesCedNv(final String rAgentesCedNv) {
        this.rAgentesCedNv.setValue(rAgentesCedNv);
    }

    public void setRAgentesCedNv(final IValue rAgentesCedNv) {
        this.rAgentesCedNv.setValue(rAgentesCedNv);
    }

    public void setRAgentesCedVi(final String rAgentesCedVi) {
        this.rAgentesCedVi.setValue(rAgentesCedVi);
    }

    public void setRAgentesCedVi(final IValue rAgentesCedVi) {
        this.rAgentesCedVi.setValue(rAgentesCedVi);
    }

    public void setRAgentesPefCve(final int rAgentesPefCve) {
        this.rAgentesPefCve.setValue(rAgentesPefCve);
    }

    public void setRAgentesPefCve(final BigDecimal rAgentesPefCve) {
        this.rAgentesPefCve.setValue(rAgentesPefCve);
    }

    public void setRAgentesPefCve(final INumericValue rAgentesPefCve) {
        this.rAgentesPefCve.setValue(rAgentesPefCve);
    }

    public void setRAgentesCdregfis(final String rAgentesCdregfis) {
        this.rAgentesCdregfis.setValue(rAgentesCdregfis);
    }

    public void setRAgentesCdregfis(final IAlphanumericValue rAgentesCdregfis) {
        this.rAgentesCdregfis.setValue(rAgentesCdregfis);
    }

    public void setRAgentesEdoCve(final String rAgentesEdoCve) {
        this.rAgentesEdoCve.setValue(rAgentesEdoCve);
    }

    public void setRAgentesEdoCve(final IAlphanumericValue rAgentesEdoCve) {
        this.rAgentesEdoCve.setValue(rAgentesEdoCve);
    }

    public void setRAgentesTfpCve(final int rAgentesTfpCve) {
        this.rAgentesTfpCve.setValue(rAgentesTfpCve);
    }

    public void setRAgentesTfpCve(final BigDecimal rAgentesTfpCve) {
        this.rAgentesTfpCve.setValue(rAgentesTfpCve);
    }

    public void setRAgentesTfpCve(final INumericValue rAgentesTfpCve) {
        this.rAgentesTfpCve.setValue(rAgentesTfpCve);
    }

    public void setRAgentesFiller(final String rAgentesFiller) {
        this.rAgentesFiller.setValue(rAgentesFiller);
    }

    public void setRAgentesFiller(final IAlphanumericValue rAgentesFiller) {
        this.rAgentesFiller.setValue(rAgentesFiller);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rAgentesAgtIdr.clear();
        this.rAgentesHfaPjeIva.clear();
        this.rAgentesHfaPjeIsr.clear();
        this.rAgentesCedNv.clear();
        this.rAgentesCedVi.clear();
        this.rAgentesPefCve.clear();
        this.rAgentesCdregfis.clear();
        this.rAgentesEdoCve.clear();
        this.rAgentesTfpCve.clear();
        this.rAgentesFiller.clear();
    }

}

