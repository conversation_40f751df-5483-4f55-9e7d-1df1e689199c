package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.linea;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DET1.
 *
 */
// 64004900*                                                                 00490000
@Setter
@Getter
public class Det1 extends DataStruct {
    // Properties
    private UnsignedNumericVar d1AgtIdr = new UnsignedNumericVar(this, 9, 0);                // 05 D1-AGT-IDR 9(9)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private NumericEditedVar d1CnaNum = new NumericEditedVar(this, "ZZZ9");  // 05 D1-CNA-NUM ZZZ9
    private AlphanumericVar filler2 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private AlphanumericVar d1FlaNum = new AlphanumericVar(this, 8);         // 05 D1-FLA-NUM X(8)
    private AlphanumericVar filler3 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private AlphanumericVar d1RamCve = new AlphanumericVar(this, 5);         // 05 D1-RAM-CVE X(5)
    private AlphanumericVar filler4 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private AlphanumericVar d1SraCve = new AlphanumericVar(this, 4);         // 05 D1-SRA-CVE X(4)
    private AlphanumericVar filler5 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private AlphanumericVar d1Obs = new AlphanumericVar(this, 20);           // 05 D1-OBS X(20)
    private AlphanumericVar filler6 = new AlphanumericVar(this, 62);         // 05 FILLER X(62)

    public Det1() {
        super();
        initialize();
    }

    public Det1(DataContainer parent) {
        super(parent);
    }

    public Det1(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setD1AgtIdr(final int d1AgtIdr) {
        this.d1AgtIdr.setValue(d1AgtIdr);
    }

    public void setD1AgtIdr(final BigDecimal d1AgtIdr) {
        this.d1AgtIdr.setValue(d1AgtIdr);
    }

    public void setD1AgtIdr(final INumericValue d1AgtIdr) {
        this.d1AgtIdr.setValue(d1AgtIdr);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setD1CnaNum(final String d1CnaNum) {
        this.d1CnaNum.setValue(d1CnaNum);
    }

    public void setD1CnaNum(final IValue d1CnaNum) {
        this.d1CnaNum.setValue(d1CnaNum);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setD1FlaNum(final String d1FlaNum) {
        this.d1FlaNum.setValue(d1FlaNum);
    }

    public void setD1FlaNum(final IAlphanumericValue d1FlaNum) {
        this.d1FlaNum.setValue(d1FlaNum);
    }

    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }

    public void setD1RamCve(final String d1RamCve) {
        this.d1RamCve.setValue(d1RamCve);
    }

    public void setD1RamCve(final IAlphanumericValue d1RamCve) {
        this.d1RamCve.setValue(d1RamCve);
    }

    public void setFiller4(final String filler4) {
        this.filler4.setValue(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.filler4.setValue(filler4);
    }

    public void setD1SraCve(final String d1SraCve) {
        this.d1SraCve.setValue(d1SraCve);
    }

    public void setD1SraCve(final IAlphanumericValue d1SraCve) {
        this.d1SraCve.setValue(d1SraCve);
    }

    public void setFiller5(final String filler5) {
        this.filler5.setValue(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.filler5.setValue(filler5);
    }

    public void setD1Obs(final String d1Obs) {
        this.d1Obs.setValue(d1Obs);
    }

    public void setD1Obs(final IAlphanumericValue d1Obs) {
        this.d1Obs.setValue(d1Obs);
    }

    public void setFiller6(final String filler6) {
        this.filler6.setValue(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.filler6.setValue(filler6);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.d1AgtIdr.clear();
        this.filler1.clear();
        this.d1CnaNum.clear();
        this.filler2.clear();
        this.d1FlaNum.clear();
        this.filler3.clear();
        this.d1RamCve.clear();
        this.filler4.clear();
        this.d1SraCve.clear();
        this.filler5.clear();
        this.d1Obs.clear();
        this.filler6.clear();
    }

}

