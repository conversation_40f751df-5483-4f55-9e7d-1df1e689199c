
/**
 *  Program: Ggqac700.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa006hcpy.Cq06hReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00olcpy.AbReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00lncpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa0053cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa0068cpy.Ccpa_01Reg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00ogcpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00n9cpy.WsLinkReg;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.WsCamposTrabajo;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.W500Fecha;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.W500FechaDb2;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.A990CifCtl;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.Banderas;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.SemSwiches;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.components.Ggqa0049cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.ISysdummy1Port;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqac700 extends AbstractCobolOs390Program implements IProgram {


    // Copy file QA$CBLCPY$GGQA006H
    private Cq06hReg cq06hReg = new  Cq06hReg();                             // 01 CQ06H-REG UsedAsReferenceParameter
    // End copy file QA$CBLCPY$GGQA006H

    // Copy file QA$CBLCPY$GGQA00OL
    private AbReg abReg = new  AbReg();                                      // 01 AB-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00OL

    // Copy file QA$CBLCPY$GGQA00LN
    private Ggqa00lncpy ggqa00lncpy = new  Ggqa00lncpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00LN

    // Copy file QA$CBLCPY$GGQA0053
    private Ggqa0053cpy ggqa0053cpy = new  Ggqa0053cpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA0053

    // Auto variables
//  private int ccI;                                // CC-I // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GGQA0068
    private Ccpa_01Reg ccpa_01Reg = new  Ccpa_01Reg();                       // 01 CCPA-01-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA0068

    // Copy file QA$CBLCPY$GGQA00OG
    private Ggqa00ogcpy ggqa00ogcpy = new  Ggqa00ogcpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00OG

    // Auto variables
//  private int t999MdI;                            // T999-MD-I // [@WARNING variable NOT USED]
    private IndexVar iError = new IndexVar();        // I-ERROR

    // Copy file QA$CBLCPY$GCQTHFO0
//  private Dclgcqthfo0 dclgcqthfo0 = new  Dclgcqthfo0();                        // 01 DCLGCQTHFO0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GCQTHFO0

    // Copy file QA$CBLCPY$GGQA00N9
    private WsLinkReg wsLinkReg = new  WsLinkReg();                          // 01 WS-LINK-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00N9

    // Level 1
    private AlphanumericVar w000Prog = new AlphanumericVar(8);               // 01 W000-PROG X(08)
//  private String w000Fs = "";                                             // 01 W000-FS XX // [@WARNING variable NOT USED]
    private WsCamposTrabajo wsCamposTrabajo = new  WsCamposTrabajo();        // 01 WS-CAMPOS-TRABAJO
    private W500Fecha w500Fecha = new  W500Fecha();                          // 01 W500-FECHA
    private W500FechaDb2 w500FechaDb2 = new  W500FechaDb2();                 // 01 W500-FECHA-DB2 UsedAsParameter
    private A990CifCtl a990CifCtl = new  A990CifCtl();                       // 01 A990-CIF-CTL
    private NumericEditedVar a990Cifra = new NumericEditedVar("ZZZ,ZZZ,ZZ9-");               // 01 A990-CIFRA ZZZ,ZZZ,ZZ9-
    private String w000Select = "SELECT";                                   // 01 W000-SELECT X(06)
    private String w000Insert = "INSERT";                                   // 01 W000-INSERT X(06)
    private String w000Update = "UPDATE";                                   // 01 W000-UPDATE X(06)
//  private String w000Delete = "DELETE";                                   // 01 W000-DELETE X(06) // [@WARNING variable NOT USED]
//  private String w000Consulta = "CONSULTA";                               // 01 W000-CONSULTA X(08) // [@WARNING variable NOT USED]
//  private String w000OpcionOpen = "OPEN";                                 // 01 W000-OPCION-OPEN X(04) // [@WARNING variable NOT USED]
//  private String w000OpcionFetch = "FETCH";                               // 01 W000-OPCION-FETCH X(05) // [@WARNING variable NOT USED]
//  private String w000OpcionClose = "CLOSE";                               // 01 W000-OPCION-CLOSE X(05) // [@WARNING variable NOT USED]
    private String w000ParamP01 = "P01";                                    // 01 W000-PARAM-P01 X(03)
//  private int w000Sqlcode_803 = -803;                                     // 01 W000-SQLCODE-803 S9(04) COMPUTATIONAL-3 // [@WARNING variable NOT USED]
    private String w000HfoRfcRcp = "GNP9211244P0";                          // 01 W000-HFO-RFC-RCP X(12)
    private String w000HfoNomRcp = "GRUPO NACIONAL PROVINCIAL S.A.B.";      // 01 W000-HFO-NOM-RCP X(100)
//  private String w000RegAct = "1";                                        // 01 W000-REG-ACT X(01) // [@WARNING variable NOT USED]
//  private String w000RegNoact = "0";                                      // 01 W000-REG-NOACT X(01) // [@WARNING variable NOT USED]
    private String w000UsuCve = "********";                                 // 01 W000-USU-CVE X(08)
    private Banderas banderas = new  Banderas();                             // 01 BANDERAS
    private SemSwiches semSwiches = new  SemSwiches();                       // 01 SEM-SWICHES

    private Sqlca sqlca;
    // Files
    private ISequentialFile catDirecOficina;


    // Copy routines
    private Ggqa0049cpy ggqa0049cpy;


    // Declare Sql Ports
    private ISysdummy1Port sysdummy1Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;

    public Ggqac700(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();

        // Copy routines
        ggqa0049cpy = new Ggqa0049cpy(this, ggqa00lncpy, abReg, w000Prog);

        initialize();
        initFiles();
    }

    // Initial values
    @Override
    public void initialize() {
        this.w000Prog.setValue("GGQAC700");
        this.w000Select = "SELECT";
        this.w000Insert = "INSERT";
        this.w000Update = "UPDATE";
//        this.w000Delete = "DELETE"; // [@WARNING variable NOT USED]
//        this.w000Consulta = "CONSULTA"; // [@WARNING variable NOT USED]
//        this.w000OpcionOpen = "OPEN"; // [@WARNING variable NOT USED]
//        this.w000OpcionFetch = "FETCH"; // [@WARNING variable NOT USED]
//        this.w000OpcionClose = "CLOSE"; // [@WARNING variable NOT USED]
        this.w000ParamP01 = "P01";
//        this.w000Sqlcode_803 = -803; // [@WARNING variable NOT USED]
        this.w000HfoRfcRcp = "GNP9211244P0";
        this.w000HfoNomRcp = "GRUPO NACIONAL PROVINCIAL S.A.B.";
//        this.w000RegAct = "1"; // [@WARNING variable NOT USED]
//        this.w000RegNoact = "0"; // [@WARNING variable NOT USED]
        this.w000UsuCve = "********";
    }


    public void initFiles() {

        this.catDirecOficina = createSequentialFile("CAT-DIREC-OFICINA", "GGQAC7I1", cq06hReg);

        this.catDirecOficina.setRecordLength(260);
        this.catDirecOficina.onChangeFileStatus(status -> banderas.setStCatDirec(status));

    }

    public void run() {
        p001Proceso();
    }

    /**
     *  Section: 001-PROCESO
     *  Paragraph: unnamedProcedure.
     */
    void p001Proceso() {
        p100Inicio();
        p200Proceso();
        p999Termina();
    }

    /**
     *  Section: 100-INICIO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA INICIO DE PROGRAMA
     */
    void p100Inicio() {
        p110InicializaCampos();
        p120LeeParametroP01();
        p130AbreArchivoOfn();
        p210LeeArchivoOfn();
    }

    /**
     *  Section: 110-INICIALIZA-CAMPOS
     *  Paragraph: unnamedProcedure.
     *
     * INICIALIZA CAMPOS A UTILIZAR
     */
    void p110InicializaCampos() {
        wsCamposTrabajo.initialize();
        wsLinkReg.initialize();
        w500Fecha.initialize();
        w500FechaDb2.initialize();
        a990CifCtl.initialize();
        banderas.setWsFinCatDirecc(0);

        // --SEMAFORO -- NO ES CAMPO DIFERENTE --                           
        semSwiches.setSwNoCampoDifer();
    }

    /**
     *  Section: 120-LEE-PARAMETRO-P01
     *  Paragraph: unnamedProcedure.
     *
     * RUTINA DE LECTURA DEL PARAMETRO P01
     */
    void p120LeeParametroP01() {
        wsCamposTrabajo.setS999Err("0");
        ccpa_01Reg.setSpaces();
        ccpa_01Reg.setCcpa_01CParam(w000ParamP01);
        runProgram("GGQAIE00", ccpa_01Reg, 
                wsCamposTrabajo.getS999Err());
        if (wsCamposTrabajo.getS999Err().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("PARAMETRO P01 NO EXISTE");
            abReg.setAbProceso("120-LEE-PARAMETRO-P01");
            ggqa0049cpy.p980Aborta();
        }
        w500Fecha.setValue(ccpa_01Reg.getCcpa_01Fhproc());
        w500FechaDb2.setW500AnoDb2(w500Fecha.getW500Ano());
        w500FechaDb2.setW500MesDb2(w500Fecha.getW500Mes());
        w500FechaDb2.setW500DiaDb2(w500Fecha.getW500Dia());
    }

    /**
     *  Section: 130-ABRE-ARCHIVO-OFN
     *  Paragraph: unnamedProcedure.
     *
     * ABRE EL ARCHIVO DE CATALOGO DE DIRECCIONES
     */
    void p130AbreArchivoOfn() {
        catDirecOficina.openInput();
        if (!banderas.getStCatDirec().equals("00") 
            && !banderas.getStCatDirec().equals("97")) {
            //         SET SW-SI-FIN-ARCH-OFN  TO TRUE                          
            banderas.setWsFinCatDirecc(1);

            abReg.getAbMensaje().at(1).setValue("CAT-DIREC-OFICINA  ");
            abReg.setAbProceso("130-ABRE-ARCHIVO-OFN");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 200-PROCESO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA REGISTRO
     */
    void p200Proceso() {
        while (!(banderas.isFinCatDirecc())) {
                p300BuscaRegHfo();
                p210LeeArchivoOfn();
        }
    }

    /**
     *  Section: 210-LEE-ARCHIVO-OFN
     *  Paragraph: unnamedProcedure.
     *
     * RUTINA PARA LEER EL CATALOGO DE DIRECCIONES DEL RECPTOE   *
     */
    void p210LeeArchivoOfn() {
        catDirecOficina.read();
        if(catDirecOficina.isEof()) {
            banderas.setWsFinCatDirecc(1);

        }
        if (!banderas.isFinCatDirecc()) {
            a990CifCtl.setA990RegLeiOfnA1(a990CifCtl.getA990RegLeiOfnA1() + 1);
        }
    }

    /**
     *  Section: 300-BUSCA-REG-HFO
     *  Paragraph: unnamedProcedure.
     *
     * BUSCA REGISTRO EN LA ENTIDAD HFO
     */
    void p300BuscaRegHfo() {
        wsLinkReg.initialize();
        semSwiches.setSwNoCampoDifer();
        wsLinkReg.setLhfoOpcion(w000Select);
        wsLinkReg.getOfnCve().setValue(cq06hReg.getCq06hOfna());
        runProgram("GGQAF900", wsLinkReg);
        if (wsLinkReg.getLhfoSwErr().isEqual("0")) {
            p310MueveValoresHfo();
            p320ComparaCampos();
            if (semSwiches.isSwSiCampoDifer()) {
                p330MueveValoresInsert();
                p340CalculaFechasProc();
                p999ActualizaControlHfo();
                p350InsertHfo();
            } else {
                p360ObtFecFinPro();
                p370UpdateHfo();
            }
        } else if (wsLinkReg.getLhfoSwErr().isEqual("1")) {
            semSwiches.setSwSiPrimeraVez();
            p330MueveValoresInsert();
            p340CalculaFechasProc();
            p999ActualizaControlHfo();
            p350InsertHfo();
        }
    }

    /**
     *  Section: 310-MUEVE-VALORES-HFO
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE VALORES RECUPERADOS DE LA RUTINA HFO0
     */
    void p310MueveValoresHfo() {
        wsCamposTrabajo.getWsHfoReg().setValue(wsLinkReg.getLhfoReg());
    }

    /**
     *  Section: 320-COMPARA-CAMPOS
     *  Paragraph: unnamedProcedure.
     *
     * COMPARA VALORES DEL ARCHIVO DE OFN CON LA ENTIDAD HFO
     */
    void p320ComparaCampos() {
        if (!cq06hReg.getCq06hCae().isEqual(wsCamposTrabajo.getWsHfoOfnCae()) 
            || !cq06hReg.getCq06hNum().isEqual(wsCamposTrabajo.getWsHfoOfnNum()) 
            || !cq06hReg.getCq06hInt().isEqual(wsCamposTrabajo.getWsHfoOfnInt()) 
            || !cq06hReg.getCq06hCol().isEqual(wsCamposTrabajo.getWsHfoOfnCol()) 
            || !cq06hReg.getCq06hMndNom().isEqual(wsCamposTrabajo.getWsHfoMndNom()) 
            || !cq06hReg.getCq06hMndEdoNom().isEqual(wsCamposTrabajo.getWsHfoEdoNom()) 
            || !cq06hReg.getCq06hMndPaiNom().isEqual(wsCamposTrabajo.getWsHfoPaiNom()) 
            || !cq06hReg.getCq06hCpoCve().isEqual(wsCamposTrabajo.getWsHfoCpoCve())) {
            semSwiches.setSwSiCampoDifer();
        }
    }

    /**
     *  Section: 330-MUEVE-VALORES-INSERT
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE VALORES PARA INSERTAR
     */
    void p330MueveValoresInsert() {
        wsLinkReg.initialize();
        wsLinkReg.getOfnCve().setValue(cq06hReg.getCq06hOfna());
        wsLinkReg.setHfoRfcRcp(w000HfoRfcRcp);
        wsLinkReg.setHfoNomRcp(w000HfoNomRcp);
        wsLinkReg.setOfnCae(cq06hReg.getCq06hCae());
        wsLinkReg.setOfnNum(cq06hReg.getCq06hNum());
        wsLinkReg.setOfnInt(cq06hReg.getCq06hInt());
        wsLinkReg.setOfnCol(cq06hReg.getCq06hCol()); // [@WARNING overflow ofnCol(30) <- cq06hCol(40)]
        wsLinkReg.setMndNom(cq06hReg.getCq06hMndNom());
        wsLinkReg.setEdoNom(cq06hReg.getCq06hMndEdoNom());
        wsLinkReg.setPaiNom(cq06hReg.getCq06hMndPaiNom());
        wsLinkReg.setCpoCve(cq06hReg.getCq06hCpoCve());
    }

    /**
     *  Section: 340-CALCULA-FECHAS-PROC
     *  Paragraph: unnamedProcedure.
     *
     * CALCULA FECHAS DE PERIODOS PARA EL INSERT
     */
    void p340CalculaFechasProc() {
        if (semSwiches.isSwSiPrimeraVez()) {
            wsLinkReg.setHfoFecIniPdo(w500FechaDb2);
            wsLinkReg.setHfoFecFinPdo(w500FechaDb2);
        } else {
            // --ADICIONA UN DIA A LA FECHA FIN DEL PERIODO SI NO ES            
            // --PRIMERA VEZ                                                    
            wsCamposTrabajo.setWsFecHfo(wsCamposTrabajo.getWsHfoFecFinPdo().toStringValue());

            genericModel = 
                sysdummy1Port.select_1(
                    GenericModel.builder().values(List.of(
                        toHostString(wsCamposTrabajo.getWsFecHfo())))
                    .build());

            if(genericModel.isPresent()) {
                wsCamposTrabajo.setWsFecMas_1(fromHostString(genericModel.get().values().get(0)));
            }
            wsLinkReg.setHfoFecIniPdo(wsCamposTrabajo.getWsFecMas_1());
            //        MOVE WS-FEC-MAS-1       TO HFO-FEC-FIN-PDO OF  LHFO-REG   
            wsLinkReg.setHfoFecFinPdo(w500FechaDb2);
        }
    }

    /**
     *  Section: 350-INSERT-HFO
     *  Paragraph: unnamedProcedure.
     *
     * INSERTA REGISTRO EN LA ENTIDAD HFO0
     */
    void p350InsertHfo() {
        wsLinkReg.setLhfoOpcion(w000Insert);
        runProgram("GGQAF900", wsLinkReg);
        if (wsLinkReg.getLhfoSwErr().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("ERROR INSERT - RUTINA GGQAF900");
            abReg.setAbProceso("350-INSERT-HFO");
            ggqa0049cpy.p980Aborta();
        } else {
            a990CifCtl.setA990RegLeiInsHfo(a990CifCtl.getA990RegLeiInsHfo() + 1);
            semSwiches.setSwNoPrimeraVez();
        }
    }

    /**
     *  Section: 360-OBT-FEC-FIN-PRO
     *  Paragraph: unnamedProcedure.
     *
     * OBTIENE FECHA FIN DE PROCESO PARA UPDATE
     */
    void p360ObtFecFinPro() {
        if (wsCamposTrabajo.getWsHfoFecFinPdo().compareTo(w500FechaDb2) <= 0) {
            wsLinkReg.setHfoFecFinPdo(w500FechaDb2);
        } else {
            wsCamposTrabajo.setWsFecHfo(wsCamposTrabajo.getWsHfoFecFinPdo().toStringValue());

            genericModel = 
                sysdummy1Port.select_1(
                    GenericModel.builder().values(List.of(
                        toHostString(wsCamposTrabajo.getWsFecHfo())))
                    .build());

            if(genericModel.isPresent()) {
                wsCamposTrabajo.setWsFecMas_1(fromHostString(genericModel.get().values().get(0)));
            }
            wsLinkReg.setHfoFecFinPdo(wsCamposTrabajo.getWsFecMas_1());
        }
    }

    /**
     *  Section: 370-UPDATE-HFO
     *  Paragraph: unnamedProcedure.
     *
     * ACTUALIZA REGISTRO DE LA ENTIDAD HFO0
     */
    void p370UpdateHfo() {
        wsLinkReg.setLhfoOpcion(w000Update);
        wsLinkReg.setOfnCve(wsCamposTrabajo.getWsHfoOfnCve());
        wsLinkReg.setHfoFecIniPdo(wsCamposTrabajo.getWsHfoFecIniPdo());
        runProgram("GGQAF900", wsLinkReg);
        if (wsLinkReg.getLhfoSwErr().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("ERROR UPDATE - RUTINA GGQAF900");
            abReg.setAbProceso("370-UPDATE-HFO");
            ggqa0049cpy.p980Aborta();
        } else {
            a990CifCtl.setA990RegLeiUpdHfo(a990CifCtl.getA990RegLeiUpdHfo() + 1);
        }
    }

    /**
     *  Section: 999-ACTUALIZA-CONTROL-HFO
     *  Paragraph: unnamedProcedure.
     *
     * ACTUALIZAR CONTROL DE LA ENTIDAD HFO0
     */
    void p999ActualizaControlHfo() {
        wsLinkReg.setFecUltAct(w500FechaDb2);
        wsLinkReg.setNomPrg(w000Prog);
        wsLinkReg.setUsuCve(w000UsuCve);
        runProgram("GGQA2000", wsLinkReg.getFecUltAct());
    }

    /**
     *  Section: 990-CIF-CTL
     *  Paragraph: unnamedProcedure.
     *
     * CIFRAS CONTROL
     */
    void p990CifCtl() {
        display(w000Prog, "********** CIFRAS DE CONTROL **********");
        display("FECHA PARAMETRO P01              : ", w500FechaDb2);
        a990Cifra.setValue(a990CifCtl.getA990RegLeiOfnA1());
        display("REG LEI ARCHIVO OFICINAS         (", a990Cifra, ")");
        a990Cifra.setValue(a990CifCtl.getA990RegLeiInsHfo());
        display("REG LEI INSERTADOS EN HFO0       (", a990Cifra, ")");
        a990Cifra.setValue(a990CifCtl.getA990RegLeiUpdHfo());
        display("REG LEI MODIFICADOS EN HFO0      (", a990Cifra, ")");
        display(w000Prog, "********FIN DE CIFRAS CONTROL**********");
        display("                                                 ");
    }

    /**
     *  Section: 999-TERMINA
     *  Paragraph: unnamedProcedure.
     *
     * TERMINA PROGRAMA
     */
    void p999Termina() {
        p990CifCtl();
        throw new StopRunException();
    }

    /**
     *  Section: 999-VER-ERR-SQL
     *  Paragraph: unnamedProcedure.
     * EXEC SQL
     * INCLUDE GGQA00OI
     * END-EXEC.
     * AHCPSQLE: RUTINA PARA EL MANEJO DEL SQL ERROR
     * AUTOR: SOFTTEK/CASE/TEN  17/NOV/95
     * TEN 1.50 CAF: 002
     * SE ADICIONAN CON EL PARRAFO 999-VER-SQL-ERR
     * (ES UN COPY FISICO, QUE SE ADICIONA POR EL CONVERTIDOR DE
     * LENGUAJE)
     * REFERENCIAS:                AHCWSQLE
     */
    void p999VerErrSql() {
        ggqa0053cpy.setBdErr("0");
        if (sqlca.getSqlcode() == 100) {
            ggqa0053cpy.setBdErr("1");
        } else if (sqlca.getSqlcode() != 0) {
            p999DespliegaSqlMsg();
            abReg.setAbCodigon(sqlca.getSqlcode());
            abReg.setAbProceso("999-VER-SQL-ERR");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 999-DESPLIEGA-SQL-MSG
     *  Paragraph: unnamedProcedure.
     *
     */
    void p999DespliegaSqlMsg() {
        display("ERROR SQL; -- <INICIO DESCRIPCION>: ");
        runProgram("DSNTIAR", sqlca, 
                ggqa00ogcpy.getW999ErrorMensaje(), 
                ggqa00ogcpy.getW999ErrorTextLong());
        ggqa00ogcpy.setW999ContadorLng(1);
        while (!(ggqa00ogcpy.getW999ContadorLng().toInt() > 10)) {
                iError.setValue(ggqa00ogcpy.getW999ContadorLng());
                display("--", ggqa00ogcpy.getW999ErrorText().at(iError));
                ggqa00ogcpy.getW999ContadorLng().add(1);
        }
        display("ERROR SQL; -- <FIN DESCRIPCION>");
    }

    @Autowired
    public void setSysdummy1Port(ISysdummy1Port sysdummy1Port) {
        this.sysdummy1Port = sysdummy1Port;
        this.sysdummy1Port.setProgramContext(getProgramContext());
    }
}
