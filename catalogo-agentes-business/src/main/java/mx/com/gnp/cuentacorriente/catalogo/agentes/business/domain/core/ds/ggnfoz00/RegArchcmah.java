package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-ARCHCMAH.
 *
 */
@Setter
@Getter
public class RegArchcmah extends DataStruct {
    // Properties
    private UnsignedNumericVar ccmCve4 = new UnsignedNumericVar(this, 5, 0);                 // 05 CCM-CVE4 9(05)
    private UnsignedNumericVar agtIdr4 = new UnsignedNumericVar(this, 9, 0);                 // 05 AGT-IDR4 9(09)
    private AlphanumericVar agtFecCamRazSo4 = new AlphanumericVar(this, 10);                 // 05 AGT-FEC-CAM-RAZ-SO4 X(10)
    private UnsignedNumericVar dmaIdr4 = new UnsignedNumericVar(this, 5, 0);                 // 05 DMA-IDR4 9(05)
    private AlphanumericVar cmaDes4 = new AlphanumericVar(this, 60);         // 05 CMA-DES4 X(60)
    private AlphanumericVar essRek4 = new AlphanumericVar(this, 1);          // 05 ESS-REK4 X(01)

    public RegArchcmah() {
        super();
        initialize();
    }

    public RegArchcmah(DataContainer parent) {
        super(parent);
    }

    public RegArchcmah(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setCcmCve4(final int ccmCve4) {
        this.ccmCve4.setValue(ccmCve4);
    }

    public void setCcmCve4(final BigDecimal ccmCve4) {
        this.ccmCve4.setValue(ccmCve4);
    }

    public void setCcmCve4(final INumericValue ccmCve4) {
        this.ccmCve4.setValue(ccmCve4);
    }

    public void setAgtIdr4(final int agtIdr4) {
        this.agtIdr4.setValue(agtIdr4);
    }

    public void setAgtIdr4(final BigDecimal agtIdr4) {
        this.agtIdr4.setValue(agtIdr4);
    }

    public void setAgtIdr4(final INumericValue agtIdr4) {
        this.agtIdr4.setValue(agtIdr4);
    }

    public void setAgtFecCamRazSo4(final String agtFecCamRazSo4) {
        this.agtFecCamRazSo4.setValue(agtFecCamRazSo4);
    }

    public void setAgtFecCamRazSo4(final IAlphanumericValue agtFecCamRazSo4) {
        this.agtFecCamRazSo4.setValue(agtFecCamRazSo4);
    }

    public void setDmaIdr4(final int dmaIdr4) {
        this.dmaIdr4.setValue(dmaIdr4);
    }

    public void setDmaIdr4(final BigDecimal dmaIdr4) {
        this.dmaIdr4.setValue(dmaIdr4);
    }

    public void setDmaIdr4(final INumericValue dmaIdr4) {
        this.dmaIdr4.setValue(dmaIdr4);
    }

    public void setCmaDes4(final String cmaDes4) {
        this.cmaDes4.setValue(cmaDes4);
    }

    public void setCmaDes4(final IAlphanumericValue cmaDes4) {
        this.cmaDes4.setValue(cmaDes4);
    }

    public void setEssRek4(final String essRek4) {
        this.essRek4.setValue(essRek4);
    }

    public void setEssRek4(final IAlphanumericValue essRek4) {
        this.essRek4.setValue(essRek4);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.ccmCve4.clear();
        this.agtIdr4.clear();
        this.agtFecCamRazSo4.clear();
        this.dmaIdr4.clear();
        this.cmaDes4.clear();
        this.essRek4.clear();
    }

}

