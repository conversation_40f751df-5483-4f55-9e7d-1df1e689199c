package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-SALISR.
 *
 */
@Setter
@Getter
public class RegSalisr extends DataStruct {
    // Properties
    private AlphanumericVar wsSalwfAgtIdr = new AlphanumericVar(this, 9);    // 05 WS-SALWF-AGT-IDR X(09)
    private AlphanumericVar wsSalwfFecIniPdo = new AlphanumericVar(this, 10);                // 05 WS-SALWF-FEC-INI-PDO X(10)
    private AlphanumericVar wsSalwfFecFinPdo = new AlphanumericVar(this, 10);                // 05 WS-SALWF-FEC-FIN-PDO X(10)
    private AlphanumericVar wsSalwfEsaCve = new AlphanumericVar(this, 6);    // 05 WS-SALWF-ESA-CVE X(06)
    private AlphanumericVar wsSalwfPefCve = new AlphanumericVar(this, 6);    // 05 WS-SALWF-PEF-CVE X(06)
    private AlphanumericVar wsSalwfOfnCve = new AlphanumericVar(this, 6);    // 05 WS-SALWF-OFN-CVE X(06)
    private AlphanumericVar wsSalwfCnaGerZonEnv = new AlphanumericVar(this, 6);              // 05 WS-SALWF-CNA-GER-ZON-ENV X(06)
    private AlphanumericVar wsSalwfDsgCve = new AlphanumericVar(this, 6);    // 05 WS-SALWF-DSG-CVE X(06)
    private AlphanumericVar wsSalwfAgtRfc = new AlphanumericVar(this, 13);   // 05 WS-SALWF-AGT-RFC X(13)
    private AlphanumericVar wsSalwfNomEmi = new AlphanumericVar(this, 100);  // 05 WS-SALWF-NOM-EMI X(100)
    private AlphanumericVar wsSalwfDmaCae = new AlphanumericVar(this, 40);   // 05 WS-SALWF-DMA-CAE X(40)
    private AlphanumericVar wsSalwfDmaNum = new AlphanumericVar(this, 5);    // 05 WS-SALWF-DMA-NUM X(05)
    private AlphanumericVar wsSalwfDmaInt = new AlphanumericVar(this, 5);    // 05 WS-SALWF-DMA-INT X(05)
    private AlphanumericVar wsSalwfDmaCol = new AlphanumericVar(this, 40);   // 05 WS-SALWF-DMA-COL X(40)
    private AlphanumericVar wsSalwfMndNom = new AlphanumericVar(this, 50);   // 05 WS-SALWF-MND-NOM X(50)
    private AlphanumericVar wsSalwfEdoNom = new AlphanumericVar(this, 30);   // 05 WS-SALWF-EDO-NOM X(30)
    private AlphanumericVar wsSalwfPaiNom = new AlphanumericVar(this, 30);   // 05 WS-SALWF-PAI-NOM X(30)
    private AlphanumericVar wsSalwfCpoCve = new AlphanumericVar(this, 10);   // 05 WS-SALWF-CPO-CVE X(10)
    private AlphanumericVar wsSalwfMteCve = new AlphanumericVar(this, 6);    // 05 WS-SALWF-MTE-CVE X(06)
    private AlphanumericVar wsSalwfTfpCve = new AlphanumericVar(this, 6);    // 05 WS-SALWF-TFP-CVE X(06)
    private AlphanumericVar wsSalwfCtbNumCta = new AlphanumericVar(this, 18);                // 05 WS-SALWF-CTB-NUM-CTA X(18)
    private AlphanumericVar wsSalwfIndPag = new AlphanumericVar(this, 1);    // 05 WS-SALWF-IND-PAG X(01)
    private AlphanumericVar wsSalwfIndExc = new AlphanumericVar(this, 1);    // 05 WS-SALWF-IND-EXC X(01)
    private AlphanumericVar wsSalwfUsuCve = new AlphanumericVar(this, 8);    // 05 WS-SALWF-USU-CVE X(08)
    private AlphanumericVar wsSalwfNomPrg = new AlphanumericVar(this, 8);    // 05 WS-SALWF-NOM-PRG X(08)
    private AlphanumericVar wsSalwfFecUltAct = new AlphanumericVar(this, 26);                // 05 WS-SALWF-FEC-ULT-ACT X(26)
    private NumericVar wsSalwfPjeIva = new NumericVar(this, 5, 2);           // 05 WS-SALWF-PJE-IVA S9(3)V9(2)
    private NumericVar wsSalwfPjeIsr = new NumericVar(this, 5, 2);           // 05 WS-SALWF-PJE-ISR S9(3)V9(2)
    private AlphanumericVar wsSalwfCdregfis = new AlphanumericVar(this, 3);  // 05 WS-SALWF-CDREGFIS X(03)
    private AlphanumericVar wsSalwfNomrznso = new AlphanumericVar(this, 300);                // 05 WS-SALWF-NOMRZNSO X(300)
    private AlphanumericVar wsSalwfVerCfd = new AlphanumericVar(this, 6);    // 05 WS-SALWF-VER-CFD X(06)
    private NumericVar wsSalwfCedNvi = new NumericVar(this, 5, 2);           // 05 WS-SALWF-CED-NVI S9(3)V9(2)
    private NumericVar wsSalwfCedVid = new NumericVar(this, 5, 2);           // 05 WS-SALWF-CED-VID S9(3)V9(2)

    public RegSalisr() {
        super();
        initialize();
    }

    public RegSalisr(DataContainer parent) {
        super(parent);
    }

    public RegSalisr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsSalwfAgtIdr(final String wsSalwfAgtIdr) {
        this.wsSalwfAgtIdr.setValue(wsSalwfAgtIdr);
    }

    public void setWsSalwfAgtIdr(final IAlphanumericValue wsSalwfAgtIdr) {
        this.wsSalwfAgtIdr.setValue(wsSalwfAgtIdr);
    }

    public void setWsSalwfFecIniPdo(final String wsSalwfFecIniPdo) {
        this.wsSalwfFecIniPdo.setValue(wsSalwfFecIniPdo);
    }

    public void setWsSalwfFecIniPdo(final IAlphanumericValue wsSalwfFecIniPdo) {
        this.wsSalwfFecIniPdo.setValue(wsSalwfFecIniPdo);
    }

    public void setWsSalwfFecFinPdo(final String wsSalwfFecFinPdo) {
        this.wsSalwfFecFinPdo.setValue(wsSalwfFecFinPdo);
    }

    public void setWsSalwfFecFinPdo(final IAlphanumericValue wsSalwfFecFinPdo) {
        this.wsSalwfFecFinPdo.setValue(wsSalwfFecFinPdo);
    }

    public void setWsSalwfEsaCve(final String wsSalwfEsaCve) {
        this.wsSalwfEsaCve.setValue(wsSalwfEsaCve);
    }

    public void setWsSalwfEsaCve(final IAlphanumericValue wsSalwfEsaCve) {
        this.wsSalwfEsaCve.setValue(wsSalwfEsaCve);
    }

    public void setWsSalwfPefCve(final String wsSalwfPefCve) {
        this.wsSalwfPefCve.setValue(wsSalwfPefCve);
    }

    public void setWsSalwfPefCve(final IAlphanumericValue wsSalwfPefCve) {
        this.wsSalwfPefCve.setValue(wsSalwfPefCve);
    }

    public void setWsSalwfOfnCve(final String wsSalwfOfnCve) {
        this.wsSalwfOfnCve.setValue(wsSalwfOfnCve);
    }

    public void setWsSalwfOfnCve(final IAlphanumericValue wsSalwfOfnCve) {
        this.wsSalwfOfnCve.setValue(wsSalwfOfnCve);
    }

    public void setWsSalwfCnaGerZonEnv(final String wsSalwfCnaGerZonEnv) {
        this.wsSalwfCnaGerZonEnv.setValue(wsSalwfCnaGerZonEnv);
    }

    public void setWsSalwfCnaGerZonEnv(final IAlphanumericValue wsSalwfCnaGerZonEnv) {
        this.wsSalwfCnaGerZonEnv.setValue(wsSalwfCnaGerZonEnv);
    }

    public void setWsSalwfDsgCve(final String wsSalwfDsgCve) {
        this.wsSalwfDsgCve.setValue(wsSalwfDsgCve);
    }

    public void setWsSalwfDsgCve(final IAlphanumericValue wsSalwfDsgCve) {
        this.wsSalwfDsgCve.setValue(wsSalwfDsgCve);
    }

    public void setWsSalwfAgtRfc(final String wsSalwfAgtRfc) {
        this.wsSalwfAgtRfc.setValue(wsSalwfAgtRfc);
    }

    public void setWsSalwfAgtRfc(final IAlphanumericValue wsSalwfAgtRfc) {
        this.wsSalwfAgtRfc.setValue(wsSalwfAgtRfc);
    }

    public void setWsSalwfNomEmi(final String wsSalwfNomEmi) {
        this.wsSalwfNomEmi.setValue(wsSalwfNomEmi);
    }

    public void setWsSalwfNomEmi(final IAlphanumericValue wsSalwfNomEmi) {
        this.wsSalwfNomEmi.setValue(wsSalwfNomEmi);
    }

    public void setWsSalwfDmaCae(final String wsSalwfDmaCae) {
        this.wsSalwfDmaCae.setValue(wsSalwfDmaCae);
    }

    public void setWsSalwfDmaCae(final IAlphanumericValue wsSalwfDmaCae) {
        this.wsSalwfDmaCae.setValue(wsSalwfDmaCae);
    }

    public void setWsSalwfDmaNum(final String wsSalwfDmaNum) {
        this.wsSalwfDmaNum.setValue(wsSalwfDmaNum);
    }

    public void setWsSalwfDmaNum(final IAlphanumericValue wsSalwfDmaNum) {
        this.wsSalwfDmaNum.setValue(wsSalwfDmaNum);
    }

    public void setWsSalwfDmaInt(final String wsSalwfDmaInt) {
        this.wsSalwfDmaInt.setValue(wsSalwfDmaInt);
    }

    public void setWsSalwfDmaInt(final IAlphanumericValue wsSalwfDmaInt) {
        this.wsSalwfDmaInt.setValue(wsSalwfDmaInt);
    }

    public void setWsSalwfDmaCol(final String wsSalwfDmaCol) {
        this.wsSalwfDmaCol.setValue(wsSalwfDmaCol);
    }

    public void setWsSalwfDmaCol(final IAlphanumericValue wsSalwfDmaCol) {
        this.wsSalwfDmaCol.setValue(wsSalwfDmaCol);
    }

    public void setWsSalwfMndNom(final String wsSalwfMndNom) {
        this.wsSalwfMndNom.setValue(wsSalwfMndNom);
    }

    public void setWsSalwfMndNom(final IAlphanumericValue wsSalwfMndNom) {
        this.wsSalwfMndNom.setValue(wsSalwfMndNom);
    }

    public void setWsSalwfEdoNom(final String wsSalwfEdoNom) {
        this.wsSalwfEdoNom.setValue(wsSalwfEdoNom);
    }

    public void setWsSalwfEdoNom(final IAlphanumericValue wsSalwfEdoNom) {
        this.wsSalwfEdoNom.setValue(wsSalwfEdoNom);
    }

    public void setWsSalwfPaiNom(final String wsSalwfPaiNom) {
        this.wsSalwfPaiNom.setValue(wsSalwfPaiNom);
    }

    public void setWsSalwfPaiNom(final IAlphanumericValue wsSalwfPaiNom) {
        this.wsSalwfPaiNom.setValue(wsSalwfPaiNom);
    }

    public void setWsSalwfCpoCve(final String wsSalwfCpoCve) {
        this.wsSalwfCpoCve.setValue(wsSalwfCpoCve);
    }

    public void setWsSalwfCpoCve(final IAlphanumericValue wsSalwfCpoCve) {
        this.wsSalwfCpoCve.setValue(wsSalwfCpoCve);
    }

    public void setWsSalwfMteCve(final String wsSalwfMteCve) {
        this.wsSalwfMteCve.setValue(wsSalwfMteCve);
    }

    public void setWsSalwfMteCve(final IAlphanumericValue wsSalwfMteCve) {
        this.wsSalwfMteCve.setValue(wsSalwfMteCve);
    }

    public void setWsSalwfTfpCve(final String wsSalwfTfpCve) {
        this.wsSalwfTfpCve.setValue(wsSalwfTfpCve);
    }

    public void setWsSalwfTfpCve(final IAlphanumericValue wsSalwfTfpCve) {
        this.wsSalwfTfpCve.setValue(wsSalwfTfpCve);
    }

    public void setWsSalwfCtbNumCta(final String wsSalwfCtbNumCta) {
        this.wsSalwfCtbNumCta.setValue(wsSalwfCtbNumCta);
    }

    public void setWsSalwfCtbNumCta(final IAlphanumericValue wsSalwfCtbNumCta) {
        this.wsSalwfCtbNumCta.setValue(wsSalwfCtbNumCta);
    }

    public void setWsSalwfIndPag(final String wsSalwfIndPag) {
        this.wsSalwfIndPag.setValue(wsSalwfIndPag);
    }

    public void setWsSalwfIndPag(final IAlphanumericValue wsSalwfIndPag) {
        this.wsSalwfIndPag.setValue(wsSalwfIndPag);
    }

    public void setWsSalwfIndExc(final String wsSalwfIndExc) {
        this.wsSalwfIndExc.setValue(wsSalwfIndExc);
    }

    public void setWsSalwfIndExc(final IAlphanumericValue wsSalwfIndExc) {
        this.wsSalwfIndExc.setValue(wsSalwfIndExc);
    }

    public void setWsSalwfUsuCve(final String wsSalwfUsuCve) {
        this.wsSalwfUsuCve.setValue(wsSalwfUsuCve);
    }

    public void setWsSalwfUsuCve(final IAlphanumericValue wsSalwfUsuCve) {
        this.wsSalwfUsuCve.setValue(wsSalwfUsuCve);
    }

    public void setWsSalwfNomPrg(final String wsSalwfNomPrg) {
        this.wsSalwfNomPrg.setValue(wsSalwfNomPrg);
    }

    public void setWsSalwfNomPrg(final IAlphanumericValue wsSalwfNomPrg) {
        this.wsSalwfNomPrg.setValue(wsSalwfNomPrg);
    }

    public void setWsSalwfFecUltAct(final String wsSalwfFecUltAct) {
        this.wsSalwfFecUltAct.setValue(wsSalwfFecUltAct);
    }

    public void setWsSalwfFecUltAct(final IAlphanumericValue wsSalwfFecUltAct) {
        this.wsSalwfFecUltAct.setValue(wsSalwfFecUltAct);
    }

    public void setWsSalwfPjeIva(final BigDecimal wsSalwfPjeIva) {
        this.wsSalwfPjeIva.setValue(wsSalwfPjeIva);
    }

    public void setWsSalwfPjeIva(final int wsSalwfPjeIva) {
        this.wsSalwfPjeIva.setValue(wsSalwfPjeIva);
    }

    public void setWsSalwfPjeIva(final INumericValue wsSalwfPjeIva) {
        this.wsSalwfPjeIva.setValue(wsSalwfPjeIva);
    }

    public void setWsSalwfPjeIsr(final BigDecimal wsSalwfPjeIsr) {
        this.wsSalwfPjeIsr.setValue(wsSalwfPjeIsr);
    }

    public void setWsSalwfPjeIsr(final int wsSalwfPjeIsr) {
        this.wsSalwfPjeIsr.setValue(wsSalwfPjeIsr);
    }

    public void setWsSalwfPjeIsr(final INumericValue wsSalwfPjeIsr) {
        this.wsSalwfPjeIsr.setValue(wsSalwfPjeIsr);
    }

    public void setWsSalwfCdregfis(final String wsSalwfCdregfis) {
        this.wsSalwfCdregfis.setValue(wsSalwfCdregfis);
    }

    public void setWsSalwfCdregfis(final IAlphanumericValue wsSalwfCdregfis) {
        this.wsSalwfCdregfis.setValue(wsSalwfCdregfis);
    }

    public void setWsSalwfNomrznso(final String wsSalwfNomrznso) {
        this.wsSalwfNomrznso.setValue(wsSalwfNomrznso);
    }

    public void setWsSalwfNomrznso(final IAlphanumericValue wsSalwfNomrznso) {
        this.wsSalwfNomrznso.setValue(wsSalwfNomrznso);
    }

    public void setWsSalwfVerCfd(final String wsSalwfVerCfd) {
        this.wsSalwfVerCfd.setValue(wsSalwfVerCfd);
    }

    public void setWsSalwfVerCfd(final IAlphanumericValue wsSalwfVerCfd) {
        this.wsSalwfVerCfd.setValue(wsSalwfVerCfd);
    }

    public void setWsSalwfCedNvi(final BigDecimal wsSalwfCedNvi) {
        this.wsSalwfCedNvi.setValue(wsSalwfCedNvi);
    }

    public void setWsSalwfCedNvi(final int wsSalwfCedNvi) {
        this.wsSalwfCedNvi.setValue(wsSalwfCedNvi);
    }

    public void setWsSalwfCedNvi(final INumericValue wsSalwfCedNvi) {
        this.wsSalwfCedNvi.setValue(wsSalwfCedNvi);
    }

    public void setWsSalwfCedVid(final BigDecimal wsSalwfCedVid) {
        this.wsSalwfCedVid.setValue(wsSalwfCedVid);
    }

    public void setWsSalwfCedVid(final int wsSalwfCedVid) {
        this.wsSalwfCedVid.setValue(wsSalwfCedVid);
    }

    public void setWsSalwfCedVid(final INumericValue wsSalwfCedVid) {
        this.wsSalwfCedVid.setValue(wsSalwfCedVid);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsSalwfAgtIdr.clear();
        this.wsSalwfFecIniPdo.clear();
        this.wsSalwfFecFinPdo.clear();
        this.wsSalwfEsaCve.clear();
        this.wsSalwfPefCve.clear();
        this.wsSalwfOfnCve.clear();
        this.wsSalwfCnaGerZonEnv.clear();
        this.wsSalwfDsgCve.clear();
        this.wsSalwfAgtRfc.clear();
        this.wsSalwfNomEmi.clear();
        this.wsSalwfDmaCae.clear();
        this.wsSalwfDmaNum.clear();
        this.wsSalwfDmaInt.clear();
        this.wsSalwfDmaCol.clear();
        this.wsSalwfMndNom.clear();
        this.wsSalwfEdoNom.clear();
        this.wsSalwfPaiNom.clear();
        this.wsSalwfCpoCve.clear();
        this.wsSalwfMteCve.clear();
        this.wsSalwfTfpCve.clear();
        this.wsSalwfCtbNumCta.clear();
        this.wsSalwfIndPag.clear();
        this.wsSalwfIndExc.clear();
        this.wsSalwfUsuCve.clear();
        this.wsSalwfNomPrg.clear();
        this.wsSalwfFecUltAct.clear();
        this.wsSalwfPjeIva.clear();
        this.wsSalwfPjeIsr.clear();
        this.wsSalwfCdregfis.clear();
        this.wsSalwfNomrznso.clear();
        this.wsSalwfVerCfd.clear();
        this.wsSalwfCedNvi.clear();
        this.wsSalwfCedVid.clear();
    }

}

