
/**
 *  Program: Ggqa9a00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.ISqlErrorListener;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.common.cbl.functions.Functions;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00.WVariables;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00.CContadores;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00.IIndices;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00.RAgentes;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa9a00.ErrorMessage;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctedo0cpy.Dclgcctedo0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcqthfa0cpy.Gcqthfa0;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcqthfa0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcqthfa0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqa9a00 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private AlphanumericVar regAgentes = new AlphanumericVar(80);            // 01 REG-AGENTES X(080) UsedAsReferenceParameter
    private WVariables wVariables = new  WVariables();                       // 01 W-VARIABLES
    private CContadores cContadores = new  CContadores();                    // 01 C-CONTADORES
    private IIndices iIndices = new  IIndices();                             // 01 I-INDICES
    private RAgentes rAgentes = new  RAgentes();                             // 01 R-AGENTES
    // Auto variables
//  private int errorIndex;                         // ERROR-INDEX // [@WARNING variable NOT USED]
    // Level 1
    private ErrorMessage errorMessage = new  ErrorMessage();                 // 01 ERROR-MESSAGE UsedAsParameter
    // Level 77
    private BinaryVar errorTextLen = new BinaryVar(9, 0);            // 77 ERROR-TEXT-LEN S9(9) COMPUTATIONAL UsedAsParameter

    // Copy file QA$CBLCPY$GCCTEDO0
    private Dclgcctedo0 dclgcctedo0 = new  Dclgcctedo0();                    // 01 DCLGCCTEDO0
    // End copy file QA$CBLCPY$GCCTEDO0

    // Copy file QA$CBLCPY$GCQTHFA0
    private Gcqthfa0 gcqthfa0 = new  Gcqthfa0();                             // 01 GCQTHFA0

    private Sqlca sqlca;
    // Files
    private ISequentialFile agentes;



    // Declare Sql Ports
    private IGcqthfa0Port gcqthfa0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gcqthfa0Model> gcqthfa0Model;

    // Declare Sql Cursors
    private ICursor<GenericModel> agentesCursor;

    public Ggqa9a00(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initialize();
        initFiles();
    }

    // Initial values
    @Override
    public void initialize() {
        this.errorTextLen.setValue(72);
    }


    public void initFiles() {

        this.agentes = createSequentialFile("AGENTES", regAgentes);


    }

    public void run() {
        sqlDelayedParagraph();
        p000ModuloControl();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(agentesCursor == null) {
            agentesCursor = gcqthfa0Port.createCursorAgentes(
                () -> null);
        }
        //
        // EXIT-SQL-DELAYED-PARAGRAPH - Last paragraph
        //
    }

    /**
     *  Paragraph: 000-MODULO-CONTROL.
     */
    void p000ModuloControl() {
        inicio();
        proceso();
        fin();
        throw new GobackException();
    }

    /**
     *  Paragraph: INICIO.
     *
     */
    void inicio() {
        display(Functions.length(rAgentes));
        getProgramContext().onSqlError(new ISqlErrorListener() {
            public void onError() {
                db2MsgText();
            }
        });
        wVariables.setWFecSist(Functions.currentDateString(getOs390Context()));

        agentes.openOutput();
    }

    /**
     *  Paragraph: PROCESO.
     *
     */
    void proceso() {
        agentesCursor.open();
        readAgentes();
        while (!(sqlca.getSqlcode() == 100)) {
                writeAgentes();
                readAgentes();
        }
        if (agentesCursor != null) {
            agentesCursor.close();
        }
    }

    /**
     *  Paragraph: FIN.
     *
     */
    void fin() {
        agentes.close();
    }

    /**
     *  Paragraph: READ-AGENTES.
     *
     */
    void readAgentes() {
        genericModel = agentesCursor.next();

        if (genericModel.isPresent()) {
            gcqthfa0.setAgtIdr(fromHostInt(genericModel.get().values().get(0)));
            gcqthfa0.setPefCve(fromHostInt(genericModel.get().values().get(1)));
            gcqthfa0.setHfaPjeIva(fromHostBigDecimal(genericModel.get().values().get(2)));
            gcqthfa0.setHfaPjeIsr(fromHostBigDecimal(genericModel.get().values().get(3)));
            gcqthfa0.setHfaPjeCedNv(fromHostBigDecimal(genericModel.get().values().get(4)));
            gcqthfa0.setHfaPjeCedVi(fromHostBigDecimal(genericModel.get().values().get(5)));
            gcqthfa0.setCdregfis(fromHostString(genericModel.get().values().get(6)));
            dclgcctedo0.setEdoCve(fromHostString(genericModel.get().values().get(7)));
            gcqthfa0.setTfpCve(fromHostInt(genericModel.get().values().get(8)));
        }
        if (sqlca.getSqlcode() == 0) {
            cContadores.getCFetchAgtced().add(1);
        }
    }

    /**
     *  Paragraph: WRITE-AGENTES.
     *
     */
    void writeAgentes() {
        rAgentes.initialize();
        rAgentes.setRAgentesAgtIdr(gcqthfa0.getAgtIdr());
        rAgentes.getRAgentesHfaPjeIva().setValue(gcqthfa0.getHfaPjeIva());
        rAgentes.getRAgentesHfaPjeIsr().setValue(gcqthfa0.getHfaPjeIsr());
        rAgentes.getRAgentesCedNv().setValue(gcqthfa0.getHfaPjeCedNv());
        rAgentes.getRAgentesCedVi().setValue(gcqthfa0.getHfaPjeCedVi());
        rAgentes.setRAgentesPefCve(gcqthfa0.getPefCve());
        rAgentes.setRAgentesCdregfis(gcqthfa0.getCdregfis());
        rAgentes.setRAgentesTfpCve(gcqthfa0.getTfpCve());
        rAgentes.setRAgentesEdoCve(dclgcctedo0.getEdoCve());
        display("AGT-IDR     ", gcqthfa0.getAgtIdr());
        display("TFP-CVE     ", gcqthfa0.getTfpCve());
        agentes.write(regAgentes, rAgentes);
    }

    /**
     *  Paragraph: DB2-MSG-TEXT.
     *
     */
    void db2MsgText() {
        runProgram("DSNTIAR", sqlca, 
                errorMessage, 
                errorTextLen);
        iIndices.setI(1);
        while (!(iIndices.getI().compareTo(10) > 0)) {
            display(errorMessage.getErrorText().at(iIndices.getI()));
            returnCode.setValue(12);
            throw new StopRunException();
        }
    }

    @Autowired
    public void setGcqthfa0Port(IGcqthfa0Port gcqthfa0Port) {
        this.gcqthfa0Port = gcqthfa0Port;
        this.gcqthfa0Port.setProgramContext(getProgramContext());
    }
}
