package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpparametro;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-PARAM-FECHA-INI-1-R.
 *
 */
@Setter
@Getter
public class RpParamFechaIni_1R extends DataStruct {
    // Properties
    private UnsignedNumericVar rpFechaIni_1Aaaa = new UnsignedNumericVar(this, 4, 0);        // 07 RP-FECHA-INI-1-AAAA 9(04)
    // Level 88 - Conditional names
    private static final int FECHA_INI_1_AAAA_OK = 1900;

    private AlphanumericVar rpGuionIni_1_1 = new AlphanumericVar(this, 1);   // 07 RP-GUION-INI-1-1 X(01)
    private UnsignedNumericVar rpFechaIni_1Mm = new UnsignedNumericVar(this, 2, 0);          // 07 RP-FECHA-INI-1-MM 9(02)
    // Level 88 - Conditional names
    private static final int FECHA_INI_1_MM_OK = 01;

    private AlphanumericVar rpGuionIni_1_2 = new AlphanumericVar(this, 1);   // 07 RP-GUION-INI-1-2 X(01)
    private UnsignedNumericVar rpFechaIni_1Dd = new UnsignedNumericVar(this, 2, 0);          // 07 RP-FECHA-INI-1-DD 9(02)
    // Level 88 - Conditional names
    private static final int FECHA_INI_1_DD_OK = 01;


    public RpParamFechaIni_1R() {
        super();
        initialize();
    }

    public RpParamFechaIni_1R(DataContainer parent) {
        super(parent);
    }

    public RpParamFechaIni_1R(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRpFechaIni_1Aaaa(final int rpFechaIni_1Aaaa) {
        this.rpFechaIni_1Aaaa.setValue(rpFechaIni_1Aaaa);
    }

    public void setRpFechaIni_1Aaaa(final BigDecimal rpFechaIni_1Aaaa) {
        this.rpFechaIni_1Aaaa.setValue(rpFechaIni_1Aaaa);
    }

    public void setRpFechaIni_1Aaaa(final INumericValue rpFechaIni_1Aaaa) {
        this.rpFechaIni_1Aaaa.setValue(rpFechaIni_1Aaaa);
    }

    // Conditionals (88) - RP-FECHA-INI-1-AAAA
    public boolean isFechaIni_1AaaaOk() {
        return this.rpFechaIni_1Aaaa.isEqual(FECHA_INI_1_AAAA_OK);
    }

    public void setFechaIni_1AaaaOk() {
        this.rpFechaIni_1Aaaa.setValue(FECHA_INI_1_AAAA_OK);
    }


    public void setRpGuionIni_1_1(final String rpGuionIni_1_1) {
        this.rpGuionIni_1_1.setValue(rpGuionIni_1_1);
    }

    public void setRpGuionIni_1_1(final IAlphanumericValue rpGuionIni_1_1) {
        this.rpGuionIni_1_1.setValue(rpGuionIni_1_1);
    }

    public void setRpFechaIni_1Mm(final int rpFechaIni_1Mm) {
        this.rpFechaIni_1Mm.setValue(rpFechaIni_1Mm);
    }

    public void setRpFechaIni_1Mm(final BigDecimal rpFechaIni_1Mm) {
        this.rpFechaIni_1Mm.setValue(rpFechaIni_1Mm);
    }

    public void setRpFechaIni_1Mm(final INumericValue rpFechaIni_1Mm) {
        this.rpFechaIni_1Mm.setValue(rpFechaIni_1Mm);
    }

    // Conditionals (88) - RP-FECHA-INI-1-MM
    public boolean isFechaIni_1MmOk() {
        return this.rpFechaIni_1Mm.isEqual(FECHA_INI_1_MM_OK);
    }

    public void setFechaIni_1MmOk() {
        this.rpFechaIni_1Mm.setValue(FECHA_INI_1_MM_OK);
    }


    public void setRpGuionIni_1_2(final String rpGuionIni_1_2) {
        this.rpGuionIni_1_2.setValue(rpGuionIni_1_2);
    }

    public void setRpGuionIni_1_2(final IAlphanumericValue rpGuionIni_1_2) {
        this.rpGuionIni_1_2.setValue(rpGuionIni_1_2);
    }

    public void setRpFechaIni_1Dd(final int rpFechaIni_1Dd) {
        this.rpFechaIni_1Dd.setValue(rpFechaIni_1Dd);
    }

    public void setRpFechaIni_1Dd(final BigDecimal rpFechaIni_1Dd) {
        this.rpFechaIni_1Dd.setValue(rpFechaIni_1Dd);
    }

    public void setRpFechaIni_1Dd(final INumericValue rpFechaIni_1Dd) {
        this.rpFechaIni_1Dd.setValue(rpFechaIni_1Dd);
    }

    // Conditionals (88) - RP-FECHA-INI-1-DD
    public boolean isFechaIni_1DdOk() {
        return this.rpFechaIni_1Dd.isEqual(FECHA_INI_1_DD_OK);
    }

    public void setFechaIni_1DdOk() {
        this.rpFechaIni_1Dd.setValue(FECHA_INI_1_DD_OK);
    }



    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rpFechaIni_1Aaaa.clear();
        this.rpGuionIni_1_1.clear();
        this.rpFechaIni_1Mm.clear();
        this.rpGuionIni_1_2.clear();
        this.rpFechaIni_1Dd.clear();
    }

}

