package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00adcpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00adcpy.PlhcMktHeader;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00adcpy.plhcmktheader.PlhcMktCompany;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00adcpy.PlhcMktDetail;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00adcpy.PlhcMktTrailer;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00adcpy.plhcmkttrailer.PlhcMktCompanyT;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-INTERFAZ.
 *
 */
// 500      ******************************************************************
// 501      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 14      *
// 502      ******************************************************************
// 505      *                                                                 
// 506      *-------- DEFINICION DEL REGISTRO EXTRACCION AGENTES ------------*
@Setter
@Getter
public class WsInterfaz extends DataStruct {
    // Properties
    private Ggqa00adcpy ggqa00adcpy = new  Ggqa00adcpy(this);        // Copy file

    public WsInterfaz() {
        super();
        initialize();
    }

    public WsInterfaz(DataContainer parent) {
        super(parent);
    }

    public WsInterfaz(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }



    // Deeper properties

    // Property: ggqa00adcpy.plhcMktHeader -> 05 PLHC-MKT-HEADER
    public PlhcMktHeader getPlhcMktHeader() {
        return this.ggqa00adcpy.getPlhcMktHeader();
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktRecordIdh -> 10 PLHC-MKT-RECORD-IDH X(05)
    public AlphanumericVar getPlhcMktRecordIdh() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktRecordIdh();
    }

    public void setPlhcMktRecordIdh(final String plhcMktRecordIdh) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktRecordIdh(plhcMktRecordIdh);
    }

    public void setPlhcMktRecordIdh(final IAlphanumericValue plhcMktRecordIdh) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktRecordIdh(plhcMktRecordIdh);
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktCompany -> 10 PLHC-MKT-COMPANY
    public PlhcMktCompany getPlhcMktCompany() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCompany();
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktCompany.plhcMktCompanyId -> 15 PLHC-MKT-COMPANY-ID X(07)
    public AlphanumericVar getPlhcMktCompanyId() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCompany().getPlhcMktCompanyId();
    }

    public void setPlhcMktCompanyId(final String plhcMktCompanyId) {
        this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCompany().setPlhcMktCompanyId(plhcMktCompanyId);
    }

    public void setPlhcMktCompanyId(final IAlphanumericValue plhcMktCompanyId) {
        this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCompany().setPlhcMktCompanyId(plhcMktCompanyId);
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktCompany.filler1 -> 15 FILLER X(02)
    public AlphanumericVar getFiller1() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCompany().getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCompany().setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCompany().setFiller1(filler1);
    }

    // Property: ggqa00adcpy.plhcMktHeader.filler2 -> 10 FILLER X(20)
    public AlphanumericVar getFiller2() {
        return this.ggqa00adcpy.getPlhcMktHeader().getFiller2();
    }

    public void setFiller2(final String filler2) {
        this.ggqa00adcpy.getPlhcMktHeader().setFiller2(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.ggqa00adcpy.getPlhcMktHeader().setFiller2(filler2);
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktProcessDt -> 10 PLHC-MKT-PROCESS-DT 9(08)
    public UnsignedNumericVar getPlhcMktProcessDt() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktProcessDt();
    }

    public void setPlhcMktProcessDt(final int plhcMktProcessDt) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktProcessDt(plhcMktProcessDt);
    }

    public void setPlhcMktProcessDt(final INumericValue plhcMktProcessDt) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktProcessDt(plhcMktProcessDt);
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktCreationDt -> 10 PLHC-MKT-CREATION-DT 9(08)
    public UnsignedNumericVar getPlhcMktCreationDt() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktCreationDt();
    }

    public void setPlhcMktCreationDt(final int plhcMktCreationDt) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktCreationDt(plhcMktCreationDt);
    }

    public void setPlhcMktCreationDt(final INumericValue plhcMktCreationDt) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktCreationDt(plhcMktCreationDt);
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktRecordCt -> 10 PLHC-MKT-RECORD-CT 9(06)
    public UnsignedNumericVar getPlhcMktRecordCt() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktRecordCt();
    }

    public void setPlhcMktRecordCt(final int plhcMktRecordCt) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktRecordCt(plhcMktRecordCt);
    }

    public void setPlhcMktRecordCt(final INumericValue plhcMktRecordCt) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktRecordCt(plhcMktRecordCt);
    }

    // Property: ggqa00adcpy.plhcMktHeader.filler3 -> 10 FILLER X(0776)
    public AlphanumericVar getFiller3() {
        return this.ggqa00adcpy.getPlhcMktHeader().getFiller3();
    }

    public void setFiller3(final String filler3) {
        this.ggqa00adcpy.getPlhcMktHeader().setFiller3(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.ggqa00adcpy.getPlhcMktHeader().setFiller3(filler3);
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktSortIndh -> 10 PLHC-MKT-SORT-INDH X(0001)
    public AlphanumericVar getPlhcMktSortIndh() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktSortIndh();
    }

    public void setPlhcMktSortIndh(final String plhcMktSortIndh) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktSortIndh(plhcMktSortIndh);
    }

    public void setPlhcMktSortIndh(final IAlphanumericValue plhcMktSortIndh) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktSortIndh(plhcMktSortIndh);
    }

    // Property: ggqa00adcpy.plhcMktHeader.filler4 -> 10 FILLER X(0010)
    public AlphanumericVar getFiller4() {
        return this.ggqa00adcpy.getPlhcMktHeader().getFiller4();
    }

    public void setFiller4(final String filler4) {
        this.ggqa00adcpy.getPlhcMktHeader().setFiller4(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.ggqa00adcpy.getPlhcMktHeader().setFiller4(filler4);
    }

    // Property: ggqa00adcpy.plhcMktHeader.plhcMktEndrech -> 10 PLHC-MKT-ENDRECH X(0001)
    public AlphanumericVar getPlhcMktEndrech() {
        return this.ggqa00adcpy.getPlhcMktHeader().getPlhcMktEndrech();
    }

    public void setPlhcMktEndrech(final String plhcMktEndrech) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktEndrech(plhcMktEndrech);
    }

    public void setPlhcMktEndrech(final IAlphanumericValue plhcMktEndrech) {
        this.ggqa00adcpy.getPlhcMktHeader().setPlhcMktEndrech(plhcMktEndrech);
    }

    // Property: ggqa00adcpy.plhcMktDetail -> 05 PLHC-MKT-DETAIL
    public PlhcMktDetail getPlhcMktDetail() {
        return this.ggqa00adcpy.getPlhcMktDetail();
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktCompanyD -> 10 PLHC-MKT-COMPANY-D X(05)
    public AlphanumericVar getPlhcMktCompanyD() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktCompanyD();
    }

    public void setPlhcMktCompanyD(final String plhcMktCompanyD) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCompanyD(plhcMktCompanyD);
    }

    public void setPlhcMktCompanyD(final IAlphanumericValue plhcMktCompanyD) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCompanyD(plhcMktCompanyD);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAgentId -> 10 PLHC-MKT-AGENT-ID X(05)
    public AlphanumericVar getPlhcMktAgentId() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAgentId();
    }

    public void setPlhcMktAgentId(final String plhcMktAgentId) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAgentId(plhcMktAgentId);
    }

    public void setPlhcMktAgentId(final IAlphanumericValue plhcMktAgentId) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAgentId(plhcMktAgentId);
    }

    // Property: ggqa00adcpy.plhcMktDetail.filler5 -> 10 FILLER X(04)
    public AlphanumericVar getFiller5() {
        return this.ggqa00adcpy.getPlhcMktDetail().getFiller5();
    }

    public void setFiller5(final String filler5) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller5(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller5(filler5);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAgencyId -> 10 PLHC-MKT-AGENCY-ID X(03)
    public AlphanumericVar getPlhcMktAgencyId() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAgencyId();
    }

    public void setPlhcMktAgencyId(final String plhcMktAgencyId) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAgencyId(plhcMktAgencyId);
    }

    public void setPlhcMktAgencyId(final IAlphanumericValue plhcMktAgencyId) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAgencyId(plhcMktAgencyId);
    }

    // Property: ggqa00adcpy.plhcMktDetail.filler6 -> 10 FILLER X(06)
    public AlphanumericVar getFiller6() {
        return this.ggqa00adcpy.getPlhcMktDetail().getFiller6();
    }

    public void setFiller6(final String filler6) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller6(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller6(filler6);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktSsn -> 10 PLHC-MKT-SSN X(13)
    public AlphanumericVar getPlhcMktSsn() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktSsn();
    }

    public void setPlhcMktSsn(final String plhcMktSsn) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktSsn(plhcMktSsn);
    }

    public void setPlhcMktSsn(final IAlphanumericValue plhcMktSsn) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktSsn(plhcMktSsn);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktNasd -> 10 PLHC-MKT-NASD X(10)
    public AlphanumericVar getPlhcMktNasd() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktNasd();
    }

    public void setPlhcMktNasd(final String plhcMktNasd) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNasd(plhcMktNasd);
    }

    public void setPlhcMktNasd(final IAlphanumericValue plhcMktNasd) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNasd(plhcMktNasd);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktSex -> 10 PLHC-MKT-SEX X(01)
    public AlphanumericVar getPlhcMktSex() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktSex();
    }

    public void setPlhcMktSex(final String plhcMktSex) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktSex(plhcMktSex);
    }

    public void setPlhcMktSex(final IAlphanumericValue plhcMktSex) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktSex(plhcMktSex);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktHireDate -> 10 PLHC-MKT-HIRE-DATE 9(08)
    public UnsignedNumericVar getPlhcMktHireDate() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktHireDate();
    }

    public void setPlhcMktHireDate(final int plhcMktHireDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktHireDate(plhcMktHireDate);
    }

    public void setPlhcMktHireDate(final INumericValue plhcMktHireDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktHireDate(plhcMktHireDate);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktStatus -> 10 PLHC-MKT-STATUS X(01)
    public AlphanumericVar getPlhcMktStatus() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktStatus();
    }

    public void setPlhcMktStatus(final String plhcMktStatus) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktStatus(plhcMktStatus);
    }

    public void setPlhcMktStatus(final IAlphanumericValue plhcMktStatus) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktStatus(plhcMktStatus);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktTermDate -> 10 PLHC-MKT-TERM-DATE 9(08)
    public UnsignedNumericVar getPlhcMktTermDate() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktTermDate();
    }

    public void setPlhcMktTermDate(final int plhcMktTermDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktTermDate(plhcMktTermDate);
    }

    public void setPlhcMktTermDate(final INumericValue plhcMktTermDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktTermDate(plhcMktTermDate);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktDob -> 10 PLHC-MKT-DOB 9(08)
    public UnsignedNumericVar getPlhcMktDob() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktDob();
    }

    public void setPlhcMktDob(final int plhcMktDob) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktDob(plhcMktDob);
    }

    public void setPlhcMktDob(final INumericValue plhcMktDob) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktDob(plhcMktDob);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktCorpName -> 10 PLHC-MKT-CORP-NAME X(60)
    public AlphanumericVar getPlhcMktCorpName() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktCorpName();
    }

    public void setPlhcMktCorpName(final String plhcMktCorpName) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCorpName(plhcMktCorpName);
    }

    public void setPlhcMktCorpName(final IAlphanumericValue plhcMktCorpName) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCorpName(plhcMktCorpName);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameLast -> 10 PLHC-MKT-NAME-LAST X(40)
    public AlphanumericVar getPlhcMktNameLast() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktNameLast();
    }

    public void setPlhcMktNameLast(final String plhcMktNameLast) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameLast(plhcMktNameLast);
    }

    public void setPlhcMktNameLast(final IAlphanumericValue plhcMktNameLast) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameLast(plhcMktNameLast);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameFirst -> 10 PLHC-MKT-NAME-FIRST X(20)
    public AlphanumericVar getPlhcMktNameFirst() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktNameFirst();
    }

    public void setPlhcMktNameFirst(final String plhcMktNameFirst) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameFirst(plhcMktNameFirst);
    }

    public void setPlhcMktNameFirst(final IAlphanumericValue plhcMktNameFirst) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameFirst(plhcMktNameFirst);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressOne -> 10 PLHC-MKT-ADDRESS-ONE X(35)
    public AlphanumericVar getPlhcMktAddressOne() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressOne();
    }

    public void setPlhcMktAddressOne(final String plhcMktAddressOne) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressOne(plhcMktAddressOne);
    }

    public void setPlhcMktAddressOne(final IAlphanumericValue plhcMktAddressOne) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressOne(plhcMktAddressOne);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressTwo -> 10 PLHC-MKT-ADDRESS-TWO X(35)
    public AlphanumericVar getPlhcMktAddressTwo() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressTwo();
    }

    public void setPlhcMktAddressTwo(final String plhcMktAddressTwo) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressTwo(plhcMktAddressTwo);
    }

    public void setPlhcMktAddressTwo(final IAlphanumericValue plhcMktAddressTwo) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressTwo(plhcMktAddressTwo);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressThree -> 10 PLHC-MKT-ADDRESS-THREE X(35)
    public AlphanumericVar getPlhcMktAddressThree() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressThree();
    }

    public void setPlhcMktAddressThree(final String plhcMktAddressThree) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressThree(plhcMktAddressThree);
    }

    public void setPlhcMktAddressThree(final IAlphanumericValue plhcMktAddressThree) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressThree(plhcMktAddressThree);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressFour -> 10 PLHC-MKT-ADDRESS-FOUR X(35)
    public AlphanumericVar getPlhcMktAddressFour() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressFour();
    }

    public void setPlhcMktAddressFour(final String plhcMktAddressFour) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressFour(plhcMktAddressFour);
    }

    public void setPlhcMktAddressFour(final IAlphanumericValue plhcMktAddressFour) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressFour(plhcMktAddressFour);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressState -> 10 PLHC-MKT-ADDRESS-STATE X(03)
    public AlphanumericVar getPlhcMktAddressState() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressState();
    }

    public void setPlhcMktAddressState(final String plhcMktAddressState) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressState(plhcMktAddressState);
    }

    public void setPlhcMktAddressState(final IAlphanumericValue plhcMktAddressState) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressState(plhcMktAddressState);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktZipFive -> 10 PLHC-MKT-ZIP-FIVE X(05)
    public AlphanumericVar getPlhcMktZipFive() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktZipFive();
    }

    public void setPlhcMktZipFive(final String plhcMktZipFive) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktZipFive(plhcMktZipFive);
    }

    public void setPlhcMktZipFive(final IAlphanumericValue plhcMktZipFive) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktZipFive(plhcMktZipFive);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktPdcFive -> 10 PLHC-MKT-PDC-FIVE X(05)
    public AlphanumericVar getPlhcMktPdcFive() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktPdcFive();
    }

    public void setPlhcMktPdcFive(final String plhcMktPdcFive) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPdcFive(plhcMktPdcFive);
    }

    public void setPlhcMktPdcFive(final IAlphanumericValue plhcMktPdcFive) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPdcFive(plhcMktPdcFive);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktPhone -> 10 PLHC-MKT-PHONE X(14)
    public AlphanumericVar getPlhcMktPhone() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktPhone();
    }

    public void setPlhcMktPhone(final String plhcMktPhone) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPhone(plhcMktPhone);
    }

    public void setPlhcMktPhone(final IAlphanumericValue plhcMktPhone) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPhone(plhcMktPhone);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktCorpNamea -> 10 PLHC-MKT-CORP-NAMEA X(60)
    public AlphanumericVar getPlhcMktCorpNamea() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktCorpNamea();
    }

    public void setPlhcMktCorpNamea(final String plhcMktCorpNamea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCorpNamea(plhcMktCorpNamea);
    }

    public void setPlhcMktCorpNamea(final IAlphanumericValue plhcMktCorpNamea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCorpNamea(plhcMktCorpNamea);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameLasta -> 10 PLHC-MKT-NAME-LASTA X(40)
    public AlphanumericVar getPlhcMktNameLasta() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktNameLasta();
    }

    public void setPlhcMktNameLasta(final String plhcMktNameLasta) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameLasta(plhcMktNameLasta);
    }

    public void setPlhcMktNameLasta(final IAlphanumericValue plhcMktNameLasta) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameLasta(plhcMktNameLasta);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktNameFirsta -> 10 PLHC-MKT-NAME-FIRSTA X(20)
    public AlphanumericVar getPlhcMktNameFirsta() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktNameFirsta();
    }

    public void setPlhcMktNameFirsta(final String plhcMktNameFirsta) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameFirsta(plhcMktNameFirsta);
    }

    public void setPlhcMktNameFirsta(final IAlphanumericValue plhcMktNameFirsta) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNameFirsta(plhcMktNameFirsta);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressOnea -> 10 PLHC-MKT-ADDRESS-ONEA X(35)
    public AlphanumericVar getPlhcMktAddressOnea() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressOnea();
    }

    public void setPlhcMktAddressOnea(final String plhcMktAddressOnea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressOnea(plhcMktAddressOnea);
    }

    public void setPlhcMktAddressOnea(final IAlphanumericValue plhcMktAddressOnea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressOnea(plhcMktAddressOnea);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressTwoa -> 10 PLHC-MKT-ADDRESS-TWOA X(35)
    public AlphanumericVar getPlhcMktAddressTwoa() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressTwoa();
    }

    public void setPlhcMktAddressTwoa(final String plhcMktAddressTwoa) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressTwoa(plhcMktAddressTwoa);
    }

    public void setPlhcMktAddressTwoa(final IAlphanumericValue plhcMktAddressTwoa) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressTwoa(plhcMktAddressTwoa);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressThreea -> 10 PLHC-MKT-ADDRESS-THREEA X(35)
    public AlphanumericVar getPlhcMktAddressThreea() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressThreea();
    }

    public void setPlhcMktAddressThreea(final String plhcMktAddressThreea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressThreea(plhcMktAddressThreea);
    }

    public void setPlhcMktAddressThreea(final IAlphanumericValue plhcMktAddressThreea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressThreea(plhcMktAddressThreea);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressFoura -> 10 PLHC-MKT-ADDRESS-FOURA X(35)
    public AlphanumericVar getPlhcMktAddressFoura() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressFoura();
    }

    public void setPlhcMktAddressFoura(final String plhcMktAddressFoura) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressFoura(plhcMktAddressFoura);
    }

    public void setPlhcMktAddressFoura(final IAlphanumericValue plhcMktAddressFoura) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressFoura(plhcMktAddressFoura);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktAddressStatea -> 10 PLHC-MKT-ADDRESS-STATEA X(03)
    public AlphanumericVar getPlhcMktAddressStatea() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktAddressStatea();
    }

    public void setPlhcMktAddressStatea(final String plhcMktAddressStatea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressStatea(plhcMktAddressStatea);
    }

    public void setPlhcMktAddressStatea(final IAlphanumericValue plhcMktAddressStatea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktAddressStatea(plhcMktAddressStatea);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktZipFivea -> 10 PLHC-MKT-ZIP-FIVEA X(05)
    public AlphanumericVar getPlhcMktZipFivea() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktZipFivea();
    }

    public void setPlhcMktZipFivea(final String plhcMktZipFivea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktZipFivea(plhcMktZipFivea);
    }

    public void setPlhcMktZipFivea(final IAlphanumericValue plhcMktZipFivea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktZipFivea(plhcMktZipFivea);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktPdcFivea -> 10 PLHC-MKT-PDC-FIVEA X(05)
    public AlphanumericVar getPlhcMktPdcFivea() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktPdcFivea();
    }

    public void setPlhcMktPdcFivea(final String plhcMktPdcFivea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPdcFivea(plhcMktPdcFivea);
    }

    public void setPlhcMktPdcFivea(final IAlphanumericValue plhcMktPdcFivea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPdcFivea(plhcMktPdcFivea);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktPhonea -> 10 PLHC-MKT-PHONEA X(14)
    public AlphanumericVar getPlhcMktPhonea() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktPhonea();
    }

    public void setPlhcMktPhonea(final String plhcMktPhonea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPhonea(plhcMktPhonea);
    }

    public void setPlhcMktPhonea(final IAlphanumericValue plhcMktPhonea) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPhonea(plhcMktPhonea);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktNasdEffDate -> 10 PLHC-MKT-NASD-EFF-DATE 9(08)
    public UnsignedNumericVar getPlhcMktNasdEffDate() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktNasdEffDate();
    }

    public void setPlhcMktNasdEffDate(final int plhcMktNasdEffDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNasdEffDate(plhcMktNasdEffDate);
    }

    public void setPlhcMktNasdEffDate(final INumericValue plhcMktNasdEffDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNasdEffDate(plhcMktNasdEffDate);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktNasdRenDate -> 10 PLHC-MKT-NASD-REN-DATE 9(08)
    public UnsignedNumericVar getPlhcMktNasdRenDate() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktNasdRenDate();
    }

    public void setPlhcMktNasdRenDate(final int plhcMktNasdRenDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNasdRenDate(plhcMktNasdRenDate);
    }

    public void setPlhcMktNasdRenDate(final INumericValue plhcMktNasdRenDate) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktNasdRenDate(plhcMktNasdRenDate);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktLicName -> 10 PLHC-MKT-LIC-NAME X(50)
    public AlphanumericVar getPlhcMktLicName() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktLicName();
    }

    public void setPlhcMktLicName(final String plhcMktLicName) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktLicName(plhcMktLicName);
    }

    public void setPlhcMktLicName(final IAlphanumericValue plhcMktLicName) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktLicName(plhcMktLicName);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktLevel -> 10 PLHC-MKT-LEVEL X(02)
    public AlphanumericVar getPlhcMktLevel() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktLevel();
    }

    public void setPlhcMktLevel(final String plhcMktLevel) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktLevel(plhcMktLevel);
    }

    public void setPlhcMktLevel(final IAlphanumericValue plhcMktLevel) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktLevel(plhcMktLevel);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktPayOption -> 10 PLHC-MKT-PAY-OPTION X(05)
    public AlphanumericVar getPlhcMktPayOption() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktPayOption();
    }

    public void setPlhcMktPayOption(final String plhcMktPayOption) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPayOption(plhcMktPayOption);
    }

    public void setPlhcMktPayOption(final IAlphanumericValue plhcMktPayOption) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPayOption(plhcMktPayOption);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktChain -> 10 PLHC-MKT-CHAIN X(10)
    public AlphanumericVar getPlhcMktChain() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktChain();
    }

    public void setPlhcMktChain(final String plhcMktChain) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktChain(plhcMktChain);
    }

    public void setPlhcMktChain(final IAlphanumericValue plhcMktChain) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktChain(plhcMktChain);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktCommIndex -> 10 PLHC-MKT-COMM-INDEX X(06)
    public AlphanumericVar getPlhcMktCommIndex() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktCommIndex();
    }

    public void setPlhcMktCommIndex(final String plhcMktCommIndex) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCommIndex(plhcMktCommIndex);
    }

    public void setPlhcMktCommIndex(final IAlphanumericValue plhcMktCommIndex) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktCommIndex(plhcMktCommIndex);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktPayMethod -> 10 PLHC-MKT-PAY-METHOD X(01)
    public AlphanumericVar getPlhcMktPayMethod() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktPayMethod();
    }

    public void setPlhcMktPayMethod(final String plhcMktPayMethod) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPayMethod(plhcMktPayMethod);
    }

    public void setPlhcMktPayMethod(final IAlphanumericValue plhcMktPayMethod) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPayMethod(plhcMktPayMethod);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktPayFreq -> 10 PLHC-MKT-PAY-FREQ X(02)
    public AlphanumericVar getPlhcMktPayFreq() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktPayFreq();
    }

    public void setPlhcMktPayFreq(final String plhcMktPayFreq) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPayFreq(plhcMktPayFreq);
    }

    public void setPlhcMktPayFreq(final IAlphanumericValue plhcMktPayFreq) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktPayFreq(plhcMktPayFreq);
    }

    // Property: ggqa00adcpy.plhcMktDetail.filler7 -> 10 FILLER X(78)
    public AlphanumericVar getFiller7() {
        return this.ggqa00adcpy.getPlhcMktDetail().getFiller7();
    }

    public void setFiller7(final String filler7) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller7(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller7(filler7);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktUniqueAgentCode -> 10 PLHC-MKT-UNIQUE-AGENT-CODE X(08)
    public AlphanumericVar getPlhcMktUniqueAgentCode() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktUniqueAgentCode();
    }

    public void setPlhcMktUniqueAgentCode(final String plhcMktUniqueAgentCode) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktUniqueAgentCode(plhcMktUniqueAgentCode);
    }

    public void setPlhcMktUniqueAgentCode(final IAlphanumericValue plhcMktUniqueAgentCode) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktUniqueAgentCode(plhcMktUniqueAgentCode);
    }

    // Property: ggqa00adcpy.plhcMktDetail.filler8 -> 10 FILLER X(08)
    public AlphanumericVar getFiller8() {
        return this.ggqa00adcpy.getPlhcMktDetail().getFiller8();
    }

    public void setFiller8(final String filler8) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller8(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller8(filler8);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktSortInd -> 10 PLHC-MKT-SORT-IND X(01)
    public AlphanumericVar getPlhcMktSortInd() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktSortInd();
    }

    public void setPlhcMktSortInd(final String plhcMktSortInd) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktSortInd(plhcMktSortInd);
    }

    public void setPlhcMktSortInd(final IAlphanumericValue plhcMktSortInd) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktSortInd(plhcMktSortInd);
    }

    // Property: ggqa00adcpy.plhcMktDetail.filler9 -> 10 FILLER X(10)
    public AlphanumericVar getFiller9() {
        return this.ggqa00adcpy.getPlhcMktDetail().getFiller9();
    }

    public void setFiller9(final String filler9) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller9(filler9);
    }

    public void setFiller9(final IAlphanumericValue filler9) {
        this.ggqa00adcpy.getPlhcMktDetail().setFiller9(filler9);
    }

    // Property: ggqa00adcpy.plhcMktDetail.plhcMktEndrec -> 10 PLHC-MKT-ENDREC X(01)
    public AlphanumericVar getPlhcMktEndrec() {
        return this.ggqa00adcpy.getPlhcMktDetail().getPlhcMktEndrec();
    }

    public void setPlhcMktEndrec(final String plhcMktEndrec) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktEndrec(plhcMktEndrec);
    }

    public void setPlhcMktEndrec(final IAlphanumericValue plhcMktEndrec) {
        this.ggqa00adcpy.getPlhcMktDetail().setPlhcMktEndrec(plhcMktEndrec);
    }

    // Property: ggqa00adcpy.plhcMktTrailer -> 05 PLHC-MKT-TRAILER
    public PlhcMktTrailer getPlhcMktTrailer() {
        return this.ggqa00adcpy.getPlhcMktTrailer();
    }

    // Property: ggqa00adcpy.plhcMktTrailer.plhcMktRecordIdt -> 10 PLHC-MKT-RECORD-IDT X(05)
    public AlphanumericVar getPlhcMktRecordIdt() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktRecordIdt();
    }

    public void setPlhcMktRecordIdt(final String plhcMktRecordIdt) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktRecordIdt(plhcMktRecordIdt);
    }

    public void setPlhcMktRecordIdt(final IAlphanumericValue plhcMktRecordIdt) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktRecordIdt(plhcMktRecordIdt);
    }

    // Property: ggqa00adcpy.plhcMktTrailer.plhcMktCompanyT -> 10 PLHC-MKT-COMPANY-T
    public PlhcMktCompanyT getPlhcMktCompanyT() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktCompanyT();
    }

    // Property: ggqa00adcpy.plhcMktTrailer.plhcMktCompanyT.plhcMktCompanyIdT -> 15 PLHC-MKT-COMPANY-ID-T X(03)
    public AlphanumericVar getPlhcMktCompanyIdT() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktCompanyT().getPlhcMktCompanyIdT();
    }

    public void setPlhcMktCompanyIdT(final String plhcMktCompanyIdT) {
        this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktCompanyT().setPlhcMktCompanyIdT(plhcMktCompanyIdT);
    }

    public void setPlhcMktCompanyIdT(final IAlphanumericValue plhcMktCompanyIdT) {
        this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktCompanyT().setPlhcMktCompanyIdT(plhcMktCompanyIdT);
    }

    // Property: ggqa00adcpy.plhcMktTrailer.plhcMktCompanyT.filler10 -> 15 FILLER X(03)
    public AlphanumericVar getFiller10() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktCompanyT().getFiller10();
    }

    public void setFiller10(final String filler10) {
        this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktCompanyT().setFiller10(filler10);
    }

    public void setFiller10(final IAlphanumericValue filler10) {
        this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktCompanyT().setFiller10(filler10);
    }

    // Property: ggqa00adcpy.plhcMktTrailer.plhcMktRecordCount -> 10 PLHC-MKT-RECORD-COUNT 9(06)
    public UnsignedNumericVar getPlhcMktRecordCount() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktRecordCount();
    }

    public void setPlhcMktRecordCount(final int plhcMktRecordCount) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktRecordCount(plhcMktRecordCount);
    }

    public void setPlhcMktRecordCount(final INumericValue plhcMktRecordCount) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktRecordCount(plhcMktRecordCount);
    }

    // Property: ggqa00adcpy.plhcMktTrailer.filler11 -> 10 FILLER X(815)
    public AlphanumericVar getFiller11() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getFiller11();
    }

    public void setFiller11(final String filler11) {
        this.ggqa00adcpy.getPlhcMktTrailer().setFiller11(filler11);
    }

    public void setFiller11(final IAlphanumericValue filler11) {
        this.ggqa00adcpy.getPlhcMktTrailer().setFiller11(filler11);
    }

    // Property: ggqa00adcpy.plhcMktTrailer.plhcMktSortIndt -> 10 PLHC-MKT-SORT-INDT X(0001)
    public AlphanumericVar getPlhcMktSortIndt() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktSortIndt();
    }

    public void setPlhcMktSortIndt(final String plhcMktSortIndt) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktSortIndt(plhcMktSortIndt);
    }

    public void setPlhcMktSortIndt(final IAlphanumericValue plhcMktSortIndt) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktSortIndt(plhcMktSortIndt);
    }

    // Property: ggqa00adcpy.plhcMktTrailer.filler12 -> 10 FILLER X(0010)
    public AlphanumericVar getFiller12() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getFiller12();
    }

    public void setFiller12(final String filler12) {
        this.ggqa00adcpy.getPlhcMktTrailer().setFiller12(filler12);
    }

    public void setFiller12(final IAlphanumericValue filler12) {
        this.ggqa00adcpy.getPlhcMktTrailer().setFiller12(filler12);
    }

    // Property: ggqa00adcpy.plhcMktTrailer.plhcMktEndrect -> 10 PLHC-MKT-ENDRECT X(0001)
    public AlphanumericVar getPlhcMktEndrect() {
        return this.ggqa00adcpy.getPlhcMktTrailer().getPlhcMktEndrect();
    }

    public void setPlhcMktEndrect(final String plhcMktEndrect) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktEndrect(plhcMktEndrect);
    }

    public void setPlhcMktEndrect(final IAlphanumericValue plhcMktEndrect) {
        this.ggqa00adcpy.getPlhcMktTrailer().setPlhcMktEndrect(plhcMktEndrect);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.ggqa00adcpy.initialize();
    }

}

