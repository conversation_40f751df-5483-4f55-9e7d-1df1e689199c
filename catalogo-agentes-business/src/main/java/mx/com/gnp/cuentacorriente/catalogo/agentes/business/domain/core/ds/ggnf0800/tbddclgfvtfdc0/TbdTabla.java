package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.tbddclgfvtfdc0;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TBD-TABLA.
 *
 */
@Setter
@Getter
public class TbdTabla extends DataStructArray<TbdTabla> {
    // Properties
    private BinaryVar tbdAgtIdr = new BinaryVar(this, 9, 0);                 // 10 TBD-AGT-IDR S9(9) COMPUTATIONAL
    private AlphanumericVar tbdEmpCve = new AlphanumericVar(this, 5);        // 10 TBD-EMP-CVE X(5)
    private BinaryVar tbdCnaNum = new BinaryVar(this, 4, 0);                 // 10 TBD-CNA-NUM S9(4) COMPUTATIONAL
    private AlphanumericVar tbdRamCve = new AlphanumericVar(this, 2);        // 10 TBD-RAM-CVE X(2)
    private AlphanumericVar tbdSraCve = new AlphanumericVar(this, 4);        // 10 TBD-SRA-CVE X(4)

    public TbdTabla(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TbdTabla(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TbdTabla(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TbdTabla(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TbdTabla at(int idx) {
        return new TbdTabla(this, idx, getSizeElement());
    }

    public void setTbdAgtIdr(final int tbdAgtIdr) {
        this.tbdAgtIdr.setValue(tbdAgtIdr);
    }

    public void setTbdAgtIdr(final BigDecimal tbdAgtIdr) {
        this.tbdAgtIdr.setValue(tbdAgtIdr);
    }

    public void setTbdAgtIdr(final INumericValue tbdAgtIdr) {
        this.tbdAgtIdr.setValue(tbdAgtIdr);
    }

    public void setTbdEmpCve(final String tbdEmpCve) {
        this.tbdEmpCve.setValue(tbdEmpCve);
    }

    public void setTbdEmpCve(final IAlphanumericValue tbdEmpCve) {
        this.tbdEmpCve.setValue(tbdEmpCve);
    }

    public void setTbdCnaNum(final int tbdCnaNum) {
        this.tbdCnaNum.setValue(tbdCnaNum);
    }

    public void setTbdCnaNum(final BigDecimal tbdCnaNum) {
        this.tbdCnaNum.setValue(tbdCnaNum);
    }

    public void setTbdCnaNum(final INumericValue tbdCnaNum) {
        this.tbdCnaNum.setValue(tbdCnaNum);
    }

    public void setTbdRamCve(final String tbdRamCve) {
        this.tbdRamCve.setValue(tbdRamCve);
    }

    public void setTbdRamCve(final IAlphanumericValue tbdRamCve) {
        this.tbdRamCve.setValue(tbdRamCve);
    }

    public void setTbdSraCve(final String tbdSraCve) {
        this.tbdSraCve.setValue(tbdSraCve);
    }

    public void setTbdSraCve(final IAlphanumericValue tbdSraCve) {
        this.tbdSraCve.setValue(tbdSraCve);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbdAgtIdr.clear();
        this.tbdEmpCve.clear();
        this.tbdCnaNum.clear();
        this.tbdRamCve.clear();
        this.tbdSraCve.clear();
    }

}

