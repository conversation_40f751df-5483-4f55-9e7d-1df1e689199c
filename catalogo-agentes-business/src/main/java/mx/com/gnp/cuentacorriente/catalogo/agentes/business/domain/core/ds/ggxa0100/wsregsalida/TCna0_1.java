package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tcna0_1.TCna0Elem_1;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-CNA0-1.
 *
 */
// 182           10 T-CNA0-1    OCCURS 1 TO 255                               
@Setter
@Getter
public class TCna0_1 extends DataStructArray<TCna0_1> {
    // Properties
    private TCna0Elem_1 tCna0Elem_1 = new  TCna0Elem_1(this);                // 15 T-CNA0-ELEM-1

    public TCna0_1(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TCna0_1(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TCna0_1(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TCna0_1(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TCna0_1 at(int idx) {
        return new TCna0_1(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tCna0Elem_1.sCnaCnaNumElem -> 20 S-CNA-CNA-NUM-ELEM X(01)
    public AlphanumericVar getSCnaCnaNumElem() {
        return this.tCna0Elem_1.getSCnaCnaNumElem();
    }

    public void setSCnaCnaNumElem(final String sCnaCnaNumElem) {
        this.tCna0Elem_1.setSCnaCnaNumElem(sCnaCnaNumElem);
    }

    public void setSCnaCnaNumElem(final IAlphanumericValue sCnaCnaNumElem) {
        this.tCna0Elem_1.setSCnaCnaNumElem(sCnaCnaNumElem);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tCna0Elem_1.initialize();
    }

}

