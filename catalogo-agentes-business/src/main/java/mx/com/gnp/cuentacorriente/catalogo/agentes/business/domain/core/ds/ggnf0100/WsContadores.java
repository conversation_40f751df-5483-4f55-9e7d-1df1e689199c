package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CONTADORES.
 *
 */
// 1126066100*                                                                 06610000
// 1127066200*                                                                 06620000
// 1128066300*                                                                 06630000
// 1129066400*-------------------------------------------------------------    06640000
// 1130066500*****             VARIABLES GENERALES                        ***  06650000
// 1131066600*-------------------------------------------------------------    06660000
// 1132066700*                                                                 06670000
@Setter
@Getter
public class WsContadores { // VStruct
    // Properties
    private int wsGrabados = 0;                                             // 05 WS-GRABADOS 9(7)
    private int wsContador = 0;                                             // 05 WS-CONTADOR 9(7)
    private int wsCuantos = 0;                                              // 05 WS-CUANTOS 9(7)

    public WsContadores() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsGrabados = 0;
        this.wsContador = 0;
        this.wsCuantos = 0;
    }

}

