package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores.FlaNumAux;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores.WsFolioxx;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores.WsFoliozz;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores.WsFolioxxn;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores.WsFolioxxp;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores.WsFlaDes;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-INDICADORES.
 *
 */
// 724045600*                                                                 04560002
// 725045700*                                                                 04570002
@Setter
@Getter
public class WsIndicadores { // VStruct
    // Properties
    private int wsGerZon;                                                   // 03 WS-GER-ZON S9(4) COMPUTATIONAL
    private int wsAgtIdr;                                                   // 03 WS-AGT-IDR S9(9) COMPUTATIONAL
    private String wsFlaNum = "";                                           // 03 WS-FLA-NUM X(8)
    private int wsDraCve;                                                   // 03 WS-DRA-CVE S9(4) COMPUTATIONAL
    private int wsMesAux = 0;                                               // 03 WS-MES-AUX 99
    private int wsFinAgc = 0;                                               // 03 WS-FIN-AGC 9
    private int wsFinAgt = 0;                                               // 03 WS-FIN-AGT 9
    private int wsFinCna = 0;                                               // 03 WS-FIN-CNA 9
    private int wsFinCna1 = 0;                                              // 03 WS-FIN-CNA1 9
    private int wsFinFla = 0;                                               // 03 WS-FIN-FLA 9
    private int wsFinFla2 = 0;                                              // 03 WS-FIN-FLA2 9
    private int wsFinMte = 0;                                               // 03 WS-FIN-MTE 9
    private int wsFinOrv = 0;                                               // 03 WS-FIN-ORV 9
    private int wsFinDrc = 0;                                               // 03 WS-FIN-DRC 9
    private int i = 0;                                                      // 03 I 999
    private int j = 0;                                                      // 03 J 999
    private int contador = 0;                                               // 03 CONTADOR 99999
    private int contLineas = 0;                                             // 03 CONT-LINEAS 999
    private int contLineas1 = 0;                                            // 03 CONT-LINEAS1 999
    private int contHojas = 0;                                              // 03 CONT-HOJAS 999
    private int contHojas1 = 0;                                             // 03 CONT-HOJAS1 999
    private FlaNumAux flaNumAux = new  FlaNumAux();                          // 03 FLA-NUM-AUX
    private WsFolioxx wsFolioxx = new  WsFolioxx();                          // 03 WS-FOLIOXX
    private WsFoliozz wsFoliozz = new  WsFoliozz();                          // 03 WS-FOLIOZZ
    private WsFolioxxn wsFolioxxn = new  WsFolioxxn();                       // 03 WS-FOLIOXXN UsedAsParameter
    private WsFolioxxp wsFolioxxp = new  WsFolioxxp();                       // 03 WS-FOLIOXXP UsedAsParameter
    private WsFlaDes wsFlaDes = new  WsFlaDes();                             // 03 WS-FLA-DES

    public WsIndicadores() {
        initialize();
    }




    // Deeper properties

    // Property: flaNumAux.flaNum0Aux -> 05 FLA-NUM0-AUX X
    public String getFlaNum0Aux() {
        return this.flaNumAux.getFlaNum0Aux();
    }

    public void setFlaNum0Aux(final String flaNum0Aux) {
        this.flaNumAux.setFlaNum0Aux(flaNum0Aux);
    }

    // Property: flaNumAux.flaNum1Aux -> 05 FLA-NUM1-AUX 9(7)
    public int getFlaNum1Aux() {
        return this.flaNumAux.getFlaNum1Aux();
    }

    public void setFlaNum1Aux(final int flaNum1Aux) {
        this.flaNumAux.setFlaNum1Aux(flaNum1Aux);
    }


    // Deeper properties

    // Property: wsFolioxx.wsFolioxx1 -> 05 WS-FOLIOXX1 X
    public AlphanumericVar getWsFolioxx1() {
        return this.wsFolioxx.getWsFolioxx1();
    }

    public void setWsFolioxx1(final String wsFolioxx1) {
        this.wsFolioxx.setWsFolioxx1(wsFolioxx1);
    }

    public void setWsFolioxx1(final IAlphanumericValue wsFolioxx1) {
        this.wsFolioxx.setWsFolioxx1(wsFolioxx1);
    }

    // Property: wsFolioxx.wsFolioxx2 -> 05 WS-FOLIOXX2 9(7)
    public UnsignedNumericVar getWsFolioxx2() {
        return this.wsFolioxx.getWsFolioxx2();
    }

    public void setWsFolioxx2(final int wsFolioxx2) {
        this.wsFolioxx.setWsFolioxx2(wsFolioxx2);
    }

    public void setWsFolioxx2(final INumericValue wsFolioxx2) {
        this.wsFolioxx.setWsFolioxx2(wsFolioxx2);
    }


    // Deeper properties

    // Property: wsFoliozz.wsFoliozz1 -> 05 WS-FOLIOZZ1 X
    public AlphanumericVar getWsFoliozz1() {
        return this.wsFoliozz.getWsFoliozz1();
    }

    public void setWsFoliozz1(final String wsFoliozz1) {
        this.wsFoliozz.setWsFoliozz1(wsFoliozz1);
    }

    public void setWsFoliozz1(final IAlphanumericValue wsFoliozz1) {
        this.wsFoliozz.setWsFoliozz1(wsFoliozz1);
    }

    // Property: wsFoliozz.wsFoliozz2 -> 05 WS-FOLIOZZ2 9(7)
    public UnsignedNumericVar getWsFoliozz2() {
        return this.wsFoliozz.getWsFoliozz2();
    }

    public void setWsFoliozz2(final int wsFoliozz2) {
        this.wsFoliozz.setWsFoliozz2(wsFoliozz2);
    }

    public void setWsFoliozz2(final INumericValue wsFoliozz2) {
        this.wsFoliozz.setWsFoliozz2(wsFoliozz2);
    }


    // Deeper properties

    // Property: wsFolioxxn.wsFolioxx1n -> 05 WS-FOLIOXX1N X
    public AlphanumericVar getWsFolioxx1n() {
        return this.wsFolioxxn.getWsFolioxx1n();
    }

    public void setWsFolioxx1n(final String wsFolioxx1n) {
        this.wsFolioxxn.setWsFolioxx1n(wsFolioxx1n);
    }

    public void setWsFolioxx1n(final IAlphanumericValue wsFolioxx1n) {
        this.wsFolioxxn.setWsFolioxx1n(wsFolioxx1n);
    }

    // Property: wsFolioxxn.wsFolioxx2n -> 05 WS-FOLIOXX2N 9(7)
    public UnsignedNumericVar getWsFolioxx2n() {
        return this.wsFolioxxn.getWsFolioxx2n();
    }

    public void setWsFolioxx2n(final int wsFolioxx2n) {
        this.wsFolioxxn.setWsFolioxx2n(wsFolioxx2n);
    }

    public void setWsFolioxx2n(final INumericValue wsFolioxx2n) {
        this.wsFolioxxn.setWsFolioxx2n(wsFolioxx2n);
    }


    // Deeper properties

    // Property: wsFolioxxp.wsFolioxx1p -> 05 WS-FOLIOXX1P X
    public AlphanumericVar getWsFolioxx1p() {
        return this.wsFolioxxp.getWsFolioxx1p();
    }

    public void setWsFolioxx1p(final String wsFolioxx1p) {
        this.wsFolioxxp.setWsFolioxx1p(wsFolioxx1p);
    }

    public void setWsFolioxx1p(final IAlphanumericValue wsFolioxx1p) {
        this.wsFolioxxp.setWsFolioxx1p(wsFolioxx1p);
    }

    // Property: wsFolioxxp.wsFolioxx2p -> 05 WS-FOLIOXX2P 9(7)
    public UnsignedNumericVar getWsFolioxx2p() {
        return this.wsFolioxxp.getWsFolioxx2p();
    }

    public void setWsFolioxx2p(final int wsFolioxx2p) {
        this.wsFolioxxp.setWsFolioxx2p(wsFolioxx2p);
    }

    public void setWsFolioxx2p(final INumericValue wsFolioxx2p) {
        this.wsFolioxxp.setWsFolioxx2p(wsFolioxx2p);
    }


    // Deeper properties

    // Property: wsFlaDes.wsFlaDesDos -> 05 WS-FLA-DES-DOS XX
    public String getWsFlaDesDos() {
        return this.wsFlaDes.getWsFlaDesDos();
    }

    public void setWsFlaDesDos(final String wsFlaDesDos) {
        this.wsFlaDes.setWsFlaDesDos(wsFlaDesDos);
    }

    // Property: wsFlaDes.wsFlaDesResto -> 05 WS-FLA-DES-RESTO X(38)
    public String getWsFlaDesResto() {
        return this.wsFlaDes.getWsFlaDesResto();
    }

    public void setWsFlaDesResto(final String wsFlaDesResto) {
        this.wsFlaDes.setWsFlaDesResto(wsFlaDesResto);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsGerZon = 0;
        this.wsAgtIdr = 0;
        this.wsFlaNum = "";
        this.wsDraCve = 0;
        this.wsMesAux = 0;
        this.wsFinAgc = 0;
        this.wsFinAgt = 0;
        this.wsFinCna = 0;
        this.wsFinCna1 = 0;
        this.wsFinFla = 0;
        this.wsFinFla2 = 0;
        this.wsFinMte = 0;
        this.wsFinOrv = 0;
        this.wsFinDrc = 0;
        this.i = 0;
        this.j = 0;
        this.contador = 0;
        this.contLineas = 0;
        this.contLineas1 = 0;
        this.contHojas = 0;
        this.contHojas1 = 0;
        this.flaNumAux.initialize();
        this.wsFolioxx.initialize();
        this.wsFoliozz.initialize();
        this.wsFolioxxn.initialize();
        this.wsFolioxxp.initialize();
        this.wsFlaDes.initialize();
    }

}

