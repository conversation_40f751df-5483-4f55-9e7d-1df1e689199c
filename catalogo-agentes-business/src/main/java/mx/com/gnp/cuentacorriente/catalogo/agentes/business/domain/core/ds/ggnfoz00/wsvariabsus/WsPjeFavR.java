package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-PJE-FAV-R.
 *
 */
@Setter
@Getter
public class WsPjeFavR extends DataStruct {
    // Properties
    private AlphanumericVar rsPjeFav3 = new AlphanumericVar(this, 3);        // 10 RS-PJE-FAV3 X(03)
    private AlphanumericVar rsPjeFav4 = new AlphanumericVar(this, 4);        // 10 RS-PJE-FAV4 X(04)

    public WsPjeFavR() {
        super();
        initialize();
    }

    public WsPjeFavR(DataContainer parent) {
        super(parent);
    }

    public WsPjeFavR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRsPjeFav3(final String rsPjeFav3) {
        this.rsPjeFav3.setValue(rsPjeFav3);
    }

    public void setRsPjeFav3(final IAlphanumericValue rsPjeFav3) {
        this.rsPjeFav3.setValue(rsPjeFav3);
    }

    public void setRsPjeFav4(final String rsPjeFav4) {
        this.rsPjeFav4.setValue(rsPjeFav4);
    }

    public void setRsPjeFav4(final IAlphanumericValue rsPjeFav4) {
        this.rsPjeFav4.setValue(rsPjeFav4);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rsPjeFav3.clear();
        this.rsPjeFav4.clear();
    }

}

