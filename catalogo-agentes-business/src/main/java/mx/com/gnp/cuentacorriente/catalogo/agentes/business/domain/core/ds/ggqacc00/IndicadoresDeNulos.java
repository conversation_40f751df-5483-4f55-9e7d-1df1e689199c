package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos.IndicadoresCna;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos.IndicadoresDsc;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos.IndicadoresDma0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos.IndicadoresCma0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos.IndicadoresAgt0;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICADORES-DE-NULOS.
 *
 */
@Setter
@Getter
public class IndicadoresDeNulos { // VStruct
    // Properties
    private IndicadoresCna indicadoresCna = new  IndicadoresCna();           // 03 INDICADORES-CNA
    private IndicadoresDsc indicadoresDsc = new  IndicadoresDsc();           // 03 INDICADORES-DSC
    private IndicadoresDma0 indicadoresDma0 = new  IndicadoresDma0();        // 03 INDICADORES-DMA0
    private IndicadoresCma0 indicadoresCma0 = new  IndicadoresCma0();        // 03 INDICADORES-CMA0
    private IndicadoresAgt0 indicadoresAgt0 = new  IndicadoresAgt0();        // 03 INDICADORES-AGT0

    public IndicadoresDeNulos() {
        initialize();
    }




    // Deeper properties

    // Property: indicadoresCna.indcnatfp -> 05 INDCNATFP S9(4) COMPUTATIONAL
    public int getIndcnatfp() {
        return this.indicadoresCna.getIndcnatfp();
    }

    public void setIndcnatfp(final int indcnatfp) {
        this.indicadoresCna.setIndcnatfp(indcnatfp);
    }

    // Property: indicadoresCna.indcnamte -> 05 INDCNAMTE S9(4) COMPUTATIONAL
    public int getIndcnamte() {
        return this.indicadoresCna.getIndcnamte();
    }

    public void setIndcnamte(final int indcnamte) {
        this.indicadoresCna.setIndcnamte(indcnamte);
    }


    // Deeper properties

    // Property: indicadoresDsc.inddscdsg -> 05 INDDSCDSG S9(4) COMPUTATIONAL
    public int getInddscdsg() {
        return this.indicadoresDsc.getInddscdsg();
    }

    public void setInddscdsg(final int inddscdsg) {
        this.indicadoresDsc.setInddscdsg(inddscdsg);
    }


    // Deeper properties

    // Property: indicadoresDma0.inddmaagt -> 05 INDDMAAGT S9(4) COMPUTATIONAL
    public int getInddmaagt() {
        return this.indicadoresDma0.getInddmaagt();
    }

    public void setInddmaagt(final int inddmaagt) {
        this.indicadoresDma0.setInddmaagt(inddmaagt);
    }

    // Property: indicadoresDma0.inddmaidr -> 05 INDDMAIDR S9(4) COMPUTATIONAL
    public int getInddmaidr() {
        return this.indicadoresDma0.getInddmaidr();
    }

    public void setInddmaidr(final int inddmaidr) {
        this.indicadoresDma0.setInddmaidr(inddmaidr);
    }

    // Property: indicadoresDma0.inddmacol -> 05 INDDMACOL S9(4) COMPUTATIONAL
    public int getInddmacol() {
        return this.indicadoresDma0.getInddmacol();
    }

    public void setInddmacol(final int inddmacol) {
        this.indicadoresDma0.setInddmacol(inddmacol);
    }

    // Property: indicadoresDma0.inddmatdm -> 05 INDDMATDM S9(4) COMPUTATIONAL
    public int getInddmatdm() {
        return this.indicadoresDma0.getInddmatdm();
    }

    public void setInddmatdm(final int inddmatdm) {
        this.indicadoresDma0.setInddmatdm(inddmatdm);
    }

    // Property: indicadoresDma0.inddmacpo -> 05 INDDMACPO S9(4) COMPUTATIONAL
    public int getInddmacpo() {
        return this.indicadoresDma0.getInddmacpo();
    }

    public void setInddmacpo(final int inddmacpo) {
        this.indicadoresDma0.setInddmacpo(inddmacpo);
    }


    // Deeper properties

    // Property: indicadoresCma0.indcmades -> 05 INDCMADES S9(4) COMPUTATIONAL
    public int getIndcmades() {
        return this.indicadoresCma0.getIndcmades();
    }

    public void setIndcmades(final int indcmades) {
        this.indicadoresCma0.setIndcmades(indcmades);
    }


    // Deeper properties

    // Property: indicadoresAgt0.indagtnal -> 05 INDAGTNAL S9(4) COMPUTATIONAL
    public int getIndagtnal() {
        return this.indicadoresAgt0.getIndagtnal();
    }

    public void setIndagtnal(final int indagtnal) {
        this.indicadoresAgt0.setIndagtnal(indagtnal);
    }

    // Property: indicadoresAgt0.indagtpef -> 05 INDAGTPEF S9(4) COMPUTATIONAL
    public int getIndagtpef() {
        return this.indicadoresAgt0.getIndagtpef();
    }

    public void setIndagtpef(final int indagtpef) {
        this.indicadoresAgt0.setIndagtpef(indagtpef);
    }

    // Property: indicadoresAgt0.indagttca -> 05 INDAGTTCA S9(4) COMPUTATIONAL
    public int getIndagttca() {
        return this.indicadoresAgt0.getIndagttca();
    }

    public void setIndagttca(final int indagttca) {
        this.indicadoresAgt0.setIndagttca(indagttca);
    }

    // Property: indicadoresAgt0.indagtmte -> 05 INDAGTMTE S9(4) COMPUTATIONAL
    public int getIndagtmte() {
        return this.indicadoresAgt0.getIndagtmte();
    }

    public void setIndagtmte(final int indagtmte) {
        this.indicadoresAgt0.setIndagtmte(indagtmte);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.indicadoresCna.initialize();
        this.indicadoresDsc.initialize();
        this.indicadoresDma0.initialize();
        this.indicadoresCma0.initialize();
        this.indicadoresAgt0.initialize();
    }

}

