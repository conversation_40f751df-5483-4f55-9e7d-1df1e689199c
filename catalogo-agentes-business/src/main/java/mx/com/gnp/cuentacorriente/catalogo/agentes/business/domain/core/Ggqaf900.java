
/**
 *  Program: Ggqaf900.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00olcpy.AbReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00lncpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa0053cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcqthfo0cpy.Dclgcqthfo0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00ogcpy;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaf900.WsTrabajo;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00n9cpy.WsLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.components.Ggqa0049cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00n9cpy.WsLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcqthfo0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcqthfo0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqaf900 extends AbstractCobolOs390Program implements IProgram {


    // Copy file QA$CBLCPY$GGQA00OL
    private AbReg abReg = new  AbReg();                                      // 01 AB-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00OL

    // Copy file QA$CBLCPY$GGQA00LN
    private Ggqa00lncpy ggqa00lncpy = new  Ggqa00lncpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00LN

    // Copy file QA$CBLCPY$GGQA0053
    private Ggqa0053cpy ggqa0053cpy = new  Ggqa0053cpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA0053

    // Auto variables
//  private int ccI;                                // CC-I // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GCQTHFO0
    private Dclgcqthfo0 dclgcqthfo0 = new  Dclgcqthfo0();                    // 01 DCLGCQTHFO0 UsedAsParameter
    // End copy file QA$CBLCPY$GCQTHFO0

    // Copy file QA$CBLCPY$GGQA00OG
    private Ggqa00ogcpy ggqa00ogcpy = new  Ggqa00ogcpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00OG

    // Auto variables
//  private int t999MdI;                            // T999-MD-I // [@WARNING variable NOT USED]
    private IndexVar iError = new IndexVar();        // I-ERROR
    // Level 1
    private AlphanumericVar w000Prog = new AlphanumericVar(8);               // 01 W000-PROG X(08)
//  private String w000Fs = "";                                             // 01 W000-FS XX // [@WARNING variable NOT USED]
    private String w000Insert = "INSERT";                                   // 01 W000-INSERT X(06)
    private String w000Update = "UPDATE";                                   // 01 W000-UPDATE X(06)
    private String w000Select = "SELECT";                                   // 01 W000-SELECT X(06)
    private String w000Open = "OPEN";                                       // 01 W000-OPEN X(04)
    private String w000Fetch = "FETCH";                                     // 01 W000-FETCH X(05)
    private String w000Close = "CLOSE";                                     // 01 W000-CLOSE X(05)
//  private String w000RegAct = "1";                                        // 01 W000-REG-ACT X(01) // [@WARNING variable NOT USED]
    private WsTrabajo wsTrabajo = new  WsTrabajo();                          // 01 WS-TRABAJO

    // Copy file QA$CBLCPY$GGQA00N9
    private WsLinkReg wsLinkReg = new  WsLinkReg();                          // 01 WS-LINK-REG UsedAsParameter

    private Sqlca sqlca;

    // Copy routines
    private Ggqa0049cpy ggqa0049cpy;


    // Declare Sql Ports
    private IGcqthfo0Port gcqthfo0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gcqthfo0Model> gcqthfo0Model;

    // Declare Sql Cursors
    private ICursor<Gcqthfo0Model> c1cdCursor;

    public Ggqaf900(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();

        // Copy routines
        ggqa0049cpy = new Ggqa0049cpy(this, ggqa00lncpy, abReg, w000Prog);

        initialize();
    }

    // Initial values
    @Override
    public void initialize() {
        this.w000Prog.setValue("GGQAF900");
        this.w000Insert = "INSERT";
        this.w000Update = "UPDATE";
        this.w000Select = "SELECT";
        this.w000Open = "OPEN";
        this.w000Fetch = "FETCH";
        this.w000Close = "CLOSE";
//        this.w000RegAct = "1"; // [@WARNING variable NOT USED]
    }


    /**
     *  Main execution entry point of the program.
     */
    public void run(AlphanumericVar wsLinkReg) {
        this.wsLinkReg.setValue(wsLinkReg);
        try {
            run();
        } finally {
            wsLinkReg.setValue(this.wsLinkReg);
        }
    }

    @Override
    public void run(Object... args) {
        assignInputArgument(args, 0, this.wsLinkReg);
        try {
            run();
        } finally {
            updateOutputArgument(args, 0, this.wsLinkReg);
        }
    }

    public void run() {
        sqlDelayedParagraph();
        p001Proceso();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(c1cdCursor == null) {
            c1cdCursor = gcqthfo0Port.createCursorC1cd(
                () -> null);
        }
        //
        // EXIT-SQL-DELAYED-PARAGRAPH - Last paragraph
        //
    }

    /**
     *  Section: 001-PROCESO
     *  Paragraph: unnamedProcedure.
     */
    void p001Proceso() {
        p010Inicio();
        p020Procesa();
        p100Termina();
    }

    /**
     *  Section: 010-INICIO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA INICIO DE PROGRAMA
     */
    void p010Inicio() {
        p060MueveVarLinkHost();
    }

    /**
     *  Section: 020-PROCESA
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA REGISTRO
     */
    void p020Procesa() {
        if (wsLinkReg.getLhfoOpcion().isEqual(w000Select)) {
            p050ValidaSelect();
        } else if (wsLinkReg.getLhfoOpcion().isEqual(w000Insert)) {
            p030ValidaInsert();
        } else if (wsLinkReg.getLhfoOpcion().isEqual(w000Update)) {
            p040ValidaUpdate();
        } else if (wsLinkReg.getLhfoOpcion().isEqual(w000Open)) {
            p150AbreCursorC1cd();
        } else if (wsLinkReg.getLhfoOpcion().isEqual(w000Fetch)) {
            p160ValidaFetch();
            p170ValidaRegActivo();
        } else if (wsLinkReg.getLhfoOpcion().isEqual(w000Close)) {
            p180CierraCursorC1cd();
        } else {
            abReg.setAbProceso("020-PROCESA");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 050-VALIDA-SELECT
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA SELECT
     */
    void p050ValidaSelect() {
        wsTrabajo.setWsOfnCve(wsLinkReg.getOfnCve().toInt());

        p110SelectHfo();
        if (ggqa0053cpy.getBdErr().isEqual("0")) {
            p070MueveVarHostLink();
        }
        wsLinkReg.setLhfoSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 030-VALIDA-INSERT
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA INSERT
     */
    void p030ValidaInsert() {
        p120InsertHfo();
        wsLinkReg.setLhfoSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 040-VALIDA-UPDATE
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA PROCESO UPDATE
     */
    void p040ValidaUpdate() {
        p130UpdateHfo();
        wsLinkReg.setLhfoSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 060-MUEVE-VAR-LINK-HOST
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE DATOS DE LINKAGE A VARIABLES HOST
     */
    void p060MueveVarLinkHost() {
        dclgcqthfo0.setValue(wsLinkReg.getLhfoReg());
    }

    /**
     *  Section: 070-MUEVE-VAR-HOST-LINK
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE DATOS DE  VARIABLES HOST  A LINKAGE
     */
    void p070MueveVarHostLink() {
        wsLinkReg.getLhfoReg().setValue(dclgcqthfo0);
    }

    /**
     *  Section: 110-SELECT-HFO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA SELECT
     */
    void p110SelectHfo() {
        genericModel = 
            gcqthfo0Port.select(
                GenericModel.builder().values(List.of(
                    wsTrabajo.getWsOfnCve(),
                    wsTrabajo.getWsOfnCve()))
                .build());

        if(genericModel.isPresent()) {
            dclgcqthfo0.setOfnCve(fromHostInt(genericModel.get().values().get(0)));
            dclgcqthfo0.setHfoFecIniPdo(fromHostString(genericModel.get().values().get(1)));
            dclgcqthfo0.setHfoFecFinPdo(fromHostString(genericModel.get().values().get(2)));
            dclgcqthfo0.setHfoRfcRcp(fromHostString(genericModel.get().values().get(3)));
            dclgcqthfo0.setHfoNomRcp(fromHostString(genericModel.get().values().get(4)));
            dclgcqthfo0.setOfnCae(fromHostString(genericModel.get().values().get(5)));
            dclgcqthfo0.setOfnNum(fromHostString(genericModel.get().values().get(6)));
            dclgcqthfo0.setOfnInt(fromHostString(genericModel.get().values().get(7)));
            dclgcqthfo0.setOfnCol(fromHostString(genericModel.get().values().get(8)));
            dclgcqthfo0.setMndNom(fromHostString(genericModel.get().values().get(9)));
            dclgcqthfo0.setEdoNom(fromHostString(genericModel.get().values().get(10)));
            dclgcqthfo0.setPaiNom(fromHostString(genericModel.get().values().get(11)));
            dclgcqthfo0.setCpoCve(fromHostInt(genericModel.get().values().get(12)));
            dclgcqthfo0.setUsuCve(fromHostString(genericModel.get().values().get(13)));
            dclgcqthfo0.setNomPrg(fromHostString(genericModel.get().values().get(14)));
            dclgcqthfo0.setFecUltAct(fromHostString(genericModel.get().values().get(15)));
        }
        abReg.setAbProceso("110-SELECT-HFO");
        p999VerErrSql();
    }

    /**
     *  Section: 120-INSERT-HFO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA INSERT
     */
    void p120InsertHfo() {
        gcqthfo0Port.insert(
            GenericModel.builder().values(List.of(
                dclgcqthfo0.getOfnCve().toInt(),
                toHostString(dclgcqthfo0.getHfoFecIniPdo()),
                toHostString(dclgcqthfo0.getHfoFecFinPdo()),
                toHostString(dclgcqthfo0.getHfoRfcRcp()),
                toHostString(dclgcqthfo0.getHfoNomRcp()),
                toHostString(dclgcqthfo0.getOfnCae()),
                toHostString(dclgcqthfo0.getOfnNum()),
                toHostString(dclgcqthfo0.getOfnInt()),
                toHostString(dclgcqthfo0.getOfnCol()),
                toHostString(dclgcqthfo0.getMndNom()),
                toHostString(dclgcqthfo0.getEdoNom()),
                toHostString(dclgcqthfo0.getPaiNom()),
                dclgcqthfo0.getCpoCve().toInt(),
                toHostString(dclgcqthfo0.getUsuCve()),
                toHostString(dclgcqthfo0.getNomPrg())))
            .build());
        abReg.setAbProceso("120-INSERT-HFO");
        p999VerErrSql();
    }

    /**
     *  Section: 130-UPDATE-HFO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA UPDATE
     */
    void p130UpdateHfo() {
        gcqthfo0Port.update(
            Gcqthfo0Model.builder()
                .hfoFecFinPdo(toHostLocalDate(dclgcqthfo0.getHfoFecFinPdo()))
                .ofnCve(dclgcqthfo0.getOfnCve().toInt())
                .hfoFecIniPdo(toHostLocalDate(dclgcqthfo0.getHfoFecIniPdo()))
            .build());
        abReg.setAbProceso("130-UPDATE-HFO");
        p999VerErrSql();
    }

    /**
     *  Section: 150-ABRE-CURSOR-C1CD
     *  Paragraph: unnamedProcedure.
     *
     * ABRE CURSOR C1CD
     */
    void p150AbreCursorC1cd() {
        abReg.setAbProceso("150-ABRE-CURSOR-C1CD");
        p152OpenC1cd();
        wsLinkReg.setLhfoSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 152-OPEN-C1CD
     *  Paragraph: unnamedProcedure.
     *
     */
    void p152OpenC1cd() {
        c1cdCursor.open();
        p999VerErrSql();
    }

    /**
     *  Section: 160-VALIDA-FETCH
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA PROCESO FETCH
     */
    void p160ValidaFetch() {
        p162LeeCursorC1cd();
        if (ggqa0053cpy.getBdErr().isEqual("1")) {
            wsLinkReg.setLhfoSwErr(ggqa0053cpy.getBdErr());
        } else {
            //        PERFORM UNTIL CED-REG-ACT = W000-REG-ACT OR BD-ERR = '1'  
            //           PERFORM 162-LEE-CURSOR-C1CD                            
            //        END-PERFORM                                               
            ;
        }
    }

    /**
     *  Section: 162-LEE-CURSOR-C1CD
     *  Paragraph: unnamedProcedure.
     *
     */
    void p162LeeCursorC1cd() {
        abReg.setAbProceso("162-LEE-CURSOR-C1CD ");
        gcqthfo0Model = c1cdCursor.next();

        if (gcqthfo0Model.isPresent()) {
            dclgcqthfo0.setOfnCve(fromHostInt(gcqthfo0Model.get().ofnCve()));
            dclgcqthfo0.setHfoFecIniPdo(fromHostString(gcqthfo0Model.get().hfoFecIniPdo()));
            dclgcqthfo0.setHfoFecFinPdo(fromHostString(gcqthfo0Model.get().hfoFecFinPdo()));
            dclgcqthfo0.setHfoRfcRcp(fromHostString(gcqthfo0Model.get().hfoRfcRcp()));
            dclgcqthfo0.setHfoNomRcp(fromHostString(gcqthfo0Model.get().hfoNomRcp()));
            dclgcqthfo0.setOfnCae(fromHostString(gcqthfo0Model.get().ofnCae()));
            dclgcqthfo0.setOfnNum(fromHostString(gcqthfo0Model.get().ofnNum()));
            dclgcqthfo0.setOfnInt(fromHostString(gcqthfo0Model.get().ofnInt()));
            dclgcqthfo0.setOfnCol(fromHostString(gcqthfo0Model.get().ofnCol()));
            dclgcqthfo0.setMndNom(fromHostString(gcqthfo0Model.get().mndNom()));
            dclgcqthfo0.setEdoNom(fromHostString(gcqthfo0Model.get().edoNom()));
            dclgcqthfo0.setPaiNom(fromHostString(gcqthfo0Model.get().paiNom()));
            dclgcqthfo0.setCpoCve(fromHostInt(gcqthfo0Model.get().cpoCve()));
            dclgcqthfo0.setUsuCve(fromHostString(gcqthfo0Model.get().usuCve()));
            dclgcqthfo0.setNomPrg(fromHostString(gcqthfo0Model.get().nomPrg()));
            dclgcqthfo0.setFecUltAct(fromHostString(gcqthfo0Model.get().fecUltAct()));
        }
        p999VerErrSql();
        wsLinkReg.setLhfoSwErr(ggqa0053cpy.getBdErr());
    }

    /**
     *  Section: 170-VALIDA-REG-ACTIVO
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA REGISTRO ACTIVO
     */
    void p170ValidaRegActivo() {
        //     IF CED-REG-ACT = W000-REG-ACT AND BD-ERR = '0'               
        if (ggqa0053cpy.getBdErr().isEqual("0")) {
            p070MueveVarHostLink();
        }
    }

    /**
     *  Section: 180-CIERRA-CURSOR-C1CD
     *  Paragraph: unnamedProcedure.
     *
     * CIERRA CURSOR C1CD
     */
    void p180CierraCursorC1cd() {
        abReg.setAbProceso("180-CIERRA-CURSOR-C1CD");
        p999CloseC1cd();
    }

    /**
     *  Section: 999-CLOSE-C1CD
     *  Paragraph: unnamedProcedure.
     *
     */
    void p999CloseC1cd() {
        if (c1cdCursor != null) {
            c1cdCursor.close();
        }
        p999VerErrSql();
    }

    /**
     *  Section: 100-TERMINA
     *  Paragraph: unnamedProcedure.
     *
     * TERMINA PROGRAMA
     */
    void p100Termina() {
        throw new ExitProgramException();
    }

    /**
     *  Section: 990-CIF-CTL
     *  Paragraph: unnamedProcedure.
     *
     */
    void p990CifCtl() {
    }

    /**
     *  Section: 999-VER-ERR-SQL
     *  Paragraph: unnamedProcedure.
     *
     * EXEC SQL
     * INCLUDE GGQA00OI
     * END-EXEC.
     * AHCPSQLE: RUTINA PARA EL MANEJO DEL SQL ERROR
     * AUTOR: SOFTTEK/CASE/TEN  17/NOV/95
     * TEN 1.50 CAF: 002
     * SE ADICIONAN CON EL PARRAFO 999-VER-SQL-ERR
     * (ES UN COPY FISICO, QUE SE ADICIONA POR EL CONVERTIDOR DE
     * LENGUAJE)
     * REFERENCIAS:                AHCWSQLE
     */
    void p999VerErrSql() {
        ggqa0053cpy.setBdErr("0");
        if (sqlca.getSqlcode() == 100) {
            ggqa0053cpy.setBdErr("1");
        } else if (sqlca.getSqlcode() != 0) {
            p999DespliegaSqlMsg();
            abReg.setAbCodigon(sqlca.getSqlcode());
            abReg.setAbProceso("999-VER-SQL-ERR");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 999-DESPLIEGA-SQL-MSG
     *  Paragraph: unnamedProcedure.
     *
     */
    void p999DespliegaSqlMsg() {
        display("ERROR SQL; -- <INICIO DESCRIPCION>: ");
        runProgram("DSNTIAR", sqlca, 
                ggqa00ogcpy.getW999ErrorMensaje(), 
                ggqa00ogcpy.getW999ErrorTextLong());
        ggqa00ogcpy.setW999ContadorLng(1);
        while (!(ggqa00ogcpy.getW999ContadorLng().toInt() > 10)) {
                iError.setValue(ggqa00ogcpy.getW999ContadorLng());
                display("--", ggqa00ogcpy.getW999ErrorText().at(iError));
                ggqa00ogcpy.getW999ContadorLng().add(1);
        }
        display("ERROR SQL; -- <FIN DESCRIPCION>");
    }

    @Autowired
    public void setGcqthfo0Port(IGcqthfo0Port gcqthfo0Port) {
        this.gcqthfo0Port = gcqthfo0Port;
        this.gcqthfo0Port.setProgramContext(getProgramContext());
    }
}
