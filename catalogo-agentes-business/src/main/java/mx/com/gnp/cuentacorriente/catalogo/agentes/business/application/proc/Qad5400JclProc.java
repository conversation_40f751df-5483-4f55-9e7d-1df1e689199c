/**
 *  JCL Program: QAD5400.
 *  Code generation type: <PERSON><PERSON>NDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Qad5400JclProc extends AbstractJclProgram {

    public Qad5400JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("QAD5400", (qad5400) -> {
            qad5400.addParameter("PROC");
            if (qad5400.getLastReturnCode()<=0) {
                /*//**********************************************************************/
                /*//*  NOMBRE PROCESO : VERSIONAMIENTO DE OFICINAS*/
                /*//*  APLICACION     : CUENTA CORRIENTE (ACTUALIZACION DE INFORMACION)*/
                /*//*  PERIODICIDAD   : DIARIA*/
                /*//*  OBJETIVO       : DE ACUERDO A LA INFORMACION DEL ARCHIVO DE OFICIN*/
                /*//*                   SE REALIZARA (INSERT, UPDATE) A LA ENTIDAD DE*/
                /*//*                   HISTORICO DE FACTURACION POR OFICINA (HFO0).*/
                /*//*  DEPENDENCIAS   : N/A*/
                /*//*  FECHA          : 15/OCTUBRE/2014*/
                /*//**********************************************************************/
                /*//*U11RMS   EXEC PGM=U11RMS,*/
                /*//*             PARM=('P,,,RE=//')*/
                /*//*CMT      DD  DSN=SYS5.CA11.CMT,DISP=SHR*/
                /*//*RMSRPT   DD  SYSOUT=**/
                /*//**********************************************************************/
                /*//*********************************************************************/
                /*//*                      * QAD5404 **/
                /*//* OBJETIVO:   BORRA BIBLIOTECA DE CARDS*/
                /*//* PROGRAMA:   IEFBR14*/
                /*//* REINICIO:   SI*/
                /*//*********************************************************************/
                qad5400.execProgram("QAD5404", "IEFBR14", (qad5404) -> {
                    qad5404.addDataDefinition("BIBCARDS", qad5404.getParameter("ALIASS")+"QAD5400",  (bibcards) -> {
                        bibcards.addParameter("DISP", "(MOD,DELETE,DELETE)");
                        bibcards.addParameter("UNIT",bibcards.getParameter("VOLPER"));
                        bibcards.addParameter("SPACE", "(CYL,(1,,1),RLSE)");
                        bibcards.addParameter("DCB", "(DSORG=PS)");
                    });
                });
            }
            if (qad5400.getLastReturnCode()<=0) {
                /*//*********************************************************************/
                /*//*                      * QAD5403 **/
                /*//* OBJETIVO:   GENERA LAS CARDS DE RUN, SORT E IDCAMS DINAMICA*/
                /*//* PROGRAMA:   GGQA3M00*/
                /*//* REINICIO:   SI*/
                /*//*********************************************************************/
                qad5400.execProgram("QAD5403", "GGQA3M00", (qad5403) -> {
                    qad5403.addParameter("PARM","SSID="+qad5403.getParameter("SSID")+",AMB="+qad5403.getParameter("AMB")+",ALIASS="+qad5403.getParameter("ALIASS"));
                    /*//**/
                    qad5403.addDataDefinition("CIFRACTR",  (cifractr) -> {
                        cifractr.addParameter("SYSOUT","*");
                    });
                    qad5403.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5403.addDummyDataDefinition("PARAMETR",  (parametr) -> {
                    });
                    /*//**/
                    qad5403.addDataDefinition("CARDPAR1", qad5403.getParameter("BIBCAR")+"(QAD5401)",  (cardpar1) -> {
                        cardpar1.addParameter("DISP","SHR");
                    });
                    qad5403.addDataDefinition("CARDEXE1", qad5403.getParameter("ALIASS")+"QAD5400(QAD5401)",  (cardexe1) -> {
                        cardexe1.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        cardexe1.addParameter("UNIT",cardexe1.getParameter("VOLPER"));
                        cardexe1.addParameter("SPACE", "(CYL,(1,,1),RLSE)");
                        cardexe1.addParameter("DCB", "(DSORG=PS)");
                    });
                    qad5403.addDataDefinition("CARDPAR2", qad5403.getParameter("BIBCAR")+"(QAD5402)",  (cardpar2) -> {
                        cardpar2.addParameter("DISP","SHR");
                    });
                    qad5403.addDataDefinition("CARDEXE2", qad5403.getParameter("ALIASS")+"QAD5400(QAD5402)",  (cardexe2) -> {
                        cardexe2.addParameter("DISP","SHR");
                        cardexe2.addParameter("VOL","REF=*.QAD5403.CARDEXE1");
                    });
                });
            }
            if (qad5400.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//* PASO    :   QAD5402*/
                /*//* OBJETIVO:   EVALUA PERIODOS FACTURADOS PARA MARCALOS SI*/
                /*//*             TIENEN MAS DE 150 MOVIMIENTOS*/
                /*//* PROGRAMA:   GGQASV00*/
                /*//* REINICIO:   REINICIABLE A PARTIR DE QAD5402*/
                /*//**********************************************************************/
                qad5400.execProgram("QAD5402", "IKJEFT01", (qad5402) -> {
                    qad5402.addParameter("DYNAMNBR","20");
                    qad5402.addParameter("REGION","0M");
                    qad5402.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5402.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    qad5402.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qad5402.addDummyDataDefinition("SYSUDUMP",  (sysudump) -> {
                    });
                    qad5402.addDataDefinition("SYSTSIN", qad5402.getParameter("ALIASS")+"QAD5400(QAD5402)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qad5400.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*  PASO     : QAD5401*/
                /*//*  OBJETIVO : EJECUTA PROGRAMA DE VERSIONAMIENTO DE AGENTES*/
                /*//*  REINICIO : NO REINICIABLE*/
                /*//*  PROGRAMA : GGQAC700*/
                /*//**********************************************************************/
                qad5400.execProgram("QAD5401", "IKJEFT01", (qad5401) -> {
                    qad5401.addParameter("DYNAMNBR","20");
                    /*//*   CATALOGO DE DIRECCIONES DEL RECEPTOR*/
                    qad5401.addDataDefinition("GGQAC7I1", "PGA.EACQ280",  (ggqac7i1) -> {
                        ggqac7i1.addParameter("DISP","SHR");
                    });
                    qad5401.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5401.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    qad5401.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qad5401.addDummyDataDefinition("SYSUDUMP",  (sysudump) -> {
                    });
                    /*//* CARD DEL PROGRAMA A EJECUTAR*/
                    qad5401.addDataDefinition("SYSTSIN", qad5401.getParameter("ALIASS")+"QAD5400(QAD5401)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
