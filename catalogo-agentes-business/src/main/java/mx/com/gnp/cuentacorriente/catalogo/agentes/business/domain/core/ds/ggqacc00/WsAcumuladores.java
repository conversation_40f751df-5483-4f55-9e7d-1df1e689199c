package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-ACUMULADORES.
 *
 */
@Setter
@Getter
public class WsAcumuladores { // VStruct
    // Properties
    private NumericVar wsImporte = new NumericVar(16, 2);                    // 03 WS-IMPORTE S9(14)V99
    private NumericVar wsTotImporte = new NumericVar(16, 2);                 // 03 WS-TOT-IMPORTE S9(14)V99
    private NumericVar wsTotComision = new NumericVar(16, 2);                // 03 WS-TOT-COMISION S9(14)V99
    private NumericVar wsImcomis = new NumericVar(13, 2);                    // 03 WS-IMCOMIS S9(11)V99
    private NumericVar wsImpDssIpu = new NumericVar(13, 2);                  // 03 WS-IMP-DSS-IPU S9(11)V99
    private NumericVar wsImpAncIpu = new NumericVar(13, 2);                  // 03 WS-IMP-ANC-IPU S9(11)V99
    private NumericVar wsDiferencia = new NumericVar(16, 2);                 // 03 WS-DIFERENCIA S9(14)V99
    private NumericVar wsTotalDiferencia = new NumericVar(16, 2);            // 03 WS-TOTAL-DIFERENCIA S9(14)V99
    private NumericVar wsCf5ImpCtoDse = new NumericVar(16, 2);               // 03 WS-CF5-IMP-CTO-DSE S9(14)V99

    public WsAcumuladores() {
        initialize();
    }



    public void setWsImporte(final BigDecimal wsImporte) {
        this.wsImporte.setValue(wsImporte);
    }

    public void setWsImporte(final int wsImporte) {
        this.wsImporte.setValue(wsImporte);
    }

    public void setWsImporte(final INumericValue wsImporte) {
        this.wsImporte.setValue(wsImporte);
    }

    public void setWsTotImporte(final BigDecimal wsTotImporte) {
        this.wsTotImporte.setValue(wsTotImporte);
    }

    public void setWsTotImporte(final int wsTotImporte) {
        this.wsTotImporte.setValue(wsTotImporte);
    }

    public void setWsTotImporte(final INumericValue wsTotImporte) {
        this.wsTotImporte.setValue(wsTotImporte);
    }

    public void setWsTotComision(final BigDecimal wsTotComision) {
        this.wsTotComision.setValue(wsTotComision);
    }

    public void setWsTotComision(final int wsTotComision) {
        this.wsTotComision.setValue(wsTotComision);
    }

    public void setWsTotComision(final INumericValue wsTotComision) {
        this.wsTotComision.setValue(wsTotComision);
    }

    public void setWsImcomis(final BigDecimal wsImcomis) {
        this.wsImcomis.setValue(wsImcomis);
    }

    public void setWsImcomis(final int wsImcomis) {
        this.wsImcomis.setValue(wsImcomis);
    }

    public void setWsImcomis(final INumericValue wsImcomis) {
        this.wsImcomis.setValue(wsImcomis);
    }

    public void setWsImpDssIpu(final BigDecimal wsImpDssIpu) {
        this.wsImpDssIpu.setValue(wsImpDssIpu);
    }

    public void setWsImpDssIpu(final int wsImpDssIpu) {
        this.wsImpDssIpu.setValue(wsImpDssIpu);
    }

    public void setWsImpDssIpu(final INumericValue wsImpDssIpu) {
        this.wsImpDssIpu.setValue(wsImpDssIpu);
    }

    public void setWsImpAncIpu(final BigDecimal wsImpAncIpu) {
        this.wsImpAncIpu.setValue(wsImpAncIpu);
    }

    public void setWsImpAncIpu(final int wsImpAncIpu) {
        this.wsImpAncIpu.setValue(wsImpAncIpu);
    }

    public void setWsImpAncIpu(final INumericValue wsImpAncIpu) {
        this.wsImpAncIpu.setValue(wsImpAncIpu);
    }

    public void setWsDiferencia(final BigDecimal wsDiferencia) {
        this.wsDiferencia.setValue(wsDiferencia);
    }

    public void setWsDiferencia(final int wsDiferencia) {
        this.wsDiferencia.setValue(wsDiferencia);
    }

    public void setWsDiferencia(final INumericValue wsDiferencia) {
        this.wsDiferencia.setValue(wsDiferencia);
    }

    public void setWsTotalDiferencia(final BigDecimal wsTotalDiferencia) {
        this.wsTotalDiferencia.setValue(wsTotalDiferencia);
    }

    public void setWsTotalDiferencia(final int wsTotalDiferencia) {
        this.wsTotalDiferencia.setValue(wsTotalDiferencia);
    }

    public void setWsTotalDiferencia(final INumericValue wsTotalDiferencia) {
        this.wsTotalDiferencia.setValue(wsTotalDiferencia);
    }

    public void setWsCf5ImpCtoDse(final BigDecimal wsCf5ImpCtoDse) {
        this.wsCf5ImpCtoDse.setValue(wsCf5ImpCtoDse);
    }

    public void setWsCf5ImpCtoDse(final int wsCf5ImpCtoDse) {
        this.wsCf5ImpCtoDse.setValue(wsCf5ImpCtoDse);
    }

    public void setWsCf5ImpCtoDse(final INumericValue wsCf5ImpCtoDse) {
        this.wsCf5ImpCtoDse.setValue(wsCf5ImpCtoDse);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsImporte.clear();
        this.wsTotImporte.clear();
        this.wsTotComision.clear();
        this.wsImcomis.clear();
        this.wsImpDssIpu.clear();
        this.wsImpAncIpu.clear();
        this.wsDiferencia.clear();
        this.wsTotalDiferencia.clear();
        this.wsCf5ImpCtoDse.clear();
    }

}

