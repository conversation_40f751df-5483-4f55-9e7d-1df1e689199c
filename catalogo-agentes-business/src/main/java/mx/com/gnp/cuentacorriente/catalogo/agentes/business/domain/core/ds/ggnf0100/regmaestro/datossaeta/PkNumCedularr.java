package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PK-NUM-CEDULARR.
 *
 */
@Setter
@Getter
public class PkNumCedularr extends DataStruct {
    // Properties
    private AlphanumericVar pkNumCedulaTres = new AlphanumericVar(this, 3);  // 07 PK-NUM-CEDULA-TRES XXX
    private AlphanumericVar pkNumCedulaR17 = new AlphanumericVar(this, 17);  // 07 PK-NUM-CEDULA-R17 X(17)

    public PkNumCedularr() {
        super();
        initialize();
    }

    public PkNumCedularr(DataContainer parent) {
        super(parent);
    }

    public PkNumCedularr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkNumCedulaTres(final String pkNumCedulaTres) {
        this.pkNumCedulaTres.setValue(pkNumCedulaTres);
    }

    public void setPkNumCedulaTres(final IAlphanumericValue pkNumCedulaTres) {
        this.pkNumCedulaTres.setValue(pkNumCedulaTres);
    }

    public void setPkNumCedulaR17(final String pkNumCedulaR17) {
        this.pkNumCedulaR17.setValue(pkNumCedulaR17);
    }

    public void setPkNumCedulaR17(final IAlphanumericValue pkNumCedulaR17) {
        this.pkNumCedulaR17.setValue(pkNumCedulaR17);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkNumCedulaTres.clear();
        this.pkNumCedulaR17.clear();
    }

}

