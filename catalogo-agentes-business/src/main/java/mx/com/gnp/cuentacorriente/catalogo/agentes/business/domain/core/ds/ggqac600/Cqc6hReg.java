package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.cqc6hreg.Cqc6hDatos;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: CQC6H-REG.
 *
 */
// 49EMP                                                                  
@Setter
@Getter
public class Cqc6hReg extends DataStruct {
    // Properties
    private Cqc6hDatos cqc6hDatos = new  Cqc6hDatos(this);                   // 02 CQC6H-DATOS

    public Cqc6hReg() {
        super();
        initialize();
    }

    public Cqc6hReg(DataContainer parent) {
        super(parent);
    }

    public Cqc6hReg(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }



    // Deeper properties

    // Property: cqc6hDatos.cqc6hAgt -> 10 CQC6H-AGT X(05)
    public AlphanumericVar getCqc6hAgt() {
        return this.cqc6hDatos.getCqc6hAgt();
    }

    public void setCqc6hAgt(final String cqc6hAgt) {
        this.cqc6hDatos.setCqc6hAgt(cqc6hAgt);
    }

    public void setCqc6hAgt(final IAlphanumericValue cqc6hAgt) {
        this.cqc6hDatos.setCqc6hAgt(cqc6hAgt);
    }

    // Property: cqc6hDatos.cqc6hSpaces -> 10 CQC6H-SPACES X(01)
    public AlphanumericVar getCqc6hSpaces() {
        return this.cqc6hDatos.getCqc6hSpaces();
    }

    public void setCqc6hSpaces(final String cqc6hSpaces) {
        this.cqc6hDatos.setCqc6hSpaces(cqc6hSpaces);
    }

    public void setCqc6hSpaces(final IAlphanumericValue cqc6hSpaces) {
        this.cqc6hDatos.setCqc6hSpaces(cqc6hSpaces);
    }

    // Property: cqc6hDatos.cqc6hIndExc -> 10 CQC6H-IND-EXC X(01)
    public AlphanumericVar getCqc6hIndExc() {
        return this.cqc6hDatos.getCqc6hIndExc();
    }

    public void setCqc6hIndExc(final String cqc6hIndExc) {
        this.cqc6hDatos.setCqc6hIndExc(cqc6hIndExc);
    }

    public void setCqc6hIndExc(final IAlphanumericValue cqc6hIndExc) {
        this.cqc6hDatos.setCqc6hIndExc(cqc6hIndExc);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.cqc6hDatos.initialize();
    }

}

