package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTMTE0-NULL.
 *
 */
// 1087062200*                                                                 06220000
// 1088062300*                                                                 06230000
@Setter
@Getter
public class WsGfvtmte0Null { // VStruct
    // Properties
    private int mte01;                                                      // 03 MTE01 S9(4) COMPUTATIONAL
    private int mte02;                                                      // 03 MTE02 S9(4) COMPUTATIONAL
    private int mte03;                                                      // 03 MTE03 S9(4) COMPUTATIONAL
    private int mte04;                                                      // 03 MTE04 S9(4) COMPUTATIONAL
    private int mte05;                                                      // 03 MTE05 S9(4) COMPUTATIONAL

    public WsGfvtmte0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.mte01 = 0;
        this.mte02 = 0;
        this.mte03 = 0;
        this.mte04 = 0;
        this.mte05 = 0;
    }

}

