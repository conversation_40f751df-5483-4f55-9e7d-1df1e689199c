
/**
 *  Program: Ggnf4100.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.ISqlErrorListener;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.ErrorMessage;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.StatusCode;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.CContadores;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.IIndices;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.WkRegistro;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtbrt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtbrt0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggnf4100 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private AlphanumericVar regDepura = new AlphanumericVar(138);            // 01 REG-DEPURA X(138) UsedAsReferenceParameter
//  private SwBanderas swBanderas = new  SwBanderas();                           // 01 SW-BANDERAS // [@WARNING variable NOT USED]
    // Auto variables
//  private int errorIndex;                         // ERROR-INDEX // [@WARNING variable NOT USED]
    // Level 1
    private ErrorMessage errorMessage = new  ErrorMessage();                 // 01 ERROR-MESSAGE UsedAsParameter
    // Level 77
    private BinaryVar errorTextLen = new BinaryVar(9, 0);            // 77 ERROR-TEXT-LEN S9(9) COMPUTATIONAL UsedAsParameter
    // Level 1
    private StatusCode statusCode = new  StatusCode();                       // 01 STATUS-CODE
    private CContadores cContadores = new  CContadores();                    // 01 C-CONTADORES
    private IIndices iIndices = new  IIndices();                             // 01 I-INDICES
    private WkRegistro wkRegistro = new  WkRegistro();                       // 01 WK-REGISTRO
//  private TPar tPar = new  TPar();                                             // 01 T-PAR // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GFVTBRT0
//  private Gfvtbrt0 gfvtbrt0 = new  Gfvtbrt0();                                 // 01 GFVTBRT0 // [@WARNING variable NOT USED]

    private Sqlca sqlca;
    // Files
    private ISequentialFile depura;



    // Declare Sql Ports
    private IGfvtbrt0Port gfvtbrt0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtbrt0Model> gfvtbrt0Model;

    // Declare Sql Cursors
    private ICursor<GenericModel> cDepuraCursor;

    public Ggnf4100(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initialize();
        initFiles();
    }

    // Initial values
    @Override
    public void initialize() {
        this.errorTextLen.setValue(72);
    }


    public void initFiles() {

        this.depura = createSequentialFile("DEPURA", "SALIDA", regDepura);

        this.depura.onChangeFileStatus(status -> statusCode.setWsFile(status));

    }

    public void run() {
        p000ModuloControl();
    }

    /**
     *  Paragraph: 000-MODULO-CONTROL.
     */
    void p000ModuloControl() {
        inicio();
        proceso();
        fin();
        throw new GobackException();
    }

    /**
     *  Section: INICIO
     *  Paragraph: unnamedProcedure.
     *
     */
    void inicio() {
        depura.openOutput();
        getProgramContext().onSqlError(new ISqlErrorListener() {
            public void onError() {
                db2MsgText();
            }
        });
        declara();
    }

    /**
     *  Section: DECLARA
     *  Paragraph: unnamedProcedure.
     *
     */
    void declara() {
        //  CURSOR DE REGISTROS A DEPURAR                                   
        cDepuraCursor = gfvtbrt0Port.createCursorC_depura(
            () -> null);
    }

    /**
     *  Section: PROCESO
     *  Paragraph: unnamedProcedure.
     *
     */
    void proceso() {
        openDepura();
        fetchDepura();
        while (!(sqlca.getSqlcode() == 100)) {
                writeDepura();
                fetchDepura();
        }
        closeDepura();
        depura.close();
        deleteDepura();
    }

    /**
     *  Section: FIN
     *  Paragraph: unnamedProcedure.
     *
     */
    void fin() {
        //     DISPLAY ' '                                                  
        //     DISPLAY 'CIFRAS CONTROL'                                     
        //     DISPLAY '*'                                                  
        //     DISPLAY 'MOVTOS LEIDOS   : ' C-FETCH-DEPURA                  
        //     DISPLAY 'MOVTOS GRABADOS : ' C-WRITE-DEPURA                  
        //     DISPLAY 'MOVTOS DEPURADOS: ' C-DELETE-GFVTBRT0               
        if (cContadores.getCFetchDepura().compareTo(0) > 0) {
            returnCode.setValue(4);
        }
    }

    /**
     *  Section: OPEN-DEPURA
     *  Paragraph: unnamedProcedure.
     *
     */
    void openDepura() {
        cDepuraCursor.open();
    }

    /**
     *  Section: FETCH-DEPURA
     *  Paragraph: unnamedProcedure.
     *
     */
    void fetchDepura() {
        genericModel = cDepuraCursor.next();

        if (genericModel.isPresent()) {
            wkRegistro.setTablaTabAfe(fromHostString(genericModel.get().values().get(0)));
            wkRegistro.setTablaNum(fromHostString(genericModel.get().values().get(1)));
            wkRegistro.setTablaTipOpe(fromHostString(genericModel.get().values().get(2)));
            wkRegistro.setTablaCodSql(fromHostString(genericModel.get().values().get(3)));
            wkRegistro.setTablaCodEdo(fromHostString(genericModel.get().values().get(4)));
            wkRegistro.setTablaCadOpe(fromHostString(genericModel.get().values().get(5)));
            wkRegistro.setTablaAgtIncEss(fromHostString(genericModel.get().values().get(6)));
            wkRegistro.setTablaAgtIdrInc(fromHostString(genericModel.get().values().get(7)));
            wkRegistro.setTablaCdusuari(fromHostString(genericModel.get().values().get(8)));
            wkRegistro.setTablaCdempusu(fromHostString(genericModel.get().values().get(9)));
            wkRegistro.setTablaNoprogra(fromHostString(genericModel.get().values().get(10)));
            wkRegistro.setTablaTsultmod(fromHostString(genericModel.get().values().get(11)));
        }
        if (sqlca.getSqlcode() == 0) {
            cContadores.getCFetchDepura().add(1);
        }
        //       IF FUNCTION MOD(C-FETCH-DEPURA 1000) = 0                   
        //          DISPLAY 'LEIDOS: ' C-FETCH-DEPURA                       
        //       END-IF                                                     
    }

    /**
     *  Section: CLOSE-DEPURA
     *  Paragraph: unnamedProcedure.
     *
     */
    void closeDepura() {
        if (cDepuraCursor != null) {
            cDepuraCursor.close();
        }
    }

    /**
     *  Section: DELETE-DEPURA
     *  Paragraph: unnamedProcedure.
     *
     */
    void deleteDepura() {
        gfvtbrt0Port.delete(
            null);
        //                                                                  
        cContadores.setCDeleteGfvtbrt0(sqlca.getSqlerrd()[2]);
    }

    /**
     *  Section: WRITE-DEPURA
     *  Paragraph: unnamedProcedure.
     *
     */
    void writeDepura() {
        depura.write(regDepura, wkRegistro);
        cContadores.getCWriteDepura().add(1);
    }

    /**
     *  Section: DB2-MSG-TEXT
     *  Paragraph: unnamedProcedure.
     *
     */
    void db2MsgText() {
        //                                                                  
        runProgram("DSNTIAR", sqlca, 
                errorMessage, 
                errorTextLen);
        iIndices.setI(1);
        while (!(iIndices.getI().compareTo(10) > 0
            || errorMessage.getErrorText().at(iIndices.getI()).isSpaces())) {
            //        DISPLAY ERROR-TEXT (I)                                    
            returnCode.setValue(16);
            throw new StopRunException();
            //iIndices.getI().add(1);
        }
    }

    @Autowired
    public void setGfvtbrt0Port(IGfvtbrt0Port gfvtbrt0Port) {
        this.gfvtbrt0Port = gfvtbrt0Port;
        this.gfvtbrt0Port.setProgramContext(getProgramContext());
    }
}
