package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.string.StringUtils;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.WsHfaReg;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.WsAgtReg;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.WsDatosGralAgt;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.WsCnaAgtReg;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.WsTabAgtReg;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo.wstabagtreg.WsTabAgtR;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CAMPOS-TRABAJO.
 *
 */
// 589      *---                                                              
// 590      *   DECLARACION DE VARIABLES DE TRABAJO                           
// 591      *---                                                              
@Setter
@Getter
public class WsCamposTrabajo { // VStruct
    // Properties
    private AlphanumericVar s999Err = new AlphanumericVar(1);                // 05 S999-ERR X(01) UsedAsParameter
    private int wsAgtIdrAux = 0;                                            // 05 WS-AGT-IDR-AUX 9(05)
    private int wsAgtIdrAux3 = 0;                                           // 05 WS-AGT-IDR-AUX3 9(05)
    private int wsAgtIdrAux2;                                               // 05 WS-AGT-IDR-AUX2 S9(09) COMPUTATIONAL-3
    private String wsIndPag = "";                                           // 05 WS-IND-PAG X(01)
    private int wnNro = 0;                                                  // 05 WN-NRO S9(04) COMPUTATIONAL
    private int wnNull = 0;                                                 // 05 WN-NULL S9(04) COMPUTATIONAL
    private int wsDraCve = 0;                                               // 05 WS-DRA-CVE S9(4) COMPUTATIONAL
    private UnsignedNumericVar wsAgtInsert = new UnsignedNumericVar(10, 0);  // 05 WS-AGT-INSERT 9(10)
    private UnsignedNumericVar wsAgtUpdate = new UnsignedNumericVar(10, 0);  // 05 WS-AGT-UPDATE 9(10)
    private int wsEsaCve = 0;                                               // 05 WS-ESA-CVE S9(4) COMPUTATIONAL
    private String wsEsaDes = "";                                           // 05 WS-ESA-DES X(15)
    private String wsFecMas_1 = "";                                         // 05 WS-FEC-MAS-1 X(10)
    private String wsFecHfa = StringUtils.all("0", 10);                     // 05 WS-FEC-HFA X(10)
    private String wsIndExcAgt = "";                                        // 05 WS-IND-EXC-AGT X(01)
    private PackedVar wsPjeCedNovi = new PackedVar(5, 2);                    // 05 WS-PJE-CED-NOVI S9(3)V9(2) COMPUTATIONAL-3
    private PackedVar wsPjeCedVida = new PackedVar(5, 2);                    // 05 WS-PJE-CED-VIDA S9(3)V9(2) COMPUTATIONAL-3
    private WsHfaReg wsHfaReg = new  WsHfaReg();                             // 05 WS-HFA-REG
    private WsAgtReg wsAgtReg = new  WsAgtReg();                             // 05 WS-AGT-REG
    private WsDatosGralAgt wsDatosGralAgt = new  WsDatosGralAgt();           // 05 WS-DATOS-GRAL-AGT
    private WsCnaAgtReg wsCnaAgtReg = new  WsCnaAgtReg();                    // 05 WS-CNA-AGT-REG
    private WsTabAgtReg wsTabAgtReg = new  WsTabAgtReg();                    // 05 WS-TAB-AGT-REG

    public WsCamposTrabajo() {
        initialize();
    }



    public void setS999Err(final String s999Err) {
        this.s999Err.setValue(s999Err);
    }

    public void setS999Err(final IAlphanumericValue s999Err) {
        this.s999Err.setValue(s999Err);
    }

    public void setWsAgtInsert(final BigDecimal wsAgtInsert) {
        this.wsAgtInsert.setValue(wsAgtInsert);
    }

    public void setWsAgtInsert(final int wsAgtInsert) {
        this.wsAgtInsert.setValue(wsAgtInsert);
    }

    public void setWsAgtInsert(final INumericValue wsAgtInsert) {
        this.wsAgtInsert.setValue(wsAgtInsert);
    }

    public void setWsAgtUpdate(final BigDecimal wsAgtUpdate) {
        this.wsAgtUpdate.setValue(wsAgtUpdate);
    }

    public void setWsAgtUpdate(final int wsAgtUpdate) {
        this.wsAgtUpdate.setValue(wsAgtUpdate);
    }

    public void setWsAgtUpdate(final INumericValue wsAgtUpdate) {
        this.wsAgtUpdate.setValue(wsAgtUpdate);
    }

    public void setWsPjeCedNovi(final BigDecimal wsPjeCedNovi) {
        this.wsPjeCedNovi.setValue(wsPjeCedNovi);
    }

    public void setWsPjeCedNovi(final int wsPjeCedNovi) {
        this.wsPjeCedNovi.setValue(wsPjeCedNovi);
    }

    public void setWsPjeCedNovi(final INumericValue wsPjeCedNovi) {
        this.wsPjeCedNovi.setValue(wsPjeCedNovi);
    }

    public void setWsPjeCedVida(final BigDecimal wsPjeCedVida) {
        this.wsPjeCedVida.setValue(wsPjeCedVida);
    }

    public void setWsPjeCedVida(final int wsPjeCedVida) {
        this.wsPjeCedVida.setValue(wsPjeCedVida);
    }

    public void setWsPjeCedVida(final INumericValue wsPjeCedVida) {
        this.wsPjeCedVida.setValue(wsPjeCedVida);
    }


    // Deeper properties

    // Property: wsHfaReg.wsHfaAgtIdr -> 10 WS-HFA-AGT-IDR S9(09) COMPUTATIONAL-3
    public PackedVar getWsHfaAgtIdr() {
        return this.wsHfaReg.getWsHfaAgtIdr();
    }

    public void setWsHfaAgtIdr(final int wsHfaAgtIdr) {
        this.wsHfaReg.setWsHfaAgtIdr(wsHfaAgtIdr);
    }

    public void setWsHfaAgtIdr(final INumericValue wsHfaAgtIdr) {
        this.wsHfaReg.setWsHfaAgtIdr(wsHfaAgtIdr);
    }

    // Property: wsHfaReg.wsHfaFecIniPdo -> 10 WS-HFA-FEC-INI-PDO X(10)
    public AlphanumericVar getWsHfaFecIniPdo() {
        return this.wsHfaReg.getWsHfaFecIniPdo();
    }

    public void setWsHfaFecIniPdo(final String wsHfaFecIniPdo) {
        this.wsHfaReg.setWsHfaFecIniPdo(wsHfaFecIniPdo);
    }

    public void setWsHfaFecIniPdo(final IAlphanumericValue wsHfaFecIniPdo) {
        this.wsHfaReg.setWsHfaFecIniPdo(wsHfaFecIniPdo);
    }

    // Property: wsHfaReg.wsHfaFecFinPdo -> 10 WS-HFA-FEC-FIN-PDO X(10)
    public AlphanumericVar getWsHfaFecFinPdo() {
        return this.wsHfaReg.getWsHfaFecFinPdo();
    }

    public void setWsHfaFecFinPdo(final String wsHfaFecFinPdo) {
        this.wsHfaReg.setWsHfaFecFinPdo(wsHfaFecFinPdo);
    }

    public void setWsHfaFecFinPdo(final IAlphanumericValue wsHfaFecFinPdo) {
        this.wsHfaReg.setWsHfaFecFinPdo(wsHfaFecFinPdo);
    }

    // Property: wsHfaReg.wsHfaEsaCve -> 10 WS-HFA-ESA-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsHfaEsaCve() {
        return this.wsHfaReg.getWsHfaEsaCve();
    }

    public void setWsHfaEsaCve(final int wsHfaEsaCve) {
        this.wsHfaReg.setWsHfaEsaCve(wsHfaEsaCve);
    }

    public void setWsHfaEsaCve(final INumericValue wsHfaEsaCve) {
        this.wsHfaReg.setWsHfaEsaCve(wsHfaEsaCve);
    }

    // Property: wsHfaReg.wsHfaPefCve -> 10 WS-HFA-PEF-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsHfaPefCve() {
        return this.wsHfaReg.getWsHfaPefCve();
    }

    public void setWsHfaPefCve(final int wsHfaPefCve) {
        this.wsHfaReg.setWsHfaPefCve(wsHfaPefCve);
    }

    public void setWsHfaPefCve(final INumericValue wsHfaPefCve) {
        this.wsHfaReg.setWsHfaPefCve(wsHfaPefCve);
    }

    // Property: wsHfaReg.wsHfaOfnCve -> 10 WS-HFA-OFN-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsHfaOfnCve() {
        return this.wsHfaReg.getWsHfaOfnCve();
    }

    public void setWsHfaOfnCve(final int wsHfaOfnCve) {
        this.wsHfaReg.setWsHfaOfnCve(wsHfaOfnCve);
    }

    public void setWsHfaOfnCve(final INumericValue wsHfaOfnCve) {
        this.wsHfaReg.setWsHfaOfnCve(wsHfaOfnCve);
    }

    // Property: wsHfaReg.wsHfaCnaGerZonEnv -> 10 WS-HFA-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    public BinaryVar getWsHfaCnaGerZonEnv() {
        return this.wsHfaReg.getWsHfaCnaGerZonEnv();
    }

    public void setWsHfaCnaGerZonEnv(final int wsHfaCnaGerZonEnv) {
        this.wsHfaReg.setWsHfaCnaGerZonEnv(wsHfaCnaGerZonEnv);
    }

    public void setWsHfaCnaGerZonEnv(final INumericValue wsHfaCnaGerZonEnv) {
        this.wsHfaReg.setWsHfaCnaGerZonEnv(wsHfaCnaGerZonEnv);
    }

    // Property: wsHfaReg.wsHfaDsgCve -> 10 WS-HFA-DSG-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsHfaDsgCve() {
        return this.wsHfaReg.getWsHfaDsgCve();
    }

    public void setWsHfaDsgCve(final int wsHfaDsgCve) {
        this.wsHfaReg.setWsHfaDsgCve(wsHfaDsgCve);
    }

    public void setWsHfaDsgCve(final INumericValue wsHfaDsgCve) {
        this.wsHfaReg.setWsHfaDsgCve(wsHfaDsgCve);
    }

    // Property: wsHfaReg.wsHfaAgtRfc -> 10 WS-HFA-AGT-RFC X(13)
    public AlphanumericVar getWsHfaAgtRfc() {
        return this.wsHfaReg.getWsHfaAgtRfc();
    }

    public void setWsHfaAgtRfc(final String wsHfaAgtRfc) {
        this.wsHfaReg.setWsHfaAgtRfc(wsHfaAgtRfc);
    }

    public void setWsHfaAgtRfc(final IAlphanumericValue wsHfaAgtRfc) {
        this.wsHfaReg.setWsHfaAgtRfc(wsHfaAgtRfc);
    }

    // Property: wsHfaReg.wsHfaNomEmi -> 10 WS-HFA-NOM-EMI X(100)
    public AlphanumericVar getWsHfaNomEmi() {
        return this.wsHfaReg.getWsHfaNomEmi();
    }

    public void setWsHfaNomEmi(final String wsHfaNomEmi) {
        this.wsHfaReg.setWsHfaNomEmi(wsHfaNomEmi);
    }

    public void setWsHfaNomEmi(final IAlphanumericValue wsHfaNomEmi) {
        this.wsHfaReg.setWsHfaNomEmi(wsHfaNomEmi);
    }

    // Property: wsHfaReg.wsHfaDmaCae -> 10 WS-HFA-DMA-CAE X(40)
    public AlphanumericVar getWsHfaDmaCae() {
        return this.wsHfaReg.getWsHfaDmaCae();
    }

    public void setWsHfaDmaCae(final String wsHfaDmaCae) {
        this.wsHfaReg.setWsHfaDmaCae(wsHfaDmaCae);
    }

    public void setWsHfaDmaCae(final IAlphanumericValue wsHfaDmaCae) {
        this.wsHfaReg.setWsHfaDmaCae(wsHfaDmaCae);
    }

    // Property: wsHfaReg.wsHfaDmaNum -> 10 WS-HFA-DMA-NUM X(05)
    public AlphanumericVar getWsHfaDmaNum() {
        return this.wsHfaReg.getWsHfaDmaNum();
    }

    public void setWsHfaDmaNum(final String wsHfaDmaNum) {
        this.wsHfaReg.setWsHfaDmaNum(wsHfaDmaNum);
    }

    public void setWsHfaDmaNum(final IAlphanumericValue wsHfaDmaNum) {
        this.wsHfaReg.setWsHfaDmaNum(wsHfaDmaNum);
    }

    // Property: wsHfaReg.wsHfaDmaInt -> 10 WS-HFA-DMA-INT X(05)
    public AlphanumericVar getWsHfaDmaInt() {
        return this.wsHfaReg.getWsHfaDmaInt();
    }

    public void setWsHfaDmaInt(final String wsHfaDmaInt) {
        this.wsHfaReg.setWsHfaDmaInt(wsHfaDmaInt);
    }

    public void setWsHfaDmaInt(final IAlphanumericValue wsHfaDmaInt) {
        this.wsHfaReg.setWsHfaDmaInt(wsHfaDmaInt);
    }

    // Property: wsHfaReg.wsHfaDmaCol -> 10 WS-HFA-DMA-COL X(40)
    public AlphanumericVar getWsHfaDmaCol() {
        return this.wsHfaReg.getWsHfaDmaCol();
    }

    public void setWsHfaDmaCol(final String wsHfaDmaCol) {
        this.wsHfaReg.setWsHfaDmaCol(wsHfaDmaCol);
    }

    public void setWsHfaDmaCol(final IAlphanumericValue wsHfaDmaCol) {
        this.wsHfaReg.setWsHfaDmaCol(wsHfaDmaCol);
    }

    // Property: wsHfaReg.wsHfaMndNom -> 10 WS-HFA-MND-NOM X(50)
    public AlphanumericVar getWsHfaMndNom() {
        return this.wsHfaReg.getWsHfaMndNom();
    }

    public void setWsHfaMndNom(final String wsHfaMndNom) {
        this.wsHfaReg.setWsHfaMndNom(wsHfaMndNom);
    }

    public void setWsHfaMndNom(final IAlphanumericValue wsHfaMndNom) {
        this.wsHfaReg.setWsHfaMndNom(wsHfaMndNom);
    }

    // Property: wsHfaReg.wsHfaEdoNom -> 10 WS-HFA-EDO-NOM X(30)
    public AlphanumericVar getWsHfaEdoNom() {
        return this.wsHfaReg.getWsHfaEdoNom();
    }

    public void setWsHfaEdoNom(final String wsHfaEdoNom) {
        this.wsHfaReg.setWsHfaEdoNom(wsHfaEdoNom);
    }

    public void setWsHfaEdoNom(final IAlphanumericValue wsHfaEdoNom) {
        this.wsHfaReg.setWsHfaEdoNom(wsHfaEdoNom);
    }

    // Property: wsHfaReg.wsHfaPaiNom -> 10 WS-HFA-PAI-NOM X(30)
    public AlphanumericVar getWsHfaPaiNom() {
        return this.wsHfaReg.getWsHfaPaiNom();
    }

    public void setWsHfaPaiNom(final String wsHfaPaiNom) {
        this.wsHfaReg.setWsHfaPaiNom(wsHfaPaiNom);
    }

    public void setWsHfaPaiNom(final IAlphanumericValue wsHfaPaiNom) {
        this.wsHfaReg.setWsHfaPaiNom(wsHfaPaiNom);
    }

    // Property: wsHfaReg.wsHfaCpoCve -> 10 WS-HFA-CPO-CVE S9(9) COMPUTATIONAL
    public BinaryVar getWsHfaCpoCve() {
        return this.wsHfaReg.getWsHfaCpoCve();
    }

    public void setWsHfaCpoCve(final int wsHfaCpoCve) {
        this.wsHfaReg.setWsHfaCpoCve(wsHfaCpoCve);
    }

    public void setWsHfaCpoCve(final INumericValue wsHfaCpoCve) {
        this.wsHfaReg.setWsHfaCpoCve(wsHfaCpoCve);
    }

    // Property: wsHfaReg.wsHfaMteCve -> 10 WS-HFA-MTE-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsHfaMteCve() {
        return this.wsHfaReg.getWsHfaMteCve();
    }

    public void setWsHfaMteCve(final int wsHfaMteCve) {
        this.wsHfaReg.setWsHfaMteCve(wsHfaMteCve);
    }

    public void setWsHfaMteCve(final INumericValue wsHfaMteCve) {
        this.wsHfaReg.setWsHfaMteCve(wsHfaMteCve);
    }

    // Property: wsHfaReg.wsHfaTfpCve -> 10 WS-HFA-TFP-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsHfaTfpCve() {
        return this.wsHfaReg.getWsHfaTfpCve();
    }

    public void setWsHfaTfpCve(final int wsHfaTfpCve) {
        this.wsHfaReg.setWsHfaTfpCve(wsHfaTfpCve);
    }

    public void setWsHfaTfpCve(final INumericValue wsHfaTfpCve) {
        this.wsHfaReg.setWsHfaTfpCve(wsHfaTfpCve);
    }

    // Property: wsHfaReg.wsHfaCtbNumCta -> 10 WS-HFA-CTB-NUM-CTA X(18)
    public AlphanumericVar getWsHfaCtbNumCta() {
        return this.wsHfaReg.getWsHfaCtbNumCta();
    }

    public void setWsHfaCtbNumCta(final String wsHfaCtbNumCta) {
        this.wsHfaReg.setWsHfaCtbNumCta(wsHfaCtbNumCta);
    }

    public void setWsHfaCtbNumCta(final IAlphanumericValue wsHfaCtbNumCta) {
        this.wsHfaReg.setWsHfaCtbNumCta(wsHfaCtbNumCta);
    }

    // Property: wsHfaReg.wsHfaIndPag -> 10 WS-HFA-IND-PAG X(01)
    public AlphanumericVar getWsHfaIndPag() {
        return this.wsHfaReg.getWsHfaIndPag();
    }

    public void setWsHfaIndPag(final String wsHfaIndPag) {
        this.wsHfaReg.setWsHfaIndPag(wsHfaIndPag);
    }

    public void setWsHfaIndPag(final IAlphanumericValue wsHfaIndPag) {
        this.wsHfaReg.setWsHfaIndPag(wsHfaIndPag);
    }

    // Property: wsHfaReg.wsHfaIndExc -> 10 WS-HFA-IND-EXC X(01)
    public AlphanumericVar getWsHfaIndExc() {
        return this.wsHfaReg.getWsHfaIndExc();
    }

    public void setWsHfaIndExc(final String wsHfaIndExc) {
        this.wsHfaReg.setWsHfaIndExc(wsHfaIndExc);
    }

    public void setWsHfaIndExc(final IAlphanumericValue wsHfaIndExc) {
        this.wsHfaReg.setWsHfaIndExc(wsHfaIndExc);
    }

    // Property: wsHfaReg.wsHfaUsuCve -> 10 WS-HFA-USU-CVE X(08)
    public AlphanumericVar getWsHfaUsuCve() {
        return this.wsHfaReg.getWsHfaUsuCve();
    }

    public void setWsHfaUsuCve(final String wsHfaUsuCve) {
        this.wsHfaReg.setWsHfaUsuCve(wsHfaUsuCve);
    }

    public void setWsHfaUsuCve(final IAlphanumericValue wsHfaUsuCve) {
        this.wsHfaReg.setWsHfaUsuCve(wsHfaUsuCve);
    }

    // Property: wsHfaReg.wsHfaNomPrg -> 10 WS-HFA-NOM-PRG X(08)
    public AlphanumericVar getWsHfaNomPrg() {
        return this.wsHfaReg.getWsHfaNomPrg();
    }

    public void setWsHfaNomPrg(final String wsHfaNomPrg) {
        this.wsHfaReg.setWsHfaNomPrg(wsHfaNomPrg);
    }

    public void setWsHfaNomPrg(final IAlphanumericValue wsHfaNomPrg) {
        this.wsHfaReg.setWsHfaNomPrg(wsHfaNomPrg);
    }

    // Property: wsHfaReg.wsHfaFecUltAct -> 10 WS-HFA-FEC-ULT-ACT X(26)
    public AlphanumericVar getWsHfaFecUltAct() {
        return this.wsHfaReg.getWsHfaFecUltAct();
    }

    public void setWsHfaFecUltAct(final String wsHfaFecUltAct) {
        this.wsHfaReg.setWsHfaFecUltAct(wsHfaFecUltAct);
    }

    public void setWsHfaFecUltAct(final IAlphanumericValue wsHfaFecUltAct) {
        this.wsHfaReg.setWsHfaFecUltAct(wsHfaFecUltAct);
    }

    // Property: wsHfaReg.wsHfaPjeIva -> 10 WS-HFA-PJE-IVA S9(3)V9(2) COMPUTATIONAL-3
    public PackedVar getWsHfaPjeIva() {
        return this.wsHfaReg.getWsHfaPjeIva();
    }

    public void setWsHfaPjeIva(final BigDecimal wsHfaPjeIva) {
        this.wsHfaReg.setWsHfaPjeIva(wsHfaPjeIva);
    }

    public void setWsHfaPjeIva(final int wsHfaPjeIva) {
        this.wsHfaReg.setWsHfaPjeIva(wsHfaPjeIva);
    }

    public void setWsHfaPjeIva(final INumericValue wsHfaPjeIva) {
        this.wsHfaReg.setWsHfaPjeIva(wsHfaPjeIva);
    }

    // Property: wsHfaReg.wsHfaPjeIsr -> 10 WS-HFA-PJE-ISR S9(3)V9(2) COMPUTATIONAL-3
    public PackedVar getWsHfaPjeIsr() {
        return this.wsHfaReg.getWsHfaPjeIsr();
    }

    public void setWsHfaPjeIsr(final BigDecimal wsHfaPjeIsr) {
        this.wsHfaReg.setWsHfaPjeIsr(wsHfaPjeIsr);
    }

    public void setWsHfaPjeIsr(final int wsHfaPjeIsr) {
        this.wsHfaReg.setWsHfaPjeIsr(wsHfaPjeIsr);
    }

    public void setWsHfaPjeIsr(final INumericValue wsHfaPjeIsr) {
        this.wsHfaReg.setWsHfaPjeIsr(wsHfaPjeIsr);
    }

    // Property: wsHfaReg.wsHfaCdregfis -> 10 WS-HFA-CDREGFIS X(03)
    public AlphanumericVar getWsHfaCdregfis() {
        return this.wsHfaReg.getWsHfaCdregfis();
    }

    public void setWsHfaCdregfis(final String wsHfaCdregfis) {
        this.wsHfaReg.setWsHfaCdregfis(wsHfaCdregfis);
    }

    public void setWsHfaCdregfis(final IAlphanumericValue wsHfaCdregfis) {
        this.wsHfaReg.setWsHfaCdregfis(wsHfaCdregfis);
    }

    // Property: wsHfaReg.wsHfaNomrznso -> 10 WS-HFA-NOMRZNSO X(300)
    public AlphanumericVar getWsHfaNomrznso() {
        return this.wsHfaReg.getWsHfaNomrznso();
    }

    public void setWsHfaNomrznso(final String wsHfaNomrznso) {
        this.wsHfaReg.setWsHfaNomrznso(wsHfaNomrznso);
    }

    public void setWsHfaNomrznso(final IAlphanumericValue wsHfaNomrznso) {
        this.wsHfaReg.setWsHfaNomrznso(wsHfaNomrznso);
    }

    // Property: wsHfaReg.wsHfaCedNovi -> 10 WS-HFA-CED-NOVI S9(3)V9(2) COMPUTATIONAL-3
    public PackedVar getWsHfaCedNovi() {
        return this.wsHfaReg.getWsHfaCedNovi();
    }

    public void setWsHfaCedNovi(final BigDecimal wsHfaCedNovi) {
        this.wsHfaReg.setWsHfaCedNovi(wsHfaCedNovi);
    }

    public void setWsHfaCedNovi(final int wsHfaCedNovi) {
        this.wsHfaReg.setWsHfaCedNovi(wsHfaCedNovi);
    }

    public void setWsHfaCedNovi(final INumericValue wsHfaCedNovi) {
        this.wsHfaReg.setWsHfaCedNovi(wsHfaCedNovi);
    }

    // Property: wsHfaReg.wsHfaCedVida -> 10 WS-HFA-CED-VIDA S9(3)V9(2) COMPUTATIONAL-3
    public PackedVar getWsHfaCedVida() {
        return this.wsHfaReg.getWsHfaCedVida();
    }

    public void setWsHfaCedVida(final BigDecimal wsHfaCedVida) {
        this.wsHfaReg.setWsHfaCedVida(wsHfaCedVida);
    }

    public void setWsHfaCedVida(final int wsHfaCedVida) {
        this.wsHfaReg.setWsHfaCedVida(wsHfaCedVida);
    }

    public void setWsHfaCedVida(final INumericValue wsHfaCedVida) {
        this.wsHfaReg.setWsHfaCedVida(wsHfaCedVida);
    }


    // Deeper properties

    // Property: wsAgtReg.wsAgtIdr -> 10 WS-AGT-IDR S9(9) COMPUTATIONAL
    public BinaryVar getWsAgtIdr() {
        return this.wsAgtReg.getWsAgtIdr();
    }

    public void setWsAgtIdr(final int wsAgtIdr) {
        this.wsAgtReg.setWsAgtIdr(wsAgtIdr);
    }

    public void setWsAgtIdr(final INumericValue wsAgtIdr) {
        this.wsAgtReg.setWsAgtIdr(wsAgtIdr);
    }

    // Property: wsAgtReg.wsAgtRfc -> 10 WS-AGT-RFC X(13)
    public AlphanumericVar getWsAgtRfc() {
        return this.wsAgtReg.getWsAgtRfc();
    }

    public void setWsAgtRfc(final String wsAgtRfc) {
        this.wsAgtReg.setWsAgtRfc(wsAgtRfc);
    }

    public void setWsAgtRfc(final IAlphanumericValue wsAgtRfc) {
        this.wsAgtReg.setWsAgtRfc(wsAgtRfc);
    }

    // Property: wsAgtReg.wsAgtNom -> 10 WS-AGT-NOM X(40)
    public AlphanumericVar getWsAgtNom() {
        return this.wsAgtReg.getWsAgtNom();
    }

    public void setWsAgtNom(final String wsAgtNom) {
        this.wsAgtReg.setWsAgtNom(wsAgtNom);
    }

    public void setWsAgtNom(final IAlphanumericValue wsAgtNom) {
        this.wsAgtReg.setWsAgtNom(wsAgtNom);
    }

    // Property: wsAgtReg.wsAgtApePat -> 10 WS-AGT-APE-PAT X(40)
    public AlphanumericVar getWsAgtApePat() {
        return this.wsAgtReg.getWsAgtApePat();
    }

    public void setWsAgtApePat(final String wsAgtApePat) {
        this.wsAgtReg.setWsAgtApePat(wsAgtApePat);
    }

    public void setWsAgtApePat(final IAlphanumericValue wsAgtApePat) {
        this.wsAgtReg.setWsAgtApePat(wsAgtApePat);
    }

    // Property: wsAgtReg.wsAgtApeMat -> 10 WS-AGT-APE-MAT X(30)
    public AlphanumericVar getWsAgtApeMat() {
        return this.wsAgtReg.getWsAgtApeMat();
    }

    public void setWsAgtApeMat(final String wsAgtApeMat) {
        this.wsAgtReg.setWsAgtApeMat(wsAgtApeMat);
    }

    public void setWsAgtApeMat(final IAlphanumericValue wsAgtApeMat) {
        this.wsAgtReg.setWsAgtApeMat(wsAgtApeMat);
    }

    // Property: wsAgtReg.wsAgtFecNac -> 10 WS-AGT-FEC-NAC X(10)
    public AlphanumericVar getWsAgtFecNac() {
        return this.wsAgtReg.getWsAgtFecNac();
    }

    public void setWsAgtFecNac(final String wsAgtFecNac) {
        this.wsAgtReg.setWsAgtFecNac(wsAgtFecNac);
    }

    public void setWsAgtFecNac(final IAlphanumericValue wsAgtFecNac) {
        this.wsAgtReg.setWsAgtFecNac(wsAgtFecNac);
    }

    // Property: wsAgtReg.wsAgtNumCed -> 10 WS-AGT-NUM-CED X(20)
    public AlphanumericVar getWsAgtNumCed() {
        return this.wsAgtReg.getWsAgtNumCed();
    }

    public void setWsAgtNumCed(final String wsAgtNumCed) {
        this.wsAgtReg.setWsAgtNumCed(wsAgtNumCed);
    }

    public void setWsAgtNumCed(final IAlphanumericValue wsAgtNumCed) {
        this.wsAgtReg.setWsAgtNumCed(wsAgtNumCed);
    }

    // Property: wsAgtReg.wsAgtFecIniCed -> 10 WS-AGT-FEC-INI-CED X(10)
    public AlphanumericVar getWsAgtFecIniCed() {
        return this.wsAgtReg.getWsAgtFecIniCed();
    }

    public void setWsAgtFecIniCed(final String wsAgtFecIniCed) {
        this.wsAgtReg.setWsAgtFecIniCed(wsAgtFecIniCed);
    }

    public void setWsAgtFecIniCed(final IAlphanumericValue wsAgtFecIniCed) {
        this.wsAgtReg.setWsAgtFecIniCed(wsAgtFecIniCed);
    }

    // Property: wsAgtReg.wsAgtFecFinCed -> 10 WS-AGT-FEC-FIN-CED X(10)
    public AlphanumericVar getWsAgtFecFinCed() {
        return this.wsAgtReg.getWsAgtFecFinCed();
    }

    public void setWsAgtFecFinCed(final String wsAgtFecFinCed) {
        this.wsAgtReg.setWsAgtFecFinCed(wsAgtFecFinCed);
    }

    public void setWsAgtFecFinCed(final IAlphanumericValue wsAgtFecFinCed) {
        this.wsAgtReg.setWsAgtFecFinCed(wsAgtFecFinCed);
    }

    // Property: wsAgtReg.wsAgtExpSeg -> 10 WS-AGT-EXP-SEG X(01)
    public AlphanumericVar getWsAgtExpSeg() {
        return this.wsAgtReg.getWsAgtExpSeg();
    }

    public void setWsAgtExpSeg(final String wsAgtExpSeg) {
        this.wsAgtReg.setWsAgtExpSeg(wsAgtExpSeg);
    }

    public void setWsAgtExpSeg(final IAlphanumericValue wsAgtExpSeg) {
        this.wsAgtReg.setWsAgtExpSeg(wsAgtExpSeg);
    }

    // Property: wsAgtReg.wsAgtTraEsp -> 10 WS-AGT-TRA-ESP X(01)
    public AlphanumericVar getWsAgtTraEsp() {
        return this.wsAgtReg.getWsAgtTraEsp();
    }

    public void setWsAgtTraEsp(final String wsAgtTraEsp) {
        this.wsAgtReg.setWsAgtTraEsp(wsAgtTraEsp);
    }

    public void setWsAgtTraEsp(final IAlphanumericValue wsAgtTraEsp) {
        this.wsAgtReg.setWsAgtTraEsp(wsAgtTraEsp);
    }

    // Property: wsAgtReg.wsAgtClfLmr -> 10 WS-AGT-CLF-LMR S9(4) COMPUTATIONAL
    public BinaryVar getWsAgtClfLmr() {
        return this.wsAgtReg.getWsAgtClfLmr();
    }

    public void setWsAgtClfLmr(final int wsAgtClfLmr) {
        this.wsAgtReg.setWsAgtClfLmr(wsAgtClfLmr);
    }

    public void setWsAgtClfLmr(final INumericValue wsAgtClfLmr) {
        this.wsAgtReg.setWsAgtClfLmr(wsAgtClfLmr);
    }

    // Property: wsAgtReg.wsFecUltAct -> 10 WS-FEC-ULT-ACT X(10)
    public AlphanumericVar getWsFecUltAct() {
        return this.wsAgtReg.getWsFecUltAct();
    }

    public void setWsFecUltAct(final String wsFecUltAct) {
        this.wsAgtReg.setWsFecUltAct(wsFecUltAct);
    }

    public void setWsFecUltAct(final IAlphanumericValue wsFecUltAct) {
        this.wsAgtReg.setWsFecUltAct(wsFecUltAct);
    }

    // Property: wsAgtReg.wsAgtRazSoc -> 10 WS-AGT-RAZ-SOC X(60)
    public AlphanumericVar getWsAgtRazSoc() {
        return this.wsAgtReg.getWsAgtRazSoc();
    }

    public void setWsAgtRazSoc(final String wsAgtRazSoc) {
        this.wsAgtReg.setWsAgtRazSoc(wsAgtRazSoc);
    }

    public void setWsAgtRazSoc(final IAlphanumericValue wsAgtRazSoc) {
        this.wsAgtReg.setWsAgtRazSoc(wsAgtRazSoc);
    }

    // Property: wsAgtReg.wsUsuCveAct -> 10 WS-USU-CVE-ACT X(08)
    public AlphanumericVar getWsUsuCveAct() {
        return this.wsAgtReg.getWsUsuCveAct();
    }

    public void setWsUsuCveAct(final String wsUsuCveAct) {
        this.wsAgtReg.setWsUsuCveAct(wsUsuCveAct);
    }

    public void setWsUsuCveAct(final IAlphanumericValue wsUsuCveAct) {
        this.wsAgtReg.setWsUsuCveAct(wsUsuCveAct);
    }

    // Property: wsAgtReg.wsNalCve -> 10 WS-NAL-CVE X(03)
    public AlphanumericVar getWsNalCve() {
        return this.wsAgtReg.getWsNalCve();
    }

    public void setWsNalCve(final String wsNalCve) {
        this.wsAgtReg.setWsNalCve(wsNalCve);
    }

    public void setWsNalCve(final IAlphanumericValue wsNalCve) {
        this.wsAgtReg.setWsNalCve(wsNalCve);
    }

    // Property: wsAgtReg.wsPefCve -> 10 WS-PEF-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsPefCve() {
        return this.wsAgtReg.getWsPefCve();
    }

    public void setWsPefCve(final int wsPefCve) {
        this.wsAgtReg.setWsPefCve(wsPefCve);
    }

    public void setWsPefCve(final INumericValue wsPefCve) {
        this.wsAgtReg.setWsPefCve(wsPefCve);
    }

    // Property: wsAgtReg.wsTcaCve -> 10 WS-TCA-CVE X(02)
    public AlphanumericVar getWsTcaCve() {
        return this.wsAgtReg.getWsTcaCve();
    }

    public void setWsTcaCve(final String wsTcaCve) {
        this.wsAgtReg.setWsTcaCve(wsTcaCve);
    }

    public void setWsTcaCve(final IAlphanumericValue wsTcaCve) {
        this.wsAgtReg.setWsTcaCve(wsTcaCve);
    }

    // Property: wsAgtReg.wsMteCve -> 10 WS-MTE-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsMteCve() {
        return this.wsAgtReg.getWsMteCve();
    }

    public void setWsMteCve(final int wsMteCve) {
        this.wsAgtReg.setWsMteCve(wsMteCve);
    }

    public void setWsMteCve(final INumericValue wsMteCve) {
        this.wsAgtReg.setWsMteCve(wsMteCve);
    }

    // Property: wsAgtReg.wsAgtNumFolInv -> 10 WS-AGT-NUM-FOL-INV S9(9) COMPUTATIONAL
    public BinaryVar getWsAgtNumFolInv() {
        return this.wsAgtReg.getWsAgtNumFolInv();
    }

    public void setWsAgtNumFolInv(final int wsAgtNumFolInv) {
        this.wsAgtReg.setWsAgtNumFolInv(wsAgtNumFolInv);
    }

    public void setWsAgtNumFolInv(final INumericValue wsAgtNumFolInv) {
        this.wsAgtReg.setWsAgtNumFolInv(wsAgtNumFolInv);
    }

    // Property: wsAgtReg.wsAgtNomExi -> 10 WS-AGT-NOM-EXI X(40)
    public AlphanumericVar getWsAgtNomExi() {
        return this.wsAgtReg.getWsAgtNomExi();
    }

    public void setWsAgtNomExi(final String wsAgtNomExi) {
        this.wsAgtReg.setWsAgtNomExi(wsAgtNomExi);
    }

    public void setWsAgtNomExi(final IAlphanumericValue wsAgtNomExi) {
        this.wsAgtReg.setWsAgtNomExi(wsAgtNomExi);
    }

    // Property: wsAgtReg.wsAgtRstInv -> 10 WS-AGT-RST-INV X(05)
    public AlphanumericVar getWsAgtRstInv() {
        return this.wsAgtReg.getWsAgtRstInv();
    }

    public void setWsAgtRstInv(final String wsAgtRstInv) {
        this.wsAgtReg.setWsAgtRstInv(wsAgtRstInv);
    }

    public void setWsAgtRstInv(final IAlphanumericValue wsAgtRstInv) {
        this.wsAgtReg.setWsAgtRstInv(wsAgtRstInv);
    }

    // Property: wsAgtReg.wsAgtFecIniInv -> 10 WS-AGT-FEC-INI-INV X(10)
    public AlphanumericVar getWsAgtFecIniInv() {
        return this.wsAgtReg.getWsAgtFecIniInv();
    }

    public void setWsAgtFecIniInv(final String wsAgtFecIniInv) {
        this.wsAgtReg.setWsAgtFecIniInv(wsAgtFecIniInv);
    }

    public void setWsAgtFecIniInv(final IAlphanumericValue wsAgtFecIniInv) {
        this.wsAgtReg.setWsAgtFecIniInv(wsAgtFecIniInv);
    }

    // Property: wsAgtReg.wsAgtFecFinInv -> 10 WS-AGT-FEC-FIN-INV X(10)
    public AlphanumericVar getWsAgtFecFinInv() {
        return this.wsAgtReg.getWsAgtFecFinInv();
    }

    public void setWsAgtFecFinInv(final String wsAgtFecFinInv) {
        this.wsAgtReg.setWsAgtFecFinInv(wsAgtFecFinInv);
    }

    public void setWsAgtFecFinInv(final IAlphanumericValue wsAgtFecFinInv) {
        this.wsAgtReg.setWsAgtFecFinInv(wsAgtFecFinInv);
    }

    // Property: wsAgtReg.wsAgtNomDphInv -> 10 WS-AGT-NOM-DPH-INV X(40)
    public AlphanumericVar getWsAgtNomDphInv() {
        return this.wsAgtReg.getWsAgtNomDphInv();
    }

    public void setWsAgtNomDphInv(final String wsAgtNomDphInv) {
        this.wsAgtReg.setWsAgtNomDphInv(wsAgtNomDphInv);
    }

    public void setWsAgtNomDphInv(final IAlphanumericValue wsAgtNomDphInv) {
        this.wsAgtReg.setWsAgtNomDphInv(wsAgtNomDphInv);
    }

    // Property: wsAgtReg.wsAgtFecReh -> 10 WS-AGT-FEC-REH X(10)
    public AlphanumericVar getWsAgtFecReh() {
        return this.wsAgtReg.getWsAgtFecReh();
    }

    public void setWsAgtFecReh(final String wsAgtFecReh) {
        this.wsAgtReg.setWsAgtFecReh(wsAgtFecReh);
    }

    public void setWsAgtFecReh(final IAlphanumericValue wsAgtFecReh) {
        this.wsAgtReg.setWsAgtFecReh(wsAgtFecReh);
    }

    // Property: wsAgtReg.wsAgtFecMotEss -> 10 WS-AGT-FEC-MOT-ESS X(10)
    public AlphanumericVar getWsAgtFecMotEss() {
        return this.wsAgtReg.getWsAgtFecMotEss();
    }

    public void setWsAgtFecMotEss(final String wsAgtFecMotEss) {
        this.wsAgtReg.setWsAgtFecMotEss(wsAgtFecMotEss);
    }

    public void setWsAgtFecMotEss(final IAlphanumericValue wsAgtFecMotEss) {
        this.wsAgtReg.setWsAgtFecMotEss(wsAgtFecMotEss);
    }

    // Property: wsAgtReg.wsAgtFecMotAsgIn -> 10 WS-AGT-FEC-MOT-ASG-IN X(10)
    public AlphanumericVar getWsAgtFecMotAsgIn() {
        return this.wsAgtReg.getWsAgtFecMotAsgIn();
    }

    public void setWsAgtFecMotAsgIn(final String wsAgtFecMotAsgIn) {
        this.wsAgtReg.setWsAgtFecMotAsgIn(wsAgtFecMotAsgIn);
    }

    public void setWsAgtFecMotAsgIn(final IAlphanumericValue wsAgtFecMotAsgIn) {
        this.wsAgtReg.setWsAgtFecMotAsgIn(wsAgtFecMotAsgIn);
    }

    // Property: wsAgtReg.wsAgtObs -> 10 WS-AGT-OBS X(60)
    public AlphanumericVar getWsAgtObs() {
        return this.wsAgtReg.getWsAgtObs();
    }

    public void setWsAgtObs(final String wsAgtObs) {
        this.wsAgtReg.setWsAgtObs(wsAgtObs);
    }

    public void setWsAgtObs(final IAlphanumericValue wsAgtObs) {
        this.wsAgtReg.setWsAgtObs(wsAgtObs);
    }

    // Property: wsAgtReg.wsAgtNip -> 10 WS-AGT-NIP S9(4) COMPUTATIONAL
    public BinaryVar getWsAgtNip() {
        return this.wsAgtReg.getWsAgtNip();
    }

    public void setWsAgtNip(final int wsAgtNip) {
        this.wsAgtReg.setWsAgtNip(wsAgtNip);
    }

    public void setWsAgtNip(final INumericValue wsAgtNip) {
        this.wsAgtReg.setWsAgtNip(wsAgtNip);
    }

    // Property: wsAgtReg.wsAgtNumFacIso -> 10 WS-AGT-NUM-FAC-ISO S9(9) COMPUTATIONAL
    public BinaryVar getWsAgtNumFacIso() {
        return this.wsAgtReg.getWsAgtNumFacIso();
    }

    public void setWsAgtNumFacIso(final int wsAgtNumFacIso) {
        this.wsAgtReg.setWsAgtNumFacIso(wsAgtNumFacIso);
    }

    public void setWsAgtNumFacIso(final INumericValue wsAgtNumFacIso) {
        this.wsAgtReg.setWsAgtNumFacIso(wsAgtNumFacIso);
    }

    // Property: wsAgtReg.wsAgtImpFacIso -> 10 WS-AGT-IMP-FAC-ISO S9(11)V9(2) COMPUTATIONAL-3
    public PackedVar getWsAgtImpFacIso() {
        return this.wsAgtReg.getWsAgtImpFacIso();
    }

    public void setWsAgtImpFacIso(final BigDecimal wsAgtImpFacIso) {
        this.wsAgtReg.setWsAgtImpFacIso(wsAgtImpFacIso);
    }

    public void setWsAgtImpFacIso(final int wsAgtImpFacIso) {
        this.wsAgtReg.setWsAgtImpFacIso(wsAgtImpFacIso);
    }

    public void setWsAgtImpFacIso(final INumericValue wsAgtImpFacIso) {
        this.wsAgtReg.setWsAgtImpFacIso(wsAgtImpFacIso);
    }


    // Deeper properties

    // Property: wsDatosGralAgt.wsGralAgtRfc -> 10 WS-GRAL-AGT-RFC X(13)
    public AlphanumericVar getWsGralAgtRfc() {
        return this.wsDatosGralAgt.getWsGralAgtRfc();
    }

    public void setWsGralAgtRfc(final String wsGralAgtRfc) {
        this.wsDatosGralAgt.setWsGralAgtRfc(wsGralAgtRfc);
    }

    public void setWsGralAgtRfc(final IAlphanumericValue wsGralAgtRfc) {
        this.wsDatosGralAgt.setWsGralAgtRfc(wsGralAgtRfc);
    }

    // Property: wsDatosGralAgt.wsGralAgtNom -> 10 WS-GRAL-AGT-NOM X(40)
    public AlphanumericVar getWsGralAgtNom() {
        return this.wsDatosGralAgt.getWsGralAgtNom();
    }

    public void setWsGralAgtNom(final String wsGralAgtNom) {
        this.wsDatosGralAgt.setWsGralAgtNom(wsGralAgtNom);
    }

    public void setWsGralAgtNom(final IAlphanumericValue wsGralAgtNom) {
        this.wsDatosGralAgt.setWsGralAgtNom(wsGralAgtNom);
    }

    // Property: wsDatosGralAgt.wsGralAgtApePat -> 10 WS-GRAL-AGT-APE-PAT X(40)
    public AlphanumericVar getWsGralAgtApePat() {
        return this.wsDatosGralAgt.getWsGralAgtApePat();
    }

    public void setWsGralAgtApePat(final String wsGralAgtApePat) {
        this.wsDatosGralAgt.setWsGralAgtApePat(wsGralAgtApePat);
    }

    public void setWsGralAgtApePat(final IAlphanumericValue wsGralAgtApePat) {
        this.wsDatosGralAgt.setWsGralAgtApePat(wsGralAgtApePat);
    }

    // Property: wsDatosGralAgt.wsGralAgtApeMat -> 10 WS-GRAL-AGT-APE-MAT X(30)
    public AlphanumericVar getWsGralAgtApeMat() {
        return this.wsDatosGralAgt.getWsGralAgtApeMat();
    }

    public void setWsGralAgtApeMat(final String wsGralAgtApeMat) {
        this.wsDatosGralAgt.setWsGralAgtApeMat(wsGralAgtApeMat);
    }

    public void setWsGralAgtApeMat(final IAlphanumericValue wsGralAgtApeMat) {
        this.wsDatosGralAgt.setWsGralAgtApeMat(wsGralAgtApeMat);
    }

    // Property: wsDatosGralAgt.wsGralAgtRazSoc -> 10 WS-GRAL-AGT-RAZ-SOC X(60)
    public AlphanumericVar getWsGralAgtRazSoc() {
        return this.wsDatosGralAgt.getWsGralAgtRazSoc();
    }

    public void setWsGralAgtRazSoc(final String wsGralAgtRazSoc) {
        this.wsDatosGralAgt.setWsGralAgtRazSoc(wsGralAgtRazSoc);
    }

    public void setWsGralAgtRazSoc(final IAlphanumericValue wsGralAgtRazSoc) {
        this.wsDatosGralAgt.setWsGralAgtRazSoc(wsGralAgtRazSoc);
    }

    // Property: wsDatosGralAgt.wsGralAgtNomConcatenado -> 10 WS-GRAL-AGT-NOM-CONCATENADO X(100)
    public AlphanumericVar getWsGralAgtNomConcatenado() {
        return this.wsDatosGralAgt.getWsGralAgtNomConcatenado();
    }

    public void setWsGralAgtNomConcatenado(final String wsGralAgtNomConcatenado) {
        this.wsDatosGralAgt.setWsGralAgtNomConcatenado(wsGralAgtNomConcatenado);
    }

    public void setWsGralAgtNomConcatenado(final IAlphanumericValue wsGralAgtNomConcatenado) {
        this.wsDatosGralAgt.setWsGralAgtNomConcatenado(wsGralAgtNomConcatenado);
    }

    // Property: wsDatosGralAgt.wsGralPraAgtCxxCurp -> 10 WS-GRAL-PRA-AGT-CXX-CURP X(20)
    public AlphanumericVar getWsGralPraAgtCxxCurp() {
        return this.wsDatosGralAgt.getWsGralPraAgtCxxCurp();
    }

    public void setWsGralPraAgtCxxCurp(final String wsGralPraAgtCxxCurp) {
        this.wsDatosGralAgt.setWsGralPraAgtCxxCurp(wsGralPraAgtCxxCurp);
    }

    public void setWsGralPraAgtCxxCurp(final IAlphanumericValue wsGralPraAgtCxxCurp) {
        this.wsDatosGralAgt.setWsGralPraAgtCxxCurp(wsGralPraAgtCxxCurp);
    }

    // Property: wsDatosGralAgt.wsGralAgtNalCve -> 10 WS-GRAL-AGT-NAL-CVE X(03)
    public AlphanumericVar getWsGralAgtNalCve() {
        return this.wsDatosGralAgt.getWsGralAgtNalCve();
    }

    public void setWsGralAgtNalCve(final String wsGralAgtNalCve) {
        this.wsDatosGralAgt.setWsGralAgtNalCve(wsGralAgtNalCve);
    }

    public void setWsGralAgtNalCve(final IAlphanumericValue wsGralAgtNalCve) {
        this.wsDatosGralAgt.setWsGralAgtNalCve(wsGralAgtNalCve);
    }

    // Property: wsDatosGralAgt.wsGralExtranjero -> 10 WS-GRAL-EXTRANJERO X(03)
    public AlphanumericVar getWsGralExtranjero() {
        return this.wsDatosGralAgt.getWsGralExtranjero();
    }

    public void setWsGralExtranjero(final String wsGralExtranjero) {
        this.wsDatosGralAgt.setWsGralExtranjero(wsGralExtranjero);
    }

    public void setWsGralExtranjero(final IAlphanumericValue wsGralExtranjero) {
        this.wsDatosGralAgt.setWsGralExtranjero(wsGralExtranjero);
    }

    // Property: wsDatosGralAgt.wsGralEsaCve -> 10 WS-GRAL-ESA-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsGralEsaCve() {
        return this.wsDatosGralAgt.getWsGralEsaCve();
    }

    public void setWsGralEsaCve(final int wsGralEsaCve) {
        this.wsDatosGralAgt.setWsGralEsaCve(wsGralEsaCve);
    }

    public void setWsGralEsaCve(final INumericValue wsGralEsaCve) {
        this.wsDatosGralAgt.setWsGralEsaCve(wsGralEsaCve);
    }

    // Property: wsDatosGralAgt.wsGralPefCve -> 10 WS-GRAL-PEF-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsGralPefCve() {
        return this.wsDatosGralAgt.getWsGralPefCve();
    }

    public void setWsGralPefCve(final int wsGralPefCve) {
        this.wsDatosGralAgt.setWsGralPefCve(wsGralPefCve);
    }

    public void setWsGralPefCve(final INumericValue wsGralPefCve) {
        this.wsDatosGralAgt.setWsGralPefCve(wsGralPefCve);
    }

    // Property: wsDatosGralAgt.wsGralDmaCae -> 10 WS-GRAL-DMA-CAE X(40)
    public AlphanumericVar getWsGralDmaCae() {
        return this.wsDatosGralAgt.getWsGralDmaCae();
    }

    public void setWsGralDmaCae(final String wsGralDmaCae) {
        this.wsDatosGralAgt.setWsGralDmaCae(wsGralDmaCae);
    }

    public void setWsGralDmaCae(final IAlphanumericValue wsGralDmaCae) {
        this.wsDatosGralAgt.setWsGralDmaCae(wsGralDmaCae);
    }

    // Property: wsDatosGralAgt.wsGralDmaNum -> 10 WS-GRAL-DMA-NUM X(05)
    public AlphanumericVar getWsGralDmaNum() {
        return this.wsDatosGralAgt.getWsGralDmaNum();
    }

    public void setWsGralDmaNum(final String wsGralDmaNum) {
        this.wsDatosGralAgt.setWsGralDmaNum(wsGralDmaNum);
    }

    public void setWsGralDmaNum(final IAlphanumericValue wsGralDmaNum) {
        this.wsDatosGralAgt.setWsGralDmaNum(wsGralDmaNum);
    }

    // Property: wsDatosGralAgt.wsGralDmaInt -> 10 WS-GRAL-DMA-INT X(05)
    public AlphanumericVar getWsGralDmaInt() {
        return this.wsDatosGralAgt.getWsGralDmaInt();
    }

    public void setWsGralDmaInt(final String wsGralDmaInt) {
        this.wsDatosGralAgt.setWsGralDmaInt(wsGralDmaInt);
    }

    public void setWsGralDmaInt(final IAlphanumericValue wsGralDmaInt) {
        this.wsDatosGralAgt.setWsGralDmaInt(wsGralDmaInt);
    }

    // Property: wsDatosGralAgt.wsGralDmaCol -> 10 WS-GRAL-DMA-COL X(40)
    public AlphanumericVar getWsGralDmaCol() {
        return this.wsDatosGralAgt.getWsGralDmaCol();
    }

    public void setWsGralDmaCol(final String wsGralDmaCol) {
        this.wsDatosGralAgt.setWsGralDmaCol(wsGralDmaCol);
    }

    public void setWsGralDmaCol(final IAlphanumericValue wsGralDmaCol) {
        this.wsDatosGralAgt.setWsGralDmaCol(wsGralDmaCol);
    }

    // Property: wsDatosGralAgt.wsGralMndNom -> 10 WS-GRAL-MND-NOM X(50)
    public AlphanumericVar getWsGralMndNom() {
        return this.wsDatosGralAgt.getWsGralMndNom();
    }

    public void setWsGralMndNom(final String wsGralMndNom) {
        this.wsDatosGralAgt.setWsGralMndNom(wsGralMndNom);
    }

    public void setWsGralMndNom(final IAlphanumericValue wsGralMndNom) {
        this.wsDatosGralAgt.setWsGralMndNom(wsGralMndNom);
    }

    // Property: wsDatosGralAgt.wsGralDmaCpoCve -> 10 WS-GRAL-DMA-CPO-CVE S9(9) COMPUTATIONAL
    public BinaryVar getWsGralDmaCpoCve() {
        return this.wsDatosGralAgt.getWsGralDmaCpoCve();
    }

    public void setWsGralDmaCpoCve(final int wsGralDmaCpoCve) {
        this.wsDatosGralAgt.setWsGralDmaCpoCve(wsGralDmaCpoCve);
    }

    public void setWsGralDmaCpoCve(final INumericValue wsGralDmaCpoCve) {
        this.wsDatosGralAgt.setWsGralDmaCpoCve(wsGralDmaCpoCve);
    }

    // Property: wsDatosGralAgt.wsGralPobNom -> 10 WS-GRAL-POB-NOM X(40)
    public AlphanumericVar getWsGralPobNom() {
        return this.wsDatosGralAgt.getWsGralPobNom();
    }

    public void setWsGralPobNom(final String wsGralPobNom) {
        this.wsDatosGralAgt.setWsGralPobNom(wsGralPobNom);
    }

    public void setWsGralPobNom(final IAlphanumericValue wsGralPobNom) {
        this.wsDatosGralAgt.setWsGralPobNom(wsGralPobNom);
    }

    // Property: wsDatosGralAgt.wsGralMndEdoCve -> 10 WS-GRAL-MND-EDO-CVE X(04)
    public AlphanumericVar getWsGralMndEdoCve() {
        return this.wsDatosGralAgt.getWsGralMndEdoCve();
    }

    public void setWsGralMndEdoCve(final String wsGralMndEdoCve) {
        this.wsDatosGralAgt.setWsGralMndEdoCve(wsGralMndEdoCve);
    }

    public void setWsGralMndEdoCve(final IAlphanumericValue wsGralMndEdoCve) {
        this.wsDatosGralAgt.setWsGralMndEdoCve(wsGralMndEdoCve);
    }

    // Property: wsDatosGralAgt.wsGralEdoNom -> 10 WS-GRAL-EDO-NOM X(30)
    public AlphanumericVar getWsGralEdoNom() {
        return this.wsDatosGralAgt.getWsGralEdoNom();
    }

    public void setWsGralEdoNom(final String wsGralEdoNom) {
        this.wsDatosGralAgt.setWsGralEdoNom(wsGralEdoNom);
    }

    public void setWsGralEdoNom(final IAlphanumericValue wsGralEdoNom) {
        this.wsDatosGralAgt.setWsGralEdoNom(wsGralEdoNom);
    }

    // Property: wsDatosGralAgt.wsGralEdoPaiCve -> 10 WS-GRAL-EDO-PAI-CVE X(03)
    public AlphanumericVar getWsGralEdoPaiCve() {
        return this.wsDatosGralAgt.getWsGralEdoPaiCve();
    }

    public void setWsGralEdoPaiCve(final String wsGralEdoPaiCve) {
        this.wsDatosGralAgt.setWsGralEdoPaiCve(wsGralEdoPaiCve);
    }

    public void setWsGralEdoPaiCve(final IAlphanumericValue wsGralEdoPaiCve) {
        this.wsDatosGralAgt.setWsGralEdoPaiCve(wsGralEdoPaiCve);
    }

    // Property: wsDatosGralAgt.wsGralPaiPaiNom -> 10 WS-GRAL-PAI-PAI-NOM X(30)
    public AlphanumericVar getWsGralPaiPaiNom() {
        return this.wsDatosGralAgt.getWsGralPaiPaiNom();
    }

    public void setWsGralPaiPaiNom(final String wsGralPaiPaiNom) {
        this.wsDatosGralAgt.setWsGralPaiPaiNom(wsGralPaiPaiNom);
    }

    public void setWsGralPaiPaiNom(final IAlphanumericValue wsGralPaiPaiNom) {
        this.wsDatosGralAgt.setWsGralPaiPaiNom(wsGralPaiPaiNom);
    }

    // Property: wsDatosGralAgt.wsGralCmaDesTelefon -> 10 WS-GRAL-CMA-DES-TELEFON X(60)
    public AlphanumericVar getWsGralCmaDesTelefon() {
        return this.wsDatosGralAgt.getWsGralCmaDesTelefon();
    }

    public void setWsGralCmaDesTelefon(final String wsGralCmaDesTelefon) {
        this.wsDatosGralAgt.setWsGralCmaDesTelefon(wsGralCmaDesTelefon);
    }

    public void setWsGralCmaDesTelefon(final IAlphanumericValue wsGralCmaDesTelefon) {
        this.wsDatosGralAgt.setWsGralCmaDesTelefon(wsGralCmaDesTelefon);
    }


    // Deeper properties

    // Property: wsCnaAgtReg.wsCnaOfnCve -> 10 WS-CNA-OFN-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsCnaOfnCve() {
        return this.wsCnaAgtReg.getWsCnaOfnCve();
    }

    public void setWsCnaOfnCve(final int wsCnaOfnCve) {
        this.wsCnaAgtReg.setWsCnaOfnCve(wsCnaOfnCve);
    }

    public void setWsCnaOfnCve(final INumericValue wsCnaOfnCve) {
        this.wsCnaAgtReg.setWsCnaOfnCve(wsCnaOfnCve);
    }

    // Property: wsCnaAgtReg.wsCnaTfpCve -> 10 WS-CNA-TFP-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsCnaTfpCve() {
        return this.wsCnaAgtReg.getWsCnaTfpCve();
    }

    public void setWsCnaTfpCve(final int wsCnaTfpCve) {
        this.wsCnaAgtReg.setWsCnaTfpCve(wsCnaTfpCve);
    }

    public void setWsCnaTfpCve(final INumericValue wsCnaTfpCve) {
        this.wsCnaAgtReg.setWsCnaTfpCve(wsCnaTfpCve);
    }

    // Property: wsCnaAgtReg.wsCnaMteCve -> 10 WS-CNA-MTE-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsCnaMteCve() {
        return this.wsCnaAgtReg.getWsCnaMteCve();
    }

    public void setWsCnaMteCve(final int wsCnaMteCve) {
        this.wsCnaAgtReg.setWsCnaMteCve(wsCnaMteCve);
    }

    public void setWsCnaMteCve(final INumericValue wsCnaMteCve) {
        this.wsCnaAgtReg.setWsCnaMteCve(wsCnaMteCve);
    }

    // Property: wsCnaAgtReg.wsCnaGerZonEnv -> 10 WS-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    public BinaryVar getWsCnaGerZonEnv() {
        return this.wsCnaAgtReg.getWsCnaGerZonEnv();
    }

    public void setWsCnaGerZonEnv(final int wsCnaGerZonEnv) {
        this.wsCnaAgtReg.setWsCnaGerZonEnv(wsCnaGerZonEnv);
    }

    public void setWsCnaGerZonEnv(final INumericValue wsCnaGerZonEnv) {
        this.wsCnaAgtReg.setWsCnaGerZonEnv(wsCnaGerZonEnv);
    }

    // Property: wsCnaAgtReg.wsCnaCtbNumCta -> 10 WS-CNA-CTB-NUM-CTA X(18)
    public AlphanumericVar getWsCnaCtbNumCta() {
        return this.wsCnaAgtReg.getWsCnaCtbNumCta();
    }

    public void setWsCnaCtbNumCta(final String wsCnaCtbNumCta) {
        this.wsCnaAgtReg.setWsCnaCtbNumCta(wsCnaCtbNumCta);
    }

    public void setWsCnaCtbNumCta(final IAlphanumericValue wsCnaCtbNumCta) {
        this.wsCnaAgtReg.setWsCnaCtbNumCta(wsCnaCtbNumCta);
    }

    // Property: wsCnaAgtReg.wsCnaPjeIva -> 10 WS-CNA-PJE-IVA S9(3)V9(2) COMPUTATIONAL-3
    public PackedVar getWsCnaPjeIva() {
        return this.wsCnaAgtReg.getWsCnaPjeIva();
    }

    public void setWsCnaPjeIva(final BigDecimal wsCnaPjeIva) {
        this.wsCnaAgtReg.setWsCnaPjeIva(wsCnaPjeIva);
    }

    public void setWsCnaPjeIva(final int wsCnaPjeIva) {
        this.wsCnaAgtReg.setWsCnaPjeIva(wsCnaPjeIva);
    }

    public void setWsCnaPjeIva(final INumericValue wsCnaPjeIva) {
        this.wsCnaAgtReg.setWsCnaPjeIva(wsCnaPjeIva);
    }

    // Property: wsCnaAgtReg.wsCnaPjeIsr -> 10 WS-CNA-PJE-ISR S9(3)V9(2) COMPUTATIONAL-3
    public PackedVar getWsCnaPjeIsr() {
        return this.wsCnaAgtReg.getWsCnaPjeIsr();
    }

    public void setWsCnaPjeIsr(final BigDecimal wsCnaPjeIsr) {
        this.wsCnaAgtReg.setWsCnaPjeIsr(wsCnaPjeIsr);
    }

    public void setWsCnaPjeIsr(final int wsCnaPjeIsr) {
        this.wsCnaAgtReg.setWsCnaPjeIsr(wsCnaPjeIsr);
    }

    public void setWsCnaPjeIsr(final INumericValue wsCnaPjeIsr) {
        this.wsCnaAgtReg.setWsCnaPjeIsr(wsCnaPjeIsr);
    }

    // Property: wsCnaAgtReg.wsCnaDsgCve -> 10 WS-CNA-DSG-CVE S9(04) COMPUTATIONAL
    public BinaryVar getWsCnaDsgCve() {
        return this.wsCnaAgtReg.getWsCnaDsgCve();
    }

    public void setWsCnaDsgCve(final int wsCnaDsgCve) {
        this.wsCnaAgtReg.setWsCnaDsgCve(wsCnaDsgCve);
    }

    public void setWsCnaDsgCve(final INumericValue wsCnaDsgCve) {
        this.wsCnaAgtReg.setWsCnaDsgCve(wsCnaDsgCve);
    }


    // Deeper properties

    // Property: wsTabAgtReg.wsTabAgtN -> 10 WS-TAB-AGT-N 9(10) REDEFINED BY WS-TAB-AGT-R
    public UnsignedNumericVar getWsTabAgtN() {
        return this.wsTabAgtReg.getWsTabAgtN();
    }

    public void setWsTabAgtN(final BigDecimal wsTabAgtN) {
        this.wsTabAgtReg.setWsTabAgtN(wsTabAgtN);
    }

    public void setWsTabAgtN(final int wsTabAgtN) {
        this.wsTabAgtReg.setWsTabAgtN(wsTabAgtN);
    }

    public void setWsTabAgtN(final INumericValue wsTabAgtN) {
        this.wsTabAgtReg.setWsTabAgtN(wsTabAgtN);
    }

    // Property: wsTabAgtReg.wsTabAgtR -> 10 WS-TAB-AGT-R REDEFINES WS-TAB-AGT-N
    public WsTabAgtR getWsTabAgtR() {
        return this.wsTabAgtReg.getWsTabAgtR();
    }

    // Property: wsTabAgtReg.wsTabAgtR.wsTabFiller -> 15 WS-TAB-FILLER X(03)
    public AlphanumericVar getWsTabFiller() {
        return this.wsTabAgtReg.getWsTabAgtR().getWsTabFiller();
    }

    public void setWsTabFiller(final String wsTabFiller) {
        this.wsTabAgtReg.getWsTabAgtR().setWsTabFiller(wsTabFiller);
    }

    public void setWsTabFiller(final IAlphanumericValue wsTabFiller) {
        this.wsTabAgtReg.getWsTabAgtR().setWsTabFiller(wsTabFiller);
    }

    // Property: wsTabAgtReg.wsTabAgtR.wsTabAgt -> 15 WS-TAB-AGT X(07)
    public AlphanumericVar getWsTabAgt() {
        return this.wsTabAgtReg.getWsTabAgtR().getWsTabAgt();
    }

    public void setWsTabAgt(final String wsTabAgt) {
        this.wsTabAgtReg.getWsTabAgtR().setWsTabAgt(wsTabAgt);
    }

    public void setWsTabAgt(final IAlphanumericValue wsTabAgt) {
        this.wsTabAgtReg.getWsTabAgtR().setWsTabAgt(wsTabAgt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.s999Err.clear();
        this.wsAgtIdrAux = 0;
        this.wsAgtIdrAux3 = 0;
        this.wsAgtIdrAux2 = 0;
        this.wsIndPag = "";
        this.wnNro = 0;
        this.wnNull = 0;
        this.wsDraCve = 0;
        this.wsAgtInsert.setZero();
        this.wsAgtUpdate.setZero();
        this.wsEsaCve = 0;
        this.wsEsaDes = "";
        this.wsFecMas_1 = "";
        this.wsFecHfa = StringUtils.all("0", 10);
        this.wsIndExcAgt = "";
        this.wsPjeCedNovi.clear();
        this.wsPjeCedVida.clear();
        this.wsHfaReg.initialize();
        this.wsAgtReg.initialize();
        this.wsDatosGralAgt.initialize();
        this.wsCnaAgtReg.initialize();
        this.wsTabAgtReg.initialize();
    }

}

