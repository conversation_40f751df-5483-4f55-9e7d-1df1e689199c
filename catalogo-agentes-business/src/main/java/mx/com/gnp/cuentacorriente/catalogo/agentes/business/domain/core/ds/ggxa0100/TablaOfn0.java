package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaofn0.TOfn0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaofn0.tofn0.TOfn0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-OFN0.
 *
 */
@Setter
@Getter
public class TablaOfn0 { // VStruct
    // Properties
    private TOfn0 tOfn0 = new  TOfn0(51);                                    // 10 T-OFN0 [1]

    public TablaOfn0() {
        initialize();
    }



    public TOfn0 getTOfn0(int index) {
        return this.tOfn0.at(index);
    }


    // Deeper properties

    // Property: tOfn0.tOfn0Elem -> 15 T-OFN0-ELEM UsedAsParameter
    public TOfn0Elem getTOfn0Elem() {
        return this.tOfn0.getTOfn0Elem();
    }

    // Property: tOfn0.tOfn0Elem.sCnaOfnCve -> 20 S-CNA-OFN-CVE X(04)
    public AlphanumericVar getSCnaOfnCve() {
        return this.tOfn0.getTOfn0Elem().getSCnaOfnCve();
    }

    public void setSCnaOfnCve(final String sCnaOfnCve) {
        this.tOfn0.getTOfn0Elem().setSCnaOfnCve(sCnaOfnCve);
    }

    public void setSCnaOfnCve(final IAlphanumericValue sCnaOfnCve) {
        this.tOfn0.getTOfn0Elem().setSCnaOfnCve(sCnaOfnCve);
    }

    // Property: tOfn0.tOfn0Elem.sOfnLimitad -> 20 S-OFN-LIMITAD X(01)
    public AlphanumericVar getSOfnLimitad() {
        return this.tOfn0.getTOfn0Elem().getSOfnLimitad();
    }

    public void setSOfnLimitad(final String sOfnLimitad) {
        this.tOfn0.getTOfn0Elem().setSOfnLimitad(sOfnLimitad);
    }

    public void setSOfnLimitad(final IAlphanumericValue sOfnLimitad) {
        this.tOfn0.getTOfn0Elem().setSOfnLimitad(sOfnLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tOfn0.initialize();
    }

}

