package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGFVTTDM0.
 *
 */
@Setter
@Getter
public class WsDclgfvttdm0 { // VStruct
    // Properties
    private int tdmTdmCve;                                                  // 10 TDM-TDM-CVE S9(4) COMPUTATIONAL
    private String tdmTdmDes = "";                                          // 10 TDM-TDM-DES X(20)
    private String tdmFecUltAct = "";                                       // 10 TDM-FEC-ULT-ACT X(10)
    private String tdmUsuCveAct = "";                                       // 10 TDM-USU-CVE-ACT X(8)

    public WsDclgfvttdm0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tdmTdmCve = 0;
        this.tdmTdmDes = "";
        this.tdmFecUltAct = "";
        this.tdmUsuCveAct = "";
    }

}

