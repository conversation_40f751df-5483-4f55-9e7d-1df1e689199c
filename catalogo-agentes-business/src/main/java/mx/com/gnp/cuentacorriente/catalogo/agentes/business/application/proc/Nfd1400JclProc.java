/**
 *  JCL Program: NFD1400.
 *  Code generation type: <PERSON><PERSON><PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Nfd1400JclProc extends AbstractJclProgram {

    public Nfd1400JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("NFD1400", (nfd1400) -> {
            nfd1400.addParameter("PROC");
            /*//**********************************************************************/
            /*//*   NOMBRE PROCESO : VALIDAR ERRORES DE TRIGGERS*/
            /*//*   OBJETIVO       : ALERTAR DE ERRORES DE SINCRONIZACION*/
            /*//*                    ENTRE CANALES Y CUENTA CORRIENTE*/
            /*//*   DEPENDENCIAS   : DESPUES DE PGAFVDRI,*/
            /*//*                    ANTES DE PGANFD01, PGANFD08, PGAXAD01 & PGACQDA2*/
            /*//*   ELABORO        : GUILLERMO PACHECO*/
            /*//*   FECHA          : ABR 17, 2018*/
            /*//*   ID RELEASE     : RFC0038951*/
            /*//**/
            /*//*   MODIFICO       : SE AGREGAN LOS PASOS DE TRANFERENCIA Y ENVIO DE*/
            /*//*                  : CORREO EN CASO DE QUE EL PASO NFD1404 TERMINE*/
            /*//*                  : CON COND-CODE = 004.*/
            /*//*   FECHA          : AGOSTO 2018.*/
            /*//*   ID RELEASE     : SOPPROD.*/
            /*//**********************************************************************/
            /*//**********************************************************************/
            /*//* OBJETIVO DEL PASO : ASIGNACION DE PRODUCTOS A LOS*/
            /*//*                     MOVIMIENTOS DE VIDA*/
            /*//* PASO REINICIABLE*/
            /*//**********************************************************************/
            nfd1400.execProgram("NFD1404", "IKJEFT01", (nfd1404) -> {
                nfd1404.addDataDefinition("SALIDA", "PGA.NFD1400.GFVTBRT0.DESCARGA",  (salida) -> {
                    salida.addParameter("DISP", "(NEW,CATLG,DELETE)");
                    salida.addParameter("UNIT","SHAREDA");
                    salida.addParameter("SPACE", "(CYL,(10,10),RLSE)");
                    salida.addParameter("DCB", "(LRECL=138,RECFM=FB,DSORG=PS,BUFNO=4)");
                });
                nfd1404.addDataDefinition("SYSPRINT",  (sysprint) -> {
                    sysprint.addParameter("SYSOUT","*");
                });
                nfd1404.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                    systsprt.addParameter("SYSOUT","*");
                });
                nfd1404.addDataDefinition("SYSOUT",  (sysout) -> {
                    sysout.addParameter("SYSOUT","*");
                });
                nfd1404.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFD1401)",  (systsin) -> {
                    systsin.addParameter("DISP","SHR");
                });
            });
            /*//**/
            if (nfd1400.getReturnCode("NFD1404") == 4) {
                /*//**/
                /*//**********************************************************************/
                /*//* OBJETIVO DEL PASO : ENVIA ARCHIVO AL SERVIDOR DE NOTES*/
                /*//**/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                nfd1400.execProgram("NFD1403", "FTP", (nfd1403) -> {
                    nfd1403.addParameter("PARM","-t ANDANTIS (EXIT");
                    nfd1403.addParameter("REGION","0M");
                    nfd1403.addDataDefinition("OUTPUT",  (output) -> {
                        output.addParameter("SYSOUT","*");
                    });
                    nfd1403.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*            TARJETA DE PARAMETROS PARA TRANSFERENCIA*/
                    nfd1403.addDataDefinition("INPUT", "PNCQP.BTCH.CARDS(NFD1403)",  (input) -> {
                        input.addParameter("DISP","SHR");
                    });
                });
                /*//**********************************************************************/
                /*//* OBJETIVO DEL PASO : EJECUCIóN REXEC PARA ENVIO DE CORREO*/
                /*//**/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                nfd1400.execProgram("NFD1402", "IKJEFT01", (nfd1402) -> {
                    nfd1402.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfd1402.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","H");
                    });
                    nfd1402.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFD1404)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfd1400.getLastReturnCode()<=4) {
                /*//**/
                nfd1400.execProgram("NFD1401", "IEBGENER", (nfd1401) -> {
                    nfd1401.addDataDefinition("SYSUT1", "PGA.NFD1400.GFVTBRT0.DESCARGA",  (sysut1) -> {
                        sysut1.addParameter("DISP","SHR");
                    });
                    nfd1401.addDataDefinition("SYSUT2", "PGA.NFD1400.GDG.GFVTBRT0(+1)",  (sysut2) -> {
                        sysut2.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysut2.addParameter("UNIT","SHAREDA");
                        sysut2.addParameter("SPACE", "(CYL,(100,100),RLSE)");
                        sysut2.addParameter("DCB", "(GDG.MODELO,LRECL=138,RECFM=FB,DSORG=PS,BUFNO=4)");
                    });
                    nfd1401.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfd1401.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfd1401.addDataDefinition("SYUDUMP",  (syudump) -> {
                        syudump.addParameter("SYSOUT","*");
                    });
                    nfd1401.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                        sysdbout.addParameter("SYSOUT","*");
                    });
                    nfd1401.addDummyDataDefinition("SYSIN",  (sysin) -> {
                    });
                });
            }
            /*//**/
            if (nfd1400.getReturnCode("NFD1404") == 4) {
                /*//* >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>*/
                /*//**********************************************************************/
                /*//* PASO:01        KAESFT02.*/
                /*//* DESCRIPCION:   TRANSFIERE INFORMACION SFT  INICIO.*/
                /*//**/
                /*//**********************************************************************/
                nfd1400.execProcedure("KAESFT02", "KAEPPSFA", (kaesft02) -> {
                    kaesft02.addParameter("JOB","PGANFD14");
                    kaesft02.addParameter("SFTPCMD","PNCQP.BTCH.CARDS(NFD1405)");
                });
                /*//**********************************************************************/
                /*//* PASO:1         KAESFT01.*/
                /*//* DESCRIPCION:   TRANSFIERE INFORMACION SFTP FIN*/
                /*//**/
                /*//**********************************************************************/
                nfd1400.execProcedure("KAESFT01", "KAEPPSFB", (kaesft01) -> {
                    kaesft01.addParameter("JOB","PGANFD14");
                });
            }
        });
    }
}
