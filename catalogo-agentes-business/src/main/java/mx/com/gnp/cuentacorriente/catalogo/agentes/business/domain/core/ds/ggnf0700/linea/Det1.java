package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DET1.
 *
 */
// 66006600*                                                                 00660002
@Setter
@Getter
public class Det1 extends DataStruct {
    // Properties
    private AlphanumericVar d1FlaNum = new AlphanumericVar(this, 8);         // 05 D1-FLA-NUM X(8)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 1);          // 05 FILLER X
    private AlphanumericVar d1EmpCve = new AlphanumericVar(this, 5);         // 05 D1-EMP-CVE X(5)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private NumericEditedVar d1EsaCve = new NumericEditedVar(this, "ZZZ9");  // 05 D1-ESA-CVE ZZZ9
    private AlphanumericVar filler3 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private NumericEditedVar d1OfnCve = new NumericEditedVar(this, "ZZZ9");  // 05 D1-OFN-CVE ZZZ9
    private AlphanumericVar filler4 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private NumericEditedVar d1OrvCve = new NumericEditedVar(this, "ZZZ9");  // 05 D1-ORV-CVE ZZZ9
    private AlphanumericVar filler5 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private NumericEditedVar d1PefCve = new NumericEditedVar(this, "ZZZ9");  // 05 D1-PEF-CVE ZZZ9
    private AlphanumericVar filler6 = new AlphanumericVar(this, 4);          // 05 FILLER X(4)
    private AlphanumericVar d1AgtNom = new AlphanumericVar(this, 10);        // 05 D1-AGT-NOM X(10)
    private AlphanumericVar filler7 = new AlphanumericVar(this, 2);          // 05 FILLER XX
    private AlphanumericVar d1AgtApePat = new AlphanumericVar(this, 10);     // 05 D1-AGT-APE-PAT X(10)
    private AlphanumericVar filler8 = new AlphanumericVar(this, 2);          // 05 FILLER XX
    private AlphanumericVar d1AgtApeMat = new AlphanumericVar(this, 10);     // 05 D1-AGT-APE-MAT X(10)
    private AlphanumericVar filler9 = new AlphanumericVar(this, 2);          // 05 FILLER XX
    private AlphanumericVar d1AgtRazSoc = new AlphanumericVar(this, 8);      // 05 D1-AGT-RAZ-SOC X(8)
    private AlphanumericVar filler10 = new AlphanumericVar(this, 6);         // 05 FILLER X(6)
    private AlphanumericVar d1Obs = new AlphanumericVar(this, 20);           // 05 D1-OBS X(20)

    public Det1() {
        super();
        initialize();
    }

    public Det1(DataContainer parent) {
        super(parent);
    }

    public Det1(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setD1FlaNum(final String d1FlaNum) {
        this.d1FlaNum.setValue(d1FlaNum);
    }

    public void setD1FlaNum(final IAlphanumericValue d1FlaNum) {
        this.d1FlaNum.setValue(d1FlaNum);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setD1EmpCve(final String d1EmpCve) {
        this.d1EmpCve.setValue(d1EmpCve);
    }

    public void setD1EmpCve(final IAlphanumericValue d1EmpCve) {
        this.d1EmpCve.setValue(d1EmpCve);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setD1EsaCve(final String d1EsaCve) {
        this.d1EsaCve.setValue(d1EsaCve);
    }

    public void setD1EsaCve(final IValue d1EsaCve) {
        this.d1EsaCve.setValue(d1EsaCve);
    }

    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }

    public void setD1OfnCve(final String d1OfnCve) {
        this.d1OfnCve.setValue(d1OfnCve);
    }

    public void setD1OfnCve(final IValue d1OfnCve) {
        this.d1OfnCve.setValue(d1OfnCve);
    }

    public void setFiller4(final String filler4) {
        this.filler4.setValue(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.filler4.setValue(filler4);
    }

    public void setD1OrvCve(final String d1OrvCve) {
        this.d1OrvCve.setValue(d1OrvCve);
    }

    public void setD1OrvCve(final IValue d1OrvCve) {
        this.d1OrvCve.setValue(d1OrvCve);
    }

    public void setFiller5(final String filler5) {
        this.filler5.setValue(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.filler5.setValue(filler5);
    }

    public void setD1PefCve(final String d1PefCve) {
        this.d1PefCve.setValue(d1PefCve);
    }

    public void setD1PefCve(final IValue d1PefCve) {
        this.d1PefCve.setValue(d1PefCve);
    }

    public void setFiller6(final String filler6) {
        this.filler6.setValue(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.filler6.setValue(filler6);
    }

    public void setD1AgtNom(final String d1AgtNom) {
        this.d1AgtNom.setValue(d1AgtNom);
    }

    public void setD1AgtNom(final IAlphanumericValue d1AgtNom) {
        this.d1AgtNom.setValue(d1AgtNom);
    }

    public void setFiller7(final String filler7) {
        this.filler7.setValue(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.filler7.setValue(filler7);
    }

    public void setD1AgtApePat(final String d1AgtApePat) {
        this.d1AgtApePat.setValue(d1AgtApePat);
    }

    public void setD1AgtApePat(final IAlphanumericValue d1AgtApePat) {
        this.d1AgtApePat.setValue(d1AgtApePat);
    }

    public void setFiller8(final String filler8) {
        this.filler8.setValue(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.filler8.setValue(filler8);
    }

    public void setD1AgtApeMat(final String d1AgtApeMat) {
        this.d1AgtApeMat.setValue(d1AgtApeMat);
    }

    public void setD1AgtApeMat(final IAlphanumericValue d1AgtApeMat) {
        this.d1AgtApeMat.setValue(d1AgtApeMat);
    }

    public void setFiller9(final String filler9) {
        this.filler9.setValue(filler9);
    }

    public void setFiller9(final IAlphanumericValue filler9) {
        this.filler9.setValue(filler9);
    }

    public void setD1AgtRazSoc(final String d1AgtRazSoc) {
        this.d1AgtRazSoc.setValue(d1AgtRazSoc);
    }

    public void setD1AgtRazSoc(final IAlphanumericValue d1AgtRazSoc) {
        this.d1AgtRazSoc.setValue(d1AgtRazSoc);
    }

    public void setFiller10(final String filler10) {
        this.filler10.setValue(filler10);
    }

    public void setFiller10(final IAlphanumericValue filler10) {
        this.filler10.setValue(filler10);
    }

    public void setD1Obs(final String d1Obs) {
        this.d1Obs.setValue(d1Obs);
    }

    public void setD1Obs(final IAlphanumericValue d1Obs) {
        this.d1Obs.setValue(d1Obs);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.d1FlaNum.clear();
        this.filler1.clear();
        this.d1EmpCve.clear();
        this.filler2.clear();
        this.d1EsaCve.clear();
        this.filler3.clear();
        this.d1OfnCve.clear();
        this.filler4.clear();
        this.d1OrvCve.clear();
        this.filler5.clear();
        this.d1PefCve.clear();
        this.filler6.clear();
        this.d1AgtNom.clear();
        this.filler7.clear();
        this.d1AgtApePat.clear();
        this.filler8.clear();
        this.d1AgtApeMat.clear();
        this.filler9.clear();
        this.d1AgtRazSoc.clear();
        this.filler10.clear();
        this.d1Obs.clear();
    }

}

