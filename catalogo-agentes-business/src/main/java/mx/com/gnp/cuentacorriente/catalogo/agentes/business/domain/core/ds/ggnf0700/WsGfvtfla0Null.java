package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTFLA0-NULL.
 *
 */
// 580031200*                                                                 03120002
// 581031300*                                                                 03130002
@Setter
@Getter
public class WsGfvtfla0Null extends DataStruct {
    // Properties
    private BinaryVar fla01 = new BinaryVar(this, 4, 0);                     // 03 FLA01 S9(4) COMPUTATIONAL
    private BinaryVar fla02 = new BinaryVar(this, 4, 0);                     // 03 FLA02 S9(4) COMPUTATIONAL
    private BinaryVar fla03 = new BinaryVar(this, 4, 0);                     // 03 FLA03 S9(4) COMPUTATIONAL
    private BinaryVar fla04 = new BinaryVar(this, 4, 0);                     // 03 FLA04 S9(4) COMPUTATIONAL
    private BinaryVar fla05 = new BinaryVar(this, 4, 0);                     // 03 FLA05 S9(4) COMPUTATIONAL
    private BinaryVar fla06 = new BinaryVar(this, 4, 0);                     // 03 FLA06 S9(4) COMPUTATIONAL
    private BinaryVar fla07 = new BinaryVar(this, 4, 0);                     // 03 FLA07 S9(4) COMPUTATIONAL
    private BinaryVar fla08 = new BinaryVar(this, 4, 0);                     // 03 FLA08 S9(4) COMPUTATIONAL
    private BinaryVar fla09 = new BinaryVar(this, 4, 0);                     // 03 FLA09 S9(4) COMPUTATIONAL
    private BinaryVar fla10 = new BinaryVar(this, 4, 0);                     // 03 FLA10 S9(4) COMPUTATIONAL
    private BinaryVar fla11 = new BinaryVar(this, 4, 0);                     // 03 FLA11 S9(4) COMPUTATIONAL
    private BinaryVar fla12 = new BinaryVar(this, 4, 0);                     // 03 FLA12 S9(4) COMPUTATIONAL
    private BinaryVar fla13 = new BinaryVar(this, 4, 0);                     // 03 FLA13 S9(4) COMPUTATIONAL

    public WsGfvtfla0Null() {
        super();
        initialize();
    }

    public WsGfvtfla0Null(DataContainer parent) {
        super(parent);
    }

    public WsGfvtfla0Null(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFla01(final int fla01) {
        this.fla01.setValue(fla01);
    }

    public void setFla01(final BigDecimal fla01) {
        this.fla01.setValue(fla01);
    }

    public void setFla01(final INumericValue fla01) {
        this.fla01.setValue(fla01);
    }

    public void setFla02(final int fla02) {
        this.fla02.setValue(fla02);
    }

    public void setFla02(final BigDecimal fla02) {
        this.fla02.setValue(fla02);
    }

    public void setFla02(final INumericValue fla02) {
        this.fla02.setValue(fla02);
    }

    public void setFla03(final int fla03) {
        this.fla03.setValue(fla03);
    }

    public void setFla03(final BigDecimal fla03) {
        this.fla03.setValue(fla03);
    }

    public void setFla03(final INumericValue fla03) {
        this.fla03.setValue(fla03);
    }

    public void setFla04(final int fla04) {
        this.fla04.setValue(fla04);
    }

    public void setFla04(final BigDecimal fla04) {
        this.fla04.setValue(fla04);
    }

    public void setFla04(final INumericValue fla04) {
        this.fla04.setValue(fla04);
    }

    public void setFla05(final int fla05) {
        this.fla05.setValue(fla05);
    }

    public void setFla05(final BigDecimal fla05) {
        this.fla05.setValue(fla05);
    }

    public void setFla05(final INumericValue fla05) {
        this.fla05.setValue(fla05);
    }

    public void setFla06(final int fla06) {
        this.fla06.setValue(fla06);
    }

    public void setFla06(final BigDecimal fla06) {
        this.fla06.setValue(fla06);
    }

    public void setFla06(final INumericValue fla06) {
        this.fla06.setValue(fla06);
    }

    public void setFla07(final int fla07) {
        this.fla07.setValue(fla07);
    }

    public void setFla07(final BigDecimal fla07) {
        this.fla07.setValue(fla07);
    }

    public void setFla07(final INumericValue fla07) {
        this.fla07.setValue(fla07);
    }

    public void setFla08(final int fla08) {
        this.fla08.setValue(fla08);
    }

    public void setFla08(final BigDecimal fla08) {
        this.fla08.setValue(fla08);
    }

    public void setFla08(final INumericValue fla08) {
        this.fla08.setValue(fla08);
    }

    public void setFla09(final int fla09) {
        this.fla09.setValue(fla09);
    }

    public void setFla09(final BigDecimal fla09) {
        this.fla09.setValue(fla09);
    }

    public void setFla09(final INumericValue fla09) {
        this.fla09.setValue(fla09);
    }

    public void setFla10(final int fla10) {
        this.fla10.setValue(fla10);
    }

    public void setFla10(final BigDecimal fla10) {
        this.fla10.setValue(fla10);
    }

    public void setFla10(final INumericValue fla10) {
        this.fla10.setValue(fla10);
    }

    public void setFla11(final int fla11) {
        this.fla11.setValue(fla11);
    }

    public void setFla11(final BigDecimal fla11) {
        this.fla11.setValue(fla11);
    }

    public void setFla11(final INumericValue fla11) {
        this.fla11.setValue(fla11);
    }

    public void setFla12(final int fla12) {
        this.fla12.setValue(fla12);
    }

    public void setFla12(final BigDecimal fla12) {
        this.fla12.setValue(fla12);
    }

    public void setFla12(final INumericValue fla12) {
        this.fla12.setValue(fla12);
    }

    public void setFla13(final int fla13) {
        this.fla13.setValue(fla13);
    }

    public void setFla13(final BigDecimal fla13) {
        this.fla13.setValue(fla13);
    }

    public void setFla13(final INumericValue fla13) {
        this.fla13.setValue(fla13);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.fla01.clear();
        this.fla02.clear();
        this.fla03.clear();
        this.fla04.clear();
        this.fla05.clear();
        this.fla06.clear();
        this.fla07.clear();
        this.fla08.clear();
        this.fla09.clear();
        this.fla10.clear();
        this.fla11.clear();
        this.fla12.clear();
        this.fla13.clear();
    }

}

