package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.banderas.BanderasGlobales;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: BANDERAS.
 *
 */
// 772      *----                                                             
// 773      *    BANDERAS                                                     
// 774      *----                                                             
@Setter
@Getter
public class Banderas { // VStruct
    // Properties
    private BanderasGlobales banderasGlobales = new  BanderasGlobales();     // 02 BANDERAS-GLOBALES

    public Banderas() {
        initialize();
    }




    // Deeper properties

    // Property: banderasGlobales.stExcAgt -> 03 ST-EXC-AGT X(02)
    public String getStExcAgt() {
        return this.banderasGlobales.getStExcAgt();
    }

    public void setStExcAgt(final String stExcAgt) {
        this.banderasGlobales.setStExcAgt(stExcAgt);
    }

    // Property: banderasGlobales.wsFinAgtExc -> 03 WS-FIN-AGT-EXC 9(01)
    public int getWsFinAgtExc() {
        return this.banderasGlobales.getWsFinAgtExc();
    }

    public void setWsFinAgtExc(final int wsFinAgtExc) {
        this.banderasGlobales.setWsFinAgtExc(wsFinAgtExc);
    }

    // Property: banderasGlobales : condition FIN-AGT-EXC
    public boolean isFinAgtExc() {
        return this.banderasGlobales.isFinAgtExc();
    }

    public void setFinAgtExc() {
        this.banderasGlobales.setFinAgtExc();
    }

    // Property: banderasGlobales.wsEncAgtExc -> 03 WS-ENC-AGT-EXC 9(01)
    public int getWsEncAgtExc() {
        return this.banderasGlobales.getWsEncAgtExc();
    }

    public void setWsEncAgtExc(final int wsEncAgtExc) {
        this.banderasGlobales.setWsEncAgtExc(wsEncAgtExc);
    }

    // Property: banderasGlobales : condition ENC-AGT-EXC
    public boolean isEncAgtExc() {
        return this.banderasGlobales.isEncAgtExc();
    }

    public void setEncAgtExc() {
        this.banderasGlobales.setEncAgtExc();
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.banderasGlobales.initialize();
    }

}

