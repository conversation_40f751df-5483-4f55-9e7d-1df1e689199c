package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.tbddclgfvtfdc0.TbdTabla;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TBD-DCLGFVTFDC0.
 *
 */
@Setter
@Getter
public class TbdDclgfvtfdc0 { // VStruct
    // Properties
    private TbdTabla tbdTabla = new  TbdTabla(5000);                         // 05 TBD-TABLA [5000]

    public TbdDclgfvtfdc0() {
        initialize();
    }



    public TbdTabla getTbdTabla(int index) {
        return this.tbdTabla.at(index);
    }


    // Deeper properties

    // Property: tbdTabla.tbdAgtIdr -> 10 TBD-AGT-IDR S9(9) COMPUTATIONAL
    public BinaryVar getTbdAgtIdr() {
        return this.tbdTabla.getTbdAgtIdr();
    }

    public void setTbdAgtIdr(final int tbdAgtIdr) {
        this.tbdTabla.setTbdAgtIdr(tbdAgtIdr);
    }

    public void setTbdAgtIdr(final INumericValue tbdAgtIdr) {
        this.tbdTabla.setTbdAgtIdr(tbdAgtIdr);
    }

    // Property: tbdTabla.tbdEmpCve -> 10 TBD-EMP-CVE X(5)
    public AlphanumericVar getTbdEmpCve() {
        return this.tbdTabla.getTbdEmpCve();
    }

    public void setTbdEmpCve(final String tbdEmpCve) {
        this.tbdTabla.setTbdEmpCve(tbdEmpCve);
    }

    public void setTbdEmpCve(final IAlphanumericValue tbdEmpCve) {
        this.tbdTabla.setTbdEmpCve(tbdEmpCve);
    }

    // Property: tbdTabla.tbdCnaNum -> 10 TBD-CNA-NUM S9(4) COMPUTATIONAL
    public BinaryVar getTbdCnaNum() {
        return this.tbdTabla.getTbdCnaNum();
    }

    public void setTbdCnaNum(final int tbdCnaNum) {
        this.tbdTabla.setTbdCnaNum(tbdCnaNum);
    }

    public void setTbdCnaNum(final INumericValue tbdCnaNum) {
        this.tbdTabla.setTbdCnaNum(tbdCnaNum);
    }

    // Property: tbdTabla.tbdRamCve -> 10 TBD-RAM-CVE X(2)
    public AlphanumericVar getTbdRamCve() {
        return this.tbdTabla.getTbdRamCve();
    }

    public void setTbdRamCve(final String tbdRamCve) {
        this.tbdTabla.setTbdRamCve(tbdRamCve);
    }

    public void setTbdRamCve(final IAlphanumericValue tbdRamCve) {
        this.tbdTabla.setTbdRamCve(tbdRamCve);
    }

    // Property: tbdTabla.tbdSraCve -> 10 TBD-SRA-CVE X(4)
    public AlphanumericVar getTbdSraCve() {
        return this.tbdTabla.getTbdSraCve();
    }

    public void setTbdSraCve(final String tbdSraCve) {
        this.tbdTabla.setTbdSraCve(tbdSraCve);
    }

    public void setTbdSraCve(final IAlphanumericValue tbdSraCve) {
        this.tbdTabla.setTbdSraCve(tbdSraCve);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tbdTabla.initialize();
    }

}

