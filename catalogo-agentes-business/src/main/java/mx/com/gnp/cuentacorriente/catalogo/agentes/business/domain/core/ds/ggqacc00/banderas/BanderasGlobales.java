package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.banderas;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: BANDERAS-GLOBALES.
 *
 */
@Setter
@Getter
public class BanderasGlobales { // VStruct
    // Properties
    private int wkDisp = 0;                                                 // 03 WK-DISP 9(01)
    private int wkDisp1 = 0;                                                // 03 WK-DISP1 9(01)
    private String stExtraccion = "00";                                     // 03 ST-EXTRACCION X(02)
    private String stFacturacion = "00";                                    // 03 ST-FACTURACION X(02)
    private String stDetSusp = "00";                                        // 03 ST-DET-SUSP X(02)
    private String stCatDirec = "00";                                       // 03 ST-CAT-DIREC X(02)
    private String wsSt = "";                                               // 03 WS-ST X(02)
    private int wsFinExtraccion = 0;                                        // 03 WS-FIN-EXTRACCION 9(01)
    // Level 88 - Conditional names
    private static final int FIN_EXTRACCION = 1;

    private int wsFinCatDirecc = 0;                                         // 03 WS-FIN-CAT-DIRECC 9(01)
    // Level 88 - Conditional names
    private static final int FIN_CAT_DIRECC = 1;

    private int wsFinCurMte = 0;                                            // 03 WS-FIN-CUR-MTE 9(01)
    // Level 88 - Conditional names
    private static final int FIN_CUR_MTE = 1;

    private int wsFinCurEsa = 0;                                            // 03 WS-FIN-CUR-ESA 9(01)
    // Level 88 - Conditional names
    private static final int FIN_CUR_ESA = 1;

    private int wsEncDirecc = 0;                                            // 03 WS-ENC-DIRECC 9(01)
    // Level 88 - Conditional names
    private static final int ENC_DIRECC = 1;

    private int wsEncAgente = 0;                                            // 03 WS-ENC-AGENTE 9(01)
    // Level 88 - Conditional names
    private static final int ENC_AGENTE = 1;

    private int wsEncCna = 0;                                               // 03 WS-ENC-CNA 9(01)
    // Level 88 - Conditional names
    private static final int ENC_CNA = 1;

    private int wsEncDsc = 0;                                               // 03 WS-ENC-DSC 9(01)
    // Level 88 - Conditional names
    private static final int ENC_DSC = 1;

    private int wsEncEstatus = 0;                                           // 03 WS-ENC-ESTATUS 9(01)
    // Level 88 - Conditional names
    private static final int ENC_ESTATUS = 1;

    private int wsEncEstatusCna = 0;                                        // 03 WS-ENC-ESTATUS-CNA 9(01)
    // Level 88 - Conditional names
    private static final int ENC_ESTATUS_CNA = 1;

    private int wsEncExcluido = 0;                                          // 03 WS-ENC-EXCLUIDO 9(01)
    // Level 88 - Conditional names
    private static final int ENC_EXCLUIDO = 1;

    private int wsAgenteEstatus = 0;                                        // 03 WS-AGENTE-ESTATUS 9(04)
    // Level 88 - Conditional names
    private static final int AGENTE_ESTATUS$1 = 1;
    private static final int AGENTE_ESTATUS$2 = 2;
    private static final int AGENTE_ESTATUS$3 = 3;
    private static final int AGENTE_VIGENTE = 1;
    private static final int AGENTE_CANCELADO = 2;
    private static final int AGENTE_SUSPENDIDO = 3;

    private int wsCnaEstatus = 0;                                           // 03 WS-CNA-ESTATUS 9(04)
    // Level 88 - Conditional names
    private static final int CNA_ESTATUS$1 = 1;
    private static final int CNA_ESTATUS$2 = 2;
    private static final int CNA_ESTATUS$3 = 3;
    private static final int CNA_VIGENTE = 1;
    private static final int CNA_CANCELADO = 2;
    private static final int CNA_SUSPENDIDO = 3;

    private int wsFinCurDma = 0;                                            // 03 WS-FIN-CUR-DMA 9(01)
    // Level 88 - Conditional names
    private static final int FIN_CUR_DMA = 1;

    private int wsEncGfvtagt = 0;                                           // 03 WS-ENC-GFVTAGT 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GFVTAGT = 1;

    private int wsEncGfvtcna = 0;                                           // 03 WS-ENC-GFVTCNA 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GFVTCNA = 1;

    private int wsEncGfvtpra = 0;                                           // 03 WS-ENC-GFVTPRA 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GFVTPRA = 1;

    private int wsEncGcctmnd = 0;                                           // 03 WS-ENC-GCCTMND 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GCCTMND = 1;

    private int wsEncGcctpob = 0;                                           // 03 WS-ENC-GCCTPOB 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GCCTPOB = 1;

    private int wsEncGcctedo = 0;                                           // 03 WS-ENC-GCCTEDO 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GCCTEDO = 1;

    private int wsEncGcctpai = 0;                                           // 03 WS-ENC-GCCTPAI 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GCCTPAI = 1;

    private int wsEncDirec = 0;                                             // 03 WS-ENC-DIREC 9(01)
    // Level 88 - Conditional names
    private static final int ENC_DIREC = 1;

    private int wsEncGfvtcma = 0;                                           // 03 WS-ENC-GFVTCMA 9(01)
    // Level 88 - Conditional names
    private static final int ENC_GFVTCMA = 1;

    private int wsEncTrn = 0;                                               // 03 WS-ENC-TRN 9(01)
    // Level 88 - Conditional names
    private static final int ENC_TRN = 1;

    private int wsEncConcepto = 0;                                          // 03 WS-ENC-CONCEPTO 9(01)
    // Level 88 - Conditional names
    private static final int ENC_CONCEPTO = 1;

    private int wsSinMovs = 0;                                              // 03 WS-SIN-MOVS 9(01)
    // Level 88 - Conditional names
    private static final int SIN_MOVS = 1;


    public BanderasGlobales() {
        initialize();
    }



    // Conditionals (88) - WS-FIN-EXTRACCION
    public boolean isFinExtraccion() {
        return this.wsFinExtraccion == FIN_EXTRACCION;
    }

    public void setFinExtraccion() {
        this.wsFinExtraccion = FIN_EXTRACCION;
    }


    // Conditionals (88) - WS-FIN-CAT-DIRECC
    public boolean isFinCatDirecc() {
        return this.wsFinCatDirecc == FIN_CAT_DIRECC;
    }

    public void setFinCatDirecc() {
        this.wsFinCatDirecc = FIN_CAT_DIRECC;
    }


    // Conditionals (88) - WS-FIN-CUR-MTE
    public boolean isFinCurMte() {
        return this.wsFinCurMte == FIN_CUR_MTE;
    }

    public void setFinCurMte() {
        this.wsFinCurMte = FIN_CUR_MTE;
    }


    // Conditionals (88) - WS-FIN-CUR-ESA
    public boolean isFinCurEsa() {
        return this.wsFinCurEsa == FIN_CUR_ESA;
    }

    public void setFinCurEsa() {
        this.wsFinCurEsa = FIN_CUR_ESA;
    }


    // Conditionals (88) - WS-ENC-DIRECC
    public boolean isEncDirecc() {
        return this.wsEncDirecc == ENC_DIRECC;
    }

    public void setEncDirecc() {
        this.wsEncDirecc = ENC_DIRECC;
    }


    // Conditionals (88) - WS-ENC-AGENTE
    public boolean isEncAgente() {
        return this.wsEncAgente == ENC_AGENTE;
    }

    public void setEncAgente() {
        this.wsEncAgente = ENC_AGENTE;
    }


    // Conditionals (88) - WS-ENC-CNA
    public boolean isEncCna() {
        return this.wsEncCna == ENC_CNA;
    }

    public void setEncCna() {
        this.wsEncCna = ENC_CNA;
    }


    // Conditionals (88) - WS-ENC-DSC
    public boolean isEncDsc() {
        return this.wsEncDsc == ENC_DSC;
    }

    public void setEncDsc() {
        this.wsEncDsc = ENC_DSC;
    }


    // Conditionals (88) - WS-ENC-ESTATUS
    public boolean isEncEstatus() {
        return this.wsEncEstatus == ENC_ESTATUS;
    }

    public void setEncEstatus() {
        this.wsEncEstatus = ENC_ESTATUS;
    }


    // Conditionals (88) - WS-ENC-ESTATUS-CNA
    public boolean isEncEstatusCna() {
        return this.wsEncEstatusCna == ENC_ESTATUS_CNA;
    }

    public void setEncEstatusCna() {
        this.wsEncEstatusCna = ENC_ESTATUS_CNA;
    }


    // Conditionals (88) - WS-ENC-EXCLUIDO
    public boolean isEncExcluido() {
        return this.wsEncExcluido == ENC_EXCLUIDO;
    }

    public void setEncExcluido() {
        this.wsEncExcluido = ENC_EXCLUIDO;
    }


    // Conditionals (88) - WS-AGENTE-ESTATUS
    public boolean isAgenteEstatus() {
        return this.wsAgenteEstatus == AGENTE_ESTATUS$1 || this.wsAgenteEstatus == AGENTE_ESTATUS$2 || this.wsAgenteEstatus == AGENTE_ESTATUS$3;
    }

    public void setAgenteEstatus() {
        this.wsAgenteEstatus = AGENTE_ESTATUS$1;
    }

    public boolean isAgenteVigente() {
        return this.wsAgenteEstatus == AGENTE_VIGENTE;
    }

    public void setAgenteVigente() {
        this.wsAgenteEstatus = AGENTE_VIGENTE;
    }

    public boolean isAgenteCancelado() {
        return this.wsAgenteEstatus == AGENTE_CANCELADO;
    }

    public void setAgenteCancelado() {
        this.wsAgenteEstatus = AGENTE_CANCELADO;
    }

    public boolean isAgenteSuspendido() {
        return this.wsAgenteEstatus == AGENTE_SUSPENDIDO;
    }

    public void setAgenteSuspendido() {
        this.wsAgenteEstatus = AGENTE_SUSPENDIDO;
    }


    // Conditionals (88) - WS-CNA-ESTATUS
    public boolean isCnaEstatus() {
        return this.wsCnaEstatus == CNA_ESTATUS$1 || this.wsCnaEstatus == CNA_ESTATUS$2 || this.wsCnaEstatus == CNA_ESTATUS$3;
    }

    public void setCnaEstatus() {
        this.wsCnaEstatus = CNA_ESTATUS$1;
    }

    public boolean isCnaVigente() {
        return this.wsCnaEstatus == CNA_VIGENTE;
    }

    public void setCnaVigente() {
        this.wsCnaEstatus = CNA_VIGENTE;
    }

    public boolean isCnaCancelado() {
        return this.wsCnaEstatus == CNA_CANCELADO;
    }

    public void setCnaCancelado() {
        this.wsCnaEstatus = CNA_CANCELADO;
    }

    public boolean isCnaSuspendido() {
        return this.wsCnaEstatus == CNA_SUSPENDIDO;
    }

    public void setCnaSuspendido() {
        this.wsCnaEstatus = CNA_SUSPENDIDO;
    }


    // Conditionals (88) - WS-FIN-CUR-DMA
    public boolean isFinCurDma() {
        return this.wsFinCurDma == FIN_CUR_DMA;
    }

    public void setFinCurDma() {
        this.wsFinCurDma = FIN_CUR_DMA;
    }


    // Conditionals (88) - WS-ENC-GFVTAGT
    public boolean isEncGfvtagt() {
        return this.wsEncGfvtagt == ENC_GFVTAGT;
    }

    public void setEncGfvtagt() {
        this.wsEncGfvtagt = ENC_GFVTAGT;
    }


    // Conditionals (88) - WS-ENC-GFVTCNA
    public boolean isEncGfvtcna() {
        return this.wsEncGfvtcna == ENC_GFVTCNA;
    }

    public void setEncGfvtcna() {
        this.wsEncGfvtcna = ENC_GFVTCNA;
    }


    // Conditionals (88) - WS-ENC-GFVTPRA
    public boolean isEncGfvtpra() {
        return this.wsEncGfvtpra == ENC_GFVTPRA;
    }

    public void setEncGfvtpra() {
        this.wsEncGfvtpra = ENC_GFVTPRA;
    }


    // Conditionals (88) - WS-ENC-GCCTMND
    public boolean isEncGcctmnd() {
        return this.wsEncGcctmnd == ENC_GCCTMND;
    }

    public void setEncGcctmnd() {
        this.wsEncGcctmnd = ENC_GCCTMND;
    }


    // Conditionals (88) - WS-ENC-GCCTPOB
    public boolean isEncGcctpob() {
        return this.wsEncGcctpob == ENC_GCCTPOB;
    }

    public void setEncGcctpob() {
        this.wsEncGcctpob = ENC_GCCTPOB;
    }


    // Conditionals (88) - WS-ENC-GCCTEDO
    public boolean isEncGcctedo() {
        return this.wsEncGcctedo == ENC_GCCTEDO;
    }

    public void setEncGcctedo() {
        this.wsEncGcctedo = ENC_GCCTEDO;
    }


    // Conditionals (88) - WS-ENC-GCCTPAI
    public boolean isEncGcctpai() {
        return this.wsEncGcctpai == ENC_GCCTPAI;
    }

    public void setEncGcctpai() {
        this.wsEncGcctpai = ENC_GCCTPAI;
    }


    // Conditionals (88) - WS-ENC-DIREC
    public boolean isEncDirec() {
        return this.wsEncDirec == ENC_DIREC;
    }

    public void setEncDirec() {
        this.wsEncDirec = ENC_DIREC;
    }


    // Conditionals (88) - WS-ENC-GFVTCMA
    public boolean isEncGfvtcma() {
        return this.wsEncGfvtcma == ENC_GFVTCMA;
    }

    public void setEncGfvtcma() {
        this.wsEncGfvtcma = ENC_GFVTCMA;
    }


    // Conditionals (88) - WS-ENC-TRN
    public boolean isEncTrn() {
        return this.wsEncTrn == ENC_TRN;
    }

    public void setEncTrn() {
        this.wsEncTrn = ENC_TRN;
    }


    // Conditionals (88) - WS-ENC-CONCEPTO
    public boolean isEncConcepto() {
        return this.wsEncConcepto == ENC_CONCEPTO;
    }

    public void setEncConcepto() {
        this.wsEncConcepto = ENC_CONCEPTO;
    }


    // Conditionals (88) - WS-SIN-MOVS
    public boolean isSinMovs() {
        return this.wsSinMovs == SIN_MOVS;
    }

    public void setSinMovs() {
        this.wsSinMovs = SIN_MOVS;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wkDisp = 0;
        this.wkDisp1 = 0;
        this.stExtraccion = "00";
        this.stFacturacion = "00";
        this.stDetSusp = "00";
        this.stCatDirec = "00";
        this.wsSt = "";
        this.wsFinExtraccion = 0;
        this.wsFinCatDirecc = 0;
        this.wsFinCurMte = 0;
        this.wsFinCurEsa = 0;
        this.wsEncDirecc = 0;
        this.wsEncAgente = 0;
        this.wsEncCna = 0;
        this.wsEncDsc = 0;
        this.wsEncEstatus = 0;
        this.wsEncEstatusCna = 0;
        this.wsEncExcluido = 0;
        this.wsAgenteEstatus = 0;
        this.wsCnaEstatus = 0;
        this.wsFinCurDma = 0;
        this.wsEncGfvtagt = 0;
        this.wsEncGfvtcna = 0;
        this.wsEncGfvtpra = 0;
        this.wsEncGcctmnd = 0;
        this.wsEncGcctpob = 0;
        this.wsEncGcctedo = 0;
        this.wsEncGcctpai = 0;
        this.wsEncDirec = 0;
        this.wsEncGfvtcma = 0;
        this.wsEncTrn = 0;
        this.wsEncConcepto = 0;
        this.wsSinMovs = 0;
    }

}

