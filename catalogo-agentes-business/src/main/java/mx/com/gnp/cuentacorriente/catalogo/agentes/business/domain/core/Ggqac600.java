
/**
 *  Program: Ggqac600.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.common.cbl.functions.Functions;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.Cqc6hReg;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.RegSalisr;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00olcpy.AbReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00lncpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa0053cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa0068cpy.Ccpa_01Reg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00ogcpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00d0cpy.LhfaLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00l6cpy.WsLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa001kcpy.LcnaLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa001lcpy.LgralLinkReg;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.WlParams;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.WlParamsSal;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.WsCamposTrabajo;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.W500Fecha;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.W500FechaDb2;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.A990CifCtl;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.SemSwiches;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.Banderas;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.Indices;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.TablaExcAgt;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.components.Ggqa0049cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcna0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdrc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.ISysdummy1Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcna0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdrc0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagt0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqac600 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private Cqc6hReg cqc6hReg = new  Cqc6hReg();                             // 01 CQC6H-REG UsedAsReferenceParameter
    private RegSalisr regSalisr = new  RegSalisr();                          // 01 REG-SALISR UsedAsReferenceParameter

    // Copy file QA$CBLCPY$GGQA00OL
    private AbReg abReg = new  AbReg();                                      // 01 AB-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00OL

    // Copy file QA$CBLCPY$GGQA00LN
    private Ggqa00lncpy ggqa00lncpy = new  Ggqa00lncpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00LN

    // Copy file QA$CBLCPY$GGQA0053
    private Ggqa0053cpy ggqa0053cpy = new  Ggqa0053cpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA0053

    // Auto variables
//  private int ccI;                                // CC-I // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GGQA0068
    private Ccpa_01Reg ccpa_01Reg = new  Ccpa_01Reg();                       // 01 CCPA-01-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA0068

    // Copy file QA$CBLCPY$GGQA00OG
    private Ggqa00ogcpy ggqa00ogcpy = new  Ggqa00ogcpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00OG

    // Auto variables
//  private int t999MdI;                            // T999-MD-I // [@WARNING variable NOT USED]
    private IndexVar iError = new IndexVar();        // I-ERROR

    // Copy file QA$CBLCPY$GGQA00D0
    private LhfaLinkReg lhfaLinkReg = new  LhfaLinkReg();                    // 01 LHFA-LINK-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00D0

    // Copy file QA$CBLCPY$GGQA00L6
    private WsLinkReg wsLinkReg = new  WsLinkReg();                          // 01 WS-LINK-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00L6

    // Copy file QA$CBLCPY$GGQA001K
    private LcnaLinkReg lcnaLinkReg = new  LcnaLinkReg();                    // 01 LCNA-LINK-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA001K

    // Copy file QA$CBLCPY$GGQA001L
    private LgralLinkReg lgralLinkReg = new  LgralLinkReg();                 // 01 LGRAL-LINK-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA001L

    // Copy file QA$CBLCPY$GFVTAGT0
//  private Dclgfvtagt0 dclgfvtagt0 = new  Dclgfvtagt0();                        // 01 DCLGFVTAGT0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTAGT0

    // Copy file QA$CBLCPY$GCQTHFA0
//  private Gcqthfa0 gcqthfa0 = new  Gcqthfa0();                                 // 01 GCQTHFA0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GCQTHFA0

    // Copy file QA$CBLCPY$GFVTDRC0
//  private Dclgfvtdrc0 dclgfvtdrc0 = new  Dclgfvtdrc0();                        // 01 DCLGFVTDRC0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTDRC0

    // Level 1
    private WlParams wlParams = new  WlParams();                             // 01 WL-PARAMS UsedAsParameter
    private WlParamsSal wlParamsSal = new  WlParamsSal();                    // 01 WL-PARAMS-SAL UsedAsParameter
    private AlphanumericVar w000Prog = new AlphanumericVar(8);               // 01 W000-PROG X(08)
//  private String w000Fs = "";                                             // 01 W000-FS XX // [@WARNING variable NOT USED]
    private WsCamposTrabajo wsCamposTrabajo = new  WsCamposTrabajo();        // 01 WS-CAMPOS-TRABAJO
    private W500Fecha w500Fecha = new  W500Fecha();                          // 01 W500-FECHA
    private W500FechaDb2 w500FechaDb2 = new  W500FechaDb2();                 // 01 W500-FECHA-DB2 UsedAsParameter
    private A990CifCtl a990CifCtl = new  A990CifCtl();                       // 01 A990-CIF-CTL
    private NumericEditedVar a990Cifra = new NumericEditedVar("ZZZ,ZZZ,ZZ9-");               // 01 A990-CIFRA ZZZ,ZZZ,ZZ9-
    private String w000Select = "SELECT";                                   // 01 W000-SELECT X(06)
    private String w000Insert = "INSERT";                                   // 01 W000-INSERT X(06)
    private String w000Update = "UPDATE";                                   // 01 W000-UPDATE X(06)
//  private String w000Delete = "DELETE";                                   // 01 W000-DELETE X(06) // [@WARNING variable NOT USED]
    private String w000Consulta = "CONSULTA";                               // 01 W000-CONSULTA X(08)
    private String w000OpcionOpen = "OPEN";                                 // 01 W000-OPCION-OPEN X(04)
    private String w000OpcionFetch = "FETCH";                               // 01 W000-OPCION-FETCH X(05)
    private String w000OpcionClose = "CLOSE";                               // 01 W000-OPCION-CLOSE X(05)
    private String w000ParamP01 = "P01";                                    // 01 W000-PARAM-P01 X(03)
    private String w000S = "S";                                             // 01 W000-S X(01)
    private String w000N = "N";                                             // 01 W000-N X(01)
    private int w000_1 = 1;                                                 // 01 W000-1 S9(4) COMPUTATIONAL
    private int w000_10 = 10;                                               // 01 W000-10 S9(4) COMPUTATIONAL
    private String wc_1 = "1";                                              // 01 WC-1 X(01)
    private int w000LimiteEstatus = 100;                                    // 01 W000-LIMITE-ESTATUS 9(4)
//  private int w000Sqlcode_803 = -803;                                     // 01 W000-SQLCODE-803 S9(4) COMPUTATIONAL-3 // [@WARNING variable NOT USED]
//  private String w000RegAct = "1";                                        // 01 W000-REG-ACT X(01) // [@WARNING variable NOT USED]
//  private String w000RegNoact = "0";                                      // 01 W000-REG-NOACT X(01) // [@WARNING variable NOT USED]
    private String w000UsuCve = "********";                                 // 01 W000-USU-CVE X(08)
    private SemSwiches semSwiches = new  SemSwiches();                       // 01 SEM-SWICHES
    private Banderas banderas = new  Banderas();                             // 01 BANDERAS
    private Indices indices = new  Indices();                                // 01 INDICES
    private TablaExcAgt tablaExcAgt = new  TablaExcAgt();                    // 01 TABLA-EXC-AGT

    private Sqlca sqlca;
    // Files
    private ISequentialFile catExclusionAgt;
    // INI
    private ISequentialFile agentisr;


    // Copy routines
    private Ggqa0049cpy ggqa0049cpy;


    // Declare Sql Ports
    private IGfvtcna0Port gfvtcna0Port;
    private IGfvtdrc0Port gfvtdrc0Port;
    private IGfvtagt0Port gfvtagt0Port;
    private ISysdummy1Port sysdummy1Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtcna0Model> gfvtcna0Model;
    private Optional<Gfvtdrc0Model> gfvtdrc0Model;
    private Optional<Gfvtagt0Model> gfvtagt0Model;

    public Ggqac600(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();

        // Copy routines
        ggqa0049cpy = new Ggqa0049cpy(this, ggqa00lncpy, abReg, w000Prog);

        initialize();
        initFiles();
    }

    // Initial values
    @Override
    public void initialize() {
        this.w000Prog.setValue("GGQAC600");
        this.w000Select = "SELECT";
        this.w000Insert = "INSERT";
        this.w000Update = "UPDATE";
//        this.w000Delete = "DELETE"; // [@WARNING variable NOT USED]
        this.w000Consulta = "CONSULTA";
        this.w000OpcionOpen = "OPEN";
        this.w000OpcionFetch = "FETCH";
        this.w000OpcionClose = "CLOSE";
        this.w000ParamP01 = "P01";
        this.w000S = "S";
        this.w000N = "N";
        this.w000_1 = 1;
        this.w000_10 = 10;
        this.wc_1 = "1";
        this.w000LimiteEstatus = 100;
//        this.w000Sqlcode_803 = -803; // [@WARNING variable NOT USED]
//        this.w000RegAct = "1"; // [@WARNING variable NOT USED]
//        this.w000RegNoact = "0"; // [@WARNING variable NOT USED]
        this.w000UsuCve = "********";
        this.indices.initialize();
    }


    public void initFiles() {

        this.catExclusionAgt = createSequentialFile("CAT-EXCLUSION-AGT", "GGQAC6I1", cqc6hReg);
        // INI

        this.agentisr = createSequentialFile("AGENTISR", regSalisr);

        this.catExclusionAgt.setRecordLength(7);
        this.catExclusionAgt.onChangeFileStatus(status -> banderas.setStExcAgt(status));


    }

    public void run() {
        p001Proceso();
    }

    /**
     *  Section: 001-PROCESO
     *  Paragraph: unnamedProcedure.
     */
    void p001Proceso() {
        p100Inicio();
        p200Proceso();
        p999Termina();
    }

    /**
     *  Section: 100-INICIO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA INICIO DE PROGRAMA
     */
    void p100Inicio() {
        agentisr.openOutput();
        p110InicializaCampos();
        p120LeeParametroP01();
        p130AbreCursorAgt0();
        p140AbreExclusionAgt();
        p160LoopExclusionAgt();
    }

    /**
     *  Section: 110-INICIALIZA-CAMPOS
     *  Paragraph: unnamedProcedure.
     *
     * INICIALIZA CAMPOS A UTILIZAR
     */
    void p110InicializaCampos() {
        wsCamposTrabajo.initialize();
        wsLinkReg.initialize();
        w500Fecha.initialize();
        w500FechaDb2.initialize();
        a990CifCtl.initialize();
        banderas.setWsFinAgtExc(0);

        tablaExcAgt.initialize();
        // --SEMAFORO NO ES FIN DE AGT0                                     
        semSwiches.setSwNoFinAgt0();
        // --SEMAFORO NO ES CAMPO DIFERENTE                                 
        semSwiches.setSwNoCampoDifer();
    }

    /**
     *  Section: 120-LEE-PARAMETRO-P01
     *  Paragraph: unnamedProcedure.
     *
     * RUTINA DE LECTURA DEL PARAMETRO P01
     */
    void p120LeeParametroP01() {
        wsCamposTrabajo.setS999Err("0");
        ccpa_01Reg.setSpaces();
        ccpa_01Reg.setCcpa_01CParam(w000ParamP01);
        runProgram("GGQAIE00", ccpa_01Reg, 
                wsCamposTrabajo.getS999Err());
        if (wsCamposTrabajo.getS999Err().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("PARAMETRO P01 NO EXISTE");
            abReg.setAbProceso("120-LEE-PARAMETRO-P01");
            ggqa0049cpy.p980Aborta();
        }
        w500Fecha.setValue(ccpa_01Reg.getCcpa_01Fhproc());
        w500FechaDb2.setW500AnoDb2(w500Fecha.getW500Ano());
        w500FechaDb2.setW500MesDb2(w500Fecha.getW500Mes());
        w500FechaDb2.setW500DiaDb2(w500Fecha.getW500Dia());
    }

    /**
     *  Section: 130-ABRE-CURSOR-AGT0
     *  Paragraph: unnamedProcedure.
     *
     * RUTINA PARA ABRIR CURSOR AGT0
     */
    void p130AbreCursorAgt0() {
        wsLinkReg.setLagtOpcion(w000OpcionOpen);
        runProgram("GGQAF100", wsLinkReg);
        if (!wsLinkReg.getLagtSwErr().isEqual("0")) {
            abReg.getAbMensaje().at(1).setValue("ERROR AL ABRIR CURSOR AGT0");
            abReg.setAbProceso("130-ABRE-CURSOR-AGT0");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 140-ABRE-EXCLUSION-AGT
     *  Paragraph: unnamedProcedure.
     *
     * ABRE EL ARCHIVO DE AGENTES EXCLUIDOS
     */
    void p140AbreExclusionAgt() {
        catExclusionAgt.openInput();
        if (!banderas.getStExcAgt().equals("00") 
            && !banderas.getStExcAgt().equals("97")) {
            banderas.setWsFinAgtExc(1);

            abReg.getAbMensaje().at(1).setValue("CAT-EXCLSION-AGT   ");
            abReg.getAbMensaje().at(2).setValue(banderas.getStExcAgt());
            abReg.setAbProceso("140-ABRE-EXCLUSION-AGT");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 160-LOOP-EXCLUSION-AGT
     *  Paragraph: unnamedProcedure.
     *
     * PROCESO PARA CARGA DE LA TABLA                            *
     */
    void p160LoopExclusionAgt() {
        p161LeeExclusionAgt();
        while (!(banderas.isFinAgtExc())) {
                p162CargaExclusionAgt();
                p161LeeExclusionAgt();
        }
    }

    /**
     *  Section: 161-LEE-EXCLUSION-AGT
     *  Paragraph: unnamedProcedure.
     *
     * RUTINA PARA LEER EL ARCHIVO DE EXCLUSION DE AGENTES       *
     */
    void p161LeeExclusionAgt() {
        catExclusionAgt.read();
        if(catExclusionAgt.isEof()) {
            banderas.setWsFinAgtExc(1);

        } else {
            a990CifCtl.setA990RegLeiAgtExc(a990CifCtl.getA990RegLeiAgtExc() + 1);
        }
    }

    /**
     *  Section: 162-CARGA-EXCLUSION-AGT
     *  Paragraph: unnamedProcedure.
     *
     * CARGA EL CATALOGO DE ESTATUS A UN ARREGLO EN WORKING      *
     */
    void p162CargaExclusionAgt() {
        indices.setWsInd(indices.getWsInd() + 1);
        if (indices.getWsInd() > w000LimiteEstatus) {
            display("ERROR, EL ARREGLO DE LA EXCLUSION AGT   ", "REBASA EL LIMITE: ", w000LimiteEstatus, " ARREGLO: ", indices.getWsInd());
            display("INCREMENTE EL LIMITE EN EL ARREGLO    ");
            abReg.getAbMensaje().at(1).setValue("ERROR ARREGLO AGT EXCLUIDOS   ");
            abReg.getAbMensaje().at(2).setValue("016");
            abReg.setAbProceso("162-CARGA-EXCLUSION");
            ggqa0049cpy.p980Aborta();
        }
        tablaExcAgt.getTabExcAgt().at(indices.getWsInd()).getTbAgente().setValue(cqc6hReg.getCqc6hAgt());
        tablaExcAgt.getTabExcAgt().at(indices.getWsInd()).setTbIndExcAgt(cqc6hReg.getCqc6hIndExc());
    }

    /**
     *  Section: 200-PROCESO
     *  Paragraph: unnamedProcedure.
     *
     * PROCESA REGISTRO
     */
    void p200Proceso() {
        while (!(semSwiches.isSwSiFinAgt0())) {
                p300FetchAgt0();
        }
        p999CloseCursorAgt0();
    }

    /**
     *  Section: 300-FETCH-AGT0
     *  Paragraph: unnamedProcedure.
     *
     * RUTINA PARA LEER CURSOR AGT0
     */
    void p300FetchAgt0() {
        wsLinkReg.setLagtOpcion(w000OpcionFetch);
        runProgram("GGQAF100", wsLinkReg);
        if (wsLinkReg.getLagtSwErr().isEqual("0")) {
            a990CifCtl.setA990RegLeiAgtA1(a990CifCtl.getA990RegLeiAgtA1() + 1);
            p310MueveValoresAgt();
            p400LlamadoRutinaGral();
        } else if (wsLinkReg.getLagtSwErr().isEqual("1")) {
            semSwiches.setSwSiFinAgt0();
            abReg.getAbMensaje().at(1).setValue("SIN DATOS EN EN RUTINA AGT0");
            abReg.setAbProceso("300-FETCH-AGT0 ");
        }
    }

    /**
     *  Section: 310-MUEVE-VALORES-AGT
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE VALORES RECUPERADOS DE LA RUTINA AGT0
     */
    void p310MueveValoresAgt() {
        wsCamposTrabajo.getWsAgtReg().initialize();
        wsCamposTrabajo.getWsAgtReg().setValue(wsLinkReg.getLagtReg());
    }

    /**
     *  Section: 400-LLAMADO-RUTINA-GRAL
     *  Paragraph: unnamedProcedure.
     *
     * LLAMADO DE LA RUTINA QUE OBTIENE DATOS GENERALES DEL AGENTE
     */
    void p400LlamadoRutinaGral() {
        lgralLinkReg.initialize();
        lgralLinkReg.setLgralAgtIdr(wsCamposTrabajo.getWsAgtIdr());
        wsCamposTrabajo.setWsTabAgtN(wsCamposTrabajo.getWsAgtIdr());
        runProgram("GGQACC00", lgralLinkReg);
        if (lgralLinkReg.getLgralSwErr().isEqual("0")) {
            p410MueveValoresGral();
            p500LlamadoRutinaCna();
        } else if (lgralLinkReg.getLgralSwErr().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("ERROR EN RUTINA GGQACC00");
            abReg.setAbProceso("400-LLAMADO-RUTINA-GRAL");
        }
        //           PERFORM 980-ABORTA                                     
    }

    /**
     *  Section: 410-MUEVE-VALORES-GRAL
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE VALORES DEL AGENTE RECUPERADOS DE LA RUTINA GENERAL
     */
    void p410MueveValoresGral() {
        wsCamposTrabajo.getWsDatosGralAgt().initialize();
        wsCamposTrabajo.getWsDatosGralAgt().setValue(lgralLinkReg.getLgralSalida());
    }

    /**
     *  Section: 500-LLAMADO-RUTINA-CNA
     *  Paragraph: unnamedProcedure.
     *
     * REALIZA LLAMADO A LA RUTINA CNA0 Y DSC0 PARA OBTENER
     * LOS VALORES   DEL AGENTE
     */
    void p500LlamadoRutinaCna() {
        lcnaLinkReg.initialize();
        //  BUSCA AGENTE EN TABLA DE REGIMEN FISCAL Y RECUPERA DATOS        
        //  FISCALES                                                        
        llamadoRegimenFiscal();
        lcnaLinkReg.setLcnaCdoper(w000Consulta);
        lcnaLinkReg.setLcnaAgtIdr(wsCamposTrabajo.getWsAgtIdr());
        runProgram("GGQACB00", lcnaLinkReg);
        if (lcnaLinkReg.getLcnaSwErr().isEqual("0")) {
            p510MueveValoresCna();
            p530ValidarEstatusAgt();
            p540ObtenerIndPago();
            p600LlamadoRutinaHfa();
        } else if (lcnaLinkReg.getLcnaSwErr().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("ERROR EN RUTINA GGQACB00 ");
            abReg.setAbProceso("600-LLAMADO-RUTINA-CNA");
        }
    }

    /**
     *  Section: LLAMADO-REGIMEN-FISCAL
     *  Paragraph: unnamedProcedure.
     *
     * BUSCA AGENTE Y RECUPERA DATOS DE LA TABLA DE REGIMEN
     * FISCAL
     */
    void llamadoRegimenFiscal() {
        wlParams.setWlOpcion("BUSQ");
        wlParams.setWlAgente(wsCamposTrabajo.getWsTabAgt());
        runProgram("GGQA5Q00", wlParams, 
                wlParamsSal);
        wsCamposTrabajo.setWsPjeCedNovi(numvalC(wlParamsSal.getWlPcenvi()));
        wsCamposTrabajo.setWsPjeCedVida(numvalC(wlParamsSal.getWlPcevid()));
    }

    /**
     *  Section: 510-MUEVE-VALORES-CNA
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE VALORES RECUPERADOS DE LA RUTINA CNA0
     */
    void p510MueveValoresCna() {
        wsCamposTrabajo.getWsCnaAgtReg().initialize();
        wsCamposTrabajo.getWsCnaAgtReg().setValue(lcnaLinkReg.getLcnaSalida());
    }

    /**
     *  Section: 520-OBTIENE-INDI-EXC
     *  Paragraph: unnamedProcedure.
     *
     * OBTIENE DE LA TABLA EL INDICADOR DE EXCLUSION
     */
    void p520ObtieneIndiExc() {
        banderas.setWsEncAgtExc(0);

        wsCamposTrabajo.setWsAgtIdrAux(wsCamposTrabajo.getWsAgtIdr().toInt());

        indices.setWsI(1);
        while (!(indices.getWsI() > w000LimiteEstatus
            || banderas.isEncAgtExc()
            || tablaExcAgt.getTabExcAgt().at(indices.getWsI()).getTbAgente().toInt() == 0)) {
            if (tablaExcAgt.getTabExcAgt().at(indices.getWsI()).getTbAgente().toInt() == wsCamposTrabajo.getWsAgtIdrAux()) {
                wsCamposTrabajo.setWsIndExcAgt(tablaExcAgt.getTabExcAgt().at(indices.getWsI()).getTbIndExcAgt().toStringValue());

                banderas.setWsEncAgtExc(1);

            } else if (wsCamposTrabajo.getWsHfaIndExc().isSpaces()) {
                wsCamposTrabajo.setWsIndExcAgt(w000N);

            } else {
                wsCamposTrabajo.setWsIndExcAgt(wsCamposTrabajo.getWsHfaIndExc().toStringValue());

            }
            indices.setWsI(indices.getWsI() + 1);
        }
    }

    /**
     *  Section: 530-VALIDAR-ESTATUS-AGT
     *  Paragraph: unnamedProcedure.
     *
     * VALIDA ESTATUS AGENTE
     */
    void p530ValidarEstatusAgt() {
        semSwiches.setSwNoCambiarStAgtVig();
        genericModel = 
            gfvtagt0Port.select_11(
                GenericModel.builder().values(List.of(
                    wsCamposTrabajo.getWsAgtIdr().toInt(),
                    w000_1))
                .build());

        if(genericModel.isPresent()) {
            wsCamposTrabajo.setWsEsaCve(fromHostInt(genericModel.get().values().get(0)));
            wsCamposTrabajo.setWsEsaDes(fromHostString(genericModel.get().values().get(1)));
        }
        p999VerErrSql();
        if (ggqa0053cpy.getBdErr().isEqual(wc_1)) {
            wsCamposTrabajo.setWnNro(0);

            wsCamposTrabajo.setWnNull(0);

            genericModel = 
                gfvtcna0Port.select_13(
                    GenericModel.builder().values(List.of(
                        w000_1,
                        wsCamposTrabajo.getWsAgtIdr().toInt()))
                    .build());

            if(genericModel.isPresent()) {
                wsCamposTrabajo.setWsAgtIdrAux2(fromHostInt(genericModel.get().values().get(0)));
                wsCamposTrabajo.setWnNro(fromHostInt(genericModel.get().values().get(1)));
                wsCamposTrabajo.setWnNull(isNull(genericModel.get().values().get(1)) ? -1 : 0);
            }
            p999VerErrSql();
            if (ggqa0053cpy.getBdErr().isZero()) {
                semSwiches.setSwSiCambiarStAgtVig();
            }
        }
    }

    /**
     *  Section: 540-OBTENER-IND-PAGO
     *  Paragraph: unnamedProcedure.
     *
     * OBTIENE INDICADOR DE PAGO
     */
    void p540ObtenerIndPago() {
        gfvtdrc0Model = 
            gfvtdrc0Port.select_5(
                Gfvtdrc0Model.builder()
                    .draCve(w000_10)
                    .agtIdr(wsCamposTrabajo.getWsAgtIdr().toInt())
                .build());

        if(gfvtdrc0Model.isPresent()) {
            wsCamposTrabajo.setWsAgtIdrAux2(fromHostInt(gfvtdrc0Model.get().agtIdr()));
        }
        if (ggqa0053cpy.getBdErr().isZero()) {
            wsCamposTrabajo.setWsIndPag(w000S);

        } else {
            wsCamposTrabajo.setWsIndPag(w000N);

        }
    }

    /**
     *  Section: 600-LLAMADO-RUTINA-HFA
     *  Paragraph: unnamedProcedure.
     *
     * REALIZA LLAMADO A LA RUTINA HFA0 ENVIANDO AGT_IDR
     */
    void p600LlamadoRutinaHfa() {
        semSwiches.setSwNoCampoDifer();
        semSwiches.setSwNoPrimeraVez();
        lhfaLinkReg.initialize();
        lhfaLinkReg.setLhfaOpcion(w000Select);
        lhfaLinkReg.setLhfaAgtIdr(wsCamposTrabajo.getWsAgtIdr());
        runProgram("GGQACV00", lhfaLinkReg);
        if (lhfaLinkReg.getLhfaSwErr().isEqual("0")) {
            p610MueveValoresHfa();
            p520ObtieneIndiExc();
            p620ComparaAgtHfa();
            if (semSwiches.isSwSiCampoDifer()) {
                p630MueveValorInsert();
                p640CalculaFechasProc();
                p999ActualizaControlHfa0();
                p650InsertHfa();
            } else if (semSwiches.isSwNoCampoDifer()) {
                p660ObtFecFinPro();
                p670UpdateHfa();
            }
            semSwiches.setSwNoCampoDifer();
        } else {
            if (lhfaLinkReg.getLhfaSwErr().isEqual("1")) {
                semSwiches.setSwSiPrimeraVez();
                p520ObtieneIndiExc();
                p630MueveValorInsert();
                p640CalculaFechasProc();
                p999ActualizaControlHfa0();
                p650InsertHfa();
            }
            semSwiches.setSwNoPrimeraVez();
        }
    }

    /**
     *  Section: 610-MUEVE-VALORES-HFA
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE VALORES RECUPERADOS DE LA RUTINA HFA0
     */
    void p610MueveValoresHfa() {
        wsCamposTrabajo.getWsHfaReg().initialize();
        wsCamposTrabajo.getWsHfaReg().setValue(lhfaLinkReg.getLhfaSalida());
    }

    /**
     *  Section: 620-COMPARA-AGT-HFA
     *  Paragraph: unnamedProcedure.
     *
     * COMPARA VALORES DEL AGENTE CON VALORES DE LA ENTIDAD HFA0
     */
    void p620ComparaAgtHfa() {
        display(">>> 620-COMPARA-AGT-HFA ");
        //     DISPLAY '*********** COMPARA ************ '                  
        // *   DISPLAY 'SEM-CAMBIAR-STTT: ' SEM-CAMBIAR-ST-AGT-VIG          
        // *   DISPLAY 'WS-IND-EXC-AGT: '  WS-IND-EXC-AGT                   
        // *   DISPLAY 'WS-HFA-IND-EXC: '  WS-HFA-IND-EXC                   
        // *   DISPLAY 'WS-ESA-CVE    : '  WS-ESA-CVE                       
        // *   DISPLAY 'WS-GRAL-ESA-CVE: ' WS-GRAL-ESA-CVE                  
        // *   DISPLAY 'WS-HFA-ESA-CVE : ' WS-HFA-ESA-CVE                   
        // *   DISPLAY 'WS-GRAL-PEF-CVE: ' WS-GRAL-PEF-CVE                  
        // *   DISPLAY 'WS-HFA-PEF-CVE : ' WS-HFA-PEF-CVE                   
        // *   DISPLAY 'WS-CNA-OFN-CVE : ' WS-CNA-OFN-CVE                   
        // *   DISPLAY 'WS-HFA-OFN-CVE : ' WS-HFA-OFN-CVE                   
        // *   DISPLAY 'WS-CNA-GER-ZON : ' WS-CNA-GER-ZON-ENV               
        // *   DISPLAY 'WS-HFA-CNA-GER : ' WS-HFA-CNA-GER-ZON-ENV           
        // *   DISPLAY 'WS-CNA-DSG-CVE : ' WS-CNA-DSG-CVE                   
        // *   DISPLAY 'WS-HFA-DSG-CVE : ' WS-HFA-DSG-CVE                   
        // *   DISPLAY 'WS-GRAL-DMA-INT    : ' WS-GRAL-DMA-INT              
        // *   DISPLAY 'WS-HFA-DMA-INT     : ' WS-HFA-DMA-INT               
        // *   DISPLAY 'WS-GRAL-DMA-COL    : ' WS-GRAL-DMA-COL              
        // *   DISPLAY 'WS-HFA-DMA-COL     : ' WS-HFA-DMA-COL               
        // *   DISPLAY 'WS-GRAL-MND-NOM    : ' WS-GRAL-MND-NOM              
        // *   DISPLAY 'WS-HFA-MND-NOM     : ' WS-HFA-MND-NOM               
        // *   DISPLAY 'WS-GRAL-EDO-NOM    : ' WS-GRAL-EDO-NOM              
        // *   DISPLAY 'WS-HFA-EDO-NOM     : ' WS-HFA-EDO-NOM               
        // *   DISPLAY 'WS-GRAL-PAI-PAI-NOM: ' WS-GRAL-PAI-PAI-NOM          
        // *   DISPLAY 'WS-HFA-PAI-NOM     : ' WS-HFA-PAI-NOM               
        // *   DISPLAY 'WS-GRAL-DMA-CPO-CVE: ' WS-GRAL-DMA-CPO-CVE          
        // *   DISPLAY 'WS-HFA-CPO-CVE     : ' WS-HFA-CPO-CVE               
        // *   DISPLAY 'WS-CNA-MTE-CVE     : ' WS-CNA-MTE-CVE               
        // *   DISPLAY 'WS-HFA-MTE-CVE     : ' WS-HFA-MTE-CVE               
        // *   DISPLAY 'WS-CNA-CTB-NUM-CTA : ' WS-CNA-CTB-NUM-CTA           
        // *   DISPLAY 'WS-HFA-CTB-NUM-CTA : ' WS-HFA-CTB-NUM-CTA           
        // *   DISPLAY 'WS-IND-EXC-AGT     : ' WS-IND-EXC-AGT               
        // *   DISPLAY 'WS-HFA-IND-EXC     : ' WS-HFA-IND-EXC               
        // *   DISPLAY 'WS-IND-PAG         : ' WS-IND-PAG                   
        // *   DISPLAY 'WS-HFA-IND-PAG     : ' WS-HFA-IND-PAG               
        if (semSwiches.isSwSiCambiarStAgtVig()) {
            wsCamposTrabajo.setWsGralEsaCve(w000_1);
        }
        //        WS-GRAL-AGT-NOM-CONCATENADO NOT EQUAL                     
        //                                      TO WS-HFA-NOM-EMI  OR       
        if (!wsCamposTrabajo.getWsGralEsaCve().isEqual(wsCamposTrabajo.getWsHfaEsaCve()) 
            || !wsCamposTrabajo.getWsGralPefCve().isEqual(wsCamposTrabajo.getWsHfaPefCve()) 
            || !wsCamposTrabajo.getWsCnaOfnCve().isEqual(wsCamposTrabajo.getWsHfaOfnCve()) 
            || !wsCamposTrabajo.getWsCnaGerZonEnv().isEqual(wsCamposTrabajo.getWsHfaCnaGerZonEnv()) 
            || !wsCamposTrabajo.getWsCnaDsgCve().isEqual(wsCamposTrabajo.getWsHfaDsgCve()) 
            || !wsCamposTrabajo.getWsGralAgtRfc().isEqual(wsCamposTrabajo.getWsHfaAgtRfc()) 
            || !wsCamposTrabajo.getWsGralDmaCae().isEqual(wsCamposTrabajo.getWsHfaDmaCae()) 
            || !wsCamposTrabajo.getWsGralDmaNum().isEqual(wsCamposTrabajo.getWsHfaDmaNum()) 
            || !wsCamposTrabajo.getWsGralDmaInt().isEqual(wsCamposTrabajo.getWsHfaDmaInt()) 
            || !wsCamposTrabajo.getWsGralDmaCol().isEqual(wsCamposTrabajo.getWsHfaDmaCol()) 
            || !wsCamposTrabajo.getWsGralMndNom().isEqual(wsCamposTrabajo.getWsHfaMndNom()) 
            || !wsCamposTrabajo.getWsGralEdoNom().isEqual(wsCamposTrabajo.getWsHfaEdoNom()) 
            || !wsCamposTrabajo.getWsGralPaiPaiNom().isEqual(wsCamposTrabajo.getWsHfaPaiNom()) 
            || !wsCamposTrabajo.getWsGralDmaCpoCve().isEqual(wsCamposTrabajo.getWsHfaCpoCve()) 
            || !wsCamposTrabajo.getWsCnaMteCve().isEqual(wsCamposTrabajo.getWsHfaMteCve()) 
            || !wsCamposTrabajo.getWsCnaCtbNumCta().isEqual(wsCamposTrabajo.getWsHfaCtbNumCta()) 
            || !Functions.isEqual(wsCamposTrabajo.getWsIndExcAgt(), wsCamposTrabajo.getWsHfaIndExc()) 
            || !Functions.isEqual(wsCamposTrabajo.getWsIndPag(), wsCamposTrabajo.getWsHfaIndPag()) 
            || !wsCamposTrabajo.getWsCnaPjeIva().isEqual(wsCamposTrabajo.getWsHfaPjeIva()) 
            || !wsCamposTrabajo.getWsCnaPjeIsr().isEqual(wsCamposTrabajo.getWsHfaPjeIsr()) 
            || !wlParamsSal.getWlRegfis().isEqual(wsCamposTrabajo.getWsHfaCdregfis()) 
            || !wlParamsSal.getWlNombre().isEqual(wsCamposTrabajo.getWsHfaNomrznso()) 
            || !wsCamposTrabajo.getWsPjeCedNovi().isEqual(wsCamposTrabajo.getWsHfaCedNovi()) 
            || !wsCamposTrabajo.getWsPjeCedVida().isEqual(wsCamposTrabajo.getWsHfaCedVida())) {
            semSwiches.setSwSiCampoDifer();
        }
        if (wsCamposTrabajo.getWsGralPefCve().toInt() == 1) {
            if (!wsCamposTrabajo.getWsGralAgtNomConcatenado().isEqual(wsCamposTrabajo.getWsHfaNomEmi())) {
                semSwiches.setSwSiCampoDifer();
            }
        } else if (!wsCamposTrabajo.getWsGralAgtRazSoc().isEqual(wsCamposTrabajo.getWsHfaNomEmi())) {
            semSwiches.setSwSiCampoDifer();
        }
    }

    /**
     *  Section: 630-MUEVE-VALOR-INSERT
     *  Paragraph: unnamedProcedure.
     *
     * MUEVE VALORES PARA INSERTAR EN HFA0
     */
    void p630MueveValorInsert() {
        //     DISPLAY ' **** INSERTA **** '                                
        lhfaLinkReg.getLhfaSalida().initialize();
        if (semSwiches.isSwSiCambiarStAgtVig()) {
            wsCamposTrabajo.setWsGralEsaCve(w000_1);
        }
        lhfaLinkReg.setLhfaAgtIdr(wsCamposTrabajo.getWsAgtIdr());
        lhfaLinkReg.setLhfaEsaCve(wsCamposTrabajo.getWsGralEsaCve());
        lhfaLinkReg.setLhfaPefCve(wsCamposTrabajo.getWsGralPefCve());
        lhfaLinkReg.setLhfaOfnCve(wsCamposTrabajo.getWsCnaOfnCve());
        lhfaLinkReg.setLhfaCnaGerZonEnv(wsCamposTrabajo.getWsCnaGerZonEnv());
        lhfaLinkReg.setLhfaDsgCve(wsCamposTrabajo.getWsCnaDsgCve());
        lhfaLinkReg.setLhfaAgtRfc(wsCamposTrabajo.getWsGralAgtRfc());
        if (wsCamposTrabajo.getWsGralPefCve().toInt() == 1) {
            lhfaLinkReg.setLhfaNomEmi(wsCamposTrabajo.getWsGralAgtNomConcatenado());
        } else {
            lhfaLinkReg.setLhfaNomEmi(wsCamposTrabajo.getWsAgtRazSoc());
        }
        //     DISPLAY 'WS-AGT-RAZ-SOC     : ' WS-AGT-RAZ-SOC               
        lhfaLinkReg.setLhfaDmaCae(wsCamposTrabajo.getWsGralDmaCae());
        lhfaLinkReg.setLhfaDmaNum(wsCamposTrabajo.getWsGralDmaNum());
        lhfaLinkReg.setLhfaDmaInt(wsCamposTrabajo.getWsGralDmaInt());
        lhfaLinkReg.setLhfaDmaCol(wsCamposTrabajo.getWsGralDmaCol());
        lhfaLinkReg.setLhfaMndNom(wsCamposTrabajo.getWsGralMndNom());
        lhfaLinkReg.setLhfaEdoNom(wsCamposTrabajo.getWsGralEdoNom());
        lhfaLinkReg.setLhfaPaiNom(wsCamposTrabajo.getWsGralPaiPaiNom());
        lhfaLinkReg.setLhfaCpoCve(wsCamposTrabajo.getWsGralDmaCpoCve());
        lhfaLinkReg.setLhfaMteCve(wsCamposTrabajo.getWsCnaMteCve());
        lhfaLinkReg.setLhfaTfpCve(wsCamposTrabajo.getWsCnaTfpCve());
        lhfaLinkReg.setLhfaCtbNumCta(wsCamposTrabajo.getWsCnaCtbNumCta());
        lhfaLinkReg.setLhfaIndPag(wsCamposTrabajo.getWsIndPag());
        lhfaLinkReg.setLhfaPjeIva(wsCamposTrabajo.getWsCnaPjeIva());
        lhfaLinkReg.setLhfaPjeIsr(wsCamposTrabajo.getWsCnaPjeIsr());
        lhfaLinkReg.setLhfaCdregfis(wlParamsSal.getWlRegfis());
        lhfaLinkReg.setLhfaNomrznso(wlParamsSal.getWlNombre());
        lhfaLinkReg.setLhfaPjeCedNv(wsCamposTrabajo.getWsPjeCedNovi());
        lhfaLinkReg.setLhfaPjeCedVi(wsCamposTrabajo.getWsPjeCedVida());
        //  INI                                                             
        regSalisr.getWsSalwfAgtIdr().setValue(lhfaLinkReg.getLhfaAgtIdr());
        regSalisr.setWsSalwfFecIniPdo(lhfaLinkReg.getLhfaFecIniPdo());
        regSalisr.setWsSalwfFecFinPdo(lhfaLinkReg.getLhfaFecFinPdo());
        regSalisr.getWsSalwfEsaCve().setValue(lhfaLinkReg.getLhfaEsaCve());
        regSalisr.getWsSalwfPefCve().setValue(lhfaLinkReg.getLhfaPefCve());
        regSalisr.getWsSalwfOfnCve().setValue(lhfaLinkReg.getLhfaOfnCve());
        regSalisr.getWsSalwfCnaGerZonEnv().setValue(lhfaLinkReg.getLhfaCnaGerZonEnv());
        regSalisr.getWsSalwfDsgCve().setValue(lhfaLinkReg.getLhfaDsgCve());
        regSalisr.setWsSalwfAgtRfc(lhfaLinkReg.getLhfaAgtRfc());
        regSalisr.setWsSalwfNomEmi(lhfaLinkReg.getLhfaNomEmi());
        regSalisr.setWsSalwfDmaCae(lhfaLinkReg.getLhfaDmaCae());
        regSalisr.setWsSalwfDmaNum(lhfaLinkReg.getLhfaDmaNum());
        regSalisr.setWsSalwfDmaInt(lhfaLinkReg.getLhfaDmaInt());
        regSalisr.setWsSalwfDmaCol(lhfaLinkReg.getLhfaDmaCol());
        regSalisr.setWsSalwfMndNom(lhfaLinkReg.getLhfaMndNom());
        regSalisr.setWsSalwfEdoNom(lhfaLinkReg.getLhfaEdoNom());
        regSalisr.setWsSalwfPaiNom(lhfaLinkReg.getLhfaPaiNom());
        regSalisr.getWsSalwfCpoCve().setValue(lhfaLinkReg.getLhfaCpoCve());
        regSalisr.getWsSalwfMteCve().setValue(lhfaLinkReg.getLhfaMteCve());
        regSalisr.getWsSalwfTfpCve().setValue(lhfaLinkReg.getLhfaTfpCve());
        regSalisr.setWsSalwfCtbNumCta(lhfaLinkReg.getLhfaCtbNumCta());
        regSalisr.setWsSalwfIndPag(lhfaLinkReg.getLhfaIndPag());
        regSalisr.setWsSalwfIndExc(lhfaLinkReg.getLhfaIndExc());
        regSalisr.setWsSalwfUsuCve(lhfaLinkReg.getLhfaUsuCve());
        regSalisr.setWsSalwfNomPrg(lhfaLinkReg.getLhfaNomPrg());
        regSalisr.setWsSalwfFecUltAct(lhfaLinkReg.getLhfaFecUltAct());
        regSalisr.setWsSalwfPjeIva(lhfaLinkReg.getLhfaPjeIva());
        regSalisr.setWsSalwfPjeIsr(lhfaLinkReg.getLhfaPjeIsr());
        regSalisr.setWsSalwfCdregfis(lhfaLinkReg.getLhfaCdregfis());
        regSalisr.setWsSalwfNomrznso(lhfaLinkReg.getLhfaNomrznso());
        regSalisr.setWsSalwfCedNvi(lhfaLinkReg.getLhfaPjeCedNv());
        regSalisr.setWsSalwfCedVid(lhfaLinkReg.getLhfaPjeCedVi());
        agentisr.write(regSalisr);
        //  FIN                                                             
        if (semSwiches.isSwSiPrimeraVez()) {
            lhfaLinkReg.setLhfaIndExc(w000N);
        } else {
            lhfaLinkReg.setLhfaIndExc(wsCamposTrabajo.getWsIndExcAgt());
        }
    }

    /**
     *  Section: 640-CALCULA-FECHAS-PROC
     *  Paragraph: unnamedProcedure.
     *
     * CALCULA FECHAS DE PERIODOS PARA EL INSERT
     */
    void p640CalculaFechasProc() {
        if (semSwiches.isSwSiPrimeraVez()) {
            lhfaLinkReg.setLhfaFecIniPdo(w500FechaDb2);
            lhfaLinkReg.setLhfaFecFinPdo(w500FechaDb2);
        } else {
            //    ADICIONA UN DIA A LA ULTIMA FECHA DEL PERIODO SI NO ES        
            //    PRIMERA VEZ                                                   
            wsCamposTrabajo.setWsFecHfa(wsCamposTrabajo.getWsHfaFecFinPdo().toStringValue());

            genericModel = 
                sysdummy1Port.select_1(
                    GenericModel.builder().values(List.of(
                        toHostString(wsCamposTrabajo.getWsFecHfa())))
                    .build());

            if(genericModel.isPresent()) {
                wsCamposTrabajo.setWsFecMas_1(fromHostString(genericModel.get().values().get(0)));
            }
            lhfaLinkReg.setLhfaFecIniPdo(wsCamposTrabajo.getWsFecMas_1());
            //        MOVE WS-FEC-MAS-1       TO LHFA-FEC-FIN-PDO               
            lhfaLinkReg.setLhfaFecFinPdo(w500FechaDb2);
        }
    }

    /**
     *  Section: 650-INSERT-HFA
     *  Paragraph: unnamedProcedure.
     *
     * INSERTA EN LA ENTIDAD HFA0
     */
    void p650InsertHfa() {
        lhfaLinkReg.setLhfaOpcion(w000Insert);
        runProgram("GGQACV00", lhfaLinkReg);
        if (lhfaLinkReg.getLhfaSwErr().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("ERROR EN INSERT - RUTINA GGQACV00");
            abReg.setAbProceso("650-INSERT-HFA");
            ggqa0049cpy.p980Aborta();
        } else {
            a990CifCtl.setA990RegLeiInsHfa(a990CifCtl.getA990RegLeiInsHfa() + 1);
            semSwiches.setSwNoPrimeraVez();
        }
    }

    /**
     *  Section: 660-OBT-FEC-FIN-PRO
     *  Paragraph: unnamedProcedure.
     *
     * OBTIENE FECHA FIN DE PROCESO PARA UPDATE
     */
    void p660ObtFecFinPro() {
        if (wsCamposTrabajo.getWsHfaFecFinPdo().compareTo(w500FechaDb2) <= 0) {
            lhfaLinkReg.setLhfaFecFinPdo(w500FechaDb2);
        } else {
            wsCamposTrabajo.setWsFecHfa(wsCamposTrabajo.getWsHfaFecFinPdo().toStringValue());

            genericModel = 
                sysdummy1Port.select_1(
                    GenericModel.builder().values(List.of(
                        toHostString(wsCamposTrabajo.getWsFecHfa())))
                    .build());

            if(genericModel.isPresent()) {
                wsCamposTrabajo.setWsFecMas_1(fromHostString(genericModel.get().values().get(0)));
            }
            lhfaLinkReg.setLhfaFecFinPdo(wsCamposTrabajo.getWsFecMas_1());
        }
    }

    /**
     *  Section: 670-UPDATE-HFA
     *  Paragraph: unnamedProcedure.
     *
     * ACTUALIZA  LA ENTIDAD HFA0
     */
    void p670UpdateHfa() {
        lhfaLinkReg.setLhfaOpcion(w000Update);
        lhfaLinkReg.setLhfaAgtIdr(wsCamposTrabajo.getWsAgtIdr());
        lhfaLinkReg.setLhfaFecIniPdo(wsCamposTrabajo.getWsHfaFecIniPdo());
        runProgram("GGQACV00", lhfaLinkReg);
        if (lhfaLinkReg.getLhfaSwErr().isEqual("1")) {
            abReg.getAbMensaje().at(1).setValue("ERROR AL ACTUALIZAR - RUTINA GGQACV00");
            abReg.setAbProceso("670-UPDATE-HFA");
            ggqa0049cpy.p980Aborta();
        } else {
            a990CifCtl.setA990RegLeiUpdHfa(a990CifCtl.getA990RegLeiUpdHfa() + 1);
        }
    }

    /**
     *  Section: 999-CLOSE-CURSOR-AGT0
     *  Paragraph: unnamedProcedure.
     *
     * RUTINA PARA CERRAR CURSOR AGT0
     */
    void p999CloseCursorAgt0() {
        wsLinkReg.setLagtOpcion(w000OpcionClose);
        runProgram("GGQAF100", wsLinkReg);
        if (!wsLinkReg.getLagtSwErr().isEqual("0")) {
            abReg.getAbMensaje().at(1).setValue("ERROR CLOSE CURSOR AGT0");
            abReg.setAbProceso("999-CLOSE-CURSOR-AGT0");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 999-ACTUALIZA-CONTROL-HFA0
     *  Paragraph: unnamedProcedure.
     *
     * ACTUALIZAR CONTROL DE LA ENTIDAD HFA0
     */
    void p999ActualizaControlHfa0() {
        lhfaLinkReg.setLhfaFecUltAct(w500FechaDb2);
        lhfaLinkReg.setLhfaNomPrg(w000Prog);
        lhfaLinkReg.setLhfaUsuCve(w000UsuCve);
        runProgram("GGQA2000", lhfaLinkReg.getLhfaFecUltAct());
    }

    /**
     *  Section: 999-TERMINA
     *  Paragraph: unnamedProcedure.
     *
     * COPY GGQA0057.
     * TERMINA PROGRAMA
     */
    void p999Termina() {
        p990CifCtl();
        p991CloseArchivos();
        throw new StopRunException();
    }

    /**
     *  Section: 990-CIF-CTL
     *  Paragraph: unnamedProcedure.
     *
     * CIFRAS CONTROL
     */
    void p990CifCtl() {
        display(w000Prog, "********** CIFRAS DE CONTROL **********");
        display("FECHA PARAMETRO P01              : ", w500FechaDb2);
        a990Cifra.setValue(a990CifCtl.getA990RegLeiAgtA1());
        display("REG LEI ENTIDAD AGT0             (", a990Cifra, ")");
        a990Cifra.setValue(a990CifCtl.getA990RegLeiInsHfa());
        display("REG LEI INSERTADOS EN HFA0       (", a990Cifra, ")");
        a990Cifra.setValue(a990CifCtl.getA990RegLeiUpdHfa());
        display("REG LEI MODIFICADOS EN HFA0      (", a990Cifra, ")");
        display(w000Prog, "********FIN DE CIFRAS CONTROL**********");
        display("                                                 ");
    }

    /**
     *  Section: 991-CLOSE-ARCHIVOS
     *  Paragraph: unnamedProcedure.
     *
     * CIERRA ARCHIVOS
     */
    void p991CloseArchivos() {
        agentisr.close();
        catExclusionAgt.close();
        if (!banderas.getStExcAgt().equals("00")) {
            abReg.getAbCodigo().setValue(banderas.getStExcAgt());
            abReg.setAbProceso("991-CLOSE-ARCHIVOS");
            abReg.getAbMensaje().at(1).setValue("CAT-EXCLUSION-AGT ");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 999-VER-ERR-SQL
     *  Paragraph: unnamedProcedure.
     *
     * EXEC SQL
     * INCLUDE GGQA00OI
     * END-EXEC.
     * AHCPSQLE: RUTINA PARA EL MANEJO DEL SQL ERROR
     * AUTOR: SOFTTEK/CASE/TEN  17/NOV/95
     * TEN 1.50 CAF: 002
     * SE ADICIONAN CON EL PARRAFO 999-VER-SQL-ERR
     * (ES UN COPY FISICO, QUE SE ADICIONA POR EL CONVERTIDOR DE
     * LENGUAJE)
     * REFERENCIAS:                AHCWSQLE
     */
    void p999VerErrSql() {
        ggqa0053cpy.setBdErr("0");
        if (sqlca.getSqlcode() == 100) {
            ggqa0053cpy.setBdErr("1");
        } else if (sqlca.getSqlcode() != 0) {
            p999DespliegaSqlMsg();
            abReg.setAbCodigon(sqlca.getSqlcode());
            abReg.setAbProceso("999-VER-SQL-ERR");
            ggqa0049cpy.p980Aborta();
        }
    }

    /**
     *  Section: 999-DESPLIEGA-SQL-MSG
     *  Paragraph: unnamedProcedure.
     *
     */
    void p999DespliegaSqlMsg() {
        display("ERROR SQL; -- <INICIO DESCRIPCION>: ");
        runProgram("DSNTIAR", sqlca, 
                ggqa00ogcpy.getW999ErrorMensaje(), 
                ggqa00ogcpy.getW999ErrorTextLong());
        ggqa00ogcpy.setW999ContadorLng(1);
        while (!(ggqa00ogcpy.getW999ContadorLng().toInt() > 10)) {
                iError.setValue(ggqa00ogcpy.getW999ContadorLng());
                display("--", ggqa00ogcpy.getW999ErrorText().at(iError));
                ggqa00ogcpy.getW999ContadorLng().add(1);
        }
        display("ERROR SQL; -- <FIN DESCRIPCION>");
    }

    @Autowired
    public void setGfvtcna0Port(IGfvtcna0Port gfvtcna0Port) {
        this.gfvtcna0Port = gfvtcna0Port;
        this.gfvtcna0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdrc0Port(IGfvtdrc0Port gfvtdrc0Port) {
        this.gfvtdrc0Port = gfvtdrc0Port;
        this.gfvtdrc0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagt0Port(IGfvtagt0Port gfvtagt0Port) {
        this.gfvtagt0Port = gfvtagt0Port;
        this.gfvtagt0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setSysdummy1Port(ISysdummy1Port sysdummy1Port) {
        this.sysdummy1Port = sysdummy1Port;
        this.sysdummy1Port.setProgramContext(getProgramContext());
    }
}
