package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: SEM-SWICHES.
 *
 */
// 389      *---------------------------------------------------------------* 
// 390      * DECLARACION DE SEMAFOROS                                        
// 391      *---------------------------------------------------------------* 
@Setter
@Getter
public class SemSwiches { // VStruct
    // Properties
    private String semArchivoOfn = "N";                                     // 05 SEM-ARCHIVO-OFN X
    // Level 88 - Conditional names
    private static final String SW_SI_FIN_ARCH_OFN = "Y";
    private static final String SW_NO_FIN_ARCH_OFN = "N";

    private String semPrimeraVez = "N";                                     // 05 SEM-PRIMERA-VEZ X
    // Level 88 - Conditional names
    private static final String SW_SI_PRIMERA_VEZ = "Y";
    private static final String SW_NO_PRIMERA_VEZ = "N";

    private String semCampoDifer = "N";                                     // 05 SEM-CAMPO-DIFER X
    // Level 88 - Conditional names
    private static final String SW_SI_CAMPO_DIFER = "Y";
    private static final String SW_NO_CAMPO_DIFER = "N";


    public SemSwiches() {
        initialize();
    }



    // Conditionals (88) - SEM-ARCHIVO-OFN
    public boolean isSwSiFinArchOfn() {
        return this.semArchivoOfn.equals(SW_SI_FIN_ARCH_OFN);
    }

    public void setSwSiFinArchOfn() {
        this.semArchivoOfn = SW_SI_FIN_ARCH_OFN;
    }

    public boolean isSwNoFinArchOfn() {
        return this.semArchivoOfn.equals(SW_NO_FIN_ARCH_OFN);
    }

    public void setSwNoFinArchOfn() {
        this.semArchivoOfn = SW_NO_FIN_ARCH_OFN;
    }


    // Conditionals (88) - SEM-PRIMERA-VEZ
    public boolean isSwSiPrimeraVez() {
        return this.semPrimeraVez.equals(SW_SI_PRIMERA_VEZ);
    }

    public void setSwSiPrimeraVez() {
        this.semPrimeraVez = SW_SI_PRIMERA_VEZ;
    }

    public boolean isSwNoPrimeraVez() {
        return this.semPrimeraVez.equals(SW_NO_PRIMERA_VEZ);
    }

    public void setSwNoPrimeraVez() {
        this.semPrimeraVez = SW_NO_PRIMERA_VEZ;
    }


    // Conditionals (88) - SEM-CAMPO-DIFER
    public boolean isSwSiCampoDifer() {
        return this.semCampoDifer.equals(SW_SI_CAMPO_DIFER);
    }

    public void setSwSiCampoDifer() {
        this.semCampoDifer = SW_SI_CAMPO_DIFER;
    }

    public boolean isSwNoCampoDifer() {
        return this.semCampoDifer.equals(SW_NO_CAMPO_DIFER);
    }

    public void setSwNoCampoDifer() {
        this.semCampoDifer = SW_NO_CAMPO_DIFER;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.semArchivoOfn = "N";
        this.semPrimeraVez = "N";
        this.semCampoDifer = "N";
    }

}

