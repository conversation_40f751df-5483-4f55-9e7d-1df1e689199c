package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CALLE-NUM-IN.
 *
 */
// 1261078900*                                                                 07890000
@Setter
@Getter
public class WsCalleNumIn extends DataStruct {
    // Properties
    private AlphanumericVar wsPkCalle = new AlphanumericVar(this, 25);       // 05 WS-PK-CALLE X(25)
    private AlphanumericVar wsPkNum = new AlphanumericVar(this, 5);          // 05 WS-PK-NUM X(05)
    private AlphanumericVar wsPkInt = new AlphanumericVar(this, 5);          // 05 WS-PK-INT X(05)

    public WsCalleNumIn() {
        super();
        initialize();
    }

    public WsCalleNumIn(DataContainer parent) {
        super(parent);
    }

    public WsCalleNumIn(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsPkCalle(final String wsPkCalle) {
        this.wsPkCalle.setValue(wsPkCalle);
    }

    public void setWsPkCalle(final IAlphanumericValue wsPkCalle) {
        this.wsPkCalle.setValue(wsPkCalle);
    }

    public void setWsPkNum(final String wsPkNum) {
        this.wsPkNum.setValue(wsPkNum);
    }

    public void setWsPkNum(final IAlphanumericValue wsPkNum) {
        this.wsPkNum.setValue(wsPkNum);
    }

    public void setWsPkInt(final String wsPkInt) {
        this.wsPkInt.setValue(wsPkInt);
    }

    public void setWsPkInt(final IAlphanumericValue wsPkInt) {
        this.wsPkInt.setValue(wsPkInt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsPkCalle.clear();
        this.wsPkNum.clear();
        this.wsPkInt.clear();
    }

}

