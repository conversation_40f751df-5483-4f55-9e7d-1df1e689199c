/**
 *  JCL Program: XAD0100.
 *  Code generation type: <PERSON><PERSON><PERSON><PERSON>Y<PERSON> GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Xad0100JclProc extends AbstractJclProgram {

    public Xad0100JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("XAD0100", (xad0100) -> {
            xad0100.addParameter("PROC");
            /*//**********************************************************************/
            /*//*                    ***  XAD0100  ***                              **/
            /*//*   APLICACION:  WEB CORPORATIVO  (SEGURIDAD DEL PORTAL)            **/
            /*//*                                                                   **/
            /*//*   PROCESO:     DIARIO                                             **/
            /*//*                                                                   **/
            /*//*   OBJETIVO:    GENERA EXTRACCION DE AGENTES VIGENTES DE LA        **/
            /*//*                FUERZA PRODUCTORA REGULAR Y SUS RESPECTIVOS        **/
            /*//*                CONTRATOS, FOLIOS Y DATOS ADJUNTOS.                **/
            /*//*                                                                   **/
            /*//*   COMENTARIOS:                                                    **/
            /*//*                                                                   **/
            /*//*   CORRE                                                           **/
            /*//*   DESPUES DE : PGAFVD02  ACTUALIZA LA BD IDMS - SAETA             **/
            /*//*   ANTES DE:    NINGUNO                                            **/
            /*//*                                                                   **/
            /*//*   ELABORO: ROSALBA SANDOVAL REYNA               FECHA: 19/DIC/2000**/
            /*//**********************************************************************/
            /*//**********************************************************************/
            /*//*                         * XAD0108 *                               **/
            /*//*                                                                   **/
            /*//*   OBJETIVO:    GENERAR EXTRACCION DE AGENTES VIGENTES DE LA       **/
            /*//*                FUERZA PRODUCTORA REGULAR                          **/
            /*//*                                                                   **/
            /*//*   PASO REINICIABLE                                                **/
            /*//**********************************************************************/
            xad0100.execProgram("XAD0108", "IKJEFT01", (xad0108) -> {
                xad0108.addParameter("DYNAMNBR","20");
                xad0108.addParameter("REGION","0M");
                /*//**/
                /*//*         ARCHIVO PORTAL AGTES.VIGENTES DE LA FZA. PRODUCTORA REGULAR*/
                xad0108.addDataDefinition("GGXA0101", "PFT.EAXA0101",  (ggxa0101) -> {
                    ggxa0101.addParameter("DISP", "(NEW,CATLG,DELETE)");
                    ggxa0101.addParameter("UNIT","FTPPRD");
                    ggxa0101.addParameter("SPACE", "(CYL,(20,20),RLSE)");
                    ggxa0101.addParameter("DCB", "(LRECL=3330,BLKSIZE=23310,RECFM=VB,BUFNO=4,DSORG=PS)");
                });
                /*//*         ARCHIVO CIFRAS CONTROL DE AGTES. VIG. FZA. PROD. REGULAR*/
                xad0108.addDataDefinition("GGXA0202", "PFT.EAXA0201",  (ggxa0202) -> {
                    ggxa0202.addParameter("DISP", "(NEW,CATLG,DELETE)");
                    ggxa0202.addParameter("UNIT","FTPPRD");
                    ggxa0202.addParameter("SPACE", "(CYL,(01,02),RLSE)");
                    ggxa0202.addParameter("DCB", "(LRECL=45,BLKSIZE=23445,RECFM=FB,BUFNO=4,DSORG=PS)");
                });
                /*//**/
                xad0108.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                    systsprt.addParameter("SYSOUT","*");
                });
                xad0108.addDataDefinition("CMPRINT",  (cmprint) -> {
                    cmprint.addParameter("SYSOUT","*");
                });
                xad0108.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                    sysdbout.addParameter("SYSOUT","*");
                });
                xad0108.addDataDefinition("SYSOUT",  (sysout) -> {
                    sysout.addParameter("SYSOUT","*");
                });
                xad0108.addDataDefinition("SYSPRINT",  (sysprint) -> {
                    sysprint.addParameter("SYSOUT","*");
                });
                xad0108.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                    sysudump.addParameter("SYSOUT","D");
                });
                xad0108.addDataDefinition("SYSABOUT",  (sysabout) -> {
                    sysabout.addParameter("SYSOUT","*");
                });
                xad0108.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(XAD0101)",  (systsin) -> {
                    systsin.addParameter("DISP","SHR");
                });
            });
            if (xad0100.getLastReturnCode()==0) {
                /*//**/
                /*//**********************************************************************/
                /*//*                         * XAD0107 *                               **/
                /*//*                                                                   **/
                /*//*   OBJETIVO :   TRANSFERIR EL ARCHIVO PORTAL DE AGENTES VIGENTES   **/
                /*//*                DE LA FUERZA PRODUCTORA REGULAR A UN SERVIDOR NT.  **/
                /*//*                                                                   **/
                /*//*   PASO  REINICIABLE.                                              **/
                /*//**********************************************************************/
                xad0100.execProgram("XAD0107", "FTP", (xad0107) -> {
                    xad0107.addParameter("PARM","(EXIT");
                    xad0107.addParameter("REGION","0M");
                    /*//**/
                    /*//*            PARAMETROS PARA LA TRANSFERENCIA*/
                    xad0107.addDataDefinition("INPUT", "PNCQP.BTCH.CARDS(XAD0102)",  (input) -> {
                        input.addParameter("DISP","SHR");
                    });
                    xad0107.addDataDefinition("OUTPUT",  (output) -> {
                        output.addParameter("SYSOUT","*");
                    });
                    xad0107.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                });
            }
            if (xad0100.getLastReturnCode()==0) {
                /*//**/
                /*//**********************************************************************/
                /*//*                         * XAD0106 *                               **/
                /*//*                                                                   **/
                /*//*   OBJETIVO :   TRANSFERIR EL ARCHIVO PORTAL DE AGENTES VIGENTES   **/
                /*//*                DE LA FUERZA PRODUCTORA REGULAR A UN SERVIDOR      **/
                /*//*                WINTEL (TEMPORALMENTE AL 150.23.1.70)              **/
                /*//*                                                                   **/
                /*//*   PASO  REINICIABLE.                                              **/
                /*//**********************************************************************/
                xad0100.execProgram("XAD0106", "FTP", (xad0106) -> {
                    xad0106.addParameter("PARM","(EXIT");
                    xad0106.addParameter("REGION","0M");
                    /*//**/
                    /*//*            PARAMETROS PARA LA TRANSFERENCIA*/
                    xad0106.addDataDefinition("INPUT", "PNCQP.BTCH.CARDS(XAD0104)",  (input) -> {
                        input.addParameter("DISP","SHR");
                    });
                    xad0106.addDataDefinition("OUTPUT",  (output) -> {
                        output.addParameter("SYSOUT","*");
                    });
                    xad0106.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                });
            }
            if (xad0100.getLastReturnCode()<=0) {
                /*//**/
                /*//**********************************************************************/
                /*//*                         * XAD0105 *                               **/
                /*//*                                                                   **/
                /*//* OBJETIVO: COPIADO DE ARCHIVO DE PASO DE CONNECT DIRECT A ARCHIVO*/
                /*//*           DEFINITIVO.*/
                /*//* REINICIO: SI, DESDE EL INICIO*/
                /*//**********************************************************************/
                xad0100.execProgram("XAD0105", "IDCAMS", (xad0105) -> {
                    xad0105.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*         ARCHIVO PORTAL AGTES.VIGENTES DE LA FZA. PRODUCTORA REGULAR*/
                    xad0105.addDataDefinition("SALIDA", "PFT.EAXA0101.COP",  (salida) -> {
                        salida.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        salida.addParameter("UNIT","TEMPROD");
                        salida.addParameter("SPACE", "(CYL,(20,20),RLSE)");
                        salida.addParameter("DCB", "(LRECL=3330,BLKSIZE=23310,RECFM=VB,BUFNO=4,DSORG=PS)");
                    });
                    xad0105.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    xad0105.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(XAD0108)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (xad0100.getLastReturnCode()<=0) {
                /*//**********************************************************************/
                /*//*                         * XAD0104 *                               **/
                /*//*                                                                   **/
                /*//* OBJETIVO: COPIADO DE ARCHIVO DE PASO DE CONNECT DIRECT A ARCHIVO*/
                /*//*           DEFINITIVO.*/
                /*//* REINICIO: SI, DESDE EL INICIO*/
                /*//**********************************************************************/
                xad0100.execProgram("XAD0104", "IDCAMS", (xad0104) -> {
                    xad0104.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*         ARCHIVO CIFRAS CONTROL DE AGTES. VIG. FZA. PROD. REGULAR*/
                    xad0104.addDataDefinition("SALIDA", "PFT.EAXA0201.COP",  (salida) -> {
                        salida.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        salida.addParameter("UNIT","FTPPRD");
                        salida.addParameter("SPACE", "(CYL,(01,02),RLSE)");
                        salida.addParameter("DCB", "(LRECL=45,BLKSIZE=23445,RECFM=FB,BUFNO=4,DSORG=PS)");
                    });
                    xad0104.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    xad0104.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(XAD0109)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (xad0100.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                         * XAD0103 *                               **/
                /*//*                                                                   **/
                /*//*   OBJETIVO:    OBTIENE RESPALDO DE LOS ARCHIVO UTILIZADOS         **/
                /*//*                DURANTE EL PROCESO                                 **/
                /*//*                                                                   **/
                /*//*   PASO REINICIABLE                                                **/
                /*//**********************************************************************/
                xad0100.execProgram("XAD0103", "COPIADOR", (xad0103) -> {
                    xad0103.addParameter("REGION","0M");
                    /*//**/
                    /*//*            RESPALDO.LOCAL*/
                    xad0103.addDataDefinition("OUTFILE1", "SYSBKW.EACQ0301(+1)",  (outfile1) -> {
                        outfile1.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        outfile1.addParameter("UNIT","DDVTAV");
                        outfile1.addParameter("VOLUME", "(,,,20)");
                        outfile1.addParameter("LABEL", "(1,SL,EXPDT=99000)");
                        outfile1.addParameter("DCB", "(GDG.MODELO,BLKSIZE=32000,BUFNO=4,DSORG=PS)");
                    });
                    /*//*            RESPALDO.FORANEO*/
                    xad0103.addDummyDataDefinition("OUTFILE2",  (outfile2) -> {
                    });
                    /*//*         ARCHIVO PORTAL AGTES.VIGENTES DE LA FZA. PRODUCTORA REGULAR*/
                    xad0103.addDataDefinition("EAXA0101", "PFT.EAXA0101.COP",  (eaxa0101) -> {
                        eaxa0101.addParameter("DISP","SHR");
                    });
                    /*//*         ARCHIVO CIFRAS CONTROL DE AGTES. VIG. FZA. PROD. REGULAR*/
                    xad0103.addDataDefinition("EAXA0201", "PFT.EAXA0201.COP",  (eaxa0201) -> {
                        eaxa0201.addParameter("DISP","SHR");
                    });
                    /*//**/
                    xad0103.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    xad0103.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    xad0103.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","D");
                    });
                    /*//**/
                    xad0103.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(XAD0103)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (xad0100.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * XAD0102 *                               **/
                /*//*                                                                   **/
                /*//*   OBJETIVO :   TRANSFERIR EL ARCHIVO PORTAL DE AGENTES VIGENTES   **/
                /*//*                DE LA FUERZA PRODUCTORA REGULAR A UN SERVIDOR LINUX**/
                /*//*                CON CONNECT DIRECT                                 **/
                /*//*   PASO  REINICIABLE.                                              **/
                /*//**********************************************************************/
                xad0100.execProgram("XAD0102", "DGADBATC", (xad0102) -> {
                    xad0102.addParameter("REGION","0M");
                    xad0102.addDataDefinition("STEPLIB", "SYS5.CDIRECT.V52.SDGALINK",  (steplib) -> {
                        steplib.addParameter("DISP","SHR");
                    });
                    xad0102.addDataDefinition("DMPRINT",  (dmprint) -> {
                        dmprint.addParameter("SYSOUT","*");
                    });
                    xad0102.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    xad0102.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(XAD0105)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (xad0100.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * XAD0101 *                               **/
                /*//*                                                                   **/
                /*//*   OBJETIVO :   TRANSFERIR EL ARCHIVO PORTAL DE AGENTES VIGENTES   **/
                /*//*                DE LA FUERZA PRODUCTORA REGULAR A UN SERVIDOR LINUX**/
                /*//*                CON CONNECT DIRECT                                 **/
                /*//*   PASO  REINICIABLE.                                              **/
                /*//**********************************************************************/
                xad0100.execProgram("XAD0101", "DGADBATC", (xad0101) -> {
                    xad0101.addParameter("REGION","0M");
                    xad0101.addDataDefinition("STEPLIB", "SYS5.CDIRECT.V52.SDGALINK",  (steplib) -> {
                        steplib.addParameter("DISP","SHR");
                    });
                    xad0101.addDataDefinition("DMPRINT",  (dmprint) -> {
                        dmprint.addParameter("SYSOUT","*");
                    });
                    xad0101.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    xad0101.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(XAD0106)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
