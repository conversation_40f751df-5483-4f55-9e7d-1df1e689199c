package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.linea;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DET3.
 *
 */
// 78048100*                                                                 04810000
// 79048200*                                                                 04820000
@Setter
@Getter
public class Det3 extends DataStruct {
    // Properties
    private AlphanumericVar filler7 = new AlphanumericVar(this, 10);         // 10 FILLER X(10)
    private AlphanumericVar det12Letrero = new AlphanumericVar(this, 25);    // 10 DET12-LETRERO X(25)
    private NumericEditedVar det12Total = new NumericEditedVar(this, "ZZZZZZ9");             // 10 DET12-TOTAL ZZZZZZ9
    private AlphanumericVar filler8 = new AlphanumericVar(this, 90);         // 10 FILLER X(90)

    public Det3() {
        super();
        initialize();
    }

    public Det3(DataContainer parent) {
        super(parent);
    }

    public Det3(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFiller7(final String filler7) {
        this.filler7.setValue(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.filler7.setValue(filler7);
    }

    public void setDet12Letrero(final String det12Letrero) {
        this.det12Letrero.setValue(det12Letrero);
    }

    public void setDet12Letrero(final IAlphanumericValue det12Letrero) {
        this.det12Letrero.setValue(det12Letrero);
    }

    public void setDet12Total(final String det12Total) {
        this.det12Total.setValue(det12Total);
    }

    public void setDet12Total(final IValue det12Total) {
        this.det12Total.setValue(det12Total);
    }

    public void setFiller8(final String filler8) {
        this.filler8.setValue(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.filler8.setValue(filler8);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.filler7.clear();
        this.det12Letrero.clear();
        this.det12Total.clear();
        this.filler8.clear();
    }

}

