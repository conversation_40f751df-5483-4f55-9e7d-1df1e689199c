package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: SEM-SWICHES.
 *
 */
// 756      *---------------------------------------------------------------* 
// 757      * DECLARACION DE SEMAFOROS                                        
// 758      *---------------------------------------------------------------* 
@Setter
@Getter
public class SemSwiches { // VStruct
    // Properties
    private String semAgt0 = "N";                                           // 05 SEM-AGT0 X
    // Level 88 - Conditional names
    private static final String SW_SI_FIN_AGT0 = "Y";
    private static final String SW_NO_FIN_AGT0 = "N";

    private String semPrimeraVez = "N";                                     // 05 SEM-PRIMERA-VEZ X
    // Level 88 - Conditional names
    private static final String SW_SI_PRIMERA_VEZ = "Y";
    private static final String SW_NO_PRIMERA_VEZ = "N";

    private String semCampoDifer = "N";                                     // 05 SEM-CAMPO-DIFER X
    // Level 88 - Conditional names
    private static final String SW_SI_CAMPO_DIFER = "Y";
    private static final String SW_NO_CAMPO_DIFER = "N";

    private String semCambiarStAgtVig = "N";                                // 05 SEM-CAMBIAR-ST-AGT-VIG X
    // Level 88 - Conditional names
    private static final String SW_NO_CAMBIAR_ST_AGT_VIG = "N";
    private static final String SW_SI_CAMBIAR_ST_AGT_VIG = "Y";


    public SemSwiches() {
        initialize();
    }



    // Conditionals (88) - SEM-AGT0
    public boolean isSwSiFinAgt0() {
        return this.semAgt0.equals(SW_SI_FIN_AGT0);
    }

    public void setSwSiFinAgt0() {
        this.semAgt0 = SW_SI_FIN_AGT0;
    }

    public boolean isSwNoFinAgt0() {
        return this.semAgt0.equals(SW_NO_FIN_AGT0);
    }

    public void setSwNoFinAgt0() {
        this.semAgt0 = SW_NO_FIN_AGT0;
    }


    // Conditionals (88) - SEM-PRIMERA-VEZ
    public boolean isSwSiPrimeraVez() {
        return this.semPrimeraVez.equals(SW_SI_PRIMERA_VEZ);
    }

    public void setSwSiPrimeraVez() {
        this.semPrimeraVez = SW_SI_PRIMERA_VEZ;
    }

    public boolean isSwNoPrimeraVez() {
        return this.semPrimeraVez.equals(SW_NO_PRIMERA_VEZ);
    }

    public void setSwNoPrimeraVez() {
        this.semPrimeraVez = SW_NO_PRIMERA_VEZ;
    }


    // Conditionals (88) - SEM-CAMPO-DIFER
    public boolean isSwSiCampoDifer() {
        return this.semCampoDifer.equals(SW_SI_CAMPO_DIFER);
    }

    public void setSwSiCampoDifer() {
        this.semCampoDifer = SW_SI_CAMPO_DIFER;
    }

    public boolean isSwNoCampoDifer() {
        return this.semCampoDifer.equals(SW_NO_CAMPO_DIFER);
    }

    public void setSwNoCampoDifer() {
        this.semCampoDifer = SW_NO_CAMPO_DIFER;
    }


    // Conditionals (88) - SEM-CAMBIAR-ST-AGT-VIG
    public boolean isSwNoCambiarStAgtVig() {
        return this.semCambiarStAgtVig.equals(SW_NO_CAMBIAR_ST_AGT_VIG);
    }

    public void setSwNoCambiarStAgtVig() {
        this.semCambiarStAgtVig = SW_NO_CAMBIAR_ST_AGT_VIG;
    }

    public boolean isSwSiCambiarStAgtVig() {
        return this.semCambiarStAgtVig.equals(SW_SI_CAMBIAR_ST_AGT_VIG);
    }

    public void setSwSiCambiarStAgtVig() {
        this.semCambiarStAgtVig = SW_SI_CAMBIAR_ST_AGT_VIG;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.semAgt0 = "N";
        this.semPrimeraVez = "N";
        this.semCampoDifer = "N";
        this.semCambiarStAgtVig = "N";
    }

}

