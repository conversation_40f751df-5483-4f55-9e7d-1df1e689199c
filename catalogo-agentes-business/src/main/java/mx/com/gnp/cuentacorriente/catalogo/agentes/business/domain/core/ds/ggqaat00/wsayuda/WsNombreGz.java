package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-NOMBRE-GZ.
 *
 */
@Setter
@Getter
public class WsNombreGz extends DataStruct {
    // Properties
    private AlphanumericVar wsNombreGzk = new AlphanumericVar(this, 24);     // 10 WS-NOMBRE-GZK X(24)
    private AlphanumericVar wsNombreGzNo = new AlphanumericVar(this, 3);     // 10 WS-NOMBRE-GZ-NO X(03)
    private AlphanumericVar wsNombreFil = new AlphanumericVar(this, 8);      // 10 WS-NOMBRE-FIL X(08)

    public WsNombreGz() {
        super();
        initialize();
    }

    public WsNombreGz(DataContainer parent) {
        super(parent);
    }

    public WsNombreGz(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsNombreGzk(final String wsNombreGzk) {
        this.wsNombreGzk.setValue(wsNombreGzk);
    }

    public void setWsNombreGzk(final IAlphanumericValue wsNombreGzk) {
        this.wsNombreGzk.setValue(wsNombreGzk);
    }

    public void setWsNombreGzNo(final String wsNombreGzNo) {
        this.wsNombreGzNo.setValue(wsNombreGzNo);
    }

    public void setWsNombreGzNo(final IAlphanumericValue wsNombreGzNo) {
        this.wsNombreGzNo.setValue(wsNombreGzNo);
    }

    public void setWsNombreFil(final String wsNombreFil) {
        this.wsNombreFil.setValue(wsNombreFil);
    }

    public void setWsNombreFil(final IAlphanumericValue wsNombreFil) {
        this.wsNombreFil.setValue(wsNombreFil);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsNombreGzk.clear();
        this.wsNombreGzNo.clear();
        this.wsNombreFil.clear();
    }

}

