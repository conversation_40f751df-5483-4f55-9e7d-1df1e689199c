package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTCNA0-NULL.
 *
 */
// 541027300*                                                                 02730002
// 542027400*                                                                 02740002
@Setter
@Getter
public class WsGfvtcna0Null { // VStruct
    // Properties
    private int cna01;                                                      // 03 CNA01 S9(4) COMPUTATIONAL
    private int cna02;                                                      // 03 CNA02 S9(4) COMPUTATIONAL
    private int cna03;                                                      // 03 CNA03 S9(4) COMPUTATIONAL
    private int cna04;                                                      // 03 CNA04 S9(4) COMPUTATIONAL
    private int cna05;                                                      // 03 CNA05 S9(4) COMPUTATIONAL
    private int cna06;                                                      // 03 CNA06 S9(4) COMPUTATIONAL
    private int cna07;                                                      // 03 CNA07 S9(4) COMPUTATIONAL
    private int cna08;                                                      // 03 CNA08 S9(4) COMPUTATIONAL
    private int cna09;                                                      // 03 CNA09 S9(4) COMPUTATIONAL
    private int cna10;                                                      // 03 CNA10 S9(4) COMPUTATIONAL
    private int cna11;                                                      // 03 CNA11 S9(4) COMPUTATIONAL
    private int cna12;                                                      // 03 CNA12 S9(4) COMPUTATIONAL
    private int cna13;                                                      // 03 CNA13 S9(4) COMPUTATIONAL
    private int cna14;                                                      // 03 CNA14 S9(4) COMPUTATIONAL
    private int cna15;                                                      // 03 CNA15 S9(4) COMPUTATIONAL
    private int cna16;                                                      // 03 CNA16 S9(4) COMPUTATIONAL
    private int cna17;                                                      // 03 CNA17 S9(4) COMPUTATIONAL
    private int cna18;                                                      // 03 CNA18 S9(4) COMPUTATIONAL
    private int cna19;                                                      // 03 CNA19 S9(4) COMPUTATIONAL
    private int cna20;                                                      // 03 CNA20 S9(4) COMPUTATIONAL
    private int cna21;                                                      // 03 CNA21 S9(4) COMPUTATIONAL
    private int cna22;                                                      // 03 CNA22 S9(4) COMPUTATIONAL
    private int cna23;                                                      // 03 CNA23 S9(4) COMPUTATIONAL
    private int cna24;                                                      // 03 CNA24 S9(4) COMPUTATIONAL
    private int cna25;                                                      // 03 CNA25 S9(4) COMPUTATIONAL
    private int cna26;                                                      // 03 CNA26 S9(4) COMPUTATIONAL
    private int cna27;                                                      // 03 CNA27 S9(4) COMPUTATIONAL
    private int cna28;                                                      // 03 CNA28 S9(4) COMPUTATIONAL
    private int cna29;                                                      // 03 CNA29 S9(4) COMPUTATIONAL
    private int cna30;                                                      // 03 CNA30 S9(4) COMPUTATIONAL
    private int cna31;                                                      // 03 CNA31 S9(4) COMPUTATIONAL
    private int cna32;                                                      // 03 CNA32 S9(4) COMPUTATIONAL
    private int cna33;                                                      // 03 CNA33 S9(4) COMPUTATIONAL
    private int cna34;                                                      // 03 CNA34 S9(4) COMPUTATIONAL
    private int cna35;                                                      // 03 CNA35 S9(4) COMPUTATIONAL
    private int cna36;                                                      // 03 CNA36 S9(4) COMPUTATIONAL

    public WsGfvtcna0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.cna01 = 0;
        this.cna02 = 0;
        this.cna03 = 0;
        this.cna04 = 0;
        this.cna05 = 0;
        this.cna06 = 0;
        this.cna07 = 0;
        this.cna08 = 0;
        this.cna09 = 0;
        this.cna10 = 0;
        this.cna11 = 0;
        this.cna12 = 0;
        this.cna13 = 0;
        this.cna14 = 0;
        this.cna15 = 0;
        this.cna16 = 0;
        this.cna17 = 0;
        this.cna18 = 0;
        this.cna19 = 0;
        this.cna20 = 0;
        this.cna21 = 0;
        this.cna22 = 0;
        this.cna23 = 0;
        this.cna24 = 0;
        this.cna25 = 0;
        this.cna26 = 0;
        this.cna27 = 0;
        this.cna28 = 0;
        this.cna29 = 0;
        this.cna30 = 0;
        this.cna31 = 0;
        this.cna32 = 0;
        this.cna33 = 0;
        this.cna34 = 0;
        this.cna35 = 0;
        this.cna36 = 0;
    }

}

