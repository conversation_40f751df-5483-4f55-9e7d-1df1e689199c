package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.TCna0_1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tcna0_1.TCna0Elem_1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.TFla1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfla1.TFla0Elem;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.TGer0_1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tger0_1.TGer0Elem_1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.TCeo0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tceo0.TCeo0Elem;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.TOfn0_1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tofn0_1.TOfn0Elem_1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.TFin;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfin.TFinElem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-REG-SALIDA.
 *
 */
@Setter
@Getter
public class WsRegSalida extends DataStruct {
    // Properties
    private AlphanumericVar sIdentificador = new AlphanumericVar(this, 4);   // 10 S-IDENTIFICADOR X(04)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private UnsignedNumericVar sAgtAgtIdr = new UnsignedNumericVar(this, 5, 0);              // 10 S-AGT-AGT-IDR 9(05)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private AlphanumericVar sAgtAgtNom = new AlphanumericVar(this, 60);      // 10 S-AGT-AGT-NOM X(60)
    private AlphanumericVar filler3 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private AlphanumericVar sAgtAgtApePat = new AlphanumericVar(this, 40);   // 10 S-AGT-AGT-APE-PAT X(40)
    private AlphanumericVar filler4 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private AlphanumericVar sAgtAgtApeMat = new AlphanumericVar(this, 30);   // 10 S-AGT-AGT-APE-MAT X(30)
    private AlphanumericVar filler5 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private AlphanumericVar sWsIndGerente = new AlphanumericVar(this, 7);    // 10 S-WS-IND-GERENTE X(07)
    private AlphanumericVar filler6 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private AlphanumericVar sCmaCmaDes = new AlphanumericVar(this, 60);      // 10 S-CMA-CMA-DES X(60)
    private AlphanumericVar filler7 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private UnsignedNumericVar sAgtAgtNip = new UnsignedNumericVar(this, 4, 0);              // 10 S-AGT-AGT-NIP 9(04)
    private AlphanumericVar filler8 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private TCna0_1 tCna0_1 = new  TCna0_1(this, 255);                       // 10 T-CNA0-1 [1]
    private TFla1 tFla1 = new  TFla1(this, 84);                              // 10 T-FLA1 [1]
    private TGer0_1 tGer0_1 = new  TGer0_1(this, 255);                       // 10 T-GER0-1 [1]
    private TCeo0 tCeo0 = new  TCeo0(this, 51);                              // 10 T-CEO0 [1]
    private TOfn0_1 tOfn0_1 = new  TOfn0_1(this, 255);                       // 10 T-OFN0-1 [1]
    private TFin tFin = new  TFin(this, 2);                                  // 10 T-FIN [1]
    private AlphanumericVar wsLimitadFin = new AlphanumericVar(this, 3);     // 10 WS-LIMITAD-FIN X(03)

    public WsRegSalida() {
        super();
        initialize();
    }

    public WsRegSalida(DataContainer parent) {
        super(parent);
    }

    public WsRegSalida(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSIdentificador(final String sIdentificador) {
        this.sIdentificador.setValue(sIdentificador);
    }

    public void setSIdentificador(final IAlphanumericValue sIdentificador) {
        this.sIdentificador.setValue(sIdentificador);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setSAgtAgtIdr(final int sAgtAgtIdr) {
        this.sAgtAgtIdr.setValue(sAgtAgtIdr);
    }

    public void setSAgtAgtIdr(final BigDecimal sAgtAgtIdr) {
        this.sAgtAgtIdr.setValue(sAgtAgtIdr);
    }

    public void setSAgtAgtIdr(final INumericValue sAgtAgtIdr) {
        this.sAgtAgtIdr.setValue(sAgtAgtIdr);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setSAgtAgtNom(final String sAgtAgtNom) {
        this.sAgtAgtNom.setValue(sAgtAgtNom);
    }

    public void setSAgtAgtNom(final IAlphanumericValue sAgtAgtNom) {
        this.sAgtAgtNom.setValue(sAgtAgtNom);
    }

    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }

    public void setSAgtAgtApePat(final String sAgtAgtApePat) {
        this.sAgtAgtApePat.setValue(sAgtAgtApePat);
    }

    public void setSAgtAgtApePat(final IAlphanumericValue sAgtAgtApePat) {
        this.sAgtAgtApePat.setValue(sAgtAgtApePat);
    }

    public void setFiller4(final String filler4) {
        this.filler4.setValue(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.filler4.setValue(filler4);
    }

    public void setSAgtAgtApeMat(final String sAgtAgtApeMat) {
        this.sAgtAgtApeMat.setValue(sAgtAgtApeMat);
    }

    public void setSAgtAgtApeMat(final IAlphanumericValue sAgtAgtApeMat) {
        this.sAgtAgtApeMat.setValue(sAgtAgtApeMat);
    }

    public void setFiller5(final String filler5) {
        this.filler5.setValue(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.filler5.setValue(filler5);
    }

    public void setSWsIndGerente(final String sWsIndGerente) {
        this.sWsIndGerente.setValue(sWsIndGerente);
    }

    public void setSWsIndGerente(final IAlphanumericValue sWsIndGerente) {
        this.sWsIndGerente.setValue(sWsIndGerente);
    }

    public void setFiller6(final String filler6) {
        this.filler6.setValue(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.filler6.setValue(filler6);
    }

    public void setSCmaCmaDes(final String sCmaCmaDes) {
        this.sCmaCmaDes.setValue(sCmaCmaDes);
    }

    public void setSCmaCmaDes(final IAlphanumericValue sCmaCmaDes) {
        this.sCmaCmaDes.setValue(sCmaCmaDes);
    }

    public void setFiller7(final String filler7) {
        this.filler7.setValue(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.filler7.setValue(filler7);
    }

    public void setSAgtAgtNip(final int sAgtAgtNip) {
        this.sAgtAgtNip.setValue(sAgtAgtNip);
    }

    public void setSAgtAgtNip(final BigDecimal sAgtAgtNip) {
        this.sAgtAgtNip.setValue(sAgtAgtNip);
    }

    public void setSAgtAgtNip(final INumericValue sAgtAgtNip) {
        this.sAgtAgtNip.setValue(sAgtAgtNip);
    }

    public void setFiller8(final String filler8) {
        this.filler8.setValue(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.filler8.setValue(filler8);
    }

    public TCna0_1 getTCna0_1(int index) {
        return this.tCna0_1.at(index);
    }

    public TFla1 getTFla1(int index) {
        return this.tFla1.at(index);
    }

    public TGer0_1 getTGer0_1(int index) {
        return this.tGer0_1.at(index);
    }

    public TCeo0 getTCeo0(int index) {
        return this.tCeo0.at(index);
    }

    public TOfn0_1 getTOfn0_1(int index) {
        return this.tOfn0_1.at(index);
    }

    public TFin getTFin(int index) {
        return this.tFin.at(index);
    }

    public void setWsLimitadFin(final String wsLimitadFin) {
        this.wsLimitadFin.setValue(wsLimitadFin);
    }

    public void setWsLimitadFin(final IAlphanumericValue wsLimitadFin) {
        this.wsLimitadFin.setValue(wsLimitadFin);
    }


    // Deeper properties

    // Property: tCna0_1.tCna0Elem_1 -> 15 T-CNA0-ELEM-1
    public TCna0Elem_1 getTCna0Elem_1() {
        return this.tCna0_1.getTCna0Elem_1();
    }

    // Property: tCna0_1.tCna0Elem_1.sCnaCnaNumElem -> 20 S-CNA-CNA-NUM-ELEM X(01)
    public AlphanumericVar getSCnaCnaNumElem() {
        return this.tCna0_1.getTCna0Elem_1().getSCnaCnaNumElem();
    }

    public void setSCnaCnaNumElem(final String sCnaCnaNumElem) {
        this.tCna0_1.getTCna0Elem_1().setSCnaCnaNumElem(sCnaCnaNumElem);
    }

    public void setSCnaCnaNumElem(final IAlphanumericValue sCnaCnaNumElem) {
        this.tCna0_1.getTCna0Elem_1().setSCnaCnaNumElem(sCnaCnaNumElem);
    }


    // Deeper properties

    // Property: tFla1.tFla0Elem -> 15 T-FLA0-ELEM
    public TFla0Elem getTFla0Elem() {
        return this.tFla1.getTFla0Elem();
    }

    // Property: tFla1.tFla0Elem.sFlaFlaNum1 -> 20 S-FLA-FLA-NUM1 X(08)
    public AlphanumericVar getSFlaFlaNum1() {
        return this.tFla1.getTFla0Elem().getSFlaFlaNum1();
    }

    public void setSFlaFlaNum1(final String sFlaFlaNum1) {
        this.tFla1.getTFla0Elem().setSFlaFlaNum1(sFlaFlaNum1);
    }

    public void setSFlaFlaNum1(final IAlphanumericValue sFlaFlaNum1) {
        this.tFla1.getTFla0Elem().setSFlaFlaNum1(sFlaFlaNum1);
    }

    // Property: tFla1.tFla0Elem.sFlaLimitad1 -> 20 S-FLA-LIMITAD1 X(01)
    public AlphanumericVar getSFlaLimitad1() {
        return this.tFla1.getTFla0Elem().getSFlaLimitad1();
    }

    public void setSFlaLimitad1(final String sFlaLimitad1) {
        this.tFla1.getTFla0Elem().setSFlaLimitad1(sFlaLimitad1);
    }

    public void setSFlaLimitad1(final IAlphanumericValue sFlaLimitad1) {
        this.tFla1.getTFla0Elem().setSFlaLimitad1(sFlaLimitad1);
    }


    // Deeper properties

    // Property: tGer0_1.tGer0Elem_1 -> 15 T-GER0-ELEM-1
    public TGer0Elem_1 getTGer0Elem_1() {
        return this.tGer0_1.getTGer0Elem_1();
    }

    // Property: tGer0_1.tGer0Elem_1.sCnaGerZonElem -> 20 S-CNA-GER-ZON-ELEM X(01)
    public AlphanumericVar getSCnaGerZonElem() {
        return this.tGer0_1.getTGer0Elem_1().getSCnaGerZonElem();
    }

    public void setSCnaGerZonElem(final String sCnaGerZonElem) {
        this.tGer0_1.getTGer0Elem_1().setSCnaGerZonElem(sCnaGerZonElem);
    }

    public void setSCnaGerZonElem(final IAlphanumericValue sCnaGerZonElem) {
        this.tGer0_1.getTGer0Elem_1().setSCnaGerZonElem(sCnaGerZonElem);
    }


    // Deeper properties

    // Property: tCeo0.tCeo0Elem -> 15 T-CEO0-ELEM
    public TCeo0Elem getTCeo0Elem() {
        return this.tCeo0.getTCeo0Elem();
    }

    // Property: tCeo0.tCeo0Elem.sCeoCeoNom -> 20 S-CEO-CEO-NOM X(30)
    public AlphanumericVar getSCeoCeoNom() {
        return this.tCeo0.getTCeo0Elem().getSCeoCeoNom();
    }

    public void setSCeoCeoNom(final String sCeoCeoNom) {
        this.tCeo0.getTCeo0Elem().setSCeoCeoNom(sCeoCeoNom);
    }

    public void setSCeoCeoNom(final IAlphanumericValue sCeoCeoNom) {
        this.tCeo0.getTCeo0Elem().setSCeoCeoNom(sCeoCeoNom);
    }

    // Property: tCeo0.tCeo0Elem.sCeoLimitad -> 20 S-CEO-LIMITAD X(01)
    public AlphanumericVar getSCeoLimitad() {
        return this.tCeo0.getTCeo0Elem().getSCeoLimitad();
    }

    public void setSCeoLimitad(final String sCeoLimitad) {
        this.tCeo0.getTCeo0Elem().setSCeoLimitad(sCeoLimitad);
    }

    public void setSCeoLimitad(final IAlphanumericValue sCeoLimitad) {
        this.tCeo0.getTCeo0Elem().setSCeoLimitad(sCeoLimitad);
    }


    // Deeper properties

    // Property: tOfn0_1.tOfn0Elem_1 -> 15 T-OFN0-ELEM-1
    public TOfn0Elem_1 getTOfn0Elem_1() {
        return this.tOfn0_1.getTOfn0Elem_1();
    }

    // Property: tOfn0_1.tOfn0Elem_1.sCnaOfnCveElem -> 20 S-CNA-OFN-CVE-ELEM X(01)
    public AlphanumericVar getSCnaOfnCveElem() {
        return this.tOfn0_1.getTOfn0Elem_1().getSCnaOfnCveElem();
    }

    public void setSCnaOfnCveElem(final String sCnaOfnCveElem) {
        this.tOfn0_1.getTOfn0Elem_1().setSCnaOfnCveElem(sCnaOfnCveElem);
    }

    public void setSCnaOfnCveElem(final IAlphanumericValue sCnaOfnCveElem) {
        this.tOfn0_1.getTOfn0Elem_1().setSCnaOfnCveElem(sCnaOfnCveElem);
    }


    // Deeper properties

    // Property: tFin.tFinElem -> 15 T-FIN-ELEM
    public TFinElem getTFinElem() {
        return this.tFin.getTFinElem();
    }

    // Property: tFin.tFinElem.sTablaFin -> 20 S-TABLA-FIN X(09)
    public AlphanumericVar getSTablaFin() {
        return this.tFin.getTFinElem().getSTablaFin();
    }

    public void setSTablaFin(final String sTablaFin) {
        this.tFin.getTFinElem().setSTablaFin(sTablaFin);
    }

    public void setSTablaFin(final IAlphanumericValue sTablaFin) {
        this.tFin.getTFinElem().setSTablaFin(sTablaFin);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sIdentificador.setValue("ALTA");
        this.filler1.setValue("|");
        this.sAgtAgtIdr.setZero();
        this.filler2.setValue("|");
        this.sAgtAgtNom.setValue("");
        this.filler3.setValue("|");
        this.sAgtAgtApePat.setValue("");
        this.filler4.setValue("|");
        this.sAgtAgtApeMat.setValue("");
        this.filler5.setValue("|");
        this.sWsIndGerente.setValue("");
        this.filler6.setValue("|");
        this.sCmaCmaDes.setValue("");
        this.filler7.setValue("|");
        this.sAgtAgtNip.setZero();
        this.filler8.setValue("|");
        this.tCna0_1.initialize();
        this.tFla1.initialize();
        this.tGer0_1.initialize();
        this.tCeo0.initialize();
        this.tOfn0_1.initialize();
        this.tFin.initialize();
        this.wsLimitadFin.clear();
    }

}

