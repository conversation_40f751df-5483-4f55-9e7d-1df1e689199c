package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tceo0.TCeo0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-CEO0.
 *
 */
// 197           10 T-CEO0      OCCURS 1 TO 51                                
@Setter
@Getter
public class TCeo0 extends DataStructArray<TCeo0> {
    // Properties
    private TCeo0Elem tCeo0Elem = new  TCeo0Elem(this);                      // 15 T-CEO0-ELEM

    public TCeo0(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TCeo0(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TCeo0(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TCeo0(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TCeo0 at(int idx) {
        return new TCeo0(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tCeo0Elem.sCeoCeoNom -> 20 S-CEO-CEO-NOM X(30)
    public AlphanumericVar getSCeoCeoNom() {
        return this.tCeo0Elem.getSCeoCeoNom();
    }

    public void setSCeoCeoNom(final String sCeoCeoNom) {
        this.tCeo0Elem.setSCeoCeoNom(sCeoCeoNom);
    }

    public void setSCeoCeoNom(final IAlphanumericValue sCeoCeoNom) {
        this.tCeo0Elem.setSCeoCeoNom(sCeoCeoNom);
    }

    // Property: tCeo0Elem.sCeoLimitad -> 20 S-CEO-LIMITAD X(01)
    public AlphanumericVar getSCeoLimitad() {
        return this.tCeo0Elem.getSCeoLimitad();
    }

    public void setSCeoLimitad(final String sCeoLimitad) {
        this.tCeo0Elem.setSCeoLimitad(sCeoLimitad);
    }

    public void setSCeoLimitad(final IAlphanumericValue sCeoLimitad) {
        this.tCeo0Elem.setSCeoLimitad(sCeoLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tCeo0Elem.initialize();
    }

}

