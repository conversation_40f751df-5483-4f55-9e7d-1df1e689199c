package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CONSTANTS.
 *
 */
// 654032100*----                                                             03210000
// 657      ***                                                               
@Setter
@Getter
public class WsConstants { // VStruct
    // Properties
    private String fsAgth = "00";                                           // 05 FS-AGTH X(02)
    private String fsCnah = "00";                                           // 05 FS-CNAH X(02)
    private String fsDmah = "00";                                           // 05 FS-DMAH X(02)
    private String fsCmah = "00";                                           // 05 FS-CMAH X(02)
    private String fsDrc0 = "00";                                           // 05 FS-DRC0 X(02)
    private String fsIntr = "00";                                           // 05 FS-INTR X(02)
    private String wcUno = "1";                                             // 05 WC-UNO X(01)
    private String wc_2cr = "00";                                           // 05 WC-2CR X(02)

    public WsConstants() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.fsAgth = "00";
        this.fsCnah = "00";
        this.fsDmah = "00";
        this.fsCmah = "00";
        this.fsDrc0 = "00";
        this.fsIntr = "00";
        this.wcUno = "1";
        this.wc_2cr = "00";
    }

}

