package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DRC-PJE-COM5-R.
 *
 */
@Setter
@Getter
public class WsDrcPjeCom5R extends DataStruct {
    // Properties
    private AlphanumericVar rsDrcPjeCom53 = new AlphanumericVar(this, 3);    // 10 RS-DRC-PJE-COM53 X(03)
    private AlphanumericVar rsDrcPjeCom57 = new AlphanumericVar(this, 7);    // 10 RS-DRC-PJE-COM57 X(07)

    public WsDrcPjeCom5R() {
        super();
        initialize();
    }

    public WsDrcPjeCom5R(DataContainer parent) {
        super(parent);
    }

    public WsDrcPjeCom5R(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRsDrcPjeCom53(final String rsDrcPjeCom53) {
        this.rsDrcPjeCom53.setValue(rsDrcPjeCom53);
    }

    public void setRsDrcPjeCom53(final IAlphanumericValue rsDrcPjeCom53) {
        this.rsDrcPjeCom53.setValue(rsDrcPjeCom53);
    }

    public void setRsDrcPjeCom57(final String rsDrcPjeCom57) {
        this.rsDrcPjeCom57.setValue(rsDrcPjeCom57);
    }

    public void setRsDrcPjeCom57(final IAlphanumericValue rsDrcPjeCom57) {
        this.rsDrcPjeCom57.setValue(rsDrcPjeCom57);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rsDrcPjeCom53.clear();
        this.rsDrcPjeCom57.clear();
    }

}

