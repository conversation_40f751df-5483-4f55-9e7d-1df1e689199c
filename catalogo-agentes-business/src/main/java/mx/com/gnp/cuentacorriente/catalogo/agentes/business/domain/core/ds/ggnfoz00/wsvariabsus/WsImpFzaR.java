package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-IMP-FZA-R.
 *
 */
@Setter
@Getter
public class WsImpFzaR extends DataStruct {
    // Properties
    private AlphanumericVar rsImpFza11 = new AlphanumericVar(this, 11);      // 10 RS-IMP-FZA11 X(11)
    private AlphanumericVar rsImpFza2 = new AlphanumericVar(this, 2);        // 10 RS-IMP-FZA2 X(02)

    public WsImpFzaR() {
        super();
        initialize();
    }

    public WsImpFzaR(DataContainer parent) {
        super(parent);
    }

    public WsImpFzaR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRsImpFza11(final String rsImpFza11) {
        this.rsImpFza11.setValue(rsImpFza11);
    }

    public void setRsImpFza11(final IAlphanumericValue rsImpFza11) {
        this.rsImpFza11.setValue(rsImpFza11);
    }

    public void setRsImpFza2(final String rsImpFza2) {
        this.rsImpFza2.setValue(rsImpFza2);
    }

    public void setRsImpFza2(final IAlphanumericValue rsImpFza2) {
        this.rsImpFza2.setValue(rsImpFza2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rsImpFza11.clear();
        this.rsImpFza2.clear();
    }

}

