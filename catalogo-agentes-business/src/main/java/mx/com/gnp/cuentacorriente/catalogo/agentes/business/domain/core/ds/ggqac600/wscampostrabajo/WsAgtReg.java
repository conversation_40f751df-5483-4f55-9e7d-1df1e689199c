package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AGT-REG.
 *
 */
@Setter
@Getter
public class WsAgtReg extends DataStruct {
    // Properties
    private BinaryVar wsAgtIdr = new BinaryVar(this, 9, 0);                  // 10 WS-AGT-IDR S9(9) COMPUTATIONAL
    private AlphanumericVar wsAgtRfc = new AlphanumericVar(this, 13);        // 10 WS-AGT-RFC X(13)
    private AlphanumericVar wsAgtNom = new AlphanumericVar(this, 40);        // 10 WS-AGT-NOM X(40)
    private AlphanumericVar wsAgtApePat = new AlphanumericVar(this, 40);     // 10 WS-AGT-APE-PAT X(40)
    private AlphanumericVar wsAgtApeMat = new AlphanumericVar(this, 30);     // 10 WS-AGT-APE-MAT X(30)
    private AlphanumericVar wsAgtFecNac = new AlphanumericVar(this, 10);     // 10 WS-AGT-FEC-NAC X(10)
    private AlphanumericVar wsAgtNumCed = new AlphanumericVar(this, 20);     // 10 WS-AGT-NUM-CED X(20)
    private AlphanumericVar wsAgtFecIniCed = new AlphanumericVar(this, 10);  // 10 WS-AGT-FEC-INI-CED X(10)
    private AlphanumericVar wsAgtFecFinCed = new AlphanumericVar(this, 10);  // 10 WS-AGT-FEC-FIN-CED X(10)
    private AlphanumericVar wsAgtExpSeg = new AlphanumericVar(this, 1);      // 10 WS-AGT-EXP-SEG X(01)
    private AlphanumericVar wsAgtTraEsp = new AlphanumericVar(this, 1);      // 10 WS-AGT-TRA-ESP X(01)
    private BinaryVar wsAgtClfLmr = new BinaryVar(this, 4, 0);               // 10 WS-AGT-CLF-LMR S9(4) COMPUTATIONAL
    private AlphanumericVar wsFecUltAct = new AlphanumericVar(this, 10);     // 10 WS-FEC-ULT-ACT X(10)
    private AlphanumericVar wsAgtRazSoc = new AlphanumericVar(this, 60);     // 10 WS-AGT-RAZ-SOC X(60)
    private AlphanumericVar wsUsuCveAct = new AlphanumericVar(this, 8);      // 10 WS-USU-CVE-ACT X(08)
    private AlphanumericVar wsNalCve = new AlphanumericVar(this, 3);         // 10 WS-NAL-CVE X(03)
    private BinaryVar wsPefCve = new BinaryVar(this, 4, 0);                  // 10 WS-PEF-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar wsTcaCve = new AlphanumericVar(this, 2);         // 10 WS-TCA-CVE X(02)
    private BinaryVar wsMteCve = new BinaryVar(this, 4, 0);                  // 10 WS-MTE-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsAgtNumFolInv = new BinaryVar(this, 9, 0);            // 10 WS-AGT-NUM-FOL-INV S9(9) COMPUTATIONAL
    private AlphanumericVar wsAgtNomExi = new AlphanumericVar(this, 40);     // 10 WS-AGT-NOM-EXI X(40)
    private AlphanumericVar wsAgtRstInv = new AlphanumericVar(this, 5);      // 10 WS-AGT-RST-INV X(05)
    private AlphanumericVar wsAgtFecIniInv = new AlphanumericVar(this, 10);  // 10 WS-AGT-FEC-INI-INV X(10)
    private AlphanumericVar wsAgtFecFinInv = new AlphanumericVar(this, 10);  // 10 WS-AGT-FEC-FIN-INV X(10)
    private AlphanumericVar wsAgtNomDphInv = new AlphanumericVar(this, 40);  // 10 WS-AGT-NOM-DPH-INV X(40)
    private AlphanumericVar wsAgtFecReh = new AlphanumericVar(this, 10);     // 10 WS-AGT-FEC-REH X(10)
    private AlphanumericVar wsAgtFecMotEss = new AlphanumericVar(this, 10);  // 10 WS-AGT-FEC-MOT-ESS X(10)
    private AlphanumericVar wsAgtFecMotAsgIn = new AlphanumericVar(this, 10);                // 10 WS-AGT-FEC-MOT-ASG-IN X(10)
    private AlphanumericVar wsAgtObs = new AlphanumericVar(this, 60);        // 10 WS-AGT-OBS X(60)
    private BinaryVar wsAgtNip = new BinaryVar(this, 4, 0);                  // 10 WS-AGT-NIP S9(4) COMPUTATIONAL
    private BinaryVar wsAgtNumFacIso = new BinaryVar(this, 9, 0);            // 10 WS-AGT-NUM-FAC-ISO S9(9) COMPUTATIONAL
    private PackedVar wsAgtImpFacIso = new PackedVar(this, 13, 2);           // 10 WS-AGT-IMP-FAC-ISO S9(11)V9(2) COMPUTATIONAL-3

    public WsAgtReg() {
        super();
        initialize();
    }

    public WsAgtReg(DataContainer parent) {
        super(parent);
    }

    public WsAgtReg(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAgtIdr(final int wsAgtIdr) {
        this.wsAgtIdr.setValue(wsAgtIdr);
    }

    public void setWsAgtIdr(final BigDecimal wsAgtIdr) {
        this.wsAgtIdr.setValue(wsAgtIdr);
    }

    public void setWsAgtIdr(final INumericValue wsAgtIdr) {
        this.wsAgtIdr.setValue(wsAgtIdr);
    }

    public void setWsAgtRfc(final String wsAgtRfc) {
        this.wsAgtRfc.setValue(wsAgtRfc);
    }

    public void setWsAgtRfc(final IAlphanumericValue wsAgtRfc) {
        this.wsAgtRfc.setValue(wsAgtRfc);
    }

    public void setWsAgtNom(final String wsAgtNom) {
        this.wsAgtNom.setValue(wsAgtNom);
    }

    public void setWsAgtNom(final IAlphanumericValue wsAgtNom) {
        this.wsAgtNom.setValue(wsAgtNom);
    }

    public void setWsAgtApePat(final String wsAgtApePat) {
        this.wsAgtApePat.setValue(wsAgtApePat);
    }

    public void setWsAgtApePat(final IAlphanumericValue wsAgtApePat) {
        this.wsAgtApePat.setValue(wsAgtApePat);
    }

    public void setWsAgtApeMat(final String wsAgtApeMat) {
        this.wsAgtApeMat.setValue(wsAgtApeMat);
    }

    public void setWsAgtApeMat(final IAlphanumericValue wsAgtApeMat) {
        this.wsAgtApeMat.setValue(wsAgtApeMat);
    }

    public void setWsAgtFecNac(final String wsAgtFecNac) {
        this.wsAgtFecNac.setValue(wsAgtFecNac);
    }

    public void setWsAgtFecNac(final IAlphanumericValue wsAgtFecNac) {
        this.wsAgtFecNac.setValue(wsAgtFecNac);
    }

    public void setWsAgtNumCed(final String wsAgtNumCed) {
        this.wsAgtNumCed.setValue(wsAgtNumCed);
    }

    public void setWsAgtNumCed(final IAlphanumericValue wsAgtNumCed) {
        this.wsAgtNumCed.setValue(wsAgtNumCed);
    }

    public void setWsAgtFecIniCed(final String wsAgtFecIniCed) {
        this.wsAgtFecIniCed.setValue(wsAgtFecIniCed);
    }

    public void setWsAgtFecIniCed(final IAlphanumericValue wsAgtFecIniCed) {
        this.wsAgtFecIniCed.setValue(wsAgtFecIniCed);
    }

    public void setWsAgtFecFinCed(final String wsAgtFecFinCed) {
        this.wsAgtFecFinCed.setValue(wsAgtFecFinCed);
    }

    public void setWsAgtFecFinCed(final IAlphanumericValue wsAgtFecFinCed) {
        this.wsAgtFecFinCed.setValue(wsAgtFecFinCed);
    }

    public void setWsAgtExpSeg(final String wsAgtExpSeg) {
        this.wsAgtExpSeg.setValue(wsAgtExpSeg);
    }

    public void setWsAgtExpSeg(final IAlphanumericValue wsAgtExpSeg) {
        this.wsAgtExpSeg.setValue(wsAgtExpSeg);
    }

    public void setWsAgtTraEsp(final String wsAgtTraEsp) {
        this.wsAgtTraEsp.setValue(wsAgtTraEsp);
    }

    public void setWsAgtTraEsp(final IAlphanumericValue wsAgtTraEsp) {
        this.wsAgtTraEsp.setValue(wsAgtTraEsp);
    }

    public void setWsAgtClfLmr(final int wsAgtClfLmr) {
        this.wsAgtClfLmr.setValue(wsAgtClfLmr);
    }

    public void setWsAgtClfLmr(final BigDecimal wsAgtClfLmr) {
        this.wsAgtClfLmr.setValue(wsAgtClfLmr);
    }

    public void setWsAgtClfLmr(final INumericValue wsAgtClfLmr) {
        this.wsAgtClfLmr.setValue(wsAgtClfLmr);
    }

    public void setWsFecUltAct(final String wsFecUltAct) {
        this.wsFecUltAct.setValue(wsFecUltAct);
    }

    public void setWsFecUltAct(final IAlphanumericValue wsFecUltAct) {
        this.wsFecUltAct.setValue(wsFecUltAct);
    }

    public void setWsAgtRazSoc(final String wsAgtRazSoc) {
        this.wsAgtRazSoc.setValue(wsAgtRazSoc);
    }

    public void setWsAgtRazSoc(final IAlphanumericValue wsAgtRazSoc) {
        this.wsAgtRazSoc.setValue(wsAgtRazSoc);
    }

    public void setWsUsuCveAct(final String wsUsuCveAct) {
        this.wsUsuCveAct.setValue(wsUsuCveAct);
    }

    public void setWsUsuCveAct(final IAlphanumericValue wsUsuCveAct) {
        this.wsUsuCveAct.setValue(wsUsuCveAct);
    }

    public void setWsNalCve(final String wsNalCve) {
        this.wsNalCve.setValue(wsNalCve);
    }

    public void setWsNalCve(final IAlphanumericValue wsNalCve) {
        this.wsNalCve.setValue(wsNalCve);
    }

    public void setWsPefCve(final int wsPefCve) {
        this.wsPefCve.setValue(wsPefCve);
    }

    public void setWsPefCve(final BigDecimal wsPefCve) {
        this.wsPefCve.setValue(wsPefCve);
    }

    public void setWsPefCve(final INumericValue wsPefCve) {
        this.wsPefCve.setValue(wsPefCve);
    }

    public void setWsTcaCve(final String wsTcaCve) {
        this.wsTcaCve.setValue(wsTcaCve);
    }

    public void setWsTcaCve(final IAlphanumericValue wsTcaCve) {
        this.wsTcaCve.setValue(wsTcaCve);
    }

    public void setWsMteCve(final int wsMteCve) {
        this.wsMteCve.setValue(wsMteCve);
    }

    public void setWsMteCve(final BigDecimal wsMteCve) {
        this.wsMteCve.setValue(wsMteCve);
    }

    public void setWsMteCve(final INumericValue wsMteCve) {
        this.wsMteCve.setValue(wsMteCve);
    }

    public void setWsAgtNumFolInv(final int wsAgtNumFolInv) {
        this.wsAgtNumFolInv.setValue(wsAgtNumFolInv);
    }

    public void setWsAgtNumFolInv(final BigDecimal wsAgtNumFolInv) {
        this.wsAgtNumFolInv.setValue(wsAgtNumFolInv);
    }

    public void setWsAgtNumFolInv(final INumericValue wsAgtNumFolInv) {
        this.wsAgtNumFolInv.setValue(wsAgtNumFolInv);
    }

    public void setWsAgtNomExi(final String wsAgtNomExi) {
        this.wsAgtNomExi.setValue(wsAgtNomExi);
    }

    public void setWsAgtNomExi(final IAlphanumericValue wsAgtNomExi) {
        this.wsAgtNomExi.setValue(wsAgtNomExi);
    }

    public void setWsAgtRstInv(final String wsAgtRstInv) {
        this.wsAgtRstInv.setValue(wsAgtRstInv);
    }

    public void setWsAgtRstInv(final IAlphanumericValue wsAgtRstInv) {
        this.wsAgtRstInv.setValue(wsAgtRstInv);
    }

    public void setWsAgtFecIniInv(final String wsAgtFecIniInv) {
        this.wsAgtFecIniInv.setValue(wsAgtFecIniInv);
    }

    public void setWsAgtFecIniInv(final IAlphanumericValue wsAgtFecIniInv) {
        this.wsAgtFecIniInv.setValue(wsAgtFecIniInv);
    }

    public void setWsAgtFecFinInv(final String wsAgtFecFinInv) {
        this.wsAgtFecFinInv.setValue(wsAgtFecFinInv);
    }

    public void setWsAgtFecFinInv(final IAlphanumericValue wsAgtFecFinInv) {
        this.wsAgtFecFinInv.setValue(wsAgtFecFinInv);
    }

    public void setWsAgtNomDphInv(final String wsAgtNomDphInv) {
        this.wsAgtNomDphInv.setValue(wsAgtNomDphInv);
    }

    public void setWsAgtNomDphInv(final IAlphanumericValue wsAgtNomDphInv) {
        this.wsAgtNomDphInv.setValue(wsAgtNomDphInv);
    }

    public void setWsAgtFecReh(final String wsAgtFecReh) {
        this.wsAgtFecReh.setValue(wsAgtFecReh);
    }

    public void setWsAgtFecReh(final IAlphanumericValue wsAgtFecReh) {
        this.wsAgtFecReh.setValue(wsAgtFecReh);
    }

    public void setWsAgtFecMotEss(final String wsAgtFecMotEss) {
        this.wsAgtFecMotEss.setValue(wsAgtFecMotEss);
    }

    public void setWsAgtFecMotEss(final IAlphanumericValue wsAgtFecMotEss) {
        this.wsAgtFecMotEss.setValue(wsAgtFecMotEss);
    }

    public void setWsAgtFecMotAsgIn(final String wsAgtFecMotAsgIn) {
        this.wsAgtFecMotAsgIn.setValue(wsAgtFecMotAsgIn);
    }

    public void setWsAgtFecMotAsgIn(final IAlphanumericValue wsAgtFecMotAsgIn) {
        this.wsAgtFecMotAsgIn.setValue(wsAgtFecMotAsgIn);
    }

    public void setWsAgtObs(final String wsAgtObs) {
        this.wsAgtObs.setValue(wsAgtObs);
    }

    public void setWsAgtObs(final IAlphanumericValue wsAgtObs) {
        this.wsAgtObs.setValue(wsAgtObs);
    }

    public void setWsAgtNip(final int wsAgtNip) {
        this.wsAgtNip.setValue(wsAgtNip);
    }

    public void setWsAgtNip(final BigDecimal wsAgtNip) {
        this.wsAgtNip.setValue(wsAgtNip);
    }

    public void setWsAgtNip(final INumericValue wsAgtNip) {
        this.wsAgtNip.setValue(wsAgtNip);
    }

    public void setWsAgtNumFacIso(final int wsAgtNumFacIso) {
        this.wsAgtNumFacIso.setValue(wsAgtNumFacIso);
    }

    public void setWsAgtNumFacIso(final BigDecimal wsAgtNumFacIso) {
        this.wsAgtNumFacIso.setValue(wsAgtNumFacIso);
    }

    public void setWsAgtNumFacIso(final INumericValue wsAgtNumFacIso) {
        this.wsAgtNumFacIso.setValue(wsAgtNumFacIso);
    }

    public void setWsAgtImpFacIso(final BigDecimal wsAgtImpFacIso) {
        this.wsAgtImpFacIso.setValue(wsAgtImpFacIso);
    }

    public void setWsAgtImpFacIso(final int wsAgtImpFacIso) {
        this.wsAgtImpFacIso.setValue(wsAgtImpFacIso);
    }

    public void setWsAgtImpFacIso(final INumericValue wsAgtImpFacIso) {
        this.wsAgtImpFacIso.setValue(wsAgtImpFacIso);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAgtIdr.clear();
        this.wsAgtRfc.clear();
        this.wsAgtNom.clear();
        this.wsAgtApePat.clear();
        this.wsAgtApeMat.clear();
        this.wsAgtFecNac.clear();
        this.wsAgtNumCed.clear();
        this.wsAgtFecIniCed.clear();
        this.wsAgtFecFinCed.clear();
        this.wsAgtExpSeg.clear();
        this.wsAgtTraEsp.clear();
        this.wsAgtClfLmr.clear();
        this.wsFecUltAct.clear();
        this.wsAgtRazSoc.clear();
        this.wsUsuCveAct.clear();
        this.wsNalCve.clear();
        this.wsPefCve.clear();
        this.wsTcaCve.clear();
        this.wsMteCve.clear();
        this.wsAgtNumFolInv.clear();
        this.wsAgtNomExi.clear();
        this.wsAgtRstInv.clear();
        this.wsAgtFecIniInv.clear();
        this.wsAgtFecFinInv.clear();
        this.wsAgtNomDphInv.clear();
        this.wsAgtFecReh.clear();
        this.wsAgtFecMotEss.clear();
        this.wsAgtFecMotAsgIn.clear();
        this.wsAgtObs.clear();
        this.wsAgtNip.clear();
        this.wsAgtNumFacIso.clear();
        this.wsAgtImpFacIso.clear();
    }

}

