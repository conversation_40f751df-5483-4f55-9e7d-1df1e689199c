package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TB-DCLGFVTAGC0.
 *
 */
// 863059500                                                                  05950002
@Setter
@Getter
public class TbDclgfvtagc0 extends DataStruct {
    // Properties
    private AlphanumericVar ttFlaNum = new AlphanumericVar(this, 8);         // 10 TT-FLA-NUM X(8)
    private AlphanumericVar ttAgtApeMat = new AlphanumericVar(this, 30);     // 10 TT-AGT-APE-MAT X(30)
    private AlphanumericVar ttAgc02 = new AlphanumericVar(this, 1);          // 10 TT-AGC02 X(01)
    private AlphanumericVar ttAgtApePat = new AlphanumericVar(this, 40);     // 10 TT-AGT-APE-PAT X(40)
    private AlphanumericVar ttAgc03 = new AlphanumericVar(this, 1);          // 10 TT-AGC03 X(01)
    private AlphanumericVar ttAgtNom = new AlphanumericVar(this, 40);        // 10 TT-AGT-NOM X(40)
    private AlphanumericVar ttAgc04 = new AlphanumericVar(this, 1);          // 10 TT-AGC04 X(01)
    private AlphanumericVar ttAgtRazSoc = new AlphanumericVar(this, 60);     // 10 TT-AGT-RAZ-SOC X(60)
    private AlphanumericVar ttAgc05 = new AlphanumericVar(this, 1);          // 10 TT-AGC05 X(01)
    private AlphanumericVar ttEmpCve = new AlphanumericVar(this, 5);         // 10 TT-EMP-CVE X(5)
    private AlphanumericVar ttAgc06 = new AlphanumericVar(this, 1);          // 10 TT-AGC06 X(01)
    private BinaryVar ttEsaCve = new BinaryVar(this, 4, 0);                  // 10 TT-ESA-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc07 = new AlphanumericVar(this, 1);          // 10 TT-AGC07 X(01)
    private BinaryVar ttOfnCve = new BinaryVar(this, 4, 0);                  // 10 TT-OFN-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc08 = new AlphanumericVar(this, 1);          // 10 TT-AGC08 X(01)
    private BinaryVar ttOrvCve = new BinaryVar(this, 4, 0);                  // 10 TT-ORV-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc09 = new AlphanumericVar(this, 1);          // 10 TT-AGC09 X(01)
    private BinaryVar ttPefCve = new BinaryVar(this, 4, 0);                  // 10 TT-PEF-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc10 = new AlphanumericVar(this, 1);          // 10 TT-AGC10 X(01)
    private AlphanumericVar ttUsuCveAct = new AlphanumericVar(this, 8);      // 10 TT-USU-CVE-ACT X(8)
    private AlphanumericVar ttAgc11 = new AlphanumericVar(this, 1);          // 10 TT-AGC11 X(01)
    private AlphanumericVar ttFecUltAct = new AlphanumericVar(this, 10);     // 10 TT-FEC-ULT-ACT X(10)
    private AlphanumericVar ttAgc12 = new AlphanumericVar(this, 1);          // 10 TT-AGC12 X(01)
    private BinaryVar ttAgtIdr = new BinaryVar(this, 9, 0);                  // 10 TT-AGT-IDR S9(9) COMPUTATIONAL
    private AlphanumericVar ttAgc13 = new AlphanumericVar(this, 1);          // 10 TT-AGC13 X(01)
    private BinaryVar ttCnaNum = new BinaryVar(this, 4, 0);                  // 10 TT-CNA-NUM S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc14 = new AlphanumericVar(this, 1);          // 10 TT-AGC14 X(01)
    private BinaryVar ttTfpCve = new BinaryVar(this, 4, 0);                  // 10 TT-TFP-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc15 = new AlphanumericVar(this, 1);          // 10 TT-AGC15 X(01)
    private AlphanumericVar ttCnaIndGer = new AlphanumericVar(this, 1);      // 10 TT-CNA-IND-GER X(1)
    private AlphanumericVar ttAgc16 = new AlphanumericVar(this, 1);          // 10 TT-AGC16 X(01)
    private BinaryVar ttMteCve = new BinaryVar(this, 4, 0);                  // 10 TT-MTE-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc17 = new AlphanumericVar(this, 1);          // 10 TT-AGC17 X(01)
    private AlphanumericVar ttFlaFecMotEss = new AlphanumericVar(this, 10);  // 10 TT-FLA-FEC-MOT-ESS X(10)
    private AlphanumericVar ttAgc18 = new AlphanumericVar(this, 1);          // 10 TT-AGC18 X(01)
    private AlphanumericVar ttFlaNumGte = new AlphanumericVar(this, 8);      // 10 TT-FLA-NUM-GTE X(8)
    private BinaryVar ttCnaGerZonEnv = new BinaryVar(this, 4, 0);            // 10 TT-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc20 = new AlphanumericVar(this, 1);          // 10 TT-AGC20 X(01)
    private AlphanumericVar ttDrcIdrAgt = new AlphanumericVar(this, 1);      // 10 TT-DRC-IDR-AGT X(1)
    private AlphanumericVar ttAgcxx = new AlphanumericVar(this, 1);          // 10 TT-AGCXX X(01)
    private AlphanumericVar ttDrcIdrGte = new AlphanumericVar(this, 1);      // 10 TT-DRC-IDR-GTE X(1)
    private AlphanumericVar ttAgcyy = new AlphanumericVar(this, 1);          // 10 TT-AGCYY X(01)
    private AlphanumericVar ttFlaDes = new AlphanumericVar(this, 40);        // 10 TT-FLA-DES X(40)
    private AlphanumericVar ttAgc21 = new AlphanumericVar(this, 1);          // 10 TT-AGC21 X(01)
    private AlphanumericVar ttAgtTraEsp = new AlphanumericVar(this, 1);      // 10 TT-AGT-TRA-ESP X(1)
    private AlphanumericVar ttAgc22 = new AlphanumericVar(this, 1);          // 10 TT-AGC22 X(01)
    private AlphanumericVar ttCnaFecCnx = new AlphanumericVar(this, 10);     // 10 TT-CNA-FEC-CNX X(10)
    private AlphanumericVar ttAgc23 = new AlphanumericVar(this, 1);          // 10 TT-AGC23 X(01)
    private BinaryVar ttGnaCve = new BinaryVar(this, 4, 0);                  // 10 TT-GNA-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc24 = new AlphanumericVar(this, 1);          // 10 TT-AGC24 X(01)
    private BinaryVar ttEsaCveFla = new BinaryVar(this, 4, 0);               // 10 TT-ESA-CVE-FLA S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc25 = new AlphanumericVar(this, 1);          // 10 TT-AGC25 X(01)
    private BinaryVar ttFlaGerZon = new BinaryVar(this, 4, 0);               // 10 TT-FLA-GER-ZON S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc26 = new AlphanumericVar(this, 1);          // 10 TT-AGC26 X(01)
    private BinaryVar ttOfnCveCna = new BinaryVar(this, 4, 0);               // 10 TT-OFN-CVE-CNA S9(4) COMPUTATIONAL
    private AlphanumericVar ttAgc27 = new AlphanumericVar(this, 1);          // 10 TT-AGC27 X(01)
    private BinaryVar ttAgtIdrGte = new BinaryVar(this, 9, 0);               // 10 TT-AGT-IDR-GTE S9(9) COMPUTATIONAL
    private AlphanumericVar ttAgc28 = new AlphanumericVar(this, 1);          // 10 TT-AGC28 X(01)

    public TbDclgfvtagc0() {
        super();
        initialize();
    }

    public TbDclgfvtagc0(DataContainer parent) {
        super(parent);
    }

    public TbDclgfvtagc0(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setTtFlaNum(final String ttFlaNum) {
        this.ttFlaNum.setValue(ttFlaNum);
    }

    public void setTtFlaNum(final IAlphanumericValue ttFlaNum) {
        this.ttFlaNum.setValue(ttFlaNum);
    }

    public void setTtAgtApeMat(final String ttAgtApeMat) {
        this.ttAgtApeMat.setValue(ttAgtApeMat);
    }

    public void setTtAgtApeMat(final IAlphanumericValue ttAgtApeMat) {
        this.ttAgtApeMat.setValue(ttAgtApeMat);
    }

    public void setTtAgc02(final String ttAgc02) {
        this.ttAgc02.setValue(ttAgc02);
    }

    public void setTtAgc02(final IAlphanumericValue ttAgc02) {
        this.ttAgc02.setValue(ttAgc02);
    }

    public void setTtAgtApePat(final String ttAgtApePat) {
        this.ttAgtApePat.setValue(ttAgtApePat);
    }

    public void setTtAgtApePat(final IAlphanumericValue ttAgtApePat) {
        this.ttAgtApePat.setValue(ttAgtApePat);
    }

    public void setTtAgc03(final String ttAgc03) {
        this.ttAgc03.setValue(ttAgc03);
    }

    public void setTtAgc03(final IAlphanumericValue ttAgc03) {
        this.ttAgc03.setValue(ttAgc03);
    }

    public void setTtAgtNom(final String ttAgtNom) {
        this.ttAgtNom.setValue(ttAgtNom);
    }

    public void setTtAgtNom(final IAlphanumericValue ttAgtNom) {
        this.ttAgtNom.setValue(ttAgtNom);
    }

    public void setTtAgc04(final String ttAgc04) {
        this.ttAgc04.setValue(ttAgc04);
    }

    public void setTtAgc04(final IAlphanumericValue ttAgc04) {
        this.ttAgc04.setValue(ttAgc04);
    }

    public void setTtAgtRazSoc(final String ttAgtRazSoc) {
        this.ttAgtRazSoc.setValue(ttAgtRazSoc);
    }

    public void setTtAgtRazSoc(final IAlphanumericValue ttAgtRazSoc) {
        this.ttAgtRazSoc.setValue(ttAgtRazSoc);
    }

    public void setTtAgc05(final String ttAgc05) {
        this.ttAgc05.setValue(ttAgc05);
    }

    public void setTtAgc05(final IAlphanumericValue ttAgc05) {
        this.ttAgc05.setValue(ttAgc05);
    }

    public void setTtEmpCve(final String ttEmpCve) {
        this.ttEmpCve.setValue(ttEmpCve);
    }

    public void setTtEmpCve(final IAlphanumericValue ttEmpCve) {
        this.ttEmpCve.setValue(ttEmpCve);
    }

    public void setTtAgc06(final String ttAgc06) {
        this.ttAgc06.setValue(ttAgc06);
    }

    public void setTtAgc06(final IAlphanumericValue ttAgc06) {
        this.ttAgc06.setValue(ttAgc06);
    }

    public void setTtEsaCve(final int ttEsaCve) {
        this.ttEsaCve.setValue(ttEsaCve);
    }

    public void setTtEsaCve(final BigDecimal ttEsaCve) {
        this.ttEsaCve.setValue(ttEsaCve);
    }

    public void setTtEsaCve(final INumericValue ttEsaCve) {
        this.ttEsaCve.setValue(ttEsaCve);
    }

    public void setTtAgc07(final String ttAgc07) {
        this.ttAgc07.setValue(ttAgc07);
    }

    public void setTtAgc07(final IAlphanumericValue ttAgc07) {
        this.ttAgc07.setValue(ttAgc07);
    }

    public void setTtOfnCve(final int ttOfnCve) {
        this.ttOfnCve.setValue(ttOfnCve);
    }

    public void setTtOfnCve(final BigDecimal ttOfnCve) {
        this.ttOfnCve.setValue(ttOfnCve);
    }

    public void setTtOfnCve(final INumericValue ttOfnCve) {
        this.ttOfnCve.setValue(ttOfnCve);
    }

    public void setTtAgc08(final String ttAgc08) {
        this.ttAgc08.setValue(ttAgc08);
    }

    public void setTtAgc08(final IAlphanumericValue ttAgc08) {
        this.ttAgc08.setValue(ttAgc08);
    }

    public void setTtOrvCve(final int ttOrvCve) {
        this.ttOrvCve.setValue(ttOrvCve);
    }

    public void setTtOrvCve(final BigDecimal ttOrvCve) {
        this.ttOrvCve.setValue(ttOrvCve);
    }

    public void setTtOrvCve(final INumericValue ttOrvCve) {
        this.ttOrvCve.setValue(ttOrvCve);
    }

    public void setTtAgc09(final String ttAgc09) {
        this.ttAgc09.setValue(ttAgc09);
    }

    public void setTtAgc09(final IAlphanumericValue ttAgc09) {
        this.ttAgc09.setValue(ttAgc09);
    }

    public void setTtPefCve(final int ttPefCve) {
        this.ttPefCve.setValue(ttPefCve);
    }

    public void setTtPefCve(final BigDecimal ttPefCve) {
        this.ttPefCve.setValue(ttPefCve);
    }

    public void setTtPefCve(final INumericValue ttPefCve) {
        this.ttPefCve.setValue(ttPefCve);
    }

    public void setTtAgc10(final String ttAgc10) {
        this.ttAgc10.setValue(ttAgc10);
    }

    public void setTtAgc10(final IAlphanumericValue ttAgc10) {
        this.ttAgc10.setValue(ttAgc10);
    }

    public void setTtUsuCveAct(final String ttUsuCveAct) {
        this.ttUsuCveAct.setValue(ttUsuCveAct);
    }

    public void setTtUsuCveAct(final IAlphanumericValue ttUsuCveAct) {
        this.ttUsuCveAct.setValue(ttUsuCveAct);
    }

    public void setTtAgc11(final String ttAgc11) {
        this.ttAgc11.setValue(ttAgc11);
    }

    public void setTtAgc11(final IAlphanumericValue ttAgc11) {
        this.ttAgc11.setValue(ttAgc11);
    }

    public void setTtFecUltAct(final String ttFecUltAct) {
        this.ttFecUltAct.setValue(ttFecUltAct);
    }

    public void setTtFecUltAct(final IAlphanumericValue ttFecUltAct) {
        this.ttFecUltAct.setValue(ttFecUltAct);
    }

    public void setTtAgc12(final String ttAgc12) {
        this.ttAgc12.setValue(ttAgc12);
    }

    public void setTtAgc12(final IAlphanumericValue ttAgc12) {
        this.ttAgc12.setValue(ttAgc12);
    }

    public void setTtAgtIdr(final int ttAgtIdr) {
        this.ttAgtIdr.setValue(ttAgtIdr);
    }

    public void setTtAgtIdr(final BigDecimal ttAgtIdr) {
        this.ttAgtIdr.setValue(ttAgtIdr);
    }

    public void setTtAgtIdr(final INumericValue ttAgtIdr) {
        this.ttAgtIdr.setValue(ttAgtIdr);
    }

    public void setTtAgc13(final String ttAgc13) {
        this.ttAgc13.setValue(ttAgc13);
    }

    public void setTtAgc13(final IAlphanumericValue ttAgc13) {
        this.ttAgc13.setValue(ttAgc13);
    }

    public void setTtCnaNum(final int ttCnaNum) {
        this.ttCnaNum.setValue(ttCnaNum);
    }

    public void setTtCnaNum(final BigDecimal ttCnaNum) {
        this.ttCnaNum.setValue(ttCnaNum);
    }

    public void setTtCnaNum(final INumericValue ttCnaNum) {
        this.ttCnaNum.setValue(ttCnaNum);
    }

    public void setTtAgc14(final String ttAgc14) {
        this.ttAgc14.setValue(ttAgc14);
    }

    public void setTtAgc14(final IAlphanumericValue ttAgc14) {
        this.ttAgc14.setValue(ttAgc14);
    }

    public void setTtTfpCve(final int ttTfpCve) {
        this.ttTfpCve.setValue(ttTfpCve);
    }

    public void setTtTfpCve(final BigDecimal ttTfpCve) {
        this.ttTfpCve.setValue(ttTfpCve);
    }

    public void setTtTfpCve(final INumericValue ttTfpCve) {
        this.ttTfpCve.setValue(ttTfpCve);
    }

    public void setTtAgc15(final String ttAgc15) {
        this.ttAgc15.setValue(ttAgc15);
    }

    public void setTtAgc15(final IAlphanumericValue ttAgc15) {
        this.ttAgc15.setValue(ttAgc15);
    }

    public void setTtCnaIndGer(final String ttCnaIndGer) {
        this.ttCnaIndGer.setValue(ttCnaIndGer);
    }

    public void setTtCnaIndGer(final IAlphanumericValue ttCnaIndGer) {
        this.ttCnaIndGer.setValue(ttCnaIndGer);
    }

    public void setTtAgc16(final String ttAgc16) {
        this.ttAgc16.setValue(ttAgc16);
    }

    public void setTtAgc16(final IAlphanumericValue ttAgc16) {
        this.ttAgc16.setValue(ttAgc16);
    }

    public void setTtMteCve(final int ttMteCve) {
        this.ttMteCve.setValue(ttMteCve);
    }

    public void setTtMteCve(final BigDecimal ttMteCve) {
        this.ttMteCve.setValue(ttMteCve);
    }

    public void setTtMteCve(final INumericValue ttMteCve) {
        this.ttMteCve.setValue(ttMteCve);
    }

    public void setTtAgc17(final String ttAgc17) {
        this.ttAgc17.setValue(ttAgc17);
    }

    public void setTtAgc17(final IAlphanumericValue ttAgc17) {
        this.ttAgc17.setValue(ttAgc17);
    }

    public void setTtFlaFecMotEss(final String ttFlaFecMotEss) {
        this.ttFlaFecMotEss.setValue(ttFlaFecMotEss);
    }

    public void setTtFlaFecMotEss(final IAlphanumericValue ttFlaFecMotEss) {
        this.ttFlaFecMotEss.setValue(ttFlaFecMotEss);
    }

    public void setTtAgc18(final String ttAgc18) {
        this.ttAgc18.setValue(ttAgc18);
    }

    public void setTtAgc18(final IAlphanumericValue ttAgc18) {
        this.ttAgc18.setValue(ttAgc18);
    }

    public void setTtFlaNumGte(final String ttFlaNumGte) {
        this.ttFlaNumGte.setValue(ttFlaNumGte);
    }

    public void setTtFlaNumGte(final IAlphanumericValue ttFlaNumGte) {
        this.ttFlaNumGte.setValue(ttFlaNumGte);
    }

    public void setTtCnaGerZonEnv(final int ttCnaGerZonEnv) {
        this.ttCnaGerZonEnv.setValue(ttCnaGerZonEnv);
    }

    public void setTtCnaGerZonEnv(final BigDecimal ttCnaGerZonEnv) {
        this.ttCnaGerZonEnv.setValue(ttCnaGerZonEnv);
    }

    public void setTtCnaGerZonEnv(final INumericValue ttCnaGerZonEnv) {
        this.ttCnaGerZonEnv.setValue(ttCnaGerZonEnv);
    }

    public void setTtAgc20(final String ttAgc20) {
        this.ttAgc20.setValue(ttAgc20);
    }

    public void setTtAgc20(final IAlphanumericValue ttAgc20) {
        this.ttAgc20.setValue(ttAgc20);
    }

    public void setTtDrcIdrAgt(final String ttDrcIdrAgt) {
        this.ttDrcIdrAgt.setValue(ttDrcIdrAgt);
    }

    public void setTtDrcIdrAgt(final IAlphanumericValue ttDrcIdrAgt) {
        this.ttDrcIdrAgt.setValue(ttDrcIdrAgt);
    }

    public void setTtAgcxx(final String ttAgcxx) {
        this.ttAgcxx.setValue(ttAgcxx);
    }

    public void setTtAgcxx(final IAlphanumericValue ttAgcxx) {
        this.ttAgcxx.setValue(ttAgcxx);
    }

    public void setTtDrcIdrGte(final String ttDrcIdrGte) {
        this.ttDrcIdrGte.setValue(ttDrcIdrGte);
    }

    public void setTtDrcIdrGte(final IAlphanumericValue ttDrcIdrGte) {
        this.ttDrcIdrGte.setValue(ttDrcIdrGte);
    }

    public void setTtAgcyy(final String ttAgcyy) {
        this.ttAgcyy.setValue(ttAgcyy);
    }

    public void setTtAgcyy(final IAlphanumericValue ttAgcyy) {
        this.ttAgcyy.setValue(ttAgcyy);
    }

    public void setTtFlaDes(final String ttFlaDes) {
        this.ttFlaDes.setValue(ttFlaDes);
    }

    public void setTtFlaDes(final IAlphanumericValue ttFlaDes) {
        this.ttFlaDes.setValue(ttFlaDes);
    }

    public void setTtAgc21(final String ttAgc21) {
        this.ttAgc21.setValue(ttAgc21);
    }

    public void setTtAgc21(final IAlphanumericValue ttAgc21) {
        this.ttAgc21.setValue(ttAgc21);
    }

    public void setTtAgtTraEsp(final String ttAgtTraEsp) {
        this.ttAgtTraEsp.setValue(ttAgtTraEsp);
    }

    public void setTtAgtTraEsp(final IAlphanumericValue ttAgtTraEsp) {
        this.ttAgtTraEsp.setValue(ttAgtTraEsp);
    }

    public void setTtAgc22(final String ttAgc22) {
        this.ttAgc22.setValue(ttAgc22);
    }

    public void setTtAgc22(final IAlphanumericValue ttAgc22) {
        this.ttAgc22.setValue(ttAgc22);
    }

    public void setTtCnaFecCnx(final String ttCnaFecCnx) {
        this.ttCnaFecCnx.setValue(ttCnaFecCnx);
    }

    public void setTtCnaFecCnx(final IAlphanumericValue ttCnaFecCnx) {
        this.ttCnaFecCnx.setValue(ttCnaFecCnx);
    }

    public void setTtAgc23(final String ttAgc23) {
        this.ttAgc23.setValue(ttAgc23);
    }

    public void setTtAgc23(final IAlphanumericValue ttAgc23) {
        this.ttAgc23.setValue(ttAgc23);
    }

    public void setTtGnaCve(final int ttGnaCve) {
        this.ttGnaCve.setValue(ttGnaCve);
    }

    public void setTtGnaCve(final BigDecimal ttGnaCve) {
        this.ttGnaCve.setValue(ttGnaCve);
    }

    public void setTtGnaCve(final INumericValue ttGnaCve) {
        this.ttGnaCve.setValue(ttGnaCve);
    }

    public void setTtAgc24(final String ttAgc24) {
        this.ttAgc24.setValue(ttAgc24);
    }

    public void setTtAgc24(final IAlphanumericValue ttAgc24) {
        this.ttAgc24.setValue(ttAgc24);
    }

    public void setTtEsaCveFla(final int ttEsaCveFla) {
        this.ttEsaCveFla.setValue(ttEsaCveFla);
    }

    public void setTtEsaCveFla(final BigDecimal ttEsaCveFla) {
        this.ttEsaCveFla.setValue(ttEsaCveFla);
    }

    public void setTtEsaCveFla(final INumericValue ttEsaCveFla) {
        this.ttEsaCveFla.setValue(ttEsaCveFla);
    }

    public void setTtAgc25(final String ttAgc25) {
        this.ttAgc25.setValue(ttAgc25);
    }

    public void setTtAgc25(final IAlphanumericValue ttAgc25) {
        this.ttAgc25.setValue(ttAgc25);
    }

    public void setTtFlaGerZon(final int ttFlaGerZon) {
        this.ttFlaGerZon.setValue(ttFlaGerZon);
    }

    public void setTtFlaGerZon(final BigDecimal ttFlaGerZon) {
        this.ttFlaGerZon.setValue(ttFlaGerZon);
    }

    public void setTtFlaGerZon(final INumericValue ttFlaGerZon) {
        this.ttFlaGerZon.setValue(ttFlaGerZon);
    }

    public void setTtAgc26(final String ttAgc26) {
        this.ttAgc26.setValue(ttAgc26);
    }

    public void setTtAgc26(final IAlphanumericValue ttAgc26) {
        this.ttAgc26.setValue(ttAgc26);
    }

    public void setTtOfnCveCna(final int ttOfnCveCna) {
        this.ttOfnCveCna.setValue(ttOfnCveCna);
    }

    public void setTtOfnCveCna(final BigDecimal ttOfnCveCna) {
        this.ttOfnCveCna.setValue(ttOfnCveCna);
    }

    public void setTtOfnCveCna(final INumericValue ttOfnCveCna) {
        this.ttOfnCveCna.setValue(ttOfnCveCna);
    }

    public void setTtAgc27(final String ttAgc27) {
        this.ttAgc27.setValue(ttAgc27);
    }

    public void setTtAgc27(final IAlphanumericValue ttAgc27) {
        this.ttAgc27.setValue(ttAgc27);
    }

    public void setTtAgtIdrGte(final int ttAgtIdrGte) {
        this.ttAgtIdrGte.setValue(ttAgtIdrGte);
    }

    public void setTtAgtIdrGte(final BigDecimal ttAgtIdrGte) {
        this.ttAgtIdrGte.setValue(ttAgtIdrGte);
    }

    public void setTtAgtIdrGte(final INumericValue ttAgtIdrGte) {
        this.ttAgtIdrGte.setValue(ttAgtIdrGte);
    }

    public void setTtAgc28(final String ttAgc28) {
        this.ttAgc28.setValue(ttAgc28);
    }

    public void setTtAgc28(final IAlphanumericValue ttAgc28) {
        this.ttAgc28.setValue(ttAgc28);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.ttFlaNum.setValue("");
        this.ttAgtApeMat.clear();
        this.ttAgc02.clear();
        this.ttAgtApePat.clear();
        this.ttAgc03.clear();
        this.ttAgtNom.clear();
        this.ttAgc04.clear();
        this.ttAgtRazSoc.clear();
        this.ttAgc05.clear();
        this.ttEmpCve.clear();
        this.ttAgc06.clear();
        this.ttEsaCve.clear();
        this.ttAgc07.clear();
        this.ttOfnCve.clear();
        this.ttAgc08.clear();
        this.ttOrvCve.clear();
        this.ttAgc09.clear();
        this.ttPefCve.clear();
        this.ttAgc10.clear();
        this.ttUsuCveAct.clear();
        this.ttAgc11.clear();
        this.ttFecUltAct.clear();
        this.ttAgc12.clear();
        this.ttAgtIdr.clear();
        this.ttAgc13.clear();
        this.ttCnaNum.clear();
        this.ttAgc14.clear();
        this.ttTfpCve.clear();
        this.ttAgc15.clear();
        this.ttCnaIndGer.clear();
        this.ttAgc16.clear();
        this.ttMteCve.clear();
        this.ttAgc17.clear();
        this.ttFlaFecMotEss.clear();
        this.ttAgc18.clear();
        this.ttFlaNumGte.clear();
        this.ttCnaGerZonEnv.clear();
        this.ttAgc20.clear();
        this.ttDrcIdrAgt.clear();
        this.ttAgcxx.clear();
        this.ttDrcIdrGte.clear();
        this.ttAgcyy.clear();
        this.ttFlaDes.clear();
        this.ttAgc21.clear();
        this.ttAgtTraEsp.clear();
        this.ttAgc22.clear();
        this.ttCnaFecCnx.clear();
        this.ttAgc23.clear();
        this.ttGnaCve.clear();
        this.ttAgc24.clear();
        this.ttEsaCveFla.clear();
        this.ttAgc25.clear();
        this.ttFlaGerZon.clear();
        this.ttAgc26.clear();
        this.ttOfnCveCna.clear();
        this.ttAgc27.clear();
        this.ttAgtIdrGte.clear();
        this.ttAgc28.clear();
    }

}

