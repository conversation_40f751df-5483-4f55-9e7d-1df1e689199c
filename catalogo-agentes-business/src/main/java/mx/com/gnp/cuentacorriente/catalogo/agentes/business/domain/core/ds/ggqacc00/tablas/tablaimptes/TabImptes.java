package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablas.tablaimptes;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TAB-IMPTES.
 *
 */
@Setter
@Getter
public class TabImptes extends DataStructArray<TabImptes> {
    // Properties
    private NumericVar tbPercGravab = new NumericVar(this, 16, 2);           // 05 TB-PERC-GRAVAB S9(14)V99
    private NumericVar tbPercExenta = new NumericVar(this, 16, 2);           // 05 TB-PERC-EXENTA S9(14)V99
    private NumericVar tbIva = new NumericVar(this, 16, 2);                  // 05 TB-IVA S9(14)V99
    private NumericVar tbIsr = new NumericVar(this, 16, 2);                  // 05 TB-ISR S9(14)V99
    private NumericVar tbIvaAcr = new NumericVar(this, 16, 2);               // 05 TB-IVA-ACR S9(14)V99
    private NumericVar tbIvaRtn = new NumericVar(this, 16, 2);               // 05 TB-IVA-RTN S9(14)V99
    private NumericVar tbIsrRet = new NumericVar(this, 16, 2);               // 05 TB-ISR-RET S9(14)V99
    private NumericVar tbIpuCed = new NumericVar(this, 16, 2);               // 05 TB-IPU-CED S9(14)V99

    public TabImptes(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TabImptes(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TabImptes(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TabImptes(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TabImptes at(int idx) {
        return new TabImptes(this, idx, getSizeElement());
    }

    public void setTbPercGravab(final BigDecimal tbPercGravab) {
        this.tbPercGravab.setValue(tbPercGravab);
    }

    public void setTbPercGravab(final int tbPercGravab) {
        this.tbPercGravab.setValue(tbPercGravab);
    }

    public void setTbPercGravab(final INumericValue tbPercGravab) {
        this.tbPercGravab.setValue(tbPercGravab);
    }

    public void setTbPercExenta(final BigDecimal tbPercExenta) {
        this.tbPercExenta.setValue(tbPercExenta);
    }

    public void setTbPercExenta(final int tbPercExenta) {
        this.tbPercExenta.setValue(tbPercExenta);
    }

    public void setTbPercExenta(final INumericValue tbPercExenta) {
        this.tbPercExenta.setValue(tbPercExenta);
    }

    public void setTbIva(final BigDecimal tbIva) {
        this.tbIva.setValue(tbIva);
    }

    public void setTbIva(final int tbIva) {
        this.tbIva.setValue(tbIva);
    }

    public void setTbIva(final INumericValue tbIva) {
        this.tbIva.setValue(tbIva);
    }

    public void setTbIsr(final BigDecimal tbIsr) {
        this.tbIsr.setValue(tbIsr);
    }

    public void setTbIsr(final int tbIsr) {
        this.tbIsr.setValue(tbIsr);
    }

    public void setTbIsr(final INumericValue tbIsr) {
        this.tbIsr.setValue(tbIsr);
    }

    public void setTbIvaAcr(final BigDecimal tbIvaAcr) {
        this.tbIvaAcr.setValue(tbIvaAcr);
    }

    public void setTbIvaAcr(final int tbIvaAcr) {
        this.tbIvaAcr.setValue(tbIvaAcr);
    }

    public void setTbIvaAcr(final INumericValue tbIvaAcr) {
        this.tbIvaAcr.setValue(tbIvaAcr);
    }

    public void setTbIvaRtn(final BigDecimal tbIvaRtn) {
        this.tbIvaRtn.setValue(tbIvaRtn);
    }

    public void setTbIvaRtn(final int tbIvaRtn) {
        this.tbIvaRtn.setValue(tbIvaRtn);
    }

    public void setTbIvaRtn(final INumericValue tbIvaRtn) {
        this.tbIvaRtn.setValue(tbIvaRtn);
    }

    public void setTbIsrRet(final BigDecimal tbIsrRet) {
        this.tbIsrRet.setValue(tbIsrRet);
    }

    public void setTbIsrRet(final int tbIsrRet) {
        this.tbIsrRet.setValue(tbIsrRet);
    }

    public void setTbIsrRet(final INumericValue tbIsrRet) {
        this.tbIsrRet.setValue(tbIsrRet);
    }

    public void setTbIpuCed(final BigDecimal tbIpuCed) {
        this.tbIpuCed.setValue(tbIpuCed);
    }

    public void setTbIpuCed(final int tbIpuCed) {
        this.tbIpuCed.setValue(tbIpuCed);
    }

    public void setTbIpuCed(final INumericValue tbIpuCed) {
        this.tbIpuCed.setValue(tbIpuCed);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbPercGravab.clear();
        this.tbPercExenta.clear();
        this.tbIva.clear();
        this.tbIsr.clear();
        this.tbIvaAcr.clear();
        this.tbIvaRtn.clear();
        this.tbIsrRet.clear();
        this.tbIpuCed.clear();
    }

}

