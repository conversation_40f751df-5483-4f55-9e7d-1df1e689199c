package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaofn0.tofn0;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-OFN0-ELEM.
 *
 */
@Setter
@Getter
public class TOfn0Elem extends DataStruct {
    // Properties
    private AlphanumericVar sCnaOfnCve = new AlphanumericVar(this, 4);       // 20 S-CNA-OFN-CVE X(04)
    private AlphanumericVar sOfnLimitad = new AlphanumericVar(this, 1);      // 20 S-OFN-LIMITAD X(01)

    public TOfn0Elem() {
        super();
        initialize();
    }

    public TOfn0Elem(DataContainer parent) {
        super(parent);
    }

    public TOfn0Elem(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSCnaOfnCve(final String sCnaOfnCve) {
        this.sCnaOfnCve.setValue(sCnaOfnCve);
    }

    public void setSCnaOfnCve(final IAlphanumericValue sCnaOfnCve) {
        this.sCnaOfnCve.setValue(sCnaOfnCve);
    }

    public void setSOfnLimitad(final String sOfnLimitad) {
        this.sOfnLimitad.setValue(sOfnLimitad);
    }

    public void setSOfnLimitad(final IAlphanumericValue sOfnLimitad) {
        this.sOfnLimitad.setValue(sOfnLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sCnaOfnCve.clear();
        this.sOfnLimitad.clear();
    }

}

