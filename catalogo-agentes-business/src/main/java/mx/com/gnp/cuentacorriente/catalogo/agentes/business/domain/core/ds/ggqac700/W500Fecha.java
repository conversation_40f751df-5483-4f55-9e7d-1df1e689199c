package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: W500-FECHA.
 *
 */
@Setter
@Getter
public class W500Fecha extends DataStruct {
    // Properties
    private UnsignedNumericVar w500Ano = new UnsignedNumericVar(this, 4, 0);                 // 05 W500-ANO 9(04)
    private UnsignedNumericVar w500Mes = new UnsignedNumericVar(this, 2, 0);                 // 05 W500-MES 9(02)
    private UnsignedNumericVar w500Dia = new UnsignedNumericVar(this, 2, 0);                 // 05 W500-DIA 9(02)

    public W500Fecha() {
        super();
        initialize();
    }

    public W500Fecha(DataContainer parent) {
        super(parent);
    }

    public W500Fecha(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setW500Ano(final int w500Ano) {
        this.w500Ano.setValue(w500Ano);
    }

    public void setW500Ano(final BigDecimal w500Ano) {
        this.w500Ano.setValue(w500Ano);
    }

    public void setW500Ano(final INumericValue w500Ano) {
        this.w500Ano.setValue(w500Ano);
    }

    public void setW500Mes(final int w500Mes) {
        this.w500Mes.setValue(w500Mes);
    }

    public void setW500Mes(final BigDecimal w500Mes) {
        this.w500Mes.setValue(w500Mes);
    }

    public void setW500Mes(final INumericValue w500Mes) {
        this.w500Mes.setValue(w500Mes);
    }

    public void setW500Dia(final int w500Dia) {
        this.w500Dia.setValue(w500Dia);
    }

    public void setW500Dia(final BigDecimal w500Dia) {
        this.w500Dia.setValue(w500Dia);
    }

    public void setW500Dia(final INumericValue w500Dia) {
        this.w500Dia.setValue(w500Dia);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.w500Ano.clear();
        this.w500Mes.clear();
        this.w500Dia.clear();
    }

}

