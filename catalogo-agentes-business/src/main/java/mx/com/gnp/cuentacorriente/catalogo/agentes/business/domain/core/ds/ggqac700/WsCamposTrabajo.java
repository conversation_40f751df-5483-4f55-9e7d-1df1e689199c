package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.string.StringUtils;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.wscampostrabajo.WsHfoReg;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-CAMPOS-TRABAJO.
 *
 */
// 316      *---                                                              
// 317      *   DECLARACION DE VARIABLES DE TRABAJO                           
// 318      *---                                                              
@Setter
@Getter
public class WsCamposTrabajo { // VStruct
    // Properties
    private AlphanumericVar s999Err = new AlphanumericVar(1);                // 05 S999-ERR X(01) UsedAsParameter
    private String wsSt = "";                                               // 05 WS-ST X(02)
    private String wsArchivo = "";                                          // 05 WS-ARCHIVO X(30)
    private String wsFecMas_1 = "";                                         // 05 WS-FEC-MAS-1 X(10)
    private String wsFecHfo = StringUtils.all("0", 10);                     // 05 WS-FEC-HFO X(10)
    private int wsOfnCveAux = 0;                                            // 05 WS-OFN-CVE-AUX 9(4)
    private WsHfoReg wsHfoReg = new  WsHfoReg();                             // 05 WS-HFO-REG

    public WsCamposTrabajo() {
        initialize();
    }



    public void setS999Err(final String s999Err) {
        this.s999Err.setValue(s999Err);
    }

    public void setS999Err(final IAlphanumericValue s999Err) {
        this.s999Err.setValue(s999Err);
    }


    // Deeper properties

    // Property: wsHfoReg.wsHfoOfnCve -> 10 WS-HFO-OFN-CVE S9(4) COMPUTATIONAL
    public BinaryVar getWsHfoOfnCve() {
        return this.wsHfoReg.getWsHfoOfnCve();
    }

    public void setWsHfoOfnCve(final int wsHfoOfnCve) {
        this.wsHfoReg.setWsHfoOfnCve(wsHfoOfnCve);
    }

    public void setWsHfoOfnCve(final INumericValue wsHfoOfnCve) {
        this.wsHfoReg.setWsHfoOfnCve(wsHfoOfnCve);
    }

    // Property: wsHfoReg.wsHfoFecIniPdo -> 10 WS-HFO-FEC-INI-PDO X(10)
    public AlphanumericVar getWsHfoFecIniPdo() {
        return this.wsHfoReg.getWsHfoFecIniPdo();
    }

    public void setWsHfoFecIniPdo(final String wsHfoFecIniPdo) {
        this.wsHfoReg.setWsHfoFecIniPdo(wsHfoFecIniPdo);
    }

    public void setWsHfoFecIniPdo(final IAlphanumericValue wsHfoFecIniPdo) {
        this.wsHfoReg.setWsHfoFecIniPdo(wsHfoFecIniPdo);
    }

    // Property: wsHfoReg.wsHfoFecFinPdo -> 10 WS-HFO-FEC-FIN-PDO X(10)
    public AlphanumericVar getWsHfoFecFinPdo() {
        return this.wsHfoReg.getWsHfoFecFinPdo();
    }

    public void setWsHfoFecFinPdo(final String wsHfoFecFinPdo) {
        this.wsHfoReg.setWsHfoFecFinPdo(wsHfoFecFinPdo);
    }

    public void setWsHfoFecFinPdo(final IAlphanumericValue wsHfoFecFinPdo) {
        this.wsHfoReg.setWsHfoFecFinPdo(wsHfoFecFinPdo);
    }

    // Property: wsHfoReg.wsHfoRfcRcp -> 10 WS-HFO-RFC-RCP X(13)
    public AlphanumericVar getWsHfoRfcRcp() {
        return this.wsHfoReg.getWsHfoRfcRcp();
    }

    public void setWsHfoRfcRcp(final String wsHfoRfcRcp) {
        this.wsHfoReg.setWsHfoRfcRcp(wsHfoRfcRcp);
    }

    public void setWsHfoRfcRcp(final IAlphanumericValue wsHfoRfcRcp) {
        this.wsHfoReg.setWsHfoRfcRcp(wsHfoRfcRcp);
    }

    // Property: wsHfoReg.wsHfoNomRcp -> 10 WS-HFO-NOM-RCP X(100)
    public AlphanumericVar getWsHfoNomRcp() {
        return this.wsHfoReg.getWsHfoNomRcp();
    }

    public void setWsHfoNomRcp(final String wsHfoNomRcp) {
        this.wsHfoReg.setWsHfoNomRcp(wsHfoNomRcp);
    }

    public void setWsHfoNomRcp(final IAlphanumericValue wsHfoNomRcp) {
        this.wsHfoReg.setWsHfoNomRcp(wsHfoNomRcp);
    }

    // Property: wsHfoReg.wsHfoOfnCae -> 10 WS-HFO-OFN-CAE X(40)
    public AlphanumericVar getWsHfoOfnCae() {
        return this.wsHfoReg.getWsHfoOfnCae();
    }

    public void setWsHfoOfnCae(final String wsHfoOfnCae) {
        this.wsHfoReg.setWsHfoOfnCae(wsHfoOfnCae);
    }

    public void setWsHfoOfnCae(final IAlphanumericValue wsHfoOfnCae) {
        this.wsHfoReg.setWsHfoOfnCae(wsHfoOfnCae);
    }

    // Property: wsHfoReg.wsHfoOfnNum -> 10 WS-HFO-OFN-NUM X(5)
    public AlphanumericVar getWsHfoOfnNum() {
        return this.wsHfoReg.getWsHfoOfnNum();
    }

    public void setWsHfoOfnNum(final String wsHfoOfnNum) {
        this.wsHfoReg.setWsHfoOfnNum(wsHfoOfnNum);
    }

    public void setWsHfoOfnNum(final IAlphanumericValue wsHfoOfnNum) {
        this.wsHfoReg.setWsHfoOfnNum(wsHfoOfnNum);
    }

    // Property: wsHfoReg.wsHfoOfnInt -> 10 WS-HFO-OFN-INT X(5)
    public AlphanumericVar getWsHfoOfnInt() {
        return this.wsHfoReg.getWsHfoOfnInt();
    }

    public void setWsHfoOfnInt(final String wsHfoOfnInt) {
        this.wsHfoReg.setWsHfoOfnInt(wsHfoOfnInt);
    }

    public void setWsHfoOfnInt(final IAlphanumericValue wsHfoOfnInt) {
        this.wsHfoReg.setWsHfoOfnInt(wsHfoOfnInt);
    }

    // Property: wsHfoReg.wsHfoOfnCol -> 10 WS-HFO-OFN-COL X(30)
    public AlphanumericVar getWsHfoOfnCol() {
        return this.wsHfoReg.getWsHfoOfnCol();
    }

    public void setWsHfoOfnCol(final String wsHfoOfnCol) {
        this.wsHfoReg.setWsHfoOfnCol(wsHfoOfnCol);
    }

    public void setWsHfoOfnCol(final IAlphanumericValue wsHfoOfnCol) {
        this.wsHfoReg.setWsHfoOfnCol(wsHfoOfnCol);
    }

    // Property: wsHfoReg.wsHfoMndNom -> 10 WS-HFO-MND-NOM X(50)
    public AlphanumericVar getWsHfoMndNom() {
        return this.wsHfoReg.getWsHfoMndNom();
    }

    public void setWsHfoMndNom(final String wsHfoMndNom) {
        this.wsHfoReg.setWsHfoMndNom(wsHfoMndNom);
    }

    public void setWsHfoMndNom(final IAlphanumericValue wsHfoMndNom) {
        this.wsHfoReg.setWsHfoMndNom(wsHfoMndNom);
    }

    // Property: wsHfoReg.wsHfoEdoNom -> 10 WS-HFO-EDO-NOM X(30)
    public AlphanumericVar getWsHfoEdoNom() {
        return this.wsHfoReg.getWsHfoEdoNom();
    }

    public void setWsHfoEdoNom(final String wsHfoEdoNom) {
        this.wsHfoReg.setWsHfoEdoNom(wsHfoEdoNom);
    }

    public void setWsHfoEdoNom(final IAlphanumericValue wsHfoEdoNom) {
        this.wsHfoReg.setWsHfoEdoNom(wsHfoEdoNom);
    }

    // Property: wsHfoReg.wsHfoPaiNom -> 10 WS-HFO-PAI-NOM X(30)
    public AlphanumericVar getWsHfoPaiNom() {
        return this.wsHfoReg.getWsHfoPaiNom();
    }

    public void setWsHfoPaiNom(final String wsHfoPaiNom) {
        this.wsHfoReg.setWsHfoPaiNom(wsHfoPaiNom);
    }

    public void setWsHfoPaiNom(final IAlphanumericValue wsHfoPaiNom) {
        this.wsHfoReg.setWsHfoPaiNom(wsHfoPaiNom);
    }

    // Property: wsHfoReg.wsHfoCpoCve -> 10 WS-HFO-CPO-CVE S9(9) COMPUTATIONAL
    public BinaryVar getWsHfoCpoCve() {
        return this.wsHfoReg.getWsHfoCpoCve();
    }

    public void setWsHfoCpoCve(final int wsHfoCpoCve) {
        this.wsHfoReg.setWsHfoCpoCve(wsHfoCpoCve);
    }

    public void setWsHfoCpoCve(final INumericValue wsHfoCpoCve) {
        this.wsHfoReg.setWsHfoCpoCve(wsHfoCpoCve);
    }

    // Property: wsHfoReg.wsHfoUsuCve -> 10 WS-HFO-USU-CVE X(8)
    public AlphanumericVar getWsHfoUsuCve() {
        return this.wsHfoReg.getWsHfoUsuCve();
    }

    public void setWsHfoUsuCve(final String wsHfoUsuCve) {
        this.wsHfoReg.setWsHfoUsuCve(wsHfoUsuCve);
    }

    public void setWsHfoUsuCve(final IAlphanumericValue wsHfoUsuCve) {
        this.wsHfoReg.setWsHfoUsuCve(wsHfoUsuCve);
    }

    // Property: wsHfoReg.wsHfoNomPrg -> 10 WS-HFO-NOM-PRG X(8)
    public AlphanumericVar getWsHfoNomPrg() {
        return this.wsHfoReg.getWsHfoNomPrg();
    }

    public void setWsHfoNomPrg(final String wsHfoNomPrg) {
        this.wsHfoReg.setWsHfoNomPrg(wsHfoNomPrg);
    }

    public void setWsHfoNomPrg(final IAlphanumericValue wsHfoNomPrg) {
        this.wsHfoReg.setWsHfoNomPrg(wsHfoNomPrg);
    }

    // Property: wsHfoReg.wsHfoFecUltAct -> 10 WS-HFO-FEC-ULT-ACT X(26)
    public AlphanumericVar getWsHfoFecUltAct() {
        return this.wsHfoReg.getWsHfoFecUltAct();
    }

    public void setWsHfoFecUltAct(final String wsHfoFecUltAct) {
        this.wsHfoReg.setWsHfoFecUltAct(wsHfoFecUltAct);
    }

    public void setWsHfoFecUltAct(final IAlphanumericValue wsHfoFecUltAct) {
        this.wsHfoReg.setWsHfoFecUltAct(wsHfoFecUltAct);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.s999Err.setValue("");
        this.wsSt = "";
        this.wsArchivo = "";
        this.wsFecMas_1 = "";
        this.wsFecHfo = StringUtils.all("0", 10);
        this.wsOfnCveAux = 0;
        this.wsHfoReg.initialize();
    }

}

