package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablager0.tger0;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-GER0-ELEM.
 *
 */
@Setter
@Getter
public class TGer0Elem extends DataStruct {
    // Properties
    private AlphanumericVar sCnaGerZon = new AlphanumericVar(this, 4);       // 20 S-CNA-GER-ZON X(04)
    private AlphanumericVar sGerLimitad = new AlphanumericVar(this, 1);      // 20 S-GER-LIMITAD X(01)

    public TGer0Elem() {
        super();
        initialize();
    }

    public TGer0Elem(DataContainer parent) {
        super(parent);
    }

    public TGer0Elem(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSCnaGerZon(final String sCnaGerZon) {
        this.sCnaGerZon.setValue(sCnaGerZon);
    }

    public void setSCnaGerZon(final IAlphanumericValue sCnaGerZon) {
        this.sCnaGerZon.setValue(sCnaGerZon);
    }

    public void setSGerLimitad(final String sGerLimitad) {
        this.sGerLimitad.setValue(sGerLimitad);
    }

    public void setSGerLimitad(final IAlphanumericValue sGerLimitad) {
        this.sGerLimitad.setValue(sGerLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sCnaGerZon.clear();
        this.sGerLimitad.clear();
    }

}

