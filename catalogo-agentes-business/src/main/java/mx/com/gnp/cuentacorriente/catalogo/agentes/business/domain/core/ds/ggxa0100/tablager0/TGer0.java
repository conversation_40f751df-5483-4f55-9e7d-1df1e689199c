package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablager0;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablager0.tger0.TGer0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-GER0.
 *
 */
// 223           10 T-GER0      OCCURS 1 TO 51                                
@Setter
@Getter
public class TGer0 extends DataStructArray<TGer0> {
    // Properties
    private TGer0Elem tGer0Elem = new  TGer0Elem(this);                      // 15 T-GER0-ELEM UsedAsParameter

    public TGer0(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TGer0(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TGer0(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TGer0(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TGer0 at(int idx) {
        return new TGer0(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tGer0Elem.sCnaGerZon -> 20 S-CNA-GER-ZON X(04)
    public AlphanumericVar getSCnaGerZon() {
        return this.tGer0Elem.getSCnaGerZon();
    }

    public void setSCnaGerZon(final String sCnaGerZon) {
        this.tGer0Elem.setSCnaGerZon(sCnaGerZon);
    }

    public void setSCnaGerZon(final IAlphanumericValue sCnaGerZon) {
        this.tGer0Elem.setSCnaGerZon(sCnaGerZon);
    }

    // Property: tGer0Elem.sGerLimitad -> 20 S-GER-LIMITAD X(01)
    public AlphanumericVar getSGerLimitad() {
        return this.tGer0Elem.getSGerLimitad();
    }

    public void setSGerLimitad(final String sGerLimitad) {
        this.tGer0Elem.setSGerLimitad(sGerLimitad);
    }

    public void setSGerLimitad(final IAlphanumericValue sGerLimitad) {
        this.tGer0Elem.setSGerLimitad(sGerLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tGer0Elem.initialize();
    }

}

