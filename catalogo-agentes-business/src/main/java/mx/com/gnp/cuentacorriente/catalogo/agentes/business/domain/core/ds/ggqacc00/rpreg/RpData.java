package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-DATA.
 *
 */
@Setter
@Getter
public class RpData { // VStruct
    // Properties
    private int rpDsgCve;                                                   // 10 RP-DSG-CVE 9(04)
    private String rpFecPrxFac = "";                                        // 10 RP-FEC-PRX-FAC X(10)
    private int rpEsaCve;                                                   // 10 RP-ESA-CVE 9(04)
    private String rpEssPrx = "";                                           // 10 RP-ESS-PRX X(01)
    private String rpCf5CfbCveDseCmp = "";                                  // 10 RP-CF5-CFB-CVE-DSE-CMP X(04)
    private NumericVar rpCf5ImpCtoDse = new NumericVar(13, 2);               // 10 RP-CF5-IMP-CTO-DSE S9(11)V9(2)
    private NumericVar rpCf5PjeCtoDse = new NumericVar(5, 2);                // 10 RP-CF5-PJE-CTO-DSE S9(03)V9(2)

    public RpData() {
        initialize();
    }



    public void setRpCf5ImpCtoDse(final BigDecimal rpCf5ImpCtoDse) {
        this.rpCf5ImpCtoDse.setValue(rpCf5ImpCtoDse);
    }

    public void setRpCf5ImpCtoDse(final int rpCf5ImpCtoDse) {
        this.rpCf5ImpCtoDse.setValue(rpCf5ImpCtoDse);
    }

    public void setRpCf5ImpCtoDse(final INumericValue rpCf5ImpCtoDse) {
        this.rpCf5ImpCtoDse.setValue(rpCf5ImpCtoDse);
    }

    public void setRpCf5PjeCtoDse(final BigDecimal rpCf5PjeCtoDse) {
        this.rpCf5PjeCtoDse.setValue(rpCf5PjeCtoDse);
    }

    public void setRpCf5PjeCtoDse(final int rpCf5PjeCtoDse) {
        this.rpCf5PjeCtoDse.setValue(rpCf5PjeCtoDse);
    }

    public void setRpCf5PjeCtoDse(final INumericValue rpCf5PjeCtoDse) {
        this.rpCf5PjeCtoDse.setValue(rpCf5PjeCtoDse);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpDsgCve = 0;
        this.rpFecPrxFac = "";
        this.rpEsaCve = 0;
        this.rpEssPrx = "";
        this.rpCf5CfbCveDseCmp = "";
        this.rpCf5ImpCtoDse.clear();
        this.rpCf5PjeCtoDse.clear();
    }

}

