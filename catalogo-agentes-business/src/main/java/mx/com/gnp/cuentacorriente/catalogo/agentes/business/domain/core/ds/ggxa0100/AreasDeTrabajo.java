package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo.Variables;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo.variables.ConIncidencias;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo.Contadores;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: AREAS-DE-TRABAJO.
 *
 */
@Setter
@Getter
public class AreasDeTrabajo { // VStruct
    // Properties
    private int swContratos = 0;                                            // 05 SW-CONTRATOS 9(01)
    private int swFolios = 0;                                               // 05 SW-FOLIOS 9(01)
    private int swGerencias = 0;                                            // 05 SW-GERENCIAS 9(01)
    private int swRegiones = 0;                                             // 05 SW-REGIONES 9(01)
    private int swOficinas = 0;                                             // 05 SW-OFICINAS 9(01)
    private int swFla2 = 1;                                                 // 05 SW-FLA2 9(01)
    private int swFla3 = 1;                                                 // 05 SW-FLA3 9(01)
    private String swEofAgentes = "N";                                      // 05 SW-EOF-AGENTES X(01)
    // Level 88 - Conditional names
    private static final String EOF_AGENTES = "S";

    private String swEofContratos = "N";                                    // 05 SW-EOF-CONTRATOS X(01)
    // Level 88 - Conditional names
    private static final String EOF_CONTRATOS = "S";

    private String swEofFolios = "N";                                       // 05 SW-EOF-FOLIOS X(01)
    // Level 88 - Conditional names
    private static final String EOF_FOLIOS = "S";

    private String swEofGerencias = "N";                                    // 05 SW-EOF-GERENCIAS X(01)
    // Level 88 - Conditional names
    private static final String EOF_GERENCIAS = "S";

    private String swEofRegiones = "N";                                     // 05 SW-EOF-REGIONES X(01)
    // Level 88 - Conditional names
    private static final String EOF_REGIONES = "S";

    private String swEofOficinas = "N";                                     // 05 SW-EOF-OFICINAS X(01)
    // Level 88 - Conditional names
    private static final String EOF_OFICINAS = "S";

    private Variables variables = new  Variables();                          // 05 VARIABLES
    private Contadores contadores = new  Contadores();                       // 05 CONTADORES

    public AreasDeTrabajo() {
        initialize();
    }



    // Conditionals (88) - SW-EOF-AGENTES
    public boolean isEofAgentes() {
        return this.swEofAgentes.equals(EOF_AGENTES);
    }

    public void setEofAgentes() {
        this.swEofAgentes = EOF_AGENTES;
    }


    // Conditionals (88) - SW-EOF-CONTRATOS
    public boolean isEofContratos() {
        return this.swEofContratos.equals(EOF_CONTRATOS);
    }

    public void setEofContratos() {
        this.swEofContratos = EOF_CONTRATOS;
    }


    // Conditionals (88) - SW-EOF-FOLIOS
    public boolean isEofFolios() {
        return this.swEofFolios.equals(EOF_FOLIOS);
    }

    public void setEofFolios() {
        this.swEofFolios = EOF_FOLIOS;
    }


    // Conditionals (88) - SW-EOF-GERENCIAS
    public boolean isEofGerencias() {
        return this.swEofGerencias.equals(EOF_GERENCIAS);
    }

    public void setEofGerencias() {
        this.swEofGerencias = EOF_GERENCIAS;
    }


    // Conditionals (88) - SW-EOF-REGIONES
    public boolean isEofRegiones() {
        return this.swEofRegiones.equals(EOF_REGIONES);
    }

    public void setEofRegiones() {
        this.swEofRegiones = EOF_REGIONES;
    }


    // Conditionals (88) - SW-EOF-OFICINAS
    public boolean isEofOficinas() {
        return this.swEofOficinas.equals(EOF_OFICINAS);
    }

    public void setEofOficinas() {
        this.swEofOficinas = EOF_OFICINAS;
    }



    // Deeper properties

    // Property: variables.ctnError -> 10 CTN-ERROR S9(03)
    public int getCtnError() {
        return this.variables.getCtnError();
    }

    public void setCtnError(final int ctnError) {
        this.variables.setCtnError(ctnError);
    }

    // Property: variables.conIncidencias -> 10 CON-INCIDENCIAS
    public ConIncidencias getConIncidencias() {
        return this.variables.getConIncidencias();
    }

    // Property: variables.conIncidencias.conMensaje -> 15 CON-MENSAJE X(40)
    public String getConMensaje() {
        return this.variables.getConIncidencias().getConMensaje();
    }

    public void setConMensaje(final String conMensaje) {
        this.variables.getConIncidencias().setConMensaje(conMensaje);
    }

    // Property: variables.conError -> 10 CON-ERROR ZZZZZZZZZ+
    public NumericEditedVar getConError() {
        return this.variables.getConError();
    }

    public void setConError(final String conError) {
        this.variables.setConError(conError);
    }

    public void setConError(final IValue conError) {
        this.variables.setConError(conError);
    }


    // Deeper properties

    // Property: contadores.contadorProc -> 10 CONTADOR-PROC 9(07)
    public int getContadorProc() {
        return this.contadores.getContadorProc();
    }

    public void setContadorProc(final int contadorProc) {
        this.contadores.setContadorProc(contadorProc);
    }

    // Property: contadores.wsContador -> 10 WS-CONTADOR 9(03)
    public int getWsContador() {
        return this.contadores.getWsContador();
    }

    public void setWsContador(final int wsContador) {
        this.contadores.setWsContador(wsContador);
    }

    // Property: contadores.wsContTcna -> 10 WS-CONT-TCNA 9(03)
    public int getWsContTcna() {
        return this.contadores.getWsContTcna();
    }

    public void setWsContTcna(final int wsContTcna) {
        this.contadores.setWsContTcna(wsContTcna);
    }

    // Property: contadores.wsContTcna_1 -> 10 WS-CONT-TCNA-1 9(03)
    public int getWsContTcna_1() {
        return this.contadores.getWsContTcna_1();
    }

    public void setWsContTcna_1(final int wsContTcna_1) {
        this.contadores.setWsContTcna_1(wsContTcna_1);
    }

    // Property: contadores.wsContTfla -> 10 WS-CONT-TFLA 9(02)
    public int getWsContTfla() {
        return this.contadores.getWsContTfla();
    }

    public void setWsContTfla(final int wsContTfla) {
        this.contadores.setWsContTfla(wsContTfla);
    }

    // Property: contadores.wsContTger -> 10 WS-CONT-TGER 9(02)
    public int getWsContTger() {
        return this.contadores.getWsContTger();
    }

    public void setWsContTger(final int wsContTger) {
        this.contadores.setWsContTger(wsContTger);
    }

    // Property: contadores.wsContTger_1 -> 10 WS-CONT-TGER-1 9(03)
    public int getWsContTger_1() {
        return this.contadores.getWsContTger_1();
    }

    public void setWsContTger_1(final int wsContTger_1) {
        this.contadores.setWsContTger_1(wsContTger_1);
    }

    // Property: contadores.wsContTceo -> 10 WS-CONT-TCEO 9(02)
    public int getWsContTceo() {
        return this.contadores.getWsContTceo();
    }

    public void setWsContTceo(final int wsContTceo) {
        this.contadores.setWsContTceo(wsContTceo);
    }

    // Property: contadores.wsContTofn -> 10 WS-CONT-TOFN 9(02)
    public int getWsContTofn() {
        return this.contadores.getWsContTofn();
    }

    public void setWsContTofn(final int wsContTofn) {
        this.contadores.setWsContTofn(wsContTofn);
    }

    // Property: contadores.wsContTofn_1 -> 10 WS-CONT-TOFN-1 9(03)
    public int getWsContTofn_1() {
        return this.contadores.getWsContTofn_1();
    }

    public void setWsContTofn_1(final int wsContTofn_1) {
        this.contadores.setWsContTofn_1(wsContTofn_1);
    }

    // Property: contadores.wsContAgt0 -> 10 WS-CONT-AGT0 9(04)
    public int getWsContAgt0() {
        return this.contadores.getWsContAgt0();
    }

    public void setWsContAgt0(final int wsContAgt0) {
        this.contadores.setWsContAgt0(wsContAgt0);
    }

    // Property: contadores.wsContTfin -> 10 WS-CONT-TFIN 9(04)
    public int getWsContTfin() {
        return this.contadores.getWsContTfin();
    }

    public void setWsContTfin(final int wsContTfin) {
        this.contadores.setWsContTfin(wsContTfin);
    }

    // Property: contadores.wsContFolios -> 10 WS-CONT-FOLIOS 9(04)
    public int getWsContFolios() {
        return this.contadores.getWsContFolios();
    }

    public void setWsContFolios(final int wsContFolios) {
        this.contadores.setWsContFolios(wsContFolios);
    }

    // Property: contadores.wsContGrabas -> 10 WS-CONT-GRABAS 9(04)
    public int getWsContGrabas() {
        return this.contadores.getWsContGrabas();
    }

    public void setWsContGrabas(final int wsContGrabas) {
        this.contadores.setWsContGrabas(wsContGrabas);
    }

    // Property: contadores.wsEditLeidos -> 10 WS-EDIT-LEIDOS ZZZZZZZ,ZZZ,ZZZ
    public NumericEditedVar getWsEditLeidos() {
        return this.contadores.getWsEditLeidos();
    }

    public void setWsEditLeidos(final String wsEditLeidos) {
        this.contadores.setWsEditLeidos(wsEditLeidos);
    }

    public void setWsEditLeidos(final IValue wsEditLeidos) {
        this.contadores.setWsEditLeidos(wsEditLeidos);
    }

    // Property: contadores.wsEditGrabado -> 10 WS-EDIT-GRABADO ZZZZZZZ,ZZZ,ZZZ
    public NumericEditedVar getWsEditGrabado() {
        return this.contadores.getWsEditGrabado();
    }

    public void setWsEditGrabado(final String wsEditGrabado) {
        this.contadores.setWsEditGrabado(wsEditGrabado);
    }

    public void setWsEditGrabado(final IValue wsEditGrabado) {
        this.contadores.setWsEditGrabado(wsEditGrabado);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.swContratos = 0;
        this.swFolios = 0;
        this.swGerencias = 0;
        this.swRegiones = 0;
        this.swOficinas = 0;
        this.swFla2 = 1;
        this.swFla3 = 1;
        this.swEofAgentes = "N";
        this.swEofContratos = "N";
        this.swEofFolios = "N";
        this.swEofGerencias = "N";
        this.swEofRegiones = "N";
        this.swEofOficinas = "N";
        this.variables.initialize();
        this.contadores.initialize();
    }

}

