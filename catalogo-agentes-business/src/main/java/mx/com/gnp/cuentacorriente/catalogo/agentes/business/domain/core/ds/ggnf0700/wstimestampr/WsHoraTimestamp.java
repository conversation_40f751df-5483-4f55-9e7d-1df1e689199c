package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-HORA-TIMESTAMP.
 *
 */
@Setter
@Getter
public class WsHoraTimestamp extends DataStruct {
    // Properties
    private UnsignedNumericVar wsHoraTimestamp = new UnsignedNumericVar(this, 2, 0);         // 05 WS-HORA-TIMESTAMP 9(02)
    private AlphanumericVar filler4 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar wsMinTimestamp = new UnsignedNumericVar(this, 2, 0);          // 05 WS-MIN-TIMESTAMP 9(02)
    private AlphanumericVar filler5 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar wsSegTimestamp = new UnsignedNumericVar(this, 2, 0);          // 05 WS-SEG-TIMESTAMP 9(02)

    public WsHoraTimestamp() {
        super();
        initialize();
    }

    public WsHoraTimestamp(DataContainer parent) {
        super(parent);
    }

    public WsHoraTimestamp(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsHoraTimestamp(final int wsHoraTimestamp) {
        this.wsHoraTimestamp.setValue(wsHoraTimestamp);
    }

    public void setWsHoraTimestamp(final BigDecimal wsHoraTimestamp) {
        this.wsHoraTimestamp.setValue(wsHoraTimestamp);
    }

    public void setWsHoraTimestamp(final INumericValue wsHoraTimestamp) {
        this.wsHoraTimestamp.setValue(wsHoraTimestamp);
    }

    public void setFiller4(final String filler4) {
        this.filler4.setValue(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.filler4.setValue(filler4);
    }

    public void setWsMinTimestamp(final int wsMinTimestamp) {
        this.wsMinTimestamp.setValue(wsMinTimestamp);
    }

    public void setWsMinTimestamp(final BigDecimal wsMinTimestamp) {
        this.wsMinTimestamp.setValue(wsMinTimestamp);
    }

    public void setWsMinTimestamp(final INumericValue wsMinTimestamp) {
        this.wsMinTimestamp.setValue(wsMinTimestamp);
    }

    public void setFiller5(final String filler5) {
        this.filler5.setValue(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.filler5.setValue(filler5);
    }

    public void setWsSegTimestamp(final int wsSegTimestamp) {
        this.wsSegTimestamp.setValue(wsSegTimestamp);
    }

    public void setWsSegTimestamp(final BigDecimal wsSegTimestamp) {
        this.wsSegTimestamp.setValue(wsSegTimestamp);
    }

    public void setWsSegTimestamp(final INumericValue wsSegTimestamp) {
        this.wsSegTimestamp.setValue(wsSegTimestamp);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsHoraTimestamp.clear();
        this.filler4.setValue(".");
        this.wsMinTimestamp.clear();
        this.filler5.setValue("-");
        this.wsSegTimestamp.clear();
    }

}

