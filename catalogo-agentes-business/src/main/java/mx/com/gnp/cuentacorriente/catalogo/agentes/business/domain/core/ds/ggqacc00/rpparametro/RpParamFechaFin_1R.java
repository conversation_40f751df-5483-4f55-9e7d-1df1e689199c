package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpparametro;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-PARAM-FECHA-FIN-1-R.
 *
 */
@Setter
@Getter
public class RpParamFechaFin_1R extends DataStruct {
    // Properties
    private UnsignedNumericVar rpFechaFin_1Aaaa = new UnsignedNumericVar(this, 4, 0);        // 07 RP-FECHA-FIN-1-AAAA 9(04)
    // Level 88 - Conditional names
    private static final int FECHA_FIN_1_AAAA_OK = 1900;

    private AlphanumericVar rpGuionFin_1_1 = new AlphanumericVar(this, 1);   // 07 RP-GUION-FIN-1-1 X(01)
    private UnsignedNumericVar rpFechaFin_1Mm = new UnsignedNumericVar(this, 2, 0);          // 07 RP-FECHA-FIN-1-MM 9(02)
    // Level 88 - Conditional names
    private static final int FECHA_FIN_1_MM_OK = 01;

    private AlphanumericVar rpGuionFin_1_2 = new AlphanumericVar(this, 1);   // 07 RP-GUION-FIN-1-2 X(01)
    private UnsignedNumericVar rpFechaFin_1Dd = new UnsignedNumericVar(this, 2, 0);          // 07 RP-FECHA-FIN-1-DD 9(02)
    // Level 88 - Conditional names
    private static final int FECHA_FIN_1_DD_OK = 01;


    public RpParamFechaFin_1R() {
        super();
        initialize();
    }

    public RpParamFechaFin_1R(DataContainer parent) {
        super(parent);
    }

    public RpParamFechaFin_1R(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRpFechaFin_1Aaaa(final int rpFechaFin_1Aaaa) {
        this.rpFechaFin_1Aaaa.setValue(rpFechaFin_1Aaaa);
    }

    public void setRpFechaFin_1Aaaa(final BigDecimal rpFechaFin_1Aaaa) {
        this.rpFechaFin_1Aaaa.setValue(rpFechaFin_1Aaaa);
    }

    public void setRpFechaFin_1Aaaa(final INumericValue rpFechaFin_1Aaaa) {
        this.rpFechaFin_1Aaaa.setValue(rpFechaFin_1Aaaa);
    }

    // Conditionals (88) - RP-FECHA-FIN-1-AAAA
    public boolean isFechaFin_1AaaaOk() {
        return this.rpFechaFin_1Aaaa.isEqual(FECHA_FIN_1_AAAA_OK);
    }

    public void setFechaFin_1AaaaOk() {
        this.rpFechaFin_1Aaaa.setValue(FECHA_FIN_1_AAAA_OK);
    }


    public void setRpGuionFin_1_1(final String rpGuionFin_1_1) {
        this.rpGuionFin_1_1.setValue(rpGuionFin_1_1);
    }

    public void setRpGuionFin_1_1(final IAlphanumericValue rpGuionFin_1_1) {
        this.rpGuionFin_1_1.setValue(rpGuionFin_1_1);
    }

    public void setRpFechaFin_1Mm(final int rpFechaFin_1Mm) {
        this.rpFechaFin_1Mm.setValue(rpFechaFin_1Mm);
    }

    public void setRpFechaFin_1Mm(final BigDecimal rpFechaFin_1Mm) {
        this.rpFechaFin_1Mm.setValue(rpFechaFin_1Mm);
    }

    public void setRpFechaFin_1Mm(final INumericValue rpFechaFin_1Mm) {
        this.rpFechaFin_1Mm.setValue(rpFechaFin_1Mm);
    }

    // Conditionals (88) - RP-FECHA-FIN-1-MM
    public boolean isFechaFin_1MmOk() {
        return this.rpFechaFin_1Mm.isEqual(FECHA_FIN_1_MM_OK);
    }

    public void setFechaFin_1MmOk() {
        this.rpFechaFin_1Mm.setValue(FECHA_FIN_1_MM_OK);
    }


    public void setRpGuionFin_1_2(final String rpGuionFin_1_2) {
        this.rpGuionFin_1_2.setValue(rpGuionFin_1_2);
    }

    public void setRpGuionFin_1_2(final IAlphanumericValue rpGuionFin_1_2) {
        this.rpGuionFin_1_2.setValue(rpGuionFin_1_2);
    }

    public void setRpFechaFin_1Dd(final int rpFechaFin_1Dd) {
        this.rpFechaFin_1Dd.setValue(rpFechaFin_1Dd);
    }

    public void setRpFechaFin_1Dd(final BigDecimal rpFechaFin_1Dd) {
        this.rpFechaFin_1Dd.setValue(rpFechaFin_1Dd);
    }

    public void setRpFechaFin_1Dd(final INumericValue rpFechaFin_1Dd) {
        this.rpFechaFin_1Dd.setValue(rpFechaFin_1Dd);
    }

    // Conditionals (88) - RP-FECHA-FIN-1-DD
    public boolean isFechaFin_1DdOk() {
        return this.rpFechaFin_1Dd.isEqual(FECHA_FIN_1_DD_OK);
    }

    public void setFechaFin_1DdOk() {
        this.rpFechaFin_1Dd.setValue(FECHA_FIN_1_DD_OK);
    }



    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rpFechaFin_1Aaaa.clear();
        this.rpGuionFin_1_1.clear();
        this.rpFechaFin_1Mm.clear();
        this.rpGuionFin_1_2.clear();
        this.rpFechaFin_1Dd.clear();
    }

}

