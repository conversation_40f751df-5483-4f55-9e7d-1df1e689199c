package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.wscampostrabajo;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DATOS-GRAL-AGT.
 *
 */
@Setter
@Getter
public class WsDatosGralAgt extends DataStruct {
    // Properties
    private AlphanumericVar wsGralAgtRfc = new AlphanumericVar(this, 13);    // 10 WS-GRAL-AGT-RFC X(13)
    private AlphanumericVar wsGralAgtNom = new AlphanumericVar(this, 40);    // 10 WS-GRAL-AGT-NOM X(40)
    private AlphanumericVar wsGralAgtApePat = new AlphanumericVar(this, 40);                 // 10 WS-GRAL-AGT-APE-PAT X(40)
    private AlphanumericVar wsGralAgtApeMat = new AlphanumericVar(this, 30);                 // 10 WS-GRAL-AGT-APE-MAT X(30)
    private AlphanumericVar wsGralAgtRazSoc = new AlphanumericVar(this, 60);                 // 10 WS-GRAL-AGT-RAZ-SOC X(60)
    private AlphanumericVar wsGralAgtNomConcatenado = new AlphanumericVar(this, 100);        // 10 WS-GRAL-AGT-NOM-CONCATENADO X(100)
    private AlphanumericVar wsGralPraAgtCxxCurp = new AlphanumericVar(this, 20);             // 10 WS-GRAL-PRA-AGT-CXX-CURP X(20)
    private AlphanumericVar wsGralAgtNalCve = new AlphanumericVar(this, 3);  // 10 WS-GRAL-AGT-NAL-CVE X(03)
    private AlphanumericVar wsGralExtranjero = new AlphanumericVar(this, 3);                 // 10 WS-GRAL-EXTRANJERO X(03)
    private BinaryVar wsGralEsaCve = new BinaryVar(this, 4, 0);              // 10 WS-GRAL-ESA-CVE S9(4) COMPUTATIONAL
    private BinaryVar wsGralPefCve = new BinaryVar(this, 4, 0);              // 10 WS-GRAL-PEF-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar wsGralDmaCae = new AlphanumericVar(this, 40);    // 10 WS-GRAL-DMA-CAE X(40)
    private AlphanumericVar wsGralDmaNum = new AlphanumericVar(this, 5);     // 10 WS-GRAL-DMA-NUM X(05)
    private AlphanumericVar wsGralDmaInt = new AlphanumericVar(this, 5);     // 10 WS-GRAL-DMA-INT X(05)
    private AlphanumericVar wsGralDmaCol = new AlphanumericVar(this, 40);    // 10 WS-GRAL-DMA-COL X(40)
    private AlphanumericVar wsGralMndNom = new AlphanumericVar(this, 50);    // 10 WS-GRAL-MND-NOM X(50)
    private BinaryVar wsGralDmaCpoCve = new BinaryVar(this, 9, 0);           // 10 WS-GRAL-DMA-CPO-CVE S9(9) COMPUTATIONAL
    private AlphanumericVar wsGralPobNom = new AlphanumericVar(this, 40);    // 10 WS-GRAL-POB-NOM X(40)
    private AlphanumericVar wsGralMndEdoCve = new AlphanumericVar(this, 4);  // 10 WS-GRAL-MND-EDO-CVE X(04)
    private AlphanumericVar wsGralEdoNom = new AlphanumericVar(this, 30);    // 10 WS-GRAL-EDO-NOM X(30)
    private AlphanumericVar wsGralEdoPaiCve = new AlphanumericVar(this, 3);  // 10 WS-GRAL-EDO-PAI-CVE X(03)
    private AlphanumericVar wsGralPaiPaiNom = new AlphanumericVar(this, 30);                 // 10 WS-GRAL-PAI-PAI-NOM X(30)
    private AlphanumericVar wsGralCmaDesTelefon = new AlphanumericVar(this, 60);             // 10 WS-GRAL-CMA-DES-TELEFON X(60)

    public WsDatosGralAgt() {
        super();
        initialize();
    }

    public WsDatosGralAgt(DataContainer parent) {
        super(parent);
    }

    public WsDatosGralAgt(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsGralAgtRfc(final String wsGralAgtRfc) {
        this.wsGralAgtRfc.setValue(wsGralAgtRfc);
    }

    public void setWsGralAgtRfc(final IAlphanumericValue wsGralAgtRfc) {
        this.wsGralAgtRfc.setValue(wsGralAgtRfc);
    }

    public void setWsGralAgtNom(final String wsGralAgtNom) {
        this.wsGralAgtNom.setValue(wsGralAgtNom);
    }

    public void setWsGralAgtNom(final IAlphanumericValue wsGralAgtNom) {
        this.wsGralAgtNom.setValue(wsGralAgtNom);
    }

    public void setWsGralAgtApePat(final String wsGralAgtApePat) {
        this.wsGralAgtApePat.setValue(wsGralAgtApePat);
    }

    public void setWsGralAgtApePat(final IAlphanumericValue wsGralAgtApePat) {
        this.wsGralAgtApePat.setValue(wsGralAgtApePat);
    }

    public void setWsGralAgtApeMat(final String wsGralAgtApeMat) {
        this.wsGralAgtApeMat.setValue(wsGralAgtApeMat);
    }

    public void setWsGralAgtApeMat(final IAlphanumericValue wsGralAgtApeMat) {
        this.wsGralAgtApeMat.setValue(wsGralAgtApeMat);
    }

    public void setWsGralAgtRazSoc(final String wsGralAgtRazSoc) {
        this.wsGralAgtRazSoc.setValue(wsGralAgtRazSoc);
    }

    public void setWsGralAgtRazSoc(final IAlphanumericValue wsGralAgtRazSoc) {
        this.wsGralAgtRazSoc.setValue(wsGralAgtRazSoc);
    }

    public void setWsGralAgtNomConcatenado(final String wsGralAgtNomConcatenado) {
        this.wsGralAgtNomConcatenado.setValue(wsGralAgtNomConcatenado);
    }

    public void setWsGralAgtNomConcatenado(final IAlphanumericValue wsGralAgtNomConcatenado) {
        this.wsGralAgtNomConcatenado.setValue(wsGralAgtNomConcatenado);
    }

    public void setWsGralPraAgtCxxCurp(final String wsGralPraAgtCxxCurp) {
        this.wsGralPraAgtCxxCurp.setValue(wsGralPraAgtCxxCurp);
    }

    public void setWsGralPraAgtCxxCurp(final IAlphanumericValue wsGralPraAgtCxxCurp) {
        this.wsGralPraAgtCxxCurp.setValue(wsGralPraAgtCxxCurp);
    }

    public void setWsGralAgtNalCve(final String wsGralAgtNalCve) {
        this.wsGralAgtNalCve.setValue(wsGralAgtNalCve);
    }

    public void setWsGralAgtNalCve(final IAlphanumericValue wsGralAgtNalCve) {
        this.wsGralAgtNalCve.setValue(wsGralAgtNalCve);
    }

    public void setWsGralExtranjero(final String wsGralExtranjero) {
        this.wsGralExtranjero.setValue(wsGralExtranjero);
    }

    public void setWsGralExtranjero(final IAlphanumericValue wsGralExtranjero) {
        this.wsGralExtranjero.setValue(wsGralExtranjero);
    }

    public void setWsGralEsaCve(final int wsGralEsaCve) {
        this.wsGralEsaCve.setValue(wsGralEsaCve);
    }

    public void setWsGralEsaCve(final BigDecimal wsGralEsaCve) {
        this.wsGralEsaCve.setValue(wsGralEsaCve);
    }

    public void setWsGralEsaCve(final INumericValue wsGralEsaCve) {
        this.wsGralEsaCve.setValue(wsGralEsaCve);
    }

    public void setWsGralPefCve(final int wsGralPefCve) {
        this.wsGralPefCve.setValue(wsGralPefCve);
    }

    public void setWsGralPefCve(final BigDecimal wsGralPefCve) {
        this.wsGralPefCve.setValue(wsGralPefCve);
    }

    public void setWsGralPefCve(final INumericValue wsGralPefCve) {
        this.wsGralPefCve.setValue(wsGralPefCve);
    }

    public void setWsGralDmaCae(final String wsGralDmaCae) {
        this.wsGralDmaCae.setValue(wsGralDmaCae);
    }

    public void setWsGralDmaCae(final IAlphanumericValue wsGralDmaCae) {
        this.wsGralDmaCae.setValue(wsGralDmaCae);
    }

    public void setWsGralDmaNum(final String wsGralDmaNum) {
        this.wsGralDmaNum.setValue(wsGralDmaNum);
    }

    public void setWsGralDmaNum(final IAlphanumericValue wsGralDmaNum) {
        this.wsGralDmaNum.setValue(wsGralDmaNum);
    }

    public void setWsGralDmaInt(final String wsGralDmaInt) {
        this.wsGralDmaInt.setValue(wsGralDmaInt);
    }

    public void setWsGralDmaInt(final IAlphanumericValue wsGralDmaInt) {
        this.wsGralDmaInt.setValue(wsGralDmaInt);
    }

    public void setWsGralDmaCol(final String wsGralDmaCol) {
        this.wsGralDmaCol.setValue(wsGralDmaCol);
    }

    public void setWsGralDmaCol(final IAlphanumericValue wsGralDmaCol) {
        this.wsGralDmaCol.setValue(wsGralDmaCol);
    }

    public void setWsGralMndNom(final String wsGralMndNom) {
        this.wsGralMndNom.setValue(wsGralMndNom);
    }

    public void setWsGralMndNom(final IAlphanumericValue wsGralMndNom) {
        this.wsGralMndNom.setValue(wsGralMndNom);
    }

    public void setWsGralDmaCpoCve(final int wsGralDmaCpoCve) {
        this.wsGralDmaCpoCve.setValue(wsGralDmaCpoCve);
    }

    public void setWsGralDmaCpoCve(final BigDecimal wsGralDmaCpoCve) {
        this.wsGralDmaCpoCve.setValue(wsGralDmaCpoCve);
    }

    public void setWsGralDmaCpoCve(final INumericValue wsGralDmaCpoCve) {
        this.wsGralDmaCpoCve.setValue(wsGralDmaCpoCve);
    }

    public void setWsGralPobNom(final String wsGralPobNom) {
        this.wsGralPobNom.setValue(wsGralPobNom);
    }

    public void setWsGralPobNom(final IAlphanumericValue wsGralPobNom) {
        this.wsGralPobNom.setValue(wsGralPobNom);
    }

    public void setWsGralMndEdoCve(final String wsGralMndEdoCve) {
        this.wsGralMndEdoCve.setValue(wsGralMndEdoCve);
    }

    public void setWsGralMndEdoCve(final IAlphanumericValue wsGralMndEdoCve) {
        this.wsGralMndEdoCve.setValue(wsGralMndEdoCve);
    }

    public void setWsGralEdoNom(final String wsGralEdoNom) {
        this.wsGralEdoNom.setValue(wsGralEdoNom);
    }

    public void setWsGralEdoNom(final IAlphanumericValue wsGralEdoNom) {
        this.wsGralEdoNom.setValue(wsGralEdoNom);
    }

    public void setWsGralEdoPaiCve(final String wsGralEdoPaiCve) {
        this.wsGralEdoPaiCve.setValue(wsGralEdoPaiCve);
    }

    public void setWsGralEdoPaiCve(final IAlphanumericValue wsGralEdoPaiCve) {
        this.wsGralEdoPaiCve.setValue(wsGralEdoPaiCve);
    }

    public void setWsGralPaiPaiNom(final String wsGralPaiPaiNom) {
        this.wsGralPaiPaiNom.setValue(wsGralPaiPaiNom);
    }

    public void setWsGralPaiPaiNom(final IAlphanumericValue wsGralPaiPaiNom) {
        this.wsGralPaiPaiNom.setValue(wsGralPaiPaiNom);
    }

    public void setWsGralCmaDesTelefon(final String wsGralCmaDesTelefon) {
        this.wsGralCmaDesTelefon.setValue(wsGralCmaDesTelefon);
    }

    public void setWsGralCmaDesTelefon(final IAlphanumericValue wsGralCmaDesTelefon) {
        this.wsGralCmaDesTelefon.setValue(wsGralCmaDesTelefon);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsGralAgtRfc.clear();
        this.wsGralAgtNom.clear();
        this.wsGralAgtApePat.clear();
        this.wsGralAgtApeMat.clear();
        this.wsGralAgtRazSoc.clear();
        this.wsGralAgtNomConcatenado.clear();
        this.wsGralPraAgtCxxCurp.clear();
        this.wsGralAgtNalCve.clear();
        this.wsGralExtranjero.clear();
        this.wsGralEsaCve.clear();
        this.wsGralPefCve.clear();
        this.wsGralDmaCae.clear();
        this.wsGralDmaNum.clear();
        this.wsGralDmaInt.clear();
        this.wsGralDmaCol.clear();
        this.wsGralMndNom.clear();
        this.wsGralDmaCpoCve.clear();
        this.wsGralPobNom.clear();
        this.wsGralMndEdoCve.clear();
        this.wsGralEdoNom.clear();
        this.wsGralEdoPaiCve.clear();
        this.wsGralPaiPaiNom.clear();
        this.wsGralCmaDesTelefon.clear();
    }

}

