package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-COM-NIV-R.
 *
 */
@Setter
@Getter
public class WsComNivR extends DataStruct {
    // Properties
    private AlphanumericVar rsComNiv11 = new AlphanumericVar(this, 11);      // 10 RS-COM-NIV11 X(11)
    private AlphanumericVar rsComNiv2 = new AlphanumericVar(this, 2);        // 10 RS-COM-NIV2 X(02)

    public WsComNivR() {
        super();
        initialize();
    }

    public WsComNivR(DataContainer parent) {
        super(parent);
    }

    public WsComNivR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRsComNiv11(final String rsComNiv11) {
        this.rsComNiv11.setValue(rsComNiv11);
    }

    public void setRsComNiv11(final IAlphanumericValue rsComNiv11) {
        this.rsComNiv11.setValue(rsComNiv11);
    }

    public void setRsComNiv2(final String rsComNiv2) {
        this.rsComNiv2.setValue(rsComNiv2);
    }

    public void setRsComNiv2(final IAlphanumericValue rsComNiv2) {
        this.rsComNiv2.setValue(rsComNiv2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rsComNiv11.clear();
        this.rsComNiv2.clear();
    }

}

