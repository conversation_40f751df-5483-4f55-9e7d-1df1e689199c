package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DATOS-ABEND.
 *
 */
// 148014800*                                                                 01480002
// 149014900*                                                                 01490002
@Setter
@Getter
public class WsDatosAbend { // VStruct
    // Properties
    private String wsPrograma = "GGNF0700";                                 // 03 WS-PROGRAMA X(10)
    private String wsTabla = "";                                            // 03 WS-TABLA X(10)
    private String wsProcesoLlama = "";                                     // 03 WS-PROCESO-LLAMA X(30)
    private String wsProcesoEjecuta = "";                                   // 03 WS-PROCESO-EJECUTA X(30)
    private String wsInstruccion = "";                                      // 03 WS-INSTRUCCION X(15)

    public WsDatosAbend() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsPrograma = "GGNF0700";
        this.wsTabla = "";
        this.wsProcesoLlama = "";
        this.wsProcesoEjecuta = "";
        this.wsInstruccion = "";
    }

}

