package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscodigos;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-COD-ERROR.
 *
 */
@Setter
@Getter
public class WsCodError extends DataStruct {
    // Properties
    private AlphanumericVar wsDis1 = new AlphanumericVar(this, 1);           // 05 WS-DIS1 X
    private UnsignedNumericVar wsDis2 = new UnsignedNumericVar(this, 9, 0);  // 05 WS-DIS2 9(9)

    public WsCodError() {
        super();
        initialize();
    }

    public WsCodError(DataContainer parent) {
        super(parent);
    }

    public WsCodError(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsDis1(final String wsDis1) {
        this.wsDis1.setValue(wsDis1);
    }

    public void setWsDis1(final IAlphanumericValue wsDis1) {
        this.wsDis1.setValue(wsDis1);
    }

    public void setWsDis2(final int wsDis2) {
        this.wsDis2.setValue(wsDis2);
    }

    public void setWsDis2(final BigDecimal wsDis2) {
        this.wsDis2.setValue(wsDis2);
    }

    public void setWsDis2(final INumericValue wsDis2) {
        this.wsDis2.setValue(wsDis2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsDis1.clear();
        this.wsDis2.clear();
    }

}

