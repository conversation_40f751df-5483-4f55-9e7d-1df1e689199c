package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TIT21.
 *
 */
// 332054500*                                                                 05450000
// 333054600*                                                                 05460000
// 334054700*                                                                 05470000
@Setter
@Getter
public class Tit21 extends DataStruct {
    // Properties
    private AlphanumericVar filler1 = new AlphanumericVar(this, 44);         // 03 FILLER X(44)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 44);         // 03 FILLER X(44)
    private AlphanumericVar filler3 = new AlphanumericVar(this, 44);         // 03 FILLER X(44)

    public Tit21() {
        super();
        initialize();
    }

    public Tit21(DataContainer parent) {
        super(parent);
    }

    public Tit21(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.filler1.setValue("AGT-IDR   CNA-NUM  FLA-NUM  RAM-CVE  SRA-CVE");
        this.filler2.setValue("                                            ");
        this.filler3.setValue("                                            ");
    }

}

