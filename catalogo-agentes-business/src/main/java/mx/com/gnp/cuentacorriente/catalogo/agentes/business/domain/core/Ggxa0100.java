
/**
 *  Program: Ggxa0100.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.common.cbl.functions.Functions;
import com.base100.caravel.support.common.cbl.search.SearchListener;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.RegCifrasControl;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.WsDatosAbend;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.WsVariablesDb2;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.WsExtractoNull;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.WwAuxiliares;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.WsRegSalida;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.TablaCna0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.TablaGer0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.TablaOfn0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.TablasElementos;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.AreasDeTrabajo;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.WsCodigos;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcna0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcma0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctorv0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtfla0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcna0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcma0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctorv0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagt0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtfla0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggxa0100 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private AlphanumericVar fdRegExppc = new AlphanumericVar(3326);          // 01 FD-REG-EXPPC X(3326) UsedAsReferenceParameter
    private AlphanumericVar fdRegCcpc = new AlphanumericVar(45);             // 01 FD-REG-CCPC X(045) UsedAsReferenceParameter
    private RegCifrasControl regCifrasControl = new  RegCifrasControl();     // 01 REG-CIFRAS-CONTROL UsedAsParameter
    private WsDatosAbend wsDatosAbend = new  WsDatosAbend();                 // 01 WS-DATOS-ABEND
    private WsVariablesDb2 wsVariablesDb2 = new  WsVariablesDb2();           // 01 WS-VARIABLES-DB2
    private WsExtractoNull wsExtractoNull = new  WsExtractoNull();           // 01 WS-EXTRACTO-NULL
    private WwAuxiliares wwAuxiliares = new  WwAuxiliares();                 // 01 WW-AUXILIARES
    // Auto variables
    private int indFla1;                            // IND-FLA1
    private int indCeo0;                            // IND-CEO0
//  private int indFin;                             // IND-FIN // [@WARNING variable NOT USED]
    // Level 1
    private WsRegSalida wsRegSalida = new  WsRegSalida();                    // 01 WS-REG-SALIDA
    // Auto variables
    private int indCna0;                            // IND-CNA0
    // Level 1
    private TablaCna0 tablaCna0 = new  TablaCna0();                          // 01 TABLA-CNA0
    // Auto variables
    private int indGer0;                            // IND-GER0
    // Level 1
    private TablaGer0 tablaGer0 = new  TablaGer0();                          // 01 TABLA-GER0
    // Auto variables
    private int indOfn0;                            // IND-OFN0
    // Level 1
    private TablaOfn0 tablaOfn0 = new  TablaOfn0();                          // 01 TABLA-OFN0
    private TablasElementos tablasElementos = new  TablasElementos();        // 01 TABLAS-ELEMENTOS
    private AreasDeTrabajo areasDeTrabajo = new  AreasDeTrabajo();           // 01 AREAS-DE-TRABAJO
    private int db2ReturnCode;                                              // 01 DB2-RETURN-CODE S9(9) COMPUTATIONAL
    // Level 88 - Conditional names
    private static final int DB2_OK = 0;
    private static final int DB2_NOTFND = 100;
    private static final int DB2_DUPREC = -803;
    private static final int DB2_NOTUNI = -811;
    private static final int DB2_DATETIME = -181;
    private static final int DB2_SYSTEM = -911;

    private WsCodigos wsCodigos = new  WsCodigos();                          // 01 WS-CODIGOS

    private Sqlca sqlca;
    // Files
    private ISequentialFile archExppc;
    private ISequentialFile archCcpc;



    // Declare Sql Ports
    private IGfvtcna0Port gfvtcna0Port;
    private IGfvtcma0Port gfvtcma0Port;
    private IGcctorv0Port gcctorv0Port;
    private IGfvtagt0Port gfvtagt0Port;
    private IGfvtfla0Port gfvtfla0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtcna0Model> gfvtcna0Model;
    private Optional<Gfvtcma0Model> gfvtcma0Model;
    private Optional<Gcctorv0Model> gcctorv0Model;
    private Optional<Gfvtagt0Model> gfvtagt0Model;
    private Optional<Gfvtfla0Model> gfvtfla0Model;

    // Declare Sql Cursors
    private ICursor<GenericModel> curAgt0Cursor;
    private ICursor<Gfvtcna0Model> curCna0Cursor;
    private ICursor<GenericModel> curCeo0Cursor;
    private ICursor<Gfvtcna0Model> curOfn0Cursor;
    private ICursor<Gfvtcna0Model> curGer0Cursor;
    private ICursor<Gfvtfla0Model> curFla1Cursor;
    private ICursor<Gcctorv0Model> curOrv0Cursor;

    public Ggxa0100(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initFiles();
    }

    public void initFiles() {

        this.archExppc = createSequentialFile("ARCH-EXPPC", "GGXA0101", fdRegExppc);

        this.archCcpc = createSequentialFile("ARCH-CCPC", "GGXA0202", fdRegCcpc);



    }

    public void run() {
        sqlDelayedParagraph();
        start();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(curAgt0Cursor == null) {
            curAgt0Cursor = gfvtagt0Port.createCursorCur_agt0_2(
                () -> null);
        }
        if(curCna0Cursor == null) {
            curCna0Cursor = gfvtcna0Port.createCursorCur_cna0_4(
                () -> Gfvtcna0Model.builder()
                    .agtIdr(wsVariablesDb2.getAgtAgtIdr())
                .build());
        }
        if(curCeo0Cursor == null) {
            curCeo0Cursor = gfvtcna0Port.createCursorCur_ceo0(
                () -> GenericModel.builder().values(List.of(
                    wsVariablesDb2.getAgtAgtIdr()))
                .build());
        }
        if(curOfn0Cursor == null) {
            curOfn0Cursor = gfvtcna0Port.createCursorCur_ofn0(
                () -> Gfvtcna0Model.builder()
                    .agtIdr(wsVariablesDb2.getAgtAgtIdr())
                .build());
        }
        if(curGer0Cursor == null) {
            curGer0Cursor = gfvtcna0Port.createCursorCur_ger0(
                () -> Gfvtcna0Model.builder()
                    .agtIdr(wsVariablesDb2.getAgtAgtIdr())
                .build());
        }
        if(curFla1Cursor == null) {
            curFla1Cursor = gfvtfla0Port.createCursorCur_fla1(
                () -> Gfvtfla0Model.builder()
                    .agtIdr(wsVariablesDb2.getAgtAgtIdr())
                .build());
        }
        if(curOrv0Cursor == null) {
            curOrv0Cursor = gcctorv0Port.createCursorCur_orv0(
                () -> Gcctorv0Model.builder()
                    .orvAgtIdrGte(wsVariablesDb2.getAgtAgtIdr())
                .build());
        }
    }

    /**
     *  Paragraph: (START).
     */
    void start() {
        p7000Inicial();
        while (!(areasDeTrabajo.isEofAgentes())) {
            p1000General();
        }
        p8000Final();
        throw new GobackException();
    }

    /**
     *  Paragraph: 1000-GENERAL.
     *
     */
    void p1000General() {
        wsVariablesDb2.setWsAgtAnt(wsVariablesDb2.getAgtAgtIdr());

        while (!(wsVariablesDb2.getAgtAgtIdr() != wsVariablesDb2.getWsAgtAnt()
            || areasDeTrabajo.isEofAgentes())) {
            p2000ProcesaAgt0();
        }
        if (areasDeTrabajo.getSwContratos() == 1) {
            archExppc.write(fdRegExppc, wsRegSalida);
            areasDeTrabajo.setWsContGrabas(areasDeTrabajo.getWsContGrabas() + 1);
        }
        initRegSalida();
    }

    /**
     *  Paragraph: 2000-PROCESA-AGT0.
     *
     */
    void p2000ProcesaAgt0() {
        initContadores();
        p2001IdentGerente();
        p2002BuscaEmail();
        p2003MueveExtagtSal();
        p2100CompactaCna();
        if (areasDeTrabajo.getSwContratos() == 1) {
            p2200CompactaFla();
            p2300CompactaGer();
            p2400CompactaRegion();
            p2500CompactaOficina();
            wsRegSalida.setWsLimitadFin(" ");
        }
        wsVariablesDb2.getAgt0().initialize();
        p2012FetchCurAgt0();
    }

    /**
     *  Paragraph: 2100-COMPACTA-CNA.
     *
     */
    void p2100CompactaCna() {
        wsVariablesDb2.getCna0().initialize();
        areasDeTrabajo.setSwEofContratos("N");

        areasDeTrabajo.setSwContratos(0);

        p2004AbreCurCna0();
        p2005FetchCurCna0();
        while (!(areasDeTrabajo.isEofContratos()
            || areasDeTrabajo.getWsContTcna() == 85)) {
            p3000ProcesaCna0();
        }
        p2006CierraCurCna0();
        if (areasDeTrabajo.getSwContratos() == 0) {
            areasDeTrabajo.setWsContTcna(1);

            tablaCna0.getTCna0().at(areasDeTrabajo.getWsContTcna()).getSCnaCnaNum().setSpaces();
            tablaCna0.getTCna0().at(areasDeTrabajo.getWsContTcna()).setSCnaLimitad("|");
            p3015QuitaBlancosCna0();
        } else {
            tablaCna0.getTCna0().at(areasDeTrabajo.getWsContTcna()).setSCnaLimitad("|");
            wsRegSalida.getTCna0_1().at(areasDeTrabajo.getWsContTcna_1()).setSCnaCnaNumElem("|");
        }
    }

    /**
     *  Paragraph: 2012-FETCH-CUR-AGT0.
     *
     */
    void p2012FetchCurAgt0() {
        wsDatosAbend.setWsProcesoEjecuta("2012 FETCH-AGT0             ");

        wsDatosAbend.setWsInstruccion("FETCH               ");

        wsDatosAbend.setWsTabla("GFVTAGT0            ");

        genericModel = curAgt0Cursor.next();

        if (genericModel.isPresent()) {
            wsVariablesDb2.setAgtAgtIdr(fromHostInt(genericModel.get().values().get(0)));
            wsExtractoNull.setAgtAgtIdr1(isNull(genericModel.get().values().get(0)) ? -1 : 0);
            wsVariablesDb2.setAgtAgtNom(fromHostString(genericModel.get().values().get(1)));
            wsExtractoNull.setAgtAgtNom1(isNull(genericModel.get().values().get(1)) ? -1 : 0);
            wsVariablesDb2.setAgtAgtApePat(fromHostString(genericModel.get().values().get(2)));
            wsExtractoNull.setAgtAgtApePat1(isNull(genericModel.get().values().get(2)) ? -1 : 0);
            wsVariablesDb2.setAgtAgtApeMat(fromHostString(genericModel.get().values().get(3)));
            wsExtractoNull.setAgtAgtApeMat1(isNull(genericModel.get().values().get(3)) ? -1 : 0);
            wsVariablesDb2.setAgtAgtNip(fromHostInt(genericModel.get().values().get(4)));
            wsExtractoNull.setAgtAgtNip1(isNull(genericModel.get().values().get(4)) ? -1 : 0);
            wsVariablesDb2.setAgtRazSoc(fromHostString(genericModel.get().values().get(5)));
            wsExtractoNull.setAgtRazSoc1(isNull(genericModel.get().values().get(5)) ? -1 : 0);
            wsVariablesDb2.setAgtPefCve(fromHostInt(genericModel.get().values().get(6)));
            wsExtractoNull.setAgtPefCve1(isNull(genericModel.get().values().get(6)) ? -1 : 0);
        }
        db2check();
        if (this.isDb2Ok()) {
            areasDeTrabajo.setContadorProc(areasDeTrabajo.getContadorProc() + 1);
            areasDeTrabajo.setWsContAgt0(areasDeTrabajo.getWsContAgt0() + 1);
            if (areasDeTrabajo.getContadorProc() == 1000) {
                areasDeTrabajo.setContadorProc(0);

                display("REGS LEIDOS DE AGENTES => ", areasDeTrabajo.getWsContAgt0());
            }
        } else if (this.isDb2Notfnd()) {
            areasDeTrabajo.setSwEofAgentes("S");

        }
    }

    /**
     *  Paragraph: 2001-IDENT-GERENTE.
     *
     */
    void p2001IdentGerente() {
        wsDatosAbend.setWsProcesoEjecuta("2001 ABRE-CUR-ORV0        ");

        wsDatosAbend.setWsInstruccion("OPEN                ");

        wsDatosAbend.setWsTabla("CUR-ORV0            ");

        curOrv0Cursor.open();
        db2check();
        wsDatosAbend.setWsProcesoEjecuta("2001 FETCH-CNA0           ");

        wsDatosAbend.setWsInstruccion("FETCH               ");

        wsDatosAbend.setWsTabla("CUR-ORV0            ");

        gcctorv0Model = curOrv0Cursor.next();

        if (gcctorv0Model.isPresent()) {
            wsVariablesDb2.setWsOrvCve(fromHostInt(gcctorv0Model.get().orvCve()));
            wsExtractoNull.setWsOrvCve1(isNull(gcctorv0Model.get().orvCve()) ? -1 : 0);
        }
        db2check();
        if (this.isDb2Ok()) {
            wsVariablesDb2.setWsIndGerente("GERENTE");

        } else if (this.isDb2Notfnd()) {
            wsVariablesDb2.setWsIndGerente("AGENTE");

        }
        wsDatosAbend.setWsProcesoEjecuta("2001 CIERRA-CUR-ORV0      ");

        wsDatosAbend.setWsInstruccion("CLOSE               ");

        wsDatosAbend.setWsTabla("CUR-ORV0            ");

        if (curOrv0Cursor != null) {
            curOrv0Cursor.close();
        }
        db2check();
        if (!this.isDb2Ok()) {
            display(" 2001 EL CURSOR CUR-ORV0 NO CERRO. ");
        }
    }

    /**
     *  Paragraph: 2002-BUSCA-EMAIL.
     *
     */
    void p2002BuscaEmail() {
        wsDatosAbend.setWsProcesoEjecuta("2002 BUSCA-EMAIL          ");

        wsDatosAbend.setWsInstruccion("SELECT              ");

        wsDatosAbend.setWsTabla("GFVTDMA0-GFVTCMA0   ");

        genericModel =
            gfvtcma0Port.select_6(
                GenericModel.builder().values(List.of(
                    wsVariablesDb2.getAgtAgtIdr()))
                .build());

        if(genericModel.isPresent()) {
            wsVariablesDb2.setCmaCmaDes(fromHostString(genericModel.get().values().get(0)));
            wsExtractoNull.setCmaCmaDes1(isNull(genericModel.get().values().get(0)) ? -1 : 0);
        }
        //     PERFORM DB2CHECK                                             
        db2checkEmail();
    }

    /**
     *  Paragraph: 2003-MUEVE-EXTAGT-SAL.
     *
     */
    void p2003MueveExtagtSal() {
        wsRegSalida.setSAgtAgtIdr(wsVariablesDb2.getAgtAgtIdr());
        wsRegSalida.setSAgtAgtNom(wsVariablesDb2.getAgtAgtNom());
        wsRegSalida.setSAgtAgtApePat(wsVariablesDb2.getAgtAgtApePat());
        wsRegSalida.setSAgtAgtApeMat(wsVariablesDb2.getAgtAgtApeMat());
        if (wsVariablesDb2.getAgtPefCve() == 2) {
            wsRegSalida.setSAgtAgtNom(wsVariablesDb2.getAgtRazSoc());
            wsRegSalida.getSAgtAgtApePat().setSpaces();
            wsRegSalida.getSAgtAgtApeMat().setSpaces();
        }
        wsRegSalida.setSWsIndGerente(wsVariablesDb2.getWsIndGerente());
        wsRegSalida.getSCmaCmaDes().setSpaces();
        wsRegSalida.setSCmaCmaDes(wsVariablesDb2.getCmaCmaDes());
        wsRegSalida.setSAgtAgtNip(wsVariablesDb2.getAgtAgtNip());
    }

    /**
     *  Paragraph: 3000-PROCESA-CNA0.
     *
     */
    void p3000ProcesaCna0() {
        p3010LlenaTablaTcna0();
        wsVariablesDb2.getCna0().initialize();
        p2005FetchCurCna0();
    }

    /**
     *  Paragraph: 3010-LLENA-TABLA-TCNA0.
     *
     */
    void p3010LlenaTablaTcna0() {
        wsVariablesDb2.setWsCnaCnaNum(wsVariablesDb2.getCnaCnaNum());

        wwAuxiliares.setWwCnaCnaNum(wsVariablesDb2.getWsCnaCnaNum());
        p3011MueveBlancos();
        wwAuxiliares.setWwCnaCnaNumJ(wwAuxiliares.getWwCnaCnaNumR().toStringValue());

        indCna0 = 1;

        search(tablaCna0.getTCna0(), indCna0, () -> indCna0++, new SearchListener() {
            @Override
            public boolean accept(int i) {
                if (Functions.isEqual(wwAuxiliares.getWwCnaCnaNumJ(), tablaCna0.getTCna0().at(indCna0).getSCnaCnaNum())) {
                    ;
                    return true;
                }
                return false;
            }

            @Override
            public void atEnd() {
                areasDeTrabajo.setWsContTcna(areasDeTrabajo.getWsContTcna() + 1);
                tablaCna0.getTCna0().at(areasDeTrabajo.getWsContTcna()).setSCnaCnaNum(wwAuxiliares.getWwCnaCnaNumJ());
                tablaCna0.getTCna0().at(areasDeTrabajo.getWsContTcna()).setSCnaLimitad(";");
                p3015QuitaBlancosCna0();
            }
        });
        //     WHEN WS-CNA-CNA-NUM EQUAL S-CNA-CNA-NUM(IND-CNA0)            
    }

    /**
     *  Paragraph: 3011-MUEVE-BLANCOS.
     *
     */
    void p3011MueveBlancos() {
        if (wsVariablesDb2.getWsCnaCnaNum() < 10) {
            wwAuxiliares.getWwCnaCnaNumR().replaceAll("0", " ");        }
    }

    /**
     *  Paragraph: 3015-QUITA-BLANCOS-CNA0.
     *
     */
    void p3015QuitaBlancosCna0() {
        tablasElementos.getWwTabDato().setValue(tablaCna0.getTCna0().at(areasDeTrabajo.getWsContTcna()).getTCna0Elem());
        wwAuxiliares.setWkInd5(1);
        while (!(wwAuxiliares.getWkInd5() > 3)) {
            p3020MueveElemCna0();
            wwAuxiliares.setWkInd5(wwAuxiliares.getWkInd5() + 1);
        }
    }

    /**
     *  Paragraph: 3020-MUEVE-ELEM-CNA0.
     *
     */
    void p3020MueveElemCna0() {
        if (!tablasElementos.getWwCampo().at(wwAuxiliares.getWkInd5()).isSpaces()) {
            areasDeTrabajo.setWsContTcna_1(areasDeTrabajo.getWsContTcna_1() + 1);
            wsRegSalida.getTCna0_1().at(areasDeTrabajo.getWsContTcna_1()).setSCnaCnaNumElem(tablasElementos.getWwCampo().at(wwAuxiliares.getWkInd5()));
        }
    }

    /**
     *  Paragraph: 2004-ABRE-CUR-CNA0.
     *
     */
    void p2004AbreCurCna0() {
        wsDatosAbend.setWsProcesoEjecuta("2004 ABRE-CUR-CNA0        ");

        wsDatosAbend.setWsInstruccion("OPEN                ");

        wsDatosAbend.setWsTabla("CUR-CNA0            ");

        curCna0Cursor.open();
        db2check();
    }

    /**
     *  Paragraph: 2005-FETCH-CUR-CNA0.
     *
     */
    void p2005FetchCurCna0() {
        wsDatosAbend.setWsProcesoEjecuta("2005 FETCH-CNA0           ");

        wsDatosAbend.setWsInstruccion("FETCH               ");

        wsDatosAbend.setWsTabla("GFVTCNA0            ");

        gfvtcna0Model = curCna0Cursor.next();

        if (gfvtcna0Model.isPresent()) {
            wsVariablesDb2.setCnaCnaNum(fromHostInt(gfvtcna0Model.get().cnaNum()));
            wsExtractoNull.setCnaCnaNum1(isNull(gfvtcna0Model.get().cnaNum()) ? -1 : 0);
        }
        db2check();
        if (this.isDb2Ok()) {
            areasDeTrabajo.setSwContratos(1);

        } else if (this.isDb2Notfnd()) {
            areasDeTrabajo.setSwEofContratos("S");

            if (areasDeTrabajo.getSwContratos() == 0) {
                display("2005 NO TIENE CONTRATOS EL AGENTE: ", wsVariablesDb2.getAgtAgtIdr());
            }
        }
    }

    /**
     *  Paragraph: 2006-CIERRA-CUR-CNA0.
     *
     */
    void p2006CierraCurCna0() {
        wsDatosAbend.setWsProcesoEjecuta("2006 CIERRA-CUR-CNA0      ");

        wsDatosAbend.setWsInstruccion("CLOSE               ");

        wsDatosAbend.setWsTabla("CUR-CNA0            ");

        if (curCna0Cursor != null) {
            curCna0Cursor.close();
        }
        db2check();
        if (!this.isDb2Ok()) {
            display(" 2006 EL CURSOR CUR-CNA0 NO CERRO. ");
        }
    }

    /**
     *  Paragraph: 4000-PROCESA-FLA1.
     *
     */
    void p4000ProcesaFla1() {
        p4010LlenaTablaTfla1();
        if (areasDeTrabajo.getWsContTfla() == 28 
            || areasDeTrabajo.getWsContTfla() == 56) {
            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaLimitad1("|");
        }
        //        IF WS-CONT-TFLA EQUAL 28                                  
        //           MOVE  0       TO SW-FLA2                               
        //        ELSE                                                      
        //           MOVE  0       TO SW-FLA3                               
        //        END-IF                                                    
        wsVariablesDb2.getFla0().initialize();
        p2009FetchCurFla0();
    }

    /**
     *  Paragraph: 4010-LLENA-TABLA-TFLA1.
     *
     */
    void p4010LlenaTablaTfla1() {
        indFla1 = 1;

        search(wsRegSalida.getTFla1(), indFla1, () -> indFla1++, new SearchListener() {
            @Override
            public boolean accept(int i) {
                if (Functions.isEqual(wsVariablesDb2.getFlaFlaNum(), wsRegSalida.getTFla1().at(indFla1).getSFlaFlaNum1())) {
                    ;
                    return true;
                }
                return false;
            }

            @Override
            public void atEnd() {
                areasDeTrabajo.setWsContTfla(areasDeTrabajo.getWsContTfla() + 1);
                wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaFlaNum1(wsVariablesDb2.getFlaFlaNum());
                wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaLimitad1(";");
            }
        });
    }

    /**
     *  Paragraph: 2200-COMPACTA-FLA.
     *
     */
    void p2200CompactaFla() {
        wsVariablesDb2.getFla0().initialize();
        areasDeTrabajo.setSwEofFolios("N");

        areasDeTrabajo.setSwFolios(0);

        p2008AbreCurFla1();
        p2009FetchCurFla0();
        while (!(areasDeTrabajo.isEofFolios()
            || areasDeTrabajo.getWsContTfla() == 84)) {
            p4000ProcesaFla1();
        }
        p2206CierraCurFla1();
        if (areasDeTrabajo.getSwFolios() == 0) {
            areasDeTrabajo.setWsContTfla(1);

            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaFlaNum1(" ");
            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaLimitad1("|");
        } else {
            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaLimitad1("|");
        }
        if (areasDeTrabajo.getWsContTfla() > 28 
            && (areasDeTrabajo.getWsContTfla() == 56 
            || areasDeTrabajo.getWsContTfla() < 56)) {
            areasDeTrabajo.setSwFla3(0);

        }
        if (areasDeTrabajo.getWsContTfla() > 56) {
            areasDeTrabajo.setSwFla2(0);

            areasDeTrabajo.setSwFla3(0);

        }
        if (areasDeTrabajo.getSwFla2() == 1) {
            areasDeTrabajo.setWsContTfla(areasDeTrabajo.getWsContTfla() + 1);
            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaFlaNum1(" ");
            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaLimitad1("|");
        }
        if (areasDeTrabajo.getSwFla3() == 1) {
            areasDeTrabajo.setWsContTfla(areasDeTrabajo.getWsContTfla() + 1);
            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaFlaNum1(" ");
            wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContTfla()).setSFlaLimitad1("|");
        }
        areasDeTrabajo.setSwFla2(1);

        areasDeTrabajo.setSwFla3(1);

    }

    /**
     *  Paragraph: 2008-ABRE-CUR-FLA1.
     *
     */
    void p2008AbreCurFla1() {
        wsDatosAbend.setWsProcesoEjecuta("2008 ABRE-CUR-FLA1        ");

        wsDatosAbend.setWsInstruccion("OPEN                ");

        wsDatosAbend.setWsTabla("CUR-FLA1            ");

        curFla1Cursor.open();
        db2check();
    }

    /**
     *  Paragraph: 2009-FETCH-CUR-FLA0.
     *
     */
    void p2009FetchCurFla0() {
        wsDatosAbend.setWsProcesoEjecuta("2009 FETCH-FLA0           ");

        wsDatosAbend.setWsInstruccion("FETCH               ");

        wsDatosAbend.setWsTabla("GFVTFLA0            ");

        gfvtfla0Model = curFla1Cursor.next();

        if (gfvtfla0Model.isPresent()) {
            wsVariablesDb2.setFlaFlaNum(fromHostString(gfvtfla0Model.get().flaNum()));
            wsExtractoNull.setFlaFlaNumN(isNull(gfvtfla0Model.get().flaNum()) ? -1 : 0);
        }
        db2check();
        if (this.isDb2Ok()) {
            areasDeTrabajo.setSwFolios(1);

        } else if (this.isDb2Notfnd()) {
            areasDeTrabajo.setSwEofFolios("S");

            if (areasDeTrabajo.getSwFolios() == 0) {
                display("2009 NO TIENE FOLIOS EL AGENTE: ", wsVariablesDb2.getAgtAgtIdr(), ", CONTRATO: ", wsVariablesDb2.getCnaCnaNum());
            }
        }
    }

    /**
     *  Paragraph: 2206-CIERRA-CUR-FLA1.
     *
     */
    void p2206CierraCurFla1() {
        wsDatosAbend.setWsProcesoEjecuta("2206 CIERRA-CUR-FLA1      ");

        wsDatosAbend.setWsInstruccion("CLOSE               ");

        wsDatosAbend.setWsTabla("CUR-FLA1            ");

        if (curFla1Cursor != null) {
            curFla1Cursor.close();
        }
        db2check();
        if (!this.isDb2Ok()) {
            display(" 2206 EL CURSOR CUR-FLA1 NO CERRO. ");
        }
    }

    /**
     *  Paragraph: 2300-COMPACTA-GER.
     *
     */
    void p2300CompactaGer() {
        wsVariablesDb2.getCna0().initialize();
        areasDeTrabajo.setSwEofGerencias("N");

        areasDeTrabajo.setSwGerencias(0);

        p2304AbreCurGer0();
        p2305FetchCurGer0();
        while (!(areasDeTrabajo.isEofGerencias()
            || areasDeTrabajo.getWsContTger() > 51)) {
            p5000ProcesaGer0();
        }
        p2306CierraCurGer0();
        if (areasDeTrabajo.getSwGerencias() == 0) {
            areasDeTrabajo.setWsContTger(1);

            //        MOVE ZEROS TO S-CNA-GER-ZON(WS-CONT-TGER)
            tablaGer0.getTGer0().at(areasDeTrabajo.getWsContTger()).getSCnaGerZon().setSpaces();
            tablaGer0.getTGer0().at(areasDeTrabajo.getWsContTger()).setSGerLimitad("|");
            p4315QuitaBlancosGer0();
        } else {
            tablaGer0.getTGer0().at(areasDeTrabajo.getWsContTger()).setSGerLimitad("|");
            wsRegSalida.getTGer0_1().at(areasDeTrabajo.getWsContTger_1()).setSCnaGerZonElem("|");
        }
    }

    /**
     *  Paragraph: 7000-INICIAL.
     *
     */
    void p7000Inicial() {
        display("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
        display("% INICIA  PROGRAMA: GGXA0100                   %");
        display("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
        archExppc.openOutput();
        archCcpc.openOutput();
        p7010AbreCurAgt0();
        p2012FetchCurAgt0();
    }

    /**
     *  Paragraph: 7010-ABRE-CUR-AGT0.
     *
     */
    void p7010AbreCurAgt0() {
        wsDatosAbend.setWsProcesoEjecuta("7010 ABRE-CUR-AGT0          ");

        wsDatosAbend.setWsInstruccion("OPEN                ");

        wsDatosAbend.setWsTabla("CUR-AGT0            ");

        curAgt0Cursor.open();
        db2check();
    }

    /**
     *  Paragraph: INIT-CONTADORES.
     *
     */
    void initContadores() {
        areasDeTrabajo.setWsContTcna(0);

        areasDeTrabajo.setWsContTfla(0);

        areasDeTrabajo.setWsContTger(0);

        areasDeTrabajo.setWsContTceo(0);

        areasDeTrabajo.setWsContTofn(0);

        areasDeTrabajo.setWsContTcna_1(0);

        areasDeTrabajo.setWsContTger_1(0);

        areasDeTrabajo.setWsContTofn_1(0);

    }

    /**
     *  Paragraph: INIT-REG-SALIDA.
     *
     */
    void initRegSalida() {
        wsRegSalida.getSAgtAgtIdr().setZero();
        wsRegSalida.getSAgtAgtNip().setZero();
        wsRegSalida.getSAgtAgtNom().setSpaces();
        wsRegSalida.getSAgtAgtApePat().setSpaces();
        wsRegSalida.getSAgtAgtApeMat().setSpaces();
        wsRegSalida.getSWsIndGerente().setSpaces();
        wsRegSalida.getSCmaCmaDes().setSpaces();
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTcna())) {
                tablaCna0.getTCna0().at(areasDeTrabajo.getWsContador()).getSCnaCnaNum().setValue(0);
                tablaCna0.getTCna0().at(areasDeTrabajo.getWsContador()).setSCnaLimitad(" ");
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTfla())) {
                wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContador()).setSFlaFlaNum1(" ");
                wsRegSalida.getTFla1().at(areasDeTrabajo.getWsContador()).setSFlaLimitad1(" ");
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTger())) {
                //     MOVE ZEROS  TO S-CNA-GER-ZON(WS-CONTADOR)                    
                tablaGer0.getTGer0().at(areasDeTrabajo.getWsContador()).getSCnaGerZon().setSpaces();
                tablaGer0.getTGer0().at(areasDeTrabajo.getWsContador()).getSGerLimitad().setSpaces();
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTceo())) {
                //     MOVE ZEROS  TO S-CEO-CEO-CVE(WS-CONTADOR)                    
                wsRegSalida.getTCeo0().at(areasDeTrabajo.getWsContador()).getSCeoCeoNom().setSpaces();
                wsRegSalida.getTCeo0().at(areasDeTrabajo.getWsContador()).getSCeoLimitad().setSpaces();
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTofn())) {
                //     MOVE ZEROS  TO S-CNA-OFN-CVE(WS-CONTADOR)                    
                tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContador()).getSCnaOfnCve().setSpaces();
                tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContador()).getSOfnLimitad().setSpaces();
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTcna_1())) {
                wsRegSalida.getTCna0_1().at(areasDeTrabajo.getWsContador()).getSCnaCnaNumElem().setSpaces();
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTger_1())) {
                wsRegSalida.getTGer0_1().at(areasDeTrabajo.getWsContador()).getSCnaGerZonElem().setSpaces();
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
        areasDeTrabajo.setWsContador(1);

        while (!(areasDeTrabajo.getWsContador() > areasDeTrabajo.getWsContTofn_1())) {
                wsRegSalida.getTOfn0_1().at(areasDeTrabajo.getWsContador()).getSCnaOfnCveElem().setSpaces();
                areasDeTrabajo.setWsContador(areasDeTrabajo.getWsContador() + 1);
        }
    }

    /**
     *  Paragraph: 4310-LLENA-TABLA-TGER0.
     *
     */
    void p4310LlenaTablaTger0() {
        wsVariablesDb2.setWsCnaGerZon(wsVariablesDb2.getCnaGerZonEnv());

        wwAuxiliares.getWtClavePos().setValue(wsVariablesDb2.getWsCnaGerZon());
        wwAuxiliares.getWtCnaGerZon().setSpaces();
        wwAuxiliares.setWkInd(1);
        while (!(wwAuxiliares.getWkInd() > 4)) {
            p4311BuscaPosicion();
            wwAuxiliares.setWkInd(wwAuxiliares.getWkInd() + 1);
        }
        indGer0 = 1;

        search(tablaGer0.getTGer0(), indGer0, () -> indGer0++, new SearchListener() {
            @Override
            public boolean accept(int i) {
                if (wwAuxiliares.getWtCnaGerZon().isEqual(tablaGer0.getTGer0().at(indGer0).getSCnaGerZon())) {
                    ;
                    return true;
                }
                return false;
            }

            @Override
            public void atEnd() {
                areasDeTrabajo.setWsContTger(areasDeTrabajo.getWsContTger() + 1);
                tablaGer0.getTGer0().at(areasDeTrabajo.getWsContTger()).setSCnaGerZon(wwAuxiliares.getWtCnaGerZon());
                tablaGer0.getTGer0().at(areasDeTrabajo.getWsContTger()).setSGerLimitad(";");
                p4315QuitaBlancosGer0();
            }
        });
        // *   WHEN WS-CNA-GER-ZON     EQUAL S-CNA-GER-ZON(IND-GER0)        
    }

    /**
     *  Paragraph: 4311-BUSCA-POSICION.
     *
     */
    void p4311BuscaPosicion() {
        if (wwAuxiliares.getWtDigito().at(wwAuxiliares.getWkInd()).compareTo(0) > 0) {
            wwAuxiliares.setWkInd2(wwAuxiliares.getWkInd());

            wwAuxiliares.setWkInd4(0);

            wwAuxiliares.setWkInd3(wwAuxiliares.getWkInd2());
            while (!(wwAuxiliares.getWkInd3() > 4)) {
                p4312MueveDigito();
                wwAuxiliares.setWkInd3(wwAuxiliares.getWkInd3() + 1);
            }
            wwAuxiliares.setWkInd(4);

        }
    }

    /**
     *  Paragraph: 4312-MUEVE-DIGITO.
     *
     */
    void p4312MueveDigito() {
        wwAuxiliares.setWkInd4(wwAuxiliares.getWkInd4() + 1);
        wwAuxiliares.getWtCnaGerZonA().at(wwAuxiliares.getWkInd4()).setValue(wwAuxiliares.getWtDigito().at(wwAuxiliares.getWkInd3()));
    }

    /**
     *  Paragraph: 4315-QUITA-BLANCOS-GER0.
     *
     */
    void p4315QuitaBlancosGer0() {
        tablasElementos.getWwTabDato5().setValue(tablaGer0.getTGer0().at(areasDeTrabajo.getWsContTger()).getTGer0Elem());
        wwAuxiliares.setWkInd5(1);
        while (!(wwAuxiliares.getWkInd5() > 5)) {
            p4320MueveElemGer0();
            wwAuxiliares.setWkInd5(wwAuxiliares.getWkInd5() + 1);
        }
    }

    /**
     *  Paragraph: 4320-MUEVE-ELEM-GER0.
     *
     */
    void p4320MueveElemGer0() {
        if (!tablasElementos.getWwCampo5().at(wwAuxiliares.getWkInd5()).isSpaces()) {
            areasDeTrabajo.setWsContTger_1(areasDeTrabajo.getWsContTger_1() + 1);
            wsRegSalida.getTGer0_1().at(areasDeTrabajo.getWsContTger_1()).setSCnaGerZonElem(tablasElementos.getWwCampo5().at(wwAuxiliares.getWkInd5()));
        }
    }

    /**
     *  Paragraph: 2304-ABRE-CUR-GER0.
     *
     */
    void p2304AbreCurGer0() {
        wsDatosAbend.setWsProcesoEjecuta("2304 ABRE-CUR-GER0        ");

        wsDatosAbend.setWsInstruccion("OPEN                ");

        wsDatosAbend.setWsTabla("CUR-GER0            ");

        curGer0Cursor.open();
        db2check();
    }

    /**
     *  Paragraph: 2305-FETCH-CUR-GER0.
     *
     */
    void p2305FetchCurGer0() {
        wsDatosAbend.setWsProcesoEjecuta("2305 FETCH-GER0           ");

        wsDatosAbend.setWsInstruccion("FETCH               ");

        wsDatosAbend.setWsTabla("GFVTGER0            ");

        gfvtcna0Model = curGer0Cursor.next();

        if (gfvtcna0Model.isPresent()) {
            wsVariablesDb2.setCnaGerZonEnv(fromHostInt(gfvtcna0Model.get().cnaGerZonEnv()));
            wsExtractoNull.setCnaGerZonEnv1(isNull(gfvtcna0Model.get().cnaGerZonEnv()) ? -1 : 0);
        }
        db2check();
        if (this.isDb2Ok()) {
            areasDeTrabajo.setSwGerencias(1);

        } else if (this.isDb2Notfnd()) {
            areasDeTrabajo.setSwEofGerencias("S");

        }
    }

    /**
     *  Paragraph: 5000-PROCESA-GER0.
     *
     */
    void p5000ProcesaGer0() {
        p4310LlenaTablaTger0();
        wsVariablesDb2.setCnaGerZonEnv(0);

        p2305FetchCurGer0();
    }

    /**
     *  Paragraph: 2306-CIERRA-CUR-GER0.
     *
     */
    void p2306CierraCurGer0() {
        wsDatosAbend.setWsProcesoEjecuta("2306 CIERRA-CUR-GER0      ");

        wsDatosAbend.setWsInstruccion("CLOSE               ");

        wsDatosAbend.setWsTabla("CUR-GER0            ");

        if (curGer0Cursor != null) {
            curGer0Cursor.close();
        }
        db2check();
        if (!this.isDb2Ok()) {
            display(" 2306 EL CURSOR CUR-GER0 NO CERRO. ");
        }
    }

    /**
     *  Paragraph: 2400-COMPACTA-REGION.
     *
     */
    void p2400CompactaRegion() {
        wsVariablesDb2.getCna0().initialize();
        areasDeTrabajo.setSwEofRegiones("N");

        areasDeTrabajo.setSwRegiones(0);

        p2404AbreCurCeo0();
        p2405FetchCurCeo0();
        while (!(areasDeTrabajo.isEofRegiones()
            || areasDeTrabajo.getWsContTceo() > 51)) {
            p6000ProcesaCeo0();
        }
        p2406CierraCurCeo0();
        if (areasDeTrabajo.getSwRegiones() == 0) {
            areasDeTrabajo.setWsContTceo(1);

            //        MOVE ZEROS TO S-CEO-CEO-CVE(WS-CONT-TCEO)
            wsRegSalida.getTCeo0().at(areasDeTrabajo.getWsContTceo()).getSCeoCeoNom().setSpaces();
            wsRegSalida.getTCeo0().at(areasDeTrabajo.getWsContTceo()).setSCeoLimitad("|");
        } else {
            wsRegSalida.getTCeo0().at(areasDeTrabajo.getWsContTceo()).setSCeoLimitad("|");
        }
    }

    /**
     *  Paragraph: 4410-LLENA-TABLA-TCEO0.
     *
     */
    void p4410LlenaTablaTceo0() {
        wsVariablesDb2.setWsCeoCeoCve(wsVariablesDb2.getCeoCeoCve());

        wsVariablesDb2.setWsCeoCeoNom(wsVariablesDb2.getCeoCeoNom());

        indCeo0 = 1;

        search(wsRegSalida.getTCeo0(), indCeo0, () -> indCeo0++, new SearchListener() {
            @Override
            public boolean accept(int i) {
                if (Functions.isEqual(wsVariablesDb2.getWsCeoCeoNom(), wsRegSalida.getTCeo0().at(indCeo0).getSCeoCeoNom())) {
                    ;
                    return true;
                }
                return false;
            }

            @Override
            public void atEnd() {
                areasDeTrabajo.setWsContTceo(areasDeTrabajo.getWsContTceo() + 1);
                wsRegSalida.getTCeo0().at(areasDeTrabajo.getWsContTceo()).setSCeoCeoNom(wsVariablesDb2.getCeoCeoNom());
                wsRegSalida.getTCeo0().at(areasDeTrabajo.getWsContTceo()).setSCeoLimitad(";");
            }
        });
        //     WHEN WS-CEO-CEO-CVE EQUAL S-CEO-CEO-CVE(IND-CEO0)            
    }

    /**
     *  Paragraph: 2404-ABRE-CUR-CEO0.
     *
     */
    void p2404AbreCurCeo0() {
        wsDatosAbend.setWsProcesoEjecuta("2404 ABRE-CUR-CEO0        ");

        wsDatosAbend.setWsInstruccion("OPEN                ");

        wsDatosAbend.setWsTabla("CUR-CEO0            ");

        curCeo0Cursor.open();
        db2check();
    }

    /**
     *  Paragraph: 2405-FETCH-CUR-CEO0.
     *
     */
    void p2405FetchCurCeo0() {
        wsDatosAbend.setWsProcesoEjecuta("2405 FETCH-CEO0           ");

        wsDatosAbend.setWsInstruccion("FETCH               ");

        wsDatosAbend.setWsTabla("GFVTCEO0            ");

        genericModel = curCeo0Cursor.next();

        if (genericModel.isPresent()) {
            wsVariablesDb2.setCeoCeoCve(fromHostInt(genericModel.get().values().get(0)));
            wsExtractoNull.setCeoCeoCve1(isNull(genericModel.get().values().get(0)) ? -1 : 0);
            wsVariablesDb2.setCeoCeoNom(fromHostString(genericModel.get().values().get(1)));
            wsExtractoNull.setCeoCeoNom1(isNull(genericModel.get().values().get(1)) ? -1 : 0);
        }
        db2check();
        if (this.isDb2Ok()) {
            areasDeTrabajo.setSwRegiones(1);

        } else if (this.isDb2Notfnd()) {
            areasDeTrabajo.setSwEofRegiones("S");

        }
    }

    /**
     *  Paragraph: 6000-PROCESA-CEO0.
     *
     */
    void p6000ProcesaCeo0() {
        p4410LlenaTablaTceo0();
        wsVariablesDb2.setCeoCeoCve(0);

        wsVariablesDb2.setCeoCeoNom("");

        p2405FetchCurCeo0();
    }

    /**
     *  Paragraph: 2406-CIERRA-CUR-CEO0.
     *
     */
    void p2406CierraCurCeo0() {
        wsDatosAbend.setWsProcesoEjecuta("2406 CIERRA-CUR-CEO0      ");

        wsDatosAbend.setWsInstruccion("CLOSE               ");

        wsDatosAbend.setWsTabla("CUR-CEO0            ");

        if (curCeo0Cursor != null) {
            curCeo0Cursor.close();
        }
        db2check();
        if (!this.isDb2Ok()) {
            display(" 2406 EL CURSOR CUR-CEO0 NO CERRO. ");
        }
    }

    /**
     *  Paragraph: 2500-COMPACTA-OFICINA.
     *
     */
    void p2500CompactaOficina() {
        wsVariablesDb2.getCna0().initialize();
        areasDeTrabajo.setSwEofOficinas("N");

        areasDeTrabajo.setSwOficinas(0);

        p2504AbreCurOfn0();
        p2505FetchCurOfn0();
        while (!(areasDeTrabajo.isEofOficinas()
            || areasDeTrabajo.getWsContTofn() > 51)) {
            p6100ProcesaOfn0();
        }
        p2506CierraCurOfn0();
        if (areasDeTrabajo.getSwOficinas() == 0) {
            areasDeTrabajo.setWsContTofn(1);

            //        MOVE ZEROS TO S-CNA-OFN-CVE(WS-CONT-TOFN)
            tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContTofn()).getSCnaOfnCve().setSpaces();
            tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContTofn()).setSOfnLimitad("|");
            p5315QuitaBlancosOfn0();
        } else {
            tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContTofn()).setSOfnLimitad("|");
            wsRegSalida.getTOfn0_1().at(areasDeTrabajo.getWsContTofn_1()).setSCnaOfnCveElem("|");
        }
    }

    /**
     *  Paragraph: 5310-LLENA-TABLA-TOFN0.
     *
     */
    void p5310LlenaTablaTofn0() {
        wsVariablesDb2.setWsCnaOfnCve(wsVariablesDb2.getCnaOfnCve());

        wwAuxiliares.getWtClavePos().setValue(wsVariablesDb2.getWsCnaOfnCve());
        wwAuxiliares.getWtCnaOfnCve().setSpaces();
        wwAuxiliares.setWkInd(1);
        while (!(wwAuxiliares.getWkInd() > 4)) {
            p5311BuscaPosicion();
            wwAuxiliares.setWkInd(wwAuxiliares.getWkInd() + 1);
        }
        indOfn0 = 1;

        search(tablaOfn0.getTOfn0(), indOfn0, () -> indOfn0++, new SearchListener() {
            @Override
            public boolean accept(int i) {
                if (wwAuxiliares.getWtCnaOfnCve().isEqual(tablaOfn0.getTOfn0().at(indOfn0).getSCnaOfnCve())) {
                    ;
                    return true;
                }
                return false;
            }

            @Override
            public void atEnd() {
                areasDeTrabajo.setWsContTofn(areasDeTrabajo.getWsContTofn() + 1);
                tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContTofn()).setSCnaOfnCve(wwAuxiliares.getWtCnaOfnCve());
                tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContTofn()).setSOfnLimitad(";");
                p5315QuitaBlancosOfn0();
            }
        });
        //     WHEN WS-CNA-OFN-CVE EQUAL S-CNA-OFN-CVE(IND-OFN0)            
    }

    /**
     *  Paragraph: 5311-BUSCA-POSICION.
     *
     */
    void p5311BuscaPosicion() {
        if (wwAuxiliares.getWtDigito().at(wwAuxiliares.getWkInd()).compareTo(0) > 0) {
            wwAuxiliares.setWkInd2(wwAuxiliares.getWkInd());

            wwAuxiliares.setWkInd4(0);

            wwAuxiliares.setWkInd3(wwAuxiliares.getWkInd2());
            while (!(wwAuxiliares.getWkInd3() > 4)) {
                p5312MueveDigito();
                wwAuxiliares.setWkInd3(wwAuxiliares.getWkInd3() + 1);
            }
            wwAuxiliares.setWkInd(4);

        }
    }

    /**
     *  Paragraph: 5312-MUEVE-DIGITO.
     *
     */
    void p5312MueveDigito() {
        wwAuxiliares.setWkInd4(wwAuxiliares.getWkInd4() + 1);
        wwAuxiliares.getWtCnaOfnCveA().at(wwAuxiliares.getWkInd4()).setValue(wwAuxiliares.getWtDigito().at(wwAuxiliares.getWkInd3()));
    }

    /**
     *  Paragraph: 5315-QUITA-BLANCOS-OFN0.
     *
     */
    void p5315QuitaBlancosOfn0() {
        tablasElementos.getWwTabDato5().setValue(tablaOfn0.getTOfn0().at(areasDeTrabajo.getWsContTofn()).getTOfn0Elem());
        wwAuxiliares.setWkInd5(1);
        while (!(wwAuxiliares.getWkInd5() > 5)) {
            p5320MueveElemOfn0();
            wwAuxiliares.setWkInd5(wwAuxiliares.getWkInd5() + 1);
        }
    }

    /**
     *  Paragraph: 5320-MUEVE-ELEM-OFN0.
     *
     */
    void p5320MueveElemOfn0() {
        if (!tablasElementos.getWwCampo5().at(wwAuxiliares.getWkInd5()).isSpaces()) {
            areasDeTrabajo.setWsContTofn_1(areasDeTrabajo.getWsContTofn_1() + 1);
            wsRegSalida.getTOfn0_1().at(areasDeTrabajo.getWsContTofn_1()).setSCnaOfnCveElem(tablasElementos.getWwCampo5().at(wwAuxiliares.getWkInd5()));
        }
    }

    /**
     *  Paragraph: 2504-ABRE-CUR-OFN0.
     *
     */
    void p2504AbreCurOfn0() {
        wsDatosAbend.setWsProcesoEjecuta("2504 ABRE-CUR-OFN0        ");

        wsDatosAbend.setWsInstruccion("OPEN                ");

        wsDatosAbend.setWsTabla("CUR-OFN0            ");

        curOfn0Cursor.open();
        db2check();
    }

    /**
     *  Paragraph: 2505-FETCH-CUR-OFN0.
     *
     */
    void p2505FetchCurOfn0() {
        wsDatosAbend.setWsProcesoEjecuta("2505 FETCH-OFN0           ");

        wsDatosAbend.setWsInstruccion("FETCH               ");

        wsDatosAbend.setWsTabla("GFVTOFN0            ");

        gfvtcna0Model = curOfn0Cursor.next();

        if (gfvtcna0Model.isPresent()) {
            wsVariablesDb2.setCnaOfnCve(fromHostInt(gfvtcna0Model.get().ofnCve()));
            wsExtractoNull.setCnaOfnCve1(isNull(gfvtcna0Model.get().ofnCve()) ? -1 : 0);
        }
        db2check();
        if (this.isDb2Ok()) {
            areasDeTrabajo.setSwOficinas(1);

        } else if (this.isDb2Notfnd()) {
            areasDeTrabajo.setSwEofOficinas("S");

        }
    }

    /**
     *  Paragraph: 6100-PROCESA-OFN0.
     *
     */
    void p6100ProcesaOfn0() {
        p5310LlenaTablaTofn0();
        wsVariablesDb2.setCnaOfnCve(0);

        p2505FetchCurOfn0();
    }

    /**
     *  Paragraph: 2506-CIERRA-CUR-OFN0.
     *
     */
    void p2506CierraCurOfn0() {
        wsDatosAbend.setWsProcesoEjecuta("2506 CIERRA-CUR-OFN0      ");

        wsDatosAbend.setWsInstruccion("CLOSE               ");

        wsDatosAbend.setWsTabla("CUR-OFN0            ");

        if (curOfn0Cursor != null) {
            curOfn0Cursor.close();
        }
        db2check();
        if (!this.isDb2Ok()) {
            display(" 2506 EL CURSOR CUR-OFN0 NO CERRO. ");
        }
    }

    /**
     *  Paragraph: 8000-FINAL.
     *
     */
    void p8000Final() {
        wsDatosAbend.setWsProcesoEjecuta("8000 FINAL             ");

        wsDatosAbend.setWsInstruccion("CLOSE               ");

        wsDatosAbend.setWsTabla("CUR-AGT0         ");

        areasDeTrabajo.getWsEditLeidos().setValue(areasDeTrabajo.getWsContAgt0());
        areasDeTrabajo.getWsEditGrabado().setValue(areasDeTrabajo.getWsContGrabas());
        p8100GrabaCifrasControl();
        display("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
        display("% TERMINA PROGRAMA: GGXA0100                   %");
        display("%                                              %");
        display("% REGISTROS FOLIOS LEIDOS :", areasDeTrabajo.getWsEditLeidos(), "     %");
        display("% REGISTROS GRABADOS......:", areasDeTrabajo.getWsEditGrabado(), "     %");
        display("%                                              %");
        display("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%");
        archExppc.close();
        if (curAgt0Cursor != null) {
            curAgt0Cursor.close();
        }
        db2check();
        if (!this.isDb2Ok()) {
            display(" EL CURSOR CUR-AGT0 NO CERRO. ");
        }
    }

    /**
     *  Paragraph: DB2CHECK.
     *
     * RUTINA QUE ANALIZA CODIGO VALIDO.   DB2CHECK                 *
     */
    void db2check() {
        db2ReturnCode = sqlca.getSqlcode();

        //         *DB2-NOTUNI    OR**
        if (this.isDb2Ok() 
            || this.isDb2Notfnd() 
            || this.isDb2Duprec()) {
            ;
        } else {
            p9000Abend();
        }
    }

    /**
     *  Paragraph: DB2CHECK-EMAIL.
     *
     * RUTINA QUE ANALIZA CODIGO VALIDO.   DB2CHECK-EMAIL           *
     */
    void db2checkEmail() {
        db2ReturnCode = sqlca.getSqlcode();

        if (this.isDb2Ok()
            || this.isDb2Notfnd() 
            || this.isDb2Notuni() 
            || this.isDb2Duprec()) {
            ;
        } else {
            p9000Abend();
        }
    }

    /**
     *  Paragraph: 8100-GRABA-CIFRAS-CONTROL.
     *
     * RUTINA QUE GRABA CIFRAS CONTROL                               *
     * *================ANALIZA========================================*
     */
    void p8100GrabaCifrasControl() {
        //     MOVE 'REGISTROS DE FOLIOS LEIDOS : '  TO  REG-CC-DESCRIPCION 
        //     MOVE WS-EDIT-LEIDOS                   TO  REG-CC-CANTIDAD    
        //     MOVE REG-CIFRAS-CONTROL               TO  FD-REG-CCPC        
        //     WRITE FD-REG-CCPC                                            
        regCifrasControl.setRegCcDescripcion("REGISTROS GRABADOS PORTAL  : ");
        regCifrasControl.setRegCcCantidad(areasDeTrabajo.getWsEditGrabado());
        fdRegCcpc.setValue(regCifrasControl);
        archCcpc.write(fdRegCcpc);
    }

    /**
     *  Paragraph: 9000-ABEND.
     *
     * RUTINA QUE ANALIZA EL CODIGO DE DB2                           *
     * *================ANALIZA========================================*
     */
    void p9000Abend() {
        if (sqlca.getSqlcode() < 0) {
            wsCodigos.setWsSqlcode(Numeric.multiply(sqlca.getSqlcode(), (-1)).toInt());
            wsCodigos.setWsDis1("-");
            wsCodigos.setWsDis2(wsCodigos.getWsSqlcode());
        } else {
            wsCodigos.setWsDis1("+");
            wsCodigos.setWsDis2(sqlca.getSqlcode());
        }
        display("============ ERROR DB2 ============");
        display("  ");
        display("CODIGO DE ERROR    => ", wsCodigos.getWsCodError());
        display("INSTRUCCION SQL    => ", wsDatosAbend.getWsInstruccion());
        display("RUTINA LLAMADORA   => ", wsDatosAbend.getWsProcesoLlama());
        display("RUTINA EJECUTA     => ", wsDatosAbend.getWsProcesoEjecuta());
        display("TABLA              => ", wsDatosAbend.getWsTabla());
        display("PROGRAMA           => ", wsDatosAbend.getWsPrograma());
        display("SQLERRM            => ", sqlca.getSqlerrm());
        display("SQLERRP            => ", sqlca.getSqlerrp());
        getSqlExecutor().rollback(sqlca);
        if (sqlca.getSqlerrml() > 0) {
            display("VARIABLES ERRONEAS    . . .: ", sqlca.getSqlerrmc());
        }
        if (sqlca.getSqlwarn5().equals("W")) {
            display("MANDATO NO EJECUTADO  . . .: ");
        }
        if (sqlca.getSqlwarn6().equals("W")) {
            display("VALOR DE FECHA AJUSTADO . .: ");
        }
        if (sqlca.getSqlwarn9().equals("W")) {
            display("EXCEPCION ARITMET IGNORADA.: ");
        }
        throw new GobackException();
        /* [@WARNING Unreachable code]
        throw new StopRunException();
        */
    }

    // Conditionals (88) - DB2-RETURN-CODE
    public boolean isDb2Ok() {
        return this.db2ReturnCode == DB2_OK;
    }

    public void setDb2Ok() {
        this.db2ReturnCode = DB2_OK;
    }

    public boolean isDb2Notfnd() {
        return this.db2ReturnCode == DB2_NOTFND;
    }

    public void setDb2Notfnd() {
        this.db2ReturnCode = DB2_NOTFND;
    }

    public boolean isDb2Duprec() {
        return this.db2ReturnCode == DB2_DUPREC;
    }

    public void setDb2Duprec() {
        this.db2ReturnCode = DB2_DUPREC;
    }

    public boolean isDb2Notuni() {
        return this.db2ReturnCode == DB2_NOTUNI;
    }

    public void setDb2Notuni() {
        this.db2ReturnCode = DB2_NOTUNI;
    }

    public boolean isDb2Datetime() {
        return this.db2ReturnCode == DB2_DATETIME;
    }

    public void setDb2Datetime() {
        this.db2ReturnCode = DB2_DATETIME;
    }

    public boolean isDb2System() {
        return this.db2ReturnCode == DB2_SYSTEM;
    }

    public void setDb2System() {
        this.db2ReturnCode = DB2_SYSTEM;
    }



    @Autowired
    public void setGfvtcna0Port(IGfvtcna0Port gfvtcna0Port) {
        this.gfvtcna0Port = gfvtcna0Port;
        this.gfvtcna0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtcma0Port(IGfvtcma0Port gfvtcma0Port) {
        this.gfvtcma0Port = gfvtcma0Port;
        this.gfvtcma0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctorv0Port(IGcctorv0Port gcctorv0Port) {
        this.gcctorv0Port = gcctorv0Port;
        this.gcctorv0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagt0Port(IGfvtagt0Port gfvtagt0Port) {
        this.gfvtagt0Port = gfvtagt0Port;
        this.gfvtagt0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtfla0Port(IGfvtfla0Port gfvtfla0Port) {
        this.gfvtfla0Port = gfvtfla0Port;
        this.gfvtfla0Port.setProgramContext(getProgramContext());
    }
}
