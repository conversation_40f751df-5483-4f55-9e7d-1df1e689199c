package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsconstantes;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsconstantes.plhjconstantes.PlhjEstadosCveTrac;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PLHJ-CONSTANTES.
 *
 */
// 773      ******************************************************************00010000
// 774      *  COPY...... GNPL00HJ                                           *00020000
// 775      *  FUNCION... DEFINICION DE CONSTANTES DEL PROGRAMA GNPLQY00     *00030000
// 776      *             (MARKETER INTERFACE).                              *00040000
// 777      *  AUTOR..... GERARDO AXOTLA MU#OZ. (C.T.I.)                     *00050000
// 778      *  FECHA..... MARZO 2002                                         *00060000
// 779      *  LRECL.....                                                    *00070000
// 780      *  MODIFICO..                                                    *00080000
// 781      ******************************************************************00090000
@Setter
@Getter
public class PlhjConstantes extends DataStruct {
    // Properties
    private AlphanumericVar plhjDireccionGnp1 = new AlphanumericVar(this, 35);               // 10 PLHJ-DIRECCION-GNP1 X(35)
    private AlphanumericVar plhjDireccionGnp2 = new AlphanumericVar(this, 35);               // 10 PLHJ-DIRECCION-GNP2 X(35)
    private AlphanumericVar plhjDireccionGnp3 = new AlphanumericVar(this, 35);               // 10 PLHJ-DIRECCION-GNP3 X(35)
    private AlphanumericVar plhjNombreGz = new AlphanumericVar(this, 24);    // 10 PLHJ-NOMBRE-GZ X(24)
    private AlphanumericVar plhjNumeros = new AlphanumericVar(this, 1);      // 10 PLHJ-NUMEROS X(01)
    // Level 88 - Conditional names
    private static final String PLHJ_NUMS$1 = "0";
    private static final String PLHJ_NUMS$2 = "1";
    private static final String PLHJ_NUMS$3 = "2";
    private static final String PLHJ_NUMS$4 = "3";
    private static final String PLHJ_NUMS$5 = "4";
    private static final String PLHJ_NUMS$6 = "5";
    private static final String PLHJ_NUMS$7 = "6";
    private static final String PLHJ_NUMS$8 = "7";
    private static final String PLHJ_NUMS$9 = "8";
    private static final String PLHJ_NUMS$10 = "9";

    private AlphanumericVar plhjEstadosSaga = new AlphanumericVar(this, 2);  // 10 PLHJ-ESTADOS-SAGA X(02)
    // Level 88 - Conditional names
    private static final String PLHJ_AGS = "01";
    private static final String PLHJ_BCN = "02";
    private static final String PLHJ_BCS = "32";
    private static final String PLHJ_CAMPECHE = "03";
    private static final String PLHJ_COAHUILA = "04";
    private static final String PLHJ_COLIMA = "05";
    private static final String PLHJ_CHIAPAS = "06";
    private static final String PLHJ_CHIHUAHUA = "07";
    private static final String PLHJ_DF = "08";
    private static final String PLHJ_DURANGO = "09";
    private static final String PLHJ_GTO = "10";
    private static final String PLHJ_GUERRERO = "11";
    private static final String PLHJ_HIDALGO = "12";
    private static final String PLHJ_JALISCO = "13";
    private static final String PLHJ_EDOMEX$1 = "14";
    private static final String PLHJ_EDOMEX$2 = "34";
    private static final String PLHJ_MICHOACAN = "15";
    private static final String PLHJ_MORELOS = "16";
    private static final String PLHJ_NAYARIT = "17";
    private static final String PLHJ_NUEVOLEON = "18";
    private static final String PLHJ_OAXACA = "19";
    private static final String PLHJ_PUEBLA = "20";
    private static final String PLHJ_QUERETARO = "21";
    private static final String PLHJ_QROO = "22";
    private static final String PLHJ_SLP = "23";
    private static final String PLHJ_SINALOA = "24";
    private static final String PLHJ_SONORA = "25";
    private static final String PLHJ_TABASCO = "26";
    private static final String PLHJ_TAMAULIPAS = "27";
    private static final String PLHJ_TLAXCALA = "28";
    private static final String PLHJ_VERACRUZ = "29";
    private static final String PLHJ_YUCATAN = "30";
    private static final String PLHJ_ZACATECAS = "31";
    private static final String PLHJ_EXTRANJERO = "33";
    private static final String PLHJ_OTROS = "  ";

    private PlhjEstadosCveTrac plhjEstadosCveTrac = new  PlhjEstadosCveTrac(this);           // 10 PLHJ-ESTADOS-CVE-TRAC

    public PlhjConstantes() {
        super();
        initialize();
    }

    public PlhjConstantes(DataContainer parent) {
        super(parent);
    }

    public PlhjConstantes(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPlhjDireccionGnp1(final String plhjDireccionGnp1) {
        this.plhjDireccionGnp1.setValue(plhjDireccionGnp1);
    }

    public void setPlhjDireccionGnp1(final IAlphanumericValue plhjDireccionGnp1) {
        this.plhjDireccionGnp1.setValue(plhjDireccionGnp1);
    }

    public void setPlhjDireccionGnp2(final String plhjDireccionGnp2) {
        this.plhjDireccionGnp2.setValue(plhjDireccionGnp2);
    }

    public void setPlhjDireccionGnp2(final IAlphanumericValue plhjDireccionGnp2) {
        this.plhjDireccionGnp2.setValue(plhjDireccionGnp2);
    }

    public void setPlhjDireccionGnp3(final String plhjDireccionGnp3) {
        this.plhjDireccionGnp3.setValue(plhjDireccionGnp3);
    }

    public void setPlhjDireccionGnp3(final IAlphanumericValue plhjDireccionGnp3) {
        this.plhjDireccionGnp3.setValue(plhjDireccionGnp3);
    }

    public void setPlhjNombreGz(final String plhjNombreGz) {
        this.plhjNombreGz.setValue(plhjNombreGz);
    }

    public void setPlhjNombreGz(final IAlphanumericValue plhjNombreGz) {
        this.plhjNombreGz.setValue(plhjNombreGz);
    }

    public void setPlhjNumeros(final String plhjNumeros) {
        this.plhjNumeros.setValue(plhjNumeros);
    }

    public void setPlhjNumeros(final IAlphanumericValue plhjNumeros) {
        this.plhjNumeros.setValue(plhjNumeros);
    }

    // Conditionals (88) - PLHJ-NUMEROS
    public boolean isPlhjNums() {
        return this.plhjNumeros.isEqual(PLHJ_NUMS$1) || this.plhjNumeros.isEqual(PLHJ_NUMS$2) || this.plhjNumeros.isEqual(PLHJ_NUMS$3) || this.plhjNumeros.isEqual(PLHJ_NUMS$4) || this.plhjNumeros.isEqual(PLHJ_NUMS$5) || this.plhjNumeros.isEqual(PLHJ_NUMS$6) || this.plhjNumeros.isEqual(PLHJ_NUMS$7) || this.plhjNumeros.isEqual(PLHJ_NUMS$8) || this.plhjNumeros.isEqual(PLHJ_NUMS$9) || this.plhjNumeros.isEqual(PLHJ_NUMS$10);
    }

    public void setPlhjNums() {
        this.plhjNumeros.setValue(PLHJ_NUMS$1);
    }


    public void setPlhjEstadosSaga(final String plhjEstadosSaga) {
        this.plhjEstadosSaga.setValue(plhjEstadosSaga);
    }

    public void setPlhjEstadosSaga(final IAlphanumericValue plhjEstadosSaga) {
        this.plhjEstadosSaga.setValue(plhjEstadosSaga);
    }

    // Conditionals (88) - PLHJ-ESTADOS-SAGA
    public boolean isPlhjAgs() {
        return this.plhjEstadosSaga.isEqual(PLHJ_AGS);
    }

    public void setPlhjAgs() {
        this.plhjEstadosSaga.setValue(PLHJ_AGS);
    }

    public boolean isPlhjBcn() {
        return this.plhjEstadosSaga.isEqual(PLHJ_BCN);
    }

    public void setPlhjBcn() {
        this.plhjEstadosSaga.setValue(PLHJ_BCN);
    }

    public boolean isPlhjBcs() {
        return this.plhjEstadosSaga.isEqual(PLHJ_BCS);
    }

    public void setPlhjBcs() {
        this.plhjEstadosSaga.setValue(PLHJ_BCS);
    }

    public boolean isPlhjCampeche() {
        return this.plhjEstadosSaga.isEqual(PLHJ_CAMPECHE);
    }

    public void setPlhjCampeche() {
        this.plhjEstadosSaga.setValue(PLHJ_CAMPECHE);
    }

    public boolean isPlhjCoahuila() {
        return this.plhjEstadosSaga.isEqual(PLHJ_COAHUILA);
    }

    public void setPlhjCoahuila() {
        this.plhjEstadosSaga.setValue(PLHJ_COAHUILA);
    }

    public boolean isPlhjColima() {
        return this.plhjEstadosSaga.isEqual(PLHJ_COLIMA);
    }

    public void setPlhjColima() {
        this.plhjEstadosSaga.setValue(PLHJ_COLIMA);
    }

    public boolean isPlhjChiapas() {
        return this.plhjEstadosSaga.isEqual(PLHJ_CHIAPAS);
    }

    public void setPlhjChiapas() {
        this.plhjEstadosSaga.setValue(PLHJ_CHIAPAS);
    }

    public boolean isPlhjChihuahua() {
        return this.plhjEstadosSaga.isEqual(PLHJ_CHIHUAHUA);
    }

    public void setPlhjChihuahua() {
        this.plhjEstadosSaga.setValue(PLHJ_CHIHUAHUA);
    }

    public boolean isPlhjDf() {
        return this.plhjEstadosSaga.isEqual(PLHJ_DF);
    }

    public void setPlhjDf() {
        this.plhjEstadosSaga.setValue(PLHJ_DF);
    }

    public boolean isPlhjDurango() {
        return this.plhjEstadosSaga.isEqual(PLHJ_DURANGO);
    }

    public void setPlhjDurango() {
        this.plhjEstadosSaga.setValue(PLHJ_DURANGO);
    }

    public boolean isPlhjGto() {
        return this.plhjEstadosSaga.isEqual(PLHJ_GTO);
    }

    public void setPlhjGto() {
        this.plhjEstadosSaga.setValue(PLHJ_GTO);
    }

    public boolean isPlhjGuerrero() {
        return this.plhjEstadosSaga.isEqual(PLHJ_GUERRERO);
    }

    public void setPlhjGuerrero() {
        this.plhjEstadosSaga.setValue(PLHJ_GUERRERO);
    }

    public boolean isPlhjHidalgo() {
        return this.plhjEstadosSaga.isEqual(PLHJ_HIDALGO);
    }

    public void setPlhjHidalgo() {
        this.plhjEstadosSaga.setValue(PLHJ_HIDALGO);
    }

    public boolean isPlhjJalisco() {
        return this.plhjEstadosSaga.isEqual(PLHJ_JALISCO);
    }

    public void setPlhjJalisco() {
        this.plhjEstadosSaga.setValue(PLHJ_JALISCO);
    }

    public boolean isPlhjEdomex() {
        return this.plhjEstadosSaga.isEqual(PLHJ_EDOMEX$1) || this.plhjEstadosSaga.isEqual(PLHJ_EDOMEX$2);
    }

    public void setPlhjEdomex() {
        this.plhjEstadosSaga.setValue(PLHJ_EDOMEX$1);
    }

    public boolean isPlhjMichoacan() {
        return this.plhjEstadosSaga.isEqual(PLHJ_MICHOACAN);
    }

    public void setPlhjMichoacan() {
        this.plhjEstadosSaga.setValue(PLHJ_MICHOACAN);
    }

    public boolean isPlhjMorelos() {
        return this.plhjEstadosSaga.isEqual(PLHJ_MORELOS);
    }

    public void setPlhjMorelos() {
        this.plhjEstadosSaga.setValue(PLHJ_MORELOS);
    }

    public boolean isPlhjNayarit() {
        return this.plhjEstadosSaga.isEqual(PLHJ_NAYARIT);
    }

    public void setPlhjNayarit() {
        this.plhjEstadosSaga.setValue(PLHJ_NAYARIT);
    }

    public boolean isPlhjNuevoleon() {
        return this.plhjEstadosSaga.isEqual(PLHJ_NUEVOLEON);
    }

    public void setPlhjNuevoleon() {
        this.plhjEstadosSaga.setValue(PLHJ_NUEVOLEON);
    }

    public boolean isPlhjOaxaca() {
        return this.plhjEstadosSaga.isEqual(PLHJ_OAXACA);
    }

    public void setPlhjOaxaca() {
        this.plhjEstadosSaga.setValue(PLHJ_OAXACA);
    }

    public boolean isPlhjPuebla() {
        return this.plhjEstadosSaga.isEqual(PLHJ_PUEBLA);
    }

    public void setPlhjPuebla() {
        this.plhjEstadosSaga.setValue(PLHJ_PUEBLA);
    }

    public boolean isPlhjQueretaro() {
        return this.plhjEstadosSaga.isEqual(PLHJ_QUERETARO);
    }

    public void setPlhjQueretaro() {
        this.plhjEstadosSaga.setValue(PLHJ_QUERETARO);
    }

    public boolean isPlhjQroo() {
        return this.plhjEstadosSaga.isEqual(PLHJ_QROO);
    }

    public void setPlhjQroo() {
        this.plhjEstadosSaga.setValue(PLHJ_QROO);
    }

    public boolean isPlhjSlp() {
        return this.plhjEstadosSaga.isEqual(PLHJ_SLP);
    }

    public void setPlhjSlp() {
        this.plhjEstadosSaga.setValue(PLHJ_SLP);
    }

    public boolean isPlhjSinaloa() {
        return this.plhjEstadosSaga.isEqual(PLHJ_SINALOA);
    }

    public void setPlhjSinaloa() {
        this.plhjEstadosSaga.setValue(PLHJ_SINALOA);
    }

    public boolean isPlhjSonora() {
        return this.plhjEstadosSaga.isEqual(PLHJ_SONORA);
    }

    public void setPlhjSonora() {
        this.plhjEstadosSaga.setValue(PLHJ_SONORA);
    }

    public boolean isPlhjTabasco() {
        return this.plhjEstadosSaga.isEqual(PLHJ_TABASCO);
    }

    public void setPlhjTabasco() {
        this.plhjEstadosSaga.setValue(PLHJ_TABASCO);
    }

    public boolean isPlhjTamaulipas() {
        return this.plhjEstadosSaga.isEqual(PLHJ_TAMAULIPAS);
    }

    public void setPlhjTamaulipas() {
        this.plhjEstadosSaga.setValue(PLHJ_TAMAULIPAS);
    }

    public boolean isPlhjTlaxcala() {
        return this.plhjEstadosSaga.isEqual(PLHJ_TLAXCALA);
    }

    public void setPlhjTlaxcala() {
        this.plhjEstadosSaga.setValue(PLHJ_TLAXCALA);
    }

    public boolean isPlhjVeracruz() {
        return this.plhjEstadosSaga.isEqual(PLHJ_VERACRUZ);
    }

    public void setPlhjVeracruz() {
        this.plhjEstadosSaga.setValue(PLHJ_VERACRUZ);
    }

    public boolean isPlhjYucatan() {
        return this.plhjEstadosSaga.isEqual(PLHJ_YUCATAN);
    }

    public void setPlhjYucatan() {
        this.plhjEstadosSaga.setValue(PLHJ_YUCATAN);
    }

    public boolean isPlhjZacatecas() {
        return this.plhjEstadosSaga.isEqual(PLHJ_ZACATECAS);
    }

    public void setPlhjZacatecas() {
        this.plhjEstadosSaga.setValue(PLHJ_ZACATECAS);
    }

    public boolean isPlhjExtranjero() {
        return this.plhjEstadosSaga.isEqual(PLHJ_EXTRANJERO);
    }

    public void setPlhjExtranjero() {
        this.plhjEstadosSaga.setValue(PLHJ_EXTRANJERO);
    }

    public boolean isPlhjOtros() {
        return this.plhjEstadosSaga.isEqual(PLHJ_OTROS);
    }

    public void setPlhjOtros() {
        this.plhjEstadosSaga.setValue(PLHJ_OTROS);
    }



    // Deeper properties

    // Property: plhjEstadosCveTrac.plhjAgst -> 15 PLHJ-AGST X(03)
    public AlphanumericVar getPlhjAgst() {
        return this.plhjEstadosCveTrac.getPlhjAgst();
    }

    public void setPlhjAgst(final String plhjAgst) {
        this.plhjEstadosCveTrac.setPlhjAgst(plhjAgst);
    }

    public void setPlhjAgst(final IAlphanumericValue plhjAgst) {
        this.plhjEstadosCveTrac.setPlhjAgst(plhjAgst);
    }

    // Property: plhjEstadosCveTrac.plhjBcnt -> 15 PLHJ-BCNT X(03)
    public AlphanumericVar getPlhjBcnt() {
        return this.plhjEstadosCveTrac.getPlhjBcnt();
    }

    public void setPlhjBcnt(final String plhjBcnt) {
        this.plhjEstadosCveTrac.setPlhjBcnt(plhjBcnt);
    }

    public void setPlhjBcnt(final IAlphanumericValue plhjBcnt) {
        this.plhjEstadosCveTrac.setPlhjBcnt(plhjBcnt);
    }

    // Property: plhjEstadosCveTrac.plhjBcst -> 15 PLHJ-BCST X(03)
    public AlphanumericVar getPlhjBcst() {
        return this.plhjEstadosCveTrac.getPlhjBcst();
    }

    public void setPlhjBcst(final String plhjBcst) {
        this.plhjEstadosCveTrac.setPlhjBcst(plhjBcst);
    }

    public void setPlhjBcst(final IAlphanumericValue plhjBcst) {
        this.plhjEstadosCveTrac.setPlhjBcst(plhjBcst);
    }

    // Property: plhjEstadosCveTrac.plhjCampechet -> 15 PLHJ-CAMPECHET X(03)
    public AlphanumericVar getPlhjCampechet() {
        return this.plhjEstadosCveTrac.getPlhjCampechet();
    }

    public void setPlhjCampechet(final String plhjCampechet) {
        this.plhjEstadosCveTrac.setPlhjCampechet(plhjCampechet);
    }

    public void setPlhjCampechet(final IAlphanumericValue plhjCampechet) {
        this.plhjEstadosCveTrac.setPlhjCampechet(plhjCampechet);
    }

    // Property: plhjEstadosCveTrac.plhjCoahuilat -> 15 PLHJ-COAHUILAT X(03)
    public AlphanumericVar getPlhjCoahuilat() {
        return this.plhjEstadosCveTrac.getPlhjCoahuilat();
    }

    public void setPlhjCoahuilat(final String plhjCoahuilat) {
        this.plhjEstadosCveTrac.setPlhjCoahuilat(plhjCoahuilat);
    }

    public void setPlhjCoahuilat(final IAlphanumericValue plhjCoahuilat) {
        this.plhjEstadosCveTrac.setPlhjCoahuilat(plhjCoahuilat);
    }

    // Property: plhjEstadosCveTrac.plhjColimat -> 15 PLHJ-COLIMAT X(03)
    public AlphanumericVar getPlhjColimat() {
        return this.plhjEstadosCveTrac.getPlhjColimat();
    }

    public void setPlhjColimat(final String plhjColimat) {
        this.plhjEstadosCveTrac.setPlhjColimat(plhjColimat);
    }

    public void setPlhjColimat(final IAlphanumericValue plhjColimat) {
        this.plhjEstadosCveTrac.setPlhjColimat(plhjColimat);
    }

    // Property: plhjEstadosCveTrac.plhjChiapast -> 15 PLHJ-CHIAPAST X(03)
    public AlphanumericVar getPlhjChiapast() {
        return this.plhjEstadosCveTrac.getPlhjChiapast();
    }

    public void setPlhjChiapast(final String plhjChiapast) {
        this.plhjEstadosCveTrac.setPlhjChiapast(plhjChiapast);
    }

    public void setPlhjChiapast(final IAlphanumericValue plhjChiapast) {
        this.plhjEstadosCveTrac.setPlhjChiapast(plhjChiapast);
    }

    // Property: plhjEstadosCveTrac.plhjChihuahuat -> 15 PLHJ-CHIHUAHUAT X(03)
    public AlphanumericVar getPlhjChihuahuat() {
        return this.plhjEstadosCveTrac.getPlhjChihuahuat();
    }

    public void setPlhjChihuahuat(final String plhjChihuahuat) {
        this.plhjEstadosCveTrac.setPlhjChihuahuat(plhjChihuahuat);
    }

    public void setPlhjChihuahuat(final IAlphanumericValue plhjChihuahuat) {
        this.plhjEstadosCveTrac.setPlhjChihuahuat(plhjChihuahuat);
    }

    // Property: plhjEstadosCveTrac.plhjDft -> 15 PLHJ-DFT X(03)
    public AlphanumericVar getPlhjDft() {
        return this.plhjEstadosCveTrac.getPlhjDft();
    }

    public void setPlhjDft(final String plhjDft) {
        this.plhjEstadosCveTrac.setPlhjDft(plhjDft);
    }

    public void setPlhjDft(final IAlphanumericValue plhjDft) {
        this.plhjEstadosCveTrac.setPlhjDft(plhjDft);
    }

    // Property: plhjEstadosCveTrac.plhjDurangot -> 15 PLHJ-DURANGOT X(03)
    public AlphanumericVar getPlhjDurangot() {
        return this.plhjEstadosCveTrac.getPlhjDurangot();
    }

    public void setPlhjDurangot(final String plhjDurangot) {
        this.plhjEstadosCveTrac.setPlhjDurangot(plhjDurangot);
    }

    public void setPlhjDurangot(final IAlphanumericValue plhjDurangot) {
        this.plhjEstadosCveTrac.setPlhjDurangot(plhjDurangot);
    }

    // Property: plhjEstadosCveTrac.plhjGtot -> 15 PLHJ-GTOT X(03)
    public AlphanumericVar getPlhjGtot() {
        return this.plhjEstadosCveTrac.getPlhjGtot();
    }

    public void setPlhjGtot(final String plhjGtot) {
        this.plhjEstadosCveTrac.setPlhjGtot(plhjGtot);
    }

    public void setPlhjGtot(final IAlphanumericValue plhjGtot) {
        this.plhjEstadosCveTrac.setPlhjGtot(plhjGtot);
    }

    // Property: plhjEstadosCveTrac.plhjGuerrerot -> 15 PLHJ-GUERREROT X(03)
    public AlphanumericVar getPlhjGuerrerot() {
        return this.plhjEstadosCveTrac.getPlhjGuerrerot();
    }

    public void setPlhjGuerrerot(final String plhjGuerrerot) {
        this.plhjEstadosCveTrac.setPlhjGuerrerot(plhjGuerrerot);
    }

    public void setPlhjGuerrerot(final IAlphanumericValue plhjGuerrerot) {
        this.plhjEstadosCveTrac.setPlhjGuerrerot(plhjGuerrerot);
    }

    // Property: plhjEstadosCveTrac.plhjHidalgot -> 15 PLHJ-HIDALGOT X(03)
    public AlphanumericVar getPlhjHidalgot() {
        return this.plhjEstadosCveTrac.getPlhjHidalgot();
    }

    public void setPlhjHidalgot(final String plhjHidalgot) {
        this.plhjEstadosCveTrac.setPlhjHidalgot(plhjHidalgot);
    }

    public void setPlhjHidalgot(final IAlphanumericValue plhjHidalgot) {
        this.plhjEstadosCveTrac.setPlhjHidalgot(plhjHidalgot);
    }

    // Property: plhjEstadosCveTrac.plhjJaliscot -> 15 PLHJ-JALISCOT X(03)
    public AlphanumericVar getPlhjJaliscot() {
        return this.plhjEstadosCveTrac.getPlhjJaliscot();
    }

    public void setPlhjJaliscot(final String plhjJaliscot) {
        this.plhjEstadosCveTrac.setPlhjJaliscot(plhjJaliscot);
    }

    public void setPlhjJaliscot(final IAlphanumericValue plhjJaliscot) {
        this.plhjEstadosCveTrac.setPlhjJaliscot(plhjJaliscot);
    }

    // Property: plhjEstadosCveTrac.plhjEdomext -> 15 PLHJ-EDOMEXT X(03)
    public AlphanumericVar getPlhjEdomext() {
        return this.plhjEstadosCveTrac.getPlhjEdomext();
    }

    public void setPlhjEdomext(final String plhjEdomext) {
        this.plhjEstadosCveTrac.setPlhjEdomext(plhjEdomext);
    }

    public void setPlhjEdomext(final IAlphanumericValue plhjEdomext) {
        this.plhjEstadosCveTrac.setPlhjEdomext(plhjEdomext);
    }

    // Property: plhjEstadosCveTrac.plhjMichoacant -> 15 PLHJ-MICHOACANT X(03)
    public AlphanumericVar getPlhjMichoacant() {
        return this.plhjEstadosCveTrac.getPlhjMichoacant();
    }

    public void setPlhjMichoacant(final String plhjMichoacant) {
        this.plhjEstadosCveTrac.setPlhjMichoacant(plhjMichoacant);
    }

    public void setPlhjMichoacant(final IAlphanumericValue plhjMichoacant) {
        this.plhjEstadosCveTrac.setPlhjMichoacant(plhjMichoacant);
    }

    // Property: plhjEstadosCveTrac.plhjMorelost -> 15 PLHJ-MORELOST X(03)
    public AlphanumericVar getPlhjMorelost() {
        return this.plhjEstadosCveTrac.getPlhjMorelost();
    }

    public void setPlhjMorelost(final String plhjMorelost) {
        this.plhjEstadosCveTrac.setPlhjMorelost(plhjMorelost);
    }

    public void setPlhjMorelost(final IAlphanumericValue plhjMorelost) {
        this.plhjEstadosCveTrac.setPlhjMorelost(plhjMorelost);
    }

    // Property: plhjEstadosCveTrac.plhjNayaritt -> 15 PLHJ-NAYARITT X(03)
    public AlphanumericVar getPlhjNayaritt() {
        return this.plhjEstadosCveTrac.getPlhjNayaritt();
    }

    public void setPlhjNayaritt(final String plhjNayaritt) {
        this.plhjEstadosCveTrac.setPlhjNayaritt(plhjNayaritt);
    }

    public void setPlhjNayaritt(final IAlphanumericValue plhjNayaritt) {
        this.plhjEstadosCveTrac.setPlhjNayaritt(plhjNayaritt);
    }

    // Property: plhjEstadosCveTrac.plhjNuevoleont -> 15 PLHJ-NUEVOLEONT X(03)
    public AlphanumericVar getPlhjNuevoleont() {
        return this.plhjEstadosCveTrac.getPlhjNuevoleont();
    }

    public void setPlhjNuevoleont(final String plhjNuevoleont) {
        this.plhjEstadosCveTrac.setPlhjNuevoleont(plhjNuevoleont);
    }

    public void setPlhjNuevoleont(final IAlphanumericValue plhjNuevoleont) {
        this.plhjEstadosCveTrac.setPlhjNuevoleont(plhjNuevoleont);
    }

    // Property: plhjEstadosCveTrac.plhjOaxacat -> 15 PLHJ-OAXACAT X(03)
    public AlphanumericVar getPlhjOaxacat() {
        return this.plhjEstadosCveTrac.getPlhjOaxacat();
    }

    public void setPlhjOaxacat(final String plhjOaxacat) {
        this.plhjEstadosCveTrac.setPlhjOaxacat(plhjOaxacat);
    }

    public void setPlhjOaxacat(final IAlphanumericValue plhjOaxacat) {
        this.plhjEstadosCveTrac.setPlhjOaxacat(plhjOaxacat);
    }

    // Property: plhjEstadosCveTrac.plhjPueblat -> 15 PLHJ-PUEBLAT X(03)
    public AlphanumericVar getPlhjPueblat() {
        return this.plhjEstadosCveTrac.getPlhjPueblat();
    }

    public void setPlhjPueblat(final String plhjPueblat) {
        this.plhjEstadosCveTrac.setPlhjPueblat(plhjPueblat);
    }

    public void setPlhjPueblat(final IAlphanumericValue plhjPueblat) {
        this.plhjEstadosCveTrac.setPlhjPueblat(plhjPueblat);
    }

    // Property: plhjEstadosCveTrac.plhjQueretarot -> 15 PLHJ-QUERETAROT X(03)
    public AlphanumericVar getPlhjQueretarot() {
        return this.plhjEstadosCveTrac.getPlhjQueretarot();
    }

    public void setPlhjQueretarot(final String plhjQueretarot) {
        this.plhjEstadosCveTrac.setPlhjQueretarot(plhjQueretarot);
    }

    public void setPlhjQueretarot(final IAlphanumericValue plhjQueretarot) {
        this.plhjEstadosCveTrac.setPlhjQueretarot(plhjQueretarot);
    }

    // Property: plhjEstadosCveTrac.plhjQroot -> 15 PLHJ-QROOT X(03)
    public AlphanumericVar getPlhjQroot() {
        return this.plhjEstadosCveTrac.getPlhjQroot();
    }

    public void setPlhjQroot(final String plhjQroot) {
        this.plhjEstadosCveTrac.setPlhjQroot(plhjQroot);
    }

    public void setPlhjQroot(final IAlphanumericValue plhjQroot) {
        this.plhjEstadosCveTrac.setPlhjQroot(plhjQroot);
    }

    // Property: plhjEstadosCveTrac.plhjSlpt -> 15 PLHJ-SLPT X(03)
    public AlphanumericVar getPlhjSlpt() {
        return this.plhjEstadosCveTrac.getPlhjSlpt();
    }

    public void setPlhjSlpt(final String plhjSlpt) {
        this.plhjEstadosCveTrac.setPlhjSlpt(plhjSlpt);
    }

    public void setPlhjSlpt(final IAlphanumericValue plhjSlpt) {
        this.plhjEstadosCveTrac.setPlhjSlpt(plhjSlpt);
    }

    // Property: plhjEstadosCveTrac.plhjSinaloat -> 15 PLHJ-SINALOAT X(03)
    public AlphanumericVar getPlhjSinaloat() {
        return this.plhjEstadosCveTrac.getPlhjSinaloat();
    }

    public void setPlhjSinaloat(final String plhjSinaloat) {
        this.plhjEstadosCveTrac.setPlhjSinaloat(plhjSinaloat);
    }

    public void setPlhjSinaloat(final IAlphanumericValue plhjSinaloat) {
        this.plhjEstadosCveTrac.setPlhjSinaloat(plhjSinaloat);
    }

    // Property: plhjEstadosCveTrac.plhjSonorat -> 15 PLHJ-SONORAT X(03)
    public AlphanumericVar getPlhjSonorat() {
        return this.plhjEstadosCveTrac.getPlhjSonorat();
    }

    public void setPlhjSonorat(final String plhjSonorat) {
        this.plhjEstadosCveTrac.setPlhjSonorat(plhjSonorat);
    }

    public void setPlhjSonorat(final IAlphanumericValue plhjSonorat) {
        this.plhjEstadosCveTrac.setPlhjSonorat(plhjSonorat);
    }

    // Property: plhjEstadosCveTrac.plhjTabascot -> 15 PLHJ-TABASCOT X(03)
    public AlphanumericVar getPlhjTabascot() {
        return this.plhjEstadosCveTrac.getPlhjTabascot();
    }

    public void setPlhjTabascot(final String plhjTabascot) {
        this.plhjEstadosCveTrac.setPlhjTabascot(plhjTabascot);
    }

    public void setPlhjTabascot(final IAlphanumericValue plhjTabascot) {
        this.plhjEstadosCveTrac.setPlhjTabascot(plhjTabascot);
    }

    // Property: plhjEstadosCveTrac.plhjTamaulipast -> 15 PLHJ-TAMAULIPAST X(03)
    public AlphanumericVar getPlhjTamaulipast() {
        return this.plhjEstadosCveTrac.getPlhjTamaulipast();
    }

    public void setPlhjTamaulipast(final String plhjTamaulipast) {
        this.plhjEstadosCveTrac.setPlhjTamaulipast(plhjTamaulipast);
    }

    public void setPlhjTamaulipast(final IAlphanumericValue plhjTamaulipast) {
        this.plhjEstadosCveTrac.setPlhjTamaulipast(plhjTamaulipast);
    }

    // Property: plhjEstadosCveTrac.plhjTlaxcalat -> 15 PLHJ-TLAXCALAT X(03)
    public AlphanumericVar getPlhjTlaxcalat() {
        return this.plhjEstadosCveTrac.getPlhjTlaxcalat();
    }

    public void setPlhjTlaxcalat(final String plhjTlaxcalat) {
        this.plhjEstadosCveTrac.setPlhjTlaxcalat(plhjTlaxcalat);
    }

    public void setPlhjTlaxcalat(final IAlphanumericValue plhjTlaxcalat) {
        this.plhjEstadosCveTrac.setPlhjTlaxcalat(plhjTlaxcalat);
    }

    // Property: plhjEstadosCveTrac.plhjVeracruzt -> 15 PLHJ-VERACRUZT X(03)
    public AlphanumericVar getPlhjVeracruzt() {
        return this.plhjEstadosCveTrac.getPlhjVeracruzt();
    }

    public void setPlhjVeracruzt(final String plhjVeracruzt) {
        this.plhjEstadosCveTrac.setPlhjVeracruzt(plhjVeracruzt);
    }

    public void setPlhjVeracruzt(final IAlphanumericValue plhjVeracruzt) {
        this.plhjEstadosCveTrac.setPlhjVeracruzt(plhjVeracruzt);
    }

    // Property: plhjEstadosCveTrac.plhjYucatant -> 15 PLHJ-YUCATANT X(03)
    public AlphanumericVar getPlhjYucatant() {
        return this.plhjEstadosCveTrac.getPlhjYucatant();
    }

    public void setPlhjYucatant(final String plhjYucatant) {
        this.plhjEstadosCveTrac.setPlhjYucatant(plhjYucatant);
    }

    public void setPlhjYucatant(final IAlphanumericValue plhjYucatant) {
        this.plhjEstadosCveTrac.setPlhjYucatant(plhjYucatant);
    }

    // Property: plhjEstadosCveTrac.plhjZacatecast -> 15 PLHJ-ZACATECAST X(03)
    public AlphanumericVar getPlhjZacatecast() {
        return this.plhjEstadosCveTrac.getPlhjZacatecast();
    }

    public void setPlhjZacatecast(final String plhjZacatecast) {
        this.plhjEstadosCveTrac.setPlhjZacatecast(plhjZacatecast);
    }

    public void setPlhjZacatecast(final IAlphanumericValue plhjZacatecast) {
        this.plhjEstadosCveTrac.setPlhjZacatecast(plhjZacatecast);
    }

    // Property: plhjEstadosCveTrac.plhjExtranjerot -> 15 PLHJ-EXTRANJEROT X(03)
    public AlphanumericVar getPlhjExtranjerot() {
        return this.plhjEstadosCveTrac.getPlhjExtranjerot();
    }

    public void setPlhjExtranjerot(final String plhjExtranjerot) {
        this.plhjEstadosCveTrac.setPlhjExtranjerot(plhjExtranjerot);
    }

    public void setPlhjExtranjerot(final IAlphanumericValue plhjExtranjerot) {
        this.plhjEstadosCveTrac.setPlhjExtranjerot(plhjExtranjerot);
    }

    // Property: plhjEstadosCveTrac.plhjOtrost -> 15 PLHJ-OTROST X(03)
    public AlphanumericVar getPlhjOtrost() {
        return this.plhjEstadosCveTrac.getPlhjOtrost();
    }

    public void setPlhjOtrost(final String plhjOtrost) {
        this.plhjEstadosCveTrac.setPlhjOtrost(plhjOtrost);
    }

    public void setPlhjOtrost(final IAlphanumericValue plhjOtrost) {
        this.plhjEstadosCveTrac.setPlhjOtrost(plhjOtrost);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhjDireccionGnp1.setValue("AVENIDA CERRO DE LAS TORRES        ");
        this.plhjDireccionGnp2.setValue("NUMERO 395                         ");
        this.plhjDireccionGnp3.setValue("COL. CAMPESTRE CHURUBUSCO          ");
        this.plhjNombreGz.setValue("GERENCIA DE ZONA NUMERO ");
        this.plhjNumeros.clear();
        this.plhjEstadosSaga.clear();
        this.plhjEstadosCveTrac.initialize();
    }

}

