package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaselementos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WW-TAB-DATO5.
 *
 */
@Setter
@Getter
public class WwTabDato5 extends DataStruct {
    // Properties
    private AlphanumericVarArray wwCampo5 = new AlphanumericVarArray(this, 5, 1);            // 15 WW-CAMPO5 X(01) [5]

    public WwTabDato5() {
        super();
        initialize();
    }

    public WwTabDato5(DataContainer parent) {
        super(parent);
    }

    public WwTabDato5(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWwCampo5(int index) {
        return this.wwCampo5.at(index);
    }

    public void setWwCampo5(final String wwCampo5) {
        this.wwCampo5.setValue(wwCampo5);
    }

    public void setWwCampo5(final AlphanumericVarArray wwCampo5) {
        this.wwCampo5.setValue(wwCampo5);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wwCampo5.clear();
    }

}

