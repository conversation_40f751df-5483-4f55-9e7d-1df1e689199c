
/**
 *  Program: Ggqacb00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.components.Ggqa0049cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.components.Ggqa0054cpy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00olcpy.AbReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00lncpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa0053cpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.Ggqa00ogcpy;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcna0cpy.Dclgfvtcna0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdsc0cpy.Dclgfvtdsc0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.WsCamposTrabajo;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.IndicadoresDeNulos;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.SwBanderas;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00.TmpfillerIndicadoresDeNulos;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa001kcpy.LcnaLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa001kcpy.LcnaLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcna0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdsc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcna0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdsc0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqacb00 extends AbstractCobolOs390Program implements IProgram {


    // Copy file QA$CBLCPY$GGQA00OL
    private AbReg abReg = new  AbReg();                                      // 01 AB-REG UsedAsParameter
    // End copy file QA$CBLCPY$GGQA00OL

    // Copy file QA$CBLCPY$GGQA00LN
    private Ggqa00lncpy ggqa00lncpy = new  Ggqa00lncpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00LN

    // Copy file QA$CBLCPY$GGQA0053
    private Ggqa0053cpy ggqa0053cpy = new  Ggqa0053cpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA0053

    // Auto variables
//  private int ccI;                                // CC-I // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GGQA00QH
//  private TcReg tcReg = new  TcReg();                                          // 01 TC-REG // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA00QH

    // Copy file QA$CBLCPY$GGQA00OG
    private Ggqa00ogcpy ggqa00ogcpy = new  Ggqa00ogcpy();            // Copy file
    // End copy file QA$CBLCPY$GGQA00OG

    // Auto variables
//  private int t999MdI;                            // T999-MD-I // [@WARNING variable NOT USED]
    private IndexVar iError = new IndexVar();        // I-ERROR

    // Copy file QA$CBLCPY$GFVTCNA0
    private Dclgfvtcna0 dclgfvtcna0 = new  Dclgfvtcna0();                    // 01 DCLGFVTCNA0
    // End copy file QA$CBLCPY$GFVTCNA0

    // Copy file QA$CBLCPY$GFVTDSC0
    private Dclgfvtdsc0 dclgfvtdsc0 = new  Dclgfvtdsc0();                    // 01 DCLGFVTDSC0
    // End copy file QA$CBLCPY$GFVTDSC0

    // Level 1
    private AlphanumericVar w000Prog = new AlphanumericVar(8);               // 01 W000-PROG X(08)
//  private String w000Fs = "";                                             // 01 W000-FS XX // [@WARNING variable NOT USED]
    private WsCamposTrabajo wsCamposTrabajo = new  WsCamposTrabajo();        // 01 WS-CAMPOS-TRABAJO
    private NumericEditedVar codeError = new NumericEditedVar("------999");  // 01 CODE-ERROR ------999
    private String msgRutina = "";                                          // 01 MSG-RUTINA X(20)
    private String msgMotivo = "";                                          // 01 MSG-MOTIVO X(40)
    private IndicadoresDeNulos indicadoresDeNulos = new  IndicadoresDeNulos();               // 01 INDICADORES-DE-NULOS
//  private String w000Open = "OPEN";                                       // 01 W000-OPEN X(04) // [@WARNING variable NOT USED]
//  private String w000Fetch = "FETCH";                                     // 01 W000-FETCH X(05) // [@WARNING variable NOT USED]
//  private String w000Close = "CLOSE";                                     // 01 W000-CLOSE X(05) // [@WARNING variable NOT USED]
//  private String w000RegAct = "1";                                        // 01 W000-REG-ACT X(01) // [@WARNING variable NOT USED]
    private String w000Consulta = "CONSULTA";                               // 01 W000-CONSULTA X(10)
    private String wcGnp = "GNP";                                           // 01 WC-GNP X(03)
    private SwBanderas swBanderas = new  SwBanderas();                       // 01 SW-BANDERAS
    private TmpfillerIndicadoresDeNulos tmpfillerIndicadoresDeNulos = new  TmpfillerIndicadoresDeNulos();    // 01 tmpFILLER-INDICADORES-DE-NULOS

    // Copy file QA$CBLCPY$GGQA001K
    private LcnaLinkReg lcnaLinkReg = new  LcnaLinkReg();                    // 01 LCNA-LINK-REG UsedAsParameter

    private Sqlca sqlca;


    // Declare Sql Ports
    private IGfvtcna0Port gfvtcna0Port;
    private IGfvtdsc0Port gfvtdsc0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtcna0Model> gfvtcna0Model;
    private Optional<Gfvtdsc0Model> gfvtdsc0Model;

    // Declare Sql Cursors
    private ICursor<Gfvtcna0Model> curCna0Cursor;

    public Ggqacb00(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initialize();
    }

    // Initial values
    @Override
    public void initialize() {
        this.w000Prog.setValue("GGQACB00");
//        this.w000Open = "OPEN"; // [@WARNING variable NOT USED]
//        this.w000Fetch = "FETCH"; // [@WARNING variable NOT USED]
//        this.w000Close = "CLOSE"; // [@WARNING variable NOT USED]
//        this.w000RegAct = "1"; // [@WARNING variable NOT USED]
        this.w000Consulta = "CONSULTA";
        this.wcGnp = "GNP";
    }


    /**
     *  Main execution entry point of the program.
     */
    public void run(AlphanumericVar lcnaLinkReg) {
        this.lcnaLinkReg.setValue(lcnaLinkReg);
        try {
            run();
        } finally {
            lcnaLinkReg.setValue(this.lcnaLinkReg);
        }
    }

    @Override
    public void run(Object... args) {
        assignInputArgument(args, 0, this.lcnaLinkReg);
        try {
            run();
        } finally {
            updateOutputArgument(args, 0, this.lcnaLinkReg);
        }
    }

    public void run() {
        sqlDelayedParagraph();
        p0000Control();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(curCna0Cursor == null) {
            curCna0Cursor = gfvtcna0Port.createCursorCur_cna0_3(
                () -> Gfvtcna0Model.builder()
                    .agtIdr(wsCamposTrabajo.getWsAgtIdr())
                .build());
        }
        //
        // EXIT-SQL-DELAYED-PARAGRAPH - Last paragraph
        //
    }

    /**
     *  Section: 0000-CONTROL
     *  Paragraph: unnamedProcedure.
     */
    void p0000Control() {
        p1000Inicio();
        p2000Proceso();
        // PERFORM THRU statement from p9999Termina to p9999Termina$p9999Abend
        p9999Termina();
    }

    /**
     *  Section: 1000-INICIO
     *  Paragraph: unnamedProcedure.
     *
     * 1000-INICIO   PROCESA INICIO DE PROGRAMA
     */
    void p1000Inicio() {
        wsCamposTrabajo.initialize();
        dclgfvtcna0.initialize();
        dclgfvtdsc0.initialize();
        p1100MoveVarLinkHost();
        p1200ValidaLink();
    }

    /**
     *  Section: 1100-MOVE-VAR-LINK-HOST
     *  Paragraph: unnamedProcedure.
     *
     * 1100-MOVE-VAR-LINK-HOST  MUEVE DATOS DE LINKAGE
     * A VARIABLES HOST
     */
    void p1100MoveVarLinkHost() {
        wsCamposTrabajo.setWsCdoper(lcnaLinkReg.getLcnaCdoper().toStringValue());

        wsCamposTrabajo.setWsAgtIdr(lcnaLinkReg.getLcnaAgtIdr().toInt());

        wsCamposTrabajo.setWsAgtIdrAux(lcnaLinkReg.getLcnaAgtIdr());
    }

    /**
     *  Section: 1200-VALIDA-LINK
     *  Paragraph: unnamedProcedure.
     *
     * 1200-VALIDA-LINK        VALIDA VARIABLES RECIBIDAS
     * DE LA LINKAGE.
     */
    void p1200ValidaLink() {
        // EVALUA MODO DE OPERACION QUE SEA CONSULTA
        if (wsCamposTrabajo.getWsCdoper().equals(w000Consulta)){
            ;
        } else {
            msgMotivo = "CDOPER INCORRECTO";

            msgRutina = "1200-VALIDA-LINK";

            codeError.setValue("016");
            p9999Termina$p9999Abend();
        }
        // EVALUA NUMERO DEL AGENTE
        if ((wsCamposTrabajo.getWsAgtIdrAux().isSpaces()) || (wsCamposTrabajo.getWsAgtIdrAux().isZero())){
            msgMotivo = "AGT-IDR INVALIDO";

            msgRutina = "1200-VALIDA-LINK";

            codeError.setValue("016");
            p9999Termina$p9999Abend();
        }
    }

    /**
     *  Section: 2000-PROCESO
     *  Paragraph: unnamedProcedure.
     *
     * 2000-PROCESO      PROCESO
     */
    void p2000Proceso() {
        p2110OpenCurCna0();
        p2120FetchCurCna0();
        wsCamposTrabajo.setWsInd_1(1);

        while (!(wsCamposTrabajo.getWsInd_1() > 0)) {
                wsCamposTrabajo.setWsInd_1(wsCamposTrabajo.getWsInd_1() + 1);
                p2120FetchCurCna0();
        }
        p2121ObtDatosDsc0();
        p2140MueveVarHostLink();
        p2150CloseCurCna0();
    }

    /**
     *  Section: 2110-OPEN-CUR-CNA0
     *  Paragraph: unnamedProcedure.
     *
     * 2110-OPEN-CUR-CNA0   OPEN CURSOR CUR-CNA0
     */
    void p2110OpenCurCna0() {
        curCna0Cursor.open();
        p9999Termina$p9000Db2Check();
    }

    /**
     *  Section: 2120-FETCH-CUR-CNA0
     *  Paragraph: unnamedProcedure.
     *
     * 2120-FETCH-CUR-CNA0  REALIZA FETCH CURSOR CUR-CNA0
     */
    void p2120FetchCurCna0() {
        gfvtcna0Model = curCna0Cursor.next();

        if (gfvtcna0Model.isPresent()) {
            dclgfvtcna0.setTfpCve(fromHostInt(gfvtcna0Model.get().tfpCve()));
            dclgfvtcna0.setMteCve(fromHostInt(gfvtcna0Model.get().mteCve()));
            dclgfvtcna0.setOfnCve(fromHostInt(gfvtcna0Model.get().ofnCve()));
            dclgfvtcna0.setCnaGerZonEnv(fromHostInt(gfvtcna0Model.get().cnaGerZonEnv()));
            dclgfvtcna0.setCtbNumCta(fromHostString(gfvtcna0Model.get().ctbNumCta()));
            indicadoresDeNulos.setIndcnactbnum(isNull(gfvtcna0Model.get().ctbNumCta()) ? -1 : 0);
            dclgfvtcna0.setCnaPjeIva(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIva()));
            dclgfvtcna0.setCnaPjeIsr(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIsr()));
        }
        // --INDICADOR DE NULL CTB-NUM-CTA
        if (indicadoresDeNulos.getIndcnactbnum() < 0){
            dclgfvtcna0.getCtbNumCta().setSpaces();
        }
        p9999Termina$p9000Db2Check();
        if (sqlca.getSqlcode() == 100) {
            swBanderas.setSwCna0Fin();
        }
    }

    /**
     *  Section: 2121-OBT-DATOS-DSC0
     *  Paragraph: unnamedProcedure.
     *
     * 2121-OBT-DATOS-DSC0  OBTENER EL ESQUEMA DE FACTURA-
     * CION DEL INTERMEDIARIO
     */
    void p2121ObtDatosDsc0() {
        genericModel =
            gfvtdsc0Port.max_1(
                GenericModel.builder().values(List.of(
                    wsCamposTrabajo.getWsAgtIdr(),
                    toHostString(wcGnp)))
                .build());

        if(genericModel.isPresent()) {
            dclgfvtdsc0.setDscDsgCve(fromHostInt(genericModel.get().values().get(0)));
            tmpfillerIndicadoresDeNulos.setInddscdsg(isNull(genericModel.get().values().get(0)) ? -1 : 0);
        }
        p9999Termina$p9000Db2Check();
    }

    /**
     *  Section: 2140-MUEVE-VAR-HOST-LINK
     *  Paragraph: unnamedProcedure.
     *
     * 2130-REGLA-NEGOCIO   REGLA DE NEGOCIO
     * 2130-REGLA-NEGOCIO               SECTION.
     * MOVE TFP-CVE          TO LCNA-TFP-CVE
     * EXIT.
     * 2140-MUEVE-VAR-HOST-LINK   MUEVE DATOS DE HOST A
     * VARIABLES LINKAGE
     */
    void p2140MueveVarHostLink() {
        lcnaLinkReg.getLcnaSalida().initialize();
        lcnaLinkReg.setLcnaTfpCve(dclgfvtcna0.getTfpCve());
        lcnaLinkReg.setLcnaMteCve(dclgfvtcna0.getMteCve());
        lcnaLinkReg.setLcnaOfnCve(dclgfvtcna0.getOfnCve());
        lcnaLinkReg.setLcnaCnaGerZonEnv(dclgfvtcna0.getCnaGerZonEnv());
        lcnaLinkReg.setLcnaCtbNumCta(dclgfvtcna0.getCtbNumCta());
        lcnaLinkReg.setLcnaCnaPjeIva(dclgfvtcna0.getCnaPjeIva());
        lcnaLinkReg.setLcnaCnaPjeIsr(dclgfvtcna0.getCnaPjeIsr());
        lcnaLinkReg.setLcnaDsgCve(dclgfvtdsc0.getDscDsgCve());
    }

    /**
     *  Section: 2150-CLOSE-CUR-CNA0
     *  Paragraph: unnamedProcedure.
     *
     * 2150-CLOSE-CUR-CNA0  CLOSE CUR-CNA0
     */
    void p2150CloseCurCna0() {
        if (curCna0Cursor != null) {
            curCna0Cursor.close();
        }
        p999VerErrSql();
    }

    /**
     *  Section: 9999-TERMINA
     *  Paragraph: unnamedProcedure.
     *
     * TERMINA PROGRAMA
     */
    void p9999Termina() {
        throw new ExitProgramException();
    }

    /**
     *  Section: 9999-TERMINA
     *  Paragraph: 9999-ABEND.
     *
     */
    void p9999Termina$p9999Abend() {
        // ********************
        display("*****************************************");
        display("*** TERMINACION ANORMAL ** ANORMAL ******");
        display("*****************************************");
        display("*** ERROR EN PROGRAMA : ", w000Prog, "    ****");
        display("*****************************************");
        display("*** MOTIVO DE ERROR   : ", msgMotivo);
        display("*** RUTINA            : ", msgRutina);
        display("*** CODIGO DE ERROR NO. ", codeError);
        display("*****************************************");
        display("*****************************************");
        returnCode.setValue(16);
        throw new StopRunException();
        //
        // 9999-ABEND-FIN - Middle paragraph
        //
    }

    /**
     *  Section: 9999-TERMINA
     *  Paragraph: 9000-DB2-CHECK.
     *
     */
    void p9999Termina$p9000Db2Check() {
        // ********************
        swBanderas.setDb2ReturnCode(sqlca.getSqlcode());

        if ((swBanderas.isDb2Ok()) || (swBanderas.isDb2Notfnd())) {
            ;
        } else {
            msgMotivo = "9000-DB2-CHECK";

            msgRutina = w000Prog.toStringValue();

            codeError.setValue(sqlca.getSqlcode());
            lcnaLinkReg.setLcnaSwErr(lzPad(sqlca.getSqlcode(), 9));
            p9999Termina$p9999Abend();
        }
        lcnaLinkReg.setLcnaSwErr(lzPad(sqlca.getSqlcode(), 9));
    }

    /**
     *  Section: 990-CIF-CTL
     *  Paragraph: unnamedProcedure.
     *
     */
    public void p000Control$p990CifCtl() {
    }

    /**
     *  Section: 980-ABORTA
     *  Paragraph: unnamedProcedure.
     *
     * COPY GGQA0057.
     * COPY  : GGQA0057 (G.N.P.)
     * OBJET : AREA DE COMUNICACION PARA LA RUTINA DE ABORTO
     * (ESPECIAL PARA PROGRAMAS CON DB2)
     * FECHA : 21/NOV/1995
     * AUTOR : SOFTTEK/CASE/TEN   IMM   TEN 1.50 CAF: 002
     */
    void p980Aborta() {
        abReg.setAbPrograma(w000Prog);
        p000Control$p990CifCtl();
        ggqa00lncpy.setW999Rutina("GGQA4700");
        runProgram(ggqa00lncpy.getW999Rutina(), abReg);
    }

    /**
     *  Section: 999-VER-ERR-SQL
     *  Paragraph: unnamedProcedure.
     *
     * EXEC SQL
     * INCLUDE GGQA00OI
     * END-EXEC.
     * AHCPSQLE: RUTINA PARA EL MANEJO DEL SQL ERROR
     * AUTOR: SOFTTEK/CASE/TEN  17/NOV/95
     * TEN 1.50 CAF: 002
     * SE ADICIONAN CON EL PARRAFO 999-VER-SQL-ERR
     * (ES UN COPY FISICO, QUE SE ADICIONA POR EL CONVERTIDOR DE
     * LENGUAJE)
     * REFERENCIAS:                AHCWSQLE
     */
    void p999VerErrSql() {
        ggqa0053cpy.setBdErr("0");
        if (sqlca.getSqlcode() == 100) {
            ggqa0053cpy.setBdErr("1");
        } else if (sqlca.getSqlcode() != 0) {
            p999DespliegaSqlMsg();
            abReg.setAbCodigon(sqlca.getSqlcode());
            abReg.setAbProceso("999-VER-SQL-ERR");
            p980Aborta();
        }
    }

    /**
     *  Section: 999-DESPLIEGA-SQL-MSG
     *  Paragraph: unnamedProcedure.
     *
     */
    void p999DespliegaSqlMsg() {
        display("ERROR SQL; -- <INICIO DESCRIPCION>: ");
        runProgram("DSNTIAR", sqlca,
                ggqa00ogcpy.getW999ErrorMensaje(),
                ggqa00ogcpy.getW999ErrorTextLong());
        ggqa00ogcpy.setW999ContadorLng(1);
        while (!(ggqa00ogcpy.getW999ContadorLng().toInt() > 10)) {
                iError.setValue(ggqa00ogcpy.getW999ContadorLng());
                display("--", ggqa00ogcpy.getW999ErrorText().at(iError));
                ggqa00ogcpy.getW999ContadorLng().add(1);
        }
        display("ERROR SQL; -- <FIN DESCRIPCION>");
    }

    @Autowired
    public void setGfvtcna0Port(IGfvtcna0Port gfvtcna0Port) {
        this.gfvtcna0Port = gfvtcna0Port;
        this.gfvtcna0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdsc0Port(IGfvtdsc0Port gfvtdsc0Port) {
        this.gfvtdsc0Port = gfvtdsc0Port;
        this.gfvtdsc0Port.setProgramContext(getProgramContext());
    }
}
