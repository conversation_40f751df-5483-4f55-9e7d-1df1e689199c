package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: AGT0.
 *
 */
@Setter
@Getter
public class Agt0 { // VStruct
    // Properties
    private int wsAgtAnt;                                                   // 10 WS-AGT-ANT S9(9) COMPUTATIONAL
    private int agtAgtIdr;                                                  // 10 AGT-AGT-IDR S9(9) COMPUTATIONAL
    private String agtAgtNom = "";                                          // 10 AGT-AGT-NOM X(40)
    private String agtAgtApePat = "";                                       // 10 AGT-AGT-APE-PAT X(40)
    private String agtAgtApeMat = "";                                       // 10 AGT-AGT-APE-MAT X(30)
    private int agtAgtNip;                                                  // 10 AGT-AGT-NIP S9(4) COMPUTATIONAL
    private String cmaCmaDes = "";                                          // 10 CMA-CMA-DES X(60)
    private String wsIndGerente = "";                                       // 10 WS-IND-GERENTE X(07)
    private int wsOrvCve;                                                   // 10 WS-ORV-CVE S9(4) COMPUTATIONAL
    private String agtRazSoc = "";                                          // 10 AGT-RAZ-SOC X(60)
    private int agtPefCve;                                                  // 10 AGT-PEF-CVE S9(4) COMPUTATIONAL

    public Agt0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsAgtAnt = 0;
        this.agtAgtIdr = 0;
        this.agtAgtNom = "";
        this.agtAgtApePat = "";
        this.agtAgtApeMat = "";
        this.agtAgtNip = 0;
        this.cmaCmaDes = "";
        this.wsIndGerente = "";
        this.wsOrvCve = 0;
        this.agtRazSoc = "";
        this.agtPefCve = 0;
    }

}

