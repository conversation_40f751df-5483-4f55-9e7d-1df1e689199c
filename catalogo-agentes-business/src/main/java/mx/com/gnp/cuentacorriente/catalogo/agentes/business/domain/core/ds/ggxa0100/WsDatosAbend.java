package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DATOS-ABEND.
 *
 */
// 83      *================================================================*
// 84      *   DESCRIPCION DE ABEND'S PARA DB2                              *
// 85      *================================================================*
@Setter
@Getter
public class WsDatosAbend { // VStruct
    // Properties
    private String wsPrograma = "GGXA0100";                                 // 03 WS-PROGRAMA X(10)
    private String wsTabla = "";                                            // 03 WS-TABLA X(20)
    private String wsProcesoLlama = "";                                     // 03 WS-PROCESO-LLAMA X(30)
    private String wsProcesoEjecuta = "";                                   // 03 WS-PROCESO-EJECUTA X(30)
    private String wsInstruccion = "";                                      // 03 WS-INSTRUCCION X(20)

    public WsDatosAbend() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsPrograma = "GGXA0100";
        this.wsTabla = "";
        this.wsProcesoLlama = "";
        this.wsProcesoEjecuta = "";
        this.wsInstruccion = "";
    }

}

