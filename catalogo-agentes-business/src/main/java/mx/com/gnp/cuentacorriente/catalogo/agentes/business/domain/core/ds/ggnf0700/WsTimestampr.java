package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr.WsFechaTimestamp;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr.WsHoraTimestamp;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr.WsMicrTimestamp;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TIMESTAMPR.
 *
 */
@Setter
@Getter
public class WsTimestampr extends DataStruct {
    // Properties
    private WsFechaTimestamp wsFechaTimestamp = new  WsFechaTimestamp(this);                 // 03 WS-FECHA-TIMESTAMP UsedAsParameter
    private AlphanumericVar filler3 = new AlphanumericVar(this, 1);          // 03 FILLER X(01)
    private WsHoraTimestamp wsHoraTimestamp = new  WsHoraTimestamp(this);    // 03 WS-HORA-TIMESTAMP
    private WsMicrTimestamp wsMicrTimestamp = new  WsMicrTimestamp(this);    // 03 WS-MICR-TIMESTAMP

    public WsTimestampr() {
        super();
        initialize();
    }

    public WsTimestampr(DataContainer parent) {
        super(parent);
    }

    public WsTimestampr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }


    // Deeper properties

    // Property: wsFechaTimestamp.wsAaaaTimestamp -> 05 WS-AAAA-TIMESTAMP 9(04)
    public UnsignedNumericVar getWsAaaaTimestamp() {
        return this.wsFechaTimestamp.getWsAaaaTimestamp();
    }

    public void setWsAaaaTimestamp(final int wsAaaaTimestamp) {
        this.wsFechaTimestamp.setWsAaaaTimestamp(wsAaaaTimestamp);
    }

    public void setWsAaaaTimestamp(final INumericValue wsAaaaTimestamp) {
        this.wsFechaTimestamp.setWsAaaaTimestamp(wsAaaaTimestamp);
    }

    // Property: wsFechaTimestamp.filler1 -> 05 FILLER X(01)
    public AlphanumericVar getFiller1() {
        return this.wsFechaTimestamp.getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.wsFechaTimestamp.setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.wsFechaTimestamp.setFiller1(filler1);
    }

    // Property: wsFechaTimestamp.wsMmTimestamp -> 05 WS-MM-TIMESTAMP 9(02)
    public UnsignedNumericVar getWsMmTimestamp() {
        return this.wsFechaTimestamp.getWsMmTimestamp();
    }

    public void setWsMmTimestamp(final int wsMmTimestamp) {
        this.wsFechaTimestamp.setWsMmTimestamp(wsMmTimestamp);
    }

    public void setWsMmTimestamp(final INumericValue wsMmTimestamp) {
        this.wsFechaTimestamp.setWsMmTimestamp(wsMmTimestamp);
    }

    // Property: wsFechaTimestamp.filler2 -> 05 FILLER X(01)
    public AlphanumericVar getFiller2() {
        return this.wsFechaTimestamp.getFiller2();
    }

    public void setFiller2(final String filler2) {
        this.wsFechaTimestamp.setFiller2(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.wsFechaTimestamp.setFiller2(filler2);
    }

    // Property: wsFechaTimestamp.wsDdTimestamp -> 05 WS-DD-TIMESTAMP 9(02)
    public UnsignedNumericVar getWsDdTimestamp() {
        return this.wsFechaTimestamp.getWsDdTimestamp();
    }

    public void setWsDdTimestamp(final int wsDdTimestamp) {
        this.wsFechaTimestamp.setWsDdTimestamp(wsDdTimestamp);
    }

    public void setWsDdTimestamp(final INumericValue wsDdTimestamp) {
        this.wsFechaTimestamp.setWsDdTimestamp(wsDdTimestamp);
    }

    // Ambiguous Property: wsHoraTimestamp.wsHoraTimestamp -> 05 WS-HORA-TIMESTAMP 9(02)

    // Deeper properties

    // Property: wsHoraTimestamp.filler4 -> 05 FILLER X(01)
    public AlphanumericVar getFiller4() {
        return this.wsHoraTimestamp.getFiller4();
    }

    public void setFiller4(final String filler4) {
        this.wsHoraTimestamp.setFiller4(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.wsHoraTimestamp.setFiller4(filler4);
    }

    // Property: wsHoraTimestamp.wsMinTimestamp -> 05 WS-MIN-TIMESTAMP 9(02)
    public UnsignedNumericVar getWsMinTimestamp() {
        return this.wsHoraTimestamp.getWsMinTimestamp();
    }

    public void setWsMinTimestamp(final int wsMinTimestamp) {
        this.wsHoraTimestamp.setWsMinTimestamp(wsMinTimestamp);
    }

    public void setWsMinTimestamp(final INumericValue wsMinTimestamp) {
        this.wsHoraTimestamp.setWsMinTimestamp(wsMinTimestamp);
    }

    // Property: wsHoraTimestamp.filler5 -> 05 FILLER X(01)
    public AlphanumericVar getFiller5() {
        return this.wsHoraTimestamp.getFiller5();
    }

    public void setFiller5(final String filler5) {
        this.wsHoraTimestamp.setFiller5(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.wsHoraTimestamp.setFiller5(filler5);
    }

    // Property: wsHoraTimestamp.wsSegTimestamp -> 05 WS-SEG-TIMESTAMP 9(02)
    public UnsignedNumericVar getWsSegTimestamp() {
        return this.wsHoraTimestamp.getWsSegTimestamp();
    }

    public void setWsSegTimestamp(final int wsSegTimestamp) {
        this.wsHoraTimestamp.setWsSegTimestamp(wsSegTimestamp);
    }

    public void setWsSegTimestamp(final INumericValue wsSegTimestamp) {
        this.wsHoraTimestamp.setWsSegTimestamp(wsSegTimestamp);
    }


    // Deeper properties

    // Property: wsMicrTimestamp.filler6 -> 05 FILLER X(01)
    public AlphanumericVar getFiller6() {
        return this.wsMicrTimestamp.getFiller6();
    }

    public void setFiller6(final String filler6) {
        this.wsMicrTimestamp.setFiller6(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.wsMicrTimestamp.setFiller6(filler6);
    }

    // Property: wsMicrTimestamp.wsMicrosegTimestamp -> 05 WS-MICROSEG-TIMESTAMP 9(06)
    public UnsignedNumericVar getWsMicrosegTimestamp() {
        return this.wsMicrTimestamp.getWsMicrosegTimestamp();
    }

    public void setWsMicrosegTimestamp(final int wsMicrosegTimestamp) {
        this.wsMicrTimestamp.setWsMicrosegTimestamp(wsMicrosegTimestamp);
    }

    public void setWsMicrosegTimestamp(final INumericValue wsMicrosegTimestamp) {
        this.wsMicrTimestamp.setWsMicrosegTimestamp(wsMicrosegTimestamp);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFechaTimestamp.initialize();
        this.filler3.setValue("-");
        this.wsHoraTimestamp.initialize();
        this.wsMicrTimestamp.initialize();
    }

}

