package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.wsfecaaaa.WsAaaa;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FEC-AAAA.
 *
 */
// 395060800*                                                                 06080000
@Setter
@Getter
public class WsFecAaaa { // VStruct
    // Properties
    private WsAaaa wsAaaa = new  WsAaaa();                                   // 03 WS-AAAA REDEFINED BY WS-AAAAR
    private UnsignedNumericVar wsAaaar = new UnsignedNumericVar(null, this.wsAaaa, 4, 0);    // 03 WS-AAAAR REDEFINES WS-AAAA 9(4)

    public WsFecAaaa() {
        initialize();
    }



    public void setWsAaaar(final int wsAaaar) {
        this.wsAaaar.setValue(wsAaaar);
    }

    public void setWsAaaar(final BigDecimal wsAaaar) {
        this.wsAaaar.setValue(wsAaaar);
    }

    public void setWsAaaar(final INumericValue wsAaaar) {
        this.wsAaaar.setValue(wsAaaar);
    }


    // Deeper properties

    // Property: wsAaaa.wsAa1 -> 05 WS-AA1 99
    public UnsignedNumericVar getWsAa1() {
        return this.wsAaaa.getWsAa1();
    }

    public void setWsAa1(final int wsAa1) {
        this.wsAaaa.setWsAa1(wsAa1);
    }

    public void setWsAa1(final INumericValue wsAa1) {
        this.wsAaaa.setWsAa1(wsAa1);
    }

    // Property: wsAaaa.wsAa2 -> 05 WS-AA2 99
    public UnsignedNumericVar getWsAa2() {
        return this.wsAaaa.getWsAa2();
    }

    public void setWsAa2(final int wsAa2) {
        this.wsAaaa.setWsAa2(wsAa2);
    }

    public void setWsAa2(final INumericValue wsAa2) {
        this.wsAaaa.setWsAa2(wsAa2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsAaaa.initialize();
    }

}

