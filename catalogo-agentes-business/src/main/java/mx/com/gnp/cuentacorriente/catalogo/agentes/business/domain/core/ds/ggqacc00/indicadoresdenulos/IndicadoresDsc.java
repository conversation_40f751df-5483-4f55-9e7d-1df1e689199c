package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICADORES-DSC.
 *
 */
@Setter
@Getter
public class IndicadoresDsc { // VStruct
    // Properties
    private int inddscdsg = 0;                                              // 05 INDDSCDSG S9(4) COMPUTATIONAL

    public IndicadoresDsc() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.inddscdsg = 0;
    }

}

