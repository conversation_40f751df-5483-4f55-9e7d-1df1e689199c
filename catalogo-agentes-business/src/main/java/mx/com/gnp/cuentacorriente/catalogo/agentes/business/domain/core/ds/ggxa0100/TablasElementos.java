package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaselementos.WwTabDato;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaselementos.WwTabDato5;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLAS-ELEMENTOS.
 *
 */
@Setter
@Getter
public class TablasElementos { // VStruct
    // Properties
    private WwTabDato wwTabDato = new  WwTabDato();                          // 10 WW-TAB-DATO
    private WwTabDato5 wwTabDato5 = new  WwTabDato5();                       // 10 WW-TAB-DATO5

    public TablasElementos() {
        initialize();
    }




    // Deeper properties

    // Property: wwTabDato.wwCampo -> 15 WW-CAMPO X(01) [3]
    public AlphanumericVarArray getWwCampo() {
        return this.wwTabDato.getWwCampo();
    }

    public void setWwCampo(final String wwCampo) {
        this.wwTabDato.setWwCampo(wwCampo);
    }

    public void setWwCampo(final AlphanumericVarArray wwCampo) {
        this.wwTabDato.setWwCampo(wwCampo);
    }


    // Deeper properties

    // Property: wwTabDato5.wwCampo5 -> 15 WW-CAMPO5 X(01) [5]
    public AlphanumericVarArray getWwCampo5() {
        return this.wwTabDato5.getWwCampo5();
    }

    public void setWwCampo5(final String wwCampo5) {
        this.wwTabDato5.setWwCampo5(wwCampo5);
    }

    public void setWwCampo5(final AlphanumericVarArray wwCampo5) {
        this.wwTabDato5.setWwCampo5(wwCampo5);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wwTabDato.initialize();
        this.wwTabDato5.initialize();
    }

}

