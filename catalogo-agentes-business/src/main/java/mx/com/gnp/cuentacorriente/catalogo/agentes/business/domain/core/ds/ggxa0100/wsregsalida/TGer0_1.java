package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tger0_1.TGer0Elem_1;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-GER0-1.
 *
 */
// 193           10 T-GER0-1    OCCURS 1 TO 255                               
@Setter
@Getter
public class TGer0_1 extends DataStructArray<TGer0_1> {
    // Properties
    private TGer0Elem_1 tGer0Elem_1 = new  TGer0Elem_1(this);                // 15 T-GER0-ELEM-1

    public TGer0_1(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TGer0_1(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TGer0_1(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TGer0_1(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TGer0_1 at(int idx) {
        return new TGer0_1(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tGer0Elem_1.sCnaGerZonElem -> 20 S-CNA-GER-ZON-ELEM X(01)
    public AlphanumericVar getSCnaGerZonElem() {
        return this.tGer0Elem_1.getSCnaGerZonElem();
    }

    public void setSCnaGerZonElem(final String sCnaGerZonElem) {
        this.tGer0Elem_1.setSCnaGerZonElem(sCnaGerZonElem);
    }

    public void setSCnaGerZonElem(final IAlphanumericValue sCnaGerZonElem) {
        this.tGer0Elem_1.setSCnaGerZonElem(sCnaGerZonElem);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tGer0Elem_1.initialize();
    }

}

