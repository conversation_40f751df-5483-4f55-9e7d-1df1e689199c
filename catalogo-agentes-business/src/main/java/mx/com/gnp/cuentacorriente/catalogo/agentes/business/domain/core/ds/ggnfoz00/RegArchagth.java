package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-ARCHAGTH.
 *
 */
@Setter
@Getter
public class RegArchagth extends DataStruct {
    // Properties
    private UnsignedNumericVar agtIdr1 = new UnsignedNumericVar(this, 9, 0);                 // 05 AGT-IDR1 9(09)
    private AlphanumericVar agtFecCamRazSo1 = new AlphanumericVar(this, 10);                 // 05 AGT-FEC-CAM-RAZ-SO1 X(10)
    private AlphanumericVar agtRfc1 = new AlphanumericVar(this, 13);         // 05 AGT-RFC1 X(13)
    private AlphanumericVar agtNom1 = new AlphanumericVar(this, 40);         // 05 AGT-NOM1 X(40)
    private AlphanumericVar agtApePat1 = new AlphanumericVar(this, 40);      // 05 AGT-APE-PAT1 X(40)
    private AlphanumericVar agtApeMat1 = new AlphanumericVar(this, 30);      // 05 AGT-APE-MAT1 X(30)
    private AlphanumericVar agtFecNac1 = new AlphanumericVar(this, 10);      // 05 AGT-FEC-NAC1 X(10)
    private AlphanumericVar agtNumCed1 = new AlphanumericVar(this, 20);      // 05 AGT-NUM-CED1 X(20)
    private AlphanumericVar agtFecIniCed1 = new AlphanumericVar(this, 10);   // 05 AGT-FEC-INI-CED1 X(10)
    private AlphanumericVar agtFecFinCed1 = new AlphanumericVar(this, 10);   // 05 AGT-FEC-FIN-CED1 X(10)
    private AlphanumericVar agtExpSeg1 = new AlphanumericVar(this, 1);       // 05 AGT-EXP-SEG1 X(01)
    private AlphanumericVar agtTraEsp1 = new AlphanumericVar(this, 1);       // 05 AGT-TRA-ESP1 X(01)
    private UnsignedNumericVar agtClfLmr1 = new UnsignedNumericVar(this, 5, 0);              // 05 AGT-CLF-LMR1 9(05)
    private AlphanumericVar fecUltAct1 = new AlphanumericVar(this, 10);      // 05 FEC-ULT-ACT1 X(10)
    private AlphanumericVar agtRazSoc1 = new AlphanumericVar(this, 60);      // 05 AGT-RAZ-SOC1 X(60)
    private AlphanumericVar nalCve1 = new AlphanumericVar(this, 3);          // 05 NAL-CVE1 X(03)
    private UnsignedNumericVar pefCve1 = new UnsignedNumericVar(this, 5, 0);                 // 05 PEF-CVE1 9(05)
    private AlphanumericVar tcaCve1 = new AlphanumericVar(this, 2);          // 05 TCA-CVE1 X(02)
    private UnsignedNumericVar mteCve1 = new UnsignedNumericVar(this, 5, 0);                 // 05 MTE-CVE1 9(05)
    private UnsignedNumericVar agtNumFolInv1 = new UnsignedNumericVar(this, 9, 0);           // 05 AGT-NUM-FOL-INV1 9(09)
    private AlphanumericVar agtNomExi1 = new AlphanumericVar(this, 40);      // 05 AGT-NOM-EXI1 X(40)
    private AlphanumericVar agtRstInv1 = new AlphanumericVar(this, 5);       // 05 AGT-RST-INV1 X(05)
    private AlphanumericVar agtFecIniInv1 = new AlphanumericVar(this, 10);   // 05 AGT-FEC-INI-INV1 X(10)
    private AlphanumericVar agtFecFinInv1 = new AlphanumericVar(this, 10);   // 05 AGT-FEC-FIN-INV1 X(10)
    private AlphanumericVar agtNomDphInv1 = new AlphanumericVar(this, 40);   // 05 AGT-NOM-DPH-INV1 X(40)
    private AlphanumericVar agtFecMotEss1 = new AlphanumericVar(this, 10);   // 05 AGT-FEC-MOT-ESS1 X(10)
    private AlphanumericVar agtFecMotAsgIn1 = new AlphanumericVar(this, 10);                 // 05 AGT-FEC-MOT-ASG-IN1 X(10)
    private AlphanumericVar agtObs1 = new AlphanumericVar(this, 60);         // 05 AGT-OBS1 X(60)
    private AlphanumericVar essRek1 = new AlphanumericVar(this, 1);          // 05 ESS-REK1 X(01)

    public RegArchagth() {
        super();
        initialize();
    }

    public RegArchagth(DataContainer parent) {
        super(parent);
    }

    public RegArchagth(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setAgtIdr1(final int agtIdr1) {
        this.agtIdr1.setValue(agtIdr1);
    }

    public void setAgtIdr1(final BigDecimal agtIdr1) {
        this.agtIdr1.setValue(agtIdr1);
    }

    public void setAgtIdr1(final INumericValue agtIdr1) {
        this.agtIdr1.setValue(agtIdr1);
    }

    public void setAgtFecCamRazSo1(final String agtFecCamRazSo1) {
        this.agtFecCamRazSo1.setValue(agtFecCamRazSo1);
    }

    public void setAgtFecCamRazSo1(final IAlphanumericValue agtFecCamRazSo1) {
        this.agtFecCamRazSo1.setValue(agtFecCamRazSo1);
    }

    public void setAgtRfc1(final String agtRfc1) {
        this.agtRfc1.setValue(agtRfc1);
    }

    public void setAgtRfc1(final IAlphanumericValue agtRfc1) {
        this.agtRfc1.setValue(agtRfc1);
    }

    public void setAgtNom1(final String agtNom1) {
        this.agtNom1.setValue(agtNom1);
    }

    public void setAgtNom1(final IAlphanumericValue agtNom1) {
        this.agtNom1.setValue(agtNom1);
    }

    public void setAgtApePat1(final String agtApePat1) {
        this.agtApePat1.setValue(agtApePat1);
    }

    public void setAgtApePat1(final IAlphanumericValue agtApePat1) {
        this.agtApePat1.setValue(agtApePat1);
    }

    public void setAgtApeMat1(final String agtApeMat1) {
        this.agtApeMat1.setValue(agtApeMat1);
    }

    public void setAgtApeMat1(final IAlphanumericValue agtApeMat1) {
        this.agtApeMat1.setValue(agtApeMat1);
    }

    public void setAgtFecNac1(final String agtFecNac1) {
        this.agtFecNac1.setValue(agtFecNac1);
    }

    public void setAgtFecNac1(final IAlphanumericValue agtFecNac1) {
        this.agtFecNac1.setValue(agtFecNac1);
    }

    public void setAgtNumCed1(final String agtNumCed1) {
        this.agtNumCed1.setValue(agtNumCed1);
    }

    public void setAgtNumCed1(final IAlphanumericValue agtNumCed1) {
        this.agtNumCed1.setValue(agtNumCed1);
    }

    public void setAgtFecIniCed1(final String agtFecIniCed1) {
        this.agtFecIniCed1.setValue(agtFecIniCed1);
    }

    public void setAgtFecIniCed1(final IAlphanumericValue agtFecIniCed1) {
        this.agtFecIniCed1.setValue(agtFecIniCed1);
    }

    public void setAgtFecFinCed1(final String agtFecFinCed1) {
        this.agtFecFinCed1.setValue(agtFecFinCed1);
    }

    public void setAgtFecFinCed1(final IAlphanumericValue agtFecFinCed1) {
        this.agtFecFinCed1.setValue(agtFecFinCed1);
    }

    public void setAgtExpSeg1(final String agtExpSeg1) {
        this.agtExpSeg1.setValue(agtExpSeg1);
    }

    public void setAgtExpSeg1(final IAlphanumericValue agtExpSeg1) {
        this.agtExpSeg1.setValue(agtExpSeg1);
    }

    public void setAgtTraEsp1(final String agtTraEsp1) {
        this.agtTraEsp1.setValue(agtTraEsp1);
    }

    public void setAgtTraEsp1(final IAlphanumericValue agtTraEsp1) {
        this.agtTraEsp1.setValue(agtTraEsp1);
    }

    public void setAgtClfLmr1(final int agtClfLmr1) {
        this.agtClfLmr1.setValue(agtClfLmr1);
    }

    public void setAgtClfLmr1(final BigDecimal agtClfLmr1) {
        this.agtClfLmr1.setValue(agtClfLmr1);
    }

    public void setAgtClfLmr1(final INumericValue agtClfLmr1) {
        this.agtClfLmr1.setValue(agtClfLmr1);
    }

    public void setFecUltAct1(final String fecUltAct1) {
        this.fecUltAct1.setValue(fecUltAct1);
    }

    public void setFecUltAct1(final IAlphanumericValue fecUltAct1) {
        this.fecUltAct1.setValue(fecUltAct1);
    }

    public void setAgtRazSoc1(final String agtRazSoc1) {
        this.agtRazSoc1.setValue(agtRazSoc1);
    }

    public void setAgtRazSoc1(final IAlphanumericValue agtRazSoc1) {
        this.agtRazSoc1.setValue(agtRazSoc1);
    }

    public void setNalCve1(final String nalCve1) {
        this.nalCve1.setValue(nalCve1);
    }

    public void setNalCve1(final IAlphanumericValue nalCve1) {
        this.nalCve1.setValue(nalCve1);
    }

    public void setPefCve1(final int pefCve1) {
        this.pefCve1.setValue(pefCve1);
    }

    public void setPefCve1(final BigDecimal pefCve1) {
        this.pefCve1.setValue(pefCve1);
    }

    public void setPefCve1(final INumericValue pefCve1) {
        this.pefCve1.setValue(pefCve1);
    }

    public void setTcaCve1(final String tcaCve1) {
        this.tcaCve1.setValue(tcaCve1);
    }

    public void setTcaCve1(final IAlphanumericValue tcaCve1) {
        this.tcaCve1.setValue(tcaCve1);
    }

    public void setMteCve1(final int mteCve1) {
        this.mteCve1.setValue(mteCve1);
    }

    public void setMteCve1(final BigDecimal mteCve1) {
        this.mteCve1.setValue(mteCve1);
    }

    public void setMteCve1(final INumericValue mteCve1) {
        this.mteCve1.setValue(mteCve1);
    }

    public void setAgtNumFolInv1(final int agtNumFolInv1) {
        this.agtNumFolInv1.setValue(agtNumFolInv1);
    }

    public void setAgtNumFolInv1(final BigDecimal agtNumFolInv1) {
        this.agtNumFolInv1.setValue(agtNumFolInv1);
    }

    public void setAgtNumFolInv1(final INumericValue agtNumFolInv1) {
        this.agtNumFolInv1.setValue(agtNumFolInv1);
    }

    public void setAgtNomExi1(final String agtNomExi1) {
        this.agtNomExi1.setValue(agtNomExi1);
    }

    public void setAgtNomExi1(final IAlphanumericValue agtNomExi1) {
        this.agtNomExi1.setValue(agtNomExi1);
    }

    public void setAgtRstInv1(final String agtRstInv1) {
        this.agtRstInv1.setValue(agtRstInv1);
    }

    public void setAgtRstInv1(final IAlphanumericValue agtRstInv1) {
        this.agtRstInv1.setValue(agtRstInv1);
    }

    public void setAgtFecIniInv1(final String agtFecIniInv1) {
        this.agtFecIniInv1.setValue(agtFecIniInv1);
    }

    public void setAgtFecIniInv1(final IAlphanumericValue agtFecIniInv1) {
        this.agtFecIniInv1.setValue(agtFecIniInv1);
    }

    public void setAgtFecFinInv1(final String agtFecFinInv1) {
        this.agtFecFinInv1.setValue(agtFecFinInv1);
    }

    public void setAgtFecFinInv1(final IAlphanumericValue agtFecFinInv1) {
        this.agtFecFinInv1.setValue(agtFecFinInv1);
    }

    public void setAgtNomDphInv1(final String agtNomDphInv1) {
        this.agtNomDphInv1.setValue(agtNomDphInv1);
    }

    public void setAgtNomDphInv1(final IAlphanumericValue agtNomDphInv1) {
        this.agtNomDphInv1.setValue(agtNomDphInv1);
    }

    public void setAgtFecMotEss1(final String agtFecMotEss1) {
        this.agtFecMotEss1.setValue(agtFecMotEss1);
    }

    public void setAgtFecMotEss1(final IAlphanumericValue agtFecMotEss1) {
        this.agtFecMotEss1.setValue(agtFecMotEss1);
    }

    public void setAgtFecMotAsgIn1(final String agtFecMotAsgIn1) {
        this.agtFecMotAsgIn1.setValue(agtFecMotAsgIn1);
    }

    public void setAgtFecMotAsgIn1(final IAlphanumericValue agtFecMotAsgIn1) {
        this.agtFecMotAsgIn1.setValue(agtFecMotAsgIn1);
    }

    public void setAgtObs1(final String agtObs1) {
        this.agtObs1.setValue(agtObs1);
    }

    public void setAgtObs1(final IAlphanumericValue agtObs1) {
        this.agtObs1.setValue(agtObs1);
    }

    public void setEssRek1(final String essRek1) {
        this.essRek1.setValue(essRek1);
    }

    public void setEssRek1(final IAlphanumericValue essRek1) {
        this.essRek1.setValue(essRek1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.agtIdr1.clear();
        this.agtFecCamRazSo1.clear();
        this.agtRfc1.clear();
        this.agtNom1.clear();
        this.agtApePat1.clear();
        this.agtApeMat1.clear();
        this.agtFecNac1.clear();
        this.agtNumCed1.clear();
        this.agtFecIniCed1.clear();
        this.agtFecFinCed1.clear();
        this.agtExpSeg1.clear();
        this.agtTraEsp1.clear();
        this.agtClfLmr1.clear();
        this.fecUltAct1.clear();
        this.agtRazSoc1.clear();
        this.nalCve1.clear();
        this.pefCve1.clear();
        this.tcaCve1.clear();
        this.mteCve1.clear();
        this.agtNumFolInv1.clear();
        this.agtNomExi1.clear();
        this.agtRstInv1.clear();
        this.agtFecIniInv1.clear();
        this.agtFecFinInv1.clear();
        this.agtNomDphInv1.clear();
        this.agtFecMotEss1.clear();
        this.agtFecMotAsgIn1.clear();
        this.agtObs1.clear();
        this.essRek1.clear();
    }

}

