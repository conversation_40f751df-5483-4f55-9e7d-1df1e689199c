package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-TELEFONO.
 *
 */
@Setter
@Getter
public class WsAuxTelefono extends DataStruct {
    // Properties
    private AlphanumericVarArray wsAuxTelX = new AlphanumericVarArray(this, 60, 1);          // 10 WS-AUX-TEL-X X(01) [60]

    public WsAuxTelefono() {
        super();
        initialize();
    }

    public WsAuxTelefono(DataContainer parent) {
        super(parent);
    }

    public WsAuxTelefono(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWsAuxTelX(int index) {
        return this.wsAuxTelX.at(index);
    }

    public void setWsAuxTelX(final String wsAuxTelX) {
        this.wsAuxTelX.setValue(wsAuxTelX);
    }

    public void setWsAuxTelX(final AlphanumericVarArray wsAuxTelX) {
        this.wsAuxTelX.setValue(wsAuxTelX);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAuxTelX.clear();
    }

}

