package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.fechaformatiii.FechaFormatMmaa;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: FECHA-FORMAT-III.
 *
 */
@Setter
@Getter
public class FechaFormatIii { // VStruct
    // Properties
    private FechaFormatMmaa fechaFormatMmaa = new  FechaFormatMmaa();        // 05 FECHA-FORMAT-MMAA
    private int fechaFormatDdIii;                                           // 05 FECHA-FORMAT-DD-III 9(02)

    public FechaFormatIii() {
        initialize();
    }




    // Deeper properties

    // Property: fechaFormatMmaa.fechaFormatFiller -> 10 FECHA-FORMAT-FILLER 9(02)
    public int getFechaFormatFiller() {
        return this.fechaFormatMmaa.getFechaFormatFiller();
    }

    public void setFechaFormatFiller(final int fechaFormatFiller) {
        this.fechaFormatMmaa.setFechaFormatFiller(fechaFormatFiller);
    }

    // Property: fechaFormatMmaa.fechaFormatAaaa -> 10 FECHA-FORMAT-AAAA 9(04)
    public int getFechaFormatAaaa() {
        return this.fechaFormatMmaa.getFechaFormatAaaa();
    }

    public void setFechaFormatAaaa(final int fechaFormatAaaa) {
        this.fechaFormatMmaa.setFechaFormatAaaa(fechaFormatAaaa);
    }

    // Property: fechaFormatMmaa.fechaFormatMmIii -> 10 FECHA-FORMAT-MM-III 9(02)
    public int getFechaFormatMmIii() {
        return this.fechaFormatMmaa.getFechaFormatMmIii();
    }

    public void setFechaFormatMmIii(final int fechaFormatMmIii) {
        this.fechaFormatMmaa.setFechaFormatMmIii(fechaFormatMmIii);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.fechaFormatMmaa.initialize();
        this.fechaFormatDdIii = 0;
    }

}

