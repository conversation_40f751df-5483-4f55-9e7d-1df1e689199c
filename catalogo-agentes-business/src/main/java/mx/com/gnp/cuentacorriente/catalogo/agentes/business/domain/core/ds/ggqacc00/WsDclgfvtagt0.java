package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGFVTAGT0.
 *
 */
@Setter
@Getter
public class WsDclgfvtagt0 { // VStruct
    // Properties
    private String agtNomConcatenado = "";                                  // 10 AGT-NOM-CONCATENADO X(100)
    private String agtNalCve = "";                                          // 10 AGT-NAL-CVE X(3)
    private int agtPefCve;                                                  // 10 AGT-PEF-CVE S9(4) COMPUTATIONAL
    private String agtTcaCve = "";                                          // 10 AGT-TCA-CVE X(2)
    private int agtMteCve;                                                  // 10 AGT-MTE-CVE S9(4) COMPUTATIONAL

    public WsDclgfvtagt0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.agtNomConcatenado = "";
        this.agtNalCve = "";
        this.agtPefCve = 0;
        this.agtTcaCve = "";
        this.agtMteCve = 0;
    }

}

