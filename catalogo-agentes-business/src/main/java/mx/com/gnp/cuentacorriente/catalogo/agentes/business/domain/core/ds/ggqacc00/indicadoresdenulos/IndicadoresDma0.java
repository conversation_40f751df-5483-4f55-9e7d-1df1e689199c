package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICADORES-DMA0.
 *
 */
@Setter
@Getter
public class IndicadoresDma0 { // VStruct
    // Properties
    private int inddmaagt = 0;                                              // 05 INDDMAAGT S9(4) COMPUTATIONAL
    private int inddmaidr = 0;                                              // 05 INDDMAIDR S9(4) COMPUTATIONAL
    private int inddmacol = 0;                                              // 05 INDDMACOL S9(4) COMPUTATIONAL
    private int inddmatdm = 0;                                              // 05 INDDMATDM S9(4) COMPUTATIONAL
    private int inddmacpo = 0;                                              // 05 INDDMACPO S9(4) COMPUTATIONAL

    public IndicadoresDma0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.inddmaagt = 0;
        this.inddmaidr = 0;
        this.inddmacol = 0;
        this.inddmatdm = 0;
        this.inddmacpo = 0;
    }

}

