package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-DATA-SAT.
 *
 */
@Setter
@Getter
public class RpDataSat { // VStruct
    // Properties
    private String rpFolioFiscal = "";                                      // 10 RP-FOLIO-FISCAL X(20)
    private NumericVar rpPercepGrav = new NumericVar(13, 2);                 // 10 RP-PERCEP-GRAV S9(11)V9(02)
    private NumericVar rpPercepExen = new NumericVar(13, 2);                 // 10 RP-PERCEP-EXEN S9(11)V9(02)
    private NumericVar rpIva = new NumericVar(13, 2);                        // 10 RP-IVA S9(11)V9(02)
    private NumericVar rpIsr = new NumericVar(13, 2);                        // 10 RP-ISR S9(11)V9(02)
    private NumericVar rpIvaRtn = new NumericVar(13, 2);                     // 10 RP-IVA-RTN S9(11)V9(02)
    private NumericVar rpIvaAcr = new NumericVar(13, 2);                     // 10 RP-IVA-ACR S9(11)V9(02)
    private NumericVar rpIsrRet = new NumericVar(13, 2);                     // 10 RP-ISR-RET S9(11)V9(02)
    private NumericVar rpIpuRtnCed = new NumericVar(13, 2);                  // 10 RP-IPU-RTN-CED S9(11)V9(02)
    private NumericVar rpTasaIva = new NumericVar(5, 2);                     // 10 RP-TASA-IVA S9(03)V9(02)
    private NumericVar rpTasaCed = new NumericVar(5, 2);                     // 10 RP-TASA-CED S9(03)V9(02)

    public RpDataSat() {
        initialize();
    }



    public void setRpPercepGrav(final BigDecimal rpPercepGrav) {
        this.rpPercepGrav.setValue(rpPercepGrav);
    }

    public void setRpPercepGrav(final int rpPercepGrav) {
        this.rpPercepGrav.setValue(rpPercepGrav);
    }

    public void setRpPercepGrav(final INumericValue rpPercepGrav) {
        this.rpPercepGrav.setValue(rpPercepGrav);
    }

    public void setRpPercepExen(final BigDecimal rpPercepExen) {
        this.rpPercepExen.setValue(rpPercepExen);
    }

    public void setRpPercepExen(final int rpPercepExen) {
        this.rpPercepExen.setValue(rpPercepExen);
    }

    public void setRpPercepExen(final INumericValue rpPercepExen) {
        this.rpPercepExen.setValue(rpPercepExen);
    }

    public void setRpIva(final BigDecimal rpIva) {
        this.rpIva.setValue(rpIva);
    }

    public void setRpIva(final int rpIva) {
        this.rpIva.setValue(rpIva);
    }

    public void setRpIva(final INumericValue rpIva) {
        this.rpIva.setValue(rpIva);
    }

    public void setRpIsr(final BigDecimal rpIsr) {
        this.rpIsr.setValue(rpIsr);
    }

    public void setRpIsr(final int rpIsr) {
        this.rpIsr.setValue(rpIsr);
    }

    public void setRpIsr(final INumericValue rpIsr) {
        this.rpIsr.setValue(rpIsr);
    }

    public void setRpIvaRtn(final BigDecimal rpIvaRtn) {
        this.rpIvaRtn.setValue(rpIvaRtn);
    }

    public void setRpIvaRtn(final int rpIvaRtn) {
        this.rpIvaRtn.setValue(rpIvaRtn);
    }

    public void setRpIvaRtn(final INumericValue rpIvaRtn) {
        this.rpIvaRtn.setValue(rpIvaRtn);
    }

    public void setRpIvaAcr(final BigDecimal rpIvaAcr) {
        this.rpIvaAcr.setValue(rpIvaAcr);
    }

    public void setRpIvaAcr(final int rpIvaAcr) {
        this.rpIvaAcr.setValue(rpIvaAcr);
    }

    public void setRpIvaAcr(final INumericValue rpIvaAcr) {
        this.rpIvaAcr.setValue(rpIvaAcr);
    }

    public void setRpIsrRet(final BigDecimal rpIsrRet) {
        this.rpIsrRet.setValue(rpIsrRet);
    }

    public void setRpIsrRet(final int rpIsrRet) {
        this.rpIsrRet.setValue(rpIsrRet);
    }

    public void setRpIsrRet(final INumericValue rpIsrRet) {
        this.rpIsrRet.setValue(rpIsrRet);
    }

    public void setRpIpuRtnCed(final BigDecimal rpIpuRtnCed) {
        this.rpIpuRtnCed.setValue(rpIpuRtnCed);
    }

    public void setRpIpuRtnCed(final int rpIpuRtnCed) {
        this.rpIpuRtnCed.setValue(rpIpuRtnCed);
    }

    public void setRpIpuRtnCed(final INumericValue rpIpuRtnCed) {
        this.rpIpuRtnCed.setValue(rpIpuRtnCed);
    }

    public void setRpTasaIva(final BigDecimal rpTasaIva) {
        this.rpTasaIva.setValue(rpTasaIva);
    }

    public void setRpTasaIva(final int rpTasaIva) {
        this.rpTasaIva.setValue(rpTasaIva);
    }

    public void setRpTasaIva(final INumericValue rpTasaIva) {
        this.rpTasaIva.setValue(rpTasaIva);
    }

    public void setRpTasaCed(final BigDecimal rpTasaCed) {
        this.rpTasaCed.setValue(rpTasaCed);
    }

    public void setRpTasaCed(final int rpTasaCed) {
        this.rpTasaCed.setValue(rpTasaCed);
    }

    public void setRpTasaCed(final INumericValue rpTasaCed) {
        this.rpTasaCed.setValue(rpTasaCed);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpFolioFiscal = "";
        this.rpPercepGrav.clear();
        this.rpPercepExen.clear();
        this.rpIva.clear();
        this.rpIsr.clear();
        this.rpIvaRtn.clear();
        this.rpIvaAcr.clear();
        this.rpIsrRet.clear();
        this.rpIpuRtnCed.clear();
        this.rpTasaIva.clear();
        this.rpTasaCed.clear();
    }

}

