package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DB2-AAAA-MM-DD.
 *
 */
// 798053000*                                                                 05300002
@Setter
@Getter
public class Db2AaaaMmDd { // VStruct
    // Properties
    private int db2Aaaa;                                                    // 03 DB2-AAAA 9999
    private String filler1 = "-";                                           // 03 FILLER X
    private int db2Mm;                                                      // 03 DB2-MM 99
    private String filler2 = "-";                                           // 03 FILLER X
    private int db2Dd;                                                      // 03 DB2-DD 99

    public Db2AaaaMmDd() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.db2Aaaa = 0;
        this.filler1 = "-";
        this.db2Mm = 0;
        this.filler2 = "-";
        this.db2Dd = 0;
    }

}

