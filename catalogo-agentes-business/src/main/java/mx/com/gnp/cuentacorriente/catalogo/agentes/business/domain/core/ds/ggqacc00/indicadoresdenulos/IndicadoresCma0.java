package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.indicadoresdenulos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICADORES-CMA0.
 *
 */
@Setter
@Getter
public class IndicadoresCma0 { // VStruct
    // Properties
    private int indcmades = 0;                                              // 05 INDCMADES S9(4) COMPUTATIONAL

    public IndicadoresCma0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.indcmades = 0;
    }

}

