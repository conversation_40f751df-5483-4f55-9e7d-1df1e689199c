package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablameses;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-MES-R.
 *
 */
@Setter
@Getter
public class TablaMesR extends DataStructArray<TablaMesR> {
    // Properties
    private UnsignedNumericVar tbDia = new UnsignedNumericVar(this, 2, 0);   // 07 TB-DIA 9(02)

    public TablaMesR(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TablaMesR(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TablaMesR(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TablaMesR(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TablaMesR at(int idx) {
        return new TablaMesR(this, idx, getSizeElement());
    }

    public void setTbDia(final int tbDia) {
        this.tbDia.setValue(tbDia);
    }

    public void setTbDia(final BigDecimal tbDia) {
        this.tbDia.setValue(tbDia);
    }

    public void setTbDia(final INumericValue tbDia) {
        this.tbDia.setValue(tbDia);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbDia.clear();
    }

}

