package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: ERROR-MESSAGE.
 *
 */
@Setter
@Getter
public class ErrorMessage extends DataStruct {
    // Properties
    private BinaryVar errorLen = new BinaryVar(this, 4, 0);                  // 02 ERROR-LEN S9(4) COMPUTATIONAL
    private AlphanumericVarArray errorText = new AlphanumericVarArray(this, 10, 72);         // 02 ERROR-TEXT X(72) [10]

    public ErrorMessage() {
        super();
        initialize();
    }

    public ErrorMessage(DataContainer parent) {
        super(parent);
    }

    public ErrorMessage(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setErrorLen(final int errorLen) {
        this.errorLen.setValue(errorLen);
    }

    public void setErrorLen(final BigDecimal errorLen) {
        this.errorLen.setValue(errorLen);
    }

    public void setErrorLen(final INumericValue errorLen) {
        this.errorLen.setValue(errorLen);
    }

    public AlphanumericVar getErrorText(int index) {
        return this.errorText.at(index);
    }

    public void setErrorText(final String errorText) {
        this.errorText.setValue(errorText);
    }

    public void setErrorText(final AlphanumericVarArray errorText) {
        this.errorText.setValue(errorText);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.errorLen.setValue(720);
        this.errorText.clear();
    }

}

