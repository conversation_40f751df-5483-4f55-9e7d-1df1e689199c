package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GCCTORV0-NULL.
 *
 */
// 1037057200*                                                                 05720000
// 1038057300*                                                                 05730000
@Setter
@Getter
public class WsGcctorv0Null { // VStruct
    // Properties
    private int orv01;                                                      // 03 ORV01 S9(4) COMPUTATIONAL
    private int orv02;                                                      // 03 ORV02 S9(4) COMPUTATIONAL
    private int orv03;                                                      // 03 ORV03 S9(4) COMPUTATIONAL
    private int orv04;                                                      // 03 ORV04 S9(4) COMPUTATIONAL
    private int orv05;                                                      // 03 ORV05 S9(4) COMPUTATIONAL
    private int orv06;                                                      // 03 ORV06 S9(4) COMPUTATIONAL
    private int orv07;                                                      // 03 ORV07 S9(4) COMPUTATIONAL
    private int orv08;                                                      // 03 ORV08 S9(4) COMPUTATIONAL
    private int orv09;                                                      // 03 ORV09 S9(4) COMPUTATIONAL

    public WsGcctorv0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.orv01 = 0;
        this.orv02 = 0;
        this.orv03 = 0;
        this.orv04 = 0;
        this.orv05 = 0;
        this.orv06 = 0;
        this.orv07 = 0;
        this.orv08 = 0;
        this.orv09 = 0;
    }

}

