package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-PJE-IVA-R.
 *
 */
@Setter
@Getter
public class WsPjeIvaR extends DataStruct {
    // Properties
    private AlphanumericVar rsPjeIva3 = new AlphanumericVar(this, 3);        // 10 RS-PJE-IVA3 X(03)
    private AlphanumericVar rsPjeIva2 = new AlphanumericVar(this, 2);        // 10 RS-PJE-IVA2 X(02)

    public WsPjeIvaR() {
        super();
        initialize();
    }

    public WsPjeIvaR(DataContainer parent) {
        super(parent);
    }

    public WsPjeIvaR(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setRsPjeIva3(final String rsPjeIva3) {
        this.rsPjeIva3.setValue(rsPjeIva3);
    }

    public void setRsPjeIva3(final IAlphanumericValue rsPjeIva3) {
        this.rsPjeIva3.setValue(rsPjeIva3);
    }

    public void setRsPjeIva2(final String rsPjeIva2) {
        this.rsPjeIva2.setValue(rsPjeIva2);
    }

    public void setRsPjeIva2(final IAlphanumericValue rsPjeIva2) {
        this.rsPjeIva2.setValue(rsPjeIva2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.rsPjeIva3.clear();
        this.rsPjeIva2.clear();
    }

}

