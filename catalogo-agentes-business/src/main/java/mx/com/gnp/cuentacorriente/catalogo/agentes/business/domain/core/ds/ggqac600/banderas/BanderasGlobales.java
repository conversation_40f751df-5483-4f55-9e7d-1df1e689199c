package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.banderas;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: BANDERAS-GLOBALES.
 *
 */
@Setter
@Getter
public class BanderasGlobales { // VStruct
    // Properties
    private String stExcAgt = "00";                                         // 03 ST-EXC-AGT X(02)
    private int wsFinAgtExc = 0;                                            // 03 WS-FIN-AGT-EXC 9(01)
    // Level 88 - Conditional names
    private static final int FIN_AGT_EXC = 1;

    private int wsEncAgtExc = 0;                                            // 03 WS-ENC-AGT-EXC 9(01)
    // Level 88 - Conditional names
    private static final int ENC_AGT_EXC = 1;


    public BanderasGlobales() {
        initialize();
    }



    // Conditionals (88) - WS-FIN-AGT-EXC
    public boolean isFinAgtExc() {
        return this.wsFinAgtExc == FIN_AGT_EXC;
    }

    public void setFinAgtExc() {
        this.wsFinAgtExc = FIN_AGT_EXC;
    }


    // Conditionals (88) - WS-ENC-AGT-EXC
    public boolean isEncAgtExc() {
        return this.wsEncAgtExc == ENC_AGT_EXC;
    }

    public void setEncAgtExc() {
        this.wsEncAgtExc = ENC_AGT_EXC;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.stExcAgt = "00";
        this.wsFinAgtExc = 0;
        this.wsEncAgtExc = 0;
    }

}

