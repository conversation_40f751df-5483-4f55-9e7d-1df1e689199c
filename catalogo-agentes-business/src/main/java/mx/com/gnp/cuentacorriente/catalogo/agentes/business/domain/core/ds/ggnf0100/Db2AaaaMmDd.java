package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.db2aaaammdd.Db2Aaaar;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DB2-AAAA-MM-DD.
 *
 */
// 1274080200*                                                                 08020000
@Setter
@Getter
public class Db2AaaaMmDd extends DataStruct {
    // Properties
    private UnsignedNumericVar db2Aaaa = new UnsignedNumericVar(this, 4, 0);                 // 03 DB2-AAAA 9999 REDEFINED BY DB2-AAAAR
    private Db2Aaaar db2Aaaar = new  Db2Aaaar(this, this.db2Aaaa);           // 03 DB2-AAAAR REDEFINES DB2-AAAA
    private AlphanumericVar filler1 = new AlphanumericVar(this, 1);          // 03 FILLER X
    private UnsignedNumericVar db2Mm = new UnsignedNumericVar(this, 2, 0);   // 03 DB2-MM 99
    private AlphanumericVar filler2 = new AlphanumericVar(this, 1);          // 03 FILLER X
    private UnsignedNumericVar db2Dd = new UnsignedNumericVar(this, 2, 0);   // 03 DB2-DD 99

    public Db2AaaaMmDd() {
        super();
        initialize();
    }

    public Db2AaaaMmDd(DataContainer parent) {
        super(parent);
    }

    public Db2AaaaMmDd(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setDb2Aaaa(final int db2Aaaa) {
        this.db2Aaaa.setValue(db2Aaaa);
    }

    public void setDb2Aaaa(final BigDecimal db2Aaaa) {
        this.db2Aaaa.setValue(db2Aaaa);
    }

    public void setDb2Aaaa(final INumericValue db2Aaaa) {
        this.db2Aaaa.setValue(db2Aaaa);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setDb2Mm(final int db2Mm) {
        this.db2Mm.setValue(db2Mm);
    }

    public void setDb2Mm(final BigDecimal db2Mm) {
        this.db2Mm.setValue(db2Mm);
    }

    public void setDb2Mm(final INumericValue db2Mm) {
        this.db2Mm.setValue(db2Mm);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setDb2Dd(final int db2Dd) {
        this.db2Dd.setValue(db2Dd);
    }

    public void setDb2Dd(final BigDecimal db2Dd) {
        this.db2Dd.setValue(db2Dd);
    }

    public void setDb2Dd(final INumericValue db2Dd) {
        this.db2Dd.setValue(db2Dd);
    }


    // Deeper properties

    // Property: db2Aaaar.db2Aa1 -> 05 DB2-AA1 99
    public UnsignedNumericVar getDb2Aa1() {
        return this.db2Aaaar.getDb2Aa1();
    }

    public void setDb2Aa1(final int db2Aa1) {
        this.db2Aaaar.setDb2Aa1(db2Aa1);
    }

    public void setDb2Aa1(final INumericValue db2Aa1) {
        this.db2Aaaar.setDb2Aa1(db2Aa1);
    }

    // Property: db2Aaaar.db2Aa2 -> 05 DB2-AA2 99
    public UnsignedNumericVar getDb2Aa2() {
        return this.db2Aaaar.getDb2Aa2();
    }

    public void setDb2Aa2(final int db2Aa2) {
        this.db2Aaaar.setDb2Aa2(db2Aa2);
    }

    public void setDb2Aa2(final INumericValue db2Aa2) {
        this.db2Aaaar.setDb2Aa2(db2Aa2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.db2Aaaa.clear();
        this.filler1.setValue("-");
        this.db2Mm.clear();
        this.filler2.setValue("-");
        this.db2Dd.clear();
    }

}

