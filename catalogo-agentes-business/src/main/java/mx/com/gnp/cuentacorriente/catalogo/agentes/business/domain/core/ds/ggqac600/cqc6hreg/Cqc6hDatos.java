package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.cqc6hreg;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: CQC6H-DATOS.
 *
 */
@Setter
@Getter
public class Cqc6hDatos extends DataStruct {
    // Properties
    private AlphanumericVar cqc6hAgt = new AlphanumericVar(this, 5);         // 10 CQC6H-AGT X(05)
    private AlphanumericVar cqc6hSpaces = new AlphanumericVar(this, 1);      // 10 CQC6H-SPACES X(01)
    private AlphanumericVar cqc6hIndExc = new AlphanumericVar(this, 1);      // 10 CQC6H-IND-EXC X(01)

    public Cqc6hDatos() {
        super();
        initialize();
    }

    public Cqc6hDatos(DataContainer parent) {
        super(parent);
    }

    public Cqc6hDatos(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setCqc6hAgt(final String cqc6hAgt) {
        this.cqc6hAgt.setValue(cqc6hAgt);
    }

    public void setCqc6hAgt(final IAlphanumericValue cqc6hAgt) {
        this.cqc6hAgt.setValue(cqc6hAgt);
    }

    public void setCqc6hSpaces(final String cqc6hSpaces) {
        this.cqc6hSpaces.setValue(cqc6hSpaces);
    }

    public void setCqc6hSpaces(final IAlphanumericValue cqc6hSpaces) {
        this.cqc6hSpaces.setValue(cqc6hSpaces);
    }

    public void setCqc6hIndExc(final String cqc6hIndExc) {
        this.cqc6hIndExc.setValue(cqc6hIndExc);
    }

    public void setCqc6hIndExc(final IAlphanumericValue cqc6hIndExc) {
        this.cqc6hIndExc.setValue(cqc6hIndExc);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.cqc6hAgt.clear();
        this.cqc6hSpaces.clear();
        this.cqc6hIndExc.clear();
    }

}

