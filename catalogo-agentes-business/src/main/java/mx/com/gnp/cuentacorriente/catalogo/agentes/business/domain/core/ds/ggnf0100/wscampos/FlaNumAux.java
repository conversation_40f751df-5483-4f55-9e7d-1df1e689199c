package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: FLA-NUM-AUX.
 *
 */
// 1204073600*                                                                 07360000
@Setter
@Getter
public class FlaNumAux { // VStruct
    // Properties
    private String flaNum0Aux = "";                                         // 05 FLA-NUM0-AUX X
    private int flaNum1Aux;                                                 // 05 FLA-NUM1-AUX 9(7)

    public FlaNumAux() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.flaNum0Aux = "";
        this.flaNum1Aux = 0;
    }

}

