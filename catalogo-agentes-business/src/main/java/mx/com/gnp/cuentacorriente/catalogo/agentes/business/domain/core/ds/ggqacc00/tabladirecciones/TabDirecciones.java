package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tabladirecciones;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TAB-DIRECCIONES.
 *
 */
@Setter
@Getter
public class TabDirecciones extends DataStructArray<TabDirecciones> {
    // Properties
    private UnsignedNumericVar tbDirOfna = new UnsignedNumericVar(this, 4, 0);               // 07 TB-DIR-OFNA 9(04)
    private AlphanumericVar tbDirCae = new AlphanumericVar(this, 40);        // 07 TB-DIR-CAE X(40)
    private AlphanumericVar tbDirNum = new AlphanumericVar(this, 5);         // 07 TB-DIR-NUM X(05)
    private AlphanumericVar tbDirInt = new AlphanumericVar(this, 5);         // 07 TB-DIR-INT X(05)
    private AlphanumericVar tbDirCol = new AlphanumericVar(this, 40);        // 07 TB-DIR-COL X(40)
    private AlphanumericVar tbDirMndNom = new AlphanumericVar(this, 50);     // 07 TB-DIR-MND-NOM X(50)
    private AlphanumericVar tbDirPobNom = new AlphanumericVar(this, 40);     // 07 TB-DIR-POB-NOM X(40)
    private AlphanumericVar tbDirMndEdoCve = new AlphanumericVar(this, 4);   // 07 TB-DIR-MND-EDO-CVE X(04)
    private AlphanumericVar tbDirMndEdoNom = new AlphanumericVar(this, 30);  // 07 TB-DIR-MND-EDO-NOM X(30)
    private AlphanumericVar tbDirMndPaiCve = new AlphanumericVar(this, 3);   // 07 TB-DIR-MND-PAI-CVE X(03)
    private AlphanumericVar tbDirMndPaiNom = new AlphanumericVar(this, 30);  // 07 TB-DIR-MND-PAI-NOM X(30)
    private UnsignedNumericVar tbDirCpoCve = new UnsignedNumericVar(this, 5, 0);             // 07 TB-DIR-CPO-CVE 9(05)
    private UnsignedNumericVar tbDirGerZonEnv = new UnsignedNumericVar(this, 4, 0);          // 07 TB-DIR-GER-ZON-ENV 9(04)

    public TabDirecciones(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TabDirecciones(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TabDirecciones(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TabDirecciones(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TabDirecciones at(int idx) {
        return new TabDirecciones(this, idx, getSizeElement());
    }

    public void setTbDirOfna(final int tbDirOfna) {
        this.tbDirOfna.setValue(tbDirOfna);
    }

    public void setTbDirOfna(final BigDecimal tbDirOfna) {
        this.tbDirOfna.setValue(tbDirOfna);
    }

    public void setTbDirOfna(final INumericValue tbDirOfna) {
        this.tbDirOfna.setValue(tbDirOfna);
    }

    public void setTbDirCae(final String tbDirCae) {
        this.tbDirCae.setValue(tbDirCae);
    }

    public void setTbDirCae(final IAlphanumericValue tbDirCae) {
        this.tbDirCae.setValue(tbDirCae);
    }

    public void setTbDirNum(final String tbDirNum) {
        this.tbDirNum.setValue(tbDirNum);
    }

    public void setTbDirNum(final IAlphanumericValue tbDirNum) {
        this.tbDirNum.setValue(tbDirNum);
    }

    public void setTbDirInt(final String tbDirInt) {
        this.tbDirInt.setValue(tbDirInt);
    }

    public void setTbDirInt(final IAlphanumericValue tbDirInt) {
        this.tbDirInt.setValue(tbDirInt);
    }

    public void setTbDirCol(final String tbDirCol) {
        this.tbDirCol.setValue(tbDirCol);
    }

    public void setTbDirCol(final IAlphanumericValue tbDirCol) {
        this.tbDirCol.setValue(tbDirCol);
    }

    public void setTbDirMndNom(final String tbDirMndNom) {
        this.tbDirMndNom.setValue(tbDirMndNom);
    }

    public void setTbDirMndNom(final IAlphanumericValue tbDirMndNom) {
        this.tbDirMndNom.setValue(tbDirMndNom);
    }

    public void setTbDirPobNom(final String tbDirPobNom) {
        this.tbDirPobNom.setValue(tbDirPobNom);
    }

    public void setTbDirPobNom(final IAlphanumericValue tbDirPobNom) {
        this.tbDirPobNom.setValue(tbDirPobNom);
    }

    public void setTbDirMndEdoCve(final String tbDirMndEdoCve) {
        this.tbDirMndEdoCve.setValue(tbDirMndEdoCve);
    }

    public void setTbDirMndEdoCve(final IAlphanumericValue tbDirMndEdoCve) {
        this.tbDirMndEdoCve.setValue(tbDirMndEdoCve);
    }

    public void setTbDirMndEdoNom(final String tbDirMndEdoNom) {
        this.tbDirMndEdoNom.setValue(tbDirMndEdoNom);
    }

    public void setTbDirMndEdoNom(final IAlphanumericValue tbDirMndEdoNom) {
        this.tbDirMndEdoNom.setValue(tbDirMndEdoNom);
    }

    public void setTbDirMndPaiCve(final String tbDirMndPaiCve) {
        this.tbDirMndPaiCve.setValue(tbDirMndPaiCve);
    }

    public void setTbDirMndPaiCve(final IAlphanumericValue tbDirMndPaiCve) {
        this.tbDirMndPaiCve.setValue(tbDirMndPaiCve);
    }

    public void setTbDirMndPaiNom(final String tbDirMndPaiNom) {
        this.tbDirMndPaiNom.setValue(tbDirMndPaiNom);
    }

    public void setTbDirMndPaiNom(final IAlphanumericValue tbDirMndPaiNom) {
        this.tbDirMndPaiNom.setValue(tbDirMndPaiNom);
    }

    public void setTbDirCpoCve(final int tbDirCpoCve) {
        this.tbDirCpoCve.setValue(tbDirCpoCve);
    }

    public void setTbDirCpoCve(final BigDecimal tbDirCpoCve) {
        this.tbDirCpoCve.setValue(tbDirCpoCve);
    }

    public void setTbDirCpoCve(final INumericValue tbDirCpoCve) {
        this.tbDirCpoCve.setValue(tbDirCpoCve);
    }

    public void setTbDirGerZonEnv(final int tbDirGerZonEnv) {
        this.tbDirGerZonEnv.setValue(tbDirGerZonEnv);
    }

    public void setTbDirGerZonEnv(final BigDecimal tbDirGerZonEnv) {
        this.tbDirGerZonEnv.setValue(tbDirGerZonEnv);
    }

    public void setTbDirGerZonEnv(final INumericValue tbDirGerZonEnv) {
        this.tbDirGerZonEnv.setValue(tbDirGerZonEnv);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbDirOfna.clear();
        this.tbDirCae.clear();
        this.tbDirNum.clear();
        this.tbDirInt.clear();
        this.tbDirCol.clear();
        this.tbDirMndNom.clear();
        this.tbDirPobNom.clear();
        this.tbDirMndEdoCve.clear();
        this.tbDirMndEdoNom.clear();
        this.tbDirMndPaiCve.clear();
        this.tbDirMndPaiNom.clear();
        this.tbDirCpoCve.clear();
        this.tbDirGerZonEnv.clear();
    }

}

