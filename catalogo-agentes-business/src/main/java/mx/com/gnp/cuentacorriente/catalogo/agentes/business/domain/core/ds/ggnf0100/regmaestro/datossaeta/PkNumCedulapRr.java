package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PK-NUM-CEDULAP-RR.
 *
 */
@Setter
@Getter
public class PkNumCedulapRr extends DataStruct {
    // Properties
    private AlphanumericVar pkNumCedulaPTres = new AlphanumericVar(this, 3);                 // 07 PK-NUM-CEDULA-P-TRES XXX
    private AlphanumericVar pkNumCedulaPR17 = new AlphanumericVar(this, 17);                 // 07 PK-NUM-CEDULA-P-R17 X(17)

    public PkNumCedulapRr() {
        super();
        initialize();
    }

    public PkNumCedulapRr(DataContainer parent) {
        super(parent);
    }

    public PkNumCedulapRr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkNumCedulaPTres(final String pkNumCedulaPTres) {
        this.pkNumCedulaPTres.setValue(pkNumCedulaPTres);
    }

    public void setPkNumCedulaPTres(final IAlphanumericValue pkNumCedulaPTres) {
        this.pkNumCedulaPTres.setValue(pkNumCedulaPTres);
    }

    public void setPkNumCedulaPR17(final String pkNumCedulaPR17) {
        this.pkNumCedulaPR17.setValue(pkNumCedulaPR17);
    }

    public void setPkNumCedulaPR17(final IAlphanumericValue pkNumCedulaPR17) {
        this.pkNumCedulaPR17.setValue(pkNumCedulaPR17);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkNumCedulaPTres.clear();
        this.pkNumCedulaPR17.clear();
    }

}

