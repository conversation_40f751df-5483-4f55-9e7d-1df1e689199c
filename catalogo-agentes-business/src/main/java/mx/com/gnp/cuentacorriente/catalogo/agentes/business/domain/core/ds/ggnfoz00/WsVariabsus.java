package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus.WsDrcPjeCom5R;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus.WsPjeIvaR;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus.WsPjeIsrR;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus.WsComNivR;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus.WsImpFzaR;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00.wsvariabsus.WsPjeFavR;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-VARIABSUS.
 *
 */
// 683      ***                                                               
@Setter
@Getter
public class WsVariabsus { // VStruct
    // Properties
    private NumericEditedVar wsSqlcode = new NumericEditedVar("-ZZZ9");      // 05 WS-SQLCODE -ZZZ9
    private String fsAgtIdr = "";                                           // 05 FS-AGT-IDR X(5)
    private String fsTfpCvev = "";                                          // 05 FS-TFP-CVEV X(3)
    private String fsTfpCven = "";                                          // 05 FS-TFP-CVEN X(3)
    private String fsAgtFecCamRazSo = "";                                   // 05 FS-AGT-FEC-CAM-RAZ-SO X(10)
    private String fsFlaNum = "";                                           // 05 FS-FLA-NUM X(7)
    private int wsDraCve;                                                   // 05 WS-DRA-CVE S9(4) COMPUTATIONAL
    private int wsAgtIdr;                                                   // 05 WS-AGT-IDR S9(9) COMPUTATIONAL
    private String wsAgtFecCamRazSo = "";                                   // 05 WS-AGT-FEC-CAM-RAZ-SO X(10)
    private int wsAgtClfLmr;                                                // 05 WS-AGT-CLF-LMR S9(4) COMPUTATIONAL
    private int wsPefCve;                                                   // 05 WS-PEF-CVE S9(4) COMPUTATIONAL
    private int wsMteCve;                                                   // 05 WS-MTE-CVE S9(4) COMPUTATIONAL
    private int wsAgtNumFolInv;                                             // 05 WS-AGT-NUM-FOL-INV S9(9) COMPUTATIONAL
    private int wsCnaNum;                                                   // 05 WS-CNA-NUM S9(4) COMPUTATIONAL
    private PackedVar wsDrcPjeCom = new PackedVar(10, 7);                    // 05 WS-DRC-PJE-COM S9(3)V9(7) COMPUTATIONAL-3
    private UnsignedNumericVar wsDrcPjeCom5 = new UnsignedNumericVar(10, 7);                 // 05 WS-DRC-PJE-COM5 9(3)V9(7) REDEFINED BY WS-DRC-PJE-COM5-R
    private WsDrcPjeCom5R wsDrcPjeCom5R = new  WsDrcPjeCom5R(null, this.wsDrcPjeCom5);       // 05 WS-DRC-PJE-COM5-R REDEFINES WS-DRC-PJE-COM5
    private PackedVar wsCnaPjeIva = new PackedVar(5, 2);                     // 05 WS-CNA-PJE-IVA S9(3)V9(2) COMPUTATIONAL-3
    private UnsignedNumericVar wsPjeIva = new UnsignedNumericVar(5, 2);      // 05 WS-PJE-IVA 9(3)V9(2) REDEFINED BY WS-PJE-IVA-R
    private WsPjeIvaR wsPjeIvaR = new  WsPjeIvaR(null, this.wsPjeIva);       // 05 WS-PJE-IVA-R REDEFINES WS-PJE-IVA
    private PackedVar wsCnaPjeIsr = new PackedVar(5, 2);                     // 05 WS-CNA-PJE-ISR S9(3)V9(2) COMPUTATIONAL-3
    private UnsignedNumericVar wsPjeIsr = new UnsignedNumericVar(5, 2);      // 05 WS-PJE-ISR 9(3)V9(2) REDEFINED BY WS-PJE-ISR-R
    private WsPjeIsrR wsPjeIsrR = new  WsPjeIsrR(null, this.wsPjeIsr);       // 05 WS-PJE-ISR-R REDEFINES WS-PJE-ISR
    private PackedVar wsCnaImpComNiv = new PackedVar(13, 2);                 // 05 WS-CNA-IMP-COM-NIV S9(11)V9(2) COMPUTATIONAL-3
    private UnsignedNumericVar wsComNiv = new UnsignedNumericVar(13, 2);     // 05 WS-COM-NIV 9(11)V9(2) REDEFINED BY WS-COM-NIV-R
    private WsComNivR wsComNivR = new  WsComNivR(null, this.wsComNiv);       // 05 WS-COM-NIV-R REDEFINES WS-COM-NIV
    private PackedVar wsCnaImpFza = new PackedVar(13, 2);                    // 05 WS-CNA-IMP-FZA S9(11)V9(2) COMPUTATIONAL-3
    private UnsignedNumericVar wsImpFza = new UnsignedNumericVar(13, 2);     // 05 WS-IMP-FZA 9(11)V9(2) REDEFINED BY WS-IMP-FZA-R
    private WsImpFzaR wsImpFzaR = new  WsImpFzaR(null, this.wsImpFza);       // 05 WS-IMP-FZA-R REDEFINES WS-IMP-FZA
    private PackedVar wsCnaPjeFav = new PackedVar(7, 4);                     // 05 WS-CNA-PJE-FAV S9(3)V9(4) COMPUTATIONAL-3
    private UnsignedNumericVar wsPjeFav = new UnsignedNumericVar(7, 4);      // 05 WS-PJE-FAV 9(3)V9(4) REDEFINED BY WS-PJE-FAV-R
    private WsPjeFavR wsPjeFavR = new  WsPjeFavR(null, this.wsPjeFav);       // 05 WS-PJE-FAV-R REDEFINES WS-PJE-FAV
    private int wsPpgCve;                                                   // 05 WS-PPG-CVE S9(4) COMPUTATIONAL
    private int wsCpgCve;                                                   // 05 WS-CPG-CVE S9(4) COMPUTATIONAL
    private int wsCdeCve;                                                   // 05 WS-CDE-CVE S9(4) COMPUTATIONAL
    private int wsOfnCve;                                                   // 05 WS-OFN-CVE S9(4) COMPUTATIONAL
    private int wsGnaCve;                                                   // 05 WS-GNA-CVE S9(4) COMPUTATIONAL
    private int wsTfpCve;                                                   // 05 WS-TFP-CVE S9(4) COMPUTATIONAL
    private int wsAgtIdrRcl;                                                // 05 WS-AGT-IDR-RCL S9(9) COMPUTATIONAL
    private int wsMdeCve;                                                   // 05 WS-MDE-CVE S9(4) COMPUTATIONAL
    private int wsZecCve;                                                   // 05 WS-ZEC-CVE S9(4) COMPUTATIONAL
    private int wsCnaGerZonEnv;                                             // 05 WS-CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    private int wsCnaGerZonDcm;                                             // 05 WS-CNA-GER-ZON-DCM S9(4) COMPUTATIONAL
    private int wsDmaIdr;                                                   // 05 WS-DMA-IDR S9(4) COMPUTATIONAL
    private int wsTdmCve;                                                   // 05 WS-TDM-CVE S9(4) COMPUTATIONAL
    private int wsPobCve;                                                   // 05 WS-POB-CVE S9(9) COMPUTATIONAL
    private int wsCpoCve;                                                   // 05 WS-CPO-CVE S9(9) COMPUTATIONAL
    private int wsMndCve;                                                   // 05 WS-MND-CVE S9(9) COMPUTATIONAL
    private int wsCcmCve;                                                   // 05 WS-CCM-CVE S9(4) COMPUTATIONAL
    private String wsEssRek = "";                                           // 05 WS-ESS-REK X(1)

    public WsVariabsus() {
        initialize();
    }



    public void setWsSqlcode(final String wsSqlcode) {
        this.wsSqlcode.setValue(wsSqlcode);
    }

    public void setWsSqlcode(final IValue wsSqlcode) {
        this.wsSqlcode.setValue(wsSqlcode);
    }

    public void setWsDrcPjeCom(final BigDecimal wsDrcPjeCom) {
        this.wsDrcPjeCom.setValue(wsDrcPjeCom);
    }

    public void setWsDrcPjeCom(final int wsDrcPjeCom) {
        this.wsDrcPjeCom.setValue(wsDrcPjeCom);
    }

    public void setWsDrcPjeCom(final INumericValue wsDrcPjeCom) {
        this.wsDrcPjeCom.setValue(wsDrcPjeCom);
    }

    public void setWsDrcPjeCom5(final BigDecimal wsDrcPjeCom5) {
        this.wsDrcPjeCom5.setValue(wsDrcPjeCom5);
    }

    public void setWsDrcPjeCom5(final int wsDrcPjeCom5) {
        this.wsDrcPjeCom5.setValue(wsDrcPjeCom5);
    }

    public void setWsDrcPjeCom5(final INumericValue wsDrcPjeCom5) {
        this.wsDrcPjeCom5.setValue(wsDrcPjeCom5);
    }

    public void setWsCnaPjeIva(final BigDecimal wsCnaPjeIva) {
        this.wsCnaPjeIva.setValue(wsCnaPjeIva);
    }

    public void setWsCnaPjeIva(final int wsCnaPjeIva) {
        this.wsCnaPjeIva.setValue(wsCnaPjeIva);
    }

    public void setWsCnaPjeIva(final INumericValue wsCnaPjeIva) {
        this.wsCnaPjeIva.setValue(wsCnaPjeIva);
    }

    public void setWsPjeIva(final BigDecimal wsPjeIva) {
        this.wsPjeIva.setValue(wsPjeIva);
    }

    public void setWsPjeIva(final int wsPjeIva) {
        this.wsPjeIva.setValue(wsPjeIva);
    }

    public void setWsPjeIva(final INumericValue wsPjeIva) {
        this.wsPjeIva.setValue(wsPjeIva);
    }

    public void setWsCnaPjeIsr(final BigDecimal wsCnaPjeIsr) {
        this.wsCnaPjeIsr.setValue(wsCnaPjeIsr);
    }

    public void setWsCnaPjeIsr(final int wsCnaPjeIsr) {
        this.wsCnaPjeIsr.setValue(wsCnaPjeIsr);
    }

    public void setWsCnaPjeIsr(final INumericValue wsCnaPjeIsr) {
        this.wsCnaPjeIsr.setValue(wsCnaPjeIsr);
    }

    public void setWsPjeIsr(final BigDecimal wsPjeIsr) {
        this.wsPjeIsr.setValue(wsPjeIsr);
    }

    public void setWsPjeIsr(final int wsPjeIsr) {
        this.wsPjeIsr.setValue(wsPjeIsr);
    }

    public void setWsPjeIsr(final INumericValue wsPjeIsr) {
        this.wsPjeIsr.setValue(wsPjeIsr);
    }

    public void setWsCnaImpComNiv(final BigDecimal wsCnaImpComNiv) {
        this.wsCnaImpComNiv.setValue(wsCnaImpComNiv);
    }

    public void setWsCnaImpComNiv(final int wsCnaImpComNiv) {
        this.wsCnaImpComNiv.setValue(wsCnaImpComNiv);
    }

    public void setWsCnaImpComNiv(final INumericValue wsCnaImpComNiv) {
        this.wsCnaImpComNiv.setValue(wsCnaImpComNiv);
    }

    public void setWsComNiv(final BigDecimal wsComNiv) {
        this.wsComNiv.setValue(wsComNiv);
    }

    public void setWsComNiv(final int wsComNiv) {
        this.wsComNiv.setValue(wsComNiv);
    }

    public void setWsComNiv(final INumericValue wsComNiv) {
        this.wsComNiv.setValue(wsComNiv);
    }

    public void setWsCnaImpFza(final BigDecimal wsCnaImpFza) {
        this.wsCnaImpFza.setValue(wsCnaImpFza);
    }

    public void setWsCnaImpFza(final int wsCnaImpFza) {
        this.wsCnaImpFza.setValue(wsCnaImpFza);
    }

    public void setWsCnaImpFza(final INumericValue wsCnaImpFza) {
        this.wsCnaImpFza.setValue(wsCnaImpFza);
    }

    public void setWsImpFza(final BigDecimal wsImpFza) {
        this.wsImpFza.setValue(wsImpFza);
    }

    public void setWsImpFza(final int wsImpFza) {
        this.wsImpFza.setValue(wsImpFza);
    }

    public void setWsImpFza(final INumericValue wsImpFza) {
        this.wsImpFza.setValue(wsImpFza);
    }

    public void setWsCnaPjeFav(final BigDecimal wsCnaPjeFav) {
        this.wsCnaPjeFav.setValue(wsCnaPjeFav);
    }

    public void setWsCnaPjeFav(final int wsCnaPjeFav) {
        this.wsCnaPjeFav.setValue(wsCnaPjeFav);
    }

    public void setWsCnaPjeFav(final INumericValue wsCnaPjeFav) {
        this.wsCnaPjeFav.setValue(wsCnaPjeFav);
    }

    public void setWsPjeFav(final BigDecimal wsPjeFav) {
        this.wsPjeFav.setValue(wsPjeFav);
    }

    public void setWsPjeFav(final int wsPjeFav) {
        this.wsPjeFav.setValue(wsPjeFav);
    }

    public void setWsPjeFav(final INumericValue wsPjeFav) {
        this.wsPjeFav.setValue(wsPjeFav);
    }


    // Deeper properties

    // Property: wsDrcPjeCom5R.rsDrcPjeCom53 -> 10 RS-DRC-PJE-COM53 X(03)
    public AlphanumericVar getRsDrcPjeCom53() {
        return this.wsDrcPjeCom5R.getRsDrcPjeCom53();
    }

    public void setRsDrcPjeCom53(final String rsDrcPjeCom53) {
        this.wsDrcPjeCom5R.setRsDrcPjeCom53(rsDrcPjeCom53);
    }

    public void setRsDrcPjeCom53(final IAlphanumericValue rsDrcPjeCom53) {
        this.wsDrcPjeCom5R.setRsDrcPjeCom53(rsDrcPjeCom53);
    }

    // Property: wsDrcPjeCom5R.rsDrcPjeCom57 -> 10 RS-DRC-PJE-COM57 X(07)
    public AlphanumericVar getRsDrcPjeCom57() {
        return this.wsDrcPjeCom5R.getRsDrcPjeCom57();
    }

    public void setRsDrcPjeCom57(final String rsDrcPjeCom57) {
        this.wsDrcPjeCom5R.setRsDrcPjeCom57(rsDrcPjeCom57);
    }

    public void setRsDrcPjeCom57(final IAlphanumericValue rsDrcPjeCom57) {
        this.wsDrcPjeCom5R.setRsDrcPjeCom57(rsDrcPjeCom57);
    }


    // Deeper properties

    // Property: wsPjeIvaR.rsPjeIva3 -> 10 RS-PJE-IVA3 X(03)
    public AlphanumericVar getRsPjeIva3() {
        return this.wsPjeIvaR.getRsPjeIva3();
    }

    public void setRsPjeIva3(final String rsPjeIva3) {
        this.wsPjeIvaR.setRsPjeIva3(rsPjeIva3);
    }

    public void setRsPjeIva3(final IAlphanumericValue rsPjeIva3) {
        this.wsPjeIvaR.setRsPjeIva3(rsPjeIva3);
    }

    // Property: wsPjeIvaR.rsPjeIva2 -> 10 RS-PJE-IVA2 X(02)
    public AlphanumericVar getRsPjeIva2() {
        return this.wsPjeIvaR.getRsPjeIva2();
    }

    public void setRsPjeIva2(final String rsPjeIva2) {
        this.wsPjeIvaR.setRsPjeIva2(rsPjeIva2);
    }

    public void setRsPjeIva2(final IAlphanumericValue rsPjeIva2) {
        this.wsPjeIvaR.setRsPjeIva2(rsPjeIva2);
    }


    // Deeper properties

    // Property: wsPjeIsrR.rsPjeIsr3 -> 10 RS-PJE-ISR3 X(03)
    public AlphanumericVar getRsPjeIsr3() {
        return this.wsPjeIsrR.getRsPjeIsr3();
    }

    public void setRsPjeIsr3(final String rsPjeIsr3) {
        this.wsPjeIsrR.setRsPjeIsr3(rsPjeIsr3);
    }

    public void setRsPjeIsr3(final IAlphanumericValue rsPjeIsr3) {
        this.wsPjeIsrR.setRsPjeIsr3(rsPjeIsr3);
    }

    // Property: wsPjeIsrR.rsPjeIsr2 -> 10 RS-PJE-ISR2 X(02)
    public AlphanumericVar getRsPjeIsr2() {
        return this.wsPjeIsrR.getRsPjeIsr2();
    }

    public void setRsPjeIsr2(final String rsPjeIsr2) {
        this.wsPjeIsrR.setRsPjeIsr2(rsPjeIsr2);
    }

    public void setRsPjeIsr2(final IAlphanumericValue rsPjeIsr2) {
        this.wsPjeIsrR.setRsPjeIsr2(rsPjeIsr2);
    }


    // Deeper properties

    // Property: wsComNivR.rsComNiv11 -> 10 RS-COM-NIV11 X(11)
    public AlphanumericVar getRsComNiv11() {
        return this.wsComNivR.getRsComNiv11();
    }

    public void setRsComNiv11(final String rsComNiv11) {
        this.wsComNivR.setRsComNiv11(rsComNiv11);
    }

    public void setRsComNiv11(final IAlphanumericValue rsComNiv11) {
        this.wsComNivR.setRsComNiv11(rsComNiv11);
    }

    // Property: wsComNivR.rsComNiv2 -> 10 RS-COM-NIV2 X(02)
    public AlphanumericVar getRsComNiv2() {
        return this.wsComNivR.getRsComNiv2();
    }

    public void setRsComNiv2(final String rsComNiv2) {
        this.wsComNivR.setRsComNiv2(rsComNiv2);
    }

    public void setRsComNiv2(final IAlphanumericValue rsComNiv2) {
        this.wsComNivR.setRsComNiv2(rsComNiv2);
    }


    // Deeper properties

    // Property: wsImpFzaR.rsImpFza11 -> 10 RS-IMP-FZA11 X(11)
    public AlphanumericVar getRsImpFza11() {
        return this.wsImpFzaR.getRsImpFza11();
    }

    public void setRsImpFza11(final String rsImpFza11) {
        this.wsImpFzaR.setRsImpFza11(rsImpFza11);
    }

    public void setRsImpFza11(final IAlphanumericValue rsImpFza11) {
        this.wsImpFzaR.setRsImpFza11(rsImpFza11);
    }

    // Property: wsImpFzaR.rsImpFza2 -> 10 RS-IMP-FZA2 X(02)
    public AlphanumericVar getRsImpFza2() {
        return this.wsImpFzaR.getRsImpFza2();
    }

    public void setRsImpFza2(final String rsImpFza2) {
        this.wsImpFzaR.setRsImpFza2(rsImpFza2);
    }

    public void setRsImpFza2(final IAlphanumericValue rsImpFza2) {
        this.wsImpFzaR.setRsImpFza2(rsImpFza2);
    }


    // Deeper properties

    // Property: wsPjeFavR.rsPjeFav3 -> 10 RS-PJE-FAV3 X(03)
    public AlphanumericVar getRsPjeFav3() {
        return this.wsPjeFavR.getRsPjeFav3();
    }

    public void setRsPjeFav3(final String rsPjeFav3) {
        this.wsPjeFavR.setRsPjeFav3(rsPjeFav3);
    }

    public void setRsPjeFav3(final IAlphanumericValue rsPjeFav3) {
        this.wsPjeFavR.setRsPjeFav3(rsPjeFav3);
    }

    // Property: wsPjeFavR.rsPjeFav4 -> 10 RS-PJE-FAV4 X(04)
    public AlphanumericVar getRsPjeFav4() {
        return this.wsPjeFavR.getRsPjeFav4();
    }

    public void setRsPjeFav4(final String rsPjeFav4) {
        this.wsPjeFavR.setRsPjeFav4(rsPjeFav4);
    }

    public void setRsPjeFav4(final IAlphanumericValue rsPjeFav4) {
        this.wsPjeFavR.setRsPjeFav4(rsPjeFav4);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsSqlcode.clear();
        this.fsAgtIdr = "";
        this.fsTfpCvev = "";
        this.fsTfpCven = "";
        this.fsAgtFecCamRazSo = "";
        this.fsFlaNum = "";
        this.wsDraCve = 0;
        this.wsAgtIdr = 0;
        this.wsAgtFecCamRazSo = "";
        this.wsAgtClfLmr = 0;
        this.wsPefCve = 0;
        this.wsMteCve = 0;
        this.wsAgtNumFolInv = 0;
        this.wsCnaNum = 0;
        this.wsDrcPjeCom.clear();
        this.wsDrcPjeCom5.clear();
        this.wsCnaPjeIva.clear();
        this.wsPjeIva.clear();
        this.wsCnaPjeIsr.clear();
        this.wsPjeIsr.clear();
        this.wsCnaImpComNiv.clear();
        this.wsComNiv.clear();
        this.wsCnaImpFza.clear();
        this.wsImpFza.clear();
        this.wsCnaPjeFav.clear();
        this.wsPjeFav.clear();
        this.wsPpgCve = 0;
        this.wsCpgCve = 0;
        this.wsCdeCve = 0;
        this.wsOfnCve = 0;
        this.wsGnaCve = 0;
        this.wsTfpCve = 0;
        this.wsAgtIdrRcl = 0;
        this.wsMdeCve = 0;
        this.wsZecCve = 0;
        this.wsCnaGerZonEnv = 0;
        this.wsCnaGerZonDcm = 0;
        this.wsDmaIdr = 0;
        this.wsTdmCve = 0;
        this.wsPobCve = 0;
        this.wsCpoCve = 0;
        this.wsMndCve = 0;
        this.wsCcmCve = 0;
        this.wsEssRek = "";
    }

}

