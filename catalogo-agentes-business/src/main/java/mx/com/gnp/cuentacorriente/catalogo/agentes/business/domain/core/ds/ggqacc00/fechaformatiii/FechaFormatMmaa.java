package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.fechaformatiii;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: FECHA-FORMAT-MMAA.
 *
 */
@Setter
@Getter
public class FechaFormatMmaa { // VStruct
    // Properties
    private int fechaFormatFiller = 0;                                      // 10 FECHA-FORMAT-FILLER 9(02)
    private int fechaFormatAaaa;                                            // 10 FECHA-FORMAT-AAAA 9(04)
    private int fechaFormatMmIii;                                           // 10 FECHA-FORMAT-MM-III 9(02)

    public FechaFormatMmaa() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.fechaFormatFiller = 0;
        this.fechaFormatAaaa = 0;
        this.fechaFormatMmIii = 0;
    }

}

