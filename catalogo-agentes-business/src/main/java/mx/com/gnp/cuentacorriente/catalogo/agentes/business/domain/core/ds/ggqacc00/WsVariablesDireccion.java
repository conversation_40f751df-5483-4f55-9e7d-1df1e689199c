package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-VARIABLES-DIRECCION.
 *
 */
@Setter
@Getter
public class WsVariablesDireccion { // VStruct
    // Properties
    private String wsCae = "";                                              // 03 WS-CAE X(40)
    private String wsNum = "";                                              // 03 WS-NUM X(05)
    private String wsInt = "";                                              // 03 WS-INT X(05)
    private String wsCol = "";                                              // 03 WS-COL X(40)
    private String wsMndNom = "";                                           // 03 WS-MND-NOM X(50)
    private String wsPobNom = "";                                           // 03 WS-POB-NOM X(40)
    private String wsMndEdoCve = "";                                        // 03 WS-MND-EDO-CVE X(04)
    private String wsMndEdoNom = "";                                        // 03 WS-MND-EDO-NOM X(30)
    private String wsMndPaiCve = "";                                        // 03 WS-MND-PAI-CVE X(03)
    private String wsMndPaiNom = "";                                        // 03 WS-MND-PAI-NOM X(30)
    private int wsCpoCve;                                                   // 03 WS-CPO-CVE 9(05)
    private int wsGerZonEnv;                                                // 03 WS-GER-ZON-ENV 9(04)

    public WsVariablesDireccion() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsCae = "";
        this.wsNum = "";
        this.wsInt = "";
        this.wsCol = "";
        this.wsMndNom = "";
        this.wsPobNom = "";
        this.wsMndEdoCve = "";
        this.wsMndEdoNom = "";
        this.wsMndPaiCve = "";
        this.wsMndPaiNom = "";
        this.wsCpoCve = 0;
        this.wsGerZonEnv = 0;
    }

}

