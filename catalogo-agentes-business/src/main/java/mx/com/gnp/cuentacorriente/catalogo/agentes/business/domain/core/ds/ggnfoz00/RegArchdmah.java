package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-ARCHDMAH.
 *
 */
@Setter
@Getter
public class RegArchdmah extends DataStruct {
    // Properties
    private UnsignedNumericVar agtIdr3 = new UnsignedNumericVar(this, 9, 0);                 // 05 AGT-IDR3 9(09)
    private AlphanumericVar agtFecCamRazSo3 = new AlphanumericVar(this, 10);                 // 05 AGT-FEC-CAM-RAZ-SO3 X(10)
    private UnsignedNumericVar dmaIdr3 = new UnsignedNumericVar(this, 5, 0);                 // 05 DMA-IDR3 9(05)
    private AlphanumericVar dmaCae3 = new AlphanumericVar(this, 40);         // 05 DMA-CAE3 X(40)
    private AlphanumericVar dmaNum3 = new AlphanumericVar(this, 5);          // 05 DMA-NUM3 X(05)
    private AlphanumericVar dmaInt3 = new AlphanumericVar(this, 5);          // 05 DMA-INT3 X(05)
    private AlphanumericVar dmaCol3 = new AlphanumericVar(this, 40);         // 05 DMA-COL3 X(40)
    private UnsignedNumericVar tdmCve3 = new UnsignedNumericVar(this, 5, 0);                 // 05 TDM-CVE3 9(05)
    private UnsignedNumericVar pobCve3 = new UnsignedNumericVar(this, 9, 0);                 // 05 POB-CVE3 9(09)
    private UnsignedNumericVar cpoCve3 = new UnsignedNumericVar(this, 9, 0);                 // 05 CPO-CVE3 9(09)
    private UnsignedNumericVar mndCve3 = new UnsignedNumericVar(this, 9, 0);                 // 05 MND-CVE3 9(09)
    private AlphanumericVar dmaDirExt3 = new AlphanumericVar(this, 200);     // 05 DMA-DIR-EXT3 X(200)
    private AlphanumericVar essRek3 = new AlphanumericVar(this, 1);          // 05 ESS-REK3 X(01)

    public RegArchdmah() {
        super();
        initialize();
    }

    public RegArchdmah(DataContainer parent) {
        super(parent);
    }

    public RegArchdmah(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setAgtIdr3(final int agtIdr3) {
        this.agtIdr3.setValue(agtIdr3);
    }

    public void setAgtIdr3(final BigDecimal agtIdr3) {
        this.agtIdr3.setValue(agtIdr3);
    }

    public void setAgtIdr3(final INumericValue agtIdr3) {
        this.agtIdr3.setValue(agtIdr3);
    }

    public void setAgtFecCamRazSo3(final String agtFecCamRazSo3) {
        this.agtFecCamRazSo3.setValue(agtFecCamRazSo3);
    }

    public void setAgtFecCamRazSo3(final IAlphanumericValue agtFecCamRazSo3) {
        this.agtFecCamRazSo3.setValue(agtFecCamRazSo3);
    }

    public void setDmaIdr3(final int dmaIdr3) {
        this.dmaIdr3.setValue(dmaIdr3);
    }

    public void setDmaIdr3(final BigDecimal dmaIdr3) {
        this.dmaIdr3.setValue(dmaIdr3);
    }

    public void setDmaIdr3(final INumericValue dmaIdr3) {
        this.dmaIdr3.setValue(dmaIdr3);
    }

    public void setDmaCae3(final String dmaCae3) {
        this.dmaCae3.setValue(dmaCae3);
    }

    public void setDmaCae3(final IAlphanumericValue dmaCae3) {
        this.dmaCae3.setValue(dmaCae3);
    }

    public void setDmaNum3(final String dmaNum3) {
        this.dmaNum3.setValue(dmaNum3);
    }

    public void setDmaNum3(final IAlphanumericValue dmaNum3) {
        this.dmaNum3.setValue(dmaNum3);
    }

    public void setDmaInt3(final String dmaInt3) {
        this.dmaInt3.setValue(dmaInt3);
    }

    public void setDmaInt3(final IAlphanumericValue dmaInt3) {
        this.dmaInt3.setValue(dmaInt3);
    }

    public void setDmaCol3(final String dmaCol3) {
        this.dmaCol3.setValue(dmaCol3);
    }

    public void setDmaCol3(final IAlphanumericValue dmaCol3) {
        this.dmaCol3.setValue(dmaCol3);
    }

    public void setTdmCve3(final int tdmCve3) {
        this.tdmCve3.setValue(tdmCve3);
    }

    public void setTdmCve3(final BigDecimal tdmCve3) {
        this.tdmCve3.setValue(tdmCve3);
    }

    public void setTdmCve3(final INumericValue tdmCve3) {
        this.tdmCve3.setValue(tdmCve3);
    }

    public void setPobCve3(final int pobCve3) {
        this.pobCve3.setValue(pobCve3);
    }

    public void setPobCve3(final BigDecimal pobCve3) {
        this.pobCve3.setValue(pobCve3);
    }

    public void setPobCve3(final INumericValue pobCve3) {
        this.pobCve3.setValue(pobCve3);
    }

    public void setCpoCve3(final int cpoCve3) {
        this.cpoCve3.setValue(cpoCve3);
    }

    public void setCpoCve3(final BigDecimal cpoCve3) {
        this.cpoCve3.setValue(cpoCve3);
    }

    public void setCpoCve3(final INumericValue cpoCve3) {
        this.cpoCve3.setValue(cpoCve3);
    }

    public void setMndCve3(final int mndCve3) {
        this.mndCve3.setValue(mndCve3);
    }

    public void setMndCve3(final BigDecimal mndCve3) {
        this.mndCve3.setValue(mndCve3);
    }

    public void setMndCve3(final INumericValue mndCve3) {
        this.mndCve3.setValue(mndCve3);
    }

    public void setDmaDirExt3(final String dmaDirExt3) {
        this.dmaDirExt3.setValue(dmaDirExt3);
    }

    public void setDmaDirExt3(final IAlphanumericValue dmaDirExt3) {
        this.dmaDirExt3.setValue(dmaDirExt3);
    }

    public void setEssRek3(final String essRek3) {
        this.essRek3.setValue(essRek3);
    }

    public void setEssRek3(final IAlphanumericValue essRek3) {
        this.essRek3.setValue(essRek3);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.agtIdr3.clear();
        this.agtFecCamRazSo3.clear();
        this.dmaIdr3.clear();
        this.dmaCae3.clear();
        this.dmaNum3.clear();
        this.dmaInt3.clear();
        this.dmaCol3.clear();
        this.tdmCve3.clear();
        this.pobCve3.clear();
        this.cpoCve3.clear();
        this.mndCve3.clear();
        this.dmaDirExt3.clear();
        this.essRek3.clear();
    }

}

