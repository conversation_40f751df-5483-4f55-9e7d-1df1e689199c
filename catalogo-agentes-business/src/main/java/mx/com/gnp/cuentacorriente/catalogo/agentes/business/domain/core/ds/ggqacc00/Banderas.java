package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.banderas.BanderasGlobales;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: BANDERAS.
 *
 */
@Setter
@Getter
public class Banderas { // VStruct
    // Properties
    private BanderasGlobales banderasGlobales = new  BanderasGlobales();     // 02 BANDERAS-GLOBALES

    public Banderas() {
        initialize();
    }




    // Deeper properties

    // Property: banderasGlobales.wkDisp -> 03 WK-DISP 9(01)
    public int getWkDisp() {
        return this.banderasGlobales.getWkDisp();
    }

    public void setWkDisp(final int wkDisp) {
        this.banderasGlobales.setWkDisp(wkDisp);
    }

    // Property: banderasGlobales.wkDisp1 -> 03 WK-DISP1 9(01)
    public int getWkDisp1() {
        return this.banderasGlobales.getWkDisp1();
    }

    public void setWkDisp1(final int wkDisp1) {
        this.banderasGlobales.setWkDisp1(wkDisp1);
    }

    // Property: banderasGlobales.stExtraccion -> 03 ST-EXTRACCION X(02)
    public String getStExtraccion() {
        return this.banderasGlobales.getStExtraccion();
    }

    public void setStExtraccion(final String stExtraccion) {
        this.banderasGlobales.setStExtraccion(stExtraccion);
    }

    // Property: banderasGlobales.stFacturacion -> 03 ST-FACTURACION X(02)
    public String getStFacturacion() {
        return this.banderasGlobales.getStFacturacion();
    }

    public void setStFacturacion(final String stFacturacion) {
        this.banderasGlobales.setStFacturacion(stFacturacion);
    }

    // Property: banderasGlobales.stDetSusp -> 03 ST-DET-SUSP X(02)
    public String getStDetSusp() {
        return this.banderasGlobales.getStDetSusp();
    }

    public void setStDetSusp(final String stDetSusp) {
        this.banderasGlobales.setStDetSusp(stDetSusp);
    }

    // Property: banderasGlobales.stCatDirec -> 03 ST-CAT-DIREC X(02)
    public String getStCatDirec() {
        return this.banderasGlobales.getStCatDirec();
    }

    public void setStCatDirec(final String stCatDirec) {
        this.banderasGlobales.setStCatDirec(stCatDirec);
    }

    // Property: banderasGlobales.wsSt -> 03 WS-ST X(02)
    public String getWsSt() {
        return this.banderasGlobales.getWsSt();
    }

    public void setWsSt(final String wsSt) {
        this.banderasGlobales.setWsSt(wsSt);
    }

    // Property: banderasGlobales.wsFinExtraccion -> 03 WS-FIN-EXTRACCION 9(01)
    public int getWsFinExtraccion() {
        return this.banderasGlobales.getWsFinExtraccion();
    }

    public void setWsFinExtraccion(final int wsFinExtraccion) {
        this.banderasGlobales.setWsFinExtraccion(wsFinExtraccion);
    }

    // Property: banderasGlobales : condition FIN-EXTRACCION
    public boolean isFinExtraccion() {
        return this.banderasGlobales.isFinExtraccion();
    }

    public void setFinExtraccion() {
        this.banderasGlobales.setFinExtraccion();
    }

    // Property: banderasGlobales.wsFinCatDirecc -> 03 WS-FIN-CAT-DIRECC 9(01)
    public int getWsFinCatDirecc() {
        return this.banderasGlobales.getWsFinCatDirecc();
    }

    public void setWsFinCatDirecc(final int wsFinCatDirecc) {
        this.banderasGlobales.setWsFinCatDirecc(wsFinCatDirecc);
    }

    // Property: banderasGlobales : condition FIN-CAT-DIRECC
    public boolean isFinCatDirecc() {
        return this.banderasGlobales.isFinCatDirecc();
    }

    public void setFinCatDirecc() {
        this.banderasGlobales.setFinCatDirecc();
    }

    // Property: banderasGlobales.wsFinCurMte -> 03 WS-FIN-CUR-MTE 9(01)
    public int getWsFinCurMte() {
        return this.banderasGlobales.getWsFinCurMte();
    }

    public void setWsFinCurMte(final int wsFinCurMte) {
        this.banderasGlobales.setWsFinCurMte(wsFinCurMte);
    }

    // Property: banderasGlobales : condition FIN-CUR-MTE
    public boolean isFinCurMte() {
        return this.banderasGlobales.isFinCurMte();
    }

    public void setFinCurMte() {
        this.banderasGlobales.setFinCurMte();
    }

    // Property: banderasGlobales.wsFinCurEsa -> 03 WS-FIN-CUR-ESA 9(01)
    public int getWsFinCurEsa() {
        return this.banderasGlobales.getWsFinCurEsa();
    }

    public void setWsFinCurEsa(final int wsFinCurEsa) {
        this.banderasGlobales.setWsFinCurEsa(wsFinCurEsa);
    }

    // Property: banderasGlobales : condition FIN-CUR-ESA
    public boolean isFinCurEsa() {
        return this.banderasGlobales.isFinCurEsa();
    }

    public void setFinCurEsa() {
        this.banderasGlobales.setFinCurEsa();
    }

    // Property: banderasGlobales.wsEncDirecc -> 03 WS-ENC-DIRECC 9(01)
    public int getWsEncDirecc() {
        return this.banderasGlobales.getWsEncDirecc();
    }

    public void setWsEncDirecc(final int wsEncDirecc) {
        this.banderasGlobales.setWsEncDirecc(wsEncDirecc);
    }

    // Property: banderasGlobales : condition ENC-DIRECC
    public boolean isEncDirecc() {
        return this.banderasGlobales.isEncDirecc();
    }

    public void setEncDirecc() {
        this.banderasGlobales.setEncDirecc();
    }

    // Property: banderasGlobales.wsEncAgente -> 03 WS-ENC-AGENTE 9(01)
    public int getWsEncAgente() {
        return this.banderasGlobales.getWsEncAgente();
    }

    public void setWsEncAgente(final int wsEncAgente) {
        this.banderasGlobales.setWsEncAgente(wsEncAgente);
    }

    // Property: banderasGlobales : condition ENC-AGENTE
    public boolean isEncAgente() {
        return this.banderasGlobales.isEncAgente();
    }

    public void setEncAgente() {
        this.banderasGlobales.setEncAgente();
    }

    // Property: banderasGlobales.wsEncCna -> 03 WS-ENC-CNA 9(01)
    public int getWsEncCna() {
        return this.banderasGlobales.getWsEncCna();
    }

    public void setWsEncCna(final int wsEncCna) {
        this.banderasGlobales.setWsEncCna(wsEncCna);
    }

    // Property: banderasGlobales : condition ENC-CNA
    public boolean isEncCna() {
        return this.banderasGlobales.isEncCna();
    }

    public void setEncCna() {
        this.banderasGlobales.setEncCna();
    }

    // Property: banderasGlobales.wsEncDsc -> 03 WS-ENC-DSC 9(01)
    public int getWsEncDsc() {
        return this.banderasGlobales.getWsEncDsc();
    }

    public void setWsEncDsc(final int wsEncDsc) {
        this.banderasGlobales.setWsEncDsc(wsEncDsc);
    }

    // Property: banderasGlobales : condition ENC-DSC
    public boolean isEncDsc() {
        return this.banderasGlobales.isEncDsc();
    }

    public void setEncDsc() {
        this.banderasGlobales.setEncDsc();
    }

    // Property: banderasGlobales.wsEncEstatus -> 03 WS-ENC-ESTATUS 9(01)
    public int getWsEncEstatus() {
        return this.banderasGlobales.getWsEncEstatus();
    }

    public void setWsEncEstatus(final int wsEncEstatus) {
        this.banderasGlobales.setWsEncEstatus(wsEncEstatus);
    }

    // Property: banderasGlobales : condition ENC-ESTATUS
    public boolean isEncEstatus() {
        return this.banderasGlobales.isEncEstatus();
    }

    public void setEncEstatus() {
        this.banderasGlobales.setEncEstatus();
    }

    // Property: banderasGlobales.wsEncEstatusCna -> 03 WS-ENC-ESTATUS-CNA 9(01)
    public int getWsEncEstatusCna() {
        return this.banderasGlobales.getWsEncEstatusCna();
    }

    public void setWsEncEstatusCna(final int wsEncEstatusCna) {
        this.banderasGlobales.setWsEncEstatusCna(wsEncEstatusCna);
    }

    // Property: banderasGlobales : condition ENC-ESTATUS-CNA
    public boolean isEncEstatusCna() {
        return this.banderasGlobales.isEncEstatusCna();
    }

    public void setEncEstatusCna() {
        this.banderasGlobales.setEncEstatusCna();
    }

    // Property: banderasGlobales.wsEncExcluido -> 03 WS-ENC-EXCLUIDO 9(01)
    public int getWsEncExcluido() {
        return this.banderasGlobales.getWsEncExcluido();
    }

    public void setWsEncExcluido(final int wsEncExcluido) {
        this.banderasGlobales.setWsEncExcluido(wsEncExcluido);
    }

    // Property: banderasGlobales : condition ENC-EXCLUIDO
    public boolean isEncExcluido() {
        return this.banderasGlobales.isEncExcluido();
    }

    public void setEncExcluido() {
        this.banderasGlobales.setEncExcluido();
    }

    // Property: banderasGlobales.wsAgenteEstatus -> 03 WS-AGENTE-ESTATUS 9(04)
    public int getWsAgenteEstatus() {
        return this.banderasGlobales.getWsAgenteEstatus();
    }

    public void setWsAgenteEstatus(final int wsAgenteEstatus) {
        this.banderasGlobales.setWsAgenteEstatus(wsAgenteEstatus);
    }

    // Property: banderasGlobales : condition AGENTE-ESTATUS
    public boolean isAgenteEstatus() {
        return this.banderasGlobales.isAgenteEstatus();
    }

    public void setAgenteEstatus() {
        this.banderasGlobales.setAgenteEstatus();
    }

    // Property: banderasGlobales : condition AGENTE-VIGENTE
    public boolean isAgenteVigente() {
        return this.banderasGlobales.isAgenteVigente();
    }

    public void setAgenteVigente() {
        this.banderasGlobales.setAgenteVigente();
    }

    // Property: banderasGlobales : condition AGENTE-CANCELADO
    public boolean isAgenteCancelado() {
        return this.banderasGlobales.isAgenteCancelado();
    }

    public void setAgenteCancelado() {
        this.banderasGlobales.setAgenteCancelado();
    }

    // Property: banderasGlobales : condition AGENTE-SUSPENDIDO
    public boolean isAgenteSuspendido() {
        return this.banderasGlobales.isAgenteSuspendido();
    }

    public void setAgenteSuspendido() {
        this.banderasGlobales.setAgenteSuspendido();
    }

    // Property: banderasGlobales.wsCnaEstatus -> 03 WS-CNA-ESTATUS 9(04)
    public int getWsCnaEstatus() {
        return this.banderasGlobales.getWsCnaEstatus();
    }

    public void setWsCnaEstatus(final int wsCnaEstatus) {
        this.banderasGlobales.setWsCnaEstatus(wsCnaEstatus);
    }

    // Property: banderasGlobales : condition CNA-ESTATUS
    public boolean isCnaEstatus() {
        return this.banderasGlobales.isCnaEstatus();
    }

    public void setCnaEstatus() {
        this.banderasGlobales.setCnaEstatus();
    }

    // Property: banderasGlobales : condition CNA-VIGENTE
    public boolean isCnaVigente() {
        return this.banderasGlobales.isCnaVigente();
    }

    public void setCnaVigente() {
        this.banderasGlobales.setCnaVigente();
    }

    // Property: banderasGlobales : condition CNA-CANCELADO
    public boolean isCnaCancelado() {
        return this.banderasGlobales.isCnaCancelado();
    }

    public void setCnaCancelado() {
        this.banderasGlobales.setCnaCancelado();
    }

    // Property: banderasGlobales : condition CNA-SUSPENDIDO
    public boolean isCnaSuspendido() {
        return this.banderasGlobales.isCnaSuspendido();
    }

    public void setCnaSuspendido() {
        this.banderasGlobales.setCnaSuspendido();
    }

    // Property: banderasGlobales.wsFinCurDma -> 03 WS-FIN-CUR-DMA 9(01)
    public int getWsFinCurDma() {
        return this.banderasGlobales.getWsFinCurDma();
    }

    public void setWsFinCurDma(final int wsFinCurDma) {
        this.banderasGlobales.setWsFinCurDma(wsFinCurDma);
    }

    // Property: banderasGlobales : condition FIN-CUR-DMA
    public boolean isFinCurDma() {
        return this.banderasGlobales.isFinCurDma();
    }

    public void setFinCurDma() {
        this.banderasGlobales.setFinCurDma();
    }

    // Property: banderasGlobales.wsEncGfvtagt -> 03 WS-ENC-GFVTAGT 9(01)
    public int getWsEncGfvtagt() {
        return this.banderasGlobales.getWsEncGfvtagt();
    }

    public void setWsEncGfvtagt(final int wsEncGfvtagt) {
        this.banderasGlobales.setWsEncGfvtagt(wsEncGfvtagt);
    }

    // Property: banderasGlobales : condition ENC-GFVTAGT
    public boolean isEncGfvtagt() {
        return this.banderasGlobales.isEncGfvtagt();
    }

    public void setEncGfvtagt() {
        this.banderasGlobales.setEncGfvtagt();
    }

    // Property: banderasGlobales.wsEncGfvtcna -> 03 WS-ENC-GFVTCNA 9(01)
    public int getWsEncGfvtcna() {
        return this.banderasGlobales.getWsEncGfvtcna();
    }

    public void setWsEncGfvtcna(final int wsEncGfvtcna) {
        this.banderasGlobales.setWsEncGfvtcna(wsEncGfvtcna);
    }

    // Property: banderasGlobales : condition ENC-GFVTCNA
    public boolean isEncGfvtcna() {
        return this.banderasGlobales.isEncGfvtcna();
    }

    public void setEncGfvtcna() {
        this.banderasGlobales.setEncGfvtcna();
    }

    // Property: banderasGlobales.wsEncGfvtpra -> 03 WS-ENC-GFVTPRA 9(01)
    public int getWsEncGfvtpra() {
        return this.banderasGlobales.getWsEncGfvtpra();
    }

    public void setWsEncGfvtpra(final int wsEncGfvtpra) {
        this.banderasGlobales.setWsEncGfvtpra(wsEncGfvtpra);
    }

    // Property: banderasGlobales : condition ENC-GFVTPRA
    public boolean isEncGfvtpra() {
        return this.banderasGlobales.isEncGfvtpra();
    }

    public void setEncGfvtpra() {
        this.banderasGlobales.setEncGfvtpra();
    }

    // Property: banderasGlobales.wsEncGcctmnd -> 03 WS-ENC-GCCTMND 9(01)
    public int getWsEncGcctmnd() {
        return this.banderasGlobales.getWsEncGcctmnd();
    }

    public void setWsEncGcctmnd(final int wsEncGcctmnd) {
        this.banderasGlobales.setWsEncGcctmnd(wsEncGcctmnd);
    }

    // Property: banderasGlobales : condition ENC-GCCTMND
    public boolean isEncGcctmnd() {
        return this.banderasGlobales.isEncGcctmnd();
    }

    public void setEncGcctmnd() {
        this.banderasGlobales.setEncGcctmnd();
    }

    // Property: banderasGlobales.wsEncGcctpob -> 03 WS-ENC-GCCTPOB 9(01)
    public int getWsEncGcctpob() {
        return this.banderasGlobales.getWsEncGcctpob();
    }

    public void setWsEncGcctpob(final int wsEncGcctpob) {
        this.banderasGlobales.setWsEncGcctpob(wsEncGcctpob);
    }

    // Property: banderasGlobales : condition ENC-GCCTPOB
    public boolean isEncGcctpob() {
        return this.banderasGlobales.isEncGcctpob();
    }

    public void setEncGcctpob() {
        this.banderasGlobales.setEncGcctpob();
    }

    // Property: banderasGlobales.wsEncGcctedo -> 03 WS-ENC-GCCTEDO 9(01)
    public int getWsEncGcctedo() {
        return this.banderasGlobales.getWsEncGcctedo();
    }

    public void setWsEncGcctedo(final int wsEncGcctedo) {
        this.banderasGlobales.setWsEncGcctedo(wsEncGcctedo);
    }

    // Property: banderasGlobales : condition ENC-GCCTEDO
    public boolean isEncGcctedo() {
        return this.banderasGlobales.isEncGcctedo();
    }

    public void setEncGcctedo() {
        this.banderasGlobales.setEncGcctedo();
    }

    // Property: banderasGlobales.wsEncGcctpai -> 03 WS-ENC-GCCTPAI 9(01)
    public int getWsEncGcctpai() {
        return this.banderasGlobales.getWsEncGcctpai();
    }

    public void setWsEncGcctpai(final int wsEncGcctpai) {
        this.banderasGlobales.setWsEncGcctpai(wsEncGcctpai);
    }

    // Property: banderasGlobales : condition ENC-GCCTPAI
    public boolean isEncGcctpai() {
        return this.banderasGlobales.isEncGcctpai();
    }

    public void setEncGcctpai() {
        this.banderasGlobales.setEncGcctpai();
    }

    // Property: banderasGlobales.wsEncDirec -> 03 WS-ENC-DIREC 9(01)
    public int getWsEncDirec() {
        return this.banderasGlobales.getWsEncDirec();
    }

    public void setWsEncDirec(final int wsEncDirec) {
        this.banderasGlobales.setWsEncDirec(wsEncDirec);
    }

    // Property: banderasGlobales : condition ENC-DIREC
    public boolean isEncDirec() {
        return this.banderasGlobales.isEncDirec();
    }

    public void setEncDirec() {
        this.banderasGlobales.setEncDirec();
    }

    // Property: banderasGlobales.wsEncGfvtcma -> 03 WS-ENC-GFVTCMA 9(01)
    public int getWsEncGfvtcma() {
        return this.banderasGlobales.getWsEncGfvtcma();
    }

    public void setWsEncGfvtcma(final int wsEncGfvtcma) {
        this.banderasGlobales.setWsEncGfvtcma(wsEncGfvtcma);
    }

    // Property: banderasGlobales : condition ENC-GFVTCMA
    public boolean isEncGfvtcma() {
        return this.banderasGlobales.isEncGfvtcma();
    }

    public void setEncGfvtcma() {
        this.banderasGlobales.setEncGfvtcma();
    }

    // Property: banderasGlobales.wsEncTrn -> 03 WS-ENC-TRN 9(01)
    public int getWsEncTrn() {
        return this.banderasGlobales.getWsEncTrn();
    }

    public void setWsEncTrn(final int wsEncTrn) {
        this.banderasGlobales.setWsEncTrn(wsEncTrn);
    }

    // Property: banderasGlobales : condition ENC-TRN
    public boolean isEncTrn() {
        return this.banderasGlobales.isEncTrn();
    }

    public void setEncTrn() {
        this.banderasGlobales.setEncTrn();
    }

    // Property: banderasGlobales.wsEncConcepto -> 03 WS-ENC-CONCEPTO 9(01)
    public int getWsEncConcepto() {
        return this.banderasGlobales.getWsEncConcepto();
    }

    public void setWsEncConcepto(final int wsEncConcepto) {
        this.banderasGlobales.setWsEncConcepto(wsEncConcepto);
    }

    // Property: banderasGlobales : condition ENC-CONCEPTO
    public boolean isEncConcepto() {
        return this.banderasGlobales.isEncConcepto();
    }

    public void setEncConcepto() {
        this.banderasGlobales.setEncConcepto();
    }

    // Property: banderasGlobales.wsSinMovs -> 03 WS-SIN-MOVS 9(01)
    public int getWsSinMovs() {
        return this.banderasGlobales.getWsSinMovs();
    }

    public void setWsSinMovs(final int wsSinMovs) {
        this.banderasGlobales.setWsSinMovs(wsSinMovs);
    }

    // Property: banderasGlobales : condition SIN-MOVS
    public boolean isSinMovs() {
        return this.banderasGlobales.isSinMovs();
    }

    public void setSinMovs() {
        this.banderasGlobales.setSinMovs();
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.banderasGlobales.initialize();
    }

}

