package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WL-PARAMS.
 *
 */
// 570      ******************************************************************
// 571      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 11      *
// 572      ******************************************************************
// 575                                                                  
// 576      *---ACCESO A (GGQA5Q00) - PARA OBTENER EL REGISTRO FISCAL.        
@Setter
@Getter
public class WlParams extends DataStruct {
    // Properties
    private AlphanumericVar wlOpcion = new AlphanumericVar(this, 4);         // 05 WL-OPCION X(04)
    private AlphanumericVar wlAgente = new AlphanumericVar(this, 7);         // 05 WL-AGENTE X(07)

    public WlParams() {
        super();
        initialize();
    }

    public WlParams(DataContainer parent) {
        super(parent);
    }

    public WlParams(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWlOpcion(final String wlOpcion) {
        this.wlOpcion.setValue(wlOpcion);
    }

    public void setWlOpcion(final IAlphanumericValue wlOpcion) {
        this.wlOpcion.setValue(wlOpcion);
    }

    public void setWlAgente(final String wlAgente) {
        this.wlAgente.setValue(wlAgente);
    }

    public void setWlAgente(final IAlphanumericValue wlAgente) {
        this.wlAgente.setValue(wlAgente);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wlOpcion.clear();
        this.wlAgente.clear();
    }

}

