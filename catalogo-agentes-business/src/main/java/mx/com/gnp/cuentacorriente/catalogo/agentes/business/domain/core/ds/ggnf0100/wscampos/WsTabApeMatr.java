package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TAB-APE-MATR.
 *
 */
@Setter
@Getter
public class WsTabApeMatr extends DataStruct {
    // Properties
    private AlphanumericVarArray eleApeMat = new AlphanumericVarArray(this, 30, 1);          // 05 ELE-APE-MAT X [30]

    public WsTabApeMatr() {
        super();
        initialize();
    }

    public WsTabApeMatr(DataContainer parent) {
        super(parent);
    }

    public WsTabApeMatr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getEleApeMat(int index) {
        return this.eleApeMat.at(index);
    }

    public void setEleApeMat(final String eleApeMat) {
        this.eleApeMat.setValue(eleApeMat);
    }

    public void setEleApeMat(final AlphanumericVarArray eleApeMat) {
        this.eleApeMat.setValue(eleApeMat);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.eleApeMat.clear();
    }

}

