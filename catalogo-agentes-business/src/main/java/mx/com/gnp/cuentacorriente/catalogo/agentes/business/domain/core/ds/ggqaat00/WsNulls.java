package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-NULLS.
 *
 */
// 865      *                                                                 
// 866      *-------- AREA DE NULOS -----------------------------------------*
@Setter
@Getter
public class WsNulls { // VStruct
    // Properties
    private int wsNull01;                                                   // 05 WS-NULL01 S9(4) COMPUTATIONAL
    private int wsNull02;                                                   // 05 WS-NULL02 S9(4) COMPUTATIONAL
    private int wsNull03;                                                   // 05 WS-NULL03 S9(4) COMPUTATIONAL
    private int wsNull04;                                                   // 05 WS-NULL04 S9(4) COMPUTATIONAL
    private int wsNull05;                                                   // 05 WS-NULL05 S9(4) COMPUTATIONAL
    private int wsNull06;                                                   // 05 WS-NULL06 S9(4) COMPUTATIONAL
    private int wsNull07;                                                   // 05 WS-NULL07 S9(4) COMPUTATIONAL
    private int wsNull08;                                                   // 05 WS-NULL08 S9(4) COMPUTATIONAL
    private int wsNull09;                                                   // 05 WS-NULL09 S9(4) COMPUTATIONAL
    private int wsNull10;                                                   // 05 WS-NULL10 S9(4) COMPUTATIONAL

    public WsNulls() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsNull01 = 0;
        this.wsNull02 = 0;
        this.wsNull03 = 0;
        this.wsNull04 = 0;
        this.wsNull05 = 0;
        this.wsNull06 = 0;
        this.wsNull07 = 0;
        this.wsNull08 = 0;
        this.wsNull09 = 0;
        this.wsNull10 = 0;
    }

}

