package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tcna0_1;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-CNA0-ELEM-1.
 *
 */
@Setter
@Getter
public class TCna0Elem_1 extends DataStruct {
    // Properties
    private AlphanumericVar sCnaCnaNumElem = new AlphanumericVar(this, 1);   // 20 S-CNA-CNA-NUM-ELEM X(01)

    public TCna0Elem_1() {
        super();
        initialize();
    }

    public TCna0Elem_1(DataContainer parent) {
        super(parent);
    }

    public TCna0Elem_1(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSCnaCnaNumElem(final String sCnaCnaNumElem) {
        this.sCnaCnaNumElem.setValue(sCnaCnaNumElem);
    }

    public void setSCnaCnaNumElem(final IAlphanumericValue sCnaCnaNumElem) {
        this.sCnaCnaNumElem.setValue(sCnaCnaNumElem);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sCnaCnaNumElem.clear();
    }

}

