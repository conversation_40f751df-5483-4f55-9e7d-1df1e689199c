package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.regagentisr.VsLlaveAgt;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-AGENTISR.
 *
 */
@Setter
@Getter
public class RegAgentisr extends DataStruct {
    // Properties
    private VsLlaveAgt vsLlaveAgt = new  VsLlaveAgt(this);                   // 05 VS-LLAVE-AGT
    private AlphanumericVar vsContra = new AlphanumericVar(this, 3);         // 05 VS-CONTRA X(03)
    private AlphanumericVar vsRegfis = new AlphanumericVar(this, 3);         // 05 VS-REGFIS X(03)
    private AlphanumericVar vsNombre = new AlphanumericVar(this, 300);       // 05 VS-NOMBRE X(300)
    private AlphanumericVar vsClave = new AlphanumericVar(this, 5);          // 05 VS-CLAVE X(05)
    private AlphanumericVar vsCedNovi = new AlphanumericVar(this, 5);        // 05 VS-CED-NOVI X(05)
    private AlphanumericVar vsCedVida = new AlphanumericVar(this, 5);        // 05 VS-CED-VIDA X(05)

    public RegAgentisr() {
        super();
        initialize();
    }

    public RegAgentisr(DataContainer parent) {
        super(parent);
    }

    public RegAgentisr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setVsContra(final String vsContra) {
        this.vsContra.setValue(vsContra);
    }

    public void setVsContra(final IAlphanumericValue vsContra) {
        this.vsContra.setValue(vsContra);
    }

    public void setVsRegfis(final String vsRegfis) {
        this.vsRegfis.setValue(vsRegfis);
    }

    public void setVsRegfis(final IAlphanumericValue vsRegfis) {
        this.vsRegfis.setValue(vsRegfis);
    }

    public void setVsNombre(final String vsNombre) {
        this.vsNombre.setValue(vsNombre);
    }

    public void setVsNombre(final IAlphanumericValue vsNombre) {
        this.vsNombre.setValue(vsNombre);
    }

    public void setVsClave(final String vsClave) {
        this.vsClave.setValue(vsClave);
    }

    public void setVsClave(final IAlphanumericValue vsClave) {
        this.vsClave.setValue(vsClave);
    }

    public void setVsCedNovi(final String vsCedNovi) {
        this.vsCedNovi.setValue(vsCedNovi);
    }

    public void setVsCedNovi(final IAlphanumericValue vsCedNovi) {
        this.vsCedNovi.setValue(vsCedNovi);
    }

    public void setVsCedVida(final String vsCedVida) {
        this.vsCedVida.setValue(vsCedVida);
    }

    public void setVsCedVida(final IAlphanumericValue vsCedVida) {
        this.vsCedVida.setValue(vsCedVida);
    }


    // Deeper properties

    // Property: vsLlaveAgt.vsAgente -> 07 VS-AGENTE X(07)
    public AlphanumericVar getVsAgente() {
        return this.vsLlaveAgt.getVsAgente();
    }

    public void setVsAgente(final String vsAgente) {
        this.vsLlaveAgt.setVsAgente(vsAgente);
    }

    public void setVsAgente(final IAlphanumericValue vsAgente) {
        this.vsLlaveAgt.setVsAgente(vsAgente);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.vsLlaveAgt.initialize();
        this.vsContra.clear();
        this.vsRegfis.clear();
        this.vsNombre.clear();
        this.vsClave.clear();
        this.vsCedNovi.clear();
        this.vsCedVida.clear();
    }

}

