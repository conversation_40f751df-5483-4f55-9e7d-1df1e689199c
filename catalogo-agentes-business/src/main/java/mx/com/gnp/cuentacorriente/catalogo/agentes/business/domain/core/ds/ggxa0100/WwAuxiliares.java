package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares.WtCnaOfnCve;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares.WtCnaGerZon;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares.WtClavePos;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WW-AUXILIARES.
 *
 */
@Setter
@Getter
public class WwAuxiliares { // VStruct
    // Properties
    private int wkInd = 0;                                                  // 10 WK-IND 9(02)
    private int wkInd2 = 0;                                                 // 10 WK-IND2 9(02)
    private int wkInd3 = 0;                                                 // 10 WK-IND3 9(02)
    private int wkInd4 = 0;                                                 // 10 WK-IND4 9(02)
    private int wkInd5 = 0;                                                 // 10 WK-IND5 9(03)
    private int wkInd6 = 0;                                                 // 10 WK-IND6 9(03)
    private UnsignedNumericVar wwCnaCnaNum = new UnsignedNumericVar(2, 0);   // 10 WW-CNA-CNA-NUM 9(02) REDEFINED BY WW-CNA-CNA-NUM-R
    private AlphanumericVar wwCnaCnaNumR = new AlphanumericVar(null, this.wwCnaCnaNum, 2);   // 10 WW-CNA-CNA-NUM-R REDEFINES WW-CNA-CNA-NUM X(02)
    private String wwCnaCnaNumJ = "";                                       // 10 WW-CNA-CNA-NUM-J X(02)
    private WtCnaOfnCve wtCnaOfnCve = new  WtCnaOfnCve();                    // 10 WT-CNA-OFN-CVE UsedAsParameter
    private WtCnaGerZon wtCnaGerZon = new  WtCnaGerZon();                    // 10 WT-CNA-GER-ZON UsedAsParameter
    private WtClavePos wtClavePos = new  WtClavePos();                       // 10 WT-CLAVE-POS

    public WwAuxiliares() {
        initialize();
    }



    public void setWwCnaCnaNum(final int wwCnaCnaNum) {
        this.wwCnaCnaNum.setValue(wwCnaCnaNum);
    }

    public void setWwCnaCnaNum(final BigDecimal wwCnaCnaNum) {
        this.wwCnaCnaNum.setValue(wwCnaCnaNum);
    }

    public void setWwCnaCnaNum(final INumericValue wwCnaCnaNum) {
        this.wwCnaCnaNum.setValue(wwCnaCnaNum);
    }

    public void setWwCnaCnaNumR(final String wwCnaCnaNumR) {
        this.wwCnaCnaNumR.setValue(wwCnaCnaNumR);
    }

    public void setWwCnaCnaNumR(final IAlphanumericValue wwCnaCnaNumR) {
        this.wwCnaCnaNumR.setValue(wwCnaCnaNumR);
    }


    // Deeper properties

    // Property: wtCnaOfnCve.wtCnaOfnCveA -> 15 WT-CNA-OFN-CVE-A X(01) [4]
    public AlphanumericVarArray getWtCnaOfnCveA() {
        return this.wtCnaOfnCve.getWtCnaOfnCveA();
    }

    public void setWtCnaOfnCveA(final String wtCnaOfnCveA) {
        this.wtCnaOfnCve.setWtCnaOfnCveA(wtCnaOfnCveA);
    }

    public void setWtCnaOfnCveA(final AlphanumericVarArray wtCnaOfnCveA) {
        this.wtCnaOfnCve.setWtCnaOfnCveA(wtCnaOfnCveA);
    }


    // Deeper properties

    // Property: wtCnaGerZon.wtCnaGerZonA -> 15 WT-CNA-GER-ZON-A X(01) [4]
    public AlphanumericVarArray getWtCnaGerZonA() {
        return this.wtCnaGerZon.getWtCnaGerZonA();
    }

    public void setWtCnaGerZonA(final String wtCnaGerZonA) {
        this.wtCnaGerZon.setWtCnaGerZonA(wtCnaGerZonA);
    }

    public void setWtCnaGerZonA(final AlphanumericVarArray wtCnaGerZonA) {
        this.wtCnaGerZon.setWtCnaGerZonA(wtCnaGerZonA);
    }


    // Deeper properties

    // Property: wtClavePos.wtDigito -> 15 WT-DIGITO X(01) [4]
    public AlphanumericVarArray getWtDigito() {
        return this.wtClavePos.getWtDigito();
    }

    public void setWtDigito(final String wtDigito) {
        this.wtClavePos.setWtDigito(wtDigito);
    }

    public void setWtDigito(final AlphanumericVarArray wtDigito) {
        this.wtClavePos.setWtDigito(wtDigito);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wkInd = 0;
        this.wkInd2 = 0;
        this.wkInd3 = 0;
        this.wkInd4 = 0;
        this.wkInd5 = 0;
        this.wkInd6 = 0;
        this.wwCnaCnaNum.clear();
        this.wwCnaCnaNumJ = "";
        this.wtCnaOfnCve.initialize();
        this.wtCnaGerZon.initialize();
        this.wtClavePos.initialize();
    }

}

