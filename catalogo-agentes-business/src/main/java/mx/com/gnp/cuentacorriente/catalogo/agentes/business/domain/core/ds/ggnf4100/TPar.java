package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100.tpar.TParReg;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-PAR.
 *
 */
@Setter
@Getter
public class TPar { // VStruct
    // Properties
    private TParReg tParReg = new  TParReg(80);                              // 05 T-PAR-REG [80]

    public TPar() {
        initialize();
    }



    public TParReg getTParReg(int index) {
        return this.tParReg.at(index);
    }


    // Deeper properties

    // Property: tParReg.tParTxt -> 10 T-PAR-TXT X(08)
    public AlphanumericVar getTParTxt() {
        return this.tParReg.getTParTxt();
    }

    public void setTParTxt(final String tParTxt) {
        this.tParReg.setTParTxt(tParTxt);
    }

    public void setTParTxt(final IAlphanumericValue tParTxt) {
        this.tParReg.setTParTxt(tParTxt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tParReg.initialize();
    }

}

