package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkRfc;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedular;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedularr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedularrr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedulaPr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta.PkNumCedulapRr;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DATOS-SAETA.
 *
 */
@Setter
@Getter
public class DatosSaeta extends DataStruct {
    // Properties
    private UnsignedNumericVar datosCons = new UnsignedNumericVar(this, 7, 0);               // 05 DATOS-CONS 9(7)
    private PkRfc pkRfc = new  PkRfc(this);                                  // 05 PK-RFC
    private UnsignedNumericVar pkClaveNal = new UnsignedNumericVar(this, 7, 0);              // 05 PK-CLAVE-NAL 9(07)
    private UnsignedNumericVar pkClaveProv = new UnsignedNumericVar(this, 7, 0);             // 05 PK-CLAVE-PROV 9(07)
    private AlphanumericVar pkNombre = new AlphanumericVar(this, 35);        // 05 PK-NOMBRE X(35)
    private AlphanumericVar pkCompania = new AlphanumericVar(this, 1);       // 05 PK-COMPANIA X(01)
    private UnsignedNumericVar pkFecConexion = new UnsignedNumericVar(this, 6, 0);           // 05 PK-FEC-CONEXION 9(06) REDEFINED BY PK-FEC-CONEXIONR
    private AlphanumericVar pkFecConexionr = new AlphanumericVar(this, this.pkFecConexion, 6);               // 05 PK-FEC-CONEXIONR REDEFINES PK-FEC-CONEXION X(6)
    private AlphanumericVar pkCalleP = new AlphanumericVar(this, 35);        // 05 PK-CALLE-P X(35)
    private AlphanumericVar pkColoniaP = new AlphanumericVar(this, 20);      // 05 PK-COLONIA-P X(20)
    private UnsignedNumericVar pkCodPostP = new UnsignedNumericVar(this, 5, 0);              // 05 PK-COD-POST-P 9(05)
    private AlphanumericVar pkPoblP = new AlphanumericVar(this, 25);         // 05 PK-POBL-P X(25)
    private UnsignedNumericVar pkEstadoP = new UnsignedNumericVar(this, 2, 0);               // 05 PK-ESTADO-P 99
    private UnsignedNumericVar pkPaisP = new UnsignedNumericVar(this, 2, 0);                 // 05 PK-PAIS-P 99
    private UnsignedNumericVar pkTelefP = new UnsignedNumericVar(this, 12, 0);               // 05 PK-TELEF-P 9(12) REDEFINED BY PK-TELEF-PR
    private AlphanumericVar pkTelefPr = new AlphanumericVar(this, this.pkTelefP, 12);        // 05 PK-TELEF-PR REDEFINES PK-TELEF-P X(12)
    private AlphanumericVar pkCalleC = new AlphanumericVar(this, 35);        // 05 PK-CALLE-C X(35)
    private AlphanumericVar pkColoniaC = new AlphanumericVar(this, 20);      // 05 PK-COLONIA-C X(20)
    private UnsignedNumericVar pkCodPostC = new UnsignedNumericVar(this, 5, 0);              // 05 PK-COD-POST-C 9(05)
    private AlphanumericVar pkPoblC = new AlphanumericVar(this, 20);         // 05 PK-POBL-C X(20)
    private UnsignedNumericVar pkEstadoC = new UnsignedNumericVar(this, 2, 0);               // 05 PK-ESTADO-C 99
    private UnsignedNumericVar pkPaisC = new UnsignedNumericVar(this, 2, 0);                 // 05 PK-PAIS-C 99
    private UnsignedNumericVar pkTelefC = new UnsignedNumericVar(this, 12, 0);               // 05 PK-TELEF-C 9(12) REDEFINED BY PK-TELEF-CR
    private AlphanumericVar pkTelefCr = new AlphanumericVar(this, this.pkTelefC, 12);        // 05 PK-TELEF-CR REDEFINES PK-TELEF-C X(12)
    private UnsignedNumericVar pkFecNacim = new UnsignedNumericVar(this, 6, 0);              // 05 PK-FEC-NACIM 9(6)
    private UnsignedNumericVar pkNacionalidad = new UnsignedNumericVar(this, 2, 0);          // 05 PK-NACIONALIDAD 99
    private AlphanumericVar pkSexo = new AlphanumericVar(this, 1);           // 05 PK-SEXO X
    private AlphanumericVar pkEdoCivil = new AlphanumericVar(this, 1);       // 05 PK-EDO-CIVIL X
    private UnsignedNumericVar pkEscolaridad = new UnsignedNumericVar(this, 1, 0);           // 05 PK-ESCOLARIDAD 9
    private AlphanumericVar pkNomConyuge = new AlphanumericVar(this, 35);    // 05 PK-NOM-CONYUGE X(35)
    private UnsignedNumericVar pkFecCasam = new UnsignedNumericVar(this, 6, 0);              // 05 PK-FEC-CASAM 9(6)
    private AlphanumericVar pkStatus = new AlphanumericVar(this, 1);         // 05 PK-STATUS X
    private UnsignedNumericVar pkMotStatus = new UnsignedNumericVar(this, 2, 0);             // 05 PK-MOT-STATUS 99
    private UnsignedNumericVar pkFecStatus = new UnsignedNumericVar(this, 6, 0);             // 05 PK-FEC-STATUS 9(6)
    private UnsignedNumericVar pkFecAlta = new UnsignedNumericVar(this, 6, 0);               // 05 PK-FEC-ALTA 9(6)
    private UnsignedNumericVar pkFecTerm = new UnsignedNumericVar(this, 6, 0);               // 05 PK-FEC-TERM 9(6)
    private AlphanumericVar pkTipCedula = new AlphanumericVar(this, 1);      // 05 PK-TIP-CEDULA X
    private AlphanumericVar pkNumCedula = new AlphanumericVar(this, 20);     // 05 PK-NUM-CEDULA X(20) REDEFINED BY PK-NUM-CEDULARRR
    private PkNumCedular pkNumCedular = new  PkNumCedular(this, this.pkNumCedula);           // 05 PK-NUM-CEDULAR REDEFINES PK-NUM-CEDULA
    private PkNumCedularr pkNumCedularr = new  PkNumCedularr(this, this.pkNumCedula);        // 05 PK-NUM-CEDULARR REDEFINES PK-NUM-CEDULA
    private PkNumCedularrr pkNumCedularrr = new  PkNumCedularrr(this, this.pkNumCedula);     // 05 PK-NUM-CEDULARRR REDEFINES PK-NUM-CEDULA
    private UnsignedNumericVar pkIniCedula = new UnsignedNumericVar(this, 6, 0);             // 05 PK-INI-CEDULA 9(6)
    private UnsignedNumericVar pkFinCedula = new UnsignedNumericVar(this, 6, 0);             // 05 PK-FIN-CEDULA 9(6)
    private AlphanumericVar pkRamoP_1 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-1 X(02)
    private AlphanumericVar pkRamoP_2 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-2 X(02)
    private AlphanumericVar pkRamoP_3 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-3 X(02)
    private AlphanumericVar pkRamoP_4 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-4 X(02)
    private AlphanumericVar pkRamoP_5 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-5 X(02)
    private AlphanumericVar pkRamoP_6 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-6 X(02)
    private AlphanumericVar pkRamoP_7 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-7 X(02)
    private AlphanumericVar pkRamoP_8 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-8 X(02)
    private AlphanumericVar pkRamoP_9 = new AlphanumericVar(this, 2);        // 05 PK-RAMO-P-9 X(02)
    private AlphanumericVar pkRamoP_10 = new AlphanumericVar(this, 2);       // 05 PK-RAMO-P-10 X(02)
    private AlphanumericVar pkObservOficS = new AlphanumericVar(this, 60);   // 05 PK-OBSERV-OFIC-S X(60)
    private AlphanumericVar pkRegIva = new AlphanumericVar(this, 10);        // 05 PK-REG-IVA X(10)
    private UnsignedNumericVar pkTipo = new UnsignedNumericVar(this, 2, 0);  // 05 PK-TIPO 99
    private UnsignedNumericVar pkCodPago = new UnsignedNumericVar(this, 2, 0);               // 05 PK-COD-PAGO 99
    private UnsignedNumericVar pkCvePagoImp = new UnsignedNumericVar(this, 2, 0);            // 05 PK-CVE-PAGO-IMP 99
    private UnsignedNumericVar pkPorIva = new UnsignedNumericVar(this, 4, 2);                // 05 PK-POR-IVA 99V99
    private UnsignedNumericVar pkPorIsr = new UnsignedNumericVar(this, 4, 2);                // 05 PK-POR-ISR 99V99
    private AlphanumericVar pkNumFianza = new AlphanumericVar(this, 12);     // 05 PK-NUM-FIANZA X(12)
    private UnsignedNumericVar pkCanFianza = new UnsignedNumericVar(this, 9, 2);             // 05 PK-CAN-FIANZA 9(7)V99
    private UnsignedNumericVar pkIniFianza = new UnsignedNumericVar(this, 6, 0);             // 05 PK-INI-FIANZA 9(6)
    private UnsignedNumericVar pkFinFianza = new UnsignedNumericVar(this, 6, 0);             // 05 PK-FIN-FIANZA 9(6)
    private UnsignedNumericVar pkAgenteRec = new UnsignedNumericVar(this, 7, 0);             // 05 PK-AGENTE-REC 9(7)
    private AlphanumericVar pkOtrasCias = new AlphanumericVar(this, 1);      // 05 PK-OTRAS-CIAS X(1)
    private AlphanumericVar pkEsGerente = new AlphanumericVar(this, 1);      // 05 PK-ES-GERENTE X(1)
    private UnsignedNumericVar pkZonaNal = new UnsignedNumericVar(this, 4, 0);               // 05 PK-ZONA-NAL 9(4)
    private UnsignedNumericVar pkZonaProv = new UnsignedNumericVar(this, 4, 0);              // 05 PK-ZONA-PROV 9(4)
    private UnsignedNumericVar pkZonaNalGte = new UnsignedNumericVar(this, 4, 0);            // 05 PK-ZONA-NAL-GTE 9(04)
    private UnsignedNumericVar pkZonaProvGte = new UnsignedNumericVar(this, 4, 0);           // 05 PK-ZONA-PROV-GTE 9(04)
    private UnsignedNumericVar pkOficinaNal = new UnsignedNumericVar(this, 3, 0);            // 05 PK-OFICINA-NAL 999
    private UnsignedNumericVar pkOficinaProv = new UnsignedNumericVar(this, 3, 0);           // 05 PK-OFICINA-PROV 999
    private UnsignedNumericVar pkFecZonaNal = new UnsignedNumericVar(this, 6, 0);            // 05 PK-FEC-ZONA-NAL 9(06)
    private UnsignedNumericVar pkFecZonaProv = new UnsignedNumericVar(this, 6, 0);           // 05 PK-FEC-ZONA-PROV 9(06)
    private UnsignedNumericVar pkFecZonaNalGte = new UnsignedNumericVar(this, 6, 0);         // 05 PK-FEC-ZONA-NAL-GTE 9(06)
    private UnsignedNumericVar pkFecZonaProvGte = new UnsignedNumericVar(this, 6, 0);        // 05 PK-FEC-ZONA-PROV-GTE 9(06)
    private UnsignedNumericVar pkFecOficinaNal = new UnsignedNumericVar(this, 6, 0);         // 05 PK-FEC-OFICINA-NAL 9(06)
    private UnsignedNumericVar pkFecOficinaProv = new UnsignedNumericVar(this, 6, 0);        // 05 PK-FEC-OFICINA-PROV 9(06)
    private UnsignedNumericVar pkCveAgrup = new UnsignedNumericVar(this, 1, 0);              // 05 PK-CVE-AGRUP 9
    private UnsignedNumericVar pkCveEnvio = new UnsignedNumericVar(this, 2, 0);              // 05 PK-CVE-ENVIO 99
    private UnsignedNumericVar pkCalidad = new UnsignedNumericVar(this, 2, 0);               // 05 PK-CALIDAD 99
    private UnsignedNumericVar pkJefeGpoNal = new UnsignedNumericVar(this, 7, 0);            // 05 PK-JEFE-GPO-NAL 9(7)
    private UnsignedNumericVar pkJefeGpoProv = new UnsignedNumericVar(this, 7, 0);           // 05 PK-JEFE-GPO-PROV 9(7)
    private UnsignedNumericVar pkFecConexionP = new UnsignedNumericVar(this, 6, 0);          // 05 PK-FEC-CONEXION-P 9(6) REDEFINED BY PK-FEC-CONEXION-PR
    private AlphanumericVar pkFecConexionPr = new AlphanumericVar(this, this.pkFecConexionP, 6);             // 05 PK-FEC-CONEXION-PR REDEFINES PK-FEC-CONEXION-P X(6)
    private AlphanumericVar pkNumCedulaP = new AlphanumericVar(this, 20);    // 05 PK-NUM-CEDULA-P X(20) REDEFINED BY PK-NUM-CEDULAP-RR
    private PkNumCedulaPr pkNumCedulaPr = new  PkNumCedulaPr(this, this.pkNumCedulaP);       // 05 PK-NUM-CEDULA-PR REDEFINES PK-NUM-CEDULA-P
    private PkNumCedulapRr pkNumCedulapRr = new  PkNumCedulapRr(this, this.pkNumCedulaP);    // 05 PK-NUM-CEDULAP-RR REDEFINES PK-NUM-CEDULA-P
    private UnsignedNumericVar pkFinCedulaP = new UnsignedNumericVar(this, 6, 0);            // 05 PK-FIN-CEDULA-P 9(6)
    private AlphanumericVar pkStatusProv = new AlphanumericVar(this, 1);     // 05 PK-STATUS-PROV X
    private UnsignedNumericVar pkMotStatusProv = new UnsignedNumericVar(this, 2, 0);         // 05 PK-MOT-STATUS-PROV 99
    private UnsignedNumericVar pkGteZonaNal = new UnsignedNumericVar(this, 7, 0);            // 05 PK-GTE-ZONA-NAL 9(07)
    private UnsignedNumericVar pkGteZonaProv = new UnsignedNumericVar(this, 7, 0);           // 05 PK-GTE-ZONA-PROV 9(07)
    private UnsignedNumericVar pkFuente = new UnsignedNumericVar(this, 2, 0);                // 05 PK-FUENTE 99
    private UnsignedNumericVar pkGrupo = new UnsignedNumericVar(this, 2, 0);                 // 05 PK-GRUPO 99
    private UnsignedNumericVar pkSubgrupo = new UnsignedNumericVar(this, 3, 0);              // 05 PK-SUBGRUPO 999

    public DatosSaeta() {
        super();
        initialize();
    }

    public DatosSaeta(DataContainer parent) {
        super(parent);
    }

    public DatosSaeta(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setDatosCons(final int datosCons) {
        this.datosCons.setValue(datosCons);
    }

    public void setDatosCons(final BigDecimal datosCons) {
        this.datosCons.setValue(datosCons);
    }

    public void setDatosCons(final INumericValue datosCons) {
        this.datosCons.setValue(datosCons);
    }

    public void setPkClaveNal(final int pkClaveNal) {
        this.pkClaveNal.setValue(pkClaveNal);
    }

    public void setPkClaveNal(final BigDecimal pkClaveNal) {
        this.pkClaveNal.setValue(pkClaveNal);
    }

    public void setPkClaveNal(final INumericValue pkClaveNal) {
        this.pkClaveNal.setValue(pkClaveNal);
    }

    public void setPkClaveProv(final int pkClaveProv) {
        this.pkClaveProv.setValue(pkClaveProv);
    }

    public void setPkClaveProv(final BigDecimal pkClaveProv) {
        this.pkClaveProv.setValue(pkClaveProv);
    }

    public void setPkClaveProv(final INumericValue pkClaveProv) {
        this.pkClaveProv.setValue(pkClaveProv);
    }

    public void setPkNombre(final String pkNombre) {
        this.pkNombre.setValue(pkNombre);
    }

    public void setPkNombre(final IAlphanumericValue pkNombre) {
        this.pkNombre.setValue(pkNombre);
    }

    public void setPkCompania(final String pkCompania) {
        this.pkCompania.setValue(pkCompania);
    }

    public void setPkCompania(final IAlphanumericValue pkCompania) {
        this.pkCompania.setValue(pkCompania);
    }

    public void setPkFecConexion(final int pkFecConexion) {
        this.pkFecConexion.setValue(pkFecConexion);
    }

    public void setPkFecConexion(final BigDecimal pkFecConexion) {
        this.pkFecConexion.setValue(pkFecConexion);
    }

    public void setPkFecConexion(final INumericValue pkFecConexion) {
        this.pkFecConexion.setValue(pkFecConexion);
    }

    public void setPkFecConexionr(final String pkFecConexionr) {
        this.pkFecConexionr.setValue(pkFecConexionr);
    }

    public void setPkFecConexionr(final IAlphanumericValue pkFecConexionr) {
        this.pkFecConexionr.setValue(pkFecConexionr);
    }

    public void setPkCalleP(final String pkCalleP) {
        this.pkCalleP.setValue(pkCalleP);
    }

    public void setPkCalleP(final IAlphanumericValue pkCalleP) {
        this.pkCalleP.setValue(pkCalleP);
    }

    public void setPkColoniaP(final String pkColoniaP) {
        this.pkColoniaP.setValue(pkColoniaP);
    }

    public void setPkColoniaP(final IAlphanumericValue pkColoniaP) {
        this.pkColoniaP.setValue(pkColoniaP);
    }

    public void setPkCodPostP(final int pkCodPostP) {
        this.pkCodPostP.setValue(pkCodPostP);
    }

    public void setPkCodPostP(final BigDecimal pkCodPostP) {
        this.pkCodPostP.setValue(pkCodPostP);
    }

    public void setPkCodPostP(final INumericValue pkCodPostP) {
        this.pkCodPostP.setValue(pkCodPostP);
    }

    public void setPkPoblP(final String pkPoblP) {
        this.pkPoblP.setValue(pkPoblP);
    }

    public void setPkPoblP(final IAlphanumericValue pkPoblP) {
        this.pkPoblP.setValue(pkPoblP);
    }

    public void setPkEstadoP(final int pkEstadoP) {
        this.pkEstadoP.setValue(pkEstadoP);
    }

    public void setPkEstadoP(final BigDecimal pkEstadoP) {
        this.pkEstadoP.setValue(pkEstadoP);
    }

    public void setPkEstadoP(final INumericValue pkEstadoP) {
        this.pkEstadoP.setValue(pkEstadoP);
    }

    public void setPkPaisP(final int pkPaisP) {
        this.pkPaisP.setValue(pkPaisP);
    }

    public void setPkPaisP(final BigDecimal pkPaisP) {
        this.pkPaisP.setValue(pkPaisP);
    }

    public void setPkPaisP(final INumericValue pkPaisP) {
        this.pkPaisP.setValue(pkPaisP);
    }

    public void setPkTelefP(final BigDecimal pkTelefP) {
        this.pkTelefP.setValue(pkTelefP);
    }

    public void setPkTelefP(final int pkTelefP) {
        this.pkTelefP.setValue(pkTelefP);
    }

    public void setPkTelefP(final INumericValue pkTelefP) {
        this.pkTelefP.setValue(pkTelefP);
    }

    public void setPkTelefPr(final String pkTelefPr) {
        this.pkTelefPr.setValue(pkTelefPr);
    }

    public void setPkTelefPr(final IAlphanumericValue pkTelefPr) {
        this.pkTelefPr.setValue(pkTelefPr);
    }

    public void setPkCalleC(final String pkCalleC) {
        this.pkCalleC.setValue(pkCalleC);
    }

    public void setPkCalleC(final IAlphanumericValue pkCalleC) {
        this.pkCalleC.setValue(pkCalleC);
    }

    public void setPkColoniaC(final String pkColoniaC) {
        this.pkColoniaC.setValue(pkColoniaC);
    }

    public void setPkColoniaC(final IAlphanumericValue pkColoniaC) {
        this.pkColoniaC.setValue(pkColoniaC);
    }

    public void setPkCodPostC(final int pkCodPostC) {
        this.pkCodPostC.setValue(pkCodPostC);
    }

    public void setPkCodPostC(final BigDecimal pkCodPostC) {
        this.pkCodPostC.setValue(pkCodPostC);
    }

    public void setPkCodPostC(final INumericValue pkCodPostC) {
        this.pkCodPostC.setValue(pkCodPostC);
    }

    public void setPkPoblC(final String pkPoblC) {
        this.pkPoblC.setValue(pkPoblC);
    }

    public void setPkPoblC(final IAlphanumericValue pkPoblC) {
        this.pkPoblC.setValue(pkPoblC);
    }

    public void setPkEstadoC(final int pkEstadoC) {
        this.pkEstadoC.setValue(pkEstadoC);
    }

    public void setPkEstadoC(final BigDecimal pkEstadoC) {
        this.pkEstadoC.setValue(pkEstadoC);
    }

    public void setPkEstadoC(final INumericValue pkEstadoC) {
        this.pkEstadoC.setValue(pkEstadoC);
    }

    public void setPkPaisC(final int pkPaisC) {
        this.pkPaisC.setValue(pkPaisC);
    }

    public void setPkPaisC(final BigDecimal pkPaisC) {
        this.pkPaisC.setValue(pkPaisC);
    }

    public void setPkPaisC(final INumericValue pkPaisC) {
        this.pkPaisC.setValue(pkPaisC);
    }

    public void setPkTelefC(final BigDecimal pkTelefC) {
        this.pkTelefC.setValue(pkTelefC);
    }

    public void setPkTelefC(final int pkTelefC) {
        this.pkTelefC.setValue(pkTelefC);
    }

    public void setPkTelefC(final INumericValue pkTelefC) {
        this.pkTelefC.setValue(pkTelefC);
    }

    public void setPkTelefCr(final String pkTelefCr) {
        this.pkTelefCr.setValue(pkTelefCr);
    }

    public void setPkTelefCr(final IAlphanumericValue pkTelefCr) {
        this.pkTelefCr.setValue(pkTelefCr);
    }

    public void setPkFecNacim(final int pkFecNacim) {
        this.pkFecNacim.setValue(pkFecNacim);
    }

    public void setPkFecNacim(final BigDecimal pkFecNacim) {
        this.pkFecNacim.setValue(pkFecNacim);
    }

    public void setPkFecNacim(final INumericValue pkFecNacim) {
        this.pkFecNacim.setValue(pkFecNacim);
    }

    public void setPkNacionalidad(final int pkNacionalidad) {
        this.pkNacionalidad.setValue(pkNacionalidad);
    }

    public void setPkNacionalidad(final BigDecimal pkNacionalidad) {
        this.pkNacionalidad.setValue(pkNacionalidad);
    }

    public void setPkNacionalidad(final INumericValue pkNacionalidad) {
        this.pkNacionalidad.setValue(pkNacionalidad);
    }

    public void setPkSexo(final String pkSexo) {
        this.pkSexo.setValue(pkSexo);
    }

    public void setPkSexo(final IAlphanumericValue pkSexo) {
        this.pkSexo.setValue(pkSexo);
    }

    public void setPkEdoCivil(final String pkEdoCivil) {
        this.pkEdoCivil.setValue(pkEdoCivil);
    }

    public void setPkEdoCivil(final IAlphanumericValue pkEdoCivil) {
        this.pkEdoCivil.setValue(pkEdoCivil);
    }

    public void setPkEscolaridad(final int pkEscolaridad) {
        this.pkEscolaridad.setValue(pkEscolaridad);
    }

    public void setPkEscolaridad(final BigDecimal pkEscolaridad) {
        this.pkEscolaridad.setValue(pkEscolaridad);
    }

    public void setPkEscolaridad(final INumericValue pkEscolaridad) {
        this.pkEscolaridad.setValue(pkEscolaridad);
    }

    public void setPkNomConyuge(final String pkNomConyuge) {
        this.pkNomConyuge.setValue(pkNomConyuge);
    }

    public void setPkNomConyuge(final IAlphanumericValue pkNomConyuge) {
        this.pkNomConyuge.setValue(pkNomConyuge);
    }

    public void setPkFecCasam(final int pkFecCasam) {
        this.pkFecCasam.setValue(pkFecCasam);
    }

    public void setPkFecCasam(final BigDecimal pkFecCasam) {
        this.pkFecCasam.setValue(pkFecCasam);
    }

    public void setPkFecCasam(final INumericValue pkFecCasam) {
        this.pkFecCasam.setValue(pkFecCasam);
    }

    public void setPkStatus(final String pkStatus) {
        this.pkStatus.setValue(pkStatus);
    }

    public void setPkStatus(final IAlphanumericValue pkStatus) {
        this.pkStatus.setValue(pkStatus);
    }

    public void setPkMotStatus(final int pkMotStatus) {
        this.pkMotStatus.setValue(pkMotStatus);
    }

    public void setPkMotStatus(final BigDecimal pkMotStatus) {
        this.pkMotStatus.setValue(pkMotStatus);
    }

    public void setPkMotStatus(final INumericValue pkMotStatus) {
        this.pkMotStatus.setValue(pkMotStatus);
    }

    public void setPkFecStatus(final int pkFecStatus) {
        this.pkFecStatus.setValue(pkFecStatus);
    }

    public void setPkFecStatus(final BigDecimal pkFecStatus) {
        this.pkFecStatus.setValue(pkFecStatus);
    }

    public void setPkFecStatus(final INumericValue pkFecStatus) {
        this.pkFecStatus.setValue(pkFecStatus);
    }

    public void setPkFecAlta(final int pkFecAlta) {
        this.pkFecAlta.setValue(pkFecAlta);
    }

    public void setPkFecAlta(final BigDecimal pkFecAlta) {
        this.pkFecAlta.setValue(pkFecAlta);
    }

    public void setPkFecAlta(final INumericValue pkFecAlta) {
        this.pkFecAlta.setValue(pkFecAlta);
    }

    public void setPkFecTerm(final int pkFecTerm) {
        this.pkFecTerm.setValue(pkFecTerm);
    }

    public void setPkFecTerm(final BigDecimal pkFecTerm) {
        this.pkFecTerm.setValue(pkFecTerm);
    }

    public void setPkFecTerm(final INumericValue pkFecTerm) {
        this.pkFecTerm.setValue(pkFecTerm);
    }

    public void setPkTipCedula(final String pkTipCedula) {
        this.pkTipCedula.setValue(pkTipCedula);
    }

    public void setPkTipCedula(final IAlphanumericValue pkTipCedula) {
        this.pkTipCedula.setValue(pkTipCedula);
    }

    public void setPkNumCedula(final String pkNumCedula) {
        this.pkNumCedula.setValue(pkNumCedula);
    }

    public void setPkNumCedula(final IAlphanumericValue pkNumCedula) {
        this.pkNumCedula.setValue(pkNumCedula);
    }

    public void setPkIniCedula(final int pkIniCedula) {
        this.pkIniCedula.setValue(pkIniCedula);
    }

    public void setPkIniCedula(final BigDecimal pkIniCedula) {
        this.pkIniCedula.setValue(pkIniCedula);
    }

    public void setPkIniCedula(final INumericValue pkIniCedula) {
        this.pkIniCedula.setValue(pkIniCedula);
    }

    public void setPkFinCedula(final int pkFinCedula) {
        this.pkFinCedula.setValue(pkFinCedula);
    }

    public void setPkFinCedula(final BigDecimal pkFinCedula) {
        this.pkFinCedula.setValue(pkFinCedula);
    }

    public void setPkFinCedula(final INumericValue pkFinCedula) {
        this.pkFinCedula.setValue(pkFinCedula);
    }

    public void setPkRamoP_1(final String pkRamoP_1) {
        this.pkRamoP_1.setValue(pkRamoP_1);
    }

    public void setPkRamoP_1(final IAlphanumericValue pkRamoP_1) {
        this.pkRamoP_1.setValue(pkRamoP_1);
    }

    public void setPkRamoP_2(final String pkRamoP_2) {
        this.pkRamoP_2.setValue(pkRamoP_2);
    }

    public void setPkRamoP_2(final IAlphanumericValue pkRamoP_2) {
        this.pkRamoP_2.setValue(pkRamoP_2);
    }

    public void setPkRamoP_3(final String pkRamoP_3) {
        this.pkRamoP_3.setValue(pkRamoP_3);
    }

    public void setPkRamoP_3(final IAlphanumericValue pkRamoP_3) {
        this.pkRamoP_3.setValue(pkRamoP_3);
    }

    public void setPkRamoP_4(final String pkRamoP_4) {
        this.pkRamoP_4.setValue(pkRamoP_4);
    }

    public void setPkRamoP_4(final IAlphanumericValue pkRamoP_4) {
        this.pkRamoP_4.setValue(pkRamoP_4);
    }

    public void setPkRamoP_5(final String pkRamoP_5) {
        this.pkRamoP_5.setValue(pkRamoP_5);
    }

    public void setPkRamoP_5(final IAlphanumericValue pkRamoP_5) {
        this.pkRamoP_5.setValue(pkRamoP_5);
    }

    public void setPkRamoP_6(final String pkRamoP_6) {
        this.pkRamoP_6.setValue(pkRamoP_6);
    }

    public void setPkRamoP_6(final IAlphanumericValue pkRamoP_6) {
        this.pkRamoP_6.setValue(pkRamoP_6);
    }

    public void setPkRamoP_7(final String pkRamoP_7) {
        this.pkRamoP_7.setValue(pkRamoP_7);
    }

    public void setPkRamoP_7(final IAlphanumericValue pkRamoP_7) {
        this.pkRamoP_7.setValue(pkRamoP_7);
    }

    public void setPkRamoP_8(final String pkRamoP_8) {
        this.pkRamoP_8.setValue(pkRamoP_8);
    }

    public void setPkRamoP_8(final IAlphanumericValue pkRamoP_8) {
        this.pkRamoP_8.setValue(pkRamoP_8);
    }

    public void setPkRamoP_9(final String pkRamoP_9) {
        this.pkRamoP_9.setValue(pkRamoP_9);
    }

    public void setPkRamoP_9(final IAlphanumericValue pkRamoP_9) {
        this.pkRamoP_9.setValue(pkRamoP_9);
    }

    public void setPkRamoP_10(final String pkRamoP_10) {
        this.pkRamoP_10.setValue(pkRamoP_10);
    }

    public void setPkRamoP_10(final IAlphanumericValue pkRamoP_10) {
        this.pkRamoP_10.setValue(pkRamoP_10);
    }

    public void setPkObservOficS(final String pkObservOficS) {
        this.pkObservOficS.setValue(pkObservOficS);
    }

    public void setPkObservOficS(final IAlphanumericValue pkObservOficS) {
        this.pkObservOficS.setValue(pkObservOficS);
    }

    public void setPkRegIva(final String pkRegIva) {
        this.pkRegIva.setValue(pkRegIva);
    }

    public void setPkRegIva(final IAlphanumericValue pkRegIva) {
        this.pkRegIva.setValue(pkRegIva);
    }

    public void setPkTipo(final int pkTipo) {
        this.pkTipo.setValue(pkTipo);
    }

    public void setPkTipo(final BigDecimal pkTipo) {
        this.pkTipo.setValue(pkTipo);
    }

    public void setPkTipo(final INumericValue pkTipo) {
        this.pkTipo.setValue(pkTipo);
    }

    public void setPkCodPago(final int pkCodPago) {
        this.pkCodPago.setValue(pkCodPago);
    }

    public void setPkCodPago(final BigDecimal pkCodPago) {
        this.pkCodPago.setValue(pkCodPago);
    }

    public void setPkCodPago(final INumericValue pkCodPago) {
        this.pkCodPago.setValue(pkCodPago);
    }

    public void setPkCvePagoImp(final int pkCvePagoImp) {
        this.pkCvePagoImp.setValue(pkCvePagoImp);
    }

    public void setPkCvePagoImp(final BigDecimal pkCvePagoImp) {
        this.pkCvePagoImp.setValue(pkCvePagoImp);
    }

    public void setPkCvePagoImp(final INumericValue pkCvePagoImp) {
        this.pkCvePagoImp.setValue(pkCvePagoImp);
    }

    public void setPkPorIva(final BigDecimal pkPorIva) {
        this.pkPorIva.setValue(pkPorIva);
    }

    public void setPkPorIva(final int pkPorIva) {
        this.pkPorIva.setValue(pkPorIva);
    }

    public void setPkPorIva(final INumericValue pkPorIva) {
        this.pkPorIva.setValue(pkPorIva);
    }

    public void setPkPorIsr(final BigDecimal pkPorIsr) {
        this.pkPorIsr.setValue(pkPorIsr);
    }

    public void setPkPorIsr(final int pkPorIsr) {
        this.pkPorIsr.setValue(pkPorIsr);
    }

    public void setPkPorIsr(final INumericValue pkPorIsr) {
        this.pkPorIsr.setValue(pkPorIsr);
    }

    public void setPkNumFianza(final String pkNumFianza) {
        this.pkNumFianza.setValue(pkNumFianza);
    }

    public void setPkNumFianza(final IAlphanumericValue pkNumFianza) {
        this.pkNumFianza.setValue(pkNumFianza);
    }

    public void setPkCanFianza(final BigDecimal pkCanFianza) {
        this.pkCanFianza.setValue(pkCanFianza);
    }

    public void setPkCanFianza(final int pkCanFianza) {
        this.pkCanFianza.setValue(pkCanFianza);
    }

    public void setPkCanFianza(final INumericValue pkCanFianza) {
        this.pkCanFianza.setValue(pkCanFianza);
    }

    public void setPkIniFianza(final int pkIniFianza) {
        this.pkIniFianza.setValue(pkIniFianza);
    }

    public void setPkIniFianza(final BigDecimal pkIniFianza) {
        this.pkIniFianza.setValue(pkIniFianza);
    }

    public void setPkIniFianza(final INumericValue pkIniFianza) {
        this.pkIniFianza.setValue(pkIniFianza);
    }

    public void setPkFinFianza(final int pkFinFianza) {
        this.pkFinFianza.setValue(pkFinFianza);
    }

    public void setPkFinFianza(final BigDecimal pkFinFianza) {
        this.pkFinFianza.setValue(pkFinFianza);
    }

    public void setPkFinFianza(final INumericValue pkFinFianza) {
        this.pkFinFianza.setValue(pkFinFianza);
    }

    public void setPkAgenteRec(final int pkAgenteRec) {
        this.pkAgenteRec.setValue(pkAgenteRec);
    }

    public void setPkAgenteRec(final BigDecimal pkAgenteRec) {
        this.pkAgenteRec.setValue(pkAgenteRec);
    }

    public void setPkAgenteRec(final INumericValue pkAgenteRec) {
        this.pkAgenteRec.setValue(pkAgenteRec);
    }

    public void setPkOtrasCias(final String pkOtrasCias) {
        this.pkOtrasCias.setValue(pkOtrasCias);
    }

    public void setPkOtrasCias(final IAlphanumericValue pkOtrasCias) {
        this.pkOtrasCias.setValue(pkOtrasCias);
    }

    public void setPkEsGerente(final String pkEsGerente) {
        this.pkEsGerente.setValue(pkEsGerente);
    }

    public void setPkEsGerente(final IAlphanumericValue pkEsGerente) {
        this.pkEsGerente.setValue(pkEsGerente);
    }

    public void setPkZonaNal(final int pkZonaNal) {
        this.pkZonaNal.setValue(pkZonaNal);
    }

    public void setPkZonaNal(final BigDecimal pkZonaNal) {
        this.pkZonaNal.setValue(pkZonaNal);
    }

    public void setPkZonaNal(final INumericValue pkZonaNal) {
        this.pkZonaNal.setValue(pkZonaNal);
    }

    public void setPkZonaProv(final int pkZonaProv) {
        this.pkZonaProv.setValue(pkZonaProv);
    }

    public void setPkZonaProv(final BigDecimal pkZonaProv) {
        this.pkZonaProv.setValue(pkZonaProv);
    }

    public void setPkZonaProv(final INumericValue pkZonaProv) {
        this.pkZonaProv.setValue(pkZonaProv);
    }

    public void setPkZonaNalGte(final int pkZonaNalGte) {
        this.pkZonaNalGte.setValue(pkZonaNalGte);
    }

    public void setPkZonaNalGte(final BigDecimal pkZonaNalGte) {
        this.pkZonaNalGte.setValue(pkZonaNalGte);
    }

    public void setPkZonaNalGte(final INumericValue pkZonaNalGte) {
        this.pkZonaNalGte.setValue(pkZonaNalGte);
    }

    public void setPkZonaProvGte(final int pkZonaProvGte) {
        this.pkZonaProvGte.setValue(pkZonaProvGte);
    }

    public void setPkZonaProvGte(final BigDecimal pkZonaProvGte) {
        this.pkZonaProvGte.setValue(pkZonaProvGte);
    }

    public void setPkZonaProvGte(final INumericValue pkZonaProvGte) {
        this.pkZonaProvGte.setValue(pkZonaProvGte);
    }

    public void setPkOficinaNal(final int pkOficinaNal) {
        this.pkOficinaNal.setValue(pkOficinaNal);
    }

    public void setPkOficinaNal(final BigDecimal pkOficinaNal) {
        this.pkOficinaNal.setValue(pkOficinaNal);
    }

    public void setPkOficinaNal(final INumericValue pkOficinaNal) {
        this.pkOficinaNal.setValue(pkOficinaNal);
    }

    public void setPkOficinaProv(final int pkOficinaProv) {
        this.pkOficinaProv.setValue(pkOficinaProv);
    }

    public void setPkOficinaProv(final BigDecimal pkOficinaProv) {
        this.pkOficinaProv.setValue(pkOficinaProv);
    }

    public void setPkOficinaProv(final INumericValue pkOficinaProv) {
        this.pkOficinaProv.setValue(pkOficinaProv);
    }

    public void setPkFecZonaNal(final int pkFecZonaNal) {
        this.pkFecZonaNal.setValue(pkFecZonaNal);
    }

    public void setPkFecZonaNal(final BigDecimal pkFecZonaNal) {
        this.pkFecZonaNal.setValue(pkFecZonaNal);
    }

    public void setPkFecZonaNal(final INumericValue pkFecZonaNal) {
        this.pkFecZonaNal.setValue(pkFecZonaNal);
    }

    public void setPkFecZonaProv(final int pkFecZonaProv) {
        this.pkFecZonaProv.setValue(pkFecZonaProv);
    }

    public void setPkFecZonaProv(final BigDecimal pkFecZonaProv) {
        this.pkFecZonaProv.setValue(pkFecZonaProv);
    }

    public void setPkFecZonaProv(final INumericValue pkFecZonaProv) {
        this.pkFecZonaProv.setValue(pkFecZonaProv);
    }

    public void setPkFecZonaNalGte(final int pkFecZonaNalGte) {
        this.pkFecZonaNalGte.setValue(pkFecZonaNalGte);
    }

    public void setPkFecZonaNalGte(final BigDecimal pkFecZonaNalGte) {
        this.pkFecZonaNalGte.setValue(pkFecZonaNalGte);
    }

    public void setPkFecZonaNalGte(final INumericValue pkFecZonaNalGte) {
        this.pkFecZonaNalGte.setValue(pkFecZonaNalGte);
    }

    public void setPkFecZonaProvGte(final int pkFecZonaProvGte) {
        this.pkFecZonaProvGte.setValue(pkFecZonaProvGte);
    }

    public void setPkFecZonaProvGte(final BigDecimal pkFecZonaProvGte) {
        this.pkFecZonaProvGte.setValue(pkFecZonaProvGte);
    }

    public void setPkFecZonaProvGte(final INumericValue pkFecZonaProvGte) {
        this.pkFecZonaProvGte.setValue(pkFecZonaProvGte);
    }

    public void setPkFecOficinaNal(final int pkFecOficinaNal) {
        this.pkFecOficinaNal.setValue(pkFecOficinaNal);
    }

    public void setPkFecOficinaNal(final BigDecimal pkFecOficinaNal) {
        this.pkFecOficinaNal.setValue(pkFecOficinaNal);
    }

    public void setPkFecOficinaNal(final INumericValue pkFecOficinaNal) {
        this.pkFecOficinaNal.setValue(pkFecOficinaNal);
    }

    public void setPkFecOficinaProv(final int pkFecOficinaProv) {
        this.pkFecOficinaProv.setValue(pkFecOficinaProv);
    }

    public void setPkFecOficinaProv(final BigDecimal pkFecOficinaProv) {
        this.pkFecOficinaProv.setValue(pkFecOficinaProv);
    }

    public void setPkFecOficinaProv(final INumericValue pkFecOficinaProv) {
        this.pkFecOficinaProv.setValue(pkFecOficinaProv);
    }

    public void setPkCveAgrup(final int pkCveAgrup) {
        this.pkCveAgrup.setValue(pkCveAgrup);
    }

    public void setPkCveAgrup(final BigDecimal pkCveAgrup) {
        this.pkCveAgrup.setValue(pkCveAgrup);
    }

    public void setPkCveAgrup(final INumericValue pkCveAgrup) {
        this.pkCveAgrup.setValue(pkCveAgrup);
    }

    public void setPkCveEnvio(final int pkCveEnvio) {
        this.pkCveEnvio.setValue(pkCveEnvio);
    }

    public void setPkCveEnvio(final BigDecimal pkCveEnvio) {
        this.pkCveEnvio.setValue(pkCveEnvio);
    }

    public void setPkCveEnvio(final INumericValue pkCveEnvio) {
        this.pkCveEnvio.setValue(pkCveEnvio);
    }

    public void setPkCalidad(final int pkCalidad) {
        this.pkCalidad.setValue(pkCalidad);
    }

    public void setPkCalidad(final BigDecimal pkCalidad) {
        this.pkCalidad.setValue(pkCalidad);
    }

    public void setPkCalidad(final INumericValue pkCalidad) {
        this.pkCalidad.setValue(pkCalidad);
    }

    public void setPkJefeGpoNal(final int pkJefeGpoNal) {
        this.pkJefeGpoNal.setValue(pkJefeGpoNal);
    }

    public void setPkJefeGpoNal(final BigDecimal pkJefeGpoNal) {
        this.pkJefeGpoNal.setValue(pkJefeGpoNal);
    }

    public void setPkJefeGpoNal(final INumericValue pkJefeGpoNal) {
        this.pkJefeGpoNal.setValue(pkJefeGpoNal);
    }

    public void setPkJefeGpoProv(final int pkJefeGpoProv) {
        this.pkJefeGpoProv.setValue(pkJefeGpoProv);
    }

    public void setPkJefeGpoProv(final BigDecimal pkJefeGpoProv) {
        this.pkJefeGpoProv.setValue(pkJefeGpoProv);
    }

    public void setPkJefeGpoProv(final INumericValue pkJefeGpoProv) {
        this.pkJefeGpoProv.setValue(pkJefeGpoProv);
    }

    public void setPkFecConexionP(final int pkFecConexionP) {
        this.pkFecConexionP.setValue(pkFecConexionP);
    }

    public void setPkFecConexionP(final BigDecimal pkFecConexionP) {
        this.pkFecConexionP.setValue(pkFecConexionP);
    }

    public void setPkFecConexionP(final INumericValue pkFecConexionP) {
        this.pkFecConexionP.setValue(pkFecConexionP);
    }

    public void setPkFecConexionPr(final String pkFecConexionPr) {
        this.pkFecConexionPr.setValue(pkFecConexionPr);
    }

    public void setPkFecConexionPr(final IAlphanumericValue pkFecConexionPr) {
        this.pkFecConexionPr.setValue(pkFecConexionPr);
    }

    public void setPkNumCedulaP(final String pkNumCedulaP) {
        this.pkNumCedulaP.setValue(pkNumCedulaP);
    }

    public void setPkNumCedulaP(final IAlphanumericValue pkNumCedulaP) {
        this.pkNumCedulaP.setValue(pkNumCedulaP);
    }

    public void setPkFinCedulaP(final int pkFinCedulaP) {
        this.pkFinCedulaP.setValue(pkFinCedulaP);
    }

    public void setPkFinCedulaP(final BigDecimal pkFinCedulaP) {
        this.pkFinCedulaP.setValue(pkFinCedulaP);
    }

    public void setPkFinCedulaP(final INumericValue pkFinCedulaP) {
        this.pkFinCedulaP.setValue(pkFinCedulaP);
    }

    public void setPkStatusProv(final String pkStatusProv) {
        this.pkStatusProv.setValue(pkStatusProv);
    }

    public void setPkStatusProv(final IAlphanumericValue pkStatusProv) {
        this.pkStatusProv.setValue(pkStatusProv);
    }

    public void setPkMotStatusProv(final int pkMotStatusProv) {
        this.pkMotStatusProv.setValue(pkMotStatusProv);
    }

    public void setPkMotStatusProv(final BigDecimal pkMotStatusProv) {
        this.pkMotStatusProv.setValue(pkMotStatusProv);
    }

    public void setPkMotStatusProv(final INumericValue pkMotStatusProv) {
        this.pkMotStatusProv.setValue(pkMotStatusProv);
    }

    public void setPkGteZonaNal(final int pkGteZonaNal) {
        this.pkGteZonaNal.setValue(pkGteZonaNal);
    }

    public void setPkGteZonaNal(final BigDecimal pkGteZonaNal) {
        this.pkGteZonaNal.setValue(pkGteZonaNal);
    }

    public void setPkGteZonaNal(final INumericValue pkGteZonaNal) {
        this.pkGteZonaNal.setValue(pkGteZonaNal);
    }

    public void setPkGteZonaProv(final int pkGteZonaProv) {
        this.pkGteZonaProv.setValue(pkGteZonaProv);
    }

    public void setPkGteZonaProv(final BigDecimal pkGteZonaProv) {
        this.pkGteZonaProv.setValue(pkGteZonaProv);
    }

    public void setPkGteZonaProv(final INumericValue pkGteZonaProv) {
        this.pkGteZonaProv.setValue(pkGteZonaProv);
    }

    public void setPkFuente(final int pkFuente) {
        this.pkFuente.setValue(pkFuente);
    }

    public void setPkFuente(final BigDecimal pkFuente) {
        this.pkFuente.setValue(pkFuente);
    }

    public void setPkFuente(final INumericValue pkFuente) {
        this.pkFuente.setValue(pkFuente);
    }

    public void setPkGrupo(final int pkGrupo) {
        this.pkGrupo.setValue(pkGrupo);
    }

    public void setPkGrupo(final BigDecimal pkGrupo) {
        this.pkGrupo.setValue(pkGrupo);
    }

    public void setPkGrupo(final INumericValue pkGrupo) {
        this.pkGrupo.setValue(pkGrupo);
    }

    public void setPkSubgrupo(final int pkSubgrupo) {
        this.pkSubgrupo.setValue(pkSubgrupo);
    }

    public void setPkSubgrupo(final BigDecimal pkSubgrupo) {
        this.pkSubgrupo.setValue(pkSubgrupo);
    }

    public void setPkSubgrupo(final INumericValue pkSubgrupo) {
        this.pkSubgrupo.setValue(pkSubgrupo);
    }


    // Deeper properties

    // Property: pkRfc.pkRfc1 -> 07 PK-RFC1 X(13)
    public AlphanumericVar getPkRfc1() {
        return this.pkRfc.getPkRfc1();
    }

    public void setPkRfc1(final String pkRfc1) {
        this.pkRfc.setPkRfc1(pkRfc1);
    }

    public void setPkRfc1(final IAlphanumericValue pkRfc1) {
        this.pkRfc.setPkRfc1(pkRfc1);
    }

    // Property: pkRfc.pkRfc2 -> 07 PK-RFC2 XX
    public AlphanumericVar getPkRfc2() {
        return this.pkRfc.getPkRfc2();
    }

    public void setPkRfc2(final String pkRfc2) {
        this.pkRfc.setPkRfc2(pkRfc2);
    }

    public void setPkRfc2(final IAlphanumericValue pkRfc2) {
        this.pkRfc.setPkRfc2(pkRfc2);
    }


    // Deeper properties

    // Property: pkNumCedular.pkNumCedulaDos -> 07 PK-NUM-CEDULA-DOS XX
    public AlphanumericVar getPkNumCedulaDos() {
        return this.pkNumCedular.getPkNumCedulaDos();
    }

    public void setPkNumCedulaDos(final String pkNumCedulaDos) {
        this.pkNumCedular.setPkNumCedulaDos(pkNumCedulaDos);
    }

    public void setPkNumCedulaDos(final IAlphanumericValue pkNumCedulaDos) {
        this.pkNumCedular.setPkNumCedulaDos(pkNumCedulaDos);
    }

    // Property: pkNumCedular.pkNumCedulaR18 -> 07 PK-NUM-CEDULA-R18 X(18)
    public AlphanumericVar getPkNumCedulaR18() {
        return this.pkNumCedular.getPkNumCedulaR18();
    }

    public void setPkNumCedulaR18(final String pkNumCedulaR18) {
        this.pkNumCedular.setPkNumCedulaR18(pkNumCedulaR18);
    }

    public void setPkNumCedulaR18(final IAlphanumericValue pkNumCedulaR18) {
        this.pkNumCedular.setPkNumCedulaR18(pkNumCedulaR18);
    }


    // Deeper properties

    // Property: pkNumCedularr.pkNumCedulaTres -> 07 PK-NUM-CEDULA-TRES XXX
    public AlphanumericVar getPkNumCedulaTres() {
        return this.pkNumCedularr.getPkNumCedulaTres();
    }

    public void setPkNumCedulaTres(final String pkNumCedulaTres) {
        this.pkNumCedularr.setPkNumCedulaTres(pkNumCedulaTres);
    }

    public void setPkNumCedulaTres(final IAlphanumericValue pkNumCedulaTres) {
        this.pkNumCedularr.setPkNumCedulaTres(pkNumCedulaTres);
    }

    // Ambiguous Property: pkNumCedularr.pkNumCedulaR17 -> 07 PK-NUM-CEDULA-R17 X(17)

    // Deeper properties

    // Property: pkNumCedularrr.pkNumCedulaXx -> 07 PK-NUM-CEDULA-XX XX
    public AlphanumericVar getPkNumCedulaXx() {
        return this.pkNumCedularrr.getPkNumCedulaXx();
    }

    public void setPkNumCedulaXx(final String pkNumCedulaXx) {
        this.pkNumCedularrr.setPkNumCedulaXx(pkNumCedulaXx);
    }

    public void setPkNumCedulaXx(final IAlphanumericValue pkNumCedulaXx) {
        this.pkNumCedularrr.setPkNumCedulaXx(pkNumCedulaXx);
    }

    // Property: pkNumCedularrr.pkNumCedulaX -> 07 PK-NUM-CEDULA-X X
    public AlphanumericVar getPkNumCedulaX() {
        return this.pkNumCedularrr.getPkNumCedulaX();
    }

    public void setPkNumCedulaX(final String pkNumCedulaX) {
        this.pkNumCedularrr.setPkNumCedulaX(pkNumCedulaX);
    }

    public void setPkNumCedulaX(final IAlphanumericValue pkNumCedulaX) {
        this.pkNumCedularrr.setPkNumCedulaX(pkNumCedulaX);
    }

    // Ambiguous Property: pkNumCedularrr.pkNumCedulaR17 -> 07 PK-NUM-CEDULA-R17 X(17)

    // Deeper properties

    // Property: pkNumCedulaPr.pkNumCedulaPDos -> 07 PK-NUM-CEDULA-P-DOS XX
    public AlphanumericVar getPkNumCedulaPDos() {
        return this.pkNumCedulaPr.getPkNumCedulaPDos();
    }

    public void setPkNumCedulaPDos(final String pkNumCedulaPDos) {
        this.pkNumCedulaPr.setPkNumCedulaPDos(pkNumCedulaPDos);
    }

    public void setPkNumCedulaPDos(final IAlphanumericValue pkNumCedulaPDos) {
        this.pkNumCedulaPr.setPkNumCedulaPDos(pkNumCedulaPDos);
    }

    // Property: pkNumCedulaPr.pkNumCedulaPR18 -> 07 PK-NUM-CEDULA-P-R18 X(18)
    public AlphanumericVar getPkNumCedulaPR18() {
        return this.pkNumCedulaPr.getPkNumCedulaPR18();
    }

    public void setPkNumCedulaPR18(final String pkNumCedulaPR18) {
        this.pkNumCedulaPr.setPkNumCedulaPR18(pkNumCedulaPR18);
    }

    public void setPkNumCedulaPR18(final IAlphanumericValue pkNumCedulaPR18) {
        this.pkNumCedulaPr.setPkNumCedulaPR18(pkNumCedulaPR18);
    }


    // Deeper properties

    // Property: pkNumCedulapRr.pkNumCedulaPTres -> 07 PK-NUM-CEDULA-P-TRES XXX
    public AlphanumericVar getPkNumCedulaPTres() {
        return this.pkNumCedulapRr.getPkNumCedulaPTres();
    }

    public void setPkNumCedulaPTres(final String pkNumCedulaPTres) {
        this.pkNumCedulapRr.setPkNumCedulaPTres(pkNumCedulaPTres);
    }

    public void setPkNumCedulaPTres(final IAlphanumericValue pkNumCedulaPTres) {
        this.pkNumCedulapRr.setPkNumCedulaPTres(pkNumCedulaPTres);
    }

    // Property: pkNumCedulapRr.pkNumCedulaPR17 -> 07 PK-NUM-CEDULA-P-R17 X(17)
    public AlphanumericVar getPkNumCedulaPR17() {
        return this.pkNumCedulapRr.getPkNumCedulaPR17();
    }

    public void setPkNumCedulaPR17(final String pkNumCedulaPR17) {
        this.pkNumCedulapRr.setPkNumCedulaPR17(pkNumCedulaPR17);
    }

    public void setPkNumCedulaPR17(final IAlphanumericValue pkNumCedulaPR17) {
        this.pkNumCedulapRr.setPkNumCedulaPR17(pkNumCedulaPR17);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.datosCons.clear();
        this.pkRfc.initialize();
        this.pkClaveNal.clear();
        this.pkClaveProv.clear();
        this.pkNombre.clear();
        this.pkCompania.clear();
        this.pkFecConexion.clear();
        this.pkCalleP.clear();
        this.pkColoniaP.clear();
        this.pkCodPostP.clear();
        this.pkPoblP.clear();
        this.pkEstadoP.clear();
        this.pkPaisP.clear();
        this.pkTelefP.clear();
        this.pkCalleC.clear();
        this.pkColoniaC.clear();
        this.pkCodPostC.clear();
        this.pkPoblC.clear();
        this.pkEstadoC.clear();
        this.pkPaisC.clear();
        this.pkTelefC.clear();
        this.pkFecNacim.clear();
        this.pkNacionalidad.clear();
        this.pkSexo.clear();
        this.pkEdoCivil.clear();
        this.pkEscolaridad.clear();
        this.pkNomConyuge.clear();
        this.pkFecCasam.clear();
        this.pkStatus.clear();
        this.pkMotStatus.clear();
        this.pkFecStatus.clear();
        this.pkFecAlta.clear();
        this.pkFecTerm.clear();
        this.pkTipCedula.clear();
        this.pkNumCedula.clear();
        this.pkIniCedula.clear();
        this.pkFinCedula.clear();
        this.pkRamoP_1.clear();
        this.pkRamoP_2.clear();
        this.pkRamoP_3.clear();
        this.pkRamoP_4.clear();
        this.pkRamoP_5.clear();
        this.pkRamoP_6.clear();
        this.pkRamoP_7.clear();
        this.pkRamoP_8.clear();
        this.pkRamoP_9.clear();
        this.pkRamoP_10.clear();
        this.pkObservOficS.clear();
        this.pkRegIva.clear();
        this.pkTipo.clear();
        this.pkCodPago.clear();
        this.pkCvePagoImp.clear();
        this.pkPorIva.clear();
        this.pkPorIsr.clear();
        this.pkNumFianza.clear();
        this.pkCanFianza.clear();
        this.pkIniFianza.clear();
        this.pkFinFianza.clear();
        this.pkAgenteRec.clear();
        this.pkOtrasCias.clear();
        this.pkEsGerente.clear();
        this.pkZonaNal.clear();
        this.pkZonaProv.clear();
        this.pkZonaNalGte.clear();
        this.pkZonaProvGte.clear();
        this.pkOficinaNal.clear();
        this.pkOficinaProv.clear();
        this.pkFecZonaNal.clear();
        this.pkFecZonaProv.clear();
        this.pkFecZonaNalGte.clear();
        this.pkFecZonaProvGte.clear();
        this.pkFecOficinaNal.clear();
        this.pkFecOficinaProv.clear();
        this.pkCveAgrup.clear();
        this.pkCveEnvio.clear();
        this.pkCalidad.clear();
        this.pkJefeGpoNal.clear();
        this.pkJefeGpoProv.clear();
        this.pkFecConexionP.clear();
        this.pkNumCedulaP.clear();
        this.pkFinCedulaP.clear();
        this.pkStatusProv.clear();
        this.pkMotStatusProv.clear();
        this.pkGteZonaNal.clear();
        this.pkGteZonaProv.clear();
        this.pkFuente.clear();
        this.pkGrupo.clear();
        this.pkSubgrupo.clear();
    }

}

