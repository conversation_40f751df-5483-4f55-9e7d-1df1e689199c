package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DET3.
 *
 */
// 131013100*                                                                 01310002
// 132013200*                                                                 01320002
@Setter
@Getter
public class Det3 extends DataStruct {
    // Properties
    private AlphanumericVar filler29 = new AlphanumericVar(this, 10);        // 10 FILLER X(10)
    private AlphanumericVar det12Letrero = new AlphanumericVar(this, 25);    // 10 DET12-LETRERO X(25)
    private NumericEditedVar det12Total = new NumericEditedVar(this, "ZZZZZZ9");             // 10 DET12-TOTAL ZZZZZZ9
    private AlphanumericVar filler30 = new AlphanumericVar(this, 90);        // 10 FILLER X(90)

    public Det3() {
        super();
        initialize();
    }

    public Det3(DataContainer parent) {
        super(parent);
    }

    public Det3(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFiller29(final String filler29) {
        this.filler29.setValue(filler29);
    }

    public void setFiller29(final IAlphanumericValue filler29) {
        this.filler29.setValue(filler29);
    }

    public void setDet12Letrero(final String det12Letrero) {
        this.det12Letrero.setValue(det12Letrero);
    }

    public void setDet12Letrero(final IAlphanumericValue det12Letrero) {
        this.det12Letrero.setValue(det12Letrero);
    }

    public void setDet12Total(final String det12Total) {
        this.det12Total.setValue(det12Total);
    }

    public void setDet12Total(final IValue det12Total) {
        this.det12Total.setValue(det12Total);
    }

    public void setFiller30(final String filler30) {
        this.filler30.setValue(filler30);
    }

    public void setFiller30(final IAlphanumericValue filler30) {
        this.filler30.setValue(filler30);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.filler29.clear();
        this.det12Letrero.clear();
        this.det12Total.clear();
        this.filler30.clear();
    }

}

