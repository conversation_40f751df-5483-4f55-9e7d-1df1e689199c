package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datoscocoa;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PK-FOLIO-INVESR.
 *
 */
@Setter
@Getter
public class PkFolioInvesr extends DataStruct {
    // Properties
    private UnsignedNumericVar pkFolio1 = new UnsignedNumericVar(this, 3, 0);                // 07 PK-FOLIO1 999
    private UnsignedNumericVar pkFolio2 = new UnsignedNumericVar(this, 4, 0);                // 07 PK-FOLIO2 9999

    public PkFolioInvesr() {
        super();
        initialize();
    }

    public PkFolioInvesr(DataContainer parent) {
        super(parent);
    }

    public PkFolioInvesr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkFolio1(final int pkFolio1) {
        this.pkFolio1.setValue(pkFolio1);
    }

    public void setPkFolio1(final BigDecimal pkFolio1) {
        this.pkFolio1.setValue(pkFolio1);
    }

    public void setPkFolio1(final INumericValue pkFolio1) {
        this.pkFolio1.setValue(pkFolio1);
    }

    public void setPkFolio2(final int pkFolio2) {
        this.pkFolio2.setValue(pkFolio2);
    }

    public void setPkFolio2(final BigDecimal pkFolio2) {
        this.pkFolio2.setValue(pkFolio2);
    }

    public void setPkFolio2(final INumericValue pkFolio2) {
        this.pkFolio2.setValue(pkFolio2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkFolio1.clear();
        this.pkFolio2.clear();
    }

}

