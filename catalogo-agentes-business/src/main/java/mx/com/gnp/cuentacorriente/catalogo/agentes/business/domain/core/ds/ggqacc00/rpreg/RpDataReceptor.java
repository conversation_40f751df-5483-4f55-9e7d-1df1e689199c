package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-DATA-RECEPTOR.
 *
 */
@Setter
@Getter
public class RpDataReceptor { // VStruct
    // Properties
    private String rpRecCae = "";                                           // 10 RP-REC-CAE X(40)
    private String rpRecNum = "";                                           // 10 RP-REC-NUM X(05)
    private String rpRecInt = "";                                           // 10 RP-REC-INT X(05)
    private String rpRecCol = "";                                           // 10 RP-REC-COL X(40)
    private String rpRecMndNom = "";                                        // 10 RP-REC-MND-NOM X(50)
    private String rpRecPobNom = "";                                        // 10 RP-REC-POB-NOM X(40)
    private String rpRecMndEdoCve = "";                                     // 10 RP-REC-MND-EDO-CVE X(04)
    private String rpRecMndEdoNom = "";                                     // 10 RP-REC-MND-EDO-NOM X(30)
    private String rpRecMndPaiCve = "";                                     // 10 RP-REC-MND-PAI-CVE X(03)
    private String rpRecMndPaiNom = "";                                     // 10 RP-REC-MND-PAI-NOM X(30)
    private int rpRecCpoCve;                                                // 10 RP-REC-CPO-CVE 9(05)
    private int rpRecGerZonEnv;                                             // 10 RP-REC-GER-ZON-ENV 9(04)

    public RpDataReceptor() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpRecCae = "";
        this.rpRecNum = "";
        this.rpRecInt = "";
        this.rpRecCol = "";
        this.rpRecMndNom = "";
        this.rpRecPobNom = "";
        this.rpRecMndEdoCve = "";
        this.rpRecMndEdoNom = "";
        this.rpRecMndPaiCve = "";
        this.rpRecMndPaiNom = "";
        this.rpRecCpoCve = 0;
        this.rpRecGerZonEnv = 0;
    }

}

