package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: W530-RUTINA.
 *
 */
@Setter
@Getter
public class W530Rutina { // VStruct
    // Properties
    private String filler1 = "GGQAF";                                       // 05 FILLER X(05)
    private String w530RutinaTabla = "";                                    // 05 W530-RUTINA-TABLA X(03)

    public W530Rutina() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.filler1 = "GGQAF";
        this.w530RutinaTabla = "";
    }

}

