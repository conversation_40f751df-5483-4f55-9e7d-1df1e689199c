package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tger0_1;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-GER0-ELEM-1.
 *
 */
@Setter
@Getter
public class TGer0Elem_1 extends DataStruct {
    // Properties
    private AlphanumericVar sCnaGerZonElem = new AlphanumericVar(this, 1);   // 20 S-CNA-GER-ZON-ELEM X(01)

    public TGer0Elem_1() {
        super();
        initialize();
    }

    public TGer0Elem_1(DataContainer parent) {
        super(parent);
    }

    public TGer0Elem_1(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSCnaGerZonElem(final String sCnaGerZonElem) {
        this.sCnaGerZonElem.setValue(sCnaGerZonElem);
    }

    public void setSCnaGerZonElem(final IAlphanumericValue sCnaGerZonElem) {
        this.sCnaGerZonElem.setValue(sCnaGerZonElem);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sCnaGerZonElem.clear();
    }

}

