package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.wscampostrabajo;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-HFO-REG.
 *
 */
@Setter
@Getter
public class WsHfoReg extends DataStruct {
    // Properties
    private BinaryVar wsHfoOfnCve = new BinaryVar(this, 4, 0);               // 10 WS-HFO-OFN-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar wsHfoFecIniPdo = new AlphanumericVar(this, 10);  // 10 WS-HFO-FEC-INI-PDO X(10)
    private AlphanumericVar wsHfoFecFinPdo = new AlphanumericVar(this, 10);  // 10 WS-HFO-FEC-FIN-PDO X(10)
    private AlphanumericVar wsHfoRfcRcp = new AlphanumericVar(this, 13);     // 10 WS-HFO-RFC-RCP X(13)
    private AlphanumericVar wsHfoNomRcp = new AlphanumericVar(this, 100);    // 10 WS-HFO-NOM-RCP X(100)
    private AlphanumericVar wsHfoOfnCae = new AlphanumericVar(this, 40);     // 10 WS-HFO-OFN-CAE X(40)
    private AlphanumericVar wsHfoOfnNum = new AlphanumericVar(this, 5);      // 10 WS-HFO-OFN-NUM X(5)
    private AlphanumericVar wsHfoOfnInt = new AlphanumericVar(this, 5);      // 10 WS-HFO-OFN-INT X(5)
    private AlphanumericVar wsHfoOfnCol = new AlphanumericVar(this, 30);     // 10 WS-HFO-OFN-COL X(30)
    private AlphanumericVar wsHfoMndNom = new AlphanumericVar(this, 50);     // 10 WS-HFO-MND-NOM X(50)
    private AlphanumericVar wsHfoEdoNom = new AlphanumericVar(this, 30);     // 10 WS-HFO-EDO-NOM X(30)
    private AlphanumericVar wsHfoPaiNom = new AlphanumericVar(this, 30);     // 10 WS-HFO-PAI-NOM X(30)
    private BinaryVar wsHfoCpoCve = new BinaryVar(this, 9, 0);               // 10 WS-HFO-CPO-CVE S9(9) COMPUTATIONAL
    private AlphanumericVar wsHfoUsuCve = new AlphanumericVar(this, 8);      // 10 WS-HFO-USU-CVE X(8)
    private AlphanumericVar wsHfoNomPrg = new AlphanumericVar(this, 8);      // 10 WS-HFO-NOM-PRG X(8)
    private AlphanumericVar wsHfoFecUltAct = new AlphanumericVar(this, 26);  // 10 WS-HFO-FEC-ULT-ACT X(26)

    public WsHfoReg() {
        super();
        initialize();
    }

    public WsHfoReg(DataContainer parent) {
        super(parent);
    }

    public WsHfoReg(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsHfoOfnCve(final int wsHfoOfnCve) {
        this.wsHfoOfnCve.setValue(wsHfoOfnCve);
    }

    public void setWsHfoOfnCve(final BigDecimal wsHfoOfnCve) {
        this.wsHfoOfnCve.setValue(wsHfoOfnCve);
    }

    public void setWsHfoOfnCve(final INumericValue wsHfoOfnCve) {
        this.wsHfoOfnCve.setValue(wsHfoOfnCve);
    }

    public void setWsHfoFecIniPdo(final String wsHfoFecIniPdo) {
        this.wsHfoFecIniPdo.setValue(wsHfoFecIniPdo);
    }

    public void setWsHfoFecIniPdo(final IAlphanumericValue wsHfoFecIniPdo) {
        this.wsHfoFecIniPdo.setValue(wsHfoFecIniPdo);
    }

    public void setWsHfoFecFinPdo(final String wsHfoFecFinPdo) {
        this.wsHfoFecFinPdo.setValue(wsHfoFecFinPdo);
    }

    public void setWsHfoFecFinPdo(final IAlphanumericValue wsHfoFecFinPdo) {
        this.wsHfoFecFinPdo.setValue(wsHfoFecFinPdo);
    }

    public void setWsHfoRfcRcp(final String wsHfoRfcRcp) {
        this.wsHfoRfcRcp.setValue(wsHfoRfcRcp);
    }

    public void setWsHfoRfcRcp(final IAlphanumericValue wsHfoRfcRcp) {
        this.wsHfoRfcRcp.setValue(wsHfoRfcRcp);
    }

    public void setWsHfoNomRcp(final String wsHfoNomRcp) {
        this.wsHfoNomRcp.setValue(wsHfoNomRcp);
    }

    public void setWsHfoNomRcp(final IAlphanumericValue wsHfoNomRcp) {
        this.wsHfoNomRcp.setValue(wsHfoNomRcp);
    }

    public void setWsHfoOfnCae(final String wsHfoOfnCae) {
        this.wsHfoOfnCae.setValue(wsHfoOfnCae);
    }

    public void setWsHfoOfnCae(final IAlphanumericValue wsHfoOfnCae) {
        this.wsHfoOfnCae.setValue(wsHfoOfnCae);
    }

    public void setWsHfoOfnNum(final String wsHfoOfnNum) {
        this.wsHfoOfnNum.setValue(wsHfoOfnNum);
    }

    public void setWsHfoOfnNum(final IAlphanumericValue wsHfoOfnNum) {
        this.wsHfoOfnNum.setValue(wsHfoOfnNum);
    }

    public void setWsHfoOfnInt(final String wsHfoOfnInt) {
        this.wsHfoOfnInt.setValue(wsHfoOfnInt);
    }

    public void setWsHfoOfnInt(final IAlphanumericValue wsHfoOfnInt) {
        this.wsHfoOfnInt.setValue(wsHfoOfnInt);
    }

    public void setWsHfoOfnCol(final String wsHfoOfnCol) {
        this.wsHfoOfnCol.setValue(wsHfoOfnCol);
    }

    public void setWsHfoOfnCol(final IAlphanumericValue wsHfoOfnCol) {
        this.wsHfoOfnCol.setValue(wsHfoOfnCol);
    }

    public void setWsHfoMndNom(final String wsHfoMndNom) {
        this.wsHfoMndNom.setValue(wsHfoMndNom);
    }

    public void setWsHfoMndNom(final IAlphanumericValue wsHfoMndNom) {
        this.wsHfoMndNom.setValue(wsHfoMndNom);
    }

    public void setWsHfoEdoNom(final String wsHfoEdoNom) {
        this.wsHfoEdoNom.setValue(wsHfoEdoNom);
    }

    public void setWsHfoEdoNom(final IAlphanumericValue wsHfoEdoNom) {
        this.wsHfoEdoNom.setValue(wsHfoEdoNom);
    }

    public void setWsHfoPaiNom(final String wsHfoPaiNom) {
        this.wsHfoPaiNom.setValue(wsHfoPaiNom);
    }

    public void setWsHfoPaiNom(final IAlphanumericValue wsHfoPaiNom) {
        this.wsHfoPaiNom.setValue(wsHfoPaiNom);
    }

    public void setWsHfoCpoCve(final int wsHfoCpoCve) {
        this.wsHfoCpoCve.setValue(wsHfoCpoCve);
    }

    public void setWsHfoCpoCve(final BigDecimal wsHfoCpoCve) {
        this.wsHfoCpoCve.setValue(wsHfoCpoCve);
    }

    public void setWsHfoCpoCve(final INumericValue wsHfoCpoCve) {
        this.wsHfoCpoCve.setValue(wsHfoCpoCve);
    }

    public void setWsHfoUsuCve(final String wsHfoUsuCve) {
        this.wsHfoUsuCve.setValue(wsHfoUsuCve);
    }

    public void setWsHfoUsuCve(final IAlphanumericValue wsHfoUsuCve) {
        this.wsHfoUsuCve.setValue(wsHfoUsuCve);
    }

    public void setWsHfoNomPrg(final String wsHfoNomPrg) {
        this.wsHfoNomPrg.setValue(wsHfoNomPrg);
    }

    public void setWsHfoNomPrg(final IAlphanumericValue wsHfoNomPrg) {
        this.wsHfoNomPrg.setValue(wsHfoNomPrg);
    }

    public void setWsHfoFecUltAct(final String wsHfoFecUltAct) {
        this.wsHfoFecUltAct.setValue(wsHfoFecUltAct);
    }

    public void setWsHfoFecUltAct(final IAlphanumericValue wsHfoFecUltAct) {
        this.wsHfoFecUltAct.setValue(wsHfoFecUltAct);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsHfoOfnCve.clear();
        this.wsHfoFecIniPdo.clear();
        this.wsHfoFecFinPdo.clear();
        this.wsHfoRfcRcp.clear();
        this.wsHfoNomRcp.clear();
        this.wsHfoOfnCae.clear();
        this.wsHfoOfnNum.clear();
        this.wsHfoOfnInt.clear();
        this.wsHfoOfnCol.clear();
        this.wsHfoMndNom.clear();
        this.wsHfoEdoNom.clear();
        this.wsHfoPaiNom.clear();
        this.wsHfoCpoCve.clear();
        this.wsHfoUsuCve.clear();
        this.wsHfoNomPrg.clear();
        this.wsHfoFecUltAct.clear();
    }

}

