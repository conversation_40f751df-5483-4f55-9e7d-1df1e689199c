package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TIT11.
 *
 */
// 665039700*                                                                 03970002
// 666039800*                                                                 03980002
// 667039900*                                                                 03990002
@Setter
@Getter
public class Tit11 extends DataStruct {
    // Properties
    private AlphanumericVar tit11Pgm = new AlphanumericVar(this, 8);         // 03 TIT11-PGM X(8)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 36);         // 03 FILLER X(36)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 44);         // 03 FILLER X(44)
    private AlphanumericVar filler3 = new AlphanumericVar(this, 25);         // 03 FILLER X(25)
    private AlphanumericVar filler4 = new AlphanumericVar(this, 8);          // 03 FILLER X(08)
    private NumericEditedVar tit11Hoja = new NumericEditedVar(this, "ZZZ9");                 // 03 TIT11-HOJA ZZZ9

    public Tit11() {
        super();
        initialize();
    }

    public Tit11(DataContainer parent) {
        super(parent);
    }

    public Tit11(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setTit11Pgm(final String tit11Pgm) {
        this.tit11Pgm.setValue(tit11Pgm);
    }

    public void setTit11Pgm(final IAlphanumericValue tit11Pgm) {
        this.tit11Pgm.setValue(tit11Pgm);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller4(final String filler4) {
        this.filler4.setValue(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.filler4.setValue(filler4);
    }

    public void setTit11Hoja(final String tit11Hoja) {
        this.tit11Hoja.setValue(tit11Hoja);
    }

    public void setTit11Hoja(final IValue tit11Hoja) {
        this.tit11Hoja.setValue(tit11Hoja);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tit11Pgm.setValue("GGNF0700");
        this.filler1.setValue("                     REPORTE DE LA T");
        this.filler2.setValue("ABLA DE CARTERA DE AGENTES(GFVTAGC0)  DB2   ");
        this.filler3.setValue("                         ");
        this.filler4.setValue("  HOJA: ");
        this.tit11Hoja.clear();
    }

}

