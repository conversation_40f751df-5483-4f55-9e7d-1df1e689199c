package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-INDICADORES.
 *
 */
// 1145068000*                                                                 06800000
// 1146068100*                                                                 06810000
// 1147068200*                                                                 06820000
@Setter
@Getter
public class WsIndicadores { // VStruct
    // Properties
    private int wsN = 0;                                                    // 05 WS-N 9
    private int wsP = 0;                                                    // 05 WS-P 9
    private int wsFin = 0;                                                  // 05 WS-FIN 9
    private int wsFinAgt = 0;                                               // 05 WS-FIN-AGT 9
    private int wsFinPra = 0;                                               // 05 WS-FIN-PRA 9
    private int wsFinDma = 0;                                               // 05 WS-FIN-DMA 9
    private int wsFinDmz = 0;                                               // 05 WS-FIN-DMZ 9
    private int wsFinPob = 0;                                               // 05 WS-FIN-POB 9
    private int wsFinMnd = 0;                                               // 05 WS-FIN-MND 9
    private int wsFinEdo = 0;                                               // 05 WS-FIN-EDO 9
    private int wsFinCma = 0;                                               // 05 WS-FIN-CMA 9
    private int wsFinMte = 0;                                               // 05 WS-FIN-MTE 9
    private int wsFinOrv = 0;                                               // 05 WS-FIN-ORV 9
    private int wsFinDrc = 0;                                               // 05 WS-FIN-DRC 9
    private int wsFinCna = 0;                                               // 05 WS-FIN-CNA 9
    private int wsFinCna1 = 0;                                              // 05 WS-FIN-CNA1 9
    private int wsFinFla = 0;                                               // 05 WS-FIN-FLA 9
    private int wsFinFla1 = 0;                                              // 05 WS-FIN-FLA1 9
    private int wsFinFla2 = 0;                                              // 05 WS-FIN-FLA2 9
    private int wsFinFla3 = 0;                                              // 05 WS-FIN-FLA3 9
    private int wsIndNal = 0;                                               // 05 WS-IND-NAL 9
    private int wsIndProv = 0;                                              // 05 WS-IND-PROV 9
    private int wsFinAfo = 0;                                               // 05 WS-FIN-AFO 9
    private int wsIndExiste = 0;                                            // 05 WS-IND-EXISTE 9

    public WsIndicadores() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsN = 0;
        this.wsP = 0;
        this.wsFin = 0;
        this.wsFinAgt = 0;
        this.wsFinPra = 0;
        this.wsFinDma = 0;
        this.wsFinDmz = 0;
        this.wsFinPob = 0;
        this.wsFinMnd = 0;
        this.wsFinEdo = 0;
        this.wsFinCma = 0;
        this.wsFinMte = 0;
        this.wsFinOrv = 0;
        this.wsFinDrc = 0;
        this.wsFinCna = 0;
        this.wsFinCna1 = 0;
        this.wsFinFla = 0;
        this.wsFinFla1 = 0;
        this.wsFinFla2 = 0;
        this.wsFinFla3 = 0;
        this.wsIndNal = 0;
        this.wsIndProv = 0;
        this.wsFinAfo = 0;
        this.wsIndExiste = 0;
    }

}

