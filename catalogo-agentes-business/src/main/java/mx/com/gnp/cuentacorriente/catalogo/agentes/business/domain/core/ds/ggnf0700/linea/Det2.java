package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DET2.
 *
 */
// 89008900*                                                                 00890002
// 90009000*                                                                 00900002
@Setter
@Getter
public class Det2 extends DataStruct {
    // Properties
    private NumericEditedVar d2AgtIdr = new NumericEditedVar(this, "ZZZZZZZZ9");             // 05 D2-AGT-IDR ZZZZZZZZ9
    private AlphanumericVar filler11 = new AlphanumericVar(this, 4);         // 05 FILLER XXXX
    private NumericEditedVar d2CnaNum = new NumericEditedVar(this, "ZZZ9");  // 05 D2-CNA-NUM ZZZ9
    private AlphanumericVar filler12 = new AlphanumericVar(this, 4);         // 05 FILLER X(4)
    private NumericEditedVar d2TfpCve = new NumericEditedVar(this, "ZZZ9");  // 05 D2-TFP-CVE ZZZ9
    private AlphanumericVar filler13 = new AlphanumericVar(this, 4);         // 05 FILLER X(4)
    private AlphanumericVar d2CnaIndGer = new AlphanumericVar(this, 1);      // 05 D2-CNA-IND-GER X
    private AlphanumericVar filler14 = new AlphanumericVar(this, 4);         // 05 FILLER X(4)
    private NumericEditedVar d2MteCve = new NumericEditedVar(this, "ZZZ9");  // 05 D2-MTE-CVE ZZZ9
    private AlphanumericVar filler15 = new AlphanumericVar(this, 4);         // 05 FILLER X(4)
    private AlphanumericVar d2FlaFecMotEss = new AlphanumericVar(this, 10);  // 05 D2-FLA-FEC-MOT-ESS X(10)
    private AlphanumericVar filler16 = new AlphanumericVar(this, 4);         // 05 FILLER X(4)
    private AlphanumericVar d2FlaNumGte = new AlphanumericVar(this, 8);      // 05 D2-FLA-NUM-GTE X(08)
    private AlphanumericVar filler17 = new AlphanumericVar(this, 2);         // 05 FILLER X(02)
    private UnsignedNumericVar d2CnaGerZonEnv = new UnsignedNumericVar(this, 4, 0);          // 05 D2-CNA-GER-ZON-ENV 9999
    private AlphanumericVar filler18 = new AlphanumericVar(this, 2);         // 05 FILLER X(02)
    private AlphanumericVar d2UsuCveAct = new AlphanumericVar(this, 8);      // 05 D2-USU-CVE-ACT X(08)
    private AlphanumericVar filler19 = new AlphanumericVar(this, 2);         // 05 FILLER X(02)
    private AlphanumericVar d2FecUltAct = new AlphanumericVar(this, 10);     // 05 D2-FEC-ULT-ACT X(10)
    private AlphanumericVar filler20 = new AlphanumericVar(this, 40);        // 05 FILLER X(40)

    public Det2() {
        super();
        initialize();
    }

    public Det2(DataContainer parent) {
        super(parent);
    }

    public Det2(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setD2AgtIdr(final String d2AgtIdr) {
        this.d2AgtIdr.setValue(d2AgtIdr);
    }

    public void setD2AgtIdr(final IValue d2AgtIdr) {
        this.d2AgtIdr.setValue(d2AgtIdr);
    }

    public void setFiller11(final String filler11) {
        this.filler11.setValue(filler11);
    }

    public void setFiller11(final IAlphanumericValue filler11) {
        this.filler11.setValue(filler11);
    }

    public void setD2CnaNum(final String d2CnaNum) {
        this.d2CnaNum.setValue(d2CnaNum);
    }

    public void setD2CnaNum(final IValue d2CnaNum) {
        this.d2CnaNum.setValue(d2CnaNum);
    }

    public void setFiller12(final String filler12) {
        this.filler12.setValue(filler12);
    }

    public void setFiller12(final IAlphanumericValue filler12) {
        this.filler12.setValue(filler12);
    }

    public void setD2TfpCve(final String d2TfpCve) {
        this.d2TfpCve.setValue(d2TfpCve);
    }

    public void setD2TfpCve(final IValue d2TfpCve) {
        this.d2TfpCve.setValue(d2TfpCve);
    }

    public void setFiller13(final String filler13) {
        this.filler13.setValue(filler13);
    }

    public void setFiller13(final IAlphanumericValue filler13) {
        this.filler13.setValue(filler13);
    }

    public void setD2CnaIndGer(final String d2CnaIndGer) {
        this.d2CnaIndGer.setValue(d2CnaIndGer);
    }

    public void setD2CnaIndGer(final IAlphanumericValue d2CnaIndGer) {
        this.d2CnaIndGer.setValue(d2CnaIndGer);
    }

    public void setFiller14(final String filler14) {
        this.filler14.setValue(filler14);
    }

    public void setFiller14(final IAlphanumericValue filler14) {
        this.filler14.setValue(filler14);
    }

    public void setD2MteCve(final String d2MteCve) {
        this.d2MteCve.setValue(d2MteCve);
    }

    public void setD2MteCve(final IValue d2MteCve) {
        this.d2MteCve.setValue(d2MteCve);
    }

    public void setFiller15(final String filler15) {
        this.filler15.setValue(filler15);
    }

    public void setFiller15(final IAlphanumericValue filler15) {
        this.filler15.setValue(filler15);
    }

    public void setD2FlaFecMotEss(final String d2FlaFecMotEss) {
        this.d2FlaFecMotEss.setValue(d2FlaFecMotEss);
    }

    public void setD2FlaFecMotEss(final IAlphanumericValue d2FlaFecMotEss) {
        this.d2FlaFecMotEss.setValue(d2FlaFecMotEss);
    }

    public void setFiller16(final String filler16) {
        this.filler16.setValue(filler16);
    }

    public void setFiller16(final IAlphanumericValue filler16) {
        this.filler16.setValue(filler16);
    }

    public void setD2FlaNumGte(final String d2FlaNumGte) {
        this.d2FlaNumGte.setValue(d2FlaNumGte);
    }

    public void setD2FlaNumGte(final IAlphanumericValue d2FlaNumGte) {
        this.d2FlaNumGte.setValue(d2FlaNumGte);
    }

    public void setFiller17(final String filler17) {
        this.filler17.setValue(filler17);
    }

    public void setFiller17(final IAlphanumericValue filler17) {
        this.filler17.setValue(filler17);
    }

    public void setD2CnaGerZonEnv(final int d2CnaGerZonEnv) {
        this.d2CnaGerZonEnv.setValue(d2CnaGerZonEnv);
    }

    public void setD2CnaGerZonEnv(final BigDecimal d2CnaGerZonEnv) {
        this.d2CnaGerZonEnv.setValue(d2CnaGerZonEnv);
    }

    public void setD2CnaGerZonEnv(final INumericValue d2CnaGerZonEnv) {
        this.d2CnaGerZonEnv.setValue(d2CnaGerZonEnv);
    }

    public void setFiller18(final String filler18) {
        this.filler18.setValue(filler18);
    }

    public void setFiller18(final IAlphanumericValue filler18) {
        this.filler18.setValue(filler18);
    }

    public void setD2UsuCveAct(final String d2UsuCveAct) {
        this.d2UsuCveAct.setValue(d2UsuCveAct);
    }

    public void setD2UsuCveAct(final IAlphanumericValue d2UsuCveAct) {
        this.d2UsuCveAct.setValue(d2UsuCveAct);
    }

    public void setFiller19(final String filler19) {
        this.filler19.setValue(filler19);
    }

    public void setFiller19(final IAlphanumericValue filler19) {
        this.filler19.setValue(filler19);
    }

    public void setD2FecUltAct(final String d2FecUltAct) {
        this.d2FecUltAct.setValue(d2FecUltAct);
    }

    public void setD2FecUltAct(final IAlphanumericValue d2FecUltAct) {
        this.d2FecUltAct.setValue(d2FecUltAct);
    }

    public void setFiller20(final String filler20) {
        this.filler20.setValue(filler20);
    }

    public void setFiller20(final IAlphanumericValue filler20) {
        this.filler20.setValue(filler20);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.d2AgtIdr.clear();
        this.filler11.clear();
        this.d2CnaNum.clear();
        this.filler12.clear();
        this.d2TfpCve.clear();
        this.filler13.clear();
        this.d2CnaIndGer.clear();
        this.filler14.clear();
        this.d2MteCve.clear();
        this.filler15.clear();
        this.d2FlaFecMotEss.clear();
        this.filler16.clear();
        this.d2FlaNumGte.clear();
        this.filler17.clear();
        this.d2CnaGerZonEnv.clear();
        this.filler18.clear();
        this.d2UsuCveAct.clear();
        this.filler19.clear();
        this.d2FecUltAct.clear();
        this.filler20.clear();
    }

}

