/**
 *  JCL Program: QADBG00.
 *  Code generation type: K<PERSON>NDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Qadbg00JclProc extends AbstractJclProgram {

    public Qadbg00JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("QADBG00", (qadbg00) -> {
            qadbg00.addParameter("PROC");
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                    * * *  QADBG00  * * *                          **/
                /*//* APLICACION : CUENTA CORRIENTE                                     **/
                /*//* PROCESO    : DIARIO                                               **/
                /*//* OBJETIVO   : DESCARGA DE TABLAS DB2 CUENTA CORRIENTE PARA         **/
                /*//*              APLICATIVOS BACKLOG.                                 **/
                /*//* COMENTARIOS:                                                      **/
                /*//* CORRE                                                             **/
                /*//* DESPUES DE :                                                      **/
                /*//* ANTES DE   : PGAQADBH (BACKLOG)                                   **/
                /*//* ELABORO    : MAURO SANCHEZ HERNANDEZ      FECHA: 07/AGO/2017      **/
                /*//**********************************************************************/
                /*//**********************************************************************/
                /*//*                         * QADBGD11 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA AADTIPA0                     **/
                /*//* PASO REINICIABLE               TS SYSTSTAB                        **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG14", "DSNUTILB", (qadbg14) -> {
                    qadbg14.addParameter("REGION","0M");
                    qadbg14.addParameter("PARM","DB2P,,");
                    qadbg14.addDataDefinition("SYSREC00", "PPCQS.QADBG00.AADTIPA0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg14.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg14.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.AADTIPA0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg14.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG11)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD10 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTBCO0                     **/
                /*//* PASO REINICIABLE               TS GCCTBCOS                        **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG13", "DSNUTILB", (qadbg13) -> {
                    qadbg13.addParameter("REGION","0M");
                    qadbg13.addParameter("PARM","DB2P,,");
                    qadbg13.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTBCO0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg13.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg13.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTBCO0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg13.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG10)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD09 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTCAK0                     **/
                /*//* PASO REINICIABLE               TS GCCTCAKS                        **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG12", "DSNUTILB", (qadbg12) -> {
                    qadbg12.addParameter("REGION","0M");
                    qadbg12.addParameter("PARM","DB2P,,");
                    qadbg12.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTCAK0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg12.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg12.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTCAK0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg12.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG09)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD08 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTCEO0                     **/
                /*//* PASO REINICIABLE                TS GCCTCEOS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG11", "DSNUTILB", (qadbg11) -> {
                    qadbg11.addParameter("REGION","0M");
                    qadbg11.addParameter("PARM","DB2P,,");
                    qadbg11.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTCEO0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg11.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg11.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTCEO0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg11.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG08)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD07 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTCVM0                     **/
                /*//* PASO REINICIABLE                TS GCCTCVMS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG10", "DSNUTILB", (qadbg10) -> {
                    qadbg10.addParameter("REGION","0M");
                    qadbg10.addParameter("PARM","DB2P,,");
                    qadbg10.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTCVM0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg10.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg10.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTCVM0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg10.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG07)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD06 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTEDO0                     **/
                /*//* PASO REINICIABLE                TS GCCTEDOS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG09", "DSNUTILB", (qadbg09) -> {
                    qadbg09.addParameter("REGION","0M");
                    qadbg09.addParameter("PARM","DB2P,,");
                    qadbg09.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTEDO0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg09.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg09.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTEDO0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg09.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG06)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD05 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTOFN0                     **/
                /*//* PASO REINICIABLE                TS GCCTOFNS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG08", "DSNUTILB", (qadbg08) -> {
                    qadbg08.addParameter("REGION","0M");
                    qadbg08.addParameter("PARM","DB2P,,");
                    qadbg08.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTOFN0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg08.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg08.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTOFN0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg08.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG05)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD04 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTPAI0                     **/
                /*//* PASO REINICIABLE                TS GCCTPAIS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG07", "DSNUTILB", (qadbg07) -> {
                    qadbg07.addParameter("REGION","0M");
                    qadbg07.addParameter("PARM","DB2P,,");
                    qadbg07.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTPAI0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg07.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg07.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTPAI0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg07.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG04)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD03 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTPANC                     **/
                /*//* PASO REINICIABLE                TS GCCTPANS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG06", "DSNUTILB", (qadbg06) -> {
                    qadbg06.addParameter("REGION","0M");
                    qadbg06.addParameter("PARM","DB2P,,");
                    qadbg06.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTPANC",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg06.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg06.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTPANC.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg06.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG03)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD02 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GCCTPOB0                     **/
                /*//* PASO REINICIABLE                TS GCCTPOBS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG05", "DSNUTILB", (qadbg05) -> {
                    qadbg05.addParameter("REGION","0M");
                    qadbg05.addParameter("PARM","DB2P,,");
                    qadbg05.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GCCTPOB0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg05.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg05.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GCCTPOB0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg05.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG02)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                         * QADBGD01 *                              **/
                /*//* OBJETIVO DEL STEP: DESCARGA DE TABLA GFVTAGC0                     **/
                /*//* PASO REINICIABLE                TS GFVTAGCS                       **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG04", "DSNUTILB", (qadbg04) -> {
                    qadbg04.addParameter("REGION","0M");
                    qadbg04.addParameter("PARM","DB2P,,");
                    qadbg04.addDataDefinition("SYSREC00", "PPCQS.QADBG00.GFVTAGC0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","SHRAPL");
                        sysrec00.addParameter("SPACE", "(CYL,(200,100),RLSE)");
                    });
                    qadbg04.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg04.addDataDefinition("SYSPUNCH", "PPCQS.QADBG00.GFVTAGC0.SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        syspunch.addParameter("UNIT","SHRAPL");
                        syspunch.addParameter("SPACE", "(CYL,(20,10),RLSE)");
                    });
                    qadbg04.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG01)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()==0) {
                /*//**/
                /*//**********************************************************************/
                /*//*  OBJETIVO : EXTRACCION BASE DE DATOS FUERZA DE VENTAS*/
                /*//**/
                /*//*  REINICIO : PASO REINICIABLE*/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG03", "IKJEFT01", (qadbg03) -> {
                    qadbg03.addDataDefinition("STEPLIB",  (steplib) -> {
                        steplib.addDataDefinition("STEPLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                            _concat.addParameter("DISP","SHR");
                        });
                        steplib.addDataDefinition("STEPLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                            _concat.addParameter("DISP","SHR");
                        });
                    });
                    /*//*            ARCHIVO INTERFAZ DE AGENTES*/
                    qadbg03.addDataDefinition("GNPL505O", "PGA.QADBG00.EDPL505",  (gnpl505o) -> {
                        gnpl505o.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        gnpl505o.addParameter("UNIT","SHRAPL");
                        gnpl505o.addParameter("SPACE", "(CYL,(300,150),RLSE)");
                        gnpl505o.addParameter("DCB", "(RECFM=FB,LRECL=844,BLKSIZE=22788,DSORG=PS)");
                    });
                    qadbg03.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","X");
                    });
                    qadbg03.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg03.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(QADBG13)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=9) {
                /*//**/
                /*//*********************************************************************/
                /*//* OBJETIVO:  ELIMINAR CARACTERES ESPECIALES EN EL ARCHIVO EDPL505  **/
                /*//**/
                /*//*  REINICIO : PASO REINICIABLE*/
                /*//*********************************************************************/
                qadbg00.execProgram("QADBG03A", "IKJEFT01", (qadbg03a) -> {
                    qadbg03a.addDataDefinition("SYSPROC", "PXSTP.REXX.FUENTES",  (sysproc) -> {
                        sysproc.addParameter("DISP","SHR");
                    });
                    qadbg03a.addDataDefinition("ISPTLIB", "SYS1.ISP.SISPTENU",  (isptlib) -> {
                        isptlib.addParameter("DISP","SHR");
                    });
                    qadbg03a.addDataDefinition("ISPPLIB", "SYS1.ISP.SISPPENU",  (ispplib) -> {
                        ispplib.addParameter("DISP","SHR");
                    });
                    qadbg03a.addDataDefinition("ISPMLIB", "SYS1.ISP.SISPMENU",  (ispmlib) -> {
                        ispmlib.addParameter("DISP","SHR");
                    });
                    qadbg03a.addDataDefinition("ISPSLIB", "SYS1.ISP.SISPSLIB",  (ispslib) -> {
                        ispslib.addParameter("DISP","SHR");
                    });
                    qadbg03a.addDataDefinition("ISPPROF", "&&PROFTMP",  (ispprof) -> {
                        ispprof.addParameter("DISP", "(NEW,DELETE)");
                        ispprof.addParameter("SPACE", "(CYL,(300,150,150))");
                        ispprof.addParameter("DCB", "(DSORG=PO,RECFM=FB,LRECL=80,BLKSIZE=0)");
                        ispprof.addParameter("UNIT","3390");
                    });
                    qadbg03a.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    qadbg03a.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qadbg03a.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg03a.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(QADBG16)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=9) {
                /*//*********************************************************************/
                /*//* OBJETIVO:  ELIMINAR CARACTERES ESPECIALES EN EL ARCHIVO EDPL505  **/
                /*//**/
                /*//*  REINICIO : PASO REINICIABLE*/
                /*//*********************************************************************/
                qadbg00.execProgram("QADBG03B", "IKJEFT01", (qadbg03b) -> {
                    qadbg03b.addDataDefinition("SYSPROC", "PXSTP.REXX.FUENTES",  (sysproc) -> {
                        sysproc.addParameter("DISP","SHR");
                    });
                    qadbg03b.addDataDefinition("ISPTLIB", "SYS1.ISP.SISPTENU",  (isptlib) -> {
                        isptlib.addParameter("DISP","SHR");
                    });
                    qadbg03b.addDataDefinition("ISPPLIB", "SYS1.ISP.SISPPENU",  (ispplib) -> {
                        ispplib.addParameter("DISP","SHR");
                    });
                    qadbg03b.addDataDefinition("ISPMLIB", "SYS1.ISP.SISPMENU",  (ispmlib) -> {
                        ispmlib.addParameter("DISP","SHR");
                    });
                    qadbg03b.addDataDefinition("ISPSLIB", "SYS1.ISP.SISPSLIB",  (ispslib) -> {
                        ispslib.addParameter("DISP","SHR");
                    });
                    qadbg03b.addDataDefinition("ISPPROF", "&&PROFTMP",  (ispprof) -> {
                        ispprof.addParameter("DISP", "(NEW,DELETE)");
                        ispprof.addParameter("SPACE", "(CYL,(300,150,150))");
                        ispprof.addParameter("DCB", "(DSORG=PO,RECFM=FB,LRECL=80,BLKSIZE=0)");
                        ispprof.addParameter("UNIT","3390");
                    });
                    qadbg03b.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    qadbg03b.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qadbg03b.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg03b.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(QADBG17)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*  OBJETIVO : CLASIFICA ARCHIVO INTERFAZ DE AGENTES*/
                /*//**/
                /*//*  REINICIO : PASO REINICIABLE*/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG02", "SORT", (qadbg02) -> {
                    /*//*            ARCHIVO INTERFAZ DE AGENTES*/
                    qadbg02.addDataDefinition("SORTIN", "PGA.QADBG00.EDPL505",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//*            ARCHIVO INTERFAZ DE AGENTES CLASIFICADO*/
                    qadbg02.addDataDefinition("SORTOUT", "PPCQS.QADBG00.EDPL506",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","SHRAPL");
                        sortout.addParameter("SPACE", "(CYL,(300,150),RLSE)");
                        sortout.addParameter("DCB", "(RECFM=FB,LRECL=844,BLKSIZE=22788,DSORG=PS)");
                    });
                    qadbg02.addDataDefinition("SORTWK01",  (sortwk01) -> {
                        sortwk01.addParameter("UNIT","WORKPROD");
                        sortwk01.addParameter("SPACE", "(CYL,(5,3),RLSE)");
                    });
                    qadbg02.addDataDefinition("SORTWK02",  (sortwk02) -> {
                        sortwk02.addParameter("UNIT","WORKPROD");
                        sortwk02.addParameter("SPACE", "(CYL,(5,3),RLSE)");
                    });
                    qadbg02.addDataDefinition("SORTWK03",  (sortwk03) -> {
                        sortwk03.addParameter("UNIT","WORKPROD");
                        sortwk03.addParameter("SPACE", "(CYL,(5,3),RLSE)");
                    });
                    qadbg02.addDataDefinition("SORTWK04",  (sortwk04) -> {
                        sortwk04.addParameter("UNIT","WORKPROD");
                        sortwk04.addParameter("SPACE", "(CYL,(5,3),RLSE)");
                    });
                    qadbg02.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qadbg02.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG14)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<8) {
                /*//**/
                /*//**********************************************************************/
                /*//*  OBJETIVO : RESPALDA LOS ARCHIVOS DEL PROCESO*/
                /*//**/
                /*//*  REINICIO : PASO REINICIABLE*/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG01", "COPIADOR", (qadbg01) -> {
                    qadbg01.addParameter("REGION","0M");
                    qadbg01.addDataDefinition("OUTFILE1", "SYSBKW.QADBG00.RESPALDO(+1)",  (outfile1) -> {
                        outfile1.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        outfile1.addParameter("UNIT","DDVTAV");
                        outfile1.addParameter("LABEL", "(1,SL,EXPDT=99000)");
                        outfile1.addParameter("DCB", "(GDG.MODELO,DSORG=PS)");
                    });
                    qadbg01.addDummyDataDefinition("OUTFILE2",  (outfile2) -> {
                    });
                    /*//*            ARCHIVO INTERFAZ DE AGENTES*/
                    qadbg01.addDataDefinition("EDPL505O", "PGA.QADBG00.EDPL505",  (edpl505o) -> {
                        edpl505o.addParameter("DISP","SHR");
                    });
                    /*//*            ARCHIVO INTERFAZ DE AGENTES CLASIFICADO*/
                    qadbg01.addDataDefinition("EDPL506S", "PPCQS.QADBG00.EDPL506",  (edpl506s) -> {
                        edpl506s.addParameter("DISP","SHR");
                    });
                    qadbg01.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qadbg01.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qadbg01.addDataDefinition("SYUDUMP",  (syudump) -> {
                        syudump.addParameter("SYSOUT","*");
                    });
                    qadbg01.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                        sysdbout.addParameter("SYSOUT","*");
                    });
                    /*//*            PARAMETRO ARCHIVOS A RESPALDAR*/
                    qadbg01.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QADBG15)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qadbg00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                         * QADBG00  *                              **/
                /*//* OBJETIVO DEL STEP: ENVIA A BACKLOG ARCHIVO INTERFAZ               **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                qadbg00.execProgram("QADBG00", "FTP", (_qadbg00) -> {
                    _qadbg00.addParameter("PARM","-t ANDANTIS (EXIT");
                    _qadbg00.addParameter("REGION","0M");
                    _qadbg00.addDataDefinition("INPUT", "PNCQP.BTCH.CARDS(QADBG12)",  (input) -> {
                        input.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
