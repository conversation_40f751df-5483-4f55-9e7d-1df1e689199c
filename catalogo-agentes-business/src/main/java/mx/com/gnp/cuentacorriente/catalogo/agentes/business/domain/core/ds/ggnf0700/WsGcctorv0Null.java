package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GCCTORV0-NULL.
 *
 */
// 650038200*                                                                 03820002
// 651038300*                                                                 03830002
@Setter
@Getter
public class WsGcctorv0Null extends DataStruct {
    // Properties
    private BinaryVar orv01 = new BinaryVar(this, 4, 0);                     // 03 ORV01 S9(4) COMPUTATIONAL
    private BinaryVar orv02 = new BinaryVar(this, 4, 0);                     // 03 ORV02 S9(4) COMPUTATIONAL
    private BinaryVar orv03 = new BinaryVar(this, 4, 0);                     // 03 ORV03 S9(4) COMPUTATIONAL
    private BinaryVar orv04 = new BinaryVar(this, 4, 0);                     // 03 ORV04 S9(4) COMPUTATIONAL
    private BinaryVar orv05 = new BinaryVar(this, 4, 0);                     // 03 ORV05 S9(4) COMPUTATIONAL
    private BinaryVar orv06 = new BinaryVar(this, 4, 0);                     // 03 ORV06 S9(4) COMPUTATIONAL
    private BinaryVar orv07 = new BinaryVar(this, 4, 0);                     // 03 ORV07 S9(4) COMPUTATIONAL
    private BinaryVar orv08 = new BinaryVar(this, 4, 0);                     // 03 ORV08 S9(4) COMPUTATIONAL
    private BinaryVar orv09 = new BinaryVar(this, 4, 0);                     // 03 ORV09 S9(4) COMPUTATIONAL

    public WsGcctorv0Null() {
        super();
        initialize();
    }

    public WsGcctorv0Null(DataContainer parent) {
        super(parent);
    }

    public WsGcctorv0Null(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setOrv01(final int orv01) {
        this.orv01.setValue(orv01);
    }

    public void setOrv01(final BigDecimal orv01) {
        this.orv01.setValue(orv01);
    }

    public void setOrv01(final INumericValue orv01) {
        this.orv01.setValue(orv01);
    }

    public void setOrv02(final int orv02) {
        this.orv02.setValue(orv02);
    }

    public void setOrv02(final BigDecimal orv02) {
        this.orv02.setValue(orv02);
    }

    public void setOrv02(final INumericValue orv02) {
        this.orv02.setValue(orv02);
    }

    public void setOrv03(final int orv03) {
        this.orv03.setValue(orv03);
    }

    public void setOrv03(final BigDecimal orv03) {
        this.orv03.setValue(orv03);
    }

    public void setOrv03(final INumericValue orv03) {
        this.orv03.setValue(orv03);
    }

    public void setOrv04(final int orv04) {
        this.orv04.setValue(orv04);
    }

    public void setOrv04(final BigDecimal orv04) {
        this.orv04.setValue(orv04);
    }

    public void setOrv04(final INumericValue orv04) {
        this.orv04.setValue(orv04);
    }

    public void setOrv05(final int orv05) {
        this.orv05.setValue(orv05);
    }

    public void setOrv05(final BigDecimal orv05) {
        this.orv05.setValue(orv05);
    }

    public void setOrv05(final INumericValue orv05) {
        this.orv05.setValue(orv05);
    }

    public void setOrv06(final int orv06) {
        this.orv06.setValue(orv06);
    }

    public void setOrv06(final BigDecimal orv06) {
        this.orv06.setValue(orv06);
    }

    public void setOrv06(final INumericValue orv06) {
        this.orv06.setValue(orv06);
    }

    public void setOrv07(final int orv07) {
        this.orv07.setValue(orv07);
    }

    public void setOrv07(final BigDecimal orv07) {
        this.orv07.setValue(orv07);
    }

    public void setOrv07(final INumericValue orv07) {
        this.orv07.setValue(orv07);
    }

    public void setOrv08(final int orv08) {
        this.orv08.setValue(orv08);
    }

    public void setOrv08(final BigDecimal orv08) {
        this.orv08.setValue(orv08);
    }

    public void setOrv08(final INumericValue orv08) {
        this.orv08.setValue(orv08);
    }

    public void setOrv09(final int orv09) {
        this.orv09.setValue(orv09);
    }

    public void setOrv09(final BigDecimal orv09) {
        this.orv09.setValue(orv09);
    }

    public void setOrv09(final INumericValue orv09) {
        this.orv09.setValue(orv09);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.orv01.clear();
        this.orv02.clear();
        this.orv03.clear();
        this.orv04.clear();
        this.orv05.clear();
        this.orv06.clear();
        this.orv07.clear();
        this.orv08.clear();
        this.orv09.clear();
    }

}

