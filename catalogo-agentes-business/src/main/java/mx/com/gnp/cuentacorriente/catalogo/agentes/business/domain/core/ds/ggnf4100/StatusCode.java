package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: STATUS-CODE.
 *
 */
@Setter
@Getter
public class StatusCode { // VStruct
    // Properties
    private String wsFile = "";                                             // 05 WS-FILE X(2)

    public StatusCode() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsFile = "";
    }

}

