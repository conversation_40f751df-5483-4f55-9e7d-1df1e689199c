package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: A990-CIF-CTL.
 *
 */
// 353      *----                                                             
// 354      *    CIFRAS CONTROL                                               
// 355      *----                                                             
@Setter
@Getter
public class A990CifCtl { // VStruct
    // Properties
    private int a990RegLeiOfnA1;                                            // 05 A990-REG-LEI-OFN-A1 S9(09) COMPUTATIONAL
    private int a990RegLeiInsHfo;                                           // 05 A990-REG-LEI-INS-HFO S9(09) COMPUTATIONAL
    private int a990RegLeiUpdHfo;                                           // 05 A990-REG-LEI-UPD-HFO S9(09) COMPUTATIONAL

    public A990CifCtl() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.a990RegLeiOfnA1 = 0;
        this.a990RegLeiInsHfo = 0;
        this.a990RegLeiUpdHfo = 0;
    }

}

