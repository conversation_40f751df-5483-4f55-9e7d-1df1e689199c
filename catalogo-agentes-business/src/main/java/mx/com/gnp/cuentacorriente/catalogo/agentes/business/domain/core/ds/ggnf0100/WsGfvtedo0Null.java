package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTEDO0-NULL.
 *
 */
// 1103063800*                                                                 06380000
// 1104063900*                                                                 06390000
@Setter
@Getter
public class WsGfvtedo0Null { // VStruct
    // Properties
    private int edo01;                                                      // 03 EDO01 S9(4) COMPUTATIONAL
    private int edo02;                                                      // 03 EDO02 S9(4) COMPUTATIONAL
    private int edo03;                                                      // 03 EDO03 S9(4) COMPUTATIONAL
    private int edo04;                                                      // 03 EDO04 S9(4) COMPUTATIONAL
    private int edo05;                                                      // 03 EDO05 S9(4) COMPUTATIONAL
    private int edo06;                                                      // 03 EDO06 S9(4) COMPUTATIONAL

    public WsGfvtedo0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.edo01 = 0;
        this.edo02 = 0;
        this.edo03 = 0;
        this.edo04 = 0;
        this.edo05 = 0;
        this.edo06 = 0;
    }

}

