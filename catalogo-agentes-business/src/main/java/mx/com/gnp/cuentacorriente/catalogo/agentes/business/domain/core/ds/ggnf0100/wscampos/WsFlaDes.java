package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FLA-DES.
 *
 */
// 1220074800*                                                                 07480000
@Setter
@Getter
public class WsFlaDes { // VStruct
    // Properties
    private String wsFlaDesDos = "";                                        // 05 WS-FLA-DES-DOS XX
    private String wsFlaDesResto = "";                                      // 05 WS-FLA-DES-RESTO X(38)

    public WsFlaDes() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsFlaDesDos = "";
        this.wsFlaDesResto = "";
    }

}

