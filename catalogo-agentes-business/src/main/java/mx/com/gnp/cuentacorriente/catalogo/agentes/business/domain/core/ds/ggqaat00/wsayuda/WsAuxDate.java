package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-DATE.
 *
 */
@Setter
@Getter
public class WsAuxDate extends DataStruct {
    // Properties
    private AlphanumericVar wsAuxDateAaaa = new AlphanumericVar(this, 4);    // 10 WS-AUX-DATE-AAAA X(04)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private AlphanumericVar wsAuxDateMm = new AlphanumericVar(this, 2);      // 10 WS-AUX-DATE-MM X(02)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 1);          // 10 FILLER X(01)
    private AlphanumericVar wsAuxDateDd = new AlphanumericVar(this, 2);      // 10 WS-AUX-DATE-DD X(02)

    public WsAuxDate() {
        super();
        initialize();
    }

    public WsAuxDate(DataContainer parent) {
        super(parent);
    }

    public WsAuxDate(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAuxDateAaaa(final String wsAuxDateAaaa) {
        this.wsAuxDateAaaa.setValue(wsAuxDateAaaa);
    }

    public void setWsAuxDateAaaa(final IAlphanumericValue wsAuxDateAaaa) {
        this.wsAuxDateAaaa.setValue(wsAuxDateAaaa);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setWsAuxDateMm(final String wsAuxDateMm) {
        this.wsAuxDateMm.setValue(wsAuxDateMm);
    }

    public void setWsAuxDateMm(final IAlphanumericValue wsAuxDateMm) {
        this.wsAuxDateMm.setValue(wsAuxDateMm);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setWsAuxDateDd(final String wsAuxDateDd) {
        this.wsAuxDateDd.setValue(wsAuxDateDd);
    }

    public void setWsAuxDateDd(final IAlphanumericValue wsAuxDateDd) {
        this.wsAuxDateDd.setValue(wsAuxDateDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAuxDateAaaa.clear();
        this.filler1.clear();
        this.wsAuxDateMm.clear();
        this.filler2.clear();
        this.wsAuxDateDd.clear();
    }

}

