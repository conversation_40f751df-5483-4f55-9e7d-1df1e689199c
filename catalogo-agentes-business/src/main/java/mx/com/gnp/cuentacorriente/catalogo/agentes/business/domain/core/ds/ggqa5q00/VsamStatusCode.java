package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: VSAM-STATUS-CODE.
 *
 */
@Setter
@Getter
public class VsamStatusCode { // VStruct
    // Properties
    private int vsamReturnCode;                                             // 02 VSAM-RETURN-CODE S9(4) COMPUTATIONAL
    private int vsamFunctionCode;                                           // 02 VSAM-FUNCTION-CODE S9(4) COMPUTATIONAL
    private int vsamFeedbackCode;                                           // 02 VSAM-FEEDBACK-CODE S9(4) COMPUTATIONAL

    public VsamStatusCode() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.vsamReturnCode = 0;
        this.vsamFunctionCode = 0;
        this.vsamFeedbackCode = 0;
    }

}

