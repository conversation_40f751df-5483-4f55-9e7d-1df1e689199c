/**
 *  JCL Program: NFDBF00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Nfdbf00JclProc extends AbstractJclProgram {

    public Nfdbf00JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("NFDBF00", (nfdbf00) -> {
            nfdbf00.addParameter("PROC");
            if (nfdbf00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                     *** NFDBF00 ***                  S.A.E.T.A.   **/
                /*//*   APLICACION:  SAETA 2000.                                        **/
                /*//*   PROCESO:     NFDBF00.                                           **/
                /*//*   OBJETIVO:    ACTUALIZACION DE TABLAS:                           **/
                /*//*                           GVTAGC0  : CARTERA DE AGENTES           **/
                /*//*                                      (ALTAS, CAMBIOS)             **/
                /*//*                           GVTCAF0  : CARTERA AGENTES FACULTAD.    **/
                /*//*                                      (ALTAS)                      **/
                /*//*                           GVTCAF0  : CARTERA AGENTES FACULTAD.    **/
                /*//*                                      (BAJAS)                      **/
                /*//*   COMENTARIOS:                                                    **/
                /*//*   CORRE                                                           **/
                /*//*   DESPUES DE:  NFD0200  ACT. DIARIA DE SAETA.                     **/
                /*//*   ANTES DE:    NFD0400  ACT. DIARIA DE CATALOGO DE AGENTES.       **/
                /*//*   ELABORO:     MOISES OLVERA HERNANDEZ  (CTI). FECHA: 19/OCT/99.  **/
                /*//**********************************************************************/
                /*//*   MODIFICO  (CBA) CARLOS BALCAZAR AMARO  (CODESIS)                **/
                /*//*   FECHA   : 28/FEB/2001                                           **/
                /*//*   OBJETIVO: REINGENIERIA. SE AGREGARON DOS PASOS DE DESCARGA DE   **/
                /*//*             TABLAS DE LA BASE DE DATOS Y TRES DE CLASIFICACION    **/
                /*//**********************************************************************/
                /*//*                         * NFDBF07 *                               **/
                /*//* OBJETIVO DEL STEP : DESCARGA LA INFORMACION DE LA ENTIDAD DE    - **/
                /*//*                     AGENTES                                       **/
                /*//*                              " GFVTAGC0 "                         **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdbf00.execProgram("NFDBF07", "IKJEFT01", (nfdbf07) -> {
                    nfdbf07.addParameter("REGION","4M");
                    nfdbf07.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdbf07.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdbf07.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    nfdbf07.addDataDefinition("SYSREC00", "PGA.EAFVBF09",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","TEMPROD");
                        sysrec00.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sysrec00.addParameter("DCB", "(LRECL=333,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdbf07.addDummyDataDefinition("SYSPUNCH",  (syspunch) -> {
                    });
                    nfdbf07.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDBF05)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                    nfdbf07.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDBF08)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdbf00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                         * NFDBF06 *                               **/
                /*//* OBJETIVO DEL PASO:  CLASIFICA ARCHIVO DE AGENTES.*/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                nfdbf00.execProgram("NFDBF06", "SORT", (nfdbf06) -> {
                    /*//*           ARCHIVO DE FOLIOS*/
                    nfdbf06.addDataDefinition("SORTIN", "PGA.EAFVBF09",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//*           ARCHIVO DE FOLIOS CLASIFICADO*/
                    nfdbf06.addDataDefinition("SORTOUT", "PGA.EAFVBF08",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(250,200),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=333,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdbf06.addDataDefinition("SORTWK01",  (sortwk01) -> {
                        sortwk01.addParameter("UNIT","WORKPROD");
                        sortwk01.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf06.addDataDefinition("SORTWK02",  (sortwk02) -> {
                        sortwk02.addParameter("UNIT","WORKPROD");
                        sortwk02.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf06.addDataDefinition("SORTWK03",  (sortwk03) -> {
                        sortwk03.addParameter("UNIT","WORKPROD");
                        sortwk03.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf06.addDataDefinition("SORTWK04",  (sortwk04) -> {
                        sortwk04.addParameter("UNIT","WORKPROD");
                        sortwk04.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf06.addDataDefinition("SORTWK05",  (sortwk05) -> {
                        sortwk05.addParameter("UNIT","WORKPROD");
                        sortwk05.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf06.addDataDefinition("SORTWK06",  (sortwk06) -> {
                        sortwk06.addParameter("UNIT","WORKPROD");
                        sortwk06.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf06.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdbf06.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*           TARJETA.PARAMETRO.DE.SORT.*/
                    nfdbf06.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDBF07)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdbf00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                         * NFDBF05 *                               **/
                /*//* OBJETIVO DEL STEP : DESCARGA LA INFORMACION DE LA ENTIDAD DE    - **/
                /*//*                     FOLIOS                                        **/
                /*//*                              " GNFTFLA0 "                         **/
                /*//* PASO REINICIABLE                                                  **/
                /*//**********************************************************************/
                nfdbf00.execProgram("NFDBF05", "IKJEFT01", (nfdbf05) -> {
                    nfdbf05.addParameter("REGION","4M");
                    nfdbf05.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdbf05.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdbf05.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    nfdbf05.addDataDefinition("SYSREC00", "PGA.EAFVBF07",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysrec00.addParameter("UNIT","TEMPROD");
                        sysrec00.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sysrec00.addParameter("DCB", "(LRECL=106,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdbf05.addDummyDataDefinition("SYSPUNCH",  (syspunch) -> {
                    });
                    nfdbf05.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDBF05)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                    nfdbf05.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDBF06)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdbf00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                         * NFDBF04 **/
                /*//* OBJETIVO DEL PASO:  CLASIFICA ARCHIVO DE FOLIOS PARA PGMS*/
                /*//* PASO REINICIABLE    GGNF0700 Y GGNF0900*/
                /*//**********************************************************************/
                nfdbf00.execProgram("NFDBF04", "SORT", (nfdbf04) -> {
                    /*//*           ARCHIVO DE FOLIOS*/
                    nfdbf04.addDataDefinition("SORTIN", "PGA.EAFVBF07",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//*           ARCHIVO DE FOLIOS CLASIFICADO*/
                    nfdbf04.addDataDefinition("SORTOUT", "PGA.EAFVBF06",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=106,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdbf04.addDataDefinition("SORTWK01",  (sortwk01) -> {
                        sortwk01.addParameter("UNIT","WORKPROD");
                        sortwk01.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf04.addDataDefinition("SORTWK02",  (sortwk02) -> {
                        sortwk02.addParameter("UNIT","WORKPROD");
                        sortwk02.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf04.addDataDefinition("SORTWK03",  (sortwk03) -> {
                        sortwk03.addParameter("UNIT","WORKPROD");
                        sortwk03.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf04.addDataDefinition("SORTWK04",  (sortwk04) -> {
                        sortwk04.addParameter("UNIT","WORKPROD");
                        sortwk04.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf04.addDataDefinition("SORTWK05",  (sortwk05) -> {
                        sortwk05.addParameter("UNIT","WORKPROD");
                        sortwk05.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf04.addDataDefinition("SORTWK06",  (sortwk06) -> {
                        sortwk06.addParameter("UNIT","WORKPROD");
                        sortwk06.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf04.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdbf04.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*           TARJETA.PARAMETRO.DE.SORT.*/
                    nfdbf04.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDBF07)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdbf00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                         * NFDBF03 **/
                /*//* OBJETIVO DEL PASO:  CLASIFICA ARCHIVO DE FOLIOS PARA PROGRAMA*/
                /*//* PASO REINICIABLE    GGNF0800*/
                /*//**********************************************************************/
                nfdbf00.execProgram("NFDBF03", "SORT", (nfdbf03) -> {
                    /*//*           ARCHIVO DE FOLIOS*/
                    nfdbf03.addDataDefinition("SORTIN", "PGA.EAFVBF07",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//*           ARCHIVO DE FOLIOS CLASIFICADO*/
                    nfdbf03.addDataDefinition("SORTOUT", "PGA.EAFVBF05",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(50,20),RLSE)");
                        sortout.addParameter("DCB", "(LRECL=106,BLKSIZE=0,RECFM=FB,DSORG=PS)");
                    });
                    nfdbf03.addDataDefinition("SORTWK01",  (sortwk01) -> {
                        sortwk01.addParameter("UNIT","WORKPROD");
                        sortwk01.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf03.addDataDefinition("SORTWK02",  (sortwk02) -> {
                        sortwk02.addParameter("UNIT","WORKPROD");
                        sortwk02.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf03.addDataDefinition("SORTWK03",  (sortwk03) -> {
                        sortwk03.addParameter("UNIT","WORKPROD");
                        sortwk03.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf03.addDataDefinition("SORTWK04",  (sortwk04) -> {
                        sortwk04.addParameter("UNIT","WORKPROD");
                        sortwk04.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf03.addDataDefinition("SORTWK05",  (sortwk05) -> {
                        sortwk05.addParameter("UNIT","WORKPROD");
                        sortwk05.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf03.addDataDefinition("SORTWK06",  (sortwk06) -> {
                        sortwk06.addParameter("UNIT","WORKPROD");
                        sortwk06.addParameter("SPACE", "(CYL,(10,10))");
                    });
                    nfdbf03.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdbf03.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*           TARJETA.PARAMETRO.DE.SORT.*/
                    nfdbf03.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDBF09)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdbf00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                         * NFDBF02 **/
                /*//* OBJETIVO DEL STEP:  ACTUALIZA  TABLA  GNFTAGC0 (DB2)*/
                /*//*                     (ALTAS, CAMBIOS)*/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                nfdbf00.execProgram("NFDBF02", "IKJEFT01", (nfdbf02) -> {
                    nfdbf02.addParameter("REGION","0K");
                    /*//*        ** DESCARGA DE LA TABLA DE FOLIOS GNFTFLA0*/
                    nfdbf02.addDataDefinition("FOLIOS", "PGA.EAFVBF06",  (folios) -> {
                        folios.addParameter("DISP","SHR");
                    });
                    /*//*        ** DESCARGA DE LA TABLA DE AGENTES GNFTAGC0*/
                    nfdbf02.addDataDefinition("AGENTES", "PGA.EAFVBF08",  (agentes) -> {
                        agentes.addParameter("DISP","SHR");
                    });
                    /*//*        ** REPORTE DE ALTAS, CAMBIOS A  GNFCAGC0*/
                    nfdbf02.addDataDefinition("GGNF07R1", "PGA.EAFVDM01",  (ggnf07r1) -> {
                        ggnf07r1.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        ggnf07r1.addParameter("UNIT","TEMPROD");
                        ggnf07r1.addParameter("SPACE", "(CYL,(80,20),RLSE)");
                        ggnf07r1.addParameter("DCB", "(RECFM=FB,LRECL=134,BLKSIZE=23450,BUFNO=04,DSORG=PS)");
                    });
                    nfdbf02.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdbf02.addDataDefinition("CMPRINT",  (cmprint) -> {
                        cmprint.addParameter("SYSOUT","*");
                    });
                    nfdbf02.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                        sysdbout.addParameter("SYSOUT","*");
                    });
                    nfdbf02.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdbf02.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdbf02.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","D");
                    });
                    nfdbf02.addDataDefinition("SYSABOUT",  (sysabout) -> {
                        sysabout.addParameter("SYSOUT","*");
                    });
                    nfdbf02.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDBF01)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdbf00.getLastReturnCode()==0) {
                /*//**********************************************************************/
                /*//*                         * NFDBF01 **/
                /*//* OBJETIVO DEL STEP:  ACTUALIZA  TABLA  GNFTCAF0 (DB2)*/
                /*//*                     (ALTAS)*/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                nfdbf00.execProgram("NFDBF01", "IKJEFT01", (nfdbf01) -> {
                    nfdbf01.addParameter("REGION","0K");
                    /*//*        ** DESCARGA DE LA TABLA DE FOLIOS GNFTFLA0*/
                    nfdbf01.addDataDefinition("FOLIOS", "PGA.EAFVBF05",  (folios) -> {
                        folios.addParameter("DISP","SHR");
                    });
                    /*//*        ** REPORTE DE ALTAS  A  GNFCCAF0*/
                    nfdbf01.addDataDefinition("GGNF08R1", "PGA.EAFVDN01",  (ggnf08r1) -> {
                        ggnf08r1.addParameter("DISP", "(NEW,CATLG,CATLG)");
                        ggnf08r1.addParameter("UNIT","TEMPROD");
                        ggnf08r1.addParameter("SPACE", "(CYL,(80,20),RLSE)");
                        ggnf08r1.addParameter("DCB", "(RECFM=FB,LRECL=134,BLKSIZE=23450,BUFNO=04,DSORG=PS)");
                    });
                    nfdbf01.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdbf01.addDataDefinition("CMPRINT",  (cmprint) -> {
                        cmprint.addParameter("SYSOUT","*");
                    });
                    nfdbf01.addDataDefinition("SYSDBOUT",  (sysdbout) -> {
                        sysdbout.addParameter("SYSOUT","*");
                    });
                    nfdbf01.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdbf01.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdbf01.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","D");
                    });
                    nfdbf01.addDataDefinition("SYSABOUT",  (sysabout) -> {
                        sysabout.addParameter("SYSOUT","*");
                    });
                    nfdbf01.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDBF02)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
