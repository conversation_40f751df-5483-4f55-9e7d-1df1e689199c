package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.plhcmkttrailer.PlhcMktCompanyT;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PLHC-MKT-TRAILER.
 *
 */
// 644LUV001*=>     0844-00844                                                01310020
// 645      *                                                                 01320000
@Setter
@Getter
public class PlhcMktTrailer extends DataStruct {
    // Properties
    private AlphanumericVar plhcMktRecordIdt = new AlphanumericVar(this, 5);                 // 10 PLHC-MKT-RECORD-IDT X(05)
    private PlhcMktCompanyT plhcMktCompanyT = new  PlhcMktCompanyT(this);    // 10 PLHC-MKT-COMPANY-T
    private UnsignedNumericVar plhcMktRecordCount = new UnsignedNumericVar(this, 6, 0);      // 10 PLHC-MKT-RECORD-COUNT 9(06)
    private AlphanumericVar filler11 = new AlphanumericVar(this, 815);       // 10 FILLER X(815)
    private AlphanumericVar plhcMktSortIndt = new AlphanumericVar(this, 1);  // 10 PLHC-MKT-SORT-INDT X(0001)
    private AlphanumericVar filler12 = new AlphanumericVar(this, 10);        // 10 FILLER X(0010)
    private AlphanumericVar plhcMktEndrect = new AlphanumericVar(this, 1);   // 10 PLHC-MKT-ENDRECT X(0001)

    public PlhcMktTrailer() {
        super();
        initialize();
    }

    public PlhcMktTrailer(DataContainer parent) {
        super(parent);
    }

    public PlhcMktTrailer(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPlhcMktRecordIdt(final String plhcMktRecordIdt) {
        this.plhcMktRecordIdt.setValue(plhcMktRecordIdt);
    }

    public void setPlhcMktRecordIdt(final IAlphanumericValue plhcMktRecordIdt) {
        this.plhcMktRecordIdt.setValue(plhcMktRecordIdt);
    }

    public void setPlhcMktRecordCount(final int plhcMktRecordCount) {
        this.plhcMktRecordCount.setValue(plhcMktRecordCount);
    }

    public void setPlhcMktRecordCount(final BigDecimal plhcMktRecordCount) {
        this.plhcMktRecordCount.setValue(plhcMktRecordCount);
    }

    public void setPlhcMktRecordCount(final INumericValue plhcMktRecordCount) {
        this.plhcMktRecordCount.setValue(plhcMktRecordCount);
    }

    public void setFiller11(final String filler11) {
        this.filler11.setValue(filler11);
    }

    public void setFiller11(final IAlphanumericValue filler11) {
        this.filler11.setValue(filler11);
    }

    public void setPlhcMktSortIndt(final String plhcMktSortIndt) {
        this.plhcMktSortIndt.setValue(plhcMktSortIndt);
    }

    public void setPlhcMktSortIndt(final IAlphanumericValue plhcMktSortIndt) {
        this.plhcMktSortIndt.setValue(plhcMktSortIndt);
    }

    public void setFiller12(final String filler12) {
        this.filler12.setValue(filler12);
    }

    public void setFiller12(final IAlphanumericValue filler12) {
        this.filler12.setValue(filler12);
    }

    public void setPlhcMktEndrect(final String plhcMktEndrect) {
        this.plhcMktEndrect.setValue(plhcMktEndrect);
    }

    public void setPlhcMktEndrect(final IAlphanumericValue plhcMktEndrect) {
        this.plhcMktEndrect.setValue(plhcMktEndrect);
    }


    // Deeper properties

    // Property: plhcMktCompanyT.plhcMktCompanyIdT -> 15 PLHC-MKT-COMPANY-ID-T X(03)
    public AlphanumericVar getPlhcMktCompanyIdT() {
        return this.plhcMktCompanyT.getPlhcMktCompanyIdT();
    }

    public void setPlhcMktCompanyIdT(final String plhcMktCompanyIdT) {
        this.plhcMktCompanyT.setPlhcMktCompanyIdT(plhcMktCompanyIdT);
    }

    public void setPlhcMktCompanyIdT(final IAlphanumericValue plhcMktCompanyIdT) {
        this.plhcMktCompanyT.setPlhcMktCompanyIdT(plhcMktCompanyIdT);
    }

    // Property: plhcMktCompanyT.filler10 -> 15 FILLER X(03)
    public AlphanumericVar getFiller10() {
        return this.plhcMktCompanyT.getFiller10();
    }

    public void setFiller10(final String filler10) {
        this.plhcMktCompanyT.setFiller10(filler10);
    }

    public void setFiller10(final IAlphanumericValue filler10) {
        this.plhcMktCompanyT.setFiller10(filler10);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhcMktRecordIdt.setValue("TRAIL");
        this.plhcMktCompanyT.initialize();
        this.plhcMktRecordCount.setZero();
        this.filler11.setValue("");
        this.plhcMktSortIndt.setValue("");
        this.filler12.setValue("");
        this.plhcMktEndrect.setValue("X");
    }

}

