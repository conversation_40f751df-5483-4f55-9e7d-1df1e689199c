package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTAGC0-NULL.
 *
 */
// 618035000*                                                                 03500002
// 619035100*                                                                 03510002
// 620035200*                                                                 03520002
@Setter
@Getter
public class WsGfvtagc0Null { // VStruct
    // Properties
    private int agc01;                                                      // 03 AGC01 S9(4) COMPUTATIONAL
    private int agc02;                                                      // 03 AGC02 S9(4) COMPUTATIONAL
    private int agc03;                                                      // 03 AGC03 S9(4) COMPUTATIONAL
    private int agc04;                                                      // 03 AGC04 S9(4) COMPUTATIONAL
    private int agc05;                                                      // 03 AGC05 S9(4) COMPUTATIONAL
    private int agc06;                                                      // 03 AGC06 S9(4) COMPUTATIONAL
    private int agc07;                                                      // 03 AGC07 S9(4) COMPUTATIONAL
    private int agc08;                                                      // 03 AGC08 S9(4) COMPUTATIONAL
    private int agc09;                                                      // 03 AGC09 S9(4) COMPUTATIONAL
    private int agc10;                                                      // 03 AGC10 S9(4) COMPUTATIONAL
    private int agc11;                                                      // 03 AGC11 S9(4) COMPUTATIONAL
    private int agc12;                                                      // 03 AGC12 S9(4) COMPUTATIONAL
    private int agc13;                                                      // 03 AGC13 S9(4) COMPUTATIONAL
    private int agc14;                                                      // 03 AGC14 S9(4) COMPUTATIONAL
    private int agc15;                                                      // 03 AGC15 S9(4) COMPUTATIONAL
    private int agc16;                                                      // 03 AGC16 S9(4) COMPUTATIONAL
    private int agc17;                                                      // 03 AGC17 S9(4) COMPUTATIONAL
    private int agc18;                                                      // 03 AGC18 S9(4) COMPUTATIONAL
    private int agc19;                                                      // 03 AGC19 S9(4) COMPUTATIONAL
    private int agc20;                                                      // 03 AGC20 S9(4) COMPUTATIONAL
    private int agc21;                                                      // 03 AGC21 S9(4) COMPUTATIONAL
    private int agc22;                                                      // 03 AGC22 S9(4) COMPUTATIONAL
    private int agc23;                                                      // 03 AGC23 S9(4) COMPUTATIONAL
    private int agc24;                                                      // 03 AGC24 S9(4) COMPUTATIONAL
    private int agc25;                                                      // 03 AGC25 S9(4) COMPUTATIONAL
    private int agc26;                                                      // 03 AGC26 S9(4) COMPUTATIONAL
    private int agc27;                                                      // 03 AGC27 S9(4) COMPUTATIONAL
    private int agc28;                                                      // 03 AGC28 S9(4) COMPUTATIONAL

    public WsGfvtagc0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.agc01 = 0;
        this.agc02 = 0;
        this.agc03 = 0;
        this.agc04 = 0;
        this.agc05 = 0;
        this.agc06 = 0;
        this.agc07 = 0;
        this.agc08 = 0;
        this.agc09 = 0;
        this.agc10 = 0;
        this.agc11 = 0;
        this.agc12 = 0;
        this.agc13 = 0;
        this.agc14 = 0;
        this.agc15 = 0;
        this.agc16 = 0;
        this.agc17 = 0;
        this.agc18 = 0;
        this.agc19 = 0;
        this.agc20 = 0;
        this.agc21 = 0;
        this.agc22 = 0;
        this.agc23 = 0;
        this.agc24 = 0;
        this.agc25 = 0;
        this.agc26 = 0;
        this.agc27 = 0;
        this.agc28 = 0;
    }

}

