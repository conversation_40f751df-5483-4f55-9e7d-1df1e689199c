package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-REGISTROS.
 *
 */
// 777050900*                                                                 05090002
// 778051000*                                                                 05100002
@Setter
@Getter
public class WsRegistros { // VStruct
    // Properties
    private String wsDclgfvtfla00 = "";                                     // 03 WS-DCLGFVTFLA00 X(100)
    private String wsDclgfvtfla01 = "";                                     // 03 WS-DCLGFVTFLA01 X(100)
    private String wsDclgfvtfla02 = "";                                     // 03 WS-DCLGFVTFLA02 X(100)
    private String wsDclgfvtcna00 = "";                                     // 03 WS-DCLGFVTCNA00 X(178)
    private String wsDclgfvtcna01 = "";                                     // 03 WS-DCLGFVTCNA01 X(178)
    private int wsIndNal = 0;                                               // 03 WS-IND-NAL 9
    private int wsIndProv = 0;                                              // 03 WS-IND-PROV 9

    public WsRegistros() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsDclgfvtfla00 = "";
        this.wsDclgfvtfla01 = "";
        this.wsDclgfvtfla02 = "";
        this.wsDclgfvtcna00 = "";
        this.wsDclgfvtcna01 = "";
        this.wsIndNal = 0;
        this.wsIndProv = 0;
    }

}

