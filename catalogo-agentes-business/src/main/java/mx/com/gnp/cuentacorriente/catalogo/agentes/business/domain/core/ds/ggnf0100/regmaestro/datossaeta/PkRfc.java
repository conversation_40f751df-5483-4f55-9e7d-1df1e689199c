package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PK-RFC.
 *
 */
// 78004600*=>     0001-00007                                                00460000
@Setter
@Getter
public class PkRfc extends DataStruct {
    // Properties
    private AlphanumericVar pkRfc1 = new AlphanumericVar(this, 13);          // 07 PK-RFC1 X(13)
    private AlphanumericVar pkRfc2 = new AlphanumericVar(this, 2);           // 07 PK-RFC2 XX

    public PkRfc() {
        super();
        initialize();
    }

    public PkRfc(DataContainer parent) {
        super(parent);
    }

    public PkRfc(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkRfc1(final String pkRfc1) {
        this.pkRfc1.setValue(pkRfc1);
    }

    public void setPkRfc1(final IAlphanumericValue pkRfc1) {
        this.pkRfc1.setValue(pkRfc1);
    }

    public void setPkRfc2(final String pkRfc2) {
        this.pkRfc2.setValue(pkRfc2);
    }

    public void setPkRfc2(final IAlphanumericValue pkRfc2) {
        this.pkRfc2.setValue(pkRfc2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkRfc1.clear();
        this.pkRfc2.clear();
    }

}

