package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablameses.TablaMesR;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-MESES.
 *
 */
// 424      *---------------------------------------------------------------* 
// 425      * TABLA DE DIA DE MESES                                         * 
// 426      *---------------------------------------------------------------* 
@Setter
@Getter
public class TablaMeses { // VStruct
    // Properties
    private AlphanumericVar tablaMes = new AlphanumericVar(24);              // 05 TABLA-MES X(24) REDEFINED BY TABLA-MES-R
    private TablaMesR tablaMesR = new  TablaMesR(null, this.tablaMes, 12);   // 05 TABLA-MES-R REDEFINES TABLA-MES [12]

    public TablaMeses() {
        initialize();
    }



    public void setTablaMes(final String tablaMes) {
        this.tablaMes.setValue(tablaMes);
    }

    public void setTablaMes(final IAlphanumericValue tablaMes) {
        this.tablaMes.setValue(tablaMes);
    }

    public TablaMesR getTablaMesR(int index) {
        return this.tablaMesR.at(index);
    }


    // Deeper properties

    // Property: tablaMesR.tbDia -> 07 TB-DIA 9(02)
    public UnsignedNumericVar getTbDia() {
        return this.tablaMesR.getTbDia();
    }

    public void setTbDia(final int tbDia) {
        this.tablaMesR.setTbDia(tbDia);
    }

    public void setTbDia(final INumericValue tbDia) {
        this.tablaMesR.setTbDia(tbDia);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tablaMes.setValue("312831303130313130313031");
    }

}

