package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-VARIABLES-ALFA.
 *
 */
@Setter
@Getter
public class WsVariablesAlfa { // VStruct
    // Properties
    private String wsTabla = "";                                            // 03 WS-TABLA X(08)
    private String wsArchivo = "";                                          // 03 WS-ARCHIVO X(30)
    private String wsAccion = "";                                           // 03 WS-ACCION X(05)
    private String wsUniNeg = "";                                           // 03 WS-UNI-NEG X(05)
    private String wsTrnCveAnt = "";                                        // 03 WS-TRN-CVE-ANT X(10)
    private String wsAgtNalCve = "";                                        // 03 WS-AGT-NAL-CVE X(03)
    // Level 88 - Conditional names
    private static final String NACIONAL = "MEX";
    private static final String EXTRANJERO = "EXT";


    public WsVariablesAlfa() {
        initialize();
    }



    // Conditionals (88) - WS-AGT-NAL-CVE
    public boolean isNacional() {
        return this.wsAgtNalCve.equals(NACIONAL);
    }

    public void setNacional() {
        this.wsAgtNalCve = NACIONAL;
    }

    public boolean isExtranjero() {
        return this.wsAgtNalCve.equals(EXTRANJERO);
    }

    public void setExtranjero() {
        this.wsAgtNalCve = EXTRANJERO;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsTabla = "";
        this.wsArchivo = "";
        this.wsAccion = "";
        this.wsUniNeg = "";
        this.wsTrnCveAnt = "";
        this.wsAgtNalCve = "";
    }

}

