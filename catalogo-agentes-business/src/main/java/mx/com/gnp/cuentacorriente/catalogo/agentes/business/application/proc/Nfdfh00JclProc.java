/**
 *  JCL Program: NFDFH00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Nfdfh00JclProc extends AbstractJclProgram {

    public Nfdfh00JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("NFDFH00", (nfdfh00) -> {
            nfdfh00.addParameter("PROC");
            if (nfdfh00.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*                 * * *    NFDFH00    * * **/
                /*//*   APLICACION:  SAETA 2000 CANALES*/
                /*//*   PROCESO:*/
                /*//*   OBJETIVO:    GENERA UNA EXTRACCION DE DATOS DE MEDICA PARA INFO*/
                /*//*   COMENTARIOS:*/
                /*//*   CORRE*/
                /*//*   DESPUES DE:  PGAQADCW  REPLICADOR DE CUENTA CORRIENTES*/
                /*//*   ANTES   DE:  KCDDDMT4  GENERA ARCHIVOS META 4 DE INFO*/
                /*//*   ELABORO:    JOSE LUIS CRUZ CRUZ               FECHA: 12.ENE.2009*/
                /*//**********************************************************************/
                /*//*                        * NFDFH03 **/
                /*//* OBJETIVO DEL STEP : GENERA LA EXTRACCION DE LOS AGENTES DE MEDICA*/
                /*//* PASO REINICIBLE*/
                /*//**********************************************************************/
                nfdfh00.execProgram("NFDFH03", "IKJEFT01", (nfdfh03) -> {
                    nfdfh03.addParameter("REGION","0M");
                    nfdfh03.addDataDefinition("STEPLIB",  (steplib) -> {
                        steplib.addDataDefinition("STEPLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                            _concat.addParameter("DISP","SHR");
                        });
                        steplib.addDataDefinition("STEPLIB", "SYS6.DB2.RUNLIB.LOAD",  (_concat) -> {
                            _concat.addParameter("DISP","SHR");
                        });
                    });
                    nfdfh03.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    nfdfh03.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    nfdfh03.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    nfdfh03.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    /*//*           ARCHIVO DE DESCARGA DE TABLA GCQTCED0*/
                    nfdfh03.addDataDefinition("SYSREC00", "PGA.EANFDFH0",  (sysrec00) -> {
                        sysrec00.addParameter("DISP", "(NEW,CATLG,CATLG)");
                        sysrec00.addParameter("SPACE", "(CYL,(500,250),RLSE)");
                        sysrec00.addParameter("UNIT","TEMPROD");
                        sysrec00.addParameter("DCB", "(DSORG=PS,RECFM=FB,LRECL=00000,BLKSIZE=00000)");
                    });
                    nfdfh03.addDataDefinition("SYSPUNCH",  (syspunch) -> {
                        syspunch.addParameter("SYSOUT","*");
                    });
                    nfdfh03.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(NFDFH01)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                    nfdfh03.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDFH02)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (nfdfh00.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//*                        * NFDFH02 **/
                /*//* OBJETIVO DEL STEP : RESPALDAR LOS ARCHIVOS GENERADOS EN EL PROCESO.*/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                nfdfh00.execProgram("NFDFH02", "IEBGENER", (nfdfh02) -> {
                    nfdfh02.addDataDefinition("SYSUT1", "PGA.EANFDFH0",  (sysut1) -> {
                        sysut1.addParameter("DISP","SHR");
                    });
                    nfdfh02.addDataDefinition("SYSUT2", "PRCDS.CDDMT4.INTRMS.KD890MDK",  (sysut2) -> {
                        sysut2.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sysut2.addParameter("UNIT","SHRAPL");
                        sysut2.addParameter("SPACE", "(CYL,(81,81),RLSE)");
                        sysut2.addParameter("DCB", "(RECFM=FB,LRECL=333,BLKSIZE=27972,BUFNO=4,DSORG=PS)");
                    });
                    nfdfh02.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    nfdfh02.addDummyDataDefinition("SYSIN",  (sysin) -> {
                    });
                });
            }
            /*//**********************************************************************/
            /*//*                      * *  NFDFH01  * **/
            /*//* OBJETIVO DEL STEP : BORRAR ARCHIVO DEL PROCESO ANTERIOR*/
            /*//* PASO REINICIABLE:*/
            /*//**********************************************************************/
            nfdfh00.execProgram("NFDFH01", "IDCAMS", (nfdfh01) -> {
                nfdfh01.addParameter("REGION","0M");
                nfdfh01.addDataDefinition("SYSPRINT",  (sysprint) -> {
                    sysprint.addParameter("SYSOUT","X");
                });
                nfdfh01.addDataDefinition("SYSOUT",  (sysout) -> {
                    sysout.addParameter("SYSOUT","*");
                });
                nfdfh01.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(NFDFH04)",  (sysin) -> {
                    sysin.addParameter("DISP","SHR");
                });
            });
        });
    }
}
