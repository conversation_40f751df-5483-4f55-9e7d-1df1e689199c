package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTCNA0-NULL00.
 *
 */
// 942047700*                                                                 04770000
// 943047800*                                                                 04780000
// 944047900*                                                                 04790000
// 945048000*                                                                 04800000
@Setter
@Getter
public class WsGfvtcna0Null00 extends DataStruct {
    // Properties
    private BinaryVarArray cna0100 = new BinaryVarArray(this, 36, 4, 0);     // 03 CNA0100 S9(4) [36] COMPUTATIONAL

    public WsGfvtcna0Null00() {
        super();
        initialize();
    }

    public WsGfvtcna0Null00(DataContainer parent) {
        super(parent);
    }

    public WsGfvtcna0Null00(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public BinaryVar getCna0100(int index) {
        return this.cna0100.at(index);
    }

    public void setCna0100(final int cna0100) {
        this.cna0100.setValue(cna0100);
    }

    public void setCna0100(final BigDecimal cna0100) {
        this.cna0100.setValue(cna0100);
    }

    public void setCna0100(final BinaryVarArray cna0100) {
        this.cna0100.setValue(cna0100);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.cna0100.clear();
    }

}

