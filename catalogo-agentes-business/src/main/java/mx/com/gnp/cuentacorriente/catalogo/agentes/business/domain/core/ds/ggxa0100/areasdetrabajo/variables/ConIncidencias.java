package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo.variables;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: CON-INCIDENCIAS.
 *
 */
@Setter
@Getter
public class ConIncidencias { // VStruct
    // Properties
    private String conMensaje = "";                                         // 15 CON-MENSAJE X(40)

    public ConIncidencias() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.conMensaje = "";
    }

}

