package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfla1;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-FLA0-ELEM.
 *
 */
@Setter
@Getter
public class TFla0Elem extends DataStruct {
    // Properties
    private AlphanumericVar sFlaFlaNum1 = new AlphanumericVar(this, 8);      // 20 S-FLA-FLA-NUM1 X(08)
    private AlphanumericVar sFlaLimitad1 = new AlphanumericVar(this, 1);     // 20 S-FLA-LIMITAD1 X(01)

    public TFla0Elem() {
        super();
        initialize();
    }

    public TFla0Elem(DataContainer parent) {
        super(parent);
    }

    public TFla0Elem(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSFlaFlaNum1(final String sFlaFlaNum1) {
        this.sFlaFlaNum1.setValue(sFlaFlaNum1);
    }

    public void setSFlaFlaNum1(final IAlphanumericValue sFlaFlaNum1) {
        this.sFlaFlaNum1.setValue(sFlaFlaNum1);
    }

    public void setSFlaLimitad1(final String sFlaLimitad1) {
        this.sFlaLimitad1.setValue(sFlaLimitad1);
    }

    public void setSFlaLimitad1(final IAlphanumericValue sFlaLimitad1) {
        this.sFlaLimitad1.setValue(sFlaLimitad1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.sFlaFlaNum1.clear();
        this.sFlaLimitad1.clear();
    }

}

