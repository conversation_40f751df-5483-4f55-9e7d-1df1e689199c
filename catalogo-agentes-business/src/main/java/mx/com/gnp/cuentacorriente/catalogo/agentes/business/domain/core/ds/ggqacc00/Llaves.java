package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: LLAVES.
 *
 */
// 179      *--------------------------------------------------------------*  
// 180      * LLAVES                                                       *  
// 181      *--------------------------------------------------------------*  
@Setter
@Getter
public class Llaves { // VStruct
    // Properties
    private String wsEmpCve = "";                                           // 03 WS-EMP-CVE X(05)
    private int wsCnaNum = 0;                                               // 03 WS-CNA-NUM 9(04)
    private String wsConcepto = "";                                         // 03 WS-CONCEPTO X(04)
    private String wsCdoper = "";                                           // 03 WS-CDOPER X(10)
    private String wsOpcion = "";                                           // 03 WS-OPCION X(01)
    private int wsAgtIdr = 0;                                               // 03 WS-AGT-IDR S9(09) COMPUTATIONAL
    private UnsignedNumericVar wsAgtIdrAux = new UnsignedNumericVar(10, 0);  // 03 WS-AGT-IDR-AUX 9(10)
    private int wsAgtIdrAnt = 0;                                            // 03 WS-AGT-IDR-ANT S9(09) COMPUTATIONAL
    private String wsEmpCveAnt = "";                                        // 03 WS-EMP-CVE-ANT X(05)
    private int wsCnaNumAnt = 0;                                            // 03 WS-CNA-NUM-ANT S9(04) COMPUTATIONAL
    private String wsCfbCveDseCmpAnt = "";                                  // 03 WS-CFB-CVE-DSE-CMP-ANT X(04)
    private UnsignedNumericVar wsCf5PjeCtoDseAnt = new UnsignedNumericVar(5, 2);             // 03 WS-CF5-PJE-CTO-DSE-ANT 9(03)V99

    public Llaves() {
        initialize();
    }



    public void setWsAgtIdrAux(final BigDecimal wsAgtIdrAux) {
        this.wsAgtIdrAux.setValue(wsAgtIdrAux);
    }

    public void setWsAgtIdrAux(final int wsAgtIdrAux) {
        this.wsAgtIdrAux.setValue(wsAgtIdrAux);
    }

    public void setWsAgtIdrAux(final INumericValue wsAgtIdrAux) {
        this.wsAgtIdrAux.setValue(wsAgtIdrAux);
    }

    public void setWsCf5PjeCtoDseAnt(final BigDecimal wsCf5PjeCtoDseAnt) {
        this.wsCf5PjeCtoDseAnt.setValue(wsCf5PjeCtoDseAnt);
    }

    public void setWsCf5PjeCtoDseAnt(final int wsCf5PjeCtoDseAnt) {
        this.wsCf5PjeCtoDseAnt.setValue(wsCf5PjeCtoDseAnt);
    }

    public void setWsCf5PjeCtoDseAnt(final INumericValue wsCf5PjeCtoDseAnt) {
        this.wsCf5PjeCtoDseAnt.setValue(wsCf5PjeCtoDseAnt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsEmpCve = "";
        this.wsCnaNum = 0;
        this.wsConcepto = "";
        this.wsCdoper = "";
        this.wsOpcion = "";
        this.wsAgtIdr = 0;
        this.wsAgtIdrAux.setZero();
        this.wsAgtIdrAnt = 0;
        this.wsEmpCveAnt = "";
        this.wsCnaNumAnt = 0;
        this.wsCfbCveDseCmpAnt = "";
        this.wsCf5PjeCtoDseAnt.setZero();
    }

}

