package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wwauxiliares;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WT-CNA-GER-ZON.
 *
 */
@Setter
@Getter
public class WtCnaGerZon extends DataStruct {
    // Properties
    private AlphanumericVarArray wtCnaGerZonA = new AlphanumericVarArray(this, 4, 1);        // 15 WT-CNA-GER-ZON-A X(01) [4]

    public WtCnaGerZon() {
        super();
        initialize();
    }

    public WtCnaGerZon(DataContainer parent) {
        super(parent);
    }

    public WtCnaGerZon(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWtCnaGerZonA(int index) {
        return this.wtCnaGerZonA.at(index);
    }

    public void setWtCnaGerZonA(final String wtCnaGerZonA) {
        this.wtCnaGerZonA.setValue(wtCnaGerZonA);
    }

    public void setWtCnaGerZonA(final AlphanumericVarArray wtCnaGerZonA) {
        this.wtCnaGerZonA.setValue(wtCnaGerZonA);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wtCnaGerZonA.clear();
    }

}

