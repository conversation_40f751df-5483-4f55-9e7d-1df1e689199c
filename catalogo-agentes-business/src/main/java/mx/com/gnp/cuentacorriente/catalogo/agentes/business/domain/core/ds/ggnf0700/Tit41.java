package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TIT41.
 *
 */
// 703043500*        '                                            '.          04350002
// 704043600                                                                  04360002
@Setter
@Getter
public class Tit41 extends DataStruct {
    // Properties
    private AlphanumericVar filler1 = new AlphanumericVar(this, 46);         // 03 FILLER X(46)
    private AlphanumericVar filler2 = new AlphanumericVar(this, 54);         // 03 FILLER X(54)
    private AlphanumericVar filler3 = new AlphanumericVar(this, 44);         // 03 FILLER X(44)

    public Tit41() {
        super();
        initialize();
    }

    public Tit41(DataContainer parent) {
        super(parent);
    }

    public Tit41(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller2(final String filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.filler2.setValue(filler2);
    }

    public void setFiller3(final String filler3) {
        this.filler3.setValue(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.filler3.setValue(filler3);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.filler1.setValue("FLA-DES                                    TRA");
        this.filler2.setValue("-ESP    FEC-CNX      GNA-CVE  ESA-CVE-FLA  FLA-GER-ZON");
        this.filler3.setValue("  OFN-CVE-CNA  AGT-IDR-GTE                  ");
    }

}

