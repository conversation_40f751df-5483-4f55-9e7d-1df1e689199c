package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac700.banderas.BanderasGlobales;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: BANDERAS.
 *
 */
// 381      *----                                                             
// 382      *    BANDERAS                                                     
// 383      *----                                                             
@Setter
@Getter
public class Banderas { // VStruct
    // Properties
    private BanderasGlobales banderasGlobales = new  BanderasGlobales();     // 02 BANDERAS-GLOBALES

    public Banderas() {
        initialize();
    }




    // Deeper properties

    // Property: banderasGlobales.stCatDirec -> 03 ST-CAT-DIREC X(02)
    public String getStCatDirec() {
        return this.banderasGlobales.getStCatDirec();
    }

    public void setStCatDirec(final String stCatDirec) {
        this.banderasGlobales.setStCatDirec(stCatDirec);
    }

    // Property: banderasGlobales.wsFinCatDirecc -> 03 WS-FIN-CAT-DIRECC 9(01)
    public int getWsFinCatDirecc() {
        return this.banderasGlobales.getWsFinCatDirecc();
    }

    public void setWsFinCatDirecc(final int wsFinCatDirecc) {
        this.banderasGlobales.setWsFinCatDirecc(wsFinCatDirecc);
    }

    // Property: banderasGlobales : condition FIN-CAT-DIRECC
    public boolean isFinCatDirecc() {
        return this.banderasGlobales.isFinCatDirecc();
    }

    public void setFinCatDirecc() {
        this.banderasGlobales.setFinCatDirecc();
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.banderasGlobales.initialize();
    }

}

