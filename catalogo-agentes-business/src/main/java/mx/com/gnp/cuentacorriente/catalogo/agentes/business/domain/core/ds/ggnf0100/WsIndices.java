package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-INDICES.
 *
 */
// 1137067200*                                                                 06720000
// 1138067300*                                                                 06730000
// 1139067400*                                                                 06740000
@Setter
@Getter
public class WsIndices { // VStruct
    // Properties
    private int i = 0;                                                      // 05 I 99
    private int j = 0;                                                      // 05 J 99
    private int k = 0;                                                      // 05 K 99
    private int l = 0;                                                      // 05 L 99

    public WsIndices() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.i = 0;
        this.j = 0;
        this.k = 0;
        this.l = 0;
    }

}

