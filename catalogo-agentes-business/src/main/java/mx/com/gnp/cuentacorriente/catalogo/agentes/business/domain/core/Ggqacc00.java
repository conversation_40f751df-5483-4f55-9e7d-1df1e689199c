
/**
 *  Program: Ggqacc00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;

import com.base100.caravel.support.common.cbl.context.Debug;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.functions.Functions;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagt0cpy.Dclgfvtagt0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtpra0cpy.Dclgfvtpra0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcma0cpy.Dclgfvtcma0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctpai0cpy.Dclgcctpai0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa001lcpy.LgralLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa001lcpy.LgralLinkReg;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcma0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtesa0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtmte0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtpra0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctedo0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctpob0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctmnd0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctpai0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdma0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcma0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtesa0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtmte0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtpra0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctedo0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctpob0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctmnd0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagt0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctpai0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdma0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqacc00 extends AbstractCobolOs390Program implements IProgram {

	// Level 1
//	private RpParametro rpParametro= new  RpParametro();                    // 01 RP-PARAMETRO//[@WARNING variable NOT USED]
//	private RpReg rpReg= new  RpReg();                                      // 01 RP-REG//[@WARNING variable NOT USED]
	private Constantes constantes= new  Constantes();                       // 01 CONSTANTES
	private Llaves llaves= new  Llaves();                                   // 01 LLAVES
	private Indices indices= new  Indices();                                // 01 INDICES
	private Banderas banderas= new  Banderas();                             // 01 BANDERAS
//	private String s500Err = "";                                            // 01 S500-ERR X(01)//[@WARNING variable NOT USED]
	private WsVariablesNume wsVariablesNume= new  WsVariablesNume();        // 01 WS-VARIABLES-NUME
//	private WsVariablesAlfa wsVariablesAlfa= new  WsVariablesAlfa();        // 01 WS-VARIABLES-ALFA//[@WARNING variable NOT USED]
	private WsContadores wsContadores= new  WsContadores();                 // 01 WS-CONTADORES
//	private WsAcumuladores wsAcumuladores= new  WsAcumuladores();           // 01 WS-ACUMULADORES//[@WARNING variable NOT USED]
//	private WsDisplay wsDisplay= new  WsDisplay();                          // 01 WS-DISPLAY//[@WARNING variable NOT USED]
//	private WsDatosParam wsDatosParam= new  WsDatosParam();                 // 01 WS-DATOS-PARAM//[@WARNING variable NOT USED]
//	private WsFechas wsFechas= new  WsFechas();                             // 01 WS-FECHAS//[@WARNING variable NOT USED]
	private IndicadoresDeNulos indicadoresDeNulos= new  IndicadoresDeNulos();               // 01 INDICADORES-DE-NULOS
	private String wsFecCurr = "";                                          // 01 WS-FEC-CURR X(10)
//	private String wNulls = "01/01/0001";                                   // 01 W-NULLS X(10)//[@WARNING variable NOT USED]
	private FechaFormatI fechaFormatI= new  FechaFormatI();                 // 01 FECHA-FORMAT-I UsedAsParameter
	private FechaFormatIi fechaFormatIi= new  FechaFormatIi();              // 01 FECHA-FORMAT-II UsedAsParameter
//	private FechaFormatIii fechaFormatIii= new  FechaFormatIii();           // 01 FECHA-FORMAT-III//[@WARNING variable NOT USED]
//	private TablaMeses tablaMeses= new  TablaMeses();                       // 01 TABLA-MESES//[@WARNING variable NOT USED]
	private Tablas tablas= new  Tablas();                                   // 01 TABLAS//[@WARNING variable NOT USED]
	private TablaEstatus tablaEstatus= new  TablaEstatus();                 // 01 TABLA-ESTATUS
//	private TablaCatEsa tablaCatEsa= new  TablaCatEsa();                    // 01 TABLA-CAT-ESA//[@WARNING variable NOT USED]
//	private TablaDirecciones tablaDirecciones= new  TablaDirecciones();     // 01 TABLA-DIRECCIONES//[@WARNING variable NOT USED]
//	private WsVariablesDireccion wsVariablesDireccion= new  WsVariablesDireccion();         // 01 WS-VARIABLES-DIRECCION//[@WARNING variable NOT USED]
	private NumericEditedVar codeError= new NumericEditedVar("------999");  // 01 CODE-ERROR ------999
	private String msgRutina = "";                                          // 01 MSG-RUTINA X(20)
	private String msgMotivo = "";                                          // 01 MSG-MOTIVO X(40)

    // Copy file QA$CBLCPY$GGQA00GY
//  private Ccta_09Reg ccta_09Reg = new  Ccta_09Reg();                           // 01 CCTA-09-REG // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA00GY

    // Copy file QA$CBLCPY$GGQA00JW
//  private Ccta_99Reg ccta_99Reg = new  Ccta_99Reg();                           // 01 CCTA-99-REG // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA00JW

    // Level 1
//  private String vhTrnCve = "";                                           // 01 VH-TRN-CVE X(10) // [@WARNING variable NOT USED]
//  private String vhTrnCveCod = "";                                        // 01 VH-TRN-CVE-COD X(02) // [@WARNING variable NOT USED]
//  private W530Rutina w530Rutina = new  W530Rutina();                           // 01 W530-RUTINA // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GGQA006C
//  private Lcf1LinkReg lcf1LinkReg = new  Lcf1LinkReg();                        // 01 LCF1-LINK-REG // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA006C

    // Copy file QA$CBLCPY$GGQA006D
//  private Lcf5LinkReg lcf5LinkReg = new  Lcf5LinkReg();                        // 01 LCF5-LINK-REG // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GGQA006D

    // Level 1
    private WsDclgfvtcna0 wsDclgfvtcna0 = new  WsDclgfvtcna0();              // 01 WS-DCLGFVTCNA0
//  private WsDclgfvtesa0 wsDclgfvtesa0 = new  WsDclgfvtesa0();                  // 01 WS-DCLGFVTESA0 // [@WARNING variable NOT USED]
    private WsDclgfvtagt0 wsDclgfvtagt0 = new  WsDclgfvtagt0();              // 01 WS-DCLGFVTAGT0
    private WsDclgfvtdma0 wsDclgfvtdma0 = new  WsDclgfvtdma0();              // 01 WS-DCLGFVTDMA0
//  private WsDclgfvttdm0 wsDclgfvttdm0 = new  WsDclgfvttdm0();                  // 01 WS-DCLGFVTTDM0 // [@WARNING variable NOT USED]
    private WsDclgcctpob0 wsDclgcctpob0 = new  WsDclgcctpob0();              // 01 WS-DCLGCCTPOB0
    private WsDclgcctmnd0 wsDclgcctmnd0 = new  WsDclgcctmnd0();              // 01 WS-DCLGCCTMND0
    private WsDclgcctedo0 wsDclgcctedo0 = new  WsDclgcctedo0();              // 01 WS-DCLGCCTEDO0
    private WsDclgfvtmte0 wsDclgfvtmte0 = new  WsDclgfvtmte0();              // 01 WS-DCLGFVTMTE0
//  private WsDclgfvtpai0 wsDclgfvtpai0 = new  WsDclgfvtpai0();                  // 01 WS-DCLGFVTPAI0 // [@WARNING variable NOT USED]

    // Copy file QA$CBLCPY$GFVTDSC0
//  private Dclgfvtdsc0 dclgfvtdsc0 = new  Dclgfvtdsc0();                        // 01 DCLGFVTDSC0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTDSC0

    // Copy file QA$CBLCPY$GFVTMTE0
//  private Dclgfvtmte0 dclgfvtmte0 = new  Dclgfvtmte0();                        // 01 DCLGFVTMTE0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTMTE0

    // Copy file QA$CBLCPY$GFVTESA0
//  private Dclgfvtesa0 dclgfvtesa0 = new  Dclgfvtesa0();                        // 01 DCLGFVTESA0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTESA0

    // Copy file QA$CBLCPY$GCQTCCO0
//  private Dclgcqtcco0 dclgcqtcco0 = new  Dclgcqtcco0();                        // 01 DCLGCQTCCO0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GCQTCCO0

    // Copy file QA$CBLCPY$GFVTAGT0
    private Dclgfvtagt0 dclgfvtagt0 = new  Dclgfvtagt0();                    // 01 DCLGFVTAGT0
    // End copy file QA$CBLCPY$GFVTAGT0

    // Copy file QA$CBLCPY$GFVTPRA0
    private Dclgfvtpra0 dclgfvtpra0 = new  Dclgfvtpra0();                    // 01 DCLGFVTPRA0
    // End copy file QA$CBLCPY$GFVTPRA0

    // Copy file QA$CBLCPY$GFVTDMA0
//  private Gfvtdma0 gfvtdma0 = new  Gfvtdma0();                                 // 01 GFVTDMA0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GFVTDMA0

    // Copy file QA$CBLCPY$GCCTPOB0
//  private Dclgcctpob0 dclgcctpob0 = new  Dclgcctpob0();                        // 01 DCLGCCTPOB0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GCCTPOB0

    // Copy file QA$CBLCPY$GCCTMND0
//  private Dclgcctmnd0 dclgcctmnd0 = new  Dclgcctmnd0();                        // 01 DCLGCCTMND0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GCCTMND0

    // Copy file QA$CBLCPY$GFVTCMA0
    private Dclgfvtcma0 dclgfvtcma0 = new  Dclgfvtcma0();                    // 01 DCLGFVTCMA0
    // End copy file QA$CBLCPY$GFVTCMA0

    // Copy file QA$CBLCPY$GCCTEDO0
//  private Dclgcctedo0 dclgcctedo0 = new  Dclgcctedo0();                        // 01 DCLGCCTEDO0 // [@WARNING variable NOT USED]
    // End copy file QA$CBLCPY$GCCTEDO0

    // Copy file QA$CBLCPY$GCCTPAI0
    private Dclgcctpai0 dclgcctpai0 = new  Dclgcctpai0();                    // 01 DCLGCCTPAI0
    // End copy file QA$CBLCPY$GCCTPAI0

    // Copy file QA$CBLCPY$GGQA001L
    private LgralLinkReg lgralLinkReg = new  LgralLinkReg();                 // 01 LGRAL-LINK-REG UsedAsParameter

    private Sqlca sqlca;


    // Declare Sql Ports
    private IGfvtcma0Port gfvtcma0Port;
    private IGfvtesa0Port gfvtesa0Port;
    private IGfvtmte0Port gfvtmte0Port;
    private IGfvtpra0Port gfvtpra0Port;
    private IGcctedo0Port gcctedo0Port;
    private IGcctpob0Port gcctpob0Port;
    private IGcctmnd0Port gcctmnd0Port;
    private IGfvtagt0Port gfvtagt0Port;
    private IGcctpai0Port gcctpai0Port;
    private IGfvtdma0Port gfvtdma0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtcma0Model> gfvtcma0Model;
    private Optional<Gfvtesa0Model> gfvtesa0Model;
    private Optional<Gfvtmte0Model> gfvtmte0Model;
    private Optional<Gfvtpra0Model> gfvtpra0Model;
    private Optional<Gcctedo0Model> gcctedo0Model;
    private Optional<Gcctpob0Model> gcctpob0Model;
    private Optional<Gcctmnd0Model> gcctmnd0Model;
    private Optional<Gfvtagt0Model> gfvtagt0Model;
    private Optional<Gcctpai0Model> gcctpai0Model;
    private Optional<Gfvtdma0Model> gfvtdma0Model;

    // Declare Sql Cursors
    private ICursor<Gfvtmte0Model> curMte0Cursor;
    private ICursor<Gfvtesa0Model> curEsa0Cursor;
    private ICursor<Gfvtdma0Model> curDma0Cursor;

    public Ggqacc00(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initialize();
    }

    // Initial values
    @Override
    public void initialize() {
        this.indices.initialize();
        this.wsVariablesNume.initialize();
//        this.wsVariablesAlfa.initialize(); // [@WARNING variable NOT USED]
        this.wsContadores.initialize();
//        this.wsAcumuladores.initialize(); // [@WARNING variable NOT USED]
//        this.wNulls = "01/01/0001"; // [@WARNING variable NOT USED]
//        this.wsVariablesDireccion.initialize(); // [@WARNING variable NOT USED]
    }


    /**
     *  Main execution entry point of the program.
     */
    public void run(AlphanumericVar lgralLinkReg) {
        this.lgralLinkReg.setValue(lgralLinkReg);
        try {
            run();
        } finally {
            lgralLinkReg.setValue(this.lgralLinkReg);
        }
    }

    @Override
    public void run(Object... args) {
        assignInputArgument(args, 0, this.lgralLinkReg);
        try {
            run();
        } finally {
            updateOutputArgument(args, 0, this.lgralLinkReg);
        }
    }

    public void run() {
        sqlDelayedParagraph();
        p000Global();
    }

    /**
     *  Section: DEBUG-DECLARATIVES
     *  Paragraph: DEBUG-DECLARATIVES-PARAGRAPH.
     */
    void debugDeclaratives() {
        display(Debug.getDebugName());
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(curMte0Cursor == null) {
            curMte0Cursor = gfvtmte0Port.createCursorCur_mte0(
                () -> null);
        }
        if(curEsa0Cursor == null) {
            curEsa0Cursor = gfvtesa0Port.createCursorCur_esa0(
                () -> null);
        }
        if(curDma0Cursor == null) {
            curDma0Cursor = gfvtdma0Port.createCursorCur_dma0(
                () -> Gfvtdma0Model.builder()
                    .agtIdr(llaves.getWsAgtIdr())
                .build());
        }
        //
        // EXIT-SQL-DELAYED-PARAGRAPH - Last paragraph
        //
    }

    /**
     *  Paragraph: 000-GLOBAL.
     */
    void p000Global() {
        p1000Inicio();
        p3000Proceso();
        p9000Final();
        //
        // 000-GLOBAL-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 1000-INICIO.
     *
     */
    void p1000Inicio() {
        // ====================*
        fechaFormatI.setValue(Functions.substring(Functions.currentDateString(getOs390Context()), 1, 8));
        fechaFormatIi.getFechaFormatIiAa().setValue(fechaFormatI.substring(1, 4));
        fechaFormatIi.getFechaFormatIiMm().setValue(fechaFormatI.substring(5, 2));
        fechaFormatIi.getFechaFormatIiDd().setValue(fechaFormatI.substring(7, 2));
        wsFecCurr = fechaFormatIi.toStringValue();

        p1100MoveVarLinkHost();
        p1100Estatus();
        tablas.getTablaImptes().initialize();
        //
        // 1000-INICIO-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 1100-MOVE-VAR-LINK-HOST.
     *
     */
    void p1100MoveVarLinkHost() {
        // ====================*
        llaves.setWsCdoper(lgralLinkReg.getLgralCdoper().toStringValue());

        llaves.setWsAgtIdr(lgralLinkReg.getLgralAgtIdr().toInt());

        llaves.setWsAgtIdrAux(lgralLinkReg.getLgralAgtIdr());
        //
        // 1100-MOVE-VAR-LINK-HOST-EXIT - Last paragraph
        //
    }

    /**
     *  Paragraph: 1100-ESTATUS.
     *
     */
    void p1100Estatus() {
        // ====================*
        // ---------------------------------------------------------------*
        //   CARGA EN UN ARREGLO LAS CLAVES DE ESTATUS DE AGENTES         *
        // ---------------------------------------------------------------*
        banderas.setWsFinCurMte(0);

        indices.setWsInd(0);

        tablaEstatus.initialize();
        p1105OpenCurMte();
        banderas.setWsFinCurMte(0);

        p1110FetchMte();
        while (!(banderas.isFinCurMte())) {
            p1120LoopMte();
        }
        p1180CloseCurMte();
        //
        // 1100-ESTATUS-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 1105-OPEN-CUR-MTE.
     *
     */
    void p1105OpenCurMte() {
        // =======================*
        curMte0Cursor.open();
        if (sqlca.getSqlcode() != 0) {
            msgMotivo = "ERROR AL OPEN DE CUR-MTE0 ";

            msgRutina = "1105-OPEN-CUR-MTE ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 1105-OPEN-CUR-MTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 1110-FETCH-MTE.
     *
     */
    void p1110FetchMte() {
        // =======================*
        if (banderas.getWkDisp() == 1){
            display("1110-FETCH-MTE   ");
        }
        gfvtmte0Model = curMte0Cursor.next();

        if (gfvtmte0Model.isPresent()) {
            wsDclgfvtmte0.setMteMteCve(fromHostInt(gfvtmte0Model.get().mteCve()));
            wsDclgfvtmte0.setMteMteDes(fromHostString(gfvtmte0Model.get().mteDes()));
            wsDclgfvtmte0.setMteEsaCve(fromHostInt(gfvtmte0Model.get().esaCve()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiCurMte(wsContadores.getWsLeiCurMte() + 1);
        } else if (sqlca.getSqlcode() == 100) {
            banderas.setWsFinCurMte(1);

        } else {
            msgMotivo = "ERROR AL FETCH DE CUR-MTE ";

            msgRutina = "1110-FETCH-MTE            ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 1110-FETCH-MTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 1120-LOOP-MTE.
     *
     */
    void p1120LoopMte() {
        // ************************
        p1130CargaMte();
        p1110FetchMte();
        //
        // 1120-LOOP-MTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 1130-CARGA-MTE.
     *
     */
    void p1130CargaMte() {
        // ===================*
        // --------------------------------------------------------------*
        //     CARGA EL CATALOGO DE ESTATUS A UN ARREGLO EN WORKING      *
        // --------------------------------------------------------------*
        indices.setWsInd(indices.getWsInd() + 1);
        if (indices.getWsInd() > constantes.getWsLimiteEstatus()) {
            display("ERROR, EL ARREGLO DEL CAT. DE ESTATUS   ", "REBASA EL LIMITE: ", constantes.getWsLimiteEstatus(), " ARREGLO: ", indices.getWsInd());
            display("INCREMENTE EL LIMITE EN EL ARREGLO    ");
            msgMotivo = "ERROR ARREGLO CAT DE ESTATUS  ";

            msgRutina = "1130-CARGA-MTE    ";

            codeError.setValue("000");
            p9999Abend();
        }
        tablaEstatus.getTabEstatus().at(indices.getWsInd()).setTbMteCve(wsDclgfvtmte0.getMteMteCve());
        tablaEstatus.getTabEstatus().at(indices.getWsInd()).setTbMteEsaCve(wsDclgfvtmte0.getMteEsaCve());
        //
        // 1130-CARGA-MTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 1180-CLOSE-CUR-MTE.
     *
     */
    void p1180CloseCurMte() {
        // =======================*
        if(curMte0Cursor != null) {
            curMte0Cursor.close();
        }
        if (sqlca.getSqlcode() != 0) {
            msgMotivo = "ERROR AL CLOSE DE CUR-MTE0 ";

            msgRutina = "1180-CLOSE-CUR-MTE ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 1180-CLOSE-CUR-MTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 3000-PROCESO.
     *
     */
    void p3000Proceso() {
        // =====================*
        // *******************************************************
        //  RUTINA PRINCIPAL
        // *******************************************************
        tablas.getTablaImptes().initialize();
        p4000ProcesoExtraccion();
        //
        // 3000-PROCESO-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 4000-PROCESO-EXTRACCION.
     *
     */
    void p4000ProcesoExtraccion() {
        // =====================*
        // --------------------------------------------------------------*
        //     LLAMA A LA RUTINA PARA: EXCLUIR EMPLEADOS Y EXTRANJEROS,
        //     OBTENER EL ESTATUS EL AGENTES Y SU ESQUEMA DE FACTURACION
        //     CORTE POR AGENTE
        // --------------------------------------------------------------*
        lgralLinkReg.getLgralSalida().initialize();
        p5000ValidaAgente();
        p5700MueveDatosAgente();
        //
        // 4000-PROCESO-EXTRACCION-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5000-VALIDA-AGENTE.
     *
     */
    void p5000ValidaAgente() {
        // =======================*
        // --------------------------------------------------------------*
        //     VALIDA EL ESTATUS DEL AGENTES EN LA TABLA GFVTAGT0
        //     LLAMA A LAS RUTINAS QUE VAN FORMAR EL REGISTRO DE DATOS
        //     DATOS GENERALES
        // --------------------------------------------------------------*
        p5010InicializaAgente();
        p5020LeeAgente();
        if (banderas.isEncGfvtagt()) {
            p5500ObtieneDatosAgente();
        }
        //
        // 5000-VALIDA-AGENTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5010-INICIALIZA-AGENTE.
     *
     */
    void p5010InicializaAgente() {
        // =======================*
        banderas.setWsCnaEstatus(0);

        wsVariablesNume.setWsCnaMteCve(0);

        wsVariablesNume.setWsCnaTfpCve(0);

        dclgfvtagt0.getAgtRfc().setSpaces();
        dclgfvtagt0.getAgtNom().setSpaces();
        dclgfvtagt0.getAgtApePat().setSpaces();
        dclgfvtagt0.getAgtApeMat().setSpaces();
        dclgfvtagt0.getAgtFecNac().setSpaces();
        dclgfvtagt0.getAgtNumCed().setSpaces();
        dclgfvtagt0.getAgtRazSoc().setSpaces();
        wsDclgfvtagt0.setAgtNalCve("");

        wsDclgfvtagt0.setAgtPefCve(0);

        wsDclgfvtagt0.setAgtTcaCve("");

        wsDclgfvtagt0.setAgtMteCve(0);

        dclgfvtpra0.getAgtCxx().setSpaces();
        wsDclgfvtdma0.setDmaDmaIdr(0);

        wsDclgfvtdma0.setDmaDmaCae("");

        wsDclgfvtdma0.setDmaDmaNum("");

        wsDclgfvtdma0.setDmaDmaInt("");

        wsDclgfvtdma0.setDmaDmaCol("");

        wsDclgfvtdma0.setDmaTdmCve(0);

        wsDclgfvtdma0.setDmaPobCve(0);

        wsDclgfvtdma0.setDmaCpoCve(0);

        wsDclgfvtdma0.setDmaMndCve(0);

        wsDclgcctmnd0.setMndMndNom("");

        wsDclgcctmnd0.setMndEdoCve("");

        dclgfvtcma0.getCmaDes().setSpaces();
        wsDclgcctpob0.setPobPobNom("");

        wsDclgfvtcna0.setCnaGerZonEnv(0);

        banderas.setWsEncGfvtagt(0);

        banderas.setWsEncGfvtcna(0);

        banderas.setWsEncGfvtpra(0);

        banderas.setWsEncGcctmnd(0);

        banderas.setWsEncGcctpob(0);

        banderas.setWsEncGcctedo(0);

        banderas.setWsEncGcctpai(0);

        banderas.setWsEncDirec(0);

        banderas.setWsEncGfvtcma(0);

        //
        // 5010-INICIALIZA-AGENTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5020-LEE-AGENTE.
     *
     */
    void p5020LeeAgente() {
        // =======================*
        // --------------------------------------------------------------*
        //     RUTINA PARA SELECCIONAR DATOS DE LA TABLA DE AGENTES      *
        // --------------------------------------------------------------*
        banderas.setWsEncGfvtagt(0);

        banderas.setWsEncAgente(0);

        genericModel =
            gfvtagt0Port.select_12(
                GenericModel.builder().values(List.of(
                    llaves.getWsAgtIdr()))
                .build());

        if(genericModel.isPresent()) {
            dclgfvtagt0.setAgtRfc(fromHostString(genericModel.get().values().get(0)));
            dclgfvtagt0.setAgtNom(fromHostString(genericModel.get().values().get(1)));
            dclgfvtagt0.setAgtApePat(fromHostString(genericModel.get().values().get(2)));
            dclgfvtagt0.setAgtApeMat(fromHostString(genericModel.get().values().get(3)));
            dclgfvtagt0.setAgtFecNac(fromHostString(genericModel.get().values().get(4)));
            dclgfvtagt0.setAgtNumCed(fromHostString(genericModel.get().values().get(5)));
            dclgfvtagt0.setAgtRazSoc(fromHostString(genericModel.get().values().get(6)));
            wsDclgfvtagt0.setAgtNomConcatenado(fromHostString(genericModel.get().values().get(7)));
            wsDclgfvtagt0.setAgtNalCve(fromHostString(genericModel.get().values().get(8)));
            indicadoresDeNulos.setIndagtnal(isNull(genericModel.get().values().get(8)) ? -1 : 0);
            wsDclgfvtagt0.setAgtPefCve(fromHostInt(genericModel.get().values().get(9)));
            indicadoresDeNulos.setIndagtpef(isNull(genericModel.get().values().get(9)) ? -1 : 0);
            wsDclgfvtagt0.setAgtTcaCve(fromHostString(genericModel.get().values().get(10)));
            indicadoresDeNulos.setIndagttca(isNull(genericModel.get().values().get(10)) ? -1 : 0);
            wsDclgfvtagt0.setAgtMteCve(fromHostInt(genericModel.get().values().get(11)));
            indicadoresDeNulos.setIndagtmte(isNull(genericModel.get().values().get(11)) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiGfvtagt(wsContadores.getWsLeiGfvtagt() + 1);
            wsContadores.setWsLeiAgentes(wsContadores.getWsLeiAgentes() + 1);
            banderas.setWsEncGfvtagt(1);

            banderas.setWsEncAgente(1);

        } else if (sqlca.getSqlcode() == 100){
            //           DISPLAY 'NO SE ENCONTRO AGENTE EN GFVTAGT0: '
            //                   WS-AGT-IDR
            wsContadores.setWsSinGfvtagt(wsContadores.getWsSinGfvtagt() + 1);
        } else {
            msgMotivo = "ERROR AL SELECT DE GFVTAGT0    ";

            msgRutina = "5020-LEE-AGENTE           ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 5020-LEE-AGENTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5040-BUSCA-ESTATUS.
     *
     */
    void p5040BuscaEstatus() {
        // ===================*
        // --------------------------------------------------------------*
        //     OBTIENE EL ESTATUS DEL AGENTE EN EL ARREGLO
        // --------------------------------------------------------------*
        banderas.setWsEncEstatus(0);

        indices.setWsI(1);
        while (!(indices.getWsI() > constantes.getWsLimiteEstatus()
            || banderas.isEncEstatus()
            || tablaEstatus.getTabEstatus().at(indices.getWsI()).getTbMteCve().toInt() == 0)){
            //          IF TB-MTE-CVE         (WS-I) =  WS-AGT-MTE-CVE
            if (tablaEstatus.getTabEstatus().at(indices.getWsI()).getTbMteCve().toInt() == wsDclgfvtagt0.getAgtMteCve()){
                banderas.setWsAgenteEstatus(tablaEstatus.getTabEstatus().at(indices.getWsI()).getTbMteEsaCve().toInt());

                banderas.setWsEncEstatus(1);

            }
            indices.setWsI(indices.getWsI() + 1);
        }
        //
        // 5040-BUSCA-ESTATUS-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5500-OBTIENE-DATOS-AGENTE.
     *
     */
    void p5500ObtieneDatosAgente() {
        // =======================*
        // --------------------------------------------------------------*
        //     RUTINA PARA SELECCIONAR CADA UNO DE LOS CAMPOS QUE SE
        //     REQUIEREN PARA LLENAR LOS DATOS GENERALES DEL AGENTE
        // --------------------------------------------------------------*
        //  OBTIENE RAZON SOCIAL
        if (wsDclgfvtagt0.getAgtPefCve() == 2
            && wsDclgfvtagt0.getAgtNalCve().equals("MEX")){
            if (dclgfvtagt0.getAgtRazSoc().isSpaces()){
                //           DISPLAY 'PERSONA MORAL SIN RAZON SOCIAL:    '
                //                   WS-AGT-IDR
                wsContadores.setWsSinRazSocial(wsContadores.getWsSinRazSocial() + 1);
            }
        } else {
            dclgfvtagt0.getAgtRazSoc().setSpaces();
        }
        //  OBTIENE CURP
        // **  IF PEF-CVE  = 1
        p5510LeeGfvtpra();
        // **  END-IF
        //  OBTIENE DIRECCION
        p5520ObtieneDirec();
        //  OBTIENE NOMBRE DE LA DELEGACION / MUNICIPIO
        if (banderas.isEncDirec()){
            p5560NombreDeleg();
        }
        //  OBTIENE CLAVE DE PAIS
        if (banderas.isEncGcctmnd()){
            p5565Pais();
        }
        //  OBTIENE NOMBRE DE LA LOCALIDAD  (CON LA CVE DE POBLACION)
        //  DESCOMENTAR LAS SIGUIENTES 4 LINEAS CUANDO DEN AUTORIDAD EN
        //  LA TABLA
        if (banderas.isEncDirec()){
            p5570NombrePob();
        }
        //  OBTIENE TELEFONO
        if (banderas.isEncDirec()){
            p5580Telefono();
        }
        //  OBTIENE NOMBRE DEL PAIS
        //  DESCOMENTAR LAS SIGUIENTES 4 LINEAS CUANDO DEN AUTORIDAD
        if (banderas.isEncGcctedo()){
            p5585NombrePais();
        }
        //  OBTIENE ESTATUS DEL AGENTE
        p5040BuscaEstatus();
        //
        // 5500-OBTIENE-DATOS-AGENTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5510-LEE-GFVTPRA.
     *
     */
    void p5510LeeGfvtpra() {
        // ************************
        // --------------------------------------------------------------*
        //     RUTINA PARA OBTENER EL CURP
        // --------------------------------------------------------------*
        banderas.setWsEncGfvtpra(0);

        gfvtpra0Model =
            gfvtpra0Port.select_2(
                Gfvtpra0Model.builder()
                    .agtIdr(llaves.getWsAgtIdr())
                .build());

        if(gfvtpra0Model.isPresent()) {
            dclgfvtpra0.setAgtCxx(fromHostString(gfvtpra0Model.get().agtCxx()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiGfvtpra(wsContadores.getWsLeiGfvtpra() + 1);
            banderas.setWsEncGfvtpra(1);

        } else if (sqlca.getSqlcode() == 100){
            //           DISPLAY 'NO SE ENCONTRO CURP (GFVTPRA0) DEL AGENTE: '
            //                   WS-AGT-IDR-ANT
            wsContadores.setWsSinGfvtpra(wsContadores.getWsSinGfvtpra() + 1);
        } else {
            msgMotivo = "ERROR AL SELECT DE GFVTPRA0    ";

            msgRutina = "5510-LEE-GFVTPRA          ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 5510-LEE-GFVTPRA-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5520-OBTIENE-DIREC.
     *
     */
    void p5520ObtieneDirec() {
        // =======================*
        // --------------------------------------------------------------*
        //     RUTINA PARA OBTENER LA DIRECCION, CALLE, NO., ETC.
        // --------------------------------------------------------------*
        banderas.setWsEncDirec(0);

        banderas.setWsFinCurDma(0);

        p5530AbreCursorDma();
        p5540LeeCursorDma();
        if (banderas.isFinCurDma()){
            //        DISPLAY 'NO SE ENCONTRO DIREC(GFVTDMA0) DEL AGENTE: '
            //                 WS-AGT-IDR
            wsContadores.setWsSinGfvtdma(wsContadores.getWsSinGfvtdma() + 1);
        } else {
            while (!(banderas.isEncDirec())) {
                    banderas.setWsEncDirec(1);

                    if (wsDclgfvtdma0.getDmaTdmCve() == 2
                        || wsDclgfvtdma0.getDmaTdmCve() == 3){
                        wsContadores.setWsConDirFiscal(wsContadores.getWsConDirFiscal() + 1);
                    } else if (wsDclgfvtdma0.getDmaTdmCve() > 3) {
                        banderas.setWsEncDirec(1);

                    } else {
                        //                 DISPLAY 'AGENTE SIN DIRECCION FISCAL: '
                        //                          WS-AGT-IDR
                        wsContadores.setWsSinDirFiscal(wsContadores.getWsSinDirFiscal() + 1);
                    }
                    if (!banderas.isEncDirec()) {
                        p5540LeeCursorDma();
                    }
            }
        }
        p5550CierraCursorDma();
        //
        // 5520-OBTIENE-DIREC-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5530-ABRE-CURSOR-DMA.
     *
     */
    void p5530AbreCursorDma() {
        // =======================*
        curDma0Cursor.open();
        if (sqlca.getSqlcode() != 0) {
            msgMotivo = "ERROR AL OPEN DE CUR-DMA0        ";

            msgRutina = "5530-ABRE-CURSOR-DMA             ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 5530-ABRE-CURSOR-DMA-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5540-LEE-CURSOR-DMA.
     *
     */
    void p5540LeeCursorDma() {
        // =======================*
        // --------------------------------------------------------------*
        //     RUTINA PARA LEER EL CURSOR DMA DIRECCIONES DEL AGENTE
        // --------------------------------------------------------------*
        gfvtdma0Model = curDma0Cursor.next();

        if (gfvtdma0Model.isPresent()) {
            wsDclgfvtdma0.setDmaAgtIdr(fromHostInt(gfvtdma0Model.get().agtIdr()));
            indicadoresDeNulos.setInddmaagt(isNull(gfvtdma0Model.get().agtIdr()) ? -1 : 0);
            wsDclgfvtdma0.setDmaDmaIdr(fromHostInt(gfvtdma0Model.get().dmaIdr()));
            indicadoresDeNulos.setInddmaidr(isNull(gfvtdma0Model.get().dmaIdr()) ? -1 : 0);
            wsDclgfvtdma0.setDmaDmaCae(fromHostString(gfvtdma0Model.get().dmaCae()));
            wsDclgfvtdma0.setDmaDmaNum(fromHostString(gfvtdma0Model.get().dmaNum()));
            wsDclgfvtdma0.setDmaDmaInt(fromHostString(gfvtdma0Model.get().dmaInt()));
            wsDclgfvtdma0.setDmaDmaCol(fromHostString(gfvtdma0Model.get().dmaCol()));
            indicadoresDeNulos.setInddmacol(isNull(gfvtdma0Model.get().dmaCol()) ? -1 : 0);
            wsDclgfvtdma0.setDmaTdmCve(fromHostInt(gfvtdma0Model.get().tdmCve()));
            indicadoresDeNulos.setInddmatdm(isNull(gfvtdma0Model.get().tdmCve()) ? -1 : 0);
            wsDclgfvtdma0.setDmaPobCve(fromHostInt(gfvtdma0Model.get().pobCve()));
            wsDclgfvtdma0.setDmaCpoCve(fromHostInt(gfvtdma0Model.get().cpoCve()));
            indicadoresDeNulos.setInddmacpo(isNull(gfvtdma0Model.get().cpoCve()) ? -1 : 0);
            wsDclgfvtdma0.setDmaMndCve(fromHostInt(gfvtdma0Model.get().mndCve()));
        }
        if (sqlca.getSqlcode() == -305) {
            sqlca.setSqlcode(0);

            if (indicadoresDeNulos.getInddmaagt() < 0) {
                display("INDICADOR CON NULOS ", " ", llaves.getWsAgtIdr(), " ", wsDclgfvtdma0.getDmaAgtIdr(), " ", indicadoresDeNulos.getInddmaidr());
                wsDclgfvtdma0.setDmaAgtIdr(0);

            }
            if (indicadoresDeNulos.getInddmaidr() < 0) {
                display("INDICADOR CON NULOS ", " ", llaves.getWsAgtIdr(), " ", wsDclgfvtdma0.getDmaDmaIdr(), " ", indicadoresDeNulos.getInddmaidr());
                wsDclgfvtdma0.setDmaDmaIdr(0);

            }
            if (indicadoresDeNulos.getInddmatdm() < 0) {
                display("INDICADOR CON NULOS ", " ", llaves.getWsAgtIdr(), " ", wsDclgfvtdma0.getDmaTdmCve(), " ", indicadoresDeNulos.getInddmatdm());
                wsDclgfvtdma0.setDmaTdmCve(0);

            }
            if (indicadoresDeNulos.getInddmacol() < 0) {
                display("INDICADOR CON NULOS ", " ", llaves.getWsAgtIdr(), " ", wsDclgfvtdma0.getDmaDmaCol(), " ", indicadoresDeNulos.getInddmacol());
                wsDclgfvtdma0.setDmaTdmCve(0);

            }
            if (indicadoresDeNulos.getInddmacpo() < 0) {
                display("INDICADOR CON NULOS ", " ", llaves.getWsAgtIdr(), " ", wsDclgfvtdma0.getDmaCpoCve(), " ", indicadoresDeNulos.getInddmacpo());
                wsDclgfvtdma0.setDmaTdmCve(0);

            }
            wsContadores.setWsLeiCurDmaNulos(wsContadores.getWsLeiCurDmaNulos() + 1);
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiCurDma(wsContadores.getWsLeiCurDma() + 1);
        } else if (sqlca.getSqlcode() == 100) {
            banderas.setWsFinCurDma(1);

        } else {
            display(" ", llaves.getWsAgtIdr(), " ", wsDclgfvtdma0.getDmaCpoCve(), " ", indicadoresDeNulos.getInddmacpo());
            msgMotivo = "ERROR AL FETCH DE CUR-DMA (DIR)";

            msgRutina = "5540-LEE-CURSOR-DMA       ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        lgralLinkReg.setLgralSwErr(lzPad(sqlca.getSqlcode(), 9));
        //
        // 5540-LEE-CURSOR-DMA-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5550-CIERRA-CURSOR-DMA.
     *
     */
    void p5550CierraCursorDma() {
        // =======================*
        // --------------------------------------------------------------*
        //     RUTINA PARA CERRAR CURSOR DMA DIRECCIONES
        // --------------------------------------------------------------*
        if(curDma0Cursor != null) {
            curDma0Cursor.close();
        }
        if (sqlca.getSqlcode() != 0) {
            msgMotivo = "ERROR AL OPEN DE CUR-DMA0 (DIREC)";

            msgRutina = "5550-CIERRA-CURSOR-DMA           ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 5550-CIERRA-CURSOR-DMA-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5560-NOMBRE-DELEG.
     *
     */
    void p5560NombreDeleg() {
        // ===================*
        // --------------------------------------------------------------*
        //     RUTINA PARA OBTENER EL NOMBRE DE LA DELEGACION / MUNICIPIO
        //     ACCESA LA TABLA GCCTMND0 CON LA CLAVE DE DELEGACION
        //     OBTENIDO DE GFVTDMA0  (DIRECCIONES)
        // --------------------------------------------------------------*
        banderas.setWsEncGcctmnd(0);

        gcctmnd0Model =
            gcctmnd0Port.select_3(
                Gcctmnd0Model.builder()
                    .mndCve(wsDclgfvtdma0.getDmaMndCve())
                .build());

        if(gcctmnd0Model.isPresent()) {
            wsDclgcctmnd0.setMndMndNom(fromHostString(gcctmnd0Model.get().mndNom()));
            wsDclgcctmnd0.setMndEdoCve(fromHostString(gcctmnd0Model.get().edoCve()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiGcctmnd(wsContadores.getWsLeiGcctmnd() + 1);
            banderas.setWsEncGcctmnd(1);

        } else if (sqlca.getSqlcode() == 100){
            //           DISPLAY 'NO SE ENCONTRO DELEG (GCCTMND0) DEL AGENTE: '
            //                   WS-AGT-IDR
            wsContadores.setWsSinGcctmnd(wsContadores.getWsSinGcctmnd() + 1);
        } else {
            msgMotivo = "ERROR AL SELECT DE GCCTMND0    ";

            msgRutina = "5560-NOMBRE-DELEG         ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        lgralLinkReg.setLgralSwErr(lzPad(sqlca.getSqlcode(), 9));
        //
        // 5560-NOMBRE-DELEG-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5565-PAIS.
     *
     */
    void p5565Pais() {
        // ===================*
        // --------------------------------------------------------------*
        //     RUTINA PARA OBTENER EL PAIS
        //     ACCESA LA TABLA GCCTEDO0 CON LA CLAVE DE ESTADO
        //     OBTENIDO DE GFVTMND0  (DIRECCIONES)
        // --------------------------------------------------------------*
        banderas.setWsEncGcctedo(0);

        gcctedo0Model =
            gcctedo0Port.select_1(
                Gcctedo0Model.builder()
                    .edoCve(toHostString(wsDclgcctmnd0.getMndEdoCve()))
                .build());

        if(gcctedo0Model.isPresent()) {
            wsDclgcctedo0.setEdoPaiCve(fromHostString(gcctedo0Model.get().paiCve()));
            wsDclgcctedo0.setEdoEdoNom(fromHostString(gcctedo0Model.get().edoNom()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiGcctedo(wsContadores.getWsLeiGcctedo() + 1);
            banderas.setWsEncGcctedo(1);

        } else if (sqlca.getSqlcode() == 100){
            //           DISPLAY 'NO SE ENCONTRO EDO-PAIS (GCQTEDO0)  AGENTE: '
            //                   WS-AGT-IDR
            wsContadores.setWsSinGcctedo(wsContadores.getWsSinGcctedo() + 1);
        } else {
            msgMotivo = "ERROR AL SELECT DE GCCTEDO0    ";

            msgRutina = "5565-PAIS                 ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        lgralLinkReg.setLgralSwErr(lzPad(sqlca.getSqlcode(), 9));
        //
        // 5565-PAIS-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5570-NOMBRE-POB.
     *
     */
    void p5570NombrePob() {
        // ===================*
        // --------------------------------------------------------------*
        //     RUTINA PARA OBTENER EL NOMBRE DE LA LOCALIDAD
        //     ACCESA LA TABLA GCCTPOB0 CON LA CLAVE DE POBLACION
        //     OBTENIDA DE GFVTDMA0  (DIRECCIONES)
        // --------------------------------------------------------------*
        banderas.setWsEncGcctpob(0);

        gcctpob0Model =
            gcctpob0Port.select_4(
                Gcctpob0Model.builder()
                    .pobCve(wsDclgfvtdma0.getDmaPobCve())
                .build());

        if(gcctpob0Model.isPresent()) {
            wsDclgcctpob0.setPobPobNom(fromHostString(gcctpob0Model.get().pobNom()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiGcctpob(wsContadores.getWsLeiGcctpob() + 1);
            banderas.setWsEncGcctpob(1);

        } else if (sqlca.getSqlcode() == 100){
            //           DISPLAY 'NO SE ENCONTRO POBLA (GCCTPOB0) DEL AGENTE: '
            //                   WS-AGT-IDR
            wsContadores.setWsSinGcctpob(wsContadores.getWsSinGcctpob() + 1);
        } else {
            msgMotivo = "ERROR AL SELECT DE GCCTPOB0    ";

            msgRutina = "5570-NOMBRE-POB           ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 5570-NOMBRE-POB-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5580-TELEFONO.
     *
     */
    void p5580Telefono() {
        // ===================*
        // --------------------------------------------------------------*
        //     RUTINA PARA OBTENER EL TELEFONO
        //     ACCESA LA TABLA GFVTCMA0 CON LA CLAVE DMA_IDR
        //     OBTENIDA DE GFVTDMA0  (DIRECCIONES)
        // --------------------------------------------------------------*
        banderas.setWsEncGfvtcma(0);

        gfvtcma0Model =
            gfvtcma0Port.select_3(
                Gfvtcma0Model.builder()
                    .dmaIdr(wsDclgfvtdma0.getDmaDmaIdr())
                    .agtIdr(llaves.getWsAgtIdr())
                .build());

        if(gfvtcma0Model.isPresent()) {
            dclgfvtcma0.setCmaDes(fromHostString(gfvtcma0Model.get().cmaDes()));
            indicadoresDeNulos.setIndcmades(isNull(gfvtcma0Model.get().cmaDes()) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiGfvtcma(wsContadores.getWsLeiGfvtcma() + 1);
            banderas.setWsEncGfvtcma(1);

            if (indicadoresDeNulos.getIndcmades() < 0) {
                display("INDICADOR CON NULOS ", " ", llaves.getWsAgtIdr(), " ", dclgfvtcma0.getCmaDes(), " ", indicadoresDeNulos.getIndcmades());
                dclgfvtcma0.getCmaDes().setSpaces();
            }
        } else if (sqlca.getSqlcode() == 100){
            //           DISPLAY 'NO SE ENCONTRO TELEF (GFVTCMA0) DEL AGENTE: '
            //                   WS-AGT-IDR-ANT ' DMA-IDR: ' DMA-DMA-IDR
            wsContadores.setWsSinGfvtcma(wsContadores.getWsSinGfvtcma() + 1);
        } else {
            msgMotivo = "ERROR AL SELECT DE GFVTCMA     ";

            msgRutina = "5580-TELEFONO             ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        lgralLinkReg.setLgralSwErr(lzPad(sqlca.getSqlcode(), 9));
        //
        // 5580-TELEFONO-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5585-NOMBRE-PAIS.
     *
     */
    void p5585NombrePais() {
        // ===================*
        // --------------------------------------------------------------*
        //     RUTINA PARA OBTENER EL NOMBRE DEL PAIS                    *
        //     ACCESA LA TABLA GCCTPAI0 CON LA CLAVE DE PAIS             *
        //     OBTENIDO DE GCCTEDO0  (ESTADOS)                           *
        // --------------------------------------------------------------*
        banderas.setWsEncGcctpai(0);

        gcctpai0Model =
            gcctpai0Port.select(
                Gcctpai0Model.builder()
                    .paiCve(toHostString(wsDclgcctedo0.getEdoPaiCve()))
                .build());

        if(gcctpai0Model.isPresent()) {
            dclgcctpai0.setPaiPaiNom(fromHostString(gcctpai0Model.get().paiNom()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWsLeiGcctpai(wsContadores.getWsLeiGcctpai() + 1);
            banderas.setWsEncGcctpai(1);

        } else if (sqlca.getSqlcode() == 100){
            //           DISPLAY 'NO SE ENCONTRO NOM-PAIS (GCQTPAI0)  AGENTE: '
            //                   WS-AGT-IDR
            wsContadores.setWsSinGcctpai(wsContadores.getWsSinGcctpai() + 1);
        } else {
            msgMotivo = "ERROR AL SELECT DE GCCTPAI0    ";

            msgRutina = "5585-NOMBRE-PAIS          ";

            codeError.setValue(sqlca.getSqlcode());
            p9999Abend();
        }
        //
        // 5585-NOMBRE-PAIS-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 5700-MUEVE-DATOS-AGENTE.
     *
     * EXIT.
     */
    void p5700MueveDatosAgente() {
        // ===================*
        //  MUEVE LA INFORMACION DE DATOS GENERALES DEL AGENTE
        lgralLinkReg.setLgralAgtRfc(dclgfvtagt0.getAgtRfc());
        lgralLinkReg.setLgralAgtApePat(dclgfvtagt0.getAgtApePat());
        lgralLinkReg.setLgralAgtApeMat(dclgfvtagt0.getAgtApeMat());
        lgralLinkReg.setLgralAgtNom(dclgfvtagt0.getAgtNom());
        lgralLinkReg.setLgralAgtRazSoc(dclgfvtagt0.getAgtRazSoc());
        lgralLinkReg.setLgralAgtNomConcatenado(wsDclgfvtagt0.getAgtNomConcatenado());
        lgralLinkReg.setLgralPraAgtCxxCurp(dclgfvtpra0.getAgtCxx());
        lgralLinkReg.setLgralAgtNalCve(wsDclgfvtagt0.getAgtNalCve());
        lgralLinkReg.setLgralEsaCve(banderas.getWsAgenteEstatus());
        lgralLinkReg.setLgralPefCve(wsDclgfvtagt0.getAgtPefCve());
        lgralLinkReg.setLgralDmaCae(wsDclgfvtdma0.getDmaDmaCae());
        lgralLinkReg.setLgralDmaNum(wsDclgfvtdma0.getDmaDmaNum());
        lgralLinkReg.setLgralDmaInt(wsDclgfvtdma0.getDmaDmaInt());
        lgralLinkReg.setLgralDmaCol(wsDclgfvtdma0.getDmaDmaCol());
        lgralLinkReg.setLgralMndNom(wsDclgcctmnd0.getMndMndNom());
        lgralLinkReg.setLgralDmaCpoCve(wsDclgfvtdma0.getDmaCpoCve());
        lgralLinkReg.setLgralPobNom(wsDclgcctpob0.getPobPobNom());
        lgralLinkReg.setLgralMndEdoCve(wsDclgcctmnd0.getMndEdoCve());
        lgralLinkReg.setLgralEdoEdoNom(wsDclgcctedo0.getEdoEdoNom());
        lgralLinkReg.setLgralEdoPaiCve(wsDclgcctedo0.getEdoPaiCve());
        lgralLinkReg.setLgralPaiPaiNom(dclgcctpai0.getPaiPaiNom());
        lgralLinkReg.setLgralCmaDesTelefon(dclgfvtcma0.getCmaDes());
        //
        // 5700-MUEVE-DATOS-AGENTE-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 9000-FINAL.
     *
     */
    void p9000Final() {
        // ===================*
        //     DISPLAY '******************************************'
        //     DISPLAY '*** TERMINACION NORMAL **  NORMAL  *******'
        //     DISPLAY '******************************************'
        //     PERFORM 9010-ESTADISTICAS
        //        THRU 9010-ESTADISTICAS-FIN
        throw new ExitProgramException();
        //
        // 9000-FINAL-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 9999-ABEND.
     *
     */
    void p9999Abend() {
        // ********************
        display("******************************************");
        display("*** TERMINACION ANORMAL ** ANORMAL *******");
        p9010Estadisticas();
        display("***************************************");
        display("*** ERROR EN PROGRAMA :   GGQACC00   ***");
        display("***************************************");
        display("*** MOTIVO DE ERROR   : ", msgMotivo);
        display("*** RUTINA            : ", msgRutina);
        display("*** CODIGO DE ERROR NO. ", codeError);
        display("***************************************");
        display("***************************************");
        returnCode.setValue(16);
        lgralLinkReg.getLgralSwErr().setValue(16);
        throw new StopRunException();
        //
        // 9999-ABEND-FIN - Last paragraph
        //
    }

    /**
     *  Paragraph: 9010-ESTADISTICAS.
     *
     */
    void p9010Estadisticas() {
        // ===================*
        display("*** P R O G R A M A  ---- ", constantes.getW000Prog(), "***");
        display("***      E S T A D I S T I C A S ");
        display("******************************************");
        display("******************************************");
        display("FECHA DE PROCESO ........................: ", wsFecCurr);
        display("------------------------------------------");
        display("AGENTE LEIDO ............................: ", llaves.getWsAgtIdrAux());
        display("------------------------------------------");
        display("                                ");
        //
        // 9010-ESTADISTICAS-FIN - Last paragraph
        //
    }

    @Autowired
    public void setGfvtcma0Port(IGfvtcma0Port gfvtcma0Port) {
        this.gfvtcma0Port = gfvtcma0Port;
        this.gfvtcma0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtesa0Port(IGfvtesa0Port gfvtesa0Port) {
        this.gfvtesa0Port = gfvtesa0Port;
        this.gfvtesa0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtmte0Port(IGfvtmte0Port gfvtmte0Port) {
        this.gfvtmte0Port = gfvtmte0Port;
        this.gfvtmte0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtpra0Port(IGfvtpra0Port gfvtpra0Port) {
        this.gfvtpra0Port = gfvtpra0Port;
        this.gfvtpra0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctedo0Port(IGcctedo0Port gcctedo0Port) {
        this.gcctedo0Port = gcctedo0Port;
        this.gcctedo0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctpob0Port(IGcctpob0Port gcctpob0Port) {
        this.gcctpob0Port = gcctpob0Port;
        this.gcctpob0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctmnd0Port(IGcctmnd0Port gcctmnd0Port) {
        this.gcctmnd0Port = gcctmnd0Port;
        this.gcctmnd0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagt0Port(IGfvtagt0Port gfvtagt0Port) {
        this.gfvtagt0Port = gfvtagt0Port;
        this.gfvtagt0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctpai0Port(IGcctpai0Port gcctpai0Port) {
        this.gcctpai0Port = gcctpai0Port;
        this.gcctpai0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdma0Port(IGfvtdma0Port gfvtdma0Port) {
        this.gfvtdma0Port = gfvtdma0Port;
        this.gfvtdma0Port.setProgramContext(getProgramContext());
    }
}
