package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.regagentisr;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: VS-LLAVE-AGT.
 *
 */
@Setter
@Getter
public class VsLlaveAgt extends DataStruct {
    // Properties
    private AlphanumericVar vsAgente = new AlphanumericVar(this, 7);         // 07 VS-AGENTE X(07)

    public VsLlaveAgt() {
        super();
        initialize();
    }

    public VsLlaveAgt(DataContainer parent) {
        super(parent);
    }

    public VsLlaveAgt(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setVsAgente(final String vsAgente) {
        this.vsAgente.setValue(vsAgente);
    }

    public void setVsAgente(final IAlphanumericValue vsAgente) {
        this.vsAgente.setValue(vsAgente);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.vsAgente.clear();
    }

}

