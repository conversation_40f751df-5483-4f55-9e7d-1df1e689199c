package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FECHA-SYS.
 *
 */
@Setter
@Getter
public class WsFechaSys extends DataStruct {
    // Properties
    private UnsignedNumericVar wsAaSys = new UnsignedNumericVar(this, 4, 0);                 // 10 WS-AA-SYS 9(04)
    private UnsignedNumericVar wsMmSys = new UnsignedNumericVar(this, 2, 0);                 // 10 WS-MM-SYS 9(02)
    private UnsignedNumericVar wsDdSys = new UnsignedNumericVar(this, 2, 0);                 // 10 WS-DD-SYS 9(02)

    public WsFechaSys() {
        super();
        initialize();
    }

    public WsFechaSys(DataContainer parent) {
        super(parent);
    }

    public WsFechaSys(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAaSys(final int wsAaSys) {
        this.wsAaSys.setValue(wsAaSys);
    }

    public void setWsAaSys(final BigDecimal wsAaSys) {
        this.wsAaSys.setValue(wsAaSys);
    }

    public void setWsAaSys(final INumericValue wsAaSys) {
        this.wsAaSys.setValue(wsAaSys);
    }

    public void setWsMmSys(final int wsMmSys) {
        this.wsMmSys.setValue(wsMmSys);
    }

    public void setWsMmSys(final BigDecimal wsMmSys) {
        this.wsMmSys.setValue(wsMmSys);
    }

    public void setWsMmSys(final INumericValue wsMmSys) {
        this.wsMmSys.setValue(wsMmSys);
    }

    public void setWsDdSys(final int wsDdSys) {
        this.wsDdSys.setValue(wsDdSys);
    }

    public void setWsDdSys(final BigDecimal wsDdSys) {
        this.wsDdSys.setValue(wsDdSys);
    }

    public void setWsDdSys(final INumericValue wsDdSys) {
        this.wsDdSys.setValue(wsDdSys);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAaSys.clear();
        this.wsMmSys.clear();
        this.wsDdSys.clear();
    }

}

