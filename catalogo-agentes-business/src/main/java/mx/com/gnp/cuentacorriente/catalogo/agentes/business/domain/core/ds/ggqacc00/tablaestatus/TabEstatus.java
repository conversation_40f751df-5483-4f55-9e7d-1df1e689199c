package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablaestatus;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TAB-ESTATUS.
 *
 */
@Setter
@Getter
public class TabEstatus extends DataStructArray<TabEstatus> {
    // Properties
    private UnsignedNumericVar tbMteCve = new UnsignedNumericVar(this, 4, 0);                // 07 TB-MTE-CVE 9(04)
    private UnsignedNumericVar tbMteEsaCve = new UnsignedNumericVar(this, 4, 0);             // 07 TB-MTE-ESA-CVE 9(04)

    public TabEstatus(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TabEstatus(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TabEstatus(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TabEstatus(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TabEstatus at(int idx) {
        return new TabEstatus(this, idx, getSizeElement());
    }

    public void setTbMteCve(final int tbMteCve) {
        this.tbMteCve.setValue(tbMteCve);
    }

    public void setTbMteCve(final BigDecimal tbMteCve) {
        this.tbMteCve.setValue(tbMteCve);
    }

    public void setTbMteCve(final INumericValue tbMteCve) {
        this.tbMteCve.setValue(tbMteCve);
    }

    public void setTbMteEsaCve(final int tbMteEsaCve) {
        this.tbMteEsaCve.setValue(tbMteEsaCve);
    }

    public void setTbMteEsaCve(final BigDecimal tbMteEsaCve) {
        this.tbMteEsaCve.setValue(tbMteEsaCve);
    }

    public void setTbMteEsaCve(final INumericValue tbMteEsaCve) {
        this.tbMteEsaCve.setValue(tbMteEsaCve);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbMteCve.clear();
        this.tbMteEsaCve.clear();
    }

}

