package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaofn0;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablaofn0.tofn0.TOfn0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-OFN0.
 *
 */
// 230           10 T-OFN0      OCCURS 1 TO 51                                
@Setter
@Getter
public class TOfn0 extends DataStructArray<TOfn0> {
    // Properties
    private TOfn0Elem tOfn0Elem = new  TOfn0Elem(this);                      // 15 T-OFN0-ELEM UsedAsParameter

    public TOfn0(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TOfn0(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TOfn0(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TOfn0(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TOfn0 at(int idx) {
        return new TOfn0(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tOfn0Elem.sCnaOfnCve -> 20 S-CNA-OFN-CVE X(04)
    public AlphanumericVar getSCnaOfnCve() {
        return this.tOfn0Elem.getSCnaOfnCve();
    }

    public void setSCnaOfnCve(final String sCnaOfnCve) {
        this.tOfn0Elem.setSCnaOfnCve(sCnaOfnCve);
    }

    public void setSCnaOfnCve(final IAlphanumericValue sCnaOfnCve) {
        this.tOfn0Elem.setSCnaOfnCve(sCnaOfnCve);
    }

    // Property: tOfn0Elem.sOfnLimitad -> 20 S-OFN-LIMITAD X(01)
    public AlphanumericVar getSOfnLimitad() {
        return this.tOfn0Elem.getSOfnLimitad();
    }

    public void setSOfnLimitad(final String sOfnLimitad) {
        this.tOfn0Elem.setSOfnLimitad(sOfnLimitad);
    }

    public void setSOfnLimitad(final IAlphanumericValue sOfnLimitad) {
        this.tOfn0Elem.setSOfnLimitad(sOfnLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tOfn0Elem.initialize();
    }

}

