package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsregsalida.tfla1.TFla0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: T-FLA1.
 *
 */
// 186           10 T-FLA1      OCCURS 1 TO 84                                
@Setter
@Getter
public class TFla1 extends DataStructArray<TFla1> {
    // Properties
    private TFla0Elem tFla0Elem = new  TFla0Elem(this);                      // 15 T-FLA0-ELEM

    public TFla1(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TFla1(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TFla1(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TFla1(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TFla1 at(int idx) {
        return new TFla1(this, idx, getSizeElement());
    }


    // Deeper properties

    // Property: tFla0Elem.sFlaFlaNum1 -> 20 S-FLA-FLA-NUM1 X(08)
    public AlphanumericVar getSFlaFlaNum1() {
        return this.tFla0Elem.getSFlaFlaNum1();
    }

    public void setSFlaFlaNum1(final String sFlaFlaNum1) {
        this.tFla0Elem.setSFlaFlaNum1(sFlaFlaNum1);
    }

    public void setSFlaFlaNum1(final IAlphanumericValue sFlaFlaNum1) {
        this.tFla0Elem.setSFlaFlaNum1(sFlaFlaNum1);
    }

    // Property: tFla0Elem.sFlaLimitad1 -> 20 S-FLA-LIMITAD1 X(01)
    public AlphanumericVar getSFlaLimitad1() {
        return this.tFla0Elem.getSFlaLimitad1();
    }

    public void setSFlaLimitad1(final String sFlaLimitad1) {
        this.tFla0Elem.setSFlaLimitad1(sFlaLimitad1);
    }

    public void setSFlaLimitad1(final IAlphanumericValue sFlaLimitad1) {
        this.tFla0Elem.setSFlaLimitad1(sFlaLimitad1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tFla0Elem.initialize();
    }

}

