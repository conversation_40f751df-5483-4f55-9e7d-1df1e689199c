package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.linea;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: DET22.
 *
 */
// 112011200*                                                                 01120002
// 113011300*                                                                 01130002
@Setter
@Getter
public class Det22 extends DataStruct {
    // Properties
    private AlphanumericVar d3FlaDes = new AlphanumericVar(this, 40);        // 05 D3-FLA-DES X(40)
    private AlphanumericVar filler21 = new AlphanumericVar(this, 6);         // 05 FILLER X(6)
    private AlphanumericVar d3AgtTraEsp = new AlphanumericVar(this, 1);      // 05 D3-AGT-TRA-ESP X(1)
    private AlphanumericVar filler22 = new AlphanumericVar(this, 6);         // 05 FILLER X(6)
    private AlphanumericVar d3CnaFecCnx = new AlphanumericVar(this, 10);     // 05 D3-CNA-FEC-CNX X(10)
    private AlphanumericVar filler23 = new AlphanumericVar(this, 6);         // 05 FILLER X(6)
    private NumericEditedVar d3GnaCve = new NumericEditedVar(this, "ZZZ9");  // 05 D3-GNA-CVE ZZZ9
    private AlphanumericVar filler24 = new AlphanumericVar(this, 6);         // 05 FILLER X(6)
    private NumericEditedVar d3EsaCveFla = new NumericEditedVar(this, "ZZZ9");               // 05 D3-ESA-CVE-FLA ZZZ9
    private AlphanumericVar filler25 = new AlphanumericVar(this, 10);        // 05 FILLER X(10)
    private NumericEditedVar d3FlaGerZon = new NumericEditedVar(this, "Z999");               // 05 D3-FLA-GER-ZON Z999
    private AlphanumericVar filler26 = new AlphanumericVar(this, 8);         // 05 FILLER X(8)
    private NumericEditedVar d3OfnCveCna = new NumericEditedVar(this, "ZZ99");               // 05 D3-OFN-CVE-CNA ZZ99
    private AlphanumericVar filler27 = new AlphanumericVar(this, 6);         // 05 FILLER X(6)
    private UnsignedNumericVar d3AgtIdrGte = new UnsignedNumericVar(this, 5, 0);             // 05 D3-AGT-IDR-GTE 9(05)
    private AlphanumericVar filler28 = new AlphanumericVar(this, 12);        // 05 FILLER X(12)

    public Det22() {
        super();
        initialize();
    }

    public Det22(DataContainer parent) {
        super(parent);
    }

    public Det22(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setD3FlaDes(final String d3FlaDes) {
        this.d3FlaDes.setValue(d3FlaDes);
    }

    public void setD3FlaDes(final IAlphanumericValue d3FlaDes) {
        this.d3FlaDes.setValue(d3FlaDes);
    }

    public void setFiller21(final String filler21) {
        this.filler21.setValue(filler21);
    }

    public void setFiller21(final IAlphanumericValue filler21) {
        this.filler21.setValue(filler21);
    }

    public void setD3AgtTraEsp(final String d3AgtTraEsp) {
        this.d3AgtTraEsp.setValue(d3AgtTraEsp);
    }

    public void setD3AgtTraEsp(final IAlphanumericValue d3AgtTraEsp) {
        this.d3AgtTraEsp.setValue(d3AgtTraEsp);
    }

    public void setFiller22(final String filler22) {
        this.filler22.setValue(filler22);
    }

    public void setFiller22(final IAlphanumericValue filler22) {
        this.filler22.setValue(filler22);
    }

    public void setD3CnaFecCnx(final String d3CnaFecCnx) {
        this.d3CnaFecCnx.setValue(d3CnaFecCnx);
    }

    public void setD3CnaFecCnx(final IAlphanumericValue d3CnaFecCnx) {
        this.d3CnaFecCnx.setValue(d3CnaFecCnx);
    }

    public void setFiller23(final String filler23) {
        this.filler23.setValue(filler23);
    }

    public void setFiller23(final IAlphanumericValue filler23) {
        this.filler23.setValue(filler23);
    }

    public void setD3GnaCve(final String d3GnaCve) {
        this.d3GnaCve.setValue(d3GnaCve);
    }

    public void setD3GnaCve(final IValue d3GnaCve) {
        this.d3GnaCve.setValue(d3GnaCve);
    }

    public void setFiller24(final String filler24) {
        this.filler24.setValue(filler24);
    }

    public void setFiller24(final IAlphanumericValue filler24) {
        this.filler24.setValue(filler24);
    }

    public void setD3EsaCveFla(final String d3EsaCveFla) {
        this.d3EsaCveFla.setValue(d3EsaCveFla);
    }

    public void setD3EsaCveFla(final IValue d3EsaCveFla) {
        this.d3EsaCveFla.setValue(d3EsaCveFla);
    }

    public void setFiller25(final String filler25) {
        this.filler25.setValue(filler25);
    }

    public void setFiller25(final IAlphanumericValue filler25) {
        this.filler25.setValue(filler25);
    }

    public void setD3FlaGerZon(final String d3FlaGerZon) {
        this.d3FlaGerZon.setValue(d3FlaGerZon);
    }

    public void setD3FlaGerZon(final IValue d3FlaGerZon) {
        this.d3FlaGerZon.setValue(d3FlaGerZon);
    }

    public void setFiller26(final String filler26) {
        this.filler26.setValue(filler26);
    }

    public void setFiller26(final IAlphanumericValue filler26) {
        this.filler26.setValue(filler26);
    }

    public void setD3OfnCveCna(final String d3OfnCveCna) {
        this.d3OfnCveCna.setValue(d3OfnCveCna);
    }

    public void setD3OfnCveCna(final IValue d3OfnCveCna) {
        this.d3OfnCveCna.setValue(d3OfnCveCna);
    }

    public void setFiller27(final String filler27) {
        this.filler27.setValue(filler27);
    }

    public void setFiller27(final IAlphanumericValue filler27) {
        this.filler27.setValue(filler27);
    }

    public void setD3AgtIdrGte(final int d3AgtIdrGte) {
        this.d3AgtIdrGte.setValue(d3AgtIdrGte);
    }

    public void setD3AgtIdrGte(final BigDecimal d3AgtIdrGte) {
        this.d3AgtIdrGte.setValue(d3AgtIdrGte);
    }

    public void setD3AgtIdrGte(final INumericValue d3AgtIdrGte) {
        this.d3AgtIdrGte.setValue(d3AgtIdrGte);
    }

    public void setFiller28(final String filler28) {
        this.filler28.setValue(filler28);
    }

    public void setFiller28(final IAlphanumericValue filler28) {
        this.filler28.setValue(filler28);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.d3FlaDes.clear();
        this.filler21.clear();
        this.d3AgtTraEsp.clear();
        this.filler22.clear();
        this.d3CnaFecCnx.clear();
        this.filler23.clear();
        this.d3GnaCve.clear();
        this.filler24.clear();
        this.d3EsaCveFla.clear();
        this.filler25.clear();
        this.d3FlaGerZon.clear();
        this.filler26.clear();
        this.d3OfnCveCna.clear();
        this.filler27.clear();
        this.d3AgtIdrGte.clear();
        this.filler28.clear();
    }

}

