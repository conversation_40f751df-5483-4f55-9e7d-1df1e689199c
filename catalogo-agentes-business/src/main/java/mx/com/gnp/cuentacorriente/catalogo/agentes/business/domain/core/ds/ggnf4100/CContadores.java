package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: C-CONTADORES.
 *
 */
@Setter
@Getter
public class CContadores { // VStruct
    // Properties
    private UnsignedNumericVar cFetchDepura = new UnsignedNumericVar(12, 0);                 // 05 C-FETCH-DEPURA 9(12)
    private UnsignedNumericVar cWriteDepura = new UnsignedNumericVar(12, 0);                 // 05 C-WRITE-DEPURA 9(12)
    private UnsignedNumericVar cDeleteGfvtbrt0 = new UnsignedNumericVar(12, 0);              // 05 C-DELETE-GFVTBRT0 9(12)

    public CContadores() {
        initialize();
    }



    public void setCFetchDepura(final BigDecimal cFetchDepura) {
        this.cFetchDepura.setValue(cFetchDepura);
    }

    public void setCFetchDepura(final int cFetchDepura) {
        this.cFetchDepura.setValue(cFetchDepura);
    }

    public void setCFetchDepura(final INumericValue cFetchDepura) {
        this.cFetchDepura.setValue(cFetchDepura);
    }

    public void setCWriteDepura(final BigDecimal cWriteDepura) {
        this.cWriteDepura.setValue(cWriteDepura);
    }

    public void setCWriteDepura(final int cWriteDepura) {
        this.cWriteDepura.setValue(cWriteDepura);
    }

    public void setCWriteDepura(final INumericValue cWriteDepura) {
        this.cWriteDepura.setValue(cWriteDepura);
    }

    public void setCDeleteGfvtbrt0(final BigDecimal cDeleteGfvtbrt0) {
        this.cDeleteGfvtbrt0.setValue(cDeleteGfvtbrt0);
    }

    public void setCDeleteGfvtbrt0(final int cDeleteGfvtbrt0) {
        this.cDeleteGfvtbrt0.setValue(cDeleteGfvtbrt0);
    }

    public void setCDeleteGfvtbrt0(final INumericValue cDeleteGfvtbrt0) {
        this.cDeleteGfvtbrt0.setValue(cDeleteGfvtbrt0);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.cFetchDepura.setZero();
        this.cWriteDepura.setZero();
        this.cDeleteGfvtbrt0.setZero();
    }

}

