package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGFVTDMA0.
 *
 */
@Setter
@Getter
public class WsDclgfvtdma0 { // VStruct
    // Properties
    private int dmaAgtIdr;                                                  // 10 DMA-AGT-IDR S9(09) COMPUTATIONAL
    private int dmaDmaIdr;                                                  // 10 DMA-DMA-IDR S9(04) COMPUTATIONAL
    private String dmaDmaCae = "";                                          // 10 DMA-DMA-CAE X(40)
    private String dmaDmaNum = "";                                          // 10 DMA-DMA-NUM X(05)
    private String dmaDmaInt = "";                                          // 10 DMA-DMA-INT X(05)
    private String dmaDmaCol = "";                                          // 10 DMA-DMA-COL X(40)
    private int dmaTdmCve;                                                  // 10 DMA-TDM-CVE S9(04) COMPUTATIONAL
    private int dmaPobCve;                                                  // 10 DMA-POB-CVE S9(09) COMPUTATIONAL
    private int dmaCpoCve;                                                  // 10 DMA-CPO-CVE S9(09) COMPUTATIONAL
    private int dmaMndCve;                                                  // 10 DMA-MND-CVE S9(09) COMPUTATIONAL
    private String dmaDmaDirExt = "";                                       // 10 DMA-DMA-DIR-EXT X(200)

    public WsDclgfvtdma0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.dmaAgtIdr = 0;
        this.dmaDmaIdr = 0;
        this.dmaDmaCae = "";
        this.dmaDmaNum = "";
        this.dmaDmaInt = "";
        this.dmaDmaCol = "";
        this.dmaTdmCve = 0;
        this.dmaPobCve = 0;
        this.dmaCpoCve = 0;
        this.dmaMndCve = 0;
        this.dmaDmaDirExt = "";
    }

}

