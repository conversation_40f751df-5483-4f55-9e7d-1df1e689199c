/**
 *  JCL Program: QAD5300.
 *  Code generation type: <PERSON><PERSON>NDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.business.application.proc;

import com.base100.caravel.support.os390.jcl.context.IOs390JclProgramContext;
import com.base100.caravel.support.os390.jcl.execution.AbstractJclProgram;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class Qad5300JclProc extends AbstractJclProgram {

    public Qad5300JclProc(IOs390JclProgramContext context) {
        super(context);
    }

    public void run() {
        executeJclProcedure("QAD5300", (qad5300) -> {
            qad5300.addParameter("PROC");
            if (qad5300.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*  NOMBRE PROCESO : VERSIONAMIENTO DE AGENTES*/
                /*//*  APLICACION     : CUENTA CORRIENTE (ACTUALIZACION DE INFORMACION)*/
                /*//*  PERIODICIDAD   : DIARIA*/
                /*//*  OBJETIVO       : DE ACUERDO A LA INFORMACION GENERAL DEL AGENTE*/
                /*//*                   OBTENIDA DE DIFERENTES ENTIDADES, SE REALIZA*/
                /*//*                   (INSERT, UPDATE) A LA ENTIDAD HFA0*/
                /*//*  DEPENDENCIAS   : N/A*/
                /*//*  FECHA          : 15/OCTUBRE/2014*/
                /*//**********************************************************************/
                /*//* OBJETIVO : ELIMINA REGISTROS DUPLICADOS DEL ARCHIVO AGENTES     ****/
                /*//*            CON CAMBIOS EN REGIMEN FISCAL                        ****/
                /*//* REINICIO : PASO REINICIABLE                                     ****/
                /*//**********************************************************************/
                qad5300.execProgram("QAD5306", "SORT", (qad5306) -> {
                    qad5306.addParameter("REGION","0M");
                    qad5306.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5306.addDataDefinition("SORTWK01",  (sortwk01) -> {
                        sortwk01.addParameter("UNIT","WORKPROD");
                        sortwk01.addParameter("SPACE", "(CYL,(10,10),RLSE)");
                    });
                    qad5306.addDataDefinition("SORTWK02",  (sortwk02) -> {
                        sortwk02.addParameter("UNIT","WORKPROD");
                        sortwk02.addParameter("SPACE", "(CYL,(10,10),RLSE)");
                    });
                    qad5306.addDataDefinition("SORTWK03",  (sortwk03) -> {
                        sortwk03.addParameter("UNIT","WORKPROD");
                        sortwk03.addParameter("SPACE", "(CYL,(10,10),RLSE)");
                    });
                    qad5306.addDataDefinition("SORTIN", "PRCDS.KCDPPFIT.AGENTISR",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    qad5306.addDataDefinition("SORTOUT", "PGA.QAD5300.AGENTISR",  (sortout) -> {
                        sortout.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        sortout.addParameter("UNIT","TEMPROD");
                        sortout.addParameter("SPACE", "(CYL,(300,10),RLSE)");
                        sortout.addParameter("DCB", "(DSORG=PS,RECFM=FB,LRECL=328,BLKSIZE=0)");
                    });
                    qad5306.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QAD5303)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            /*//**********************************************************************/
            /*//* OBJETIVO DEL STEP: CREA CASCARON DE ARCHIVO DE AGENTES CON      ****/
            /*//*                    CAMBIO DE REGIMEN FISCAL Y LO LLENA          ****/
            /*//* PASO REINICIABLE                                                ****/
            /*//**********************************************************************/
            qad5300.execProgram("QAD5305", "IDCAMS", (qad5305) -> {
                qad5305.addDataDefinition("SYSPRINT",  (sysprint) -> {
                    sysprint.addParameter("SYSOUT","*");
                });
                qad5305.addDataDefinition("SYSUT1", "PGA.QAD5300.AGENTISR",  (sysut1) -> {
                    sysut1.addParameter("DISP","SHR");
                });
                qad5305.addDataDefinition("SYSUT2", "PGAV.QAD5300.AGENTISR",  (sysut2) -> {
                    sysut2.addParameter("DISP","SHR");
                });
                qad5305.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QAD5304)",  (sysin) -> {
                    sysin.addParameter("DISP","SHR");
                });
            });
            if (qad5300.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*  OBJETIVO : EJECUTA PROGRAMA DE VERSIONAMIENTO DE AGENTES       ****/
                /*//*  REINICIO : NO REINICIABLE                                      ****/
                /*//*  PROGRAMA : GGQAC600                                            ****/
                /*//**********************************************************************/
                qad5300.execProgram("QAD5304", "IKJEFT01", (qad5304) -> {
                    qad5304.addParameter("REGION","0M");
                    qad5304.addParameter("DYNAMNBR","20");
                    /*//*   ARCHIVO DE AGENTES EXCLUIDOS POR EL USUARIO*/
                    qad5304.addDataDefinition("GGQAC6I1", "PFT.EACQ593",  (ggqac6i1) -> {
                        ggqac6i1.addParameter("DISP","SHR");
                    });
                    qad5304.addDataDefinition("GGQA5QI1", "PGAV.QAD5300.AGENTISR",  (ggqa5qi1) -> {
                        ggqa5qi1.addParameter("DISP","SHR");
                    });
                    /*//*   MANTENIMIENTO A LA HFA*/
                    qad5304.addDataDefinition("AGENTISR", "PGA.QAD5300.MOVTOHFA",  (agentisr) -> {
                        agentisr.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        agentisr.addParameter("UNIT","SHAREDA");
                        agentisr.addParameter("SPACE", "(CYL,(250,50),RLSE)");
                        agentisr.addParameter("DCB", "(DSORG=PS,RECFM=FB,LRECL=785,BLKSIZE=0)");
                    });
                    qad5304.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5304.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                    qad5304.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qad5304.addDummyDataDefinition("SYSUDUMP",  (sysudump) -> {
                    });
                    /*//* CARD DEL PROGRAMA A EJECUTAR*/
                    qad5304.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(QAD5301)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qad5300.getLastReturnCode()<=4) {
                /*//**********************************************************************/
                /*//*  OBJETIVO : EXTRAE AGENTES PARA EL PROCESO QAD0700 CEDULAR*/
                /*//*  REINICIO : REINICIABLE*/
                /*//*  PROGRAMA : GGQA9A00*/
                /*//**********************************************************************/
                qad5300.execProgram("QAD5303", "IKJEFT01", (qad5303) -> {
                    qad5303.addParameter("REGION","0M");
                    qad5303.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5303.addDataDefinition("AGENTES", "PGA.QAD5300.AGENTES.ACTUAL",  (agentes) -> {
                        agentes.addParameter("DISP", "(NEW,CATLG,CATLG)");
                        agentes.addParameter("UNIT","SHAREDA");
                        agentes.addParameter("SPACE", "(CYL,(250,100),RLSE)");
                        agentes.addParameter("DCB", "(DSORG=PS,RECFM=FB,LRECL=080,BLKSIZE=0)");
                    });
                    qad5303.addDataDefinition("SYSTSIN", "PNCQP.BTCH.CARDS(QAD5305)",  (systsin) -> {
                        systsin.addParameter("DISP","SHR");
                    });
                    qad5303.addDataDefinition("SYSTSPRT",  (systsprt) -> {
                        systsprt.addParameter("SYSOUT","*");
                    });
                });
            }
            if (qad5300.getLastReturnCode()<=4) {
                /*//**/
                /*//**********************************************************************/
                /*//* OBJETIVO: GENERA SUBCONJUNTOS DEL UNIVERSO DE AGENTES*/
                /*//**/
                /*//* PASO REINICIABLE*/
                /*//**********************************************************************/
                qad5300.execProgram("QAD5302", "SORT", (qad5302) -> {
                    /*//**/
                    qad5302.addDataDefinition("SORTIN", "PGA.QAD5300.AGENTES.ACTUAL",  (sortin) -> {
                        sortin.addParameter("DISP","SHR");
                    });
                    /*//**/
                    qad5302.addDataDefinition("AGTSCEDU", "PGA.QAD5300.AGTSCEDU",  (agtscedu) -> {
                        agtscedu.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        agtscedu.addParameter("UNIT","TEMPROD");
                        agtscedu.addParameter("SPACE", "(CYL,(250,10),RLSE)");
                    });
                    qad5302.addDataDefinition("TASASIMP", "PGA.QAD5300.TASASIMP",  (tasasimp) -> {
                        tasasimp.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        tasasimp.addParameter("UNIT","TEMPROD");
                        tasasimp.addParameter("SPACE", "(CYL,(250,10),RLSE)");
                    });
                    qad5302.addDataDefinition("SORTWK01",  (sortwk01) -> {
                        sortwk01.addParameter("UNIT","WORKPROD");
                        sortwk01.addParameter("SPACE", "(CYL,(10,05),RLSE)");
                    });
                    qad5302.addDataDefinition("SORTWK02",  (sortwk02) -> {
                        sortwk02.addParameter("UNIT","WORKPROD");
                        sortwk02.addParameter("SPACE", "(CYL,(10,05),RLSE)");
                    });
                    qad5302.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5302.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QAD5306)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
            if (qad5300.getLastReturnCode()<=4) {
                /*//***/
                /*//**********************************************************************/
                /*//*  OBJETIVO : RESPALDA ARCHIVO DE MOVIMIENTOS DE COMISIONES MANUALES*/
                /*//*  REINICIO : PASO REINICIABLE*/
                /*//**********************************************************************/
                qad5300.execProgram("QAD5301", "COPIADOR", (qad5301) -> {
                    /*//*            RESPALDO.LOCAL*/
                    qad5301.addDataDefinition("OUTFILE1", "SYSBKW.QAD5300(+1)",  (outfile1) -> {
                        outfile1.addParameter("DISP", "(NEW,CATLG,DELETE)");
                        outfile1.addParameter("VOLUME", "(,,,20)");
                        outfile1.addParameter("UNIT","DDVTAV");
                        outfile1.addParameter("LABEL", "(,SL,EXPDT=99000)");
                        outfile1.addParameter("DCB", "(GDG.MODELO,BLKSIZE=32000,BUFNO=10,DSORG=PS)");
                    });
                    qad5301.addDummyDataDefinition("OUTFILE2",  (outfile2) -> {
                    });
                    /*//** RESPALDO DE ARCHIVO DE COMISIONES MANUALES INFO*/
                    qad5301.addDataDefinition("AGENTISR", "PRCDS.KCDPPFIT.AGENTISR",  (agentisr) -> {
                        agentisr.addParameter("DISP","SHR");
                    });
                    qad5301.addDataDefinition("MOVTOHFA", "PGA.QAD5300.MOVTOHFA",  (movtohfa) -> {
                        movtohfa.addParameter("DISP","SHR");
                    });
                    qad5301.addDataDefinition("AGENTES", "PGA.QAD5300.AGENTES.ACTUAL",  (agentes) -> {
                        agentes.addParameter("DISP","SHR");
                    });
                    qad5301.addDataDefinition("SYSPRINT",  (sysprint) -> {
                        sysprint.addParameter("SYSOUT","*");
                    });
                    qad5301.addDataDefinition("SYSOUT",  (sysout) -> {
                        sysout.addParameter("SYSOUT","*");
                    });
                    qad5301.addDataDefinition("SYSUDUMP",  (sysudump) -> {
                        sysudump.addParameter("SYSOUT","*");
                    });
                    /*//*            ARCHIVOS.A.RESPALDAR*/
                    qad5301.addDataDefinition("SYSIN", "PNCQP.BTCH.CARDS(QAD5302)",  (sysin) -> {
                        sysin.addParameter("DISP","SHR");
                    });
                });
            }
        });
    }
}
