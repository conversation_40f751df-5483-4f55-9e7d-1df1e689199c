package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsNombreGz;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsGteR;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsCpoCveR;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsAuxPhonen;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsAuxTelefono;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsAuxNombre;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre.WsAuxAPat;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre.WsAuxAMat;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre.WsAuxLasName;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsAuxDate;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsAuxDateSinG;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsChain;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsAgentId;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsFlaNum;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsFlaNumGte;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.WsFechaSys;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AYUDA.
 *
 */
// 698      *                                                                 
// 699      *---------------- OTRAS VARIABLES -------------------------------*
@Setter
@Getter
public class WsAyuda { // VStruct
    // Properties
    private WsNombreGz wsNombreGz = new  WsNombreGz();                       // 05 WS-NOMBRE-GZ UsedAsParameter
    private AlphanumericVar wsGteA = new AlphanumericVar(10);                // 05 WS-GTE-A X(10) REDEFINED BY WS-GTE-R
    private WsGteR wsGteR = new  WsGteR(null, this.wsGteA);                  // 05 WS-GTE-R REDEFINES WS-GTE-A
    private int wsCpoCveB;                                                  // 05 WS-CPO-CVE-B 9(09) COMPUTATIONAL-3
    private UnsignedNumericVar wsCpoCve = new UnsignedNumericVar(9, 0);      // 05 WS-CPO-CVE 9(09) REDEFINED BY WS-CPO-CVE-R
    private WsCpoCveR wsCpoCveR = new  WsCpoCveR(null, this.wsCpoCve);       // 05 WS-CPO-CVE-R REDEFINES WS-CPO-CVE
    private int wsOf;                                                       // 05 WS-OF 9(03)
    private WsAuxPhonen wsAuxPhonen = new  WsAuxPhonen();                    // 05 WS-AUX-PHONEN UsedAsParameter
    private WsAuxTelefono wsAuxTelefono = new  WsAuxTelefono();              // 05 WS-AUX-TELEFONO
    private WsAuxNombre wsAuxNombre = new  WsAuxNombre();                    // 05 WS-AUX-NOMBRE
    private WsAuxDate wsAuxDate = new  WsAuxDate();                          // 05 WS-AUX-DATE
    private WsAuxDateSinG wsAuxDateSinG = new  WsAuxDateSinG();              // 05 WS-AUX-DATE-SIN-G UsedAsParameter
    private UnsignedNumericVar wsTelAux = new UnsignedNumericVar(14, 0);     // 05 WS-TEL-AUX 9(14)
    private String wsEdoInter = "";                                         // 05 WS-EDO-INTER X(03)
    private int wsDmaIdr;                                                   // 05 WS-DMA-IDR 9(01)
    private String wsOpcion = "";                                           // 05 WS-OPCION X(01)
    // Level 88 - Conditional names
    private static final String WS_AGTE_GTE = "A";
    private static final String WS_OFICINA = "O";
    private static final String WS_CR = "C";

    private AlphanumericVar wsFlaGerZon = new AlphanumericVar(3);            // 05 WS-FLA-GER-ZON X(03) REDEFINED BY WS-FLA-GER-ZON-N
    private UnsignedNumericVar wsFlaGerZonN = new UnsignedNumericVar(null, this.wsFlaGerZon, 3, 0);          // 05 WS-FLA-GER-ZON-N REDEFINES WS-FLA-GER-ZON 9(03)
    private WsChain wsChain = new  WsChain();                                // 05 WS-CHAIN REDEFINED BY WS-CHAIN-N
    private UnsignedNumericVar wsChainN = new UnsignedNumericVar(null, this.wsChain, 10, 0);                 // 05 WS-CHAIN-N REDEFINES WS-CHAIN 9(10)
    private WsAgentId wsAgentId = new  WsAgentId();                          // 05 WS-AGENT-ID REDEFINED BY WS-AGENT-ID-N UsedAsParameter
    private UnsignedNumericVar wsAgentIdN = new UnsignedNumericVar(null, this.wsAgentId, 5, 0);              // 05 WS-AGENT-ID-N REDEFINES WS-AGENT-ID 9(05)
    private AlphanumericVar wsUniqueAgentCode = new AlphanumericVar(8);      // 05 WS-UNIQUE-AGENT-CODE X(08) REDEFINED BY WS-UNIQUE-AGENT-CODE-N
    private UnsignedNumericVar wsUniqueAgentCodeN = new UnsignedNumericVar(null, this.wsUniqueAgentCode, 8, 0);              // 05 WS-UNIQUE-AGENT-CODE-N REDEFINES WS-UNIQUE-AGENT-CODE 9(08)
    private String wsIndNogte = "";                                         // 05 WS-IND-NOGTE X(01)
    private WsFlaNum wsFlaNum = new  WsFlaNum();                             // 05 WS-FLA-NUM
    private WsFlaNumGte wsFlaNumGte = new  WsFlaNumGte();                    // 05 WS-FLA-NUM-GTE
    private WsFechaSys wsFechaSys = new  WsFechaSys();                       // 05 WS-FECHA-SYS UsedAsParameter

    public WsAyuda() {
        initialize();
    }



    public void setWsGteA(final String wsGteA) {
        this.wsGteA.setValue(wsGteA);
    }

    public void setWsGteA(final IAlphanumericValue wsGteA) {
        this.wsGteA.setValue(wsGteA);
    }

    public void setWsCpoCve(final int wsCpoCve) {
        this.wsCpoCve.setValue(wsCpoCve);
    }

    public void setWsCpoCve(final BigDecimal wsCpoCve) {
        this.wsCpoCve.setValue(wsCpoCve);
    }

    public void setWsCpoCve(final INumericValue wsCpoCve) {
        this.wsCpoCve.setValue(wsCpoCve);
    }

    public void setWsTelAux(final BigDecimal wsTelAux) {
        this.wsTelAux.setValue(wsTelAux);
    }

    public void setWsTelAux(final int wsTelAux) {
        this.wsTelAux.setValue(wsTelAux);
    }

    public void setWsTelAux(final INumericValue wsTelAux) {
        this.wsTelAux.setValue(wsTelAux);
    }

    // Conditionals (88) - WS-OPCION
    public boolean isWsAgteGte() {
        return this.wsOpcion.equals(WS_AGTE_GTE);
    }

    public void setWsAgteGte() {
        this.wsOpcion = WS_AGTE_GTE;
    }

    public boolean isWsOficina() {
        return this.wsOpcion.equals(WS_OFICINA);
    }

    public void setWsOficina() {
        this.wsOpcion = WS_OFICINA;
    }

    public boolean isWsCr() {
        return this.wsOpcion.equals(WS_CR);
    }

    public void setWsCr() {
        this.wsOpcion = WS_CR;
    }


    public void setWsFlaGerZon(final String wsFlaGerZon) {
        this.wsFlaGerZon.setValue(wsFlaGerZon);
    }

    public void setWsFlaGerZon(final IAlphanumericValue wsFlaGerZon) {
        this.wsFlaGerZon.setValue(wsFlaGerZon);
    }

    public void setWsFlaGerZonN(final int wsFlaGerZonN) {
        this.wsFlaGerZonN.setValue(wsFlaGerZonN);
    }

    public void setWsFlaGerZonN(final BigDecimal wsFlaGerZonN) {
        this.wsFlaGerZonN.setValue(wsFlaGerZonN);
    }

    public void setWsFlaGerZonN(final INumericValue wsFlaGerZonN) {
        this.wsFlaGerZonN.setValue(wsFlaGerZonN);
    }

    public void setWsChainN(final BigDecimal wsChainN) {
        this.wsChainN.setValue(wsChainN);
    }

    public void setWsChainN(final int wsChainN) {
        this.wsChainN.setValue(wsChainN);
    }

    public void setWsChainN(final INumericValue wsChainN) {
        this.wsChainN.setValue(wsChainN);
    }

    public void setWsAgentIdN(final int wsAgentIdN) {
        this.wsAgentIdN.setValue(wsAgentIdN);
    }

    public void setWsAgentIdN(final BigDecimal wsAgentIdN) {
        this.wsAgentIdN.setValue(wsAgentIdN);
    }

    public void setWsAgentIdN(final INumericValue wsAgentIdN) {
        this.wsAgentIdN.setValue(wsAgentIdN);
    }

    public void setWsUniqueAgentCode(final String wsUniqueAgentCode) {
        this.wsUniqueAgentCode.setValue(wsUniqueAgentCode);
    }

    public void setWsUniqueAgentCode(final IAlphanumericValue wsUniqueAgentCode) {
        this.wsUniqueAgentCode.setValue(wsUniqueAgentCode);
    }

    public void setWsUniqueAgentCodeN(final int wsUniqueAgentCodeN) {
        this.wsUniqueAgentCodeN.setValue(wsUniqueAgentCodeN);
    }

    public void setWsUniqueAgentCodeN(final BigDecimal wsUniqueAgentCodeN) {
        this.wsUniqueAgentCodeN.setValue(wsUniqueAgentCodeN);
    }

    public void setWsUniqueAgentCodeN(final INumericValue wsUniqueAgentCodeN) {
        this.wsUniqueAgentCodeN.setValue(wsUniqueAgentCodeN);
    }


    // Deeper properties

    // Property: wsNombreGz.wsNombreGzk -> 10 WS-NOMBRE-GZK X(24)
    public AlphanumericVar getWsNombreGzk() {
        return this.wsNombreGz.getWsNombreGzk();
    }

    public void setWsNombreGzk(final String wsNombreGzk) {
        this.wsNombreGz.setWsNombreGzk(wsNombreGzk);
    }

    public void setWsNombreGzk(final IAlphanumericValue wsNombreGzk) {
        this.wsNombreGz.setWsNombreGzk(wsNombreGzk);
    }

    // Property: wsNombreGz.wsNombreGzNo -> 10 WS-NOMBRE-GZ-NO X(03)
    public AlphanumericVar getWsNombreGzNo() {
        return this.wsNombreGz.getWsNombreGzNo();
    }

    public void setWsNombreGzNo(final String wsNombreGzNo) {
        this.wsNombreGz.setWsNombreGzNo(wsNombreGzNo);
    }

    public void setWsNombreGzNo(final IAlphanumericValue wsNombreGzNo) {
        this.wsNombreGz.setWsNombreGzNo(wsNombreGzNo);
    }

    // Property: wsNombreGz.wsNombreFil -> 10 WS-NOMBRE-FIL X(08)
    public AlphanumericVar getWsNombreFil() {
        return this.wsNombreGz.getWsNombreFil();
    }

    public void setWsNombreFil(final String wsNombreFil) {
        this.wsNombreGz.setWsNombreFil(wsNombreFil);
    }

    public void setWsNombreFil(final IAlphanumericValue wsNombreFil) {
        this.wsNombreGz.setWsNombreFil(wsNombreFil);
    }


    // Deeper properties

    // Property: wsGteR.wsGteX -> 10 WS-GTE-X X(03)
    public AlphanumericVar getWsGteX() {
        return this.wsGteR.getWsGteX();
    }

    public void setWsGteX(final String wsGteX) {
        this.wsGteR.setWsGteX(wsGteX);
    }

    public void setWsGteX(final IAlphanumericValue wsGteX) {
        this.wsGteR.setWsGteX(wsGteX);
    }

    // Property: wsGteR.wsGteN -> 10 WS-GTE-N 9(07)
    public UnsignedNumericVar getWsGteN() {
        return this.wsGteR.getWsGteN();
    }

    public void setWsGteN(final int wsGteN) {
        this.wsGteR.setWsGteN(wsGteN);
    }

    public void setWsGteN(final INumericValue wsGteN) {
        this.wsGteR.setWsGteN(wsGteN);
    }


    // Deeper properties

    // Property: wsCpoCveR.wsCpoCve_4 -> 10 WS-CPO-CVE-4 X(04)
    public AlphanumericVar getWsCpoCve_4() {
        return this.wsCpoCveR.getWsCpoCve_4();
    }

    public void setWsCpoCve_4(final String wsCpoCve_4) {
        this.wsCpoCveR.setWsCpoCve_4(wsCpoCve_4);
    }

    public void setWsCpoCve_4(final IAlphanumericValue wsCpoCve_4) {
        this.wsCpoCveR.setWsCpoCve_4(wsCpoCve_4);
    }

    // Property: wsCpoCveR.wsCpoCve_5 -> 10 WS-CPO-CVE-5 X(05)
    public AlphanumericVar getWsCpoCve_5() {
        return this.wsCpoCveR.getWsCpoCve_5();
    }

    public void setWsCpoCve_5(final String wsCpoCve_5) {
        this.wsCpoCveR.setWsCpoCve_5(wsCpoCve_5);
    }

    public void setWsCpoCve_5(final IAlphanumericValue wsCpoCve_5) {
        this.wsCpoCveR.setWsCpoCve_5(wsCpoCve_5);
    }


    // Deeper properties

    // Property: wsAuxPhonen.wsAuxPhone -> 10 WS-AUX-PHONE X(01) [14]
    public AlphanumericVarArray getWsAuxPhone() {
        return this.wsAuxPhonen.getWsAuxPhone();
    }

    public void setWsAuxPhone(final String wsAuxPhone) {
        this.wsAuxPhonen.setWsAuxPhone(wsAuxPhone);
    }

    public void setWsAuxPhone(final AlphanumericVarArray wsAuxPhone) {
        this.wsAuxPhonen.setWsAuxPhone(wsAuxPhone);
    }


    // Deeper properties

    // Property: wsAuxTelefono.wsAuxTelX -> 10 WS-AUX-TEL-X X(01) [60]
    public AlphanumericVarArray getWsAuxTelX() {
        return this.wsAuxTelefono.getWsAuxTelX();
    }

    public void setWsAuxTelX(final String wsAuxTelX) {
        this.wsAuxTelefono.setWsAuxTelX(wsAuxTelX);
    }

    public void setWsAuxTelX(final AlphanumericVarArray wsAuxTelX) {
        this.wsAuxTelefono.setWsAuxTelX(wsAuxTelX);
    }


    // Deeper properties

    // Property: wsAuxNombre.wsAuxAPat -> 10 WS-AUX-A-PAT
    public WsAuxAPat getWsAuxAPat() {
        return this.wsAuxNombre.getWsAuxAPat();
    }

    // Property: wsAuxNombre.wsAuxAPat.wsAuxApPat -> 15 WS-AUX-AP-PAT X(01) [40]
    public AlphanumericVarArray getWsAuxApPat() {
        return this.wsAuxNombre.getWsAuxAPat().getWsAuxApPat();
    }

    public void setWsAuxApPat(final String wsAuxApPat) {
        this.wsAuxNombre.getWsAuxAPat().setWsAuxApPat(wsAuxApPat);
    }

    public void setWsAuxApPat(final AlphanumericVarArray wsAuxApPat) {
        this.wsAuxNombre.getWsAuxAPat().setWsAuxApPat(wsAuxApPat);
    }

    // Property: wsAuxNombre.wsAuxAMat -> 10 WS-AUX-A-MAT
    public WsAuxAMat getWsAuxAMat() {
        return this.wsAuxNombre.getWsAuxAMat();
    }

    // Property: wsAuxNombre.wsAuxAMat.wsAuxApMat -> 15 WS-AUX-AP-MAT X(01) [30]
    public AlphanumericVarArray getWsAuxApMat() {
        return this.wsAuxNombre.getWsAuxAMat().getWsAuxApMat();
    }

    public void setWsAuxApMat(final String wsAuxApMat) {
        this.wsAuxNombre.getWsAuxAMat().setWsAuxApMat(wsAuxApMat);
    }

    public void setWsAuxApMat(final AlphanumericVarArray wsAuxApMat) {
        this.wsAuxNombre.getWsAuxAMat().setWsAuxApMat(wsAuxApMat);
    }

    // Property: wsAuxNombre.wsAuxLasName -> 10 WS-AUX-LAS-NAME UsedAsParameter
    public WsAuxLasName getWsAuxLasName() {
        return this.wsAuxNombre.getWsAuxLasName();
    }

    // Property: wsAuxNombre.wsAuxLasName.wsAuxLastName -> 15 WS-AUX-LAST-NAME X(01) [40]
    public AlphanumericVarArray getWsAuxLastName() {
        return this.wsAuxNombre.getWsAuxLasName().getWsAuxLastName();
    }

    public void setWsAuxLastName(final String wsAuxLastName) {
        this.wsAuxNombre.getWsAuxLasName().setWsAuxLastName(wsAuxLastName);
    }

    public void setWsAuxLastName(final AlphanumericVarArray wsAuxLastName) {
        this.wsAuxNombre.getWsAuxLasName().setWsAuxLastName(wsAuxLastName);
    }


    // Deeper properties

    // Property: wsAuxDate.wsAuxDateAaaa -> 10 WS-AUX-DATE-AAAA X(04)
    public AlphanumericVar getWsAuxDateAaaa() {
        return this.wsAuxDate.getWsAuxDateAaaa();
    }

    public void setWsAuxDateAaaa(final String wsAuxDateAaaa) {
        this.wsAuxDate.setWsAuxDateAaaa(wsAuxDateAaaa);
    }

    public void setWsAuxDateAaaa(final IAlphanumericValue wsAuxDateAaaa) {
        this.wsAuxDate.setWsAuxDateAaaa(wsAuxDateAaaa);
    }

    // Property: wsAuxDate.filler1 -> 10 FILLER X(01)
    public AlphanumericVar getFiller1() {
        return this.wsAuxDate.getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.wsAuxDate.setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.wsAuxDate.setFiller1(filler1);
    }

    // Property: wsAuxDate.wsAuxDateMm -> 10 WS-AUX-DATE-MM X(02)
    public AlphanumericVar getWsAuxDateMm() {
        return this.wsAuxDate.getWsAuxDateMm();
    }

    public void setWsAuxDateMm(final String wsAuxDateMm) {
        this.wsAuxDate.setWsAuxDateMm(wsAuxDateMm);
    }

    public void setWsAuxDateMm(final IAlphanumericValue wsAuxDateMm) {
        this.wsAuxDate.setWsAuxDateMm(wsAuxDateMm);
    }

    // Property: wsAuxDate.filler2 -> 10 FILLER X(01)
    public AlphanumericVar getFiller2() {
        return this.wsAuxDate.getFiller2();
    }

    public void setFiller2(final String filler2) {
        this.wsAuxDate.setFiller2(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.wsAuxDate.setFiller2(filler2);
    }

    // Property: wsAuxDate.wsAuxDateDd -> 10 WS-AUX-DATE-DD X(02)
    public AlphanumericVar getWsAuxDateDd() {
        return this.wsAuxDate.getWsAuxDateDd();
    }

    public void setWsAuxDateDd(final String wsAuxDateDd) {
        this.wsAuxDate.setWsAuxDateDd(wsAuxDateDd);
    }

    public void setWsAuxDateDd(final IAlphanumericValue wsAuxDateDd) {
        this.wsAuxDate.setWsAuxDateDd(wsAuxDateDd);
    }


    // Deeper properties

    // Property: wsAuxDateSinG.wsAuxDateSinGAaaa -> 10 WS-AUX-DATE-SIN-G-AAAA 9(04)
    public UnsignedNumericVar getWsAuxDateSinGAaaa() {
        return this.wsAuxDateSinG.getWsAuxDateSinGAaaa();
    }

    public void setWsAuxDateSinGAaaa(final int wsAuxDateSinGAaaa) {
        this.wsAuxDateSinG.setWsAuxDateSinGAaaa(wsAuxDateSinGAaaa);
    }

    public void setWsAuxDateSinGAaaa(final INumericValue wsAuxDateSinGAaaa) {
        this.wsAuxDateSinG.setWsAuxDateSinGAaaa(wsAuxDateSinGAaaa);
    }

    // Property: wsAuxDateSinG.wsAuxDateSinGMm -> 10 WS-AUX-DATE-SIN-G-MM 9(02)
    public UnsignedNumericVar getWsAuxDateSinGMm() {
        return this.wsAuxDateSinG.getWsAuxDateSinGMm();
    }

    public void setWsAuxDateSinGMm(final int wsAuxDateSinGMm) {
        this.wsAuxDateSinG.setWsAuxDateSinGMm(wsAuxDateSinGMm);
    }

    public void setWsAuxDateSinGMm(final INumericValue wsAuxDateSinGMm) {
        this.wsAuxDateSinG.setWsAuxDateSinGMm(wsAuxDateSinGMm);
    }

    // Property: wsAuxDateSinG.wsAuxDateSinGDd -> 10 WS-AUX-DATE-SIN-G-DD 9(02)
    public UnsignedNumericVar getWsAuxDateSinGDd() {
        return this.wsAuxDateSinG.getWsAuxDateSinGDd();
    }

    public void setWsAuxDateSinGDd(final int wsAuxDateSinGDd) {
        this.wsAuxDateSinG.setWsAuxDateSinGDd(wsAuxDateSinGDd);
    }

    public void setWsAuxDateSinGDd(final INumericValue wsAuxDateSinGDd) {
        this.wsAuxDateSinG.setWsAuxDateSinGDd(wsAuxDateSinGDd);
    }


    // Deeper properties

    // Property: wsChain.wsChain1 -> 10 WS-CHAIN1 X(05)
    public AlphanumericVar getWsChain1() {
        return this.wsChain.getWsChain1();
    }

    public void setWsChain1(final String wsChain1) {
        this.wsChain.setWsChain1(wsChain1);
    }

    public void setWsChain1(final IAlphanumericValue wsChain1) {
        this.wsChain.setWsChain1(wsChain1);
    }

    // Property: wsChain.wsChain2 -> 10 WS-CHAIN2 X(05)
    public AlphanumericVar getWsChain2() {
        return this.wsChain.getWsChain2();
    }

    public void setWsChain2(final String wsChain2) {
        this.wsChain.setWsChain2(wsChain2);
    }

    public void setWsChain2(final IAlphanumericValue wsChain2) {
        this.wsChain.setWsChain2(wsChain2);
    }


    // Deeper properties

    // Property: wsAgentId.wsAgentId2 -> 10 WS-AGENT-ID2 X(02)
    public AlphanumericVar getWsAgentId2() {
        return this.wsAgentId.getWsAgentId2();
    }

    public void setWsAgentId2(final String wsAgentId2) {
        this.wsAgentId.setWsAgentId2(wsAgentId2);
    }

    public void setWsAgentId2(final IAlphanumericValue wsAgentId2) {
        this.wsAgentId.setWsAgentId2(wsAgentId2);
    }

    // Property: wsAgentId.wsAgentId3 -> 10 WS-AGENT-ID3 X(03)
    public AlphanumericVar getWsAgentId3() {
        return this.wsAgentId.getWsAgentId3();
    }

    public void setWsAgentId3(final String wsAgentId3) {
        this.wsAgentId.setWsAgentId3(wsAgentId3);
    }

    public void setWsAgentId3(final IAlphanumericValue wsAgentId3) {
        this.wsAgentId.setWsAgentId3(wsAgentId3);
    }


    // Deeper properties

    // Property: wsFlaNum.wsFlaNum_1 -> 10 WS-FLA-NUM-1 X(01)
    public AlphanumericVar getWsFlaNum_1() {
        return this.wsFlaNum.getWsFlaNum_1();
    }

    public void setWsFlaNum_1(final String wsFlaNum_1) {
        this.wsFlaNum.setWsFlaNum_1(wsFlaNum_1);
    }

    public void setWsFlaNum_1(final IAlphanumericValue wsFlaNum_1) {
        this.wsFlaNum.setWsFlaNum_1(wsFlaNum_1);
    }

    // Property: wsFlaNum.wsFlaNumX -> 10 WS-FLA-NUM-X 9(02)
    public UnsignedNumericVar getWsFlaNumX() {
        return this.wsFlaNum.getWsFlaNumX();
    }

    public void setWsFlaNumX(final int wsFlaNumX) {
        this.wsFlaNum.setWsFlaNumX(wsFlaNumX);
    }

    public void setWsFlaNumX(final INumericValue wsFlaNumX) {
        this.wsFlaNum.setWsFlaNumX(wsFlaNumX);
    }

    // Property: wsFlaNum.wsFlaNum_2 -> 10 WS-FLA-NUM-2 9(05)
    public UnsignedNumericVar getWsFlaNum_2() {
        return this.wsFlaNum.getWsFlaNum_2();
    }

    public void setWsFlaNum_2(final int wsFlaNum_2) {
        this.wsFlaNum.setWsFlaNum_2(wsFlaNum_2);
    }

    public void setWsFlaNum_2(final INumericValue wsFlaNum_2) {
        this.wsFlaNum.setWsFlaNum_2(wsFlaNum_2);
    }


    // Deeper properties

    // Property: wsFlaNumGte.wsFlaNumGte_1 -> 10 WS-FLA-NUM-GTE-1 X(01)
    public AlphanumericVar getWsFlaNumGte_1() {
        return this.wsFlaNumGte.getWsFlaNumGte_1();
    }

    public void setWsFlaNumGte_1(final String wsFlaNumGte_1) {
        this.wsFlaNumGte.setWsFlaNumGte_1(wsFlaNumGte_1);
    }

    public void setWsFlaNumGte_1(final IAlphanumericValue wsFlaNumGte_1) {
        this.wsFlaNumGte.setWsFlaNumGte_1(wsFlaNumGte_1);
    }

    // Property: wsFlaNumGte.wsFlaNumGteX -> 10 WS-FLA-NUM-GTE-X X(02)
    public AlphanumericVar getWsFlaNumGteX() {
        return this.wsFlaNumGte.getWsFlaNumGteX();
    }

    public void setWsFlaNumGteX(final String wsFlaNumGteX) {
        this.wsFlaNumGte.setWsFlaNumGteX(wsFlaNumGteX);
    }

    public void setWsFlaNumGteX(final IAlphanumericValue wsFlaNumGteX) {
        this.wsFlaNumGte.setWsFlaNumGteX(wsFlaNumGteX);
    }

    // Property: wsFlaNumGte.wsFlaNumGte_2 -> 10 WS-FLA-NUM-GTE-2 9(05)
    public UnsignedNumericVar getWsFlaNumGte_2() {
        return this.wsFlaNumGte.getWsFlaNumGte_2();
    }

    public void setWsFlaNumGte_2(final int wsFlaNumGte_2) {
        this.wsFlaNumGte.setWsFlaNumGte_2(wsFlaNumGte_2);
    }

    public void setWsFlaNumGte_2(final INumericValue wsFlaNumGte_2) {
        this.wsFlaNumGte.setWsFlaNumGte_2(wsFlaNumGte_2);
    }


    // Deeper properties

    // Property: wsFechaSys.wsAaSys -> 10 WS-AA-SYS 9(04)
    public UnsignedNumericVar getWsAaSys() {
        return this.wsFechaSys.getWsAaSys();
    }

    public void setWsAaSys(final int wsAaSys) {
        this.wsFechaSys.setWsAaSys(wsAaSys);
    }

    public void setWsAaSys(final INumericValue wsAaSys) {
        this.wsFechaSys.setWsAaSys(wsAaSys);
    }

    // Property: wsFechaSys.wsMmSys -> 10 WS-MM-SYS 9(02)
    public UnsignedNumericVar getWsMmSys() {
        return this.wsFechaSys.getWsMmSys();
    }

    public void setWsMmSys(final int wsMmSys) {
        this.wsFechaSys.setWsMmSys(wsMmSys);
    }

    public void setWsMmSys(final INumericValue wsMmSys) {
        this.wsFechaSys.setWsMmSys(wsMmSys);
    }

    // Property: wsFechaSys.wsDdSys -> 10 WS-DD-SYS 9(02)
    public UnsignedNumericVar getWsDdSys() {
        return this.wsFechaSys.getWsDdSys();
    }

    public void setWsDdSys(final int wsDdSys) {
        this.wsFechaSys.setWsDdSys(wsDdSys);
    }

    public void setWsDdSys(final INumericValue wsDdSys) {
        this.wsFechaSys.setWsDdSys(wsDdSys);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsNombreGz.initialize();
        this.wsGteA.clear();
        this.wsCpoCveB = 0;
        this.wsCpoCve.clear();
        this.wsOf = 0;
        this.wsAuxPhonen.initialize();
        this.wsAuxTelefono.initialize();
        this.wsAuxNombre.initialize();
        this.wsAuxDate.initialize();
        this.wsAuxDateSinG.initialize();
        this.wsTelAux.clear();
        this.wsEdoInter = "";
        this.wsDmaIdr = 0;
        this.wsOpcion = "";
        this.wsFlaGerZon.clear();
        this.wsChain.initialize();
        this.wsAgentId.initialize();
        this.wsUniqueAgentCode.clear();
        this.wsIndNogte = "";
        this.wsFlaNum.initialize();
        this.wsFlaNumGte.initialize();
        this.wsFechaSys.initialize();
    }

}

