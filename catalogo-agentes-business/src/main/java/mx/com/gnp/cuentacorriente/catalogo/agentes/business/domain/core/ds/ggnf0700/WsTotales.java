package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TOTALES.
 *
 */
// 714044600*        '78ZZ991234567812345                         '.          04460002
// 715044700*                                                                 04470002
// 716044800*                                                                 04480002
// 717044900*                                                                 04490002
// 718045000*                                                                 04500002
@Setter
@Getter
public class WsTotales { // VStruct
    // Properties
    private int totLeidos = 0;                                              // 03 TOT-LEIDOS 9(06)
    private int totGrabados = 0;                                            // 03 TOT-GRABADOS 9(06)
    private int totCambios = 0;                                             // 03 TOT-CAMBIOS 9(06)
    private int totDuplicados = 0;                                          // 03 TOT-DUPLICADOS 9(06)

    public WsTotales() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.totLeidos = 0;
        this.totGrabados = 0;
        this.totCambios = 0;
        this.totDuplicados = 0;
    }

}

