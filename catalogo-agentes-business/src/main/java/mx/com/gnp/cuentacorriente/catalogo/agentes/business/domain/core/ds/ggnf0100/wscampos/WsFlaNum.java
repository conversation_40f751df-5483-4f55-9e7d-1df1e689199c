package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FLA-NUM.
 *
 */
// 1208074000*                                                                 07400000
@Setter
@Getter
public class WsFlaNum extends DataStruct {
    // Properties
    private AlphanumericVar wsFlaNum0 = new AlphanumericVar(this, 1);        // 05 WS-FLA-NUM0 X
    private UnsignedNumericVar wsFlaNum1 = new UnsignedNumericVar(this, 7, 0);               // 05 WS-FLA-NUM1 9(7)

    public WsFlaNum() {
        super();
        initialize();
    }

    public WsFlaNum(DataContainer parent) {
        super(parent);
    }

    public WsFlaNum(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFlaNum0(final String wsFlaNum0) {
        this.wsFlaNum0.setValue(wsFlaNum0);
    }

    public void setWsFlaNum0(final IAlphanumericValue wsFlaNum0) {
        this.wsFlaNum0.setValue(wsFlaNum0);
    }

    public void setWsFlaNum1(final int wsFlaNum1) {
        this.wsFlaNum1.setValue(wsFlaNum1);
    }

    public void setWsFlaNum1(final BigDecimal wsFlaNum1) {
        this.wsFlaNum1.setValue(wsFlaNum1);
    }

    public void setWsFlaNum1(final INumericValue wsFlaNum1) {
        this.wsFlaNum1.setValue(wsFlaNum1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFlaNum0.clear();
        this.wsFlaNum1.clear();
    }

}

