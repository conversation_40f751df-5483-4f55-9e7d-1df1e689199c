package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-ARCHCNAH.
 *
 */
@Setter
@Getter
public class RegArchcnah extends DataStruct {
    // Properties
    private UnsignedNumericVar agtIdr2 = new UnsignedNumericVar(this, 9, 0);                 // 05 AGT-IDR2 9(09)
    private AlphanumericVar agtFecCamRazSo2 = new AlphanumericVar(this, 10);                 // 05 AGT-FEC-CAM-RAZ-SO2 X(10)
    private AlphanumericVar empCve2 = new AlphanumericVar(this, 5);          // 05 EMP-CVE2 X(05)
    private UnsignedNumericVar cnaNum2 = new UnsignedNumericVar(this, 5, 0);                 // 05 CNA-NUM2 9(05)
    private AlphanumericVar cnaTpoCnr2 = new AlphanumericVar(this, 2);       // 05 CNA-TPO-CNR2 X(02)
    private AlphanumericVar cnaUltCia2 = new AlphanumericVar(this, 30);      // 05 CNA-ULT-CIA2 X(30)
    private AlphanumericVar cnaFecCnx2 = new AlphanumericVar(this, 10);      // 05 CNA-FEC-CNX2 X(10)
    private AlphanumericVar cnaCodIpu2 = new AlphanumericVar(this, 1);       // 05 CNA-COD-IPU2 X(01)
    private AlphanumericVar filler01 = new AlphanumericVar(this, 1);         // 05 FILLER01 X(01)
    private UnsignedNumericVar cnaPjeIva23 = new UnsignedNumericVar(this, 3, 0);             // 05 CNA-PJE-IVA23 9(03)
    private AlphanumericVar filler02 = new AlphanumericVar(this, 1);         // 05 FILLER02 X(01)
    private UnsignedNumericVar cnaPjeIva22 = new UnsignedNumericVar(this, 2, 0);             // 05 CNA-PJE-IVA22 9(02)
    private AlphanumericVar filler03 = new AlphanumericVar(this, 1);         // 05 FILLER03 X(01)
    private UnsignedNumericVar cnaPjeIsr23 = new UnsignedNumericVar(this, 3, 0);             // 05 CNA-PJE-ISR23 9(03)
    private AlphanumericVar filler04 = new AlphanumericVar(this, 1);         // 05 FILLER04 X(01)
    private UnsignedNumericVar cnaPjeIsr22 = new UnsignedNumericVar(this, 2, 0);             // 05 CNA-PJE-ISR22 9(02)
    private AlphanumericVar cnaComNiv2 = new AlphanumericVar(this, 1);       // 05 CNA-COM-NIV2 X(01)
    private AlphanumericVar filler05 = new AlphanumericVar(this, 1);         // 05 FILLER05 X(01)
    private UnsignedNumericVar cnaImpComNiv211 = new UnsignedNumericVar(this, 11, 0);        // 05 CNA-IMP-COM-NIV211 9(11)
    private AlphanumericVar filler06 = new AlphanumericVar(this, 1);         // 05 FILLER06 X(01)
    private UnsignedNumericVar cnaImpComNiv22 = new UnsignedNumericVar(this, 2, 0);          // 05 CNA-IMP-COM-NIV22 9(02)
    private AlphanumericVar cnaNumFza2 = new AlphanumericVar(this, 10);      // 05 CNA-NUM-FZA2 X(10)
    private AlphanumericVar cnaFecIniFza2 = new AlphanumericVar(this, 10);   // 05 CNA-FEC-INI-FZA2 X(10)
    private AlphanumericVar cnaFecFinFza2 = new AlphanumericVar(this, 10);   // 05 CNA-FEC-FIN-FZA2 X(10)
    private AlphanumericVar filler07 = new AlphanumericVar(this, 1);         // 05 FILLER07 X(01)
    private UnsignedNumericVar cnaImpFza211 = new UnsignedNumericVar(this, 11, 0);           // 05 CNA-IMP-FZA211 9(11)
    private AlphanumericVar filler08 = new AlphanumericVar(this, 1);         // 05 FILLER08 X(01)
    private UnsignedNumericVar cnaImpFza22 = new UnsignedNumericVar(this, 2, 0);             // 05 CNA-IMP-FZA22 9(02)
    private AlphanumericVar cnaIdrFav2 = new AlphanumericVar(this, 1);       // 05 CNA-IDR-FAV2 X(01)
    private AlphanumericVar filler09 = new AlphanumericVar(this, 1);         // 05 FILLER09 X(01)
    private UnsignedNumericVar cnaPjeFav23 = new UnsignedNumericVar(this, 3, 0);             // 05 CNA-PJE-FAV23 9(03)
    private AlphanumericVar filler10 = new AlphanumericVar(this, 1);         // 05 FILLER10 X(01)
    private UnsignedNumericVar cnaPjeFav24 = new UnsignedNumericVar(this, 4, 0);             // 05 CNA-PJE-FAV24 9(04)
    private UnsignedNumericVar ppgCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 PPG-CVE2 9(05)
    private UnsignedNumericVar cpgCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 CPG-CVE2 9(05)
    private UnsignedNumericVar cdeCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 CDE-CVE2 9(05)
    private UnsignedNumericVar ofnCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 OFN-CVE2 9(05)
    private AlphanumericVar taaCve2 = new AlphanumericVar(this, 1);          // 05 TAA-CVE2 X(01)
    private AlphanumericVar gnaCveAgt2 = new AlphanumericVar(this, 1);       // 05 GNA-CVE-AGT2 X(01)
    private UnsignedNumericVar gnaCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 GNA-CVE2 9(05)
    private UnsignedNumericVar tfpCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 TFP-CVE2 9(05)
    private UnsignedNumericVar agtIdrRcl2 = new UnsignedNumericVar(this, 9, 0);              // 05 AGT-IDR-RCL2 9(09)
    private AlphanumericVar ctbNumCta2 = new AlphanumericVar(this, 18);      // 05 CTB-NUM-CTA2 X(18)
    private UnsignedNumericVar mteCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 MTE-CVE2 9(05)
    private UnsignedNumericVar mdeCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 MDE-CVE2 9(05)
    private UnsignedNumericVar zecCve2 = new UnsignedNumericVar(this, 5, 0);                 // 05 ZEC-CVE2 9(05)
    private AlphanumericVar cnaCodPag2 = new AlphanumericVar(this, 1);       // 05 CNA-COD-PAG2 X(01)
    private AlphanumericVar cnaFecMotEss2 = new AlphanumericVar(this, 10);   // 05 CNA-FEC-MOT-ESS2 X(10)
    private UnsignedNumericVar cnaGerZonEnv2 = new UnsignedNumericVar(this, 5, 0);           // 05 CNA-GER-ZON-ENV2 9(05)
    private UnsignedNumericVar cnaGerZonDcm2 = new UnsignedNumericVar(this, 5, 0);           // 05 CNA-GER-ZON-DCM2 9(05)
    private AlphanumericVar cnaIndGer2 = new AlphanumericVar(this, 1);       // 05 CNA-IND-GER2 X(01)
    private AlphanumericVar cnaFecAsgGer2 = new AlphanumericVar(this, 10);   // 05 CNA-FEC-ASG-GER2 X(10)
    private AlphanumericVar essRek2 = new AlphanumericVar(this, 1);          // 05 ESS-REK2 X(01)

    public RegArchcnah() {
        super();
        initialize();
    }

    public RegArchcnah(DataContainer parent) {
        super(parent);
    }

    public RegArchcnah(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setAgtIdr2(final int agtIdr2) {
        this.agtIdr2.setValue(agtIdr2);
    }

    public void setAgtIdr2(final BigDecimal agtIdr2) {
        this.agtIdr2.setValue(agtIdr2);
    }

    public void setAgtIdr2(final INumericValue agtIdr2) {
        this.agtIdr2.setValue(agtIdr2);
    }

    public void setAgtFecCamRazSo2(final String agtFecCamRazSo2) {
        this.agtFecCamRazSo2.setValue(agtFecCamRazSo2);
    }

    public void setAgtFecCamRazSo2(final IAlphanumericValue agtFecCamRazSo2) {
        this.agtFecCamRazSo2.setValue(agtFecCamRazSo2);
    }

    public void setEmpCve2(final String empCve2) {
        this.empCve2.setValue(empCve2);
    }

    public void setEmpCve2(final IAlphanumericValue empCve2) {
        this.empCve2.setValue(empCve2);
    }

    public void setCnaNum2(final int cnaNum2) {
        this.cnaNum2.setValue(cnaNum2);
    }

    public void setCnaNum2(final BigDecimal cnaNum2) {
        this.cnaNum2.setValue(cnaNum2);
    }

    public void setCnaNum2(final INumericValue cnaNum2) {
        this.cnaNum2.setValue(cnaNum2);
    }

    public void setCnaTpoCnr2(final String cnaTpoCnr2) {
        this.cnaTpoCnr2.setValue(cnaTpoCnr2);
    }

    public void setCnaTpoCnr2(final IAlphanumericValue cnaTpoCnr2) {
        this.cnaTpoCnr2.setValue(cnaTpoCnr2);
    }

    public void setCnaUltCia2(final String cnaUltCia2) {
        this.cnaUltCia2.setValue(cnaUltCia2);
    }

    public void setCnaUltCia2(final IAlphanumericValue cnaUltCia2) {
        this.cnaUltCia2.setValue(cnaUltCia2);
    }

    public void setCnaFecCnx2(final String cnaFecCnx2) {
        this.cnaFecCnx2.setValue(cnaFecCnx2);
    }

    public void setCnaFecCnx2(final IAlphanumericValue cnaFecCnx2) {
        this.cnaFecCnx2.setValue(cnaFecCnx2);
    }

    public void setCnaCodIpu2(final String cnaCodIpu2) {
        this.cnaCodIpu2.setValue(cnaCodIpu2);
    }

    public void setCnaCodIpu2(final IAlphanumericValue cnaCodIpu2) {
        this.cnaCodIpu2.setValue(cnaCodIpu2);
    }

    public void setFiller01(final String filler01) {
        this.filler01.setValue(filler01);
    }

    public void setFiller01(final IAlphanumericValue filler01) {
        this.filler01.setValue(filler01);
    }

    public void setCnaPjeIva23(final int cnaPjeIva23) {
        this.cnaPjeIva23.setValue(cnaPjeIva23);
    }

    public void setCnaPjeIva23(final BigDecimal cnaPjeIva23) {
        this.cnaPjeIva23.setValue(cnaPjeIva23);
    }

    public void setCnaPjeIva23(final INumericValue cnaPjeIva23) {
        this.cnaPjeIva23.setValue(cnaPjeIva23);
    }

    public void setFiller02(final String filler02) {
        this.filler02.setValue(filler02);
    }

    public void setFiller02(final IAlphanumericValue filler02) {
        this.filler02.setValue(filler02);
    }

    public void setCnaPjeIva22(final int cnaPjeIva22) {
        this.cnaPjeIva22.setValue(cnaPjeIva22);
    }

    public void setCnaPjeIva22(final BigDecimal cnaPjeIva22) {
        this.cnaPjeIva22.setValue(cnaPjeIva22);
    }

    public void setCnaPjeIva22(final INumericValue cnaPjeIva22) {
        this.cnaPjeIva22.setValue(cnaPjeIva22);
    }

    public void setFiller03(final String filler03) {
        this.filler03.setValue(filler03);
    }

    public void setFiller03(final IAlphanumericValue filler03) {
        this.filler03.setValue(filler03);
    }

    public void setCnaPjeIsr23(final int cnaPjeIsr23) {
        this.cnaPjeIsr23.setValue(cnaPjeIsr23);
    }

    public void setCnaPjeIsr23(final BigDecimal cnaPjeIsr23) {
        this.cnaPjeIsr23.setValue(cnaPjeIsr23);
    }

    public void setCnaPjeIsr23(final INumericValue cnaPjeIsr23) {
        this.cnaPjeIsr23.setValue(cnaPjeIsr23);
    }

    public void setFiller04(final String filler04) {
        this.filler04.setValue(filler04);
    }

    public void setFiller04(final IAlphanumericValue filler04) {
        this.filler04.setValue(filler04);
    }

    public void setCnaPjeIsr22(final int cnaPjeIsr22) {
        this.cnaPjeIsr22.setValue(cnaPjeIsr22);
    }

    public void setCnaPjeIsr22(final BigDecimal cnaPjeIsr22) {
        this.cnaPjeIsr22.setValue(cnaPjeIsr22);
    }

    public void setCnaPjeIsr22(final INumericValue cnaPjeIsr22) {
        this.cnaPjeIsr22.setValue(cnaPjeIsr22);
    }

    public void setCnaComNiv2(final String cnaComNiv2) {
        this.cnaComNiv2.setValue(cnaComNiv2);
    }

    public void setCnaComNiv2(final IAlphanumericValue cnaComNiv2) {
        this.cnaComNiv2.setValue(cnaComNiv2);
    }

    public void setFiller05(final String filler05) {
        this.filler05.setValue(filler05);
    }

    public void setFiller05(final IAlphanumericValue filler05) {
        this.filler05.setValue(filler05);
    }

    public void setCnaImpComNiv211(final BigDecimal cnaImpComNiv211) {
        this.cnaImpComNiv211.setValue(cnaImpComNiv211);
    }

    public void setCnaImpComNiv211(final int cnaImpComNiv211) {
        this.cnaImpComNiv211.setValue(cnaImpComNiv211);
    }

    public void setCnaImpComNiv211(final INumericValue cnaImpComNiv211) {
        this.cnaImpComNiv211.setValue(cnaImpComNiv211);
    }

    public void setFiller06(final String filler06) {
        this.filler06.setValue(filler06);
    }

    public void setFiller06(final IAlphanumericValue filler06) {
        this.filler06.setValue(filler06);
    }

    public void setCnaImpComNiv22(final int cnaImpComNiv22) {
        this.cnaImpComNiv22.setValue(cnaImpComNiv22);
    }

    public void setCnaImpComNiv22(final BigDecimal cnaImpComNiv22) {
        this.cnaImpComNiv22.setValue(cnaImpComNiv22);
    }

    public void setCnaImpComNiv22(final INumericValue cnaImpComNiv22) {
        this.cnaImpComNiv22.setValue(cnaImpComNiv22);
    }

    public void setCnaNumFza2(final String cnaNumFza2) {
        this.cnaNumFza2.setValue(cnaNumFza2);
    }

    public void setCnaNumFza2(final IAlphanumericValue cnaNumFza2) {
        this.cnaNumFza2.setValue(cnaNumFza2);
    }

    public void setCnaFecIniFza2(final String cnaFecIniFza2) {
        this.cnaFecIniFza2.setValue(cnaFecIniFza2);
    }

    public void setCnaFecIniFza2(final IAlphanumericValue cnaFecIniFza2) {
        this.cnaFecIniFza2.setValue(cnaFecIniFza2);
    }

    public void setCnaFecFinFza2(final String cnaFecFinFza2) {
        this.cnaFecFinFza2.setValue(cnaFecFinFza2);
    }

    public void setCnaFecFinFza2(final IAlphanumericValue cnaFecFinFza2) {
        this.cnaFecFinFza2.setValue(cnaFecFinFza2);
    }

    public void setFiller07(final String filler07) {
        this.filler07.setValue(filler07);
    }

    public void setFiller07(final IAlphanumericValue filler07) {
        this.filler07.setValue(filler07);
    }

    public void setCnaImpFza211(final BigDecimal cnaImpFza211) {
        this.cnaImpFza211.setValue(cnaImpFza211);
    }

    public void setCnaImpFza211(final int cnaImpFza211) {
        this.cnaImpFza211.setValue(cnaImpFza211);
    }

    public void setCnaImpFza211(final INumericValue cnaImpFza211) {
        this.cnaImpFza211.setValue(cnaImpFza211);
    }

    public void setFiller08(final String filler08) {
        this.filler08.setValue(filler08);
    }

    public void setFiller08(final IAlphanumericValue filler08) {
        this.filler08.setValue(filler08);
    }

    public void setCnaImpFza22(final int cnaImpFza22) {
        this.cnaImpFza22.setValue(cnaImpFza22);
    }

    public void setCnaImpFza22(final BigDecimal cnaImpFza22) {
        this.cnaImpFza22.setValue(cnaImpFza22);
    }

    public void setCnaImpFza22(final INumericValue cnaImpFza22) {
        this.cnaImpFza22.setValue(cnaImpFza22);
    }

    public void setCnaIdrFav2(final String cnaIdrFav2) {
        this.cnaIdrFav2.setValue(cnaIdrFav2);
    }

    public void setCnaIdrFav2(final IAlphanumericValue cnaIdrFav2) {
        this.cnaIdrFav2.setValue(cnaIdrFav2);
    }

    public void setFiller09(final String filler09) {
        this.filler09.setValue(filler09);
    }

    public void setFiller09(final IAlphanumericValue filler09) {
        this.filler09.setValue(filler09);
    }

    public void setCnaPjeFav23(final int cnaPjeFav23) {
        this.cnaPjeFav23.setValue(cnaPjeFav23);
    }

    public void setCnaPjeFav23(final BigDecimal cnaPjeFav23) {
        this.cnaPjeFav23.setValue(cnaPjeFav23);
    }

    public void setCnaPjeFav23(final INumericValue cnaPjeFav23) {
        this.cnaPjeFav23.setValue(cnaPjeFav23);
    }

    public void setFiller10(final String filler10) {
        this.filler10.setValue(filler10);
    }

    public void setFiller10(final IAlphanumericValue filler10) {
        this.filler10.setValue(filler10);
    }

    public void setCnaPjeFav24(final int cnaPjeFav24) {
        this.cnaPjeFav24.setValue(cnaPjeFav24);
    }

    public void setCnaPjeFav24(final BigDecimal cnaPjeFav24) {
        this.cnaPjeFav24.setValue(cnaPjeFav24);
    }

    public void setCnaPjeFav24(final INumericValue cnaPjeFav24) {
        this.cnaPjeFav24.setValue(cnaPjeFav24);
    }

    public void setPpgCve2(final int ppgCve2) {
        this.ppgCve2.setValue(ppgCve2);
    }

    public void setPpgCve2(final BigDecimal ppgCve2) {
        this.ppgCve2.setValue(ppgCve2);
    }

    public void setPpgCve2(final INumericValue ppgCve2) {
        this.ppgCve2.setValue(ppgCve2);
    }

    public void setCpgCve2(final int cpgCve2) {
        this.cpgCve2.setValue(cpgCve2);
    }

    public void setCpgCve2(final BigDecimal cpgCve2) {
        this.cpgCve2.setValue(cpgCve2);
    }

    public void setCpgCve2(final INumericValue cpgCve2) {
        this.cpgCve2.setValue(cpgCve2);
    }

    public void setCdeCve2(final int cdeCve2) {
        this.cdeCve2.setValue(cdeCve2);
    }

    public void setCdeCve2(final BigDecimal cdeCve2) {
        this.cdeCve2.setValue(cdeCve2);
    }

    public void setCdeCve2(final INumericValue cdeCve2) {
        this.cdeCve2.setValue(cdeCve2);
    }

    public void setOfnCve2(final int ofnCve2) {
        this.ofnCve2.setValue(ofnCve2);
    }

    public void setOfnCve2(final BigDecimal ofnCve2) {
        this.ofnCve2.setValue(ofnCve2);
    }

    public void setOfnCve2(final INumericValue ofnCve2) {
        this.ofnCve2.setValue(ofnCve2);
    }

    public void setTaaCve2(final String taaCve2) {
        this.taaCve2.setValue(taaCve2);
    }

    public void setTaaCve2(final IAlphanumericValue taaCve2) {
        this.taaCve2.setValue(taaCve2);
    }

    public void setGnaCveAgt2(final String gnaCveAgt2) {
        this.gnaCveAgt2.setValue(gnaCveAgt2);
    }

    public void setGnaCveAgt2(final IAlphanumericValue gnaCveAgt2) {
        this.gnaCveAgt2.setValue(gnaCveAgt2);
    }

    public void setGnaCve2(final int gnaCve2) {
        this.gnaCve2.setValue(gnaCve2);
    }

    public void setGnaCve2(final BigDecimal gnaCve2) {
        this.gnaCve2.setValue(gnaCve2);
    }

    public void setGnaCve2(final INumericValue gnaCve2) {
        this.gnaCve2.setValue(gnaCve2);
    }

    public void setTfpCve2(final int tfpCve2) {
        this.tfpCve2.setValue(tfpCve2);
    }

    public void setTfpCve2(final BigDecimal tfpCve2) {
        this.tfpCve2.setValue(tfpCve2);
    }

    public void setTfpCve2(final INumericValue tfpCve2) {
        this.tfpCve2.setValue(tfpCve2);
    }

    public void setAgtIdrRcl2(final int agtIdrRcl2) {
        this.agtIdrRcl2.setValue(agtIdrRcl2);
    }

    public void setAgtIdrRcl2(final BigDecimal agtIdrRcl2) {
        this.agtIdrRcl2.setValue(agtIdrRcl2);
    }

    public void setAgtIdrRcl2(final INumericValue agtIdrRcl2) {
        this.agtIdrRcl2.setValue(agtIdrRcl2);
    }

    public void setCtbNumCta2(final String ctbNumCta2) {
        this.ctbNumCta2.setValue(ctbNumCta2);
    }

    public void setCtbNumCta2(final IAlphanumericValue ctbNumCta2) {
        this.ctbNumCta2.setValue(ctbNumCta2);
    }

    public void setMteCve2(final int mteCve2) {
        this.mteCve2.setValue(mteCve2);
    }

    public void setMteCve2(final BigDecimal mteCve2) {
        this.mteCve2.setValue(mteCve2);
    }

    public void setMteCve2(final INumericValue mteCve2) {
        this.mteCve2.setValue(mteCve2);
    }

    public void setMdeCve2(final int mdeCve2) {
        this.mdeCve2.setValue(mdeCve2);
    }

    public void setMdeCve2(final BigDecimal mdeCve2) {
        this.mdeCve2.setValue(mdeCve2);
    }

    public void setMdeCve2(final INumericValue mdeCve2) {
        this.mdeCve2.setValue(mdeCve2);
    }

    public void setZecCve2(final int zecCve2) {
        this.zecCve2.setValue(zecCve2);
    }

    public void setZecCve2(final BigDecimal zecCve2) {
        this.zecCve2.setValue(zecCve2);
    }

    public void setZecCve2(final INumericValue zecCve2) {
        this.zecCve2.setValue(zecCve2);
    }

    public void setCnaCodPag2(final String cnaCodPag2) {
        this.cnaCodPag2.setValue(cnaCodPag2);
    }

    public void setCnaCodPag2(final IAlphanumericValue cnaCodPag2) {
        this.cnaCodPag2.setValue(cnaCodPag2);
    }

    public void setCnaFecMotEss2(final String cnaFecMotEss2) {
        this.cnaFecMotEss2.setValue(cnaFecMotEss2);
    }

    public void setCnaFecMotEss2(final IAlphanumericValue cnaFecMotEss2) {
        this.cnaFecMotEss2.setValue(cnaFecMotEss2);
    }

    public void setCnaGerZonEnv2(final int cnaGerZonEnv2) {
        this.cnaGerZonEnv2.setValue(cnaGerZonEnv2);
    }

    public void setCnaGerZonEnv2(final BigDecimal cnaGerZonEnv2) {
        this.cnaGerZonEnv2.setValue(cnaGerZonEnv2);
    }

    public void setCnaGerZonEnv2(final INumericValue cnaGerZonEnv2) {
        this.cnaGerZonEnv2.setValue(cnaGerZonEnv2);
    }

    public void setCnaGerZonDcm2(final int cnaGerZonDcm2) {
        this.cnaGerZonDcm2.setValue(cnaGerZonDcm2);
    }

    public void setCnaGerZonDcm2(final BigDecimal cnaGerZonDcm2) {
        this.cnaGerZonDcm2.setValue(cnaGerZonDcm2);
    }

    public void setCnaGerZonDcm2(final INumericValue cnaGerZonDcm2) {
        this.cnaGerZonDcm2.setValue(cnaGerZonDcm2);
    }

    public void setCnaIndGer2(final String cnaIndGer2) {
        this.cnaIndGer2.setValue(cnaIndGer2);
    }

    public void setCnaIndGer2(final IAlphanumericValue cnaIndGer2) {
        this.cnaIndGer2.setValue(cnaIndGer2);
    }

    public void setCnaFecAsgGer2(final String cnaFecAsgGer2) {
        this.cnaFecAsgGer2.setValue(cnaFecAsgGer2);
    }

    public void setCnaFecAsgGer2(final IAlphanumericValue cnaFecAsgGer2) {
        this.cnaFecAsgGer2.setValue(cnaFecAsgGer2);
    }

    public void setEssRek2(final String essRek2) {
        this.essRek2.setValue(essRek2);
    }

    public void setEssRek2(final IAlphanumericValue essRek2) {
        this.essRek2.setValue(essRek2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.agtIdr2.clear();
        this.agtFecCamRazSo2.clear();
        this.empCve2.clear();
        this.cnaNum2.clear();
        this.cnaTpoCnr2.clear();
        this.cnaUltCia2.clear();
        this.cnaFecCnx2.clear();
        this.cnaCodIpu2.clear();
        this.filler01.clear();
        this.cnaPjeIva23.clear();
        this.filler02.clear();
        this.cnaPjeIva22.clear();
        this.filler03.clear();
        this.cnaPjeIsr23.clear();
        this.filler04.clear();
        this.cnaPjeIsr22.clear();
        this.cnaComNiv2.clear();
        this.filler05.clear();
        this.cnaImpComNiv211.clear();
        this.filler06.clear();
        this.cnaImpComNiv22.clear();
        this.cnaNumFza2.clear();
        this.cnaFecIniFza2.clear();
        this.cnaFecFinFza2.clear();
        this.filler07.clear();
        this.cnaImpFza211.clear();
        this.filler08.clear();
        this.cnaImpFza22.clear();
        this.cnaIdrFav2.clear();
        this.filler09.clear();
        this.cnaPjeFav23.clear();
        this.filler10.clear();
        this.cnaPjeFav24.clear();
        this.ppgCve2.clear();
        this.cpgCve2.clear();
        this.cdeCve2.clear();
        this.ofnCve2.clear();
        this.taaCve2.clear();
        this.gnaCveAgt2.clear();
        this.gnaCve2.clear();
        this.tfpCve2.clear();
        this.agtIdrRcl2.clear();
        this.ctbNumCta2.clear();
        this.mteCve2.clear();
        this.mdeCve2.clear();
        this.zecCve2.clear();
        this.cnaCodPag2.clear();
        this.cnaFecMotEss2.clear();
        this.cnaGerZonEnv2.clear();
        this.cnaGerZonDcm2.clear();
        this.cnaIndGer2.clear();
        this.cnaFecAsgGer2.clear();
        this.essRek2.clear();
    }

}

