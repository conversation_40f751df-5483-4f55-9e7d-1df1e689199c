package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGFVTPAI0.
 *
 */
@Setter
@Getter
public class WsDclgfvtpai0 { // VStruct
    // Properties
    private int paiPaiCve;                                                  // 10 PAI-PAI-CVE S9(4) COMPUTATIONAL

    public WsDclgfvtpai0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.paiPaiCve = 0;
    }

}

