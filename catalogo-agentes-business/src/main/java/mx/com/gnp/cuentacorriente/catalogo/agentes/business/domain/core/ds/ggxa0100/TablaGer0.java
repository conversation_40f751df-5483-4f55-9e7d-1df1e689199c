package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablager0.TGer0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.tablager0.tger0.TGer0Elem;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-GER0.
 *
 */
@Setter
@Getter
public class TablaGer0 { // VStruct
    // Properties
    private TGer0 tGer0 = new  TGer0(51);                                    // 10 T-GER0 [1]

    public TablaGer0() {
        initialize();
    }



    public TGer0 getTGer0(int index) {
        return this.tGer0.at(index);
    }


    // Deeper properties

    // Property: tGer0.tGer0Elem -> 15 T-GER0-ELEM UsedAsParameter
    public TGer0Elem getTGer0Elem() {
        return this.tGer0.getTGer0Elem();
    }

    // Property: tGer0.tGer0Elem.sCnaGerZon -> 20 S-CNA-GER-ZON X(04)
    public AlphanumericVar getSCnaGerZon() {
        return this.tGer0.getTGer0Elem().getSCnaGerZon();
    }

    public void setSCnaGerZon(final String sCnaGerZon) {
        this.tGer0.getTGer0Elem().setSCnaGerZon(sCnaGerZon);
    }

    public void setSCnaGerZon(final IAlphanumericValue sCnaGerZon) {
        this.tGer0.getTGer0Elem().setSCnaGerZon(sCnaGerZon);
    }

    // Property: tGer0.tGer0Elem.sGerLimitad -> 20 S-GER-LIMITAD X(01)
    public AlphanumericVar getSGerLimitad() {
        return this.tGer0.getTGer0Elem().getSGerLimitad();
    }

    public void setSGerLimitad(final String sGerLimitad) {
        this.tGer0.getTGer0Elem().setSGerLimitad(sGerLimitad);
    }

    public void setSGerLimitad(final IAlphanumericValue sGerLimitad) {
        this.tGer0.getTGer0Elem().setSGerLimitad(sGerLimitad);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tGer0.initialize();
    }

}

