package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-DATA-IMP.
 *
 */
@Setter
@Getter
public class RpDataImp { // VStruct
    // Properties
    private NumericVar rpCf1ImpSbtFct = new NumericVar(13, 2);               // 10 RP-CF1-IMP-SBT-FCT S9(11)V9(2)
    private NumericVar rpCf1ImpTotFct = new NumericVar(13, 2);               // 10 RP-CF1-IMP-TOT-FCT S9(11)V9(2)

    public RpDataImp() {
        initialize();
    }



    public void setRpCf1ImpSbtFct(final BigDecimal rpCf1ImpSbtFct) {
        this.rpCf1ImpSbtFct.setValue(rpCf1ImpSbtFct);
    }

    public void setRpCf1ImpSbtFct(final int rpCf1ImpSbtFct) {
        this.rpCf1ImpSbtFct.setValue(rpCf1ImpSbtFct);
    }

    public void setRpCf1ImpSbtFct(final INumericValue rpCf1ImpSbtFct) {
        this.rpCf1ImpSbtFct.setValue(rpCf1ImpSbtFct);
    }

    public void setRpCf1ImpTotFct(final BigDecimal rpCf1ImpTotFct) {
        this.rpCf1ImpTotFct.setValue(rpCf1ImpTotFct);
    }

    public void setRpCf1ImpTotFct(final int rpCf1ImpTotFct) {
        this.rpCf1ImpTotFct.setValue(rpCf1ImpTotFct);
    }

    public void setRpCf1ImpTotFct(final INumericValue rpCf1ImpTotFct) {
        this.rpCf1ImpTotFct.setValue(rpCf1ImpTotFct);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpCf1ImpSbtFct.clear();
        this.rpCf1ImpTotFct.clear();
    }

}

