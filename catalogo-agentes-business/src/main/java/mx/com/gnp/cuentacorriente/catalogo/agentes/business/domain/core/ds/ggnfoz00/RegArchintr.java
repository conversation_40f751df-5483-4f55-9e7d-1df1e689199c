package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnfoz00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: REG-ARCHINTR.
 *
 */
@Setter
@Getter
public class RegArchintr extends DataStruct {
    // Properties
    private AlphanumericVar salAgtIdr = new AlphanumericVar(this, 5);        // 05 SAL-AGT-IDR X(5)
    private AlphanumericVar salTfpCvev = new AlphanumericVar(this, 3);       // 05 SAL-TFP-CVEV X(3)
    private AlphanumericVar salTfpCven = new AlphanumericVar(this, 3);       // 05 SAL-TFP-CVEN X(3)
    private AlphanumericVar salAgtFecCamRazSo = new AlphanumericVar(this, 10);               // 05 SAL-AGT-FEC-CAM-RAZ-SO X(10)
    private AlphanumericVar salFlaNum = new AlphanumericVar(this, 7);        // 05 SAL-FLA-NUM X(7)

    public RegArchintr() {
        super();
        initialize();
    }

    public RegArchintr(DataContainer parent) {
        super(parent);
    }

    public RegArchintr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setSalAgtIdr(final String salAgtIdr) {
        this.salAgtIdr.setValue(salAgtIdr);
    }

    public void setSalAgtIdr(final IAlphanumericValue salAgtIdr) {
        this.salAgtIdr.setValue(salAgtIdr);
    }

    public void setSalTfpCvev(final String salTfpCvev) {
        this.salTfpCvev.setValue(salTfpCvev);
    }

    public void setSalTfpCvev(final IAlphanumericValue salTfpCvev) {
        this.salTfpCvev.setValue(salTfpCvev);
    }

    public void setSalTfpCven(final String salTfpCven) {
        this.salTfpCven.setValue(salTfpCven);
    }

    public void setSalTfpCven(final IAlphanumericValue salTfpCven) {
        this.salTfpCven.setValue(salTfpCven);
    }

    public void setSalAgtFecCamRazSo(final String salAgtFecCamRazSo) {
        this.salAgtFecCamRazSo.setValue(salAgtFecCamRazSo);
    }

    public void setSalAgtFecCamRazSo(final IAlphanumericValue salAgtFecCamRazSo) {
        this.salAgtFecCamRazSo.setValue(salAgtFecCamRazSo);
    }

    public void setSalFlaNum(final String salFlaNum) {
        this.salFlaNum.setValue(salFlaNum);
    }

    public void setSalFlaNum(final IAlphanumericValue salFlaNum) {
        this.salFlaNum.setValue(salFlaNum);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.salAgtIdr.clear();
        this.salTfpCvev.clear();
        this.salTfpCven.clear();
        this.salAgtFecCamRazSo.clear();
        this.salFlaNum.clear();
    }

}

