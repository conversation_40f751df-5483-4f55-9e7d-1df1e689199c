package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TB-DCLGFVTFLA0.
 *
 */
@Setter
@Getter
public class TbDclgfvtfla0 extends DataStruct {
    // Properties
    private AlphanumericVar tbFlaNum = new AlphanumericVar(this, 8);         // 10 TB-FLA-NUM X(8)
    private AlphanumericVar tbFlaDes = new AlphanumericVar(this, 40);        // 10 TB-FLA-DES X(40)
    private AlphanumericVar tbFlaFecIniAsg = new AlphanumericVar(this, 10);  // 10 TB-FLA-FEC-INI-ASG X(10)
    private AlphanumericVar tbFla03 = new AlphanumericVar(this, 1);          // 10 TB-FLA03 X(01)
    private AlphanumericVar tbFlaFecFinAsg = new AlphanumericVar(this, 10);  // 10 TB-FLA-FEC-FIN-ASG X(10)
    private AlphanumericVar tbFla04 = new AlphanumericVar(this, 1);          // 10 TB-FLA04 X(01)
    private BinaryVar tbOfnCve = new BinaryVar(this, 4, 0);                  // 10 TB-OFN-CVE S9(4) COMPUTATIONAL
    private AlphanumericVar tbFla05 = new AlphanumericVar(this, 1);          // 10 TB-FLA05 X(01)
    private BinaryVar tbAgtIdr = new BinaryVar(this, 9, 0);                  // 10 TB-AGT-IDR S9(9) COMPUTATIONAL
    private AlphanumericVar tbEmpCve = new AlphanumericVar(this, 5);         // 10 TB-EMP-CVE X(5)
    private BinaryVar tbCnaNum = new BinaryVar(this, 4, 0);                  // 10 TB-CNA-NUM S9(4) COMPUTATIONAL
    private BinaryVar tbMteCve = new BinaryVar(this, 4, 0);                  // 10 TB-MTE-CVE S9(4) COMPUTATIONAL
    private BinaryVar tbOrvCve = new BinaryVar(this, 4, 0);                  // 10 TB-ORV-CVE S9(4) COMPUTATIONAL
    private BinaryVar tbFlaGerZon = new BinaryVar(this, 4, 0);               // 10 TB-FLA-GER-ZON S9(4) COMPUTATIONAL
    private AlphanumericVar tbFla11 = new AlphanumericVar(this, 1);          // 10 TB-FLA11 X(01)
    private AlphanumericVar tbFlaNegCve = new AlphanumericVar(this, 3);      // 10 TB-FLA-NEG-CVE X(3)
    private AlphanumericVar tbFla12 = new AlphanumericVar(this, 1);          // 10 TB-FLA12 X(01)
    private AlphanumericVar tbFlaFecMotEss = new AlphanumericVar(this, 10);  // 10 TB-FLA-FEC-MOT-ESS X(10)
    private AlphanumericVar tbFla13 = new AlphanumericVar(this, 1);          // 10 TB-FLA13 X(01)

    public TbDclgfvtfla0() {
        super();
        initialize();
    }

    public TbDclgfvtfla0(DataContainer parent) {
        super(parent);
    }

    public TbDclgfvtfla0(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setTbFlaNum(final String tbFlaNum) {
        this.tbFlaNum.setValue(tbFlaNum);
    }

    public void setTbFlaNum(final IAlphanumericValue tbFlaNum) {
        this.tbFlaNum.setValue(tbFlaNum);
    }

    public void setTbFlaDes(final String tbFlaDes) {
        this.tbFlaDes.setValue(tbFlaDes);
    }

    public void setTbFlaDes(final IAlphanumericValue tbFlaDes) {
        this.tbFlaDes.setValue(tbFlaDes);
    }

    public void setTbFlaFecIniAsg(final String tbFlaFecIniAsg) {
        this.tbFlaFecIniAsg.setValue(tbFlaFecIniAsg);
    }

    public void setTbFlaFecIniAsg(final IAlphanumericValue tbFlaFecIniAsg) {
        this.tbFlaFecIniAsg.setValue(tbFlaFecIniAsg);
    }

    public void setTbFla03(final String tbFla03) {
        this.tbFla03.setValue(tbFla03);
    }

    public void setTbFla03(final IAlphanumericValue tbFla03) {
        this.tbFla03.setValue(tbFla03);
    }

    public void setTbFlaFecFinAsg(final String tbFlaFecFinAsg) {
        this.tbFlaFecFinAsg.setValue(tbFlaFecFinAsg);
    }

    public void setTbFlaFecFinAsg(final IAlphanumericValue tbFlaFecFinAsg) {
        this.tbFlaFecFinAsg.setValue(tbFlaFecFinAsg);
    }

    public void setTbFla04(final String tbFla04) {
        this.tbFla04.setValue(tbFla04);
    }

    public void setTbFla04(final IAlphanumericValue tbFla04) {
        this.tbFla04.setValue(tbFla04);
    }

    public void setTbOfnCve(final int tbOfnCve) {
        this.tbOfnCve.setValue(tbOfnCve);
    }

    public void setTbOfnCve(final BigDecimal tbOfnCve) {
        this.tbOfnCve.setValue(tbOfnCve);
    }

    public void setTbOfnCve(final INumericValue tbOfnCve) {
        this.tbOfnCve.setValue(tbOfnCve);
    }

    public void setTbFla05(final String tbFla05) {
        this.tbFla05.setValue(tbFla05);
    }

    public void setTbFla05(final IAlphanumericValue tbFla05) {
        this.tbFla05.setValue(tbFla05);
    }

    public void setTbAgtIdr(final int tbAgtIdr) {
        this.tbAgtIdr.setValue(tbAgtIdr);
    }

    public void setTbAgtIdr(final BigDecimal tbAgtIdr) {
        this.tbAgtIdr.setValue(tbAgtIdr);
    }

    public void setTbAgtIdr(final INumericValue tbAgtIdr) {
        this.tbAgtIdr.setValue(tbAgtIdr);
    }

    public void setTbEmpCve(final String tbEmpCve) {
        this.tbEmpCve.setValue(tbEmpCve);
    }

    public void setTbEmpCve(final IAlphanumericValue tbEmpCve) {
        this.tbEmpCve.setValue(tbEmpCve);
    }

    public void setTbCnaNum(final int tbCnaNum) {
        this.tbCnaNum.setValue(tbCnaNum);
    }

    public void setTbCnaNum(final BigDecimal tbCnaNum) {
        this.tbCnaNum.setValue(tbCnaNum);
    }

    public void setTbCnaNum(final INumericValue tbCnaNum) {
        this.tbCnaNum.setValue(tbCnaNum);
    }

    public void setTbMteCve(final int tbMteCve) {
        this.tbMteCve.setValue(tbMteCve);
    }

    public void setTbMteCve(final BigDecimal tbMteCve) {
        this.tbMteCve.setValue(tbMteCve);
    }

    public void setTbMteCve(final INumericValue tbMteCve) {
        this.tbMteCve.setValue(tbMteCve);
    }

    public void setTbOrvCve(final int tbOrvCve) {
        this.tbOrvCve.setValue(tbOrvCve);
    }

    public void setTbOrvCve(final BigDecimal tbOrvCve) {
        this.tbOrvCve.setValue(tbOrvCve);
    }

    public void setTbOrvCve(final INumericValue tbOrvCve) {
        this.tbOrvCve.setValue(tbOrvCve);
    }

    public void setTbFlaGerZon(final int tbFlaGerZon) {
        this.tbFlaGerZon.setValue(tbFlaGerZon);
    }

    public void setTbFlaGerZon(final BigDecimal tbFlaGerZon) {
        this.tbFlaGerZon.setValue(tbFlaGerZon);
    }

    public void setTbFlaGerZon(final INumericValue tbFlaGerZon) {
        this.tbFlaGerZon.setValue(tbFlaGerZon);
    }

    public void setTbFla11(final String tbFla11) {
        this.tbFla11.setValue(tbFla11);
    }

    public void setTbFla11(final IAlphanumericValue tbFla11) {
        this.tbFla11.setValue(tbFla11);
    }

    public void setTbFlaNegCve(final String tbFlaNegCve) {
        this.tbFlaNegCve.setValue(tbFlaNegCve);
    }

    public void setTbFlaNegCve(final IAlphanumericValue tbFlaNegCve) {
        this.tbFlaNegCve.setValue(tbFlaNegCve);
    }

    public void setTbFla12(final String tbFla12) {
        this.tbFla12.setValue(tbFla12);
    }

    public void setTbFla12(final IAlphanumericValue tbFla12) {
        this.tbFla12.setValue(tbFla12);
    }

    public void setTbFlaFecMotEss(final String tbFlaFecMotEss) {
        this.tbFlaFecMotEss.setValue(tbFlaFecMotEss);
    }

    public void setTbFlaFecMotEss(final IAlphanumericValue tbFlaFecMotEss) {
        this.tbFlaFecMotEss.setValue(tbFlaFecMotEss);
    }

    public void setTbFla13(final String tbFla13) {
        this.tbFla13.setValue(tbFla13);
    }

    public void setTbFla13(final IAlphanumericValue tbFla13) {
        this.tbFla13.setValue(tbFla13);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbFlaNum.clear();
        this.tbFlaDes.clear();
        this.tbFlaFecIniAsg.clear();
        this.tbFla03.clear();
        this.tbFlaFecFinAsg.clear();
        this.tbFla04.clear();
        this.tbOfnCve.clear();
        this.tbFla05.clear();
        this.tbAgtIdr.clear();
        this.tbEmpCve.clear();
        this.tbCnaNum.clear();
        this.tbMteCve.clear();
        this.tbOrvCve.clear();
        this.tbFlaGerZon.clear();
        this.tbFla11.clear();
        this.tbFlaNegCve.clear();
        this.tbFla12.clear();
        this.tbFlaFecMotEss.clear();
        this.tbFla13.clear();
    }

}

