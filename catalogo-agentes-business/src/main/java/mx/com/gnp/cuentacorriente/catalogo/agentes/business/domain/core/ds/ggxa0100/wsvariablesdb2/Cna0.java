package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.wsvariablesdb2;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: CNA0.
 *
 */
@Setter
@Getter
public class Cna0 { // VStruct
    // Properties
    private int cnaCnaNum;                                                  // 10 CNA-CNA-NUM S9(4) COMPUTATIONAL
    private int wsCnaCnaNum;                                                // 10 WS-CNA-CNA-NUM 9(02)
    private int cnaGerZonEnv;                                               // 10 CNA-GER-ZON-ENV S9(4) COMPUTATIONAL
    private int wsCnaGerZon;                                                // 10 WS-CNA-GER-ZON 9(04)
    private int cnaOfnCve;                                                  // 10 CNA-OFN-CVE S9(4) COMPUTATIONAL
    private int wsCnaOfnCve;                                                // 10 WS-CNA-OFN-CVE 9(04)
    private String cnaEmpCve = "";                                          // 10 CNA-EMP-CVE X(05)
    private int cnaTfpCve;                                                  // 10 CNA-TFP-CVE S9(4) COMPUTATIONAL
    private int ceoCeoCve;                                                  // 10 CEO-CEO-CVE S9(4) COMPUTATIONAL
    private int wsCeoCeoCve;                                                // 10 WS-CEO-CEO-CVE 9(04)
    private String ceoCeoNom = "";                                          // 10 CEO-CEO-NOM X(30)
    private String wsCeoCeoNom = "";                                        // 10 WS-CEO-CEO-NOM X(30)

    public Cna0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.cnaCnaNum = 0;
        this.wsCnaCnaNum = 0;
        this.cnaGerZonEnv = 0;
        this.wsCnaGerZon = 0;
        this.cnaOfnCve = 0;
        this.wsCnaOfnCve = 0;
        this.cnaEmpCve = "";
        this.cnaTfpCve = 0;
        this.ceoCeoCve = 0;
        this.wsCeoCeoCve = 0;
        this.ceoCeoNom = "";
        this.wsCeoCeoNom = "";
    }

}

