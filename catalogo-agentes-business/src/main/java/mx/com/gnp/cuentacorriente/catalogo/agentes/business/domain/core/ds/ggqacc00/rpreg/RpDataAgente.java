package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.rpreg;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: RP-DATA-AGENTE.
 *
 */
@Setter
@Getter
public class RpDataAgente { // VStruct
    // Properties
    private String rpAgtRfc = "";                                           // 10 RP-AGT-RFC X(13)
    private String rpAgtNom = "";                                           // 10 RP-AGT-NOM X(40)
    private String rpAgtApePat = "";                                        // 10 RP-AGT-APE-PAT X(40)
    private String rpAgtApeMat = "";                                        // 10 RP-AGT-APE-MAT X(30)
    private String rpAgtRazSoc = "";                                        // 10 RP-AGT-RAZ-SOC X(60)
    private String rpAgtNomConcatenado = "";                                // 10 RP-AGT-NOM-CONCATENADO X(100)
    private String rpPraAgtCxxCurp = "";                                    // 10 RP-PRA-AGT-CXX-CURP X(18)
    private String rpAgtNalCve = "";                                        // 10 RP-AGT-NAL-CVE X(03)
    // Level 88 - Conditional names
    private static final String RP_EXTRANJERO = "EXT";

    private int rpMteCve;                                                   // 10 RP-MTE-CVE 9(04)
    private int rpPefCve;                                                   // 10 RP-PEF-CVE 9(04)
    private String rpDmaCae = "";                                           // 10 RP-DMA-CAE X(40)
    private String rpDmaNum = "";                                           // 10 RP-DMA-NUM X(05)
    private String rpDmaInt = "";                                           // 10 RP-DMA-INT X(05)
    private String rpDmaCol = "";                                           // 10 RP-DMA-COL X(40)
    private String rpMndNom = "";                                           // 10 RP-MND-NOM X(50)
    private int rpDmaCpoCve;                                                // 10 RP-DMA-CPO-CVE 9(05)
    private String rpPobNom = "";                                           // 10 RP-POB-NOM X(40)
    private String rpMndEdoCve = "";                                        // 10 RP-MND-EDO-CVE X(04)
    private String rpEdoEdoNom = "";                                        // 10 RP-EDO-EDO-NOM X(30)
    private String rpEdoPaiCve = "";                                        // 10 RP-EDO-PAI-CVE X(03)
    private String rpCmaDesTelefon = "";                                    // 10 RP-CMA-DES-TELEFON X(60)
    private int rpCnaGerZonEnv;                                             // 10 RP-CNA-GER-ZON-ENV 9(04)
    private int rpCnaOfnCve;                                                // 10 RP-CNA-OFN-CVE 9(04)

    public RpDataAgente() {
        initialize();
    }



    // Conditionals (88) - RP-AGT-NAL-CVE
    public boolean isRpExtranjero() {
        return this.rpAgtNalCve.equals(RP_EXTRANJERO);
    }

    public void setRpExtranjero() {
        this.rpAgtNalCve = RP_EXTRANJERO;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.rpAgtRfc = "";
        this.rpAgtNom = "";
        this.rpAgtApePat = "";
        this.rpAgtApeMat = "";
        this.rpAgtRazSoc = "";
        this.rpAgtNomConcatenado = "";
        this.rpPraAgtCxxCurp = "";
        this.rpAgtNalCve = "";
        this.rpMteCve = 0;
        this.rpPefCve = 0;
        this.rpDmaCae = "";
        this.rpDmaNum = "";
        this.rpDmaInt = "";
        this.rpDmaCol = "";
        this.rpMndNom = "";
        this.rpDmaCpoCve = 0;
        this.rpPobNom = "";
        this.rpMndEdoCve = "";
        this.rpEdoEdoNom = "";
        this.rpEdoPaiCve = "";
        this.rpCmaDesTelefon = "";
        this.rpCnaGerZonEnv = 0;
        this.rpCnaOfnCve = 0;
    }

}

