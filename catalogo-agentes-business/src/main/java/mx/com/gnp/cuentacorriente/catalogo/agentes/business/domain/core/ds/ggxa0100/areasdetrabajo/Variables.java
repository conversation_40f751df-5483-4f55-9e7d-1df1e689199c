package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100.areasdetrabajo.variables.ConIncidencias;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: VARIABLES.
 *
 */
@Setter
@Getter
public class Variables { // VStruct
    // Properties
    private int ctnError = 0;                                               // 10 CTN-ERROR S9(03)
    private ConIncidencias conIncidencias = new  ConIncidencias();           // 10 CON-INCIDENCIAS
    private NumericEditedVar conError = new NumericEditedVar("ZZZZZZZZZ+");  // 10 CON-ERROR ZZZZZZZZZ+

    public Variables() {
        initialize();
    }



    public void setConError(final String conError) {
        this.conError.setValue(conError);
    }

    public void setConError(final IValue conError) {
        this.conError.setValue(conError);
    }


    // Deeper properties

    // Property: conIncidencias.conMensaje -> 15 CON-MENSAJE X(40)
    public String getConMensaje() {
        return this.conIncidencias.getConMensaje();
    }

    public void setConMensaje(final String conMensaje) {
        this.conIncidencias.setConMensaje(conMensaje);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.ctnError = 0;
        this.conIncidencias.initialize();
        this.conError.clear();
    }

}

