package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGCCTMND0.
 *
 */
@Setter
@Getter
public class WsDclgcctmnd0 { // VStruct
    // Properties
    private int mndMndCve;                                                  // 10 MND-MND-CVE S9(9) COMPUTATIONAL
    private String mndMndNom = "";                                          // 10 MND-MND-NOM X(50)
    private String mndFecUltAct = "";                                       // 10 MND-FEC-ULT-ACT X(10)
    private String mndUsuCveAct = "";                                       // 10 MND-USU-CVE-ACT X(8)
    private String mndEdoCve = "";                                          // 10 MND-EDO-CVE X(4)

    public WsDclgcctmnd0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.mndMndCve = 0;
        this.mndMndNom = "";
        this.mndFecUltAct = "";
        this.mndUsuCveAct = "";
        this.mndEdoCve = "";
    }

}

