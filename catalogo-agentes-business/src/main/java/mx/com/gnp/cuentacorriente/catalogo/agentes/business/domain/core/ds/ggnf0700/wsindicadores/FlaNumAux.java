package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: FLA-NUM-AUX.
 *
 */
// 749048100*                                                                 04810002
// 750048200*                                                                 04820002
// 751048300*                                                                 04830002
@Setter
@Getter
public class FlaNumAux { // VStruct
    // Properties
    private String flaNum0Aux = "";                                         // 05 FLA-NUM0-AUX X
    private int flaNum1Aux;                                                 // 05 FLA-NUM1-AUX 9(7)

    public FlaNumAux() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.flaNum0Aux = "";
        this.flaNum1Aux = 0;
    }

}

