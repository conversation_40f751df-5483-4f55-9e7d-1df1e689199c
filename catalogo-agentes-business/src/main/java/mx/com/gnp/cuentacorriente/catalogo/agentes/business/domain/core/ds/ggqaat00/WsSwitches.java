package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-SWITCHES.
 *
 */
// 675      *                                                                 
// 676      *------------------- SWITCHES -----------------------------------*
@Setter
@Getter
public class WsSwitches { // VStruct
    // Properties
    private String wsTelok = "";                                            // 05 WS-TELOK X(01)
    // Level 88 - Conditional names
    private static final String TELOK = "*";

    private String wsNoesp = "";                                            // 05 WS-NOESP X(01)
    // Level 88 - Conditional names
    private static final String NOESP = "*";

    private String wsFinAgt = "";                                           // 05 WS-FIN-AGT X(01)
    // Level 88 - Conditional names
    private static final String SW_FIN_AGT = "*";

    private String wsFinOfn = "";                                           // 05 WS-FIN-OFN X(01)
    // Level 88 - Conditional names
    private static final String SW_FIN_OFN = "*";

    private String wsFinCeo = "";                                           // 05 WS-FIN-CEO X(01)
    // Level 88 - Conditional names
    private static final String SW_FIN_CEO = "*";

    private String wsPhone = "";                                            // 05 WS-PHONE X(01)
    // Level 88 - Conditional names
    private static final String SW_PHONE = "*";

    private String wsFinDma = "";                                           // 05 WS-FIN-DMA X(01)
    // Level 88 - Conditional names
    private static final String SW_FIN_DMA = "*";

    private String wsOkDma = "";                                            // 05 WS-OK-DMA X(01)
    // Level 88 - Conditional names
    private static final String SW_OK_DMA = "*";

    private String swAgtLuv = "N";                                          // 05 SW-AGT-LUV X(01)
    // Level 88 - Conditional names
    private static final String SI_AGT_LUV = "S";

    private String swGteLuv = "N";                                          // 05 SW-GTE-LUV X(01)
    // Level 88 - Conditional names
    private static final String SI_GTE_LUV = "S";


    public WsSwitches() {
        initialize();
    }



    // Conditionals (88) - WS-TELOK
    public boolean isTelok() {
        return this.wsTelok.equals(TELOK);
    }

    public void setTelok() {
        this.wsTelok = TELOK;
    }


    // Conditionals (88) - WS-NOESP
    public boolean isNoesp() {
        return this.wsNoesp.equals(NOESP);
    }

    public void setNoesp() {
        this.wsNoesp = NOESP;
    }


    // Conditionals (88) - WS-FIN-AGT
    public boolean isSwFinAgt() {
        return this.wsFinAgt.equals(SW_FIN_AGT);
    }

    public void setSwFinAgt() {
        this.wsFinAgt = SW_FIN_AGT;
    }


    // Conditionals (88) - WS-FIN-OFN
    public boolean isSwFinOfn() {
        return this.wsFinOfn.equals(SW_FIN_OFN);
    }

    public void setSwFinOfn() {
        this.wsFinOfn = SW_FIN_OFN;
    }


    // Conditionals (88) - WS-FIN-CEO
    public boolean isSwFinCeo() {
        return this.wsFinCeo.equals(SW_FIN_CEO);
    }

    public void setSwFinCeo() {
        this.wsFinCeo = SW_FIN_CEO;
    }


    // Conditionals (88) - WS-PHONE
    public boolean isSwPhone() {
        return this.wsPhone.equals(SW_PHONE);
    }

    public void setSwPhone() {
        this.wsPhone = SW_PHONE;
    }


    // Conditionals (88) - WS-FIN-DMA
    public boolean isSwFinDma() {
        return this.wsFinDma.equals(SW_FIN_DMA);
    }

    public void setSwFinDma() {
        this.wsFinDma = SW_FIN_DMA;
    }


    // Conditionals (88) - WS-OK-DMA
    public boolean isSwOkDma() {
        return this.wsOkDma.equals(SW_OK_DMA);
    }

    public void setSwOkDma() {
        this.wsOkDma = SW_OK_DMA;
    }


    // Conditionals (88) - SW-AGT-LUV
    public boolean isSiAgtLuv() {
        return this.swAgtLuv.equals(SI_AGT_LUV);
    }

    public void setSiAgtLuv() {
        this.swAgtLuv = SI_AGT_LUV;
    }


    // Conditionals (88) - SW-GTE-LUV
    public boolean isSiGteLuv() {
        return this.swGteLuv.equals(SI_GTE_LUV);
    }

    public void setSiGteLuv() {
        this.swGteLuv = SI_GTE_LUV;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsTelok = "";
        this.wsNoesp = "";
        this.wsFinAgt = "";
        this.wsFinOfn = "";
        this.wsFinCeo = "";
        this.wsPhone = "";
        this.wsFinDma = "";
        this.wsOkDma = "";
        this.swAgtLuv = "N";
        this.swGteLuv = "N";
    }

}

