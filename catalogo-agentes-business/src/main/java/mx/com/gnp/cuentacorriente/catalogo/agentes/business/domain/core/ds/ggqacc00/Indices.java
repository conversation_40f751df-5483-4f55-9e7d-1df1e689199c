package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: INDICES.
 *
 */
// 196      *--------------------------------------------------------------*  
// 197      * VARIABLES                                                    *  
// 198      *--------------------------------------------------------------*  
@Setter
@Getter
public class Indices { // VStruct
    // Properties
    private int wsI;                                                        // 03 WS-I 9(04)
    private int wsJ;                                                        // 03 WS-J 9(04)
    private int wsX;                                                        // 03 WS-X 9(04)
    private int wsY;                                                        // 03 WS-Y 9(04)
    private int wsInd;                                                      // 03 WS-IND 9(04)

    public Indices() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsI = 0;
        this.wsJ = 0;
        this.wsX = 0;
        this.wsY = 0;
        this.wsInd = 0;
    }

}

