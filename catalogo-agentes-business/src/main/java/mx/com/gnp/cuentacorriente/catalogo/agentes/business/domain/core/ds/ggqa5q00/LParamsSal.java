package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: L-PARAMS-SAL.
 *
 */
@Setter
@Getter
public class LParamsSal extends DataStruct {
    // Properties
    private AlphanumericVar lRegfis = new AlphanumericVar(this, 3);          // 05 L-REGFIS X(03)
    private AlphanumericVar lNombre = new AlphanumericVar(this, 300);        // 05 L-NOMBRE X(300)
    private AlphanumericVar lPcenvi = new AlphanumericVar(this, 5);          // 05 L-PCENVI X(05)
    private AlphanumericVar lPcevid = new AlphanumericVar(this, 5);          // 05 L-PCEVID X(05)

    public LParamsSal() {
        super();
        initialize();
    }

    public LParamsSal(DataContainer parent) {
        super(parent);
    }

    public LParamsSal(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setLRegfis(final String lRegfis) {
        this.lRegfis.setValue(lRegfis);
    }

    public void setLRegfis(final IAlphanumericValue lRegfis) {
        this.lRegfis.setValue(lRegfis);
    }

    public void setLNombre(final String lNombre) {
        this.lNombre.setValue(lNombre);
    }

    public void setLNombre(final IAlphanumericValue lNombre) {
        this.lNombre.setValue(lNombre);
    }

    public void setLPcenvi(final String lPcenvi) {
        this.lPcenvi.setValue(lPcenvi);
    }

    public void setLPcenvi(final IAlphanumericValue lPcenvi) {
        this.lPcenvi.setValue(lPcenvi);
    }

    public void setLPcevid(final String lPcevid) {
        this.lPcevid.setValue(lPcevid);
    }

    public void setLPcevid(final IAlphanumericValue lPcevid) {
        this.lPcevid.setValue(lPcevid);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.lRegfis.clear();
        this.lNombre.clear();
        this.lPcenvi.clear();
        this.lPcevid.clear();
    }

}

