package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TOTALES.
 *
 */
// 344055700*        '345678901234567890                          '.          05570000
// 345055800*                                                                 05580000
// 346055900                                                                  05590000
// 347056000*                                                                 05600000
// 348056100*                                                                 05610000
// 349056200*                                                                 05620000
@Setter
@Getter
public class WsTotales { // VStruct
    // Properties
    private int totFlaLeidos = 0;                                           // 03 TOT-FLA-LEIDOS 9(06)
    private int totFdcLeidos = 0;                                           // 03 TOT-FDC-LEIDOS 9(06)
    private int totCafLeidos = 0;                                           // 03 TOT-CAF-LEIDOS 9(06)
    private int totCafGrabados = 0;                                         // 03 TOT-CAF-GRABADOS 9(06)
    private int totCafDuplicados = 0;                                       // 03 TOT-CAF-DUPLICADOS 9(06)

    public WsTotales() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.totFlaLeidos = 0;
        this.totFdcLeidos = 0;
        this.totCafLeidos = 0;
        this.totCafGrabados = 0;
        this.totCafDuplicados = 0;
    }

}

