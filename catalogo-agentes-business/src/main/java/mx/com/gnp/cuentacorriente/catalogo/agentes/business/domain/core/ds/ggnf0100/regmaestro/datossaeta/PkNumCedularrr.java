package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.regmaestro.datossaeta;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PK-NUM-CEDULARRR.
 *
 */
@Setter
@Getter
public class PkNumCedularrr extends DataStruct {
    // Properties
    private AlphanumericVar pkNumCedulaXx = new AlphanumericVar(this, 2);    // 07 PK-NUM-CEDULA-XX XX
    private AlphanumericVar pkNumCedulaX = new AlphanumericVar(this, 1);     // 07 PK-NUM-CEDULA-X X
    private AlphanumericVar pkNumCedulaR17 = new AlphanumericVar(this, 17);  // 07 PK-NUM-CEDULA-R17 X(17)

    public PkNumCedularrr() {
        super();
        initialize();
    }

    public PkNumCedularrr(DataContainer parent) {
        super(parent);
    }

    public PkNumCedularrr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPkNumCedulaXx(final String pkNumCedulaXx) {
        this.pkNumCedulaXx.setValue(pkNumCedulaXx);
    }

    public void setPkNumCedulaXx(final IAlphanumericValue pkNumCedulaXx) {
        this.pkNumCedulaXx.setValue(pkNumCedulaXx);
    }

    public void setPkNumCedulaX(final String pkNumCedulaX) {
        this.pkNumCedulaX.setValue(pkNumCedulaX);
    }

    public void setPkNumCedulaX(final IAlphanumericValue pkNumCedulaX) {
        this.pkNumCedulaX.setValue(pkNumCedulaX);
    }

    public void setPkNumCedulaR17(final String pkNumCedulaR17) {
        this.pkNumCedulaR17.setValue(pkNumCedulaR17);
    }

    public void setPkNumCedulaR17(final IAlphanumericValue pkNumCedulaR17) {
        this.pkNumCedulaR17.setValue(pkNumCedulaR17);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.pkNumCedulaXx.clear();
        this.pkNumCedulaX.clear();
        this.pkNumCedulaR17.clear();
    }

}

