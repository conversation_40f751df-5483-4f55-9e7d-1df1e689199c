package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.linea.Det1;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0800.linea.Det3;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: LINEA.
 *
 */
// 60004500*                                                                 00450000
@Setter
@Getter
public class Linea extends DataStruct {
    // Properties
    private AlphanumericVar linSalto = new AlphanumericVar(this, 1);         // 03 LIN-SALTO X
    private AlphanumericVar linResto = new AlphanumericVar(this, 132);       // 03 LIN-RESTO X(132) REDEFINED BY DET3
    private Det1 det1 = new  Det1(this, this.linResto);                      // 03 DET1 REDEFINES LIN-RESTO
    private Det3 det3 = new  Det3(this, this.linResto);                      // 03 DET3 REDEFINES LIN-RESTO

    public Linea() {
        super();
        initialize();
    }

    public Linea(DataContainer parent) {
        super(parent);
    }

    public Linea(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setLinSalto(final String linSalto) {
        this.linSalto.setValue(linSalto);
    }

    public void setLinSalto(final IAlphanumericValue linSalto) {
        this.linSalto.setValue(linSalto);
    }

    public void setLinResto(final String linResto) {
        this.linResto.setValue(linResto);
    }

    public void setLinResto(final IAlphanumericValue linResto) {
        this.linResto.setValue(linResto);
    }


    // Deeper properties

    // Property: det1.d1AgtIdr -> 05 D1-AGT-IDR 9(9)
    public UnsignedNumericVar getD1AgtIdr() {
        return this.det1.getD1AgtIdr();
    }

    public void setD1AgtIdr(final int d1AgtIdr) {
        this.det1.setD1AgtIdr(d1AgtIdr);
    }

    public void setD1AgtIdr(final INumericValue d1AgtIdr) {
        this.det1.setD1AgtIdr(d1AgtIdr);
    }

    // Property: det1.filler1 -> 05 FILLER X(4)
    public AlphanumericVar getFiller1() {
        return this.det1.getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.det1.setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.det1.setFiller1(filler1);
    }

    // Property: det1.d1CnaNum -> 05 D1-CNA-NUM ZZZ9
    public NumericEditedVar getD1CnaNum() {
        return this.det1.getD1CnaNum();
    }

    public void setD1CnaNum(final String d1CnaNum) {
        this.det1.setD1CnaNum(d1CnaNum);
    }

    public void setD1CnaNum(final IValue d1CnaNum) {
        this.det1.setD1CnaNum(d1CnaNum);
    }

    // Property: det1.filler2 -> 05 FILLER X(4)
    public AlphanumericVar getFiller2() {
        return this.det1.getFiller2();
    }

    public void setFiller2(final String filler2) {
        this.det1.setFiller2(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.det1.setFiller2(filler2);
    }

    // Property: det1.d1FlaNum -> 05 D1-FLA-NUM X(8)
    public AlphanumericVar getD1FlaNum() {
        return this.det1.getD1FlaNum();
    }

    public void setD1FlaNum(final String d1FlaNum) {
        this.det1.setD1FlaNum(d1FlaNum);
    }

    public void setD1FlaNum(final IAlphanumericValue d1FlaNum) {
        this.det1.setD1FlaNum(d1FlaNum);
    }

    // Property: det1.filler3 -> 05 FILLER X(4)
    public AlphanumericVar getFiller3() {
        return this.det1.getFiller3();
    }

    public void setFiller3(final String filler3) {
        this.det1.setFiller3(filler3);
    }

    public void setFiller3(final IAlphanumericValue filler3) {
        this.det1.setFiller3(filler3);
    }

    // Property: det1.d1RamCve -> 05 D1-RAM-CVE X(5)
    public AlphanumericVar getD1RamCve() {
        return this.det1.getD1RamCve();
    }

    public void setD1RamCve(final String d1RamCve) {
        this.det1.setD1RamCve(d1RamCve);
    }

    public void setD1RamCve(final IAlphanumericValue d1RamCve) {
        this.det1.setD1RamCve(d1RamCve);
    }

    // Property: det1.filler4 -> 05 FILLER X(4)
    public AlphanumericVar getFiller4() {
        return this.det1.getFiller4();
    }

    public void setFiller4(final String filler4) {
        this.det1.setFiller4(filler4);
    }

    public void setFiller4(final IAlphanumericValue filler4) {
        this.det1.setFiller4(filler4);
    }

    // Property: det1.d1SraCve -> 05 D1-SRA-CVE X(4)
    public AlphanumericVar getD1SraCve() {
        return this.det1.getD1SraCve();
    }

    public void setD1SraCve(final String d1SraCve) {
        this.det1.setD1SraCve(d1SraCve);
    }

    public void setD1SraCve(final IAlphanumericValue d1SraCve) {
        this.det1.setD1SraCve(d1SraCve);
    }

    // Property: det1.filler5 -> 05 FILLER X(4)
    public AlphanumericVar getFiller5() {
        return this.det1.getFiller5();
    }

    public void setFiller5(final String filler5) {
        this.det1.setFiller5(filler5);
    }

    public void setFiller5(final IAlphanumericValue filler5) {
        this.det1.setFiller5(filler5);
    }

    // Property: det1.d1Obs -> 05 D1-OBS X(20)
    public AlphanumericVar getD1Obs() {
        return this.det1.getD1Obs();
    }

    public void setD1Obs(final String d1Obs) {
        this.det1.setD1Obs(d1Obs);
    }

    public void setD1Obs(final IAlphanumericValue d1Obs) {
        this.det1.setD1Obs(d1Obs);
    }

    // Property: det1.filler6 -> 05 FILLER X(62)
    public AlphanumericVar getFiller6() {
        return this.det1.getFiller6();
    }

    public void setFiller6(final String filler6) {
        this.det1.setFiller6(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.det1.setFiller6(filler6);
    }


    // Deeper properties

    // Property: det3.filler7 -> 10 FILLER X(10)
    public AlphanumericVar getFiller7() {
        return this.det3.getFiller7();
    }

    public void setFiller7(final String filler7) {
        this.det3.setFiller7(filler7);
    }

    public void setFiller7(final IAlphanumericValue filler7) {
        this.det3.setFiller7(filler7);
    }

    // Property: det3.det12Letrero -> 10 DET12-LETRERO X(25)
    public AlphanumericVar getDet12Letrero() {
        return this.det3.getDet12Letrero();
    }

    public void setDet12Letrero(final String det12Letrero) {
        this.det3.setDet12Letrero(det12Letrero);
    }

    public void setDet12Letrero(final IAlphanumericValue det12Letrero) {
        this.det3.setDet12Letrero(det12Letrero);
    }

    // Property: det3.det12Total -> 10 DET12-TOTAL ZZZZZZ9
    public NumericEditedVar getDet12Total() {
        return this.det3.getDet12Total();
    }

    public void setDet12Total(final String det12Total) {
        this.det3.setDet12Total(det12Total);
    }

    public void setDet12Total(final IValue det12Total) {
        this.det3.setDet12Total(det12Total);
    }

    // Property: det3.filler8 -> 10 FILLER X(90)
    public AlphanumericVar getFiller8() {
        return this.det3.getFiller8();
    }

    public void setFiller8(final String filler8) {
        this.det3.setFiller8(filler8);
    }

    public void setFiller8(final IAlphanumericValue filler8) {
        this.det3.setFiller8(filler8);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.linSalto.clear();
        this.linResto.clear();
    }

}

