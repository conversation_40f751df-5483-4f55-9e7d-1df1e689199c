package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.wsfechas.WsFechaMovR;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.wsfechas.WsFechaAltaR;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FECHAS.
 *
 */
@Setter
@Getter
public class WsFechas { // VStruct
    // Properties
    private int wsProcesoAaaa;                                              // 03 WS-PROCESO-AAAA S9(04) COMPUTATIONAL-3
    private int wsProcesoMm;                                                // 03 WS-PROCESO-MM S9(02) COMPUTATIONAL-3
    private String wsFechaPaso = "";                                        // 03 WS-FECHA-PASO X(08)
    private AlphanumericVar wsFechaMov = new AlphanumericVar(10);            // 03 WS-FECHA-MOV X(10) REDEFINED BY WS-FECHA-MOV-R
    private WsFechaMovR wsFechaMovR = new  WsFechaMovR(null, this.wsFechaMov);               // 03 WS-FECHA-MOV-R REDEFINES WS-FECHA-MOV
    private String wsFechaMovCev = "";                                      // 03 WS-FECHA-MOV-CEV X(10)
    private AlphanumericVar wsFechaAlta = new AlphanumericVar(10);           // 03 WS-FECHA-ALTA X(10) REDEFINED BY WS-FECHA-ALTA-R
    private WsFechaAltaR wsFechaAltaR = new  WsFechaAltaR(null, this.wsFechaAlta);           // 03 WS-FECHA-ALTA-R REDEFINES WS-FECHA-ALTA

    public WsFechas() {
        initialize();
    }



    public void setWsFechaMov(final String wsFechaMov) {
        this.wsFechaMov.setValue(wsFechaMov);
    }

    public void setWsFechaMov(final IAlphanumericValue wsFechaMov) {
        this.wsFechaMov.setValue(wsFechaMov);
    }

    public void setWsFechaAlta(final String wsFechaAlta) {
        this.wsFechaAlta.setValue(wsFechaAlta);
    }

    public void setWsFechaAlta(final IAlphanumericValue wsFechaAlta) {
        this.wsFechaAlta.setValue(wsFechaAlta);
    }


    // Deeper properties

    // Property: wsFechaMovR.wsFecMovAa -> 05 WS-FEC-MOV-AA 9(04)
    public UnsignedNumericVar getWsFecMovAa() {
        return this.wsFechaMovR.getWsFecMovAa();
    }

    public void setWsFecMovAa(final int wsFecMovAa) {
        this.wsFechaMovR.setWsFecMovAa(wsFecMovAa);
    }

    public void setWsFecMovAa(final INumericValue wsFecMovAa) {
        this.wsFechaMovR.setWsFecMovAa(wsFecMovAa);
    }

    // Property: wsFechaMovR.wsFecMovGuion_1 -> 05 WS-FEC-MOV-GUION-1 X(01)
    public AlphanumericVar getWsFecMovGuion_1() {
        return this.wsFechaMovR.getWsFecMovGuion_1();
    }

    public void setWsFecMovGuion_1(final String wsFecMovGuion_1) {
        this.wsFechaMovR.setWsFecMovGuion_1(wsFecMovGuion_1);
    }

    public void setWsFecMovGuion_1(final IAlphanumericValue wsFecMovGuion_1) {
        this.wsFechaMovR.setWsFecMovGuion_1(wsFecMovGuion_1);
    }

    // Property: wsFechaMovR.wsFecMovMm -> 05 WS-FEC-MOV-MM 9(02)
    public UnsignedNumericVar getWsFecMovMm() {
        return this.wsFechaMovR.getWsFecMovMm();
    }

    public void setWsFecMovMm(final int wsFecMovMm) {
        this.wsFechaMovR.setWsFecMovMm(wsFecMovMm);
    }

    public void setWsFecMovMm(final INumericValue wsFecMovMm) {
        this.wsFechaMovR.setWsFecMovMm(wsFecMovMm);
    }

    // Property: wsFechaMovR.wsFecMovGuion_2 -> 05 WS-FEC-MOV-GUION-2 X(01)
    public AlphanumericVar getWsFecMovGuion_2() {
        return this.wsFechaMovR.getWsFecMovGuion_2();
    }

    public void setWsFecMovGuion_2(final String wsFecMovGuion_2) {
        this.wsFechaMovR.setWsFecMovGuion_2(wsFecMovGuion_2);
    }

    public void setWsFecMovGuion_2(final IAlphanumericValue wsFecMovGuion_2) {
        this.wsFechaMovR.setWsFecMovGuion_2(wsFecMovGuion_2);
    }

    // Property: wsFechaMovR.wsFecMovDd -> 05 WS-FEC-MOV-DD 9(02)
    public UnsignedNumericVar getWsFecMovDd() {
        return this.wsFechaMovR.getWsFecMovDd();
    }

    public void setWsFecMovDd(final int wsFecMovDd) {
        this.wsFechaMovR.setWsFecMovDd(wsFecMovDd);
    }

    public void setWsFecMovDd(final INumericValue wsFecMovDd) {
        this.wsFechaMovR.setWsFecMovDd(wsFecMovDd);
    }


    // Deeper properties

    // Property: wsFechaAltaR.wsFecAltAa -> 05 WS-FEC-ALT-AA 9(04)
    public UnsignedNumericVar getWsFecAltAa() {
        return this.wsFechaAltaR.getWsFecAltAa();
    }

    public void setWsFecAltAa(final int wsFecAltAa) {
        this.wsFechaAltaR.setWsFecAltAa(wsFecAltAa);
    }

    public void setWsFecAltAa(final INumericValue wsFecAltAa) {
        this.wsFechaAltaR.setWsFecAltAa(wsFecAltAa);
    }

    // Property: wsFechaAltaR.filler1 -> 05 FILLER X(01)
    public AlphanumericVar getFiller1() {
        return this.wsFechaAltaR.getFiller1();
    }

    public void setFiller1(final String filler1) {
        this.wsFechaAltaR.setFiller1(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.wsFechaAltaR.setFiller1(filler1);
    }

    // Property: wsFechaAltaR.wsFecAltMm -> 05 WS-FEC-ALT-MM 9(02)
    public UnsignedNumericVar getWsFecAltMm() {
        return this.wsFechaAltaR.getWsFecAltMm();
    }

    public void setWsFecAltMm(final int wsFecAltMm) {
        this.wsFechaAltaR.setWsFecAltMm(wsFecAltMm);
    }

    public void setWsFecAltMm(final INumericValue wsFecAltMm) {
        this.wsFechaAltaR.setWsFecAltMm(wsFecAltMm);
    }

    // Property: wsFechaAltaR.filler2 -> 05 FILLER X(01)
    public AlphanumericVar getFiller2() {
        return this.wsFechaAltaR.getFiller2();
    }

    public void setFiller2(final String filler2) {
        this.wsFechaAltaR.setFiller2(filler2);
    }

    public void setFiller2(final IAlphanumericValue filler2) {
        this.wsFechaAltaR.setFiller2(filler2);
    }

    // Property: wsFechaAltaR.wsFecAltDd -> 05 WS-FEC-ALT-DD 9(02)
    public UnsignedNumericVar getWsFecAltDd() {
        return this.wsFechaAltaR.getWsFecAltDd();
    }

    public void setWsFecAltDd(final int wsFecAltDd) {
        this.wsFechaAltaR.setWsFecAltDd(wsFecAltDd);
    }

    public void setWsFecAltDd(final INumericValue wsFecAltDd) {
        this.wsFechaAltaR.setWsFecAltDd(wsFecAltDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsProcesoAaaa = 0;
        this.wsProcesoMm = 0;
        this.wsFechaPaso = "";
        this.wsFechaMov.clear();
        this.wsFechaMovCev = "";
        this.wsFechaAlta.clear();
    }

}

