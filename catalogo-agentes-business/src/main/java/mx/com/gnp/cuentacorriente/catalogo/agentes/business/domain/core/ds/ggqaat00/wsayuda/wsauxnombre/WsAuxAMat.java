package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.wsayuda.wsauxnombre;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-AUX-A-MAT.
 *
 */
@Setter
@Getter
public class WsAuxAMat extends DataStruct {
    // Properties
    private AlphanumericVarArray wsAuxApMat = new AlphanumericVarArray(this, 30, 1);         // 15 WS-AUX-AP-MAT X(01) [30]

    public WsAuxAMat() {
        super();
        initialize();
    }

    public WsAuxAMat(DataContainer parent) {
        super(parent);
    }

    public WsAuxAMat(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public AlphanumericVar getWsAuxApMat(int index) {
        return this.wsAuxApMat.at(index);
    }

    public void setWsAuxApMat(final String wsAuxApMat) {
        this.wsAuxApMat.setValue(wsAuxApMat);
    }

    public void setWsAuxApMat(final AlphanumericVarArray wsAuxApMat) {
        this.wsAuxApMat.setValue(wsAuxApMat);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAuxApMat.clear();
    }

}

