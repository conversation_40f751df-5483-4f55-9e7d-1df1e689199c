package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FEC-AAMMDDR.
 *
 */
// 1290081800*                                                                 08180000
@Setter
@Getter
public class WsFecAammddr extends DataStruct {
    // Properties
    private UnsignedNumericVar wsAa = new UnsignedNumericVar(this, 2, 0);    // 03 WS-AA 99
    private UnsignedNumericVar wsMm = new UnsignedNumericVar(this, 2, 0);    // 03 WS-MM 99
    private UnsignedNumericVar wsDd = new UnsignedNumericVar(this, 2, 0);    // 03 WS-DD 99

    public WsFecAammddr() {
        super();
        initialize();
    }

    public WsFecAammddr(DataContainer parent) {
        super(parent);
    }

    public WsFecAammddr(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsAa(final int wsAa) {
        this.wsAa.setValue(wsAa);
    }

    public void setWsAa(final BigDecimal wsAa) {
        this.wsAa.setValue(wsAa);
    }

    public void setWsAa(final INumericValue wsAa) {
        this.wsAa.setValue(wsAa);
    }

    public void setWsMm(final int wsMm) {
        this.wsMm.setValue(wsMm);
    }

    public void setWsMm(final BigDecimal wsMm) {
        this.wsMm.setValue(wsMm);
    }

    public void setWsMm(final INumericValue wsMm) {
        this.wsMm.setValue(wsMm);
    }

    public void setWsDd(final int wsDd) {
        this.wsDd.setValue(wsDd);
    }

    public void setWsDd(final BigDecimal wsDd) {
        this.wsDd.setValue(wsDd);
    }

    public void setWsDd(final INumericValue wsDd) {
        this.wsDd.setValue(wsDd);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsAa.clear();
        this.wsMm.clear();
        this.wsDd.clear();
    }

}

