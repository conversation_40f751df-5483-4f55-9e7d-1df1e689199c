package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-GFVTMND0-NULL.
 *
 */
// 1095063000*                                                                 06300000
// 1096063100*                                                                 06310000
@Setter
@Getter
public class WsGfvtmnd0Null { // VStruct
    // Properties
    private int mnd01;                                                      // 03 MND01 S9(4) COMPUTATIONAL
    private int mnd02;                                                      // 03 MND02 S9(4) COMPUTATIONAL
    private int mnd03;                                                      // 03 MND03 S9(4) COMPUTATIONAL
    private int mnd04;                                                      // 03 MND04 S9(4) COMPUTATIONAL
    private int mnd05;                                                      // 03 MND05 S9(4) COMPUTATIONAL

    public WsGfvtmnd0Null() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.mnd01 = 0;
        this.mnd02 = 0;
        this.mnd03 = 0;
        this.mnd04 = 0;
        this.mnd05 = 0;
    }

}

