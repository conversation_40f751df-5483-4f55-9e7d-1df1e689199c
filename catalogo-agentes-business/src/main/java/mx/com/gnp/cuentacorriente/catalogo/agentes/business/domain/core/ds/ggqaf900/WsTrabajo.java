package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaf900;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-TRABAJO.
 *
 */
// 202      *---                                                              
@Setter
@Getter
public class WsTrabajo { // VStruct
    // Properties
    private int wsOfnCve;                                                   // 05 WS-OFN-CVE S9(4) COMPUTATIONAL

    public WsTrabajo() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsOfnCve = 0;
    }

}

