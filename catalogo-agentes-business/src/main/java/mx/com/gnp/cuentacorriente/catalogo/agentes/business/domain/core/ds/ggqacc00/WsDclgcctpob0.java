package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGCCTPOB0.
 *
 */
@Setter
@Getter
public class WsDclgcctpob0 { // VStruct
    // Properties
    private int pobPobCve;                                                  // 10 POB-POB-CVE S9(9) COMPUTATIONAL
    private String pobPobNom = "";                                          // 10 POB-POB-NOM X(40)
    private String pobFecUltAct = "";                                       // 10 POB-FEC-ULT-ACT X(10)
    private String pobUsuCveAct = "";                                       // 10 POB-USU-CVE-ACT X(8)

    public WsDclgcctpob0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.pobPobCve = 0;
        this.pobPobNom = "";
        this.pobFecUltAct = "";
        this.pobUsuCveAct = "";
    }

}

