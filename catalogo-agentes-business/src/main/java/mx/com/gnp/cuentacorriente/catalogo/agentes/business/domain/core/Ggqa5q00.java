
/**
 *  Program: Ggqa5q00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.cte._Constants;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.AccessMode;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.RegAgentisr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.SwBanderas;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.LParams;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.LParamsSal;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IPgavQad5300AgentisrPort;
import mx.com.gnp.cuentacorriente.common.business.domain.model.PgavQad5300AgentisrModel;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqa5q00.RegAgentisrMapper;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqa5q00 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private RegAgentisr regAgentisr = new  RegAgentisr();                    // 01 REG-AGENTISR
    private int filstat = 0;                                                // 01 FILSTAT 9(02)
//  private VsamStatusCode vsamStatusCode = new  VsamStatusCode();               // 01 VSAM-STATUS-CODE // [@WARNING variable NOT USED]
    private SwBanderas swBanderas = new  SwBanderas();                       // 01 SW-BANDERAS
    private LParams lParams = new  LParams();                                // 01 L-PARAMS UsedAsParameter
    private LParamsSal lParamsSal = new  LParamsSal();                       // 01 L-PARAMS-SAL UsedAsParameter
    // Files



    // File AGENTISR
    private IPgavQad5300AgentisrPort agentisr;
    private RegAgentisrMapper regAgentisrMapper = new RegAgentisrMapper();
    private Optional<PgavQad5300AgentisrModel> pgavQad5300AgentisrModel;

    public Ggqa5q00(IOs390CblProgramContext context) {
        super(context);
        initFiles();
    }

    public void initFiles() {

        this.agentisr.setAccessMode(AccessMode.DYNAMIC);
        this.agentisr.onChangeFileStatus(status -> filstat = Integer.parseInt(status));

    }

    /**
     *  Main execution entry point of the program.
     */
    public void run(AlphanumericVar lParams, AlphanumericVar lParamsSal) {
        this.lParams.setValue(lParams);
        this.lParamsSal.setValue(lParamsSal);
        try {
            run();
        } finally {
            lParams.setValue(this.lParams);
            lParamsSal.setValue(this.lParamsSal);
        }
    }

    @Override
    public void run(Object... args) {
        assignInputArgument(args, 0, this.lParams);
        assignInputArgument(args, 1, this.lParamsSal);
        try {
            run();
        } finally {
            updateOutputArgument(args, 0, this.lParams);
            updateOutputArgument(args, 1, this.lParamsSal);
        }
    }

    public void run() {
        p000ModuloControl();
    }

    /**
     *  Paragraph: 000-MODULO-CONTROL.
     */
    void p000ModuloControl() {
        lParamsSal.getLRegfis().setSpaces();
        lParamsSal.getLNombre().setSpaces();
        lParamsSal.getLPcenvi().setSpaces();
        lParamsSal.getLPcevid().setSpaces();
        if (lParams.getLOption().isEqual("BUSQ")) {
            if (swBanderas.isSwVsamNo()) {
                abreVsam();
            }
            recuperaDatoFiscal();
        } else if (lParams.getLOption().isEqual("FIN ")) {
            cierraVsam();
        }
        throw new GobackException();
    }

    /**
     *  Paragraph: ABRE-VSAM.
     *
     */
    void abreVsam() {
        agentisr.openInput();
        if (filstat == 0) {
            swBanderas.setSwVsamSi();
        } else {
            display("ERROR AL ABRIR VSAM DE AGENTES");
            p8000ErrorEnVsam();
        }
    }

    /**
     *  Paragraph: RECUPERA-DATO-FISCAL.
     *
     */
    void recuperaDatoFiscal() {
        regAgentisr.setVsAgente(lParams.getLAgente());
        pgavQad5300AgentisrModel = agentisr.next();
        if (pgavQad5300AgentisrModel.isPresent()) {
            regAgentisrMapper.updateRecordFromModel(regAgentisr, pgavQad5300AgentisrModel.get());
        }
        if (filstat == 0) {
            lParamsSal.setLRegfis(regAgentisr.getVsRegfis());
            lParamsSal.setLNombre(regAgentisr.getVsNombre());
            lParamsSal.getLNombre().replaceAll(_Constants.LOW_VALUE, "");            lParamsSal.setLPcenvi(regAgentisr.getVsCedNovi());
            lParamsSal.setLPcevid(regAgentisr.getVsCedVida());
        } else {
            lParamsSal.setLPcenvi("00000");
            lParamsSal.setLPcevid("00000");
            display("************************************", "\\\\N");
            display(">> AGENTE NO HALLADO: ", lParams.getLAgente(), "  (GGQA5Q00)  FILE-STATUS ", filstat);
            display("************************************", "\\\\N");
        }
    }

    /**
     *  Paragraph: 8000-ERROR-EN-VSAM.
     *
     */
    void p8000ErrorEnVsam() {
        display("   ");
        display("==================================================");
        display("              E R R O R ---- V S A M              ");
        display("==================================================");
        display("   ");
        display("     FILE-STATUS FUE      --  ", filstat);
        display("     FAVOR DE CORREGIR EL ERROR ");
        display("==================================================");
        returnCode.setValue(16);
        throw new GobackException();
    }

    /**
     *  Paragraph: CIERRA-VSAM.
     *
     */
    void cierraVsam() {
        agentisr.close();
        if (filstat != 0) {
            display("ERROR AL CERRAR VSAM DE AGENTES");
            p8000ErrorEnVsam();
        }
    }

    @Autowired
    public void setIPgavQad5300AgentisrPort(IPgavQad5300AgentisrPort agentisr) {
        this.agentisr = agentisr;
        this.agentisr.setProgramContext(getProgramContext());
    }
}
