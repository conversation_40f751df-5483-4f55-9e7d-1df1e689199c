package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wstimestampr;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-MICR-TIMESTAMP.
 *
 */
@Setter
@Getter
public class WsMicrTimestamp extends DataStruct {
    // Properties
    private AlphanumericVar filler6 = new AlphanumericVar(this, 1);          // 05 FILLER X(01)
    private UnsignedNumericVar wsMicrosegTimestamp = new UnsignedNumericVar(this, 6, 0);     // 05 WS-MICROSEG-TIMESTAMP 9(06)

    public WsMicrTimestamp() {
        super();
        initialize();
    }

    public WsMicrTimestamp(DataContainer parent) {
        super(parent);
    }

    public WsMicrTimestamp(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setFiller6(final String filler6) {
        this.filler6.setValue(filler6);
    }

    public void setFiller6(final IAlphanumericValue filler6) {
        this.filler6.setValue(filler6);
    }

    public void setWsMicrosegTimestamp(final int wsMicrosegTimestamp) {
        this.wsMicrosegTimestamp.setValue(wsMicrosegTimestamp);
    }

    public void setWsMicrosegTimestamp(final BigDecimal wsMicrosegTimestamp) {
        this.wsMicrosegTimestamp.setValue(wsMicrosegTimestamp);
    }

    public void setWsMicrosegTimestamp(final INumericValue wsMicrosegTimestamp) {
        this.wsMicrosegTimestamp.setValue(wsMicrosegTimestamp);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.filler6.setValue(".");
        this.wsMicrosegTimestamp.clear();
    }

}

