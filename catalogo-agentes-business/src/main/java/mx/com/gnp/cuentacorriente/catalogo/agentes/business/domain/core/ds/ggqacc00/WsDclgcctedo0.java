package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGCCTEDO0.
 *
 */
@Setter
@Getter
public class WsDclgcctedo0 { // VStruct
    // Properties
    private String edoEdoCve = "";                                          // 10 EDO-EDO-CVE X(4)
    private String edoPaiCve = "";                                          // 10 EDO-PAI-CVE X(3)
    private String edoEdoNom = "";                                          // 10 EDO-EDO-NOM X(30)
    private int edoEdoCodNum;                                               // 10 EDO-EDO-COD-NUM S9(2)V COMPUTATIONAL-3
    private String edoFecUltAct = "";                                       // 10 EDO-FEC-ULT-ACT X(10)
    private String edoUsuCveAct = "";                                       // 10 EDO-USU-CVE-ACT X(8)

    public WsDclgcctedo0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.edoEdoCve = "";
        this.edoPaiCve = "";
        this.edoEdoNom = "";
        this.edoEdoCodNum = 0;
        this.edoFecUltAct = "";
        this.edoUsuCveAct = "";
    }

}

