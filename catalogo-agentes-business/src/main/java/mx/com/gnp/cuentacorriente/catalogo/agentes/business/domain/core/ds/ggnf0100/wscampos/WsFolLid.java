package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0100.wscampos;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FOL-LID.
 *
 */
// 1212      * SE USA PARA FOLIO LIDER                                         07431002
@Setter
@Getter
public class WsFolLid extends DataStruct {
    // Properties
    private AlphanumericVar wsFolLid0 = new AlphanumericVar(this, 1);        // 05 WS-FOL-LID0 X
    private UnsignedNumericVar wsFolLid1 = new UnsignedNumericVar(this, 7, 0);               // 05 WS-FOL-LID1 9(7)

    public WsFolLid() {
        super();
        initialize();
    }

    public WsFolLid(DataContainer parent) {
        super(parent);
    }

    public WsFolLid(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFolLid0(final String wsFolLid0) {
        this.wsFolLid0.setValue(wsFolLid0);
    }

    public void setWsFolLid0(final IAlphanumericValue wsFolLid0) {
        this.wsFolLid0.setValue(wsFolLid0);
    }

    public void setWsFolLid1(final int wsFolLid1) {
        this.wsFolLid1.setValue(wsFolLid1);
    }

    public void setWsFolLid1(final BigDecimal wsFolLid1) {
        this.wsFolLid1.setValue(wsFolLid1);
    }

    public void setWsFolLid1(final INumericValue wsFolLid1) {
        this.wsFolLid1.setValue(wsFolLid1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFolLid0.clear();
        this.wsFolLid1.clear();
    }

}

