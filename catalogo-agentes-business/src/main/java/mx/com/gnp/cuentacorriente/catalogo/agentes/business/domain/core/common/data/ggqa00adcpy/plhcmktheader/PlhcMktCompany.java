package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.common.data.wsinterfaz.ggqa00adcpy.plhcmktheader;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: PLHC-MKT-COMPANY.
 *
 */
// 521      *=>     0001-00005                                                00120000
@Setter
@Getter
public class PlhcMktCompany extends DataStruct {
    // Properties
    private AlphanumericVar plhcMktCompanyId = new AlphanumericVar(this, 7);                 // 15 PLHC-MKT-COMPANY-ID X(07)
    private AlphanumericVar filler1 = new AlphanumericVar(this, 2);          // 15 FILLER X(02)

    public PlhcMktCompany() {
        super();
        initialize();
    }

    public PlhcMktCompany(DataContainer parent) {
        super(parent);
    }

    public PlhcMktCompany(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setPlhcMktCompanyId(final String plhcMktCompanyId) {
        this.plhcMktCompanyId.setValue(plhcMktCompanyId);
    }

    public void setPlhcMktCompanyId(final IAlphanumericValue plhcMktCompanyId) {
        this.plhcMktCompanyId.setValue(plhcMktCompanyId);
    }

    public void setFiller1(final String filler1) {
        this.filler1.setValue(filler1);
    }

    public void setFiller1(final IAlphanumericValue filler1) {
        this.filler1.setValue(filler1);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.plhcMktCompanyId.setValue("");
        this.filler1.setValue("");
    }

}

