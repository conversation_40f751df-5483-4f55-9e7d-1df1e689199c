package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqac600.tablaexcagt.TabExcAgt;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-EXC-AGT.
 *
 */
// 792      *----                                                             
// 793      * TABLA                                                        *  
// 794      *----                                                             
@Setter
@Getter
public class TablaExcAgt { // VStruct
    // Properties
    private TabExcAgt tabExcAgt = new  TabExcAgt(100);                       // 02 TAB-EXC-AGT [100]

    public TablaExcAgt() {
        initialize();
    }



    public TabExcAgt getTabExcAgt(int index) {
        return this.tabExcAgt.at(index);
    }


    // Deeper properties

    // Property: tabExcAgt.tbAgente -> 07 TB-AGENTE 9(05)
    public UnsignedNumericVar getTbAgente() {
        return this.tabExcAgt.getTbAgente();
    }

    public void setTbAgente(final int tbAgente) {
        this.tabExcAgt.setTbAgente(tbAgente);
    }

    public void setTbAgente(final INumericValue tbAgente) {
        this.tabExcAgt.setTbAgente(tbAgente);
    }

    // Property: tabExcAgt.tbIndExcAgt -> 07 TB-IND-EXC-AGT X(01)
    public AlphanumericVar getTbIndExcAgt() {
        return this.tabExcAgt.getTbIndExcAgt();
    }

    public void setTbIndExcAgt(final String tbIndExcAgt) {
        this.tabExcAgt.setTbIndExcAgt(tbIndExcAgt);
    }

    public void setTbIndExcAgt(final IAlphanumericValue tbIndExcAgt) {
        this.tabExcAgt.setTbIndExcAgt(tbIndExcAgt);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tabExcAgt.initialize();
    }

}

