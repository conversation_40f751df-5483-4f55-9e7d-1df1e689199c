package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablacatesa;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TAB-CAT-ESA.
 *
 */
@Setter
@Getter
public class TabCatEsa extends DataStructArray<TabCatEsa> {
    // Properties
    private AlphanumericVar tbEsaDes = new AlphanumericVar(this, 10);        // 07 TB-ESA-DES X(10)

    public TabCatEsa(int occurs) {
        super();
        reAllocate(occurs);
    }

    public TabCatEsa(DataContainer parent, int occurs) {
        super(parent);
        reAllocate(occurs);
    }

    public TabCatEsa(DataContainer parent, int idx, int sizeElement) {
        super(parent, idx, sizeElement);
    }

    public TabCatEsa(DataContainer parent, DataContainer redefines, int occurs) {
        super(parent, redefines);
        reAllocate(occurs);
    }


    // get a single element in the array
    public TabCatEsa at(int idx) {
        return new TabCatEsa(this, idx, getSizeElement());
    }

    public void setTbEsaDes(final String tbEsaDes) {
        this.tbEsaDes.setValue(tbEsaDes);
    }

    public void setTbEsaDes(final IAlphanumericValue tbEsaDes) {
        this.tbEsaDes.setValue(tbEsaDes);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tbEsaDes.clear();
    }

}

