package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-DCLGFVTMTE0.
 *
 */
@Setter
@Getter
public class WsDclgfvtmte0 { // VStruct
    // Properties
    private int mteMteCve;                                                  // 10 MTE-MTE-CVE S9(4) COMPUTATIONAL
    private String mteMteDes = "";                                          // 10 MTE-MTE-DES X(40)
    private int mteEsaCve;                                                  // 10 MTE-ESA-CVE S9(4) COMPUTATIONAL
    private String mteFecUltAct = "";                                       // 10 MTE-FEC-ULT-ACT X(10)
    private String mteUsuCveAct = "";                                       // 10 MTE-USU-CVE-ACT X(8)

    public WsDclgfvtmte0() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.mteMteCve = 0;
        this.mteMteDes = "";
        this.mteEsaCve = 0;
        this.mteFecUltAct = "";
        this.mteUsuCveAct = "";
    }

}

