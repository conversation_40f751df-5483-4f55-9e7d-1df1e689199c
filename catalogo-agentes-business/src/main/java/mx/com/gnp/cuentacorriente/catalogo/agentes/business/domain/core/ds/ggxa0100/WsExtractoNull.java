package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggxa0100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-EXTRACTO-NULL.
 *
 */
@Setter
@Getter
public class WsExtractoNull { // VStruct
    // Properties
    private int agtAgtIdr1;                                                 // 10 AGT-AGT-IDR1 S9(4) COMPUTATIONAL
    private int agtAgtNom1;                                                 // 10 AGT-AGT-NOM1 S9(4) COMPUTATIONAL
    private int agtAgtApePat1;                                              // 10 AGT-AGT-APE-PAT1 S9(4) COMPUTATIONAL
    private int agtAgtApeMat1;                                              // 10 AGT-AGT-APE-MAT1 S9(4) COMPUTATIONAL
    private int agtAgtNip1;                                                 // 10 AGT-AGT-NIP1 S9(4) COMPUTATIONAL
    private int cmaCmaDes1;                                                 // 10 CMA-CMA-DES1 S9(4) COMPUTATIONAL
    private int cnaCnaNum1;                                                 // 10 CNA-CNA-NUM1 S9(4) COMPUTATIONAL
    private int cnaEmpCve1;                                                 // 10 CNA-EMP-CVE1 S9(4) COMPUTATIONAL
    private int cnaOfnCve1;                                                 // 10 CNA-OFN-CVE1 S9(4) COMPUTATIONAL
    private int cnaTfpCve1;                                                 // 10 CNA-TFP-CVE1 S9(4) COMPUTATIONAL
    private int cnaGerZonEnv1;                                              // 10 CNA-GER-ZON-ENV1 S9(4) COMPUTATIONAL
    private int flaFlaNumN;                                                 // 10 FLA-FLA-NUM-N S9(4) COMPUTATIONAL
    private int ceoCeoCve1;                                                 // 10 CEO-CEO-CVE1 S9(4) COMPUTATIONAL
    private int ceoCeoNom1;                                                 // 10 CEO-CEO-NOM1 S9(4) COMPUTATIONAL
    private int wsOrvCve1;                                                  // 10 WS-ORV-CVE1 S9(4) COMPUTATIONAL
    private int agtRazSoc1;                                                 // 10 AGT-RAZ-SOC1 S9(4) COMPUTATIONAL
    private int agtPefCve1;                                                 // 10 AGT-PEF-CVE1 S9(4) COMPUTATIONAL

    public WsExtractoNull() {
        initialize();
    }




    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.agtAgtIdr1 = 0;
        this.agtAgtNom1 = 0;
        this.agtAgtApePat1 = 0;
        this.agtAgtApeMat1 = 0;
        this.agtAgtNip1 = 0;
        this.cmaCmaDes1 = 0;
        this.cnaCnaNum1 = 0;
        this.cnaEmpCve1 = 0;
        this.cnaOfnCve1 = 0;
        this.cnaTfpCve1 = 0;
        this.cnaGerZonEnv1 = 0;
        this.flaFlaNumN = 0;
        this.ceoCeoCve1 = 0;
        this.ceoCeoNom1 = 0;
        this.wsOrvCve1 = 0;
        this.agtRazSoc1 = 0;
        this.agtPefCve1 = 0;
    }

}

