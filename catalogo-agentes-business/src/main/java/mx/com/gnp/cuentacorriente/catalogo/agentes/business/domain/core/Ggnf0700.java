
/**
 *  Program: Ggnf0700.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.common.cbl.functions.Functions;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.RegAgentes;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.Linea;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsDatosAbend;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsCodigos;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagt0cpy.Dclgfvtagt0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcna0cpy.Dclgfvtcna0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtfla0cpy.Dclgfvtfla0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtmte0cpy.Dclgfvtmte0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdrc0cpy.Dclgfvtdrc0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctorv0cpy.Dclgcctorv0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagc4cpy.Dclgfvtagc0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsGfvtagt0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsGfvtcna0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsGfvtfla0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsGfvtfla0NullAux;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsGfvtfla2Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsGfvtagc0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsGcctorv0Null;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.Tit11;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.Tit21;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.Tit31;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.Tit41;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsTotales;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsIndicadores;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsRegistros;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.WsTimestampr;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.TbDclgfvtfla0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.TbDclgfvtagc0;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcna0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdrc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtmte0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctorv0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtfla0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.ISysdummy1Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcna0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdrc0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtmte0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctorv0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagc0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagt0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtfla0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggnf0700 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private AlphanumericVar regFolios = new AlphanumericVar(106);            // 01 REG-FOLIOS X(106) UsedAsReferenceParameter
    private RegAgentes regAgentes = new  RegAgentes();                       // 01 REG-AGENTES UsedAsReferenceParameter
    private Linea linea = new  Linea();                                      // 01 LINEA UsedAsReferenceParameter
//  private String wsWork = "WORKING";                                      // 01 WS-WORK X(20) // [@WARNING variable NOT USED]
    private WsDatosAbend wsDatosAbend = new  WsDatosAbend();                 // 01 WS-DATOS-ABEND
    private int db2ReturnCode;                                              // 01 DB2-RETURN-CODE S9(9) COMPUTATIONAL
    // Level 88 - Conditional names
    private static final int DB2_OK = 0;
    private static final int DB2_NOTFND = 100;
    private static final int DB2_DUPREC = -803;
    private static final int DB2_NOTUNI = -811;
    private static final int DB2_DATETIME = -181;
    private static final int DB2_SYSTEM = -911;

    private WsCodigos wsCodigos = new  WsCodigos();                          // 01 WS-CODIGOS

    // Copy file QA$CBLCPY$GFVTAGT0
    private Dclgfvtagt0 dclgfvtagt0 = new  Dclgfvtagt0();                    // 01 DCLGFVTAGT0
    // End copy file QA$CBLCPY$GFVTAGT0

    // Copy file QA$CBLCPY$GFVTCNA0
    private Dclgfvtcna0 dclgfvtcna0 = new  Dclgfvtcna0();                    // 01 DCLGFVTCNA0
    // End copy file QA$CBLCPY$GFVTCNA0

    // Copy file QA$CBLCPY$GFVTFLA0
    private Dclgfvtfla0 dclgfvtfla0 = new  Dclgfvtfla0();                    // 01 DCLGFVTFLA0 UsedAsParameter
    // End copy file QA$CBLCPY$GFVTFLA0

    // Copy file QA$CBLCPY$GFVTMTE0
    private Dclgfvtmte0 dclgfvtmte0 = new  Dclgfvtmte0();                    // 01 DCLGFVTMTE0
    // End copy file QA$CBLCPY$GFVTMTE0

    // Copy file QA$CBLCPY$GFVTDRC0
    private Dclgfvtdrc0 dclgfvtdrc0 = new  Dclgfvtdrc0();                    // 01 DCLGFVTDRC0
    // End copy file QA$CBLCPY$GFVTDRC0

    // Copy file QA$CBLCPY$GCCTORV0
    private Dclgcctorv0 dclgcctorv0 = new  Dclgcctorv0();                    // 01 DCLGCCTORV0
    // End copy file QA$CBLCPY$GCCTORV0

    // Copy file QA$CBLCPY$GFVTAGC4
    private Dclgfvtagc0 dclgfvtagc0 = new  Dclgfvtagc0();                    // 01 DCLGFVTAGC0
    // End copy file QA$CBLCPY$GFVTAGC4

    // Level 1
    private WsGfvtagt0Null wsGfvtagt0Null = new  WsGfvtagt0Null();           // 01 WS-GFVTAGT0-NULL
    private WsGfvtcna0Null wsGfvtcna0Null = new  WsGfvtcna0Null();           // 01 WS-GFVTCNA0-NULL
    private WsGfvtfla0Null wsGfvtfla0Null = new  WsGfvtfla0Null();           // 01 WS-GFVTFLA0-NULL REDEFINED BY WS-GFVTFLA0-NULLR UsedAsParameter
//  private WsGfvtfla0Nullr wsGfvtfla0Nullr = new  WsGfvtfla0Nullr(null, this.wsGfvtfla0Null);   // 01 WS-GFVTFLA0-NULLR REDEFINES WS-GFVTFLA0-NULL // [@WARNING variable NOT USED]
    private WsGfvtfla0NullAux wsGfvtfla0NullAux = new  WsGfvtfla0NullAux();  // 01 WS-GFVTFLA0-NULL-AUX UsedAsParameter
    private WsGfvtfla2Null wsGfvtfla2Null = new  WsGfvtfla2Null();           // 01 WS-GFVTFLA2-NULL
    private WsGfvtagc0Null wsGfvtagc0Null = new  WsGfvtagc0Null();           // 01 WS-GFVTAGC0-NULL
    private WsGcctorv0Null wsGcctorv0Null = new  WsGcctorv0Null();           // 01 WS-GCCTORV0-NULL REDEFINED BY WS-GCCTORV0-NULLR
//  private WsGcctorv0Nullr wsGcctorv0Nullr = new  WsGcctorv0Nullr(null, this.wsGcctorv0Null);   // 01 WS-GCCTORV0-NULLR[@WARNING - REDEFINES OVERFLOW WS-GCCTORV0-NULL with size 36] // [@WARNING variable NOT USED]
    private Tit11 tit11 = new  Tit11();                                      // 01 TIT11 UsedAsParameter
    private Tit21 tit21 = new  Tit21();                                      // 01 TIT21 UsedAsParameter
    private Tit31 tit31 = new  Tit31();                                      // 01 TIT31 UsedAsParameter
    private Tit41 tit41 = new  Tit41();                                      // 01 TIT41 UsedAsParameter
    private WsTotales wsTotales = new  WsTotales();                          // 01 WS-TOTALES
    private WsIndicadores wsIndicadores = new  WsIndicadores();              // 01 WS-INDICADORES
    private WsRegistros wsRegistros = new  WsRegistros();                    // 01 WS-REGISTROS
//  private WsCurrent wsCurrent = new  WsCurrent();                              // 01 WS-CURRENT // [@WARNING variable NOT USED]
//  private Db2AaaaMmDd db2AaaaMmDd = new  Db2AaaaMmDd();                        // 01 DB2-AAAA-MM-DD // [@WARNING variable NOT USED]
//  private WsFecAaaa wsFecAaaa = new  WsFecAaaa();                              // 01 WS-FEC-AAAA // [@WARNING variable NOT USED]
//  private WsFecAammddr wsFecAammddr = new  WsFecAammddr();                     // 01 WS-FEC-AAMMDDR REDEFINED BY WS-FEC-AAMMDD // [@WARNING variable NOT USED]
//  private UnsignedNumericVar wsFecAammdd = new UnsignedNumericVar(null, this.wsFecAammddr, 6, 0);              // 01 WS-FEC-AAMMDD REDEFINES WS-FEC-AAMMDDR 9(6) // [@WARNING variable NOT USED]
    private String wsTimestamp = "";                                        // 01 WS-TIMESTAMP X(26)
    private WsTimestampr wsTimestampr = new  WsTimestampr();                 // 01 WS-TIMESTAMPR
    private int fsFolios = 0;                                               // 01 FS-FOLIOS 9(02)
    private int fsAgentes = 0;                                              // 01 FS-AGENTES 9(02)
    private int agFinArch = 0;                                              // 01 AG-FIN-ARCH 9(01)
    private int agMayor = 0;                                                // 01 AG-MAYOR 9(01)
    private TbDclgfvtfla0 tbDclgfvtfla0 = new  TbDclgfvtfla0();              // 01 TB-DCLGFVTFLA0 UsedAsReferenceParameter
    private TbDclgfvtagc0 tbDclgfvtagc0 = new  TbDclgfvtagc0();              // 01 TB-DCLGFVTAGC0 UsedAsReferenceParameter
    private int cpxEsaCveFla;                                               // 01 CPX-ESA-CVE-FLA S9(4) COMPUTATIONAL

    private Sqlca sqlca;
    // Files
    // -----> CBA 15/02/2001
    private ISequentialFile folios;
    private ISequentialFile agentes;
    // <----- CBA
    private ISequentialFile listado;



    // Declare Sql Ports
    private IGfvtcna0Port gfvtcna0Port;
    private IGfvtdrc0Port gfvtdrc0Port;
    private IGfvtmte0Port gfvtmte0Port;
    private IGcctorv0Port gcctorv0Port;
    private IGfvtagc0Port gfvtagc0Port;
    private IGfvtagt0Port gfvtagt0Port;
    private IGfvtfla0Port gfvtfla0Port;
    private ISysdummy1Port sysdummy1Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtcna0Model> gfvtcna0Model;
    private Optional<Gfvtdrc0Model> gfvtdrc0Model;
    private Optional<Gfvtmte0Model> gfvtmte0Model;
    private Optional<Gcctorv0Model> gcctorv0Model;
    private Optional<Gfvtagc0Model> gfvtagc0Model;
    private Optional<Gfvtagt0Model> gfvtagt0Model;
    private Optional<Gfvtfla0Model> gfvtfla0Model;

    // Declare Sql Cursors
    private ICursor<Gfvtfla0Model> gfvcfla2Cursor;
    private ICursor<Gfvtcna0Model> gfvccna1Cursor;

    public Ggnf0700(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initFiles();
    }

    public void initFiles() {
        // -----> CBA 15/02/2001

        this.folios = createSequentialFile("FOLIOS", regFolios);

        this.agentes = createSequentialFile("AGENTES", regAgentes);
        // <----- CBA

        this.listado = createSequentialFile("LISTADO", "GGNF07R1", linea);

        this.folios.onChangeFileStatus(status -> fsFolios = Integer.parseInt(status));

        this.agentes.onChangeFileStatus(status -> fsAgentes = Integer.parseInt(status));

        this.listado.setRecordLength(133);

    }

    public void run() {
        sqlDelayedParagraph();
        start();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     */
    void sqlDelayedParagraph() {
        if(gfvcfla2Cursor == null) {
            gfvcfla2Cursor = gfvtfla0Port.createCursorGfvcfla2_1(
                () -> Gfvtfla0Model.builder()
                    .agtIdr(dclgcctorv0.getOrvAgtIdrGte().toInt())
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                    .flaGerZon(dclgcctorv0.getOrvCve().toInt())
                .build());
        }
        if(gfvccna1Cursor == null) {
            gfvccna1Cursor = gfvtcna0Port.createCursorGfvccna1_2(
                () -> Gfvtcna0Model.builder()
                    .agtIdr(dclgcctorv0.getOrvAgtIdrGte().toInt())
                    .cnaGerZonEnv(dclgcctorv0.getOrvCve().toInt())
                .build());
        }
    }

    /**
     *  Paragraph: (START).
     */
    void start() {
        p1000Inicio();
        while (!(wsIndicadores.getWsFinFla() == 1)) {
            p2000ProcesoFolios();
        }
        p3000Fin();
        throw new GobackException();
    }

    /**
     *  Paragraph: 1000-INICIO.
     *
     * FUNCION INICIAL
     */
    void p1000Inicio() {
        listado.openOutput();
        //                                                                  08000002
        display("*************************************");
        display("**   INICIA EL PROGRAMA GGNF0700   **");
        display("*************************************");
        //                                                                  08040002
        dclgfvtagt0.initialize();
        dclgfvtcna0.initialize();
        dclgfvtfla0.initialize();
        dclgfvtmte0.initialize();
        dclgfvtdrc0.initialize();
        wsIndicadores.setWsFlaNum("");

        //                                                                  08110002
        genericModel = 
            sysdummy1Port.set(
                null);

        if (genericModel.isPresent()) {
            wsTimestamp = fromHostString(genericModel.get().values().get(0));
        }
        //                                                                  08150002
        wsTimestampr.setValue(wsTimestamp);
        p6100Enc1();
    }

    /**
     *  Paragraph: 2000-PROCESO-FOLIOS.
     *
     * 2000  PROCESO DE FOLIOS                                        *
     */
    void p2000ProcesoFolios() {
        wsDatosAbend.setWsProcesoLlama("2000-PROCESO-FOLIOS           ");

        p7850InicioGfvcfla0();
        while (!(wsIndicadores.getWsFinFla() == 1)) {
            p3000ActualizaOAlta();
        }
        p7850CierroGfvcfla0();
    }

    /**
     *  Paragraph: 3000-ACTUALIZA-O-ALTA.
     *
     * ACTUALIZA ALTA  A    GFVTAGC0
     */
    void p3000ActualizaOAlta() {
        //                                                                  08390002
        wsDatosAbend.setWsProcesoLlama("3000-ACTUALIZA-O-ALTA         ");

        // JLC/ABR/01-INI                                                   08410002
        // ****MOVE  0  TO  WS-FIN-AGC                                      08420002
        if (agFinArch != 1) {
            wsIndicadores.setWsFinAgc(0);

        }
        // JLC/ABR/01-FIN                                                   08460002
        p7100LeerGfvtagc0();
        if (wsIndicadores.getWsFinAgc() == 0) {
            p3100ActualizaGfvtagc0();
        } else {
            p3400AltaGfvtagc0();
        }
        //                                                                  08520002
        p7850LeerGfvtfla0();
    }

    /**
     *  Paragraph: 3100-ACTUALIZA-GFVTAGC0.
     *
     * ACTUALIZA A  GFVTAGC0
     */
    void p3100ActualizaGfvtagc0() {
        //     DISPLAY '3100-ACTUALIZA-GFVTAGC0 '                           08621002
        //     DISPLAY 'AGENTE : ' AGT-IDR OF DCLGFVTFLA0                   08622002
        //     DISPLAY 'AGENTE : ' FLA-NUM OF DCLGFVTFLA0                   08623002
        //                                                                  08624002
        wsIndicadores.setWsFinAgt(1);

        wsIndicadores.setWsFinCna(1);

        wsIndicadores.setWsFinMte(1);

        wsIndicadores.setWsFinOrv(0);

        p7100LeerGfvtagt0();
        if (wsIndicadores.getWsFinAgt() == 0) {
            //         DISPLAY 'SI HAY GFVTAGC0'                                08640002
            // ==>                                                              08650002
            p7100LeerGfvtcna0();
            if (wsIndicadores.getWsFinCna() == 0) {
                //             DISPLAY 'SI HAY GFVTCNA0'                            08680002
                p7100LeerGfvtmte0();
                if (wsIndicadores.getWsFinMte() == 0) {
                    //                 DISPLAY 'SI HAY GFVTMTE0'                        08710002
                    //                 IF AGC28 < 0                                     08720002
                    //                    DISPLAY 'AGC28 MENOR A CERO'                  08730002
                    //                    MOVE ZEROS TO ORV-AGT-IDR-GTE  OF DCLGCCTORV0 08740002
                    //                         DISPLAY 'ELIPSE ' FLA-NUM OF DCLGFVTFLA0 08750002
                    //                 ELSE                                             08760002
                    //                    DISPLAY 'AGC28 MAYOR A CERO'                  08770002
                    wsIndicadores.setWsGerZon(dclgfvtfla0.getFlaGerZon().toInt());

                    p7100LeerGcctorv0();
                    //                 END-IF                                           08800002
                    if (wsIndicadores.getWsFinOrv() == 0) {
                        //                     DISPLAY 'SI HAY ORV '                        08820002
                        //                     DISPLAY 'ORV-AGT-IDR-GTE OF DCLGCCTORV0'     08821002
                        //                     DISPLAY ORV-AGT-IDR-GTE OF DCLGCCTORV0       08822002
                        //                     DISPLAY 'AGT-IDR-GTE      OF DCLGFVTAGC0'    08823002
                        //                     DISPLAY AGT-IDR-GTE      OF DCLGFVTAGC0      08824002
                        p3200Pregunta();
                    }
                } else {
                    display("MTE-CVE   NO EXISTE CON FOLIO       ==> ", dclgfvtfla0.getAgtIdr(), " ", dclgfvtfla0.getCnaNum(), " ", dclgfvtfla0.getFlaNum(), " ", dclgfvtfla0.getMteCve());
                }
            } else {
                display("CONTRATO  NO EXISTE CON FOLIO       ==> ", dclgfvtfla0.getAgtIdr(), " ", dclgfvtfla0.getCnaNum(), " ", dclgfvtfla0.getFlaNum());
            }
        } else {
            display("AGENTE    NO EXISTE CON FOLIO       ==> ", dclgfvtfla0.getAgtIdr(), " ", dclgfvtfla0.getFlaNum());
        }
    }

    /**
     *  Paragraph: 3200-PREGUNTA.
     *
     * PREGUNTA SI CAMBIO ALGUN DATO
     */
    void p3200Pregunta() {
        if (dclgfvtfla0.getFlaNum().isEqual("P0011026")) {
            display("HOLAF ");
            display(dclgfvtagt0.getAgtApeMat());
            display(dclgfvtagc0.getAgtApeMat());
            display(dclgfvtagt0.getAgtApePat());
            display(dclgfvtagc0.getAgtApePat());
            display(dclgfvtagt0.getAgtNom());
            display(dclgfvtagc0.getAgtNom());
            display(dclgfvtagt0.getAgtRazSoc());
            display(dclgfvtagc0.getAgtRazSoc());
            display(dclgfvtfla0.getEmpCve());
            display(dclgfvtagc0.getEmpCve());
            display(dclgfvtmte0.getEsaCve());
            display(dclgfvtagc0.getEsaCve());
            display(dclgfvtfla0.getOfnCve());
            display(dclgfvtagc0.getOfnCve());
            display(dclgfvtfla0.getOrvCve());
            display(dclgfvtagc0.getOrvCve());
            display(dclgfvtagt0.getPefCve());
            display(dclgfvtagc0.getPefCve());
            display(dclgfvtfla0.getAgtIdr());
            display(dclgfvtagc0.getAgtIdr());
            display(dclgfvtfla0.getCnaNum());
            display(dclgfvtagc0.getCnaNum());
            display(dclgfvtcna0.getTfpCve());
            display(dclgfvtagc0.getTfpCve());
            display(dclgfvtcna0.getCnaIndGer());
            display(dclgfvtagc0.getCnaIndGer());
            display(dclgfvtfla0.getMteCve());
            display(dclgfvtagc0.getMteCve());
            display(dclgfvtfla0.getFlaFecMotEss());
            display(dclgfvtagc0.getFlaFecMotEss());
            display(dclgfvtcna0.getCnaGerZonEnv());
            display(dclgfvtagc0.getCnaGerZonEnv());
            display(dclgfvtdrc0.getDrcIdrAgt());
            display(dclgfvtagc0.getDrcIdrAgt());
            display(dclgfvtdrc0.getDrcIdrGte());
            display(dclgfvtagc0.getDrcIdrGte());
            display(dclgfvtfla0.getFlaDes());
            display(dclgfvtagc0.getFlaDes());
            display(dclgfvtagt0.getAgtTraEsp());
            display(dclgfvtagc0.getAgtTraEsp());
            display(dclgfvtcna0.getCnaFecCnx());
            display(dclgfvtagc0.getCnaFecCnx());
            display(dclgfvtcna0.getGnaCve());
            display(dclgfvtagc0.getGnaCve());
            display(cpxEsaCveFla);
            display(dclgfvtagc0.getEsaCveFla());
            display(dclgfvtfla0.getFlaGerZon());
            display(dclgfvtagc0.getFlaGerZon());
            display(dclgfvtcna0.getOfnCve());
            display(dclgfvtagc0.getOfnCveCna());
            display(dclgcctorv0.getOrvAgtIdrGte());
            display(dclgfvtagc0.getAgtIdrGte());
        }
        // EAM 15-10-2002                                                   10150002
        if (dclgfvtagt0.getAgtApeMat().isEqual(dclgfvtagc0.getAgtApeMat()) 
            && dclgfvtagt0.getAgtApePat().isEqual(dclgfvtagc0.getAgtApePat()) 
            && dclgfvtagt0.getAgtNom().isEqual(dclgfvtagc0.getAgtNom()) 
            && dclgfvtagt0.getAgtRazSoc().isEqual(dclgfvtagc0.getAgtRazSoc()) 
            && dclgfvtfla0.getEmpCve().isEqual(dclgfvtagc0.getEmpCve()) 
            && dclgfvtmte0.getEsaCve().isEqual(dclgfvtagc0.getEsaCve()) 
            && dclgfvtfla0.getOfnCve().isEqual(dclgfvtagc0.getOfnCve()) 
            && dclgfvtfla0.getOrvCve().isEqual(dclgfvtagc0.getOrvCve()) 
            && dclgfvtagt0.getPefCve().isEqual(dclgfvtagc0.getPefCve()) 
            && dclgfvtfla0.getAgtIdr().isEqual(dclgfvtagc0.getAgtIdr()) 
            && dclgfvtfla0.getCnaNum().isEqual(dclgfvtagc0.getCnaNum()) 
            && dclgfvtcna0.getTfpCve().isEqual(dclgfvtagc0.getTfpCve()) 
            && dclgfvtcna0.getCnaIndGer().isEqual(dclgfvtagc0.getCnaIndGer()) 
            && dclgfvtfla0.getMteCve().isEqual(dclgfvtagc0.getMteCve()) 
            && dclgfvtfla0.getFlaFecMotEss().isEqual(dclgfvtagc0.getFlaFecMotEss()) 
            && dclgfvtcna0.getCnaGerZonEnv().isEqual(dclgfvtagc0.getCnaGerZonEnv()) 
            && dclgfvtdrc0.getDrcIdrAgt().isEqual(dclgfvtagc0.getDrcIdrAgt()) 
            && dclgfvtdrc0.getDrcIdrGte().isEqual(dclgfvtagc0.getDrcIdrGte()) 
            && dclgfvtfla0.getFlaDes().isEqual(dclgfvtagc0.getFlaDes()) 
            && dclgfvtagt0.getAgtTraEsp().isEqual(dclgfvtagc0.getAgtTraEsp()) 
            && dclgfvtcna0.getCnaFecCnx().isEqual(dclgfvtagc0.getCnaFecCnx()) 
            && dclgfvtcna0.getGnaCve().isEqual(dclgfvtagc0.getGnaCve()) 
            && Functions.isEqual(cpxEsaCveFla, dclgfvtagc0.getEsaCveFla()) 
            && dclgfvtfla0.getFlaGerZon().isEqual(dclgfvtagc0.getFlaGerZon()) 
            && dclgfvtcna0.getOfnCve().isEqual(dclgfvtagc0.getOfnCveCna()) 
            && dclgcctorv0.getOrvAgtIdrGte().isEqual(dclgfvtagc0.getAgtIdrGte())) {
            // EAM 15-10-2002                                                   10390002
            // ==>                                                              10410002
            // OJO                                                              10420002
            // ==>VERIFICAR QUE DATO ES DENTRO DE FOLIOS                        10430002
            // ==>VERIFICAR QUE DATO ES DENTRO DE FOLIOS                        10440002
            // ==>                    AND                                       10450002
            // ==>      FLA-NUM-GTE      OF DCLGFVTFLA0   EQUAL                 10460002
            // ==>      FLA-NUM-GTE      OF DCLGFVTAGC0                         10470002
        } else {
            //          DISPLAY 'ORV-AGT-IDR-GTE  OF DCLGCCTORV0'               10500002
            //                   ORV-AGT-IDR-GTE  OF DCLGCCTORV0                10510002
            //          DISPLAY 'AGT-IDR-GTE      OF DCLGFVTAGC0'               10520002
            //                   AGT-IDR-GTE      OF DCLGFVTAGC0                10530002
            display("HOLAR ", dclgfvtfla0.getFlaNum());
            p3400UpdateGfvtagc0();
        }
    }

    /**
     *  Paragraph: 3400-UPDATE-GFVTAGC0.
     *
     * ACTUALIZA CON CAMBIOS
     * OJO===>   SOLO PARA SACAR EL REPORTE
     * OJO===>   SIN ACTUALIZAR LA TABLA GFVTAGC0.
     * 3400-UPDATE-GFVTAGC0.
     * MOVE  'ANTES  '  TO  D1-OBS
     * PERFORM  3550-IMP-GFVTAGC0.
     * PERFORM  3600-ARMA-DCLGFVTAGC0.
     * MOVE  'DESPUES'  TO  D1-OBS
     * PERFORM  3550-IMP-GFVTAGC0.
     * ADD  1  TO  TOT-CAMBIOS.
     * ACTUALIZA CON CAMBIOS
     */
    void p3400UpdateGfvtagc0() {
        //                                                                  10830002
        linea.setD1Obs("ANTES  ");
        p3550ImpGfvtagc0();
        p3600ArmaDclgfvtagc0();
        //                                                                  10870002
        linea.setD1Obs("DESPUES");
        p3550ImpGfvtagc0();
        //                                                                  10900002
        wsDatosAbend.setWsProcesoEjecuta("3350-UPDATE.GFVTAGC0          ");

        wsDatosAbend.setWsInstruccion("UPDATE          ");

        wsDatosAbend.setWsTabla("GFVTAGC0        ");

        //                                                                  10940002
        //                                                                  10950002
        gfvtagc0Port.update(
            Gfvtagc0Model.builder()
                .flaNum(toHostString(dclgfvtagc0.getFlaNum()))
                .agtApeMat(toHostString(dclgfvtagc0.getAgtApeMat()))
                .agtApePat(toHostString(dclgfvtagc0.getAgtApePat()))
                .agtNom(toHostString(dclgfvtagc0.getAgtNom()))
                .agtRazSoc(toHostString(dclgfvtagc0.getAgtRazSoc()))
                .empCve(toHostString(dclgfvtagc0.getEmpCve()))
                .esaCve(dclgfvtagc0.getEsaCve().toInt())
                .ofnCve(dclgfvtagc0.getOfnCve().toInt())
                .orvCve(dclgfvtagc0.getOrvCve().toInt())
                .pefCve(dclgfvtagc0.getPefCve().toInt())
                .usuCveAct(toHostString(dclgfvtagc0.getUsuCveAct()))
                .fecUltAct(toHostLocalDate(dclgfvtagc0.getFecUltAct()))
                .agtIdr(dclgfvtagc0.getAgtIdr().toInt())
                .cnaNum(dclgfvtagc0.getCnaNum().toInt())
                .tfpCve(dclgfvtagc0.getTfpCve().toInt())
                .cnaIndGer(toHostString(dclgfvtagc0.getCnaIndGer()))
                .mteCve(dclgfvtagc0.getMteCve().toInt())
                .flaFecMotEss(toHostLocalDate(dclgfvtagc0.getFlaFecMotEss()))
                .flaNumGte(toHostString(dclgfvtagc0.getFlaNumGte()))
                .cnaGerZonEnv(dclgfvtagc0.getCnaGerZonEnv().toInt())
                .drcIdrAgt(toHostString(dclgfvtagc0.getDrcIdrAgt()))
                .drcIdrGte(toHostString(dclgfvtagc0.getDrcIdrGte()))
                .agtTraEsp(toHostString(dclgfvtagc0.getAgtTraEsp()))
                .cnaFecCnx(toHostLocalDate(dclgfvtagc0.getCnaFecCnx()))
                .esaCveFla(dclgfvtagc0.getEsaCveFla().toInt())
                .flaDes(toHostString(dclgfvtagc0.getFlaDes()))
                .flaGerZon(dclgfvtagc0.getFlaGerZon().toInt())
                .gnaCve(dclgfvtagc0.getGnaCve().toInt())
                .ofnCveCna(dclgfvtagc0.getOfnCveCna().toInt())
                .agtIdrGte(dclgfvtagc0.getAgtIdrGte().toInt())
                .flaNum(toHostString(dclgfvtfla0.getFlaNum()))
            .build());
        //                                                                  11310002
        db2check();
        //                                                                  11330002
        if (this.isDb2Ok()) {
            wsTotales.setTotCambios(wsTotales.getTotCambios() + 1);
            if (Functions.isZero(mod(wsTotales.getTotCambios(), 100))) {
                display("UPDATES: ", wsTotales.getTotCambios());
            }
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinAgc(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 3400-ALTA-GFVTAGC0.
     *
     * EFECTUA ALTA A  GFVTAGC0
     */
    void p3400AltaGfvtagc0() {
        //                                                                  11530002
        wsIndicadores.setWsFinAgt(0);

        p7100LeerGfvtagt0();
        if (wsIndicadores.getWsFinAgt() == 0) {
            // ==>                                                              11570002
            wsIndicadores.setWsFinCna(0);

            p7100LeerGfvtcna0();
            if (wsIndicadores.getWsFinCna() == 0) {
                wsIndicadores.setWsFinMte(0);

                p7100LeerGfvtmte0();
                if (wsIndicadores.getWsFinMte() == 0) {
                    p3500InsertaGfvtagc0();
                }
            } else {
                display("CONTRATO  NO EXISTE CON FOLIO       ==> ", dclgfvtfla0.getAgtIdr(), " ", dclgfvtfla0.getCnaNum(), " ", dclgfvtfla0.getFlaNum());
            }
        } else {
            display("AGENTE    NO EXISTE CON FOLIO       ==> ", dclgfvtfla0.getAgtIdr(), " ", dclgfvtfla0.getFlaNum());
        }
    }

    /**
     *  Paragraph: 3500-INSERTA-GFVTAGC0.
     *
     * GENERA CON FOLIOS, AGENTES Y CONTRATOS
     * CARTERA DE AGENTES.
     * ==>OJO    SOLO EFECTUA LA IMPRESION DEL REGISTRO A DAR DE ALTA,
     * ==>OJO    SIN ACTUALIZAR GFVTAGC0.
     * 3500-INSERTA-GFVTAGC0.
     * PERFORM  3600-ARMA-DCLGFVTAGC0.
     * MOVE  'ALTA   '  TO  D1-OBS
     * PERFORM  3550-IMP-GFVTAGC0.
     * ADD  1  TO  TOT-GRABADOS.
     * GENERA CON FOLIOS, AGENTES Y CONTRATOS
     * CARTERA DE AGENTES.
     */
    void p3500InsertaGfvtagc0() {
        //                                                                  12000002
        p3600ArmaDclgfvtagc0();
        linea.setD1Obs("ALTA   ");
        p3550ImpGfvtagc0();
        //                                                                  12040002
        wsDatosAbend.setWsProcesoEjecuta("3500-INSERTA-GFVTAGC0         ");

        wsDatosAbend.setWsInstruccion("INSERT          ");

        wsDatosAbend.setWsTabla("GFVTAGC0        ");

        //                                                                  12080002
        //                                                                  12090002
        gfvtagc0Port.insert(
            GenericModel.builder().values(List.of(
                toHostString(dclgfvtagc0.getFlaNum()),
                toHostString(dclgfvtagc0.getAgtApeMat()),
                toHostString(dclgfvtagc0.getAgtApePat()),
                toHostString(dclgfvtagc0.getAgtNom()),
                toHostString(dclgfvtagc0.getAgtRazSoc()),
                toHostString(dclgfvtagc0.getEmpCve()),
                dclgfvtagc0.getEsaCve().toInt(),
                dclgfvtagc0.getOfnCve().toInt(),
                dclgfvtagc0.getOrvCve().toInt(),
                dclgfvtagc0.getPefCve().toInt(),
                toHostString(dclgfvtagc0.getUsuCveAct()),
                toHostString(dclgfvtagc0.getFecUltAct()),
                dclgfvtagc0.getAgtIdr().toInt(),
                dclgfvtagc0.getCnaNum().toInt(),
                dclgfvtagc0.getTfpCve().toInt(),
                toHostString(dclgfvtagc0.getCnaIndGer()),
                dclgfvtagc0.getMteCve().toInt(),
                toHostString(dclgfvtagc0.getFlaFecMotEss()),
                toHostString(dclgfvtagc0.getFlaNumGte()),
                dclgfvtagc0.getCnaGerZonEnv().toInt(),
                toHostString(dclgfvtagc0.getDrcIdrAgt()),
                toHostString(dclgfvtagc0.getDrcIdrGte()),
                toHostString(dclgfvtagc0.getAgtTraEsp()),
                toHostString(dclgfvtagc0.getCnaFecCnx()),
                dclgfvtagc0.getEsaCveFla().toInt(),
                toHostString(dclgfvtagc0.getFlaDes()),
                dclgfvtagc0.getFlaGerZon().toInt(),
                dclgfvtagc0.getGnaCve().toInt(),
                dclgfvtagc0.getOfnCveCna().toInt(),
                dclgfvtagc0.getAgtIdrGte().toInt()))
            .build());
        //                                                                  12740002
        //                                                                  12750002
        db2check();
        //                                                                  12770002
        if (this.isDb2Ok()) {
            wsTotales.setTotGrabados(wsTotales.getTotGrabados() + 1);
            if (Functions.isZero(mod(wsTotales.getTotGrabados(), 100))) {
                display("INSERTS: ", wsTotales.getTotGrabados());
            }
        } else if (this.isDb2Duprec()) {
            wsTotales.setTotDuplicados(wsTotales.getTotDuplicados() + 1);
            linea.setD1Obs("AGC.    DUPLICADO");
            p3550ImpGfvtagc0();
        } else {
            linea.setD1Obs("AGC.    ERROR    ");
            p3550ImpGfvtagc0();
            p9999AnalizaSql();
        }
        //                                                                  12920002
    }

    /**
     *  Paragraph: 3550-IMP-GFVTAGC0.
     *
     * RUTINA DE IMPRESION DE LA CARGA DE  CARTERA-AGENTES
     */
    void p3550ImpGfvtagc0() {
        linea.setD1FlaNum(dclgfvtagc0.getFlaNum());
        linea.setD1EmpCve(dclgfvtagc0.getEmpCve());
        linea.getD1EsaCve().setValue(dclgfvtagc0.getEsaCve());
        linea.getD1OfnCve().setValue(dclgfvtagc0.getOfnCve());
        linea.getD1OrvCve().setValue(dclgfvtagc0.getOrvCve());
        linea.getD1PefCve().setValue(dclgfvtagc0.getPefCve());
        linea.setD1AgtNom(dclgfvtagc0.getAgtNom()); // [@WARNING overflow d1AgtNom(10) <- agtNom(40)]
        linea.setD1AgtApePat(dclgfvtagc0.getAgtApePat()); // [@WARNING overflow d1AgtApePat(10) <- agtApePat(40)]
        linea.setD1AgtApeMat(dclgfvtagc0.getAgtApeMat()); // [@WARNING overflow d1AgtApeMat(10) <- agtApeMat(30)]
        linea.setD1AgtRazSoc(dclgfvtagc0.getAgtRazSoc()); // [@WARNING overflow d1AgtRazSoc(8) <- agtRazSoc(60)]
        p6000Imprimir();
        //                                                                  13140002
        linea.getD2AgtIdr().setValue(dclgfvtagc0.getAgtIdr());
        linea.getD2CnaNum().setValue(dclgfvtagc0.getCnaNum());
        linea.getD2TfpCve().setValue(dclgfvtagc0.getTfpCve());
        linea.setD2CnaIndGer(dclgfvtagc0.getCnaIndGer());
        linea.getD2MteCve().setValue(dclgfvtagc0.getMteCve());
        linea.setD2FlaFecMotEss(dclgfvtagc0.getFlaFecMotEss());
        linea.setD2FlaNumGte(dclgfvtagc0.getFlaNumGte());
        linea.setD2CnaGerZonEnv(dclgfvtagc0.getCnaGerZonEnv());
        linea.setD2UsuCveAct(dclgfvtagc0.getUsuCveAct());
        linea.setD2FecUltAct(dclgfvtagc0.getFecUltAct());
        p6000Imprimir();
        //                                                                  13270002
        linea.setD3FlaDes(dclgfvtagc0.getFlaDes());
        linea.setD3AgtTraEsp(dclgfvtagc0.getAgtTraEsp());
        linea.setD3CnaFecCnx(dclgfvtagc0.getCnaFecCnx());
        linea.getD3GnaCve().setValue(dclgfvtagc0.getGnaCve());
        linea.getD3EsaCveFla().setValue(dclgfvtagc0.getEsaCveFla());
        linea.getD3FlaGerZon().setValue(dclgfvtagc0.getFlaGerZon());
        linea.getD3OfnCveCna().setValue(dclgfvtagc0.getOfnCveCna());
        linea.setD3AgtIdrGte(dclgfvtagc0.getAgtIdrGte());
        for (int idx = 1; idx <= 2; idx++) {
            p6000Imprimir();
        }
    }

    /**
     *  Paragraph: 3600-ARMA-DCLGFVTAGC0.
     *
     * ARMA CAMPOS PARA LA TABLA GFVTAGC0  CARTERA-AGENTES
     */
    void p3600ArmaDclgfvtagc0() {
        //                                                                  13450002
        dclgfvtagc0.initialize();
        wsGfvtagc0Null.initialize();
        //  EAM 15-10-2002                                                  13480002
        dclgfvtagc0.setAgtTraEsp(dclgfvtagt0.getAgtTraEsp());
        //                                                                  13510002
        dclgfvtagc0.setCnaFecCnx(dclgfvtcna0.getCnaFecCnx());
        //                                                                  13540002
        dclgfvtagc0.setEsaCveFla(cpxEsaCveFla);
        //                                                                  13570002
        dclgfvtagc0.setFlaDes(dclgfvtfla0.getFlaDes());
        //                                                                  13600002
        dclgfvtagc0.setFlaGerZon(dclgfvtfla0.getFlaGerZon());
        wsGfvtagc0Null.setAgc25(wsGfvtfla0Null.getFla11().toInt());

        //                                                                  13640002
        dclgfvtagc0.setGnaCve(dclgfvtcna0.getGnaCve());
        //                                                                  13670002
        dclgfvtagc0.setOfnCveCna(dclgfvtcna0.getOfnCve());
        //  EAM 15-10-2002                                                  13700002
        dclgfvtagc0.setFlaNum(dclgfvtfla0.getFlaNum());
        //                                                                  13730002
        dclgfvtagc0.setAgtApeMat(dclgfvtagt0.getAgtApeMat());
        wsGfvtagc0Null.setAgc02(wsGfvtagt0Null.getAgt05());

        //                                                                  13770002
        dclgfvtagc0.setAgtApePat(dclgfvtagt0.getAgtApePat());
        wsGfvtagc0Null.setAgc03(wsGfvtagt0Null.getAgt04());

        //                                                                  13810002
        dclgfvtagc0.setAgtNom(dclgfvtagt0.getAgtNom());
        wsGfvtagc0Null.setAgc04(wsGfvtagt0Null.getAgt03());

        //                                                                  13850002
        dclgfvtagc0.setAgtRazSoc(dclgfvtagt0.getAgtRazSoc());
        wsGfvtagc0Null.setAgc05(wsGfvtagt0Null.getAgt14());

        //                                                                  13890002
        dclgfvtagc0.setEmpCve(dclgfvtfla0.getEmpCve());
        //                                                                  13920002
        dclgfvtagc0.setEsaCve(dclgfvtmte0.getEsaCve());
        //                                                                  13950002
        dclgfvtagc0.setOfnCve(dclgfvtfla0.getOfnCve());
        //                                                                  13980002
        dclgfvtagc0.setOrvCve(dclgfvtfla0.getOrvCve());
        //                                                                  14010002
        dclgfvtagc0.setPefCve(dclgfvtagt0.getPefCve());
        //                                                                  14040002
        dclgfvtagc0.setUsuCveAct("TD6NFS");
        //                                                                  14070002
        dclgfvtagc0.setFecUltAct(wsTimestampr.getWsFechaTimestamp());
        //                                                                  14100002
        dclgfvtagc0.setAgtIdr(dclgfvtfla0.getAgtIdr());
        //                                                                  14130002
        dclgfvtagc0.setCnaNum(dclgfvtfla0.getCnaNum());
        //                                                                  14160002
        dclgfvtagc0.setTfpCve(dclgfvtcna0.getTfpCve());
        //                                                                  14190002
        // EAM 15-10-2002                                                   14200002
        if (dclgfvtfla0.getFlaGerZon().isEqual(dclgfvtcna0.getCnaGerZonEnv())) {
            dclgfvtagc0.setCnaIndGer(dclgfvtcna0.getCnaIndGer());
        } else {
            dclgfvtagc0.setCnaIndGer("N");
        }
        // EAM 15-10-2002                                                   14280002
        //                                                                  14290002
        dclgfvtagc0.setMteCve(dclgfvtfla0.getMteCve());
        //                                                                  14320002
        dclgfvtagc0.setFlaFecMotEss(dclgfvtfla0.getFlaFecMotEss());
        //                                                                  14350002
        dclgfvtagc0.setCnaGerZonEnv(dclgfvtcna0.getCnaGerZonEnv());
        //                                                                  14380002
        dclgfvtagc0.setDrcIdrAgt(dclgfvtdrc0.getDrcIdrAgt());
        //                                                                  14410002
        dclgfvtagc0.setDrcIdrGte(dclgfvtdrc0.getDrcIdrGte());
        //                                                                  14440002
        wsIndicadores.setWsGerZon(dclgfvtfla0.getFlaGerZon().toInt());

        //                                                                  14470002
        wsRegistros.setWsDclgfvtfla00(dclgfvtfla0.toStringValue());

        wsGfvtfla0NullAux.setValue(wsGfvtfla0Null);
        p3700FolioAgente();
        dclgfvtagc0.setAgtIdrGte(dclgcctorv0.getOrvAgtIdrGte());
        dclgfvtfla0.setValue(wsRegistros.getWsDclgfvtfla00());
        wsGfvtfla0Null.setValue(wsGfvtfla0NullAux);
    }

    /**
     *  Paragraph: 3700-FOLIO-AGENTE.
     *
     */
    void p3700FolioAgente() {
        //                                                                  14610002
        wsIndicadores.setWsFinOrv(0);

        p7100LeerGcctorv0();
        if ((wsIndicadores.getWsFinOrv() == 0) 
            && (dclgcctorv0.getOrvAgtIdrGte().toInt() != 99999)) {
            // ==>                                                              14670002
            wsIndicadores.setWsFinCna1(0);

            p7960InicioGfvtcna1();
            if (wsIndicadores.getWsFinCna1() == 0) {
                //                                                                  14710002
                wsRegistros.setWsIndNal(0);

                wsRegistros.setWsIndProv(0);

                wsIndicadores.getWsFolioxx().setValue(dclgfvtfla0.getFlaNum());
                wsIndicadores.getWsFolioxxn().setSpaces();
                wsIndicadores.getWsFolioxxp().setSpaces();
                wsIndicadores.setWsFinFla2(0);

                p7970InicioGfvtfla2();
                while (!(wsIndicadores.getWsFinFla2() == 1)) {
                    p3400AccesaGfvtfla2();
                }
                p7970CerrarGfvtfla2();
                if (wsIndicadores.getWsFolioxx1().isEqual("N")) {
                    if (wsIndicadores.getWsFolioxx1n().isEqual("N")) {
                        dclgfvtagc0.setFlaNumGte(wsIndicadores.getWsFolioxxn());
                    } else if (wsIndicadores.getWsFolioxx1p().isEqual("P")) {
                        dclgfvtagc0.setFlaNumGte(wsIndicadores.getWsFolioxxp());
                    } else {
                        //                          DISPLAY 'HOLA 1' FLA-NUM OF DCLGFVTFLA0 14920002
                        dclgfvtagc0.getFlaNumGte().setValue(99999);
                    }
                } else if (wsIndicadores.getWsFolioxx1p().isEqual("P")) {
                    dclgfvtagc0.setFlaNumGte(wsIndicadores.getWsFolioxxp());
                } else if (wsIndicadores.getWsFolioxx1n().isEqual("N")) {
                    dclgfvtagc0.setFlaNumGte(wsIndicadores.getWsFolioxxn());
                } else {
                    //                          DISPLAY 'HOLA 2' FLA-NUM OF DCLGFVTFLA0 15060002
                    dclgfvtagc0.getFlaNumGte().setValue(99999);
                }
            } else {
                display("=====>CUIDADO,CUIDADO<===== ");
                display("=====>SIN CNA 7,8    <===== ");
                display("ORV_AGT_IDR_GTE  GCCTORV0 => ", dclgcctorv0.getOrvAgtIdrGte());
                display("ORV_CVE          GCCTORV0 => ", dclgcctorv0.getOrvCve());
                display("AGT_IDR          GFVTAGT0 => ", dclgfvtagt0.getAgtIdr());
                //                          DISPLAY 'HOLA 3' FLA-NUM OF DCLGFVTFLA0 15210002
                dclgfvtagc0.getFlaNumGte().setValue(99999);
            }
            p7960CerrarGfvtcna1();
        } else {
            //                          DISPLAY 'HOLA 4' FLA-NUM OF DCLGFVTFLA0 15260002
            dclgfvtagc0.getFlaNumGte().setValue(99999);
            dclgfvtagc0.setAgtIdrGte(99999);
            wsGfvtagc0Null.setAgc28(0);

        }
    }

    /**
     *  Paragraph: 3400-ACCESA-GFVTFLA2.
     *
     * MOVE  ZEROS  TO  AGT-IDR-GTE OF DCLGFVTAGC0
     * MOVE  -1     TO  AGC28.
     * OBTIENE LOS FOLIOS (NAL, PROV).
     */
    void p3400AccesaGfvtfla2() {
        //                                                                  15309002
        wsIndicadores.getWsFoliozz().setValue(dclgfvtfla0.getFlaNum());
        //                                                                  15320002
        if (wsIndicadores.getWsFoliozz1().isEqual("N")) {
            if (wsRegistros.getWsIndNal() == 0) {
                wsIndicadores.getWsFolioxxn().setValue(dclgfvtfla0.getFlaNum());
                wsRegistros.setWsIndNal(1);

            } else {
            }
        } else if (wsRegistros.getWsIndProv() == 0) {
            wsIndicadores.getWsFolioxxp().setValue(dclgfvtfla0.getFlaNum());
            wsRegistros.setWsIndProv(1);

        }
        //                                                                  15430002
        p7970LeerGfvtfla2();
    }

    /**
     *  Paragraph: 6000-IMPRIMIR.
     *
     * 6000  RUTINAS DE IMPRESION DEL REPORTE DE CONTRATOS.           *
     */
    void p6000Imprimir() {
        listado.skipLines(1);
        listado.write(linea);
        if (wsIndicadores.getContLineas() == 60) {
            wsIndicadores.setContLineas(0);

            p6100Enc1();
        }
        //                                                                  15550002
        wsIndicadores.setContLineas(wsIndicadores.getContLineas() + 1);
        linea.setSpaces();
    }

    /**
     *  Paragraph: 6100-ENC1.
     *
     */
    void p6100Enc1() {
        wsIndicadores.setContHojas(wsIndicadores.getContHojas() + 1);
        tit11.getTit11Hoja().setValue(wsIndicadores.getContHojas());
        linea.setLinResto(tit11);
        listado.skipPage();
        listado.write(linea);
        linea.setLinResto(tit21);
        p6000Imprimir();
        linea.setLinResto(tit31);
        p6000Imprimir();
        linea.setLinResto(tit41);
        p6000Imprimir();
        //                                                                  15720002
        linea.getLinResto().moveAll("-");
        p6000Imprimir();
    }

    /**
     *  Paragraph: 7100-LEER-GFVTMTE0.
     *
     * LEER LA TABLA  GFVTMTE0  MOT-STATUS
     */
    void p7100LeerGfvtmte0() {
        //                                                                  15850002
        wsDatosAbend.setWsProcesoEjecuta("7100-LEER-GFVTMTE0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTMTE0        ");

        //                                                                  15890002
        gfvtmte0Model = 
            gfvtmte0Port.findByMteCve(
                                    dclgfvtagt0.getMteCve().toInt());

        if(gfvtmte0Model.isPresent()) {
            dclgfvtmte0.setMteCve(fromHostInt(gfvtmte0Model.get().mteCve()));
            dclgfvtmte0.setMteDes(fromHostString(gfvtmte0Model.get().mteDes()));
            dclgfvtmte0.setFecUltAct(fromHostString(gfvtmte0Model.get().fecUltAct()));
            dclgfvtmte0.setUsuCveAct(fromHostString(gfvtmte0Model.get().usuCveAct()));
            dclgfvtmte0.setEsaCve(fromHostInt(gfvtmte0Model.get().esaCve()));
        }
        //                                                                  16100002
        db2check();
        //                                                                  16120002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinMte(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinMte(1);

        } else {
            p9999AnalizaSql();
        }
        //  EAM 15-10-2002                                                  16200002
        gfvtmte0Model = 
            gfvtmte0Port.select_1(
                Gfvtmte0Model.builder()
                    .mteCve(dclgfvtfla0.getMteCve().toInt())
                .build());

        if(gfvtmte0Model.isPresent()) {
            cpxEsaCveFla = fromHostInt(gfvtmte0Model.get().esaCve());
        }
        db2check();
        if (!this.isDb2Ok()) {
            p9999AnalizaSql();
        }
        //  EAM 15-10-2002                                                  16330002
    }

    /**
     *  Paragraph: 7100-LEER-GCCTORV0.
     *
     * LEER LA TABLA  GFVTORV0  ORGANIZACION VENTAS
     */
    void p7100LeerGcctorv0() {
        //     DISPLAY 'ENTRA 7100-LEER-GCCTORV0 '                          16440002
        //     DISPLAY 'WS-GER-ZON  : '  WS-GER-ZON                         16450002
        wsDatosAbend.setWsProcesoEjecuta("7100-LEER-GCCTORV0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GCCTORV0        ");

        //                                                                  16500002
        gcctorv0Model = 
            gcctorv0Port.select(
                Gcctorv0Model.builder()
                    .orvCve(wsIndicadores.getWsGerZon())
                .build());

        if(gcctorv0Model.isPresent()) {
            dclgcctorv0.setOrvCve(fromHostInt(gcctorv0Model.get().orvCve()));
            dclgcctorv0.setOrvNom(fromHostString(gcctorv0Model.get().orvNom()));
            dclgcctorv0.setOrvNomRes(fromHostString(gcctorv0Model.get().orvNomRes()));
            wsGcctorv0Null.setOrv03(isNull(gcctorv0Model.get().orvNomRes()) ? -1 : 0);
            dclgcctorv0.setFecUltAct(fromHostString(gcctorv0Model.get().fecUltAct()));
            dclgcctorv0.setUsuCveAct(fromHostString(gcctorv0Model.get().usuCveAct()));
            dclgcctorv0.setOrvCveNivSup(fromHostInt(gcctorv0Model.get().orvCveNivSup()));
            wsGcctorv0Null.setOrv06(isNull(gcctorv0Model.get().orvCveNivSup()) ? -1 : 0);
            dclgcctorv0.setEmpCve(fromHostString(gcctorv0Model.get().empCve()));
            dclgcctorv0.setNovCve(fromHostInt(gcctorv0Model.get().novCve()));
            dclgcctorv0.setOrvAgtIdrGte(fromHostInt(gcctorv0Model.get().orvAgtIdrGte()));
            wsGcctorv0Null.setOrv09(isNull(gcctorv0Model.get().orvAgtIdrGte()) ? -1 : 0);
        }
        //                                                                  16810002
        db2check();
        //                                                                  16830002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinOrv(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinOrv(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7100-LEER-GFVTAGT0.
     *
     * LEER LA TABLA  GFVTAGT0  AGENTES (NOMBRE)
     */
    void p7100LeerGfvtagt0() {
        //                                                                  17010002
        wsDatosAbend.setWsProcesoEjecuta("7100-LEER-GFVTAGT0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTAGT0        ");

        //                                                                  17050002
        gfvtagt0Model = 
            gfvtagt0Port.findByAgtIdr(
                                    dclgfvtfla0.getAgtIdr().toInt());

        if(gfvtagt0Model.isPresent()) {
            dclgfvtagt0.setAgtIdr(fromHostInt(gfvtagt0Model.get().agtIdr()));
            dclgfvtagt0.setAgtRfc(fromHostString(gfvtagt0Model.get().agtRfc()));
            wsGfvtagt0Null.setAgt02(isNull(gfvtagt0Model.get().agtRfc()) ? -1 : 0);
            dclgfvtagt0.setAgtNom(fromHostString(gfvtagt0Model.get().agtNom()));
            wsGfvtagt0Null.setAgt03(isNull(gfvtagt0Model.get().agtNom()) ? -1 : 0);
            dclgfvtagt0.setAgtApePat(fromHostString(gfvtagt0Model.get().agtApePat()));
            wsGfvtagt0Null.setAgt04(isNull(gfvtagt0Model.get().agtApePat()) ? -1 : 0);
            dclgfvtagt0.setAgtApeMat(fromHostString(gfvtagt0Model.get().agtApeMat()));
            wsGfvtagt0Null.setAgt05(isNull(gfvtagt0Model.get().agtApeMat()) ? -1 : 0);
            dclgfvtagt0.setAgtFecNac(fromHostString(gfvtagt0Model.get().agtFecNac()));
            wsGfvtagt0Null.setAgt06(isNull(gfvtagt0Model.get().agtFecNac()) ? -1 : 0);
            dclgfvtagt0.setAgtNumCed(fromHostString(gfvtagt0Model.get().agtNumCed()));
            wsGfvtagt0Null.setAgt07(isNull(gfvtagt0Model.get().agtNumCed()) ? -1 : 0);
            dclgfvtagt0.setAgtFecIniCed(fromHostString(gfvtagt0Model.get().agtFecIniCed()));
            wsGfvtagt0Null.setAgt08(isNull(gfvtagt0Model.get().agtFecIniCed()) ? -1 : 0);
            dclgfvtagt0.setAgtFecFinCed(fromHostString(gfvtagt0Model.get().agtFecFinCed()));
            wsGfvtagt0Null.setAgt09(isNull(gfvtagt0Model.get().agtFecFinCed()) ? -1 : 0);
            dclgfvtagt0.setAgtExpSeg(fromHostString(gfvtagt0Model.get().agtExpSeg()));
            dclgfvtagt0.setAgtTraEsp(fromHostString(gfvtagt0Model.get().agtTraEsp()));
            dclgfvtagt0.setAgtClfLmr(fromHostInt(gfvtagt0Model.get().agtClfLmr()));
            wsGfvtagt0Null.setAgt12(isNull(gfvtagt0Model.get().agtClfLmr()) ? -1 : 0);
            dclgfvtagt0.setFecUltAct(fromHostString(gfvtagt0Model.get().fecUltAct()));
            dclgfvtagt0.setAgtRazSoc(fromHostString(gfvtagt0Model.get().agtRazSoc()));
            wsGfvtagt0Null.setAgt14(isNull(gfvtagt0Model.get().agtRazSoc()) ? -1 : 0);
            dclgfvtagt0.setUsuCveAct(fromHostString(gfvtagt0Model.get().usuCveAct()));
            dclgfvtagt0.setNalCve(fromHostString(gfvtagt0Model.get().nalCve()));
            dclgfvtagt0.setPefCve(fromHostInt(gfvtagt0Model.get().pefCve()));
            dclgfvtagt0.setTcaCve(fromHostString(gfvtagt0Model.get().tcaCve()));
            dclgfvtagt0.setMteCve(fromHostInt(gfvtagt0Model.get().mteCve()));
            dclgfvtagt0.setAgtNumFolInv(fromHostInt(gfvtagt0Model.get().agtNumFolInv()));
            wsGfvtagt0Null.setAgt20(isNull(gfvtagt0Model.get().agtNumFolInv()) ? -1 : 0);
            dclgfvtagt0.setAgtNomExi(fromHostString(gfvtagt0Model.get().agtNomExi()));
            wsGfvtagt0Null.setAgt21(isNull(gfvtagt0Model.get().agtNomExi()) ? -1 : 0);
            dclgfvtagt0.setAgtRstInv(fromHostString(gfvtagt0Model.get().agtRstInv()));
            wsGfvtagt0Null.setAgt22(isNull(gfvtagt0Model.get().agtRstInv()) ? -1 : 0);
            dclgfvtagt0.setAgtFecIniInv(fromHostString(gfvtagt0Model.get().agtFecIniInv()));
            wsGfvtagt0Null.setAgt23(isNull(gfvtagt0Model.get().agtFecIniInv()) ? -1 : 0);
            dclgfvtagt0.setAgtFecFinInv(fromHostString(gfvtagt0Model.get().agtFecFinInv()));
            wsGfvtagt0Null.setAgt24(isNull(gfvtagt0Model.get().agtFecFinInv()) ? -1 : 0);
            dclgfvtagt0.setAgtNomDphInv(fromHostString(gfvtagt0Model.get().agtNomDphInv()));
            wsGfvtagt0Null.setAgt25(isNull(gfvtagt0Model.get().agtNomDphInv()) ? -1 : 0);
            dclgfvtagt0.setAgtFecReh(fromHostString(gfvtagt0Model.get().agtFecReh()));
            wsGfvtagt0Null.setAgt26(isNull(gfvtagt0Model.get().agtFecReh()) ? -1 : 0);
            dclgfvtagt0.setAgtFecMotEss(fromHostString(gfvtagt0Model.get().agtFecMotEss()));
            wsGfvtagt0Null.setAgt27(isNull(gfvtagt0Model.get().agtFecMotEss()) ? -1 : 0);
            dclgfvtagt0.setAgtFecMotAsgIn(fromHostString(gfvtagt0Model.get().agtFecMotAsgIn()));
            wsGfvtagt0Null.setAgt28(isNull(gfvtagt0Model.get().agtFecMotAsgIn()) ? -1 : 0);
            dclgfvtagt0.setAgtObs(fromHostString(gfvtagt0Model.get().agtObs()));
            wsGfvtagt0Null.setAgt29(isNull(gfvtagt0Model.get().agtObs()) ? -1 : 0);
            dclgfvtagt0.setAgtNip(fromHostInt(gfvtagt0Model.get().agtNip()));
            wsGfvtagt0Null.setAgt30(isNull(gfvtagt0Model.get().agtNip()) ? -1 : 0);
            dclgfvtagt0.setAgtNumFacIso(fromHostInt(gfvtagt0Model.get().agtNumFacIso()));
            wsGfvtagt0Null.setAgt31(isNull(gfvtagt0Model.get().agtNumFacIso()) ? -1 : 0);
            dclgfvtagt0.setAgtImpFacIso(fromHostBigDecimal(gfvtagt0Model.get().agtImpFacIso()));
            wsGfvtagt0Null.setAgt32(isNull(gfvtagt0Model.get().agtImpFacIso()) ? -1 : 0);
        }
        //                                                                  17810002
        db2check();
        //                                                                  17830002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinAgt(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinAgt(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7100-LEER-GFVTCNA0.
     *
     * LEER LA TABLA  GFVTCNA0  CONTRATOS
     */
    void p7100LeerGfvtcna0() {
        //                                                                  18000002
        wsDatosAbend.setWsProcesoEjecuta("7100-LEER-GFVTCNA0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  18040002
        gfvtcna0Model = 
            gfvtcna0Port.select_2(
                Gfvtcna0Model.builder()
                    .agtIdr(dclgfvtfla0.getAgtIdr().toInt())
                    .cnaNum(dclgfvtfla0.getCnaNum().toInt())
                .build());

        if(gfvtcna0Model.isPresent()) {
            dclgfvtcna0.setAgtIdr(fromHostInt(gfvtcna0Model.get().agtIdr()));
            dclgfvtcna0.setEmpCve(fromHostString(gfvtcna0Model.get().empCve()));
            dclgfvtcna0.setCnaNum(fromHostInt(gfvtcna0Model.get().cnaNum()));
            dclgfvtcna0.setCnaTpoCnr(fromHostString(gfvtcna0Model.get().cnaTpoCnr()));
            dclgfvtcna0.setCnaUltCia(fromHostString(gfvtcna0Model.get().cnaUltCia()));
            wsGfvtcna0Null.setCna05(isNull(gfvtcna0Model.get().cnaUltCia()) ? -1 : 0);
            dclgfvtcna0.setCnaFecCnx(fromHostString(gfvtcna0Model.get().cnaFecCnx()));
            dclgfvtcna0.setCnaCodIpu(fromHostString(gfvtcna0Model.get().cnaCodIpu()));
            dclgfvtcna0.setCnaPjeIva(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIva()));
            dclgfvtcna0.setCnaPjeIsr(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIsr()));
            dclgfvtcna0.setCnaComNiv(fromHostString(gfvtcna0Model.get().cnaComNiv()));
            wsGfvtcna0Null.setCna10(isNull(gfvtcna0Model.get().cnaComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaImpComNiv(fromHostBigDecimal(gfvtcna0Model.get().cnaImpComNiv()));
            wsGfvtcna0Null.setCna11(isNull(gfvtcna0Model.get().cnaImpComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaNumFza(fromHostString(gfvtcna0Model.get().cnaNumFza()));
            wsGfvtcna0Null.setCna12(isNull(gfvtcna0Model.get().cnaNumFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecIniFza(fromHostString(gfvtcna0Model.get().cnaFecIniFza()));
            wsGfvtcna0Null.setCna13(isNull(gfvtcna0Model.get().cnaFecIniFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecFinFza(fromHostString(gfvtcna0Model.get().cnaFecFinFza()));
            wsGfvtcna0Null.setCna14(isNull(gfvtcna0Model.get().cnaFecFinFza()) ? -1 : 0);
            dclgfvtcna0.setCnaImpFza(fromHostBigDecimal(gfvtcna0Model.get().cnaImpFza()));
            wsGfvtcna0Null.setCna15(isNull(gfvtcna0Model.get().cnaImpFza()) ? -1 : 0);
            dclgfvtcna0.setCnaIdrFav(fromHostString(gfvtcna0Model.get().cnaIdrFav()));
            dclgfvtcna0.setCnaPjeFav(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeFav()));
            wsGfvtcna0Null.setCna17(isNull(gfvtcna0Model.get().cnaPjeFav()) ? -1 : 0);
            dclgfvtcna0.setPpgCve(fromHostInt(gfvtcna0Model.get().ppgCve()));
            dclgfvtcna0.setCpgCve(fromHostInt(gfvtcna0Model.get().cpgCve()));
            dclgfvtcna0.setCdeCve(fromHostInt(gfvtcna0Model.get().cdeCve()));
            dclgfvtcna0.setOfnCve(fromHostInt(gfvtcna0Model.get().ofnCve()));
            dclgfvtcna0.setTaaCve(fromHostString(gfvtcna0Model.get().taaCve()));
            dclgfvtcna0.setGnaCveAgt(fromHostString(gfvtcna0Model.get().gnaCveAgt()));
            dclgfvtcna0.setGnaCve(fromHostInt(gfvtcna0Model.get().gnaCve()));
            dclgfvtcna0.setTfpCve(fromHostInt(gfvtcna0Model.get().tfpCve()));
            dclgfvtcna0.setAgtIdrRcl(fromHostInt(gfvtcna0Model.get().agtIdrRcl()));
            dclgfvtcna0.setCtbNumCta(fromHostString(gfvtcna0Model.get().ctbNumCta()));
            wsGfvtcna0Null.setCna27(isNull(gfvtcna0Model.get().ctbNumCta()) ? -1 : 0);
            dclgfvtcna0.setMteCve(fromHostInt(gfvtcna0Model.get().mteCve()));
            dclgfvtcna0.setMdeCve(fromHostInt(gfvtcna0Model.get().mdeCve()));
            dclgfvtcna0.setZecCve(fromHostInt(gfvtcna0Model.get().zecCve()));
            dclgfvtcna0.setCnaCodPag(fromHostString(gfvtcna0Model.get().cnaCodPag()));
            wsGfvtcna0Null.setCna31(isNull(gfvtcna0Model.get().cnaCodPag()) ? -1 : 0);
            dclgfvtcna0.setCnaFecMotEss(fromHostString(gfvtcna0Model.get().cnaFecMotEss()));
            wsGfvtcna0Null.setCna32(isNull(gfvtcna0Model.get().cnaFecMotEss()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonEnv(fromHostInt(gfvtcna0Model.get().cnaGerZonEnv()));
            wsGfvtcna0Null.setCna33(isNull(gfvtcna0Model.get().cnaGerZonEnv()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonDcm(fromHostInt(gfvtcna0Model.get().cnaGerZonDcm()));
            wsGfvtcna0Null.setCna34(isNull(gfvtcna0Model.get().cnaGerZonDcm()) ? -1 : 0);
            dclgfvtcna0.setCnaIndGer(fromHostString(gfvtcna0Model.get().cnaIndGer()));
            wsGfvtcna0Null.setCna35(isNull(gfvtcna0Model.get().cnaIndGer()) ? -1 : 0);
            dclgfvtcna0.setCnaFecAsgGer(fromHostString(gfvtcna0Model.get().cnaFecAsgGer()));
            wsGfvtcna0Null.setCna36(isNull(gfvtcna0Model.get().cnaFecAsgGer()) ? -1 : 0);
        }
        //                                                                  18890002
        db2check();
        //                                                                  18910002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCna(0);

            p7100LeerGfvtdrc0();
        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinCna(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7100-LEER-GFVTDRC0.
     *
     * LEER LA TABLA  GFVTDRC0  DERECHOS
     */
    void p7100LeerGfvtdrc0() {
        //                                                                  19080002
        wsDatosAbend.setWsProcesoEjecuta("7100-LEER-GFVTDRC0            ");

        wsDatosAbend.setWsInstruccion("SELECT          ");

        wsDatosAbend.setWsTabla("GCCTDRC0        ");

        if (tbDclgfvtfla0.getTbFlaNum().substring(1, 1).isEqual("N")) {
            wsIndicadores.setWsDraCve(1);

        } else {
            wsIndicadores.setWsDraCve(2);

        }
        //                                                                  19180002
        gfvtdrc0Model = 
            gfvtdrc0Port.select_2(
                Gfvtdrc0Model.builder()
                    .agtIdr(dclgfvtcna0.getAgtIdr().toInt())
                    .empCve(toHostString(dclgfvtcna0.getEmpCve()))
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                    .draCve(wsIndicadores.getWsDraCve())
                .build());

        if(gfvtdrc0Model.isPresent()) {
            dclgfvtdrc0.setDrcIdrAgt(fromHostString(gfvtdrc0Model.get().drcIdrAgt()));
            dclgfvtdrc0.setDrcIdrGte(fromHostString(gfvtdrc0Model.get().drcIdrGte()));
        }
        //                                                                  19370002
        db2check();
        //                                                                  19390002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinDrc(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinDrc(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7100-LEER-GFVTAGC0.
     *
     * LEER LA TABLA  GFVTAGC0  CARTERA AGENTES
     */
    void p7100LeerGfvtagc0() {
        //                                                                  19560002
        // -----> CBA                                                       19570002
        // *   MOVE  '7100-LEER-GFVTAGC0            ' TO  WS-PROCESO-EJECUTA19580002
        // *   MOVE  'SELECT          '               TO  WS-INSTRUCCION.   19590002
        // *   MOVE  'GFVTAGC0        '               TO  WS-TABLA.         19600002
        //                                                                  19610002
        // *   EXEC  SQL                                                    19620002
        // *         SELECT                                                 19630002
        // *                  FLA_NUM                ,                      19640002
        // *                  AGT_APE_MAT            ,                      19650002
        // *                  AGT_APE_PAT            ,                      19660002
        // *                  AGT_NOM                ,                      19670002
        // *                  AGT_RAZ_SOC            ,                      19680002
        // *                  EMP_CVE                ,                      19690002
        // *                  ESA_CVE                ,                      19700002
        // *                  OFN_CVE                ,                      19710002
        // *                  ORV_CVE                ,                      19720002
        // *                  PEF_CVE                ,                      19730002
        // *                  USU_CVE_ACT            ,                      19740002
        // *                  FEC_ULT_ACT            ,                      19750002
        // *                  AGT_IDR                ,                      19760002
        // *                  CNA_NUM                ,                      19770002
        // *                  TFP_CVE                ,                      19780002
        // *                  CNA_IND_GER            ,                      19790002
        // *                  MTE_CVE                ,                      19800002
        // *                  FLA_FEC_MOT_ESS        ,                      19810002
        // *                  FLA_NUM_GTE            ,                      19820002
        // *                  CNA_GER_ZON_ENV                               19830002
        //                                                                  19840002
        // *          INTO                                                  19850002
        // *           :DCLGFVTAGC0.FLA-NUM                  ,              19860002
        // *           :DCLGFVTAGC0.AGT-APE-MAT       :AGC02 ,              19870002
        // *           :DCLGFVTAGC0.AGT-APE-PAT       :AGC03 ,              19880002
        // *           :DCLGFVTAGC0.AGT-NOM           :AGC04 ,              19890002
        // *           :DCLGFVTAGC0.AGT-RAZ-SOC       :AGC05 ,              19900002
        // *           :DCLGFVTAGC0.EMP-CVE           :AGC06 ,              19910002
        // *           :DCLGFVTAGC0.ESA-CVE           :AGC07 ,              19920002
        // *           :DCLGFVTAGC0.OFN-CVE           :AGC08 ,              19930002
        // *           :DCLGFVTAGC0.ORV-CVE           :AGC09 ,              19940002
        // *           :DCLGFVTAGC0.PEF-CVE           :AGC10 ,              19950002
        // *           :DCLGFVTAGC0.USU-CVE-ACT       :AGC11 ,              19960002
        // *           :DCLGFVTAGC0.FEC-ULT-ACT       :AGC12 ,              19970002
        // *           :DCLGFVTAGC0.AGT-IDR           :AGC13 ,              19980002
        // *           :DCLGFVTAGC0.CNA-NUM           :AGC14 ,              19990002
        // *           :DCLGFVTAGC0.TFP-CVE           :AGC15 ,              20000002
        // *           :DCLGFVTAGC0.CNA-IND-GER       :AGC16 ,              20010002
        // *           :DCLGFVTAGC0.MTE-CVE           :AGC17 ,              20020002
        // *           :DCLGFVTAGC0.FLA-FEC-MOT-ESS   :AGC18 ,              20030002
        // *           :DCLGFVTAGC0.FLA-NUM-GTE              ,              20040002
        // *           :DCLGFVTAGC0.CNA-GER-ZON-ENV   :AGC20                20050002
        //                                                                  20060002
        // *            FROM  NPMDS.GFVTAGC0                                20070002
        //                                                                  20080002
        // *           WHERE  FLA_NUM      =  :DCLGFVTFLA0.FLA-NUM          20090002
        //                                                                  20100002
        // *   END-EXEC.                                                    20110002
        //                                                                  20120002
        // *   PERFORM  DB2CHECK.                                           20130002
        //                                                                  20140002
        // *   IF  DB2-OK                                                   20150002
        // *       MOVE 0  TO  WS-FIN-AGC                                   20160002
        // *   ELSE                                                         20170002
        // *       IF  DB2-NOTFND                                           20180002
        // *           MOVE  1  TO  WS-FIN-AGC                              20190002
        // *       ELSE                                                     20200002
        // *           PERFORM  9999-ANALIZA-SQL.                           20210002
        // *   EXIT.                                                        20220002
        agMayor = 0;

        if (tbDclgfvtagc0.getTtFlaNum().isEqual(tbDclgfvtfla0.getTbFlaNum())) {
            // * JRB                                                            20260002
            p7101MueveGfvtagc0();
            wsIndicadores.setWsFinAgc(0);

            agMayor = 1;

        } else if (tbDclgfvtagc0.getTtFlaNum().compareTo(tbDclgfvtfla0.getTbFlaNum()) > 0) {
            wsIndicadores.setWsFinAgc(1);

            agMayor = 1;

        }
        while (!(agFinArch == 1
            || agMayor == 1)) {
                agentes.read(tbDclgfvtagc0);
                if (fsAgentes == 0) {
                    // * JRB                                                            20400002
                    p7101MueveGfvtagc0();
                    if (tbDclgfvtagc0.getTtFlaNum().isEqual(tbDclgfvtfla0.getTbFlaNum())) {
                        wsIndicadores.setWsFinAgc(0);

                        agMayor = 1;

                    } else if (tbDclgfvtagc0.getTtFlaNum().compareTo(tbDclgfvtfla0.getTbFlaNum()) > 0) {
                        wsIndicadores.setWsFinAgc(1);

                        agMayor = 1;

                    }
                } else {
                    wsIndicadores.setWsFinAgc(1);

                    agFinArch = 1;

                }
        }
    }

    /**
     *  Paragraph: 7101-MUEVE-GFVTAGC0.
     *
     * <----- CBA
     * JRB--->
     */
    void p7101MueveGfvtagc0() {
        wsGfvtagc0Null.initialize();
        dclgfvtagc0.setFlaNum(tbDclgfvtagc0.getTtFlaNum());
        dclgfvtagc0.setAgtApeMat(tbDclgfvtagc0.getTtAgtApeMat());
        if (!tbDclgfvtagc0.getTtAgc02().isLowValue()) {
            wsGfvtagc0Null.setAgc02(-1);

        }
        dclgfvtagc0.setAgtApePat(tbDclgfvtagc0.getTtAgtApePat());
        if (!tbDclgfvtagc0.getTtAgc03().isLowValue()) {
            wsGfvtagc0Null.setAgc03(-1);

        }
        dclgfvtagc0.setAgtNom(tbDclgfvtagc0.getTtAgtNom());
        if (!tbDclgfvtagc0.getTtAgc04().isLowValue()) {
            wsGfvtagc0Null.setAgc04(-1);

        }
        dclgfvtagc0.setAgtRazSoc(tbDclgfvtagc0.getTtAgtRazSoc());
        if (!tbDclgfvtagc0.getTtAgc05().isLowValue()) {
            wsGfvtagc0Null.setAgc05(-1);

        }
        dclgfvtagc0.setEmpCve(tbDclgfvtagc0.getTtEmpCve());
        if (!tbDclgfvtagc0.getTtAgc06().isLowValue()) {
            wsGfvtagc0Null.setAgc06(-1);

        }
        dclgfvtagc0.setEsaCve(tbDclgfvtagc0.getTtEsaCve());
        if (!tbDclgfvtagc0.getTtAgc07().isLowValue()) {
            wsGfvtagc0Null.setAgc07(-1);

        }
        dclgfvtagc0.setOfnCve(tbDclgfvtagc0.getTtOfnCve());
        if (!tbDclgfvtagc0.getTtAgc08().isLowValue()) {
            wsGfvtagc0Null.setAgc08(-1);

        }
        dclgfvtagc0.setOrvCve(tbDclgfvtagc0.getTtOrvCve());
        if (!tbDclgfvtagc0.getTtAgc09().isLowValue()) {
            wsGfvtagc0Null.setAgc09(-1);

        }
        dclgfvtagc0.setPefCve(tbDclgfvtagc0.getTtPefCve());
        if (!tbDclgfvtagc0.getTtAgc10().isLowValue()) {
            wsGfvtagc0Null.setAgc10(-1);

        }
        dclgfvtagc0.setUsuCveAct(tbDclgfvtagc0.getTtUsuCveAct());
        if (!tbDclgfvtagc0.getTtAgc11().isLowValue()) {
            wsGfvtagc0Null.setAgc11(-1);

        }
        dclgfvtagc0.setFecUltAct(tbDclgfvtagc0.getTtFecUltAct());
        if (!tbDclgfvtagc0.getTtAgc12().isLowValue()) {
            wsGfvtagc0Null.setAgc12(-1);

        }
        dclgfvtagc0.setAgtIdr(tbDclgfvtagc0.getTtAgtIdr());
        if (!tbDclgfvtagc0.getTtAgc13().isLowValue()) {
            wsGfvtagc0Null.setAgc13(-1);

        }
        dclgfvtagc0.setCnaNum(tbDclgfvtagc0.getTtCnaNum());
        if (!tbDclgfvtagc0.getTtAgc14().isLowValue()) {
            wsGfvtagc0Null.setAgc14(-1);

        }
        dclgfvtagc0.setTfpCve(tbDclgfvtagc0.getTtTfpCve());
        if (!tbDclgfvtagc0.getTtAgc15().isLowValue()) {
            wsGfvtagc0Null.setAgc15(-1);

        }
        dclgfvtagc0.setCnaIndGer(tbDclgfvtagc0.getTtCnaIndGer());
        if (!tbDclgfvtagc0.getTtAgc16().isLowValue()) {
            wsGfvtagc0Null.setAgc16(-1);

        }
        dclgfvtagc0.setMteCve(tbDclgfvtagc0.getTtMteCve());
        if (!tbDclgfvtagc0.getTtAgc17().isLowValue()) {
            wsGfvtagc0Null.setAgc17(-1);

        }
        dclgfvtagc0.setFlaFecMotEss(tbDclgfvtagc0.getTtFlaFecMotEss());
        if (!tbDclgfvtagc0.getTtAgc18().isLowValue()) {
            wsGfvtagc0Null.setAgc18(-1);

        }
        dclgfvtagc0.setFlaNumGte(tbDclgfvtagc0.getTtFlaNumGte());
        dclgfvtagc0.setCnaGerZonEnv(tbDclgfvtagc0.getTtCnaGerZonEnv());
        dclgfvtagc0.setDrcIdrAgt(tbDclgfvtagc0.getTtDrcIdrAgt());
        dclgfvtagc0.setDrcIdrGte(tbDclgfvtagc0.getTtDrcIdrGte());
        if (!tbDclgfvtagc0.getTtAgc20().isLowValue()) {
            wsGfvtagc0Null.setAgc20(-1);

        }
        dclgfvtagc0.setFlaDes(tbDclgfvtagc0.getTtFlaDes());
        dclgfvtagc0.setAgtTraEsp(tbDclgfvtagc0.getTtAgtTraEsp());
        dclgfvtagc0.setCnaFecCnx(tbDclgfvtagc0.getTtCnaFecCnx());
        dclgfvtagc0.setGnaCve(tbDclgfvtagc0.getTtGnaCve());
        dclgfvtagc0.setEsaCveFla(tbDclgfvtagc0.getTtEsaCveFla());
        dclgfvtagc0.setFlaGerZon(tbDclgfvtagc0.getTtFlaGerZon());
        dclgfvtagc0.setOfnCveCna(tbDclgfvtagc0.getTtOfnCveCna());
        dclgfvtagc0.setAgtIdrGte(tbDclgfvtagc0.getTtAgtIdrGte());
        if (!tbDclgfvtagc0.getTtAgc21().isLowValue()) {
            wsGfvtagc0Null.setAgc21(-1);

        }
        if (!tbDclgfvtagc0.getTtAgc22().isLowValue()) {
            wsGfvtagc0Null.setAgc22(-1);

        }
        if (!tbDclgfvtagc0.getTtAgc23().isLowValue()) {
            wsGfvtagc0Null.setAgc23(-1);

        }
        if (!tbDclgfvtagc0.getTtAgc24().isLowValue()) {
            wsGfvtagc0Null.setAgc24(-1);

        }
        if (!tbDclgfvtagc0.getTtAgc25().isLowValue()) {
            wsGfvtagc0Null.setAgc25(-1);

        }
        if (!tbDclgfvtagc0.getTtAgc26().isLowValue()) {
            wsGfvtagc0Null.setAgc26(-1);

        }
        if (!tbDclgfvtagc0.getTtAgc27().isLowValue()) {
            wsGfvtagc0Null.setAgc27(-1);

        }
        if (!tbDclgfvtagc0.getTtAgc28().isLowValue()) {
            wsGfvtagc0Null.setAgc28(-1);

            //         DISPLAY 'PASS ' FLA-NUM OF DCLGFVTAGC0                   21690002
            dclgfvtagc0.getAgtIdrGte().setZero();
        }
    }

    /**
     *  Paragraph: 7850-INICIO-GFVCFLA0.
     *
     * <-----JRB
     * ABRE CURSOR Y LEE EL PRIMER GFVTFLA0
     */
    void p7850InicioGfvcfla0() {
        //                                                                  21800002
        // -----> CBA 15/02/2001                                            21810002
        // *   MOVE  '7850-INICIO-GFVCFLA0          ' TO  WS-PROCESO-EJECUTA21820002
        // *   MOVE  'OPEN            '               TO  WS-INSTRUCCION.   21830002
        // *   MOVE  'GFVTFLA0        '               TO  WS-TABLA.         21840002
        //                                                                  21850002
        // *   EXEC  SQL                                                    21860002
        // *         OPEN  GFVCFLA0                                         21870002
        // *   END-EXEC.                                                    21880002
        //                                                                  21890002
        // *   PERFORM  DB2CHECK.                                           21900002
        //                                                                  21910002
        // *   IF  DB2-OK                                                   21920002
        // *       MOVE  0  TO  WS-FIN-FLA                                  21930002
        // *   ELSE                                                         21940002
        // *       PERFORM  9999-ANALIZA-SQL.                               21950002
        folios.openInput();
        agentes.openInput();
        if (fsFolios > 0) {
            display("ERROR AL ABRIR FOLIOS FS=", fsFolios);
            returnCode.setValue(4);
            throw new StopRunException();
        }
        if (fsAgentes > 0) {
            display("ERROR AL ABRIR AGENTES FS=", fsAgentes);
            returnCode.setValue(4);
            throw new StopRunException();
        }
        // <----- CBA                                                       22070002
        //                                                                  22080002
        p7850LeerGfvtfla0();
    }

    /**
     *  Paragraph: 7850-LEER-GFVTFLA0.
     *
     * FETCH A GFVTFLA0       FOLIOS
     */
    void p7850LeerGfvtfla0() {
        //                                                                  22180002
        // -----> CBA 15/02/2001                                            22190002
        // *   MOVE  '7850-LEER-GFVTFLA0            ' TO  WS-PROCESO-EJECUTA22200002
        // *   MOVE  'FETCH           '               TO  WS-INSTRUCCION.   22210002
        // *   MOVE  'GFVTFLA0        '               TO  WS-TABLA.         22220002
        //                                                                  22230002
        // *   EXEC  SQL                                                    22240002
        // *         FETCH  GFVCFLA0                                        22250002
        // *          INTO                                                  22260002
        // *               :DCLGFVTFLA0.FLA-NUM                      ,      22270002
        // *               :DCLGFVTFLA0.FLA-DES                      ,      22280002
        // *               :DCLGFVTFLA0.FLA-FEC-INI-ASG :FLA03       ,      22290002
        // *               :DCLGFVTFLA0.FLA-FEC-FIN-ASG :FLA04       ,      22300002
        // *               :DCLGFVTFLA0.OFN-CVE         :FLA05       ,      22310002
        // *               :DCLGFVTFLA0.AGT-IDR                      ,      22320002
        // *               :DCLGFVTFLA0.EMP-CVE                      ,      22330002
        // *               :DCLGFVTFLA0.CNA-NUM                      ,      22340002
        // *               :DCLGFVTFLA0.MTE-CVE                      ,      22350002
        // *               :DCLGFVTFLA0.ORV-CVE                      ,      22360002
        // *               :DCLGFVTFLA0.FLA-GER-ZON     :FLA11       ,      22370002
        // *               :DCLGFVTFLA0.FLA-NEG-CVE     :FLA12       ,      22380002
        // *               :DCLGFVTFLA0.FLA-FEC-MOT-ESS :FLA13              22390002
        //                                                                  22400002
        // *   END-EXEC.                                                    22410002
        //                                                                  22420002
        // *   PERFORM  DB2CHECK.                                           22430002
        //                                                                  22440002
        // *   IF  DB2-OK                                                   22450002
        // *       MOVE  0  TO  WS-FIN-FLA                                  22460002
        // *       ADD   1  TO  TOT-LEIDOS                                  22470002
        // *   ELSE                                                         22480002
        // *       IF  DB2-NOTFND                                           22490002
        // *           MOVE  1  TO  WS-FIN-FLA                              22500002
        // *       ELSE                                                     22510002
        // *           PERFORM  9999-ANALIZA-SQL.                           22520002
        //                                                                  22530002
        // *   EXIT.                                                        22540002
        folios.read(tbDclgfvtfla0);
        if (fsFolios == 0) {
            dclgfvtfla0.setFlaNum(tbDclgfvtfla0.getTbFlaNum());
            dclgfvtfla0.setFlaDes(tbDclgfvtfla0.getTbFlaDes());
            dclgfvtfla0.setFlaFecIniAsg(tbDclgfvtfla0.getTbFlaFecIniAsg());
            if (!tbDclgfvtfla0.getTbFla03().isLowValue()) {
                wsGfvtfla0Null.setFla03(-1);
            }
            dclgfvtfla0.setFlaFecFinAsg(tbDclgfvtfla0.getTbFlaFecFinAsg());
            if (!tbDclgfvtfla0.getTbFla04().isLowValue()) {
                wsGfvtfla0Null.setFla04(-1);
            }
            dclgfvtfla0.setOfnCve(tbDclgfvtfla0.getTbOfnCve());
            if (!tbDclgfvtfla0.getTbFla05().isLowValue()) {
                wsGfvtfla0Null.setFla05(-1);
            }
            dclgfvtfla0.setAgtIdr(tbDclgfvtfla0.getTbAgtIdr());
            dclgfvtfla0.setEmpCve(tbDclgfvtfla0.getTbEmpCve());
            dclgfvtfla0.setCnaNum(tbDclgfvtfla0.getTbCnaNum());
            dclgfvtfla0.setMteCve(tbDclgfvtfla0.getTbMteCve());
            dclgfvtfla0.setOrvCve(tbDclgfvtfla0.getTbOrvCve());
            dclgfvtfla0.setFlaGerZon(tbDclgfvtfla0.getTbFlaGerZon());
            if (!tbDclgfvtfla0.getTbFla11().isLowValue()) {
                wsGfvtfla0Null.setFla11(-1);
            }
            dclgfvtfla0.setFlaNegCve(tbDclgfvtfla0.getTbFlaNegCve());
            if (!tbDclgfvtfla0.getTbFla12().isLowValue()) {
                wsGfvtfla0Null.setFla12(-1);
            }
            dclgfvtfla0.setFlaFecMotEss(tbDclgfvtfla0.getTbFlaFecMotEss());
            if (!tbDclgfvtfla0.getTbFla13().isLowValue()) {
                wsGfvtfla0Null.setFla13(-1);
            }
            wsIndicadores.setWsFinFla(0);

            wsTotales.setTotLeidos(wsTotales.getTotLeidos() + 1);
        } else {
            wsIndicadores.setWsFinFla(1);

        }
    }

    /**
     *  Paragraph: 7850-CIERRO-GFVCFLA0.
     *
     * <----- CBA
     */
    void p7850CierroGfvcfla0() {
        //                                                                  22990002
        // -----> CBA 15/02/2001                                            23000002
        // *   MOVE  '7850-CIERRO-GFVCFLA0          ' TO  WS-PROCESO-EJECUTA23010002
        // *   MOVE  'CLOSE           '               TO  WS-INSTRUCCION.   23020002
        // *   MOVE  'GFVTFLA0        '               TO  WS-TABLA.         23030002
        //                                                                  23040002
        // *   EXEC  SQL                                                    23050002
        // *         CLOSE GFVCFLA0                                         23060002
        // *   END-EXEC.                                                    23070002
        //                                                                  23080002
        // *   PERFORM  DB2CHECK.                                           23090002
        //                                                                  23100002
        // *   IF  DB2-OK                                                   23110002
        // *       NEXT  SENTENCE                                           23120002
        // *   ELSE                                                         23130002
        // *       PERFORM  9999-ANALIZA-SQL.                               23140002
        // *   EXIT.                                                        23150002
        folios.close();
        agentes.close();
    }

    /**
     *  Paragraph: 7960-INICIO-GFVTCNA1.
     *
     * <----- CBA
     * ABRE CURSOR Y LEE EL PRIMER CONTRATO
     */
    void p7960InicioGfvtcna1() {
        //                                                                  23260002
        wsDatosAbend.setWsProcesoEjecuta("7960-INICIO-GFVTCNA1          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  23300002
        gfvccna1Cursor.open();
        //                                                                  23340002
        db2check();
        //                                                                  23360002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCna1(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  23410002
        p7960LeerGfvtcna1();
    }

    /**
     *  Paragraph: 7960-LEER-GFVTCNA1.
     *
     * LEER LA TABLA  GFVTCNA0  CONTRATO.
     */
    void p7960LeerGfvtcna1() {
        //                                                                  23510002
        wsDatosAbend.setWsProcesoEjecuta("7960-LEER-GFVTCNA1            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        gfvtcna0Model = gfvccna1Cursor.next();

        if (gfvtcna0Model.isPresent()) {
            dclgfvtcna0.setAgtIdr(fromHostInt(gfvtcna0Model.get().agtIdr()));
            dclgfvtcna0.setEmpCve(fromHostString(gfvtcna0Model.get().empCve()));
            dclgfvtcna0.setCnaNum(fromHostInt(gfvtcna0Model.get().cnaNum()));
            dclgfvtcna0.setCnaTpoCnr(fromHostString(gfvtcna0Model.get().cnaTpoCnr()));
            dclgfvtcna0.setCnaUltCia(fromHostString(gfvtcna0Model.get().cnaUltCia()));
            wsGfvtcna0Null.setCna05(isNull(gfvtcna0Model.get().cnaUltCia()) ? -1 : 0);
            dclgfvtcna0.setCnaFecCnx(fromHostString(gfvtcna0Model.get().cnaFecCnx()));
            dclgfvtcna0.setCnaCodIpu(fromHostString(gfvtcna0Model.get().cnaCodIpu()));
            dclgfvtcna0.setCnaPjeIva(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIva()));
            dclgfvtcna0.setCnaPjeIsr(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeIsr()));
            dclgfvtcna0.setCnaComNiv(fromHostString(gfvtcna0Model.get().cnaComNiv()));
            wsGfvtcna0Null.setCna10(isNull(gfvtcna0Model.get().cnaComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaImpComNiv(fromHostBigDecimal(gfvtcna0Model.get().cnaImpComNiv()));
            wsGfvtcna0Null.setCna11(isNull(gfvtcna0Model.get().cnaImpComNiv()) ? -1 : 0);
            dclgfvtcna0.setCnaNumFza(fromHostString(gfvtcna0Model.get().cnaNumFza()));
            wsGfvtcna0Null.setCna12(isNull(gfvtcna0Model.get().cnaNumFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecIniFza(fromHostString(gfvtcna0Model.get().cnaFecIniFza()));
            wsGfvtcna0Null.setCna13(isNull(gfvtcna0Model.get().cnaFecIniFza()) ? -1 : 0);
            dclgfvtcna0.setCnaFecFinFza(fromHostString(gfvtcna0Model.get().cnaFecFinFza()));
            wsGfvtcna0Null.setCna14(isNull(gfvtcna0Model.get().cnaFecFinFza()) ? -1 : 0);
            dclgfvtcna0.setCnaImpFza(fromHostBigDecimal(gfvtcna0Model.get().cnaImpFza()));
            wsGfvtcna0Null.setCna15(isNull(gfvtcna0Model.get().cnaImpFza()) ? -1 : 0);
            dclgfvtcna0.setCnaIdrFav(fromHostString(gfvtcna0Model.get().cnaIdrFav()));
            dclgfvtcna0.setCnaPjeFav(fromHostBigDecimal(gfvtcna0Model.get().cnaPjeFav()));
            wsGfvtcna0Null.setCna17(isNull(gfvtcna0Model.get().cnaPjeFav()) ? -1 : 0);
            dclgfvtcna0.setPpgCve(fromHostInt(gfvtcna0Model.get().ppgCve()));
            dclgfvtcna0.setCpgCve(fromHostInt(gfvtcna0Model.get().cpgCve()));
            dclgfvtcna0.setCdeCve(fromHostInt(gfvtcna0Model.get().cdeCve()));
            dclgfvtcna0.setOfnCve(fromHostInt(gfvtcna0Model.get().ofnCve()));
            dclgfvtcna0.setTaaCve(fromHostString(gfvtcna0Model.get().taaCve()));
            dclgfvtcna0.setGnaCveAgt(fromHostString(gfvtcna0Model.get().gnaCveAgt()));
            dclgfvtcna0.setGnaCve(fromHostInt(gfvtcna0Model.get().gnaCve()));
            dclgfvtcna0.setTfpCve(fromHostInt(gfvtcna0Model.get().tfpCve()));
            dclgfvtcna0.setAgtIdrRcl(fromHostInt(gfvtcna0Model.get().agtIdrRcl()));
            dclgfvtcna0.setCtbNumCta(fromHostString(gfvtcna0Model.get().ctbNumCta()));
            wsGfvtcna0Null.setCna27(isNull(gfvtcna0Model.get().ctbNumCta()) ? -1 : 0);
            dclgfvtcna0.setMteCve(fromHostInt(gfvtcna0Model.get().mteCve()));
            dclgfvtcna0.setMdeCve(fromHostInt(gfvtcna0Model.get().mdeCve()));
            dclgfvtcna0.setZecCve(fromHostInt(gfvtcna0Model.get().zecCve()));
            dclgfvtcna0.setCnaCodPag(fromHostString(gfvtcna0Model.get().cnaCodPag()));
            wsGfvtcna0Null.setCna31(isNull(gfvtcna0Model.get().cnaCodPag()) ? -1 : 0);
            dclgfvtcna0.setCnaFecMotEss(fromHostString(gfvtcna0Model.get().cnaFecMotEss()));
            wsGfvtcna0Null.setCna32(isNull(gfvtcna0Model.get().cnaFecMotEss()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonEnv(fromHostInt(gfvtcna0Model.get().cnaGerZonEnv()));
            wsGfvtcna0Null.setCna33(isNull(gfvtcna0Model.get().cnaGerZonEnv()) ? -1 : 0);
            dclgfvtcna0.setCnaGerZonDcm(fromHostInt(gfvtcna0Model.get().cnaGerZonDcm()));
            wsGfvtcna0Null.setCna34(isNull(gfvtcna0Model.get().cnaGerZonDcm()) ? -1 : 0);
            dclgfvtcna0.setCnaIndGer(fromHostString(gfvtcna0Model.get().cnaIndGer()));
            wsGfvtcna0Null.setCna35(isNull(gfvtcna0Model.get().cnaIndGer()) ? -1 : 0);
            dclgfvtcna0.setCnaFecAsgGer(fromHostString(gfvtcna0Model.get().cnaFecAsgGer()));
            wsGfvtcna0Null.setCna36(isNull(gfvtcna0Model.get().cnaFecAsgGer()) ? -1 : 0);
        }
        //                                                                  23960002
        db2check();
        //                                                                  23980002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinCna1(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinCna1(1);

        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7960-CERRAR-GFVTCNA1.
     *
     * CIERRO EL CURSOR DE CONTRATOS.
     */
    void p7960CerrarGfvtcna1() {
        //                                                                  24150002
        wsDatosAbend.setWsProcesoEjecuta("7960-CERRAR-GFVTCNA1          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTCNA0        ");

        //                                                                  24190002
        if (gfvccna1Cursor != null) {
            gfvccna1Cursor.close();
        }
        //                                                                  24230002
        db2check();
        //                                                                  24250002
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 7970-INICIO-GFVTFLA2.
     *
     * ABRE CURSOR Y LEE EL PRIMER GFVTFLA2
     */
    void p7970InicioGfvtfla2() {
        //                                                                  24380002
        wsDatosAbend.setWsProcesoEjecuta("7970-INICIO-GFVTFLA2          ");

        wsDatosAbend.setWsInstruccion("OPEN            ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  24420002
        gfvcfla2Cursor.open();
        //                                                                  24460002
        db2check();
        //                                                                  24480002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFla2(0);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  24530002
        p7970LeerGfvtfla2();
    }

    /**
     *  Paragraph: 7970-LEER-GFVTFLA2.
     *
     * FETCH A GFVTFLA2       FOLIOS
     */
    void p7970LeerGfvtfla2() {
        //                                                                  24630002
        wsDatosAbend.setWsProcesoEjecuta("7970-LEER-GFVTFLA2            ");

        wsDatosAbend.setWsInstruccion("FETCH           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  24670002
        gfvtfla0Model = gfvcfla2Cursor.next();

        if (gfvtfla0Model.isPresent()) {
            dclgfvtfla0.setFlaNum(fromHostString(gfvtfla0Model.get().flaNum()));
            dclgfvtfla0.setFlaDes(fromHostString(gfvtfla0Model.get().flaDes()));
            dclgfvtfla0.setFlaFecIniAsg(fromHostString(gfvtfla0Model.get().flaFecIniAsg()));
            wsGfvtfla2Null.setFla0333(isNull(gfvtfla0Model.get().flaFecIniAsg()) ? -1 : 0);
            dclgfvtfla0.setFlaFecFinAsg(fromHostString(gfvtfla0Model.get().flaFecFinAsg()));
            wsGfvtfla2Null.setFla0444(isNull(gfvtfla0Model.get().flaFecFinAsg()) ? -1 : 0);
            dclgfvtfla0.setOfnCve(fromHostInt(gfvtfla0Model.get().ofnCve()));
            wsGfvtfla2Null.setFla0555(isNull(gfvtfla0Model.get().ofnCve()) ? -1 : 0);
            dclgfvtfla0.setAgtIdr(fromHostInt(gfvtfla0Model.get().agtIdr()));
            dclgfvtfla0.setEmpCve(fromHostString(gfvtfla0Model.get().empCve()));
            dclgfvtfla0.setCnaNum(fromHostInt(gfvtfla0Model.get().cnaNum()));
            dclgfvtfla0.setMteCve(fromHostInt(gfvtfla0Model.get().mteCve()));
            dclgfvtfla0.setOrvCve(fromHostInt(gfvtfla0Model.get().orvCve()));
            dclgfvtfla0.setFlaGerZon(fromHostInt(gfvtfla0Model.get().flaGerZon()));
            wsGfvtfla2Null.setFla1111(isNull(gfvtfla0Model.get().flaGerZon()) ? -1 : 0);
            dclgfvtfla0.setFlaNegCve(fromHostString(gfvtfla0Model.get().flaNegCve()));
            wsGfvtfla2Null.setFla1222(isNull(gfvtfla0Model.get().flaNegCve()) ? -1 : 0);
            dclgfvtfla0.setFlaFecMotEss(fromHostString(gfvtfla0Model.get().flaFecMotEss()));
            wsGfvtfla2Null.setFla1333(isNull(gfvtfla0Model.get().flaFecMotEss()) ? -1 : 0);
        }
        //                                                                  24860002
        db2check();
        //                                                                  24880002
        if (this.isDb2Ok()) {
            wsIndicadores.setWsFinFla2(0);

        } else if (this.isDb2Notfnd()) {
            wsIndicadores.setWsFinFla2(1);

        } else {
            p9999AnalizaSql();
        }
        //                                                                  24960002
    }

    /**
     *  Paragraph: 7970-CERRAR-GFVTFLA2.
     *
     */
    void p7970CerrarGfvtfla2() {
        //                                                                  25010002
        wsDatosAbend.setWsProcesoEjecuta("7970-CERRAR-GFVTFLA2          ");

        wsDatosAbend.setWsInstruccion("CLOSE           ");

        wsDatosAbend.setWsTabla("GFVTFLA0        ");

        //                                                                  25050002
        if (gfvcfla2Cursor != null) {
            gfvcfla2Cursor.close();
        }
        //                                                                  25090002
        db2check();
        //                                                                  25110002
        if (this.isDb2Ok()) {
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: DB2CHECK.
     *
     * DB2CHECK   RUTINA QUE ANALIZA CODIGO VALIDO.                   *
     */
    void db2check() {
        //                                                                  25250002
        db2ReturnCode = sqlca.getSqlcode();

        //                                                                  25270002
        if (this.isDb2Ok() 
            || this.isDb2Notfnd() 
            || this.isDb2Duprec()) {
            ;
        } else {
            p9999AnalizaSql();
        }
    }

    /**
     *  Paragraph: 9999-ANALIZA-SQL.
     *
     * 9999 RUTINA QUE ANALIZA EL CODIGO DE DB2.                      *
     * *****************ANALIZA*****************************************
     */
    void p9999AnalizaSql() {
        //                                                                  25420002
        if (sqlca.getSqlcode() < 0) {
            wsCodigos.setWsSqlcode(Numeric.multiply(sqlca.getSqlcode(), (-1)).toInt());
            wsCodigos.setWsDis1("-");
            wsCodigos.setWsDis2(wsCodigos.getWsSqlcode());
        } else {
            wsCodigos.setWsDis1("+");
            wsCodigos.setWsDis2(sqlca.getSqlcode());
        }
        //                                                                  25500002
        //                                                                  25510002
        display("============ ERROR DB2 ============");
        display("  ");
        display("CODIGO DE ERROR        ===> ", wsCodigos.getWsCodError());
        display("INSTRUCCION SQL        ===> ", wsDatosAbend.getWsInstruccion());
        display("RUTINA LLAMADORA       ===> ", wsDatosAbend.getWsProcesoLlama());
        display("RUTINA EJECUTA         ===> ", wsDatosAbend.getWsProcesoEjecuta());
        display("TABLA                  ===> ", wsDatosAbend.getWsTabla());
        display("TB-FLA-NUM             ===> ", tbDclgfvtfla0.getTbFlaNum());
        display("PROGRAMA               ===> ", wsDatosAbend.getWsPrograma());
        getSqlExecutor().rollback(sqlca);
        //                                                                  25640002
        //                                                                  25650002
        if (sqlca.getSqlerrml() > 0) {
            display("VARIABLES ERRONEAS    . . .: ", sqlca.getSqlerrmc());
        }
        if (sqlca.getSqlwarn5().equals("W")) {
            display("MANDATO NO EJECUTADO  . . .: ");
            if (sqlca.getSqlwarn6().equals("W")) {
                display("VALOR DE FECHA AJUSTADO . .: ");
            }
            if (sqlca.getSqlwarn9().equals("W")) {
                display("EXCEPCION ARITMET IGNORADA.: ");
            }
        }
        //                                                                  25770002
        // -----> CBA                                                       25780002
        returnCode.setValue(4);
        // <----- CBA                                                       25800002
        throw new GobackException();
        /* [@WARNING Unreachable code]
        throw new StopRunException();
        */
    }

    /**
     *  Paragraph: 3000-FIN.
     *
     * 30000        FINALES DEL PROCESO                               *
     */
    void p3000Fin() {
        //                                                                  25930002
        linea.setDet12Letrero("TOT. FOLIOS LEIDOS ");
        linea.getDet12Total().setValue(wsTotales.getTotLeidos());
        p6000Imprimir();
        //                                                                  25970002
        linea.setDet12Letrero("TOT. ALTAS         ");
        linea.getDet12Total().setValue(wsTotales.getTotGrabados());
        p6000Imprimir();
        //                                                                  26010002
        linea.setDet12Letrero("TOT. CAMBIOS       ");
        linea.getDet12Total().setValue(wsTotales.getTotCambios());
        p6000Imprimir();
        //                                                                  26050002
        linea.setDet12Letrero("TOT. ALTAS DUP.    ");
        linea.getDet12Total().setValue(wsTotales.getTotDuplicados());
        p6000Imprimir();
        //                                                                  26090002
        listado.close();
    }

    // Conditionals (88) - DB2-RETURN-CODE
    public boolean isDb2Ok() {
        return this.db2ReturnCode == DB2_OK;
    }

    public void setDb2Ok() {
        this.db2ReturnCode = DB2_OK;
    }

    public boolean isDb2Notfnd() {
        return this.db2ReturnCode == DB2_NOTFND;
    }

    public void setDb2Notfnd() {
        this.db2ReturnCode = DB2_NOTFND;
    }

    public boolean isDb2Duprec() {
        return this.db2ReturnCode == DB2_DUPREC;
    }

    public void setDb2Duprec() {
        this.db2ReturnCode = DB2_DUPREC;
    }

    public boolean isDb2Notuni() {
        return this.db2ReturnCode == DB2_NOTUNI;
    }

    public void setDb2Notuni() {
        this.db2ReturnCode = DB2_NOTUNI;
    }

    public boolean isDb2Datetime() {
        return this.db2ReturnCode == DB2_DATETIME;
    }

    public void setDb2Datetime() {
        this.db2ReturnCode = DB2_DATETIME;
    }

    public boolean isDb2System() {
        return this.db2ReturnCode == DB2_SYSTEM;
    }

    public void setDb2System() {
        this.db2ReturnCode = DB2_SYSTEM;
    }



    @Autowired
    public void setGfvtcna0Port(IGfvtcna0Port gfvtcna0Port) {
        this.gfvtcna0Port = gfvtcna0Port;
        this.gfvtcna0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdrc0Port(IGfvtdrc0Port gfvtdrc0Port) {
        this.gfvtdrc0Port = gfvtdrc0Port;
        this.gfvtdrc0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtmte0Port(IGfvtmte0Port gfvtmte0Port) {
        this.gfvtmte0Port = gfvtmte0Port;
        this.gfvtmte0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctorv0Port(IGcctorv0Port gcctorv0Port) {
        this.gcctorv0Port = gcctorv0Port;
        this.gcctorv0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagc0Port(IGfvtagc0Port gfvtagc0Port) {
        this.gfvtagc0Port = gfvtagc0Port;
        this.gfvtagc0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagt0Port(IGfvtagt0Port gfvtagt0Port) {
        this.gfvtagt0Port = gfvtagt0Port;
        this.gfvtagt0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtfla0Port(IGfvtfla0Port gfvtfla0Port) {
        this.gfvtfla0Port = gfvtfla0Port;
        this.gfvtfla0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setSysdummy1Port(ISysdummy1Port sysdummy1Port) {
        this.sysdummy1Port = sysdummy1Port;
        this.sysdummy1Port.setProgramContext(getProgramContext());
    }
}
