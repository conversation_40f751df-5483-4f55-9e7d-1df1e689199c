
/**
 *  Program: Ggqaat00.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */

package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core;


import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.Getter;
import lombok.Setter;
import com.base100.caravel.support.common.context.IProgram;
import com.base100.caravel.support.common.util.TokenInto;
import com.base100.caravel.support.common.cte._Constants;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;
import com.base100.caravel.support.common.hexagonal.cursor.ICursor;
import com.base100.caravel.support.common.hexagonal.model.GenericModel;
import com.base100.caravel.support.common.sql.ISqlErrorListener;
import com.base100.caravel.support.common.sql.SqlCursor;
import com.base100.caravel.support.common.string.StringUtils;
import com.base100.caravel.support.common.sql.Sqlca;
import com.base100.caravel.support.common.cbl.exception.*;
import com.base100.caravel.support.common.cbl.file.AccessMode;
import com.base100.caravel.support.common.cbl.file.ICblFile;
import com.base100.caravel.support.common.cbl.file.IIndexedFile;
import com.base100.caravel.support.common.cbl.file.ISequentialFile;
import com.base100.caravel.support.common.cbl.functions.Functions;
import com.base100.caravel.support.common.cbl.search.SearchListener;
import com.base100.caravel.support.os390.cbl.context.IOs390CblProgramContext;
import com.base100.caravel.support.os390.cbl.AbstractCobolOs390Program;
import com.base100.caravel.support.os390.cics.constants.Cics;

import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagt0cpy.Dclgfvtagt0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtagc0cpy.Dclgfvtagc0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcna0cpy.Dclgfvtcna0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtfla0cpy.Dclgfvtfla0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtdma0cpy.Gfvtdma0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtcma0cpy.Dclgfvtcma0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gfvtpra0cpy.Dclgfvtpra0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctedo0cpy.Dclgcctedo0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctpob0cpy.Dclgcctpob0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctmnd0cpy.Dclgcctmnd0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctofn0cpy.Dclgcctofn0;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.gcctceo0cpy.Dclgcctceo0;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.WsInterfaz;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.WsContadores;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.WsSwitches;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.WsAyuda;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.WsConstantes;
import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqaat00.WsNulls;
import mx.com.gnp.cuentacorriente.common.business.domain.core.common.data.ggqa00ahcpy.WsGmmSqlca;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcna0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtcma0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtpra0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctedo0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctpob0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagc0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctmnd0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctofn0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGcctceo0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtagt0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtfla0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.outgoing.IGfvtdma0Port;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcna0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtcma0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtpra0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctedo0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctpob0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagc0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctmnd0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctofn0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gcctceo0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtagt0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtfla0Model;
import mx.com.gnp.cuentacorriente.common.business.domain.model.Gfvtdma0Model;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Getter @Setter
public class Ggqaat00 extends AbstractCobolOs390Program implements IProgram {

    // Level 1
    private AlphanumericVar regSal = new AlphanumericVar(844);               // 01 REG-SAL X(844) UsedAsReferenceParameter

    // Copy file QA$CBLCPY$GFVTAGT0
    private Dclgfvtagt0 dclgfvtagt0 = new  Dclgfvtagt0();                    // 01 DCLGFVTAGT0
    // End copy file QA$CBLCPY$GFVTAGT0

    // Copy file QA$CBLCPY$GFVTAGC0
    private Dclgfvtagc0 dclgfvtagc0 = new  Dclgfvtagc0();                    // 01 DCLGFVTAGC0
    // End copy file QA$CBLCPY$GFVTAGC0

    // Copy file QA$CBLCPY$GFVTCNA0
    private Dclgfvtcna0 dclgfvtcna0 = new  Dclgfvtcna0();                    // 01 DCLGFVTCNA0
    // End copy file QA$CBLCPY$GFVTCNA0

    // Copy file QA$CBLCPY$GFVTFLA0
    private Dclgfvtfla0 dclgfvtfla0 = new  Dclgfvtfla0();                    // 01 DCLGFVTFLA0
    // End copy file QA$CBLCPY$GFVTFLA0

    // Copy file QA$CBLCPY$GFVTDMA0
    private Gfvtdma0 gfvtdma0 = new  Gfvtdma0();                             // 01 GFVTDMA0
    // End copy file QA$CBLCPY$GFVTDMA0

    // Copy file QA$CBLCPY$GFVTCMA0
    private Dclgfvtcma0 dclgfvtcma0 = new  Dclgfvtcma0();                    // 01 DCLGFVTCMA0
    // End copy file QA$CBLCPY$GFVTCMA0

    // Copy file QA$CBLCPY$GFVTPRA0
    private Dclgfvtpra0 dclgfvtpra0 = new  Dclgfvtpra0();                    // 01 DCLGFVTPRA0
    // End copy file QA$CBLCPY$GFVTPRA0

    // Copy file QA$CBLCPY$GCCTEDO0
    private Dclgcctedo0 dclgcctedo0 = new  Dclgcctedo0();                    // 01 DCLGCCTEDO0
    // End copy file QA$CBLCPY$GCCTEDO0

    // Copy file QA$CBLCPY$GCCTPOB0
    private Dclgcctpob0 dclgcctpob0 = new  Dclgcctpob0();                    // 01 DCLGCCTPOB0
    // End copy file QA$CBLCPY$GCCTPOB0

    // Copy file QA$CBLCPY$GCCTMND0
    private Dclgcctmnd0 dclgcctmnd0 = new  Dclgcctmnd0();                    // 01 DCLGCCTMND0
    // End copy file QA$CBLCPY$GCCTMND0

    // Copy file QA$CBLCPY$GCCTOFN0
    private Dclgcctofn0 dclgcctofn0 = new  Dclgcctofn0();                    // 01 DCLGCCTOFN0
    // End copy file QA$CBLCPY$GCCTOFN0

    // Copy file QA$CBLCPY$GCCTCEO0
    private Dclgcctceo0 dclgcctceo0 = new  Dclgcctceo0();                    // 01 DCLGCCTCEO0
    // End copy file QA$CBLCPY$GCCTCEO0

    // Level 1
    private WsInterfaz wsInterfaz = new  WsInterfaz();                       // 01 WS-INTERFAZ
    private WsContadores wsContadores = new  WsContadores();                 // 01 WS-CONTADORES
    private WsSwitches wsSwitches = new  WsSwitches();                       // 01 WS-SWITCHES
    private WsAyuda wsAyuda = new  WsAyuda();                                // 01 WS-AYUDA
    private WsConstantes wsConstantes = new  WsConstantes();                 // 01 WS-CONSTANTES
    private WsNulls wsNulls = new  WsNulls();                                // 01 WS-NULLS

    // Copy file QA$CBLCPY$GGQA00AH
    private WsGmmSqlca wsGmmSqlca = new  WsGmmSqlca();                       // 01 WS-GMM-SQLCA

    private Sqlca sqlca;
    // Files
    private ISequentialFile salida;



    // Declare Sql Ports
    private IGfvtcna0Port gfvtcna0Port;
    private IGfvtcma0Port gfvtcma0Port;
    private IGfvtpra0Port gfvtpra0Port;
    private IGcctedo0Port gcctedo0Port;
    private IGcctpob0Port gcctpob0Port;
    private IGfvtagc0Port gfvtagc0Port;
    private IGcctmnd0Port gcctmnd0Port;
    private IGcctofn0Port gcctofn0Port;
    private IGcctceo0Port gcctceo0Port;
    private IGfvtagt0Port gfvtagt0Port;
    private IGfvtfla0Port gfvtfla0Port;
    private IGfvtdma0Port gfvtdma0Port;

    // Declare Optional<Model>
    private Optional<GenericModel> genericModel;
    private Optional<Gfvtcna0Model> gfvtcna0Model;
    private Optional<Gfvtcma0Model> gfvtcma0Model;
    private Optional<Gfvtpra0Model> gfvtpra0Model;
    private Optional<Gcctedo0Model> gcctedo0Model;
    private Optional<Gcctpob0Model> gcctpob0Model;
    private Optional<Gfvtagc0Model> gfvtagc0Model;
    private Optional<Gcctmnd0Model> gcctmnd0Model;
    private Optional<Gcctofn0Model> gcctofn0Model;
    private Optional<Gcctceo0Model> gcctceo0Model;
    private Optional<Gfvtagt0Model> gfvtagt0Model;
    private Optional<Gfvtfla0Model> gfvtfla0Model;
    private Optional<Gfvtdma0Model> gfvtdma0Model;

    // Declare Sql Cursors
    private ICursor<Gfvtfla0Model> gfvtfla0Cursor;
    private ICursor<Gfvtdma0Model> gfvtdma0Cursor;
    private ICursor<Gcctofn0Model> gcctofn0Cursor;
    private ICursor<Gcctceo0Model> gcctceo0Cursor;

    public Ggqaat00(IOs390CblProgramContext context) {
        super(context);

        this.sqlca = getProgramContext().getSqlca();
        initFiles();
    }

    public void initFiles() {

        this.salida = createSequentialFile("SALIDA", "GNPL505O", regSal);


    }

    public void run() {
        sqlDelayedParagraph();
        p0000IniciaProceso();
    }

    /**
     *  Paragraph: SQL-DELAYED-PARAGRAPH.
     *
     * R U T I N A   P R I N C I P A L               *
     */
    void sqlDelayedParagraph() {
        if(gfvtfla0Cursor == null) {
            gfvtfla0Cursor = gfvtfla0Port.createCursorGfvtfla0(
                () -> null);
        }
        if(gfvtdma0Cursor == null) {
            gfvtdma0Cursor = gfvtdma0Port.createCursorGfvtdma0(
                () -> Gfvtdma0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                    .dmaIdr(gfvtdma0.getDmaIdr().toInt())
                .build());
        }
        if(gcctofn0Cursor == null) {
            gcctofn0Cursor = gcctofn0Port.createCursorGcctofn0(
                () -> null);
        }
        if(gcctceo0Cursor == null) {
            gcctceo0Cursor = gcctceo0Port.createCursorGcctceo0(
                () -> null);
        }
        //
        // EXIT-SQL-DELAYED-PARAGRAPH - Last paragraph
        //
    }

    /**
     *  Paragraph: 0000-INICIA-PROCESO.
     */
    void p0000IniciaProceso() {
        p7000Inicio();
        while (!(wsSwitches.isSwFinAgt())) {
            p1000ProcesaAgt();
        }
        p1160ArmaGteDummy();
        p5000LeeOfn0();
        while (!(wsSwitches.isSwFinOfn())) {
            p1200ProcesaOfn();
        }
        p5000LeeCeo0();
        while (!(wsSwitches.isSwFinCeo())) {
            p1400ProcesaCeo();
        }
        p8000Termina();
        throw new StopRunException();
    }

    /**
     *  Paragraph: 1000-PROCESA-AGT.
     *
     * LEE EL CURSOR DE AGENTES, OBTIENE INFORMACION ADICIONAL      *
     * Y PREPARA REGISTRO DE SALIDA.                                *
     */
    void p1000ProcesaAgt() {
        wsAyuda.setWsOpcion("A");

        wsSwitches.setSwAgtLuv("N");

        wsSwitches.setSwGteLuv("N");

        wsAyuda.getWsFlaNum().setValue(dclgfvtfla0.getFlaNum());
        if (wsAyuda.getWsFlaNum_1().isEqual("N")) {
            p5100ConsultaAgt0();
            if (wsSwitches.isSiAgtLuv()) {
                wsAyuda.setWsDmaIdr(1);

                wsSwitches.setWsOkDma("");

                wsSwitches.setWsFinDma("");

                // [@TODO unresolved identifier DCLGFVTDMA0]
                while (!(wsSwitches.isSwOkDma()
                    || wsSwitches.isSwFinDma())) {
                    p5100ConsultaDma0();
                }
                p5200ConsultaPob0();
                p5200ConsultaMnd0();
                p5100ConsultaAgc0();
                p5500CambiaCveEdo();
                p1050ConcatenaApellidos();
                p1060EditaTelefono();
                p5400ConsultaPra0();
                p1100ArmaSalidaAgt();
                p6000Graba();
                if (wsInterfaz.getPlhcMktLevel().isEqual("GM")) {
                    wsInterfaz.setPlhcMktLevel("WA");
                    p6000Graba();
                }
            }
        }
        p5000LeeAgt();
    }

    /**
     *  Paragraph: 1050-CONCATENA-APELLIDOS.
     *
     * ARMA LAST NAME CONCATENANDO APELLIDOS                        *
     */
    void p1050ConcatenaApellidos() {
        if (!dclgfvtagt0.getAgtApePat().isSpaces()) {
            wsSwitches.setWsNoesp("");

            wsContadores.setWcC2(0);

            wsAyuda.getWsAuxNombre().initialize();
            wsAyuda.getWsAuxAPat().setValue(dclgfvtagt0.getAgtApePat());
            wsAyuda.getWsAuxAMat().setValue(dclgfvtagt0.getAgtApeMat());
            wsContadores.setWcC1(40);
            while (!(wsSwitches.isNoesp())) {
                if (wsAyuda.getWsAuxApPat().at(wsContadores.getWcC1()).isEqual(" ")) {
                    wsContadores.setWcC2(wsContadores.getWcC2() + 1);
                } else {
                    wsSwitches.setNoesp();
                }
                wsContadores.setWcC1(wsContadores.getWcC1() + -1);
            }
            wsContadores.setWcC2(Numeric.subtract(40, wsContadores.getWcC2()).toInt());
            wsContadores.setWcC1(1);
            while (!(wsContadores.getWcC1() > wsContadores.getWcC2())) {
                wsAyuda.getWsAuxLastName().at(wsContadores.getWcC1()).setValue(wsAyuda.getWsAuxApPat().at(wsContadores.getWcC1()));
                wsContadores.setWcC1(wsContadores.getWcC1() + 1);
            }
            wsContadores.setWcC2(0);

            wsContadores.setWcC1(wsContadores.getWcC1() + 1);
            wsContadores.setWcC1(wsContadores.getWcC1());
            while (!(wsContadores.getWcC1() > 40)) {
                wsContadores.setWcC2(wsContadores.getWcC2() + 1);
                if (wsContadores.getWcC2() > 30) {
                    wsAyuda.getWsAuxLastName().at(wsContadores.getWcC1()).setSpaces();
                } else {
                    wsAyuda.getWsAuxLastName().at(wsContadores.getWcC1()).setValue(wsAyuda.getWsAuxApMat().at(wsContadores.getWcC2()));
                }
                wsContadores.setWcC1(wsContadores.getWcC1() + 1);
            }
        } else {
            wsAyuda.getWsAuxLasName().setSpaces();
        }
    }

    /**
     *  Paragraph: 1060-EDITA-TELEFONO.
     *
     * EDITA EL TELEFONO DE AGENTE-GERENTE                          *
     */
    void p1060EditaTelefono() {
        wsSwitches.setWsPhone("");

        wsAyuda.getWsAuxPhonen().setSpaces();
        p5300ConsultaCma0();
        if (wsSwitches.isSwPhone()) {
            wsContadores.setWcC4(0);

            wsSwitches.setWsTelok("");

            wsAyuda.getWsAuxTelefono().setValue(dclgfvtcma0.getCmaDes());
            wsContadores.setWcC3(1);
            while (!(wsSwitches.isTelok()
                || wsContadores.getWcC3() > 60)) {
                wsConstantes.setPlhjNumeros(wsAyuda.getWsAuxTelX().at(wsContadores.getWcC3()));
                if (wsConstantes.isPlhjNums()) {
                    wsContadores.setWcC4(wsContadores.getWcC4() + 1);
                    wsAyuda.getWsAuxPhone().at(wsContadores.getWcC4()).setValue(wsAyuda.getWsAuxTelX().at(wsContadores.getWcC3()));
                    if (wsContadores.getWcC4() > 13) {
                        wsSwitches.setTelok();
                    }
                }
                wsContadores.setWcC3(wsContadores.getWcC3() + 1);
            }
        } else {
            wsAyuda.getWsAuxPhonen().setSpaces();
        }
    }

    /**
     *  Paragraph: 1100-ARMA-SALIDA-AGT.
     *
     * ARMA REGISTRO DE SALIDA AGENTE                               *
     */
    void p1100ArmaSalidaAgt() {
        wsInterfaz.getPlhcMktDetail().initialize();
        wsAyuda.getWsChain().initialize();
        wsInterfaz.setPlhcMktSortInd("B");
        wsInterfaz.setPlhcMktEndrec("X");
        wsInterfaz.setPlhcMktCompanyD("GNPMX");
        wsAyuda.getWsAuxDate().setValue(dclgfvtagt0.getAgtFecNac());
        wsAyuda.getWsAuxDateSinGAaaa().setValue(wsAyuda.getWsAuxDateAaaa());
        wsAyuda.getWsAuxDateSinGMm().setValue(wsAyuda.getWsAuxDateMm());
        wsAyuda.getWsAuxDateSinGDd().setValue(wsAyuda.getWsAuxDateDd());
        wsInterfaz.getPlhcMktDob().setValue(wsAyuda.getWsAuxDateSinG());
        wsInterfaz.setPlhcMktCommIndex("COMM  ");
        wsInterfaz.setPlhcMktPayMethod("C");
        wsInterfaz.setPlhcMktPayFreq("12");
        wsInterfaz.getPlhcMktAgentId().setValue(wsAyuda.getWsFlaNum_2());
        wsAyuda.setWsFlaGerZonN(dclgfvtfla0.getFlaGerZon());
        wsInterfaz.setPlhcMktAgencyId(wsAyuda.getWsFlaGerZon());
        wsAyuda.setWsNombreGzNo(wsAyuda.getWsFlaGerZon());
        wsAyuda.getWsNombreFil().setSpaces();
        wsInterfaz.getPlhcMktNasd().setSpaces();
        wsInterfaz.setPlhcMktSsn(dclgfvtagt0.getAgtRfc());
        wsInterfaz.setPlhcMktHireDate(20010101);
        wsInterfaz.getPlhcMktNasdEffDate().setZero();
        wsInterfaz.getPlhcMktTermDate().setZero();
        wsInterfaz.getPlhcMktNasdRenDate().setZero();
        wsInterfaz.setPlhcMktStatus("0");
        wsInterfaz.setPlhcMktAddressOnea(gfvtdma0.getDmaCae()); // [@WARNING overflow plhcMktAddressOnea(35) <- dmaCae(40)]
        wsInterfaz.setPlhcMktAddressTwoa(gfvtdma0.getDmaCol()); // [@WARNING overflow plhcMktAddressTwoa(35) <- dmaCol(40)]
        wsInterfaz.setPlhcMktAddressThreea(dclgcctmnd0.getMndNom()); // [@WARNING overflow plhcMktAddressThreea(35) <- mndNom(50)]
        wsInterfaz.setPlhcMktAddressFoura(dclgcctpob0.getPobNom()); // [@WARNING overflow plhcMktAddressFoura(35) <- pobNom(40)]
        wsInterfaz.setPlhcMktAddressStatea(wsAyuda.getWsEdoInter());
        wsAyuda.setWsCpoCveB(gfvtdma0.getCpoCve().toInt());

        wsAyuda.setWsCpoCve(wsAyuda.getWsCpoCveB());
        wsInterfaz.setPlhcMktZipFivea(wsAyuda.getWsCpoCve_5());
        wsInterfaz.setPlhcMktPhonea(wsAyuda.getWsAuxPhonen());
        wsAyuda.setWsNombreGzk(wsConstantes.getPlhjNombreGz());
        wsInterfaz.setPlhcMktCorpName(wsAyuda.getWsNombreGz());
        wsInterfaz.setPlhcMktAddressOne(wsConstantes.getPlhjDireccionGnp1());
        wsInterfaz.setPlhcMktAddressTwo(wsConstantes.getPlhjDireccionGnp2());
        wsInterfaz.setPlhcMktAddressThree(wsConstantes.getPlhjDireccionGnp3());
        wsInterfaz.getPlhcMktAddressFour().setSpaces();
        wsInterfaz.getPlhcMktAddressState().setSpaces();
        wsInterfaz.getPlhcMktPhone().setSpaces();
        wsInterfaz.getPlhcMktZipFive().setSpaces();
        wsInterfaz.getPlhcMktPdcFive().setSpaces();
        wsInterfaz.getPlhcMktPdcFivea().setSpaces();
        wsAyuda.setWsUniqueAgentCodeN(dclgfvtfla0.getAgtIdr());
        wsInterfaz.setPlhcMktUniqueAgentCode(wsAyuda.getWsUniqueAgentCode());
        if (dclgfvtagt0.getPefCve().toInt() == 1) {
            wsInterfaz.setPlhcMktNameFirsta(dclgfvtagt0.getAgtNom()); // [@WARNING overflow plhcMktNameFirsta(20) <- agtNom(40)]
            wsInterfaz.setPlhcMktNameLasta(wsAyuda.getWsAuxLasName());
            wsInterfaz.setPlhcMktNameFirsta(dclgfvtagt0.getAgtNom()); // [@WARNING overflow plhcMktNameFirsta(20) <- agtNom(40)]
            if (wsInterfaz.getPlhcMktNameLasta().isSpaces()) {
                wsInterfaz.setPlhcMktCorpNamea(dclgfvtfla0.getFlaDes());
                wsInterfaz.getPlhcMktNameLasta().setSpaces();
                if (dclgfvtfla0.getFlaDes().isSpaces()) {
                    wsInterfaz.setPlhcMktCorpNamea(dclgfvtagt0.getAgtRazSoc());
                }
            }
            if (dclgfvtpra0.getGsxCve().isEqual("X")
                || wsInterfaz.getPlhcMktNameLasta().isSpaces()){
                wsInterfaz.setPlhcMktSex("C");
            } else if (dclgfvtpra0.getGsxCve().isEqual("F")
                || dclgfvtpra0.getGsxCve().isEqual("M")){
                wsInterfaz.setPlhcMktSex(dclgfvtpra0.getGsxCve());
            } else {
                wsInterfaz.setPlhcMktSex("M");
            }
        } else if (dclgfvtagt0.getPefCve().toInt() == 2) {
            wsInterfaz.setPlhcMktSex("C");
            wsInterfaz.setPlhcMktCorpNamea(dclgfvtagt0.getAgtRazSoc());
        } else {
            display("AGENTE SIN TIPO DE PERSONA ", wsInterfaz.getPlhcMktAgentId());
        }
        if (wsSwitches.isSiGteLuv()) {
            wsInterfaz.setPlhcMktLevel("GM");
            wsAyuda.setWsChainN(dclgfvtfla0.getOfnCve());
            wsInterfaz.setPlhcMktChain(wsAyuda.getWsChain2());
        } else {
            wsInterfaz.setPlhcMktLevel("WA");
            if (dclgfvtagc0.getFlaNumGte().isEqual("99999   ")
                || wsAyuda.getWsIndNogte().equals("*")){
                wsInterfaz.setPlhcMktChain("88888");
            } else {
                wsAyuda.getWsFlaNumGte().setValue(dclgfvtagc0.getFlaNumGte());
                wsAyuda.setWsChainN(wsAyuda.getWsFlaNumGte_2());
                wsInterfaz.setPlhcMktChain(wsAyuda.getWsChain2());
            }
        }
    }

    /**
     *  Paragraph: 1160-ARMA-GTE-DUMMY.
     *
     * ARMA REGISTRO DE SALIDA GERENTE DUMMY                        *
     */
    void p1160ArmaGteDummy() {
        wsInterfaz.getPlhcMktDetail().initialize();
        wsInterfaz.setPlhcMktSortInd("B");
        wsInterfaz.setPlhcMktEndrec("X");
        wsInterfaz.setPlhcMktCompanyD("GNPMX");
        wsInterfaz.getPlhcMktDob().setZero();
        wsInterfaz.setPlhcMktCommIndex("COMM  ");
        wsInterfaz.setPlhcMktPayMethod("C");
        wsInterfaz.setPlhcMktPayFreq("12");
        wsInterfaz.setPlhcMktAgentId("88888");
        wsInterfaz.setPlhcMktAgencyId("888");
        wsAyuda.setWsNombreGzNo("888");
        wsAyuda.getWsNombreFil().setSpaces();
        wsInterfaz.setPlhcMktNameLasta(" DUMMY AGENT ");
        wsInterfaz.setPlhcMktNameLast(" DUMMY AGENT ");
        wsInterfaz.setPlhcMktNameFirsta(" DUMMY AGENT ");
        wsInterfaz.setPlhcMktNameFirst(" DUMMY AGENT ");
        wsInterfaz.getPlhcMktCorpNamea().setSpaces();
        wsInterfaz.setPlhcMktUniqueAgentCode("88888888");
        wsInterfaz.getPlhcMktNasd().setSpaces();
        wsInterfaz.setPlhcMktSsn("DUMM991231GNP");
        wsInterfaz.setPlhcMktHireDate(20010101);
        wsInterfaz.getPlhcMktNasdEffDate().setZero();
        wsInterfaz.getPlhcMktTermDate().setZero();
        wsInterfaz.getPlhcMktNasdRenDate().setZero();
        wsInterfaz.setPlhcMktStatus("0");
        wsAyuda.setWsNombreGzk(wsConstantes.getPlhjNombreGz());
        wsInterfaz.setPlhcMktCorpName(wsAyuda.getWsNombreGz());
        wsInterfaz.setPlhcMktZipFive("04030");
        wsInterfaz.setPlhcMktZipFivea("04030");
        wsInterfaz.setPlhcMktAddressState("D.F");
        wsInterfaz.setPlhcMktAddressStatea("D.F");
        wsInterfaz.setPlhcMktAddressOne(wsConstantes.getPlhjDireccionGnp1());
        wsInterfaz.setPlhcMktAddressOnea(wsConstantes.getPlhjDireccionGnp1());
        wsInterfaz.setPlhcMktAddressTwo(wsConstantes.getPlhjDireccionGnp2());
        wsInterfaz.setPlhcMktAddressTwoa(wsConstantes.getPlhjDireccionGnp2());
        wsInterfaz.setPlhcMktAddressThree(wsConstantes.getPlhjDireccionGnp3());
        wsInterfaz.setPlhcMktAddressThreea(wsConstantes.getPlhjDireccionGnp3());
        wsInterfaz.getPlhcMktAddressFour().setSpaces();
        wsInterfaz.getPlhcMktAddressFoura().setSpaces();
        wsInterfaz.getPlhcMktPhone().setSpaces();
        wsInterfaz.getPlhcMktPhonea().setSpaces();
        wsInterfaz.getPlhcMktPdcFive().setSpaces();
        wsInterfaz.getPlhcMktPdcFivea().setSpaces();
        wsInterfaz.setPlhcMktSex("M");
        wsInterfaz.setPlhcMktLevel("GM");
        wsInterfaz.setPlhcMktChain("00020");
        p6000Graba();
    }

    /**
     *  Paragraph: 1200-PROCESA-OFN.
     *
     * LEE EL CURSOR DE OFICINA, OBTIENE INFORMACION ADICIONAL      *
     * Y PREPARA REGISTRO DE SALIDA.                                *
     */
    void p1200ProcesaOfn() {
        wsAyuda.setWsOpcion("O");

        p5200ConsultaPob0();
        p5200ConsultaMnd0();
        p5500CambiaCveEdo();
        p1300ArmaSalidaOfn();
        p6000Graba();
        p5000LeeOfn0();
    }

    /**
     *  Paragraph: 1300-ARMA-SALIDA-OFN.
     *
     * ARMA REGISTRO DE SALIDA OFICINA                              *
     */
    void p1300ArmaSalidaOfn() {
        wsInterfaz.getPlhcMktDetail().initialize();
        wsInterfaz.setPlhcMktSortInd("B");
        wsInterfaz.setPlhcMktEndrec("X");
        wsInterfaz.setPlhcMktCompanyD("GNPMX");
        wsInterfaz.getPlhcMktDob().setZero();
        wsInterfaz.getPlhcMktNasdRenDate().setZero();
        wsInterfaz.setPlhcMktCommIndex("COMM  ");
        wsInterfaz.setPlhcMktPayMethod("C");
        wsInterfaz.setPlhcMktPayFreq("12");
        wsAyuda.setWsAgentIdN(dclgcctofn0.getOfnCve());
        wsInterfaz.setPlhcMktAgentId(wsAyuda.getWsAgentId());
        wsInterfaz.setPlhcMktAgencyId(wsAyuda.getWsAgentId3());
        wsInterfaz.setPlhcMktSsn("OFIC991231GNP");
        wsInterfaz.setPlhcMktCorpName(dclgcctofn0.getOfnNom());
        wsInterfaz.setPlhcMktCorpNamea(dclgcctofn0.getOfnNom());
        wsInterfaz.getPlhcMktNameLast().setSpaces();
        wsInterfaz.getPlhcMktNameFirst().setSpaces();
        wsInterfaz.getPlhcMktNameFirsta().setSpaces();
        wsInterfaz.getPlhcMktNameLasta().setSpaces();
        wsInterfaz.getPlhcMktZipFivea().setSpaces();
        wsInterfaz.getPlhcMktPdcFive().setSpaces();
        wsInterfaz.getPlhcMktPdcFivea().setSpaces();
        wsInterfaz.getPlhcMktNasd().setSpaces();
        wsInterfaz.setPlhcMktHireDate(20010101);
        wsInterfaz.getPlhcMktTermDate().setZero();
        wsInterfaz.getPlhcMktStatus().setSpaces();
        wsInterfaz.setPlhcMktAddressOne(dclgcctofn0.getOfnCae()); // [@WARNING overflow plhcMktAddressOne(35) <- ofnCae(40)]
        wsInterfaz.setPlhcMktAddressOnea(dclgcctofn0.getOfnCae()); // [@WARNING overflow plhcMktAddressOnea(35) <- ofnCae(40)]
        wsInterfaz.setPlhcMktAddressTwo(dclgcctofn0.getOfnCol());
        wsInterfaz.setPlhcMktAddressTwoa(dclgcctofn0.getOfnCol());
        wsInterfaz.setPlhcMktAddressThree(dclgcctmnd0.getMndNom()); // [@WARNING overflow plhcMktAddressThree(35) <- mndNom(50)]
        wsInterfaz.setPlhcMktAddressThreea(dclgcctmnd0.getMndNom()); // [@WARNING overflow plhcMktAddressThreea(35) <- mndNom(50)]
        wsInterfaz.setPlhcMktAddressFour(dclgcctpob0.getPobNom()); // [@WARNING overflow plhcMktAddressFour(35) <- pobNom(40)]
        wsInterfaz.setPlhcMktAddressFoura(dclgcctpob0.getPobNom()); // [@WARNING overflow plhcMktAddressFoura(35) <- pobNom(40)]
        wsInterfaz.setPlhcMktAddressState(wsAyuda.getWsEdoInter());
        wsInterfaz.setPlhcMktAddressStatea(wsAyuda.getWsEdoInter());
        wsInterfaz.setPlhcMktSex("C");
        wsAyuda.setWsTelAux(dclgcctofn0.getOfnTelUno());
        wsInterfaz.getPlhcMktPhone().setValue(wsAyuda.getWsTelAux());
        wsInterfaz.getPlhcMktPhonea().setValue(wsAyuda.getWsTelAux());
        wsAyuda.setWsCpoCveB(dclgcctofn0.getCpoCve().toInt());

        wsAyuda.setWsCpoCve(wsAyuda.getWsCpoCveB());
        wsInterfaz.setPlhcMktZipFive(wsAyuda.getWsCpoCve_5());
        wsInterfaz.getPlhcMktNasdEffDate().setZero();
        wsInterfaz.setPlhcMktLevel("SO");
        wsAyuda.setWsChainN(dclgcctofn0.getCeoCve());
        wsInterfaz.setPlhcMktChain(wsAyuda.getWsChain2());
    }

    /**
     *  Paragraph: 1400-PROCESA-CEO.
     *
     * LEE EL CURSOR DE C.R., OBTIENE INFORMACION ADICIONAL         *
     * Y PREPARA REGISTRO DE SALIDA.                                *
     */
    void p1400ProcesaCeo() {
        wsAyuda.setWsOpcion("C");

        p1500ArmaSalidaCeo();
        p6000Graba();
        p5000LeeCeo0();
    }

    /**
     *  Paragraph: 1500-ARMA-SALIDA-CEO.
     *
     * ARMA REGISTRO DE SALIDA C.R.                                 *
     */
    void p1500ArmaSalidaCeo() {
        wsInterfaz.getPlhcMktDetail().initialize();
        wsInterfaz.setPlhcMktSortInd("B");
        wsInterfaz.setPlhcMktEndrec("X");
        wsInterfaz.setPlhcMktCompanyD("GNPMX");
        wsInterfaz.getPlhcMktDob().setZero();
        wsInterfaz.getPlhcMktNasdRenDate().setZero();
        wsInterfaz.setPlhcMktCommIndex("COMM  ");
        wsInterfaz.setPlhcMktPayMethod("C");
        wsInterfaz.setPlhcMktPayFreq("12");
        wsInterfaz.setPlhcMktSsn("CERE991231GNP");
        wsAyuda.setWsAgentIdN(dclgcctceo0.getCeoCve());
        wsInterfaz.setPlhcMktAgentId(wsAyuda.getWsAgentId());
        wsInterfaz.setPlhcMktAgencyId(wsAyuda.getWsAgentId3());
        wsInterfaz.setPlhcMktCorpName(dclgcctceo0.getCeoNom());
        wsInterfaz.setPlhcMktCorpNamea(dclgcctceo0.getCeoNom());
        wsInterfaz.getPlhcMktNameLast().setSpaces();
        wsInterfaz.getPlhcMktNameFirst().setSpaces();
        wsInterfaz.getPlhcMktNameLasta().setSpaces();
        wsInterfaz.getPlhcMktNameFirsta().setSpaces();
        wsInterfaz.getPlhcMktNasd().setSpaces();
        wsInterfaz.setPlhcMktHireDate(20010101);
        wsInterfaz.getPlhcMktTermDate().setZero();
        wsInterfaz.getPlhcMktStatus().setSpaces();
        wsInterfaz.setPlhcMktAddressOne(dclgcctceo0.getCeoCae()); // [@WARNING overflow plhcMktAddressOne(35) <- ceoCae(40)]
        wsInterfaz.setPlhcMktAddressOnea(dclgcctceo0.getCeoCae()); // [@WARNING overflow plhcMktAddressOnea(35) <- ceoCae(40)]
        wsInterfaz.setPlhcMktAddressTwo(dclgcctceo0.getCeoCol()); // [@WARNING overflow plhcMktAddressTwo(35) <- ceoCol(40)]
        wsInterfaz.setPlhcMktAddressTwoa(dclgcctceo0.getCeoCol()); // [@WARNING overflow plhcMktAddressTwoa(35) <- ceoCol(40)]
        wsInterfaz.getPlhcMktAddressThree().setSpaces();
        wsInterfaz.getPlhcMktAddressFour().setSpaces();
        wsInterfaz.getPlhcMktAddressState().setSpaces();
        wsInterfaz.getPlhcMktAddressThreea().setSpaces();
        wsInterfaz.getPlhcMktAddressFoura().setSpaces();
        wsInterfaz.getPlhcMktAddressStatea().setSpaces();
        wsInterfaz.getPlhcMktPhonea().setSpaces();
        wsInterfaz.getPlhcMktZipFivea().setSpaces();
        wsInterfaz.getPlhcMktPdcFivea().setSpaces();
        wsAyuda.setWsTelAux(dclgcctceo0.getCeoTelUno());
        wsInterfaz.getPlhcMktPhone().setValue(wsAyuda.getWsTelAux());
        wsInterfaz.getPlhcMktZipFive().setZero();
        wsInterfaz.getPlhcMktNasdEffDate().setZero();
        wsInterfaz.setPlhcMktSex("C");
        wsInterfaz.setPlhcMktLevel("RO");
        wsInterfaz.setPlhcMktChain("9999999999");
    }

    /**
     *  Paragraph: 5000-LEE-AGT.
     *
     * LEE REGISTROS DE LA TABLA DE AGENTES                         *
     */
    void p5000LeeAgt() {
        dclgfvtfla0.initialize();
        wsNulls.initialize();
        gfvtfla0Model = gfvtfla0Cursor.next();

        if (gfvtfla0Model.isPresent()) {
            dclgfvtfla0.setFlaNum(fromHostString(gfvtfla0Model.get().flaNum()));
            dclgfvtfla0.setFlaDes(fromHostString(gfvtfla0Model.get().flaDes()));
            dclgfvtfla0.setOfnCve(fromHostInt(gfvtfla0Model.get().ofnCve()));
            wsNulls.setWsNull03(isNull(gfvtfla0Model.get().ofnCve()) ? -1 : 0);
            dclgfvtfla0.setAgtIdr(fromHostInt(gfvtfla0Model.get().agtIdr()));
            dclgfvtfla0.setEmpCve(fromHostString(gfvtfla0Model.get().empCve()));
            dclgfvtfla0.setCnaNum(fromHostInt(gfvtfla0Model.get().cnaNum()));
            dclgfvtfla0.setMteCve(fromHostInt(gfvtfla0Model.get().mteCve()));
            dclgfvtfla0.setOrvCve(fromHostInt(gfvtfla0Model.get().orvCve()));
            dclgfvtfla0.setFlaGerZon(fromHostInt(gfvtfla0Model.get().flaGerZon()));
            wsNulls.setWsNull04(isNull(gfvtfla0Model.get().flaGerZon()) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            wsContadores.setWcRegistrosLeiAgt(wsContadores.getWcRegistrosLeiAgt() + 1);
        } else if (sqlca.getSqlcode() == 100) {
            wsSwitches.setSwFinAgt();
        } else {
            display("***** 5000-LEE-AGT: ERROR AL EFECTUAR FETCH.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5000-LEE-OFN0.
     *
     * EFECTUA CONSULTA A TABLA GCCTOFN0 PARA OBTENER DATOS DE OFIC.*
     */
    void p5000LeeOfn0() {
        dclgcctofn0.initialize();
        gcctofn0Model = gcctofn0Cursor.next();

        if (gcctofn0Model.isPresent()) {
            dclgcctofn0.setOfnCve(fromHostInt(gcctofn0Model.get().ofnCve()));
            dclgcctofn0.setOfnNom(fromHostString(gcctofn0Model.get().ofnNom()));
            wsNulls.setWsNull01(isNull(gcctofn0Model.get().ofnNom()) ? -1 : 0);
            dclgcctofn0.setOfnCae(fromHostString(gcctofn0Model.get().ofnCae()));
            dclgcctofn0.setOfnCol(fromHostString(gcctofn0Model.get().ofnCol()));
            dclgcctofn0.setOfnTelUno(fromHostBigDecimal(gcctofn0Model.get().ofnTelUno()));
            dclgcctofn0.setCeoCve(fromHostInt(gcctofn0Model.get().ceoCve()));
            dclgcctofn0.setPobCve(fromHostInt(gcctofn0Model.get().pobCve()));
            dclgcctofn0.setCpoCve(fromHostInt(gcctofn0Model.get().cpoCve()));
            dclgcctofn0.setMndCve(fromHostInt(gcctofn0Model.get().mndCve()));
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else if (sqlca.getSqlcode() == 100) {
            wsSwitches.setSwFinOfn();
        } else {
            display("***** 5000-LEE-OFN0: ERROR AL EFECTUAR FECTH.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5000-LEE-CEO0.
     *
     * EFECTUA CONSULTA A TABLA GCCTCEO0 PARA OBTENER DATOS DE C.R. *
     */
    void p5000LeeCeo0() {
        dclgcctceo0.initialize();
        gcctceo0Model = gcctceo0Cursor.next();

        if (gcctceo0Model.isPresent()) {
            dclgcctceo0.setCeoCve(fromHostInt(gcctceo0Model.get().ceoCve()));
            dclgcctceo0.setCeoNom(fromHostString(gcctceo0Model.get().ceoNom()));
            dclgcctceo0.setCeoFaxUno(fromHostBigDecimal(gcctceo0Model.get().ceoFaxUno()));
            wsNulls.setWsNull01(isNull(gcctceo0Model.get().ceoFaxUno()) ? -1 : 0);
            dclgcctceo0.setCeoCae(fromHostString(gcctceo0Model.get().ceoCae()));
            wsNulls.setWsNull04(isNull(gcctceo0Model.get().ceoCae()) ? -1 : 0);
            dclgcctceo0.setCeoCol(fromHostString(gcctceo0Model.get().ceoCol()));
            wsNulls.setWsNull07(isNull(gcctceo0Model.get().ceoCol()) ? -1 : 0);
            dclgcctceo0.setCeoTelUno(fromHostBigDecimal(gcctceo0Model.get().ceoTelUno()));
            wsNulls.setWsNull08(isNull(gcctceo0Model.get().ceoTelUno()) ? -1 : 0);
            dclgcctceo0.setEmpCve(fromHostString(gcctceo0Model.get().empCve()));
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else if (sqlca.getSqlcode() == 100) {
            wsSwitches.setSwFinCeo();
        } else {
            display("***** 5000-LEE-CEO0: ERROR AL EFECTUAR FETCH.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5100-CONSULTA-AGT0.
     *
     * EFECTUA CONSULTA A TABLA GFVTAGT0 PARA OBTENER NOMBRE.       *
     */
    void p5100ConsultaAgt0() {
        dclgfvtagt0.initialize();
        dclgfvtagt0.setAgtIdr(dclgfvtfla0.getAgtIdr());
        gfvtagt0Model =
            gfvtagt0Port.select_10(
                Gfvtagt0Model.builder()
                    .agtIdr(dclgfvtagt0.getAgtIdr().toInt())
                .build());

        if(gfvtagt0Model.isPresent()) {
            dclgfvtagt0.setAgtNom(fromHostString(gfvtagt0Model.get().agtNom()));
            wsNulls.setWsNull01(isNull(gfvtagt0Model.get().agtNom()) ? -1 : 0);
            dclgfvtagt0.setAgtApePat(fromHostString(gfvtagt0Model.get().agtApePat()));
            wsNulls.setWsNull02(isNull(gfvtagt0Model.get().agtApePat()) ? -1 : 0);
            dclgfvtagt0.setAgtApeMat(fromHostString(gfvtagt0Model.get().agtApeMat()));
            wsNulls.setWsNull03(isNull(gfvtagt0Model.get().agtApeMat()) ? -1 : 0);
            dclgfvtagt0.setAgtNumCed(fromHostString(gfvtagt0Model.get().agtNumCed()));
            wsNulls.setWsNull04(isNull(gfvtagt0Model.get().agtNumCed()) ? -1 : 0);
            dclgfvtagt0.setAgtFecIniCed(fromHostString(gfvtagt0Model.get().agtFecIniCed()));
            wsNulls.setWsNull05(isNull(gfvtagt0Model.get().agtFecIniCed()) ? -1 : 0);
            dclgfvtagt0.setAgtFecFinCed(fromHostString(gfvtagt0Model.get().agtFecFinCed()));
            wsNulls.setWsNull06(isNull(gfvtagt0Model.get().agtFecFinCed()) ? -1 : 0);
            dclgfvtagt0.setAgtTraEsp(fromHostString(gfvtagt0Model.get().agtTraEsp()));
            dclgfvtagt0.setMteCve(fromHostInt(gfvtagt0Model.get().mteCve()));
            dclgfvtagt0.setPefCve(fromHostInt(gfvtagt0Model.get().pefCve()));
            dclgfvtagt0.setAgtFecNac(fromHostString(gfvtagt0Model.get().agtFecNac()));
            dclgfvtagt0.setAgtRfc(fromHostString(gfvtagt0Model.get().agtRfc()));
            dclgfvtagt0.setAgtRazSoc(fromHostString(gfvtagt0Model.get().agtRazSoc()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsSwitches.setSwAgtLuv("S");

        } else if (sqlca.getSqlcode() == 100) {
            ;
        } else {
            display("***** 5100-CONSULTA-AGT0: ERROR EN SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5100-CONSULTA-DMA0.
     *
     * EFECTUA CONSULTA A TABLA GFVTDMA0 PARA OBTENER DOMICILIO     *
     */
    void p5100ConsultaDma0() {
        dclgfvtagt0.setAgtIdr(dclgfvtfla0.getAgtIdr());
        gfvtdma0.setDmaIdr(wsAyuda.getWsDmaIdr());
        p7201AbreCursorDma();
        gfvtdma0Model = gfvtdma0Cursor.next();

        if (gfvtdma0Model.isPresent()) {
            dclgfvtagt0.setAgtIdr(fromHostInt(gfvtdma0Model.get().agtIdr()));
            gfvtdma0.setDmaIdr(fromHostInt(gfvtdma0Model.get().dmaIdr()));
            gfvtdma0.setDmaCae(fromHostString(gfvtdma0Model.get().dmaCae()));
            wsNulls.setWsNull01(isNull(gfvtdma0Model.get().dmaCae()) ? -1 : 0);
            gfvtdma0.setDmaCol(fromHostString(gfvtdma0Model.get().dmaCol()));
            gfvtdma0.setTdmCve(fromHostInt(gfvtdma0Model.get().tdmCve()));
            gfvtdma0.setPobCve(fromHostInt(gfvtdma0Model.get().pobCve()));
            wsNulls.setWsNull04(isNull(gfvtdma0Model.get().pobCve()) ? -1 : 0);
            gfvtdma0.setCpoCve(fromHostInt(gfvtdma0Model.get().cpoCve()));
            gfvtdma0.setMndCve(fromHostInt(gfvtdma0Model.get().mndCve()));
            wsNulls.setWsNull05(isNull(gfvtdma0Model.get().mndCve()) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            if (gfvtdma0.getTdmCve().toInt() == 1) {
                wsSwitches.setSwOkDma();
            } else {
                wsAyuda.setWsDmaIdr(wsAyuda.getWsDmaIdr() + 1);
            }
        } else if (sqlca.getSqlcode() == 100) {
            if (wsAyuda.getWsDmaIdr() > 4) {
                wsSwitches.setSwFinDma();
            } else {
                wsAyuda.setWsDmaIdr(wsAyuda.getWsDmaIdr() + 1);
            }
        } else {
            display("* 5100-CONSULTA-DMA0: ERROR AL EFECTUAR FETCH. ");
            p9999ErrorDb2();
        }
        p8201CierraCursorDma();
    }

    /**
     *  Paragraph: 5100-CONSULTA-AGC0.
     *
     * EFECTUA CONSULTA A TABLA GFVTAGC0 PARA VALIDAR SI ES GERENTE *
     */
    void p5100ConsultaAgc0() {
        dclgfvtagc0.initialize();
        wsAyuda.setWsIndNogte("");

        dclgfvtagc0.setFlaNum(dclgfvtfla0.getFlaNum());
        gfvtagc0Model =
            gfvtagc0Port.select(
                Gfvtagc0Model.builder()
                    .flaNum(toHostString(dclgfvtfla0.getFlaNum()))
                .build());

        if(gfvtagc0Model.isPresent()) {
            dclgfvtagc0.setCnaIndGer(fromHostString(gfvtagc0Model.get().cnaIndGer()));
            wsNulls.setWsNull03(isNull(gfvtagc0Model.get().cnaIndGer()) ? -1 : 0);
            dclgfvtagc0.setFlaNumGte(fromHostString(gfvtagc0Model.get().flaNumGte()));
        }
        if (sqlca.getSqlcode() == 0) {
            if (dclgfvtagc0.getCnaIndGer().isEqual("S")) {
                wsSwitches.setSwGteLuv("S");

            }
        } else if (sqlca.getSqlcode() == 100) {
            display("***** 5100-CONSULTA-AGC0: ", "NO SE ENCONTRO CONTRATO-AGENTE: ", dclgfvtfla0.getFlaNum());
            p5100ConsultaCna0();
            wsAyuda.setWsIndNogte("*");

        } else {
            display("** 5100-CONSULTA-AGC0: ERROR AL EFECTUAR SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5100-CONSULTA-CNA0.
     *
     * EFECTUA CONSULTA A TABLA GFVTCNA0 PARA VALIDAR SI ES GERENTE *
     */
    void p5100ConsultaCna0() {
        dclgfvtcna0.initialize();
        dclgfvtcna0.setAgtIdr(dclgfvtfla0.getAgtIdr());
        dclgfvtcna0.setEmpCve(dclgfvtfla0.getEmpCve());
        dclgfvtcna0.setCnaNum(dclgfvtfla0.getCnaNum());
        gfvtcna0Model =
            gfvtcna0Port.select_6(
                Gfvtcna0Model.builder()
                    .agtIdr(dclgfvtcna0.getAgtIdr().toInt())
                    .empCve(toHostString(dclgfvtcna0.getEmpCve()))
                    .cnaNum(dclgfvtcna0.getCnaNum().toInt())
                .build());

        if(gfvtcna0Model.isPresent()) {
            dclgfvtcna0.setCnaIndGer(fromHostString(gfvtcna0Model.get().cnaIndGer()));
            wsNulls.setWsNull03(isNull(gfvtcna0Model.get().cnaIndGer()) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            if (dclgfvtcna0.getCnaIndGer().isEqual("S")) {
                wsSwitches.setSwGteLuv("S");

            }
        } else if (sqlca.getSqlcode() == 100) {
            display("***** 5100-CONSULTA-CNA0: ", "NO SE ENCONTRO CONTRATO-AGENTE: ", dclgfvtfla0.getAgtIdr());
        } else {
            display("** 5100-CONSULTA-CNA0: ERROR AL EFECTUAR SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5100-CONSULTA-EDO0.
     * EFECTUA CONSULTA A TABLA GCCTEDO0 PARA OBTENER ESTADO.       *
     */
    void p5100ConsultaEdo0() {
        dclgcctedo0.initialize();
        dclgcctedo0.setEdoCve(dclgcctmnd0.getEdoCve());
        gcctedo0Model =
            gcctedo0Port.select_3(
                Gcctedo0Model.builder()
                    .edoCve(toHostString(dclgcctedo0.getEdoCve()))
                .build());

        if(gcctedo0Model.isPresent()) {
            dclgcctedo0.setEdoNom(fromHostString(gcctedo0Model.get().edoNom()));
            wsNulls.setWsNull01(isNull(gcctedo0Model.get().edoNom()) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else if (sqlca.getSqlcode() == 100) {
            ;
        } else {
            display("** 5100-CONSULTA-EDO0: ERROR AL EFECTUAR SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5200-CONSULTA-POB0.
     *
     * EFECTUA CONSULTA A TABLA GCCTPOB0 PARA OBTENER POBLACION.    *
     */
    void p5200ConsultaPob0() {
        dclgcctpob0.initialize();
        if (wsAyuda.isWsAgteGte()) {
            dclgcctpob0.setPobCve(gfvtdma0.getPobCve());
        } else if (wsAyuda.isWsOficina()) {
            dclgcctpob0.setPobCve(dclgcctofn0.getPobCve());
        }
        gcctpob0Model =
            gcctpob0Port.select_4(
                Gcctpob0Model.builder()
                    .pobCve(dclgcctpob0.getPobCve().toInt())
                .build());

        if(gcctpob0Model.isPresent()) {
            dclgcctpob0.setPobNom(fromHostString(gcctpob0Model.get().pobNom()));
            wsNulls.setWsNull01(isNull(gcctpob0Model.get().pobNom()) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else if (sqlca.getSqlcode() == 100) {
            ;
        } else {
            display("** 5200-CONSULTA-POB0: ERROR AL EFECTUAR SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5200-CONSULTA-MND0.
     *
     * EFECTUA CONSULTA A TABLA GCCTMND0 PARA OBTENER MUNICIPIO.    *
     */
    void p5200ConsultaMnd0() {
        dclgcctmnd0.initialize();
        if (wsAyuda.isWsAgteGte()) {
            dclgcctmnd0.setMndCve(gfvtdma0.getMndCve());
        } else if (wsAyuda.isWsOficina()) {
            dclgcctmnd0.setMndCve(dclgcctofn0.getMndCve());
        }
        gcctmnd0Model =
            gcctmnd0Port.select_4(
                Gcctmnd0Model.builder()
                    .mndCve(dclgcctmnd0.getMndCve().toInt())
                .build());

        if(gcctmnd0Model.isPresent()) {
            dclgcctmnd0.setMndNom(fromHostString(gcctmnd0Model.get().mndNom()));
            wsNulls.setWsNull01(isNull(gcctmnd0Model.get().mndNom()) ? -1 : 0);
            dclgcctmnd0.setEdoCve(fromHostString(gcctmnd0Model.get().edoCve()));
            wsNulls.setWsNull02(isNull(gcctmnd0Model.get().edoCve()) ? -1 : 0);
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else if (sqlca.getSqlcode() == 100) {
            ;
        } else {
            display("** 5200-CONSULTA-MND0: ERROR AL EFECTUAR SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5300-CONSULTA-CMA0.
     *
     * EFECTUA CONSULTA A TABLA GFVTCMA0 PARA OBTENER TELEFONO.     *
     */
    void p5300ConsultaCma0() {
        dclgfvtcma0.initialize();
        dclgfvtcma0.setCcmCve(1);
        dclgfvtcma0.setDmaIdr(1);
        dclgfvtcma0.setAgtIdr(dclgfvtagt0.getAgtIdr());
        gfvtcma0Model =
            gfvtcma0Port.select_4(
                Gfvtcma0Model.builder()
                    .ccmCve(dclgfvtcma0.getCcmCve().toInt())
                    .agtIdr(dclgfvtcma0.getAgtIdr().toInt())
                    .dmaIdr(dclgfvtcma0.getDmaIdr().toInt())
                .build());

        if(gfvtcma0Model.isPresent()) {
            dclgfvtcma0.setCmaDes(fromHostString(gfvtcma0Model.get().cmaDes()));
        }
        if (sqlca.getSqlcode() == 0) {
            wsSwitches.setSwPhone();
        } else if (sqlca.getSqlcode() == 100) {
            ;
        } else {
            display("** 5300-CONSULTA-CMA0: ERROR AL EFECTUAR SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5400-CONSULTA-PRA0.
     *
     * EFECTUA CONSULTA A TABLA GFVTPRA0 PARA OBTENER GENERO SEXUAL *
     */
    void p5400ConsultaPra0() {
        dclgfvtpra0.initialize();
        dclgfvtpra0.setAgtIdr(dclgfvtagt0.getAgtIdr());
        gfvtpra0Model =
            gfvtpra0Port.select_1(
                Gfvtpra0Model.builder()
                    .agtIdr(dclgfvtpra0.getAgtIdr().toInt())
                .build());

        if(gfvtpra0Model.isPresent()) {
            dclgfvtpra0.setGsxCve(fromHostString(gfvtpra0Model.get().gsxCve()));
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else if (sqlca.getSqlcode() == 100) {
            ;
        } else {
            display("** 5400-CONSULTA-PRA0: ERROR AL EFECTUAR SELECT.");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 5500-CAMBIA-CVE-EDO.
     *
     * GRABA CLAVE DEL ESTADO
     */
    void p5500CambiaCveEdo() {
        wsConstantes.setPlhjEstadosSaga(dclgcctmnd0.getEdoCve()); // [@WARNING overflow plhjEstadosSaga(2) <- edoCve(4)]
        if (wsConstantes.isPlhjAgs()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjAgst().toStringValue());

        } else if (wsConstantes.isPlhjBcn()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjBcnt().toStringValue());

        } else if (wsConstantes.isPlhjBcs()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjBcst().toStringValue());

        } else if (wsConstantes.isPlhjCampeche()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjCampechet().toStringValue());

        } else if (wsConstantes.isPlhjCoahuila()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjCoahuilat().toStringValue());

        } else if (wsConstantes.isPlhjColima()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjColimat().toStringValue());

        } else if (wsConstantes.isPlhjChiapas()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjChiapast().toStringValue());

        } else if (wsConstantes.isPlhjChihuahua()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjChihuahuat().toStringValue());

        } else if (wsConstantes.isPlhjDf()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjDft().toStringValue());

        } else if (wsConstantes.isPlhjDurango()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjDurangot().toStringValue());

        } else if (wsConstantes.isPlhjGto()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjGtot().toStringValue());

        } else if (wsConstantes.isPlhjGuerrero()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjGuerrerot().toStringValue());

        } else if (wsConstantes.isPlhjHidalgo()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjHidalgot().toStringValue());

        } else if (wsConstantes.isPlhjJalisco()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjJaliscot().toStringValue());

        } else if (wsConstantes.isPlhjEdomex()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjEdomext().toStringValue());

        } else if (wsConstantes.isPlhjMichoacan()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjMichoacant().toStringValue());

        } else if (wsConstantes.isPlhjMorelos()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjMorelost().toStringValue());

        } else if (wsConstantes.isPlhjNayarit()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjNayaritt().toStringValue());

        } else if (wsConstantes.isPlhjNuevoleon()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjNuevoleont().toStringValue());

        } else if (wsConstantes.isPlhjOaxaca()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjOaxacat().toStringValue());

        } else if (wsConstantes.isPlhjPuebla()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjPueblat().toStringValue());

        } else if (wsConstantes.isPlhjQueretaro()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjQueretarot().toStringValue());

        } else if (wsConstantes.isPlhjQroo()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjQroot().toStringValue());

        } else if (wsConstantes.isPlhjSlp()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjSlpt().toStringValue());

        } else if (wsConstantes.isPlhjSinaloa()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjSinaloat().toStringValue());

        } else if (wsConstantes.isPlhjSonora()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjSonorat().toStringValue());

        } else if (wsConstantes.isPlhjTabasco()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjTabascot().toStringValue());

        } else if (wsConstantes.isPlhjTamaulipas()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjTamaulipast().toStringValue());

        } else if (wsConstantes.isPlhjTlaxcala()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjTlaxcalat().toStringValue());

        } else if (wsConstantes.isPlhjVeracruz()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjVeracruzt().toStringValue());

        } else if (wsConstantes.isPlhjYucatan()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjYucatant().toStringValue());

        } else if (wsConstantes.isPlhjZacatecas()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjZacatecast().toStringValue());

        } else if (wsConstantes.isPlhjExtranjero()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjExtranjerot().toStringValue());

        } else if (wsConstantes.isPlhjOtros()) {
            wsAyuda.setWsEdoInter(wsConstantes.getPlhjOtrost().toStringValue());

        }
    }

    /**
     *  Paragraph: 6000-GRABA.
     *
     * GRABA REGISTRO DE DETALLE
     */
    void p6000Graba() {
        salida.write(regSal, wsInterfaz.getPlhcMktDetail());
        wsContadores.setWcRegistrosGrab(wsContadores.getWcRegistrosGrab() + 1);
    }

    /**
     *  Paragraph: 6000-GRABA-HEAD.
     *
     * GRABA REGISTRO CABECERA
     */
    void p6000GrabaHead() {
        wsInterfaz.getPlhcMktHeader().initialize();
        wsInterfaz.setPlhcMktRecordIdh("HEADR");
        wsInterfaz.setPlhcMktCompanyId("CSC FAS");
        wsInterfaz.setPlhcMktSortIndh("A");
        wsInterfaz.getPlhcMktProcessDt().setValue(wsAyuda.getWsFechaSys());
        wsInterfaz.getPlhcMktCreationDt().setValue(wsAyuda.getWsFechaSys());
        wsInterfaz.setPlhcMktRecordCt(wsContadores.getWcRegistrosGrab()); // [@WARNING overflow plhcMktRecordCt(6,0) <- wcRegistrosGrab(9,0)]
        wsInterfaz.setPlhcMktEndrech("X");
        salida.write(regSal, wsInterfaz.getPlhcMktHeader());
    }

    /**
     *  Paragraph: 6000-GRABA-TRAIL.
     *
     * GRABA REGISTRO CONTROL
     */
    void p6000GrabaTrail() {
        wsInterfaz.getPlhcMktTrailer().initialize();
        wsInterfaz.setPlhcMktRecordIdt("TRAIL");
        wsInterfaz.setPlhcMktCompanyIdT("GNP");
        wsInterfaz.setPlhcMktSortIndt("C");
        wsInterfaz.setPlhcMktRecordCount(wsContadores.getWcRegistrosGrab()); // [@WARNING overflow plhcMktRecordCount(6,0) <- wcRegistrosGrab(9,0)]
        wsInterfaz.setPlhcMktEndrect("X");
        salida.write(regSal, wsInterfaz.getPlhcMktTrailer());
    }

    /**
     *  Paragraph: 7000-INICIO.
     *
     * ABRE ARCHIVOS DE TRABAJO Y EFECTUA PRIMERA LECTURA.          *
     */
    void p7000Inicio() {
        display("***** INICIA EL PROGRAMA GGQAAT00 *****");
        wsAyuda.getWsFechaSys().setValue(Functions.substring(Functions.currentDateString(getOs390Context()), 1, 8));
        display("LA FECHA DE HOY ES       --> ", wsAyuda.getWsFechaSys());
        p7100AbreArchivos();
        p7200AbreCursorFla();
        p7202AbreCursorOfn();
        p7203AbreCursorCeo();
        p5000LeeAgt();
        if (wsSwitches.isSwFinAgt()) {
            display("+-------------------------------------+");
            display("|   EL PROGRAMA GGQAAT00 TERMINA      |");
            display("|   PORQUE LA TABLA DE AGENTES        |");
            display("|   (GFVTFLA0) ESTA VACIA.            |");
            display("+-------------------------------------+");
        }
    }

    /**
     *  Paragraph: 7100-ABRE-ARCHIVOS.
     *
     * ABRE ARCHIVOS DE TRABAJO.                                    *
     */
    void p7100AbreArchivos() {
        salida.openOutput();
    }

    /**
     *  Paragraph: 7200-ABRE-CURSOR-FLA.
     *
     * ABRE CURSOR GFVTFLA0                                         *
     */
    void p7200AbreCursorFla() {
        gfvtfla0Cursor.open();
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 7200-ABRE-CURSOR: ERROR AL EFECTUAR OPEN FLA");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 7201-ABRE-CURSOR-DMA.
     *
     * ABRE CURSOR GFVTDMA0                                         *
     */
    void p7201AbreCursorDma() {
        gfvtdma0Cursor.open();
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 7201-ABRE-CURSOR: ERROR AL EFECTUAR OPEN DMA");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 7202-ABRE-CURSOR-OFN.
     *
     * ABRE CURSOR GFVTOFN0                                         *
     */
    void p7202AbreCursorOfn() {
        gcctofn0Cursor.open();
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 7202-ABRE-CURSOR: ERROR AL EFECTUAR OPEN OFN");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 7203-ABRE-CURSOR-CEO.
     *
     * ABRE CURSOR GFVTCEO0                                         *
     */
    void p7203AbreCursorCeo() {
        gcctceo0Cursor.open();
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 7203-ABRE-CURSOR: ERROR AL EFECTUAR OPEN CEO");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 8000-TERMINA.
     *
     * CIERRA ARCHIVO DE ENTRADA Y LLAMA A CIFRAS DE CONTROL.       *
     */
    void p8000Termina() {
        p6000GrabaHead();
        p6000GrabaTrail();
        p8100CierraArchivos();
        p8200CierraCursorFla();
        p8202CierraCursorOfn();
        p8203CierraCursorCeo();
        p9999DespliegaCifrasCont();
    }

    /**
     *  Paragraph: 8100-CIERRA-ARCHIVOS.
     *
     * CIERRA ARCHIVOS DE TRABAJO.                                  *
     */
    void p8100CierraArchivos() {
        salida.close();
    }

    /**
     *  Paragraph: 8200-CIERRA-CURSOR-FLA.
     *
     * CIERRA CURSOR FLA                                            *
     */
    void p8200CierraCursorFla() {
        if (gfvtfla0Cursor != null) {
            gfvtfla0Cursor.close();
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 8200-CIERRA-CURSOR: ERROR EN CLOSE FLA");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 8201-CIERRA-CURSOR-DMA.
     *
     * CIERRA CURSOR DMA                                            *
     */
    void p8201CierraCursorDma() {
        if (gfvtdma0Cursor != null) {
            gfvtdma0Cursor.close();
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 8201-CIERRA-CURSOR: ERROR EN CLOSE DMA");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 8202-CIERRA-CURSOR-OFN.
     *
     * CIERRA CURSOR OFN                                            *
     */
    void p8202CierraCursorOfn() {
        if (gcctofn0Cursor != null) {
            gcctofn0Cursor.close();
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 8202-CIERRA-CURSOR: ERROR EN CLOSE OFN");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 8203-CIERRA-CURSOR-CEO.
     *
     * CIERRA CURSOR CEO                                            *
     */
    void p8203CierraCursorCeo() {
        if (gcctceo0Cursor != null) {
            gcctceo0Cursor.close();
        }
        if (sqlca.getSqlcode() == 0) {
            ;
        } else {
            display("* 8203-CIERRA-CURSOR: ERROR EN CLOSE CEO");
            p9999ErrorDb2();
        }
    }

    /**
     *  Paragraph: 9999-ERROR-DB2.
     *
     * ABORTA EN CASO DE ERROR EN DB2.                              *
     */
    void p9999ErrorDb2() {
        // EXEC SQL
        //        INCLUDE GGQA00AJ
        //
        // END-EXEC.
        //  #BEGIN-COPY# QA$CBLCPY$GGQA00AJ @#REF:QA/CBLCPY/GGQA00AJ#@
        // +**************************************************************+*
        // +* NOMBRE COPY.: GNRL0007                                     *+*
        // +* OBJETIVO....: ENVIA MENSAJE DE ERROR DB2 Y TERMINA EL      *+*
        // +*               PROGRAMA CON CODIGO 16.                      *+*
        // +* SISTEMA.....: RECLAMACIONES GMA                            *+*
        // +* AUTOR.......: LIC. RAMON AGUIRRE C. (C.T.I.)               *+*
        // +* NOTAS.......: 1) SE ENVIA EL CODIGO DE RETORNO A WS-DB2RC  *+*
        // +*               2) LLAMA EL DSNTIAR PROPIO DE DB2            *+*
        // +*               3) MUESTRA EL MENSAJE DE ERROR               *+*
        // +*     ========> NO OLVIDAR DEFINIR EL COPY GNRL0008 EN WORK  *+*
        // +**************************************************************+*
        wsGmmSqlca.getWsDb2rc().setValue(sqlca.getSqlcode());
        runProgram("DSNTIAR", sqlca,
                wsGmmSqlca.getWsMsgSqlEl(),
                wsGmmSqlca.getWsMsgSqlLinLen());
        wsGmmSqlca.getWsMsgSqlElx().setSpaces();
        wsGmmSqlca.setWsMsgSqlInd(1);
        while (!(wsGmmSqlca.getWsMsgSqlInd().toInt() > 12)) {
            display(wsGmmSqlca.getWsMsgSqlLinea().at(wsGmmSqlca.getWsMsgSqlInd()));
            wsGmmSqlca.getWsMsgSqlInd().add(1);
        }
        getSqlExecutor().rollback(sqlca);
        returnCode.setValue(16);
        throw new StopRunException();
    }

    /**
     *  Paragraph: 9999-DESPLIEGA-CIFRAS-CONT.
     *
     * DESPLIEGA LAS CIFRAS DE CONTROL DEL PROGRAMA.                *
     */
    void p9999DespliegaCifrasCont() {
        display(" ");
        display("***** GGQAAT00: CIFRAS DE CONTROL *****");
        display("REGISTROS LEIDOS GFVTAGT0   --> ", wsContadores.getWcRegistrosLeiAgt());
        display("AGENTES GRABADOS            --> ", wsContadores.getWcRegistrosGrab());
    }

    @Autowired
    public void setGfvtcna0Port(IGfvtcna0Port gfvtcna0Port) {
        this.gfvtcna0Port = gfvtcna0Port;
        this.gfvtcna0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtcma0Port(IGfvtcma0Port gfvtcma0Port) {
        this.gfvtcma0Port = gfvtcma0Port;
        this.gfvtcma0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtpra0Port(IGfvtpra0Port gfvtpra0Port) {
        this.gfvtpra0Port = gfvtpra0Port;
        this.gfvtpra0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctedo0Port(IGcctedo0Port gcctedo0Port) {
        this.gcctedo0Port = gcctedo0Port;
        this.gcctedo0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctpob0Port(IGcctpob0Port gcctpob0Port) {
        this.gcctpob0Port = gcctpob0Port;
        this.gcctpob0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagc0Port(IGfvtagc0Port gfvtagc0Port) {
        this.gfvtagc0Port = gfvtagc0Port;
        this.gfvtagc0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctmnd0Port(IGcctmnd0Port gcctmnd0Port) {
        this.gcctmnd0Port = gcctmnd0Port;
        this.gcctmnd0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctofn0Port(IGcctofn0Port gcctofn0Port) {
        this.gcctofn0Port = gcctofn0Port;
        this.gcctofn0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGcctceo0Port(IGcctceo0Port gcctceo0Port) {
        this.gcctceo0Port = gcctceo0Port;
        this.gcctceo0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtagt0Port(IGfvtagt0Port gfvtagt0Port) {
        this.gfvtagt0Port = gfvtagt0Port;
        this.gfvtagt0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtfla0Port(IGfvtfla0Port gfvtfla0Port) {
        this.gfvtfla0Port = gfvtfla0Port;
        this.gfvtfla0Port.setProgramContext(getProgramContext());
    }

    @Autowired
    public void setGfvtdma0Port(IGfvtdma0Port gfvtdma0Port) {
        this.gfvtdma0Port = gfvtdma0Port;
        this.gfvtdma0Port.setProgramContext(getProgramContext());
    }
}
