package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf4100;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WK-REGISTRO.
 *
 */
@Setter
@Getter
public class WkRegistro extends DataStruct {
    // Properties
    private AlphanumericVar tablaTabAfe = new AlphanumericVar(this, 8);      // 05 TABLA-TAB-AFE X(8)
    private AlphanumericVar tablaNum = new AlphanumericVar(this, 9);         // 05 TABLA-NUM X(9)
    private AlphanumericVar tablaTipOpe = new AlphanumericVar(this, 1);      // 05 TABLA-TIP-OPE X(1)
    private AlphanumericVar tablaCodSql = new AlphanumericVar(this, 9);      // 05 TABLA-COD-SQL X(9)
    private AlphanumericVar tablaCodEdo = new AlphanumericVar(this, 5);      // 05 TABLA-COD-EDO X(5)
    private AlphanumericVar tablaCadOpe = new AlphanumericVar(this, 44);     // 05 TABLA-CAD-OPE X(44)
    private AlphanumericVar tablaAgtIncEss = new AlphanumericVar(this, 4);   // 05 TABLA-AGT-INC-ESS X(4)
    private AlphanumericVar tablaAgtIdrInc = new AlphanumericVar(this, 9);   // 05 TABLA-AGT-IDR-INC X(9)
    private AlphanumericVar tablaCdusuari = new AlphanumericVar(this, 10);   // 05 TABLA-CDUSUARI X(10)
    private AlphanumericVar tablaCdempusu = new AlphanumericVar(this, 4);    // 05 TABLA-CDEMPUSU X(4)
    private AlphanumericVar tablaNoprogra = new AlphanumericVar(this, 8);    // 05 TABLA-NOPROGRA X(8)
    private AlphanumericVar tablaTsultmod = new AlphanumericVar(this, 26);   // 05 TABLA-TSULTMOD X(26)

    public WkRegistro() {
        super();
        initialize();
    }

    public WkRegistro(DataContainer parent) {
        super(parent);
    }

    public WkRegistro(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setTablaTabAfe(final String tablaTabAfe) {
        this.tablaTabAfe.setValue(tablaTabAfe);
    }

    public void setTablaTabAfe(final IAlphanumericValue tablaTabAfe) {
        this.tablaTabAfe.setValue(tablaTabAfe);
    }

    public void setTablaNum(final String tablaNum) {
        this.tablaNum.setValue(tablaNum);
    }

    public void setTablaNum(final IAlphanumericValue tablaNum) {
        this.tablaNum.setValue(tablaNum);
    }

    public void setTablaTipOpe(final String tablaTipOpe) {
        this.tablaTipOpe.setValue(tablaTipOpe);
    }

    public void setTablaTipOpe(final IAlphanumericValue tablaTipOpe) {
        this.tablaTipOpe.setValue(tablaTipOpe);
    }

    public void setTablaCodSql(final String tablaCodSql) {
        this.tablaCodSql.setValue(tablaCodSql);
    }

    public void setTablaCodSql(final IAlphanumericValue tablaCodSql) {
        this.tablaCodSql.setValue(tablaCodSql);
    }

    public void setTablaCodEdo(final String tablaCodEdo) {
        this.tablaCodEdo.setValue(tablaCodEdo);
    }

    public void setTablaCodEdo(final IAlphanumericValue tablaCodEdo) {
        this.tablaCodEdo.setValue(tablaCodEdo);
    }

    public void setTablaCadOpe(final String tablaCadOpe) {
        this.tablaCadOpe.setValue(tablaCadOpe);
    }

    public void setTablaCadOpe(final IAlphanumericValue tablaCadOpe) {
        this.tablaCadOpe.setValue(tablaCadOpe);
    }

    public void setTablaAgtIncEss(final String tablaAgtIncEss) {
        this.tablaAgtIncEss.setValue(tablaAgtIncEss);
    }

    public void setTablaAgtIncEss(final IAlphanumericValue tablaAgtIncEss) {
        this.tablaAgtIncEss.setValue(tablaAgtIncEss);
    }

    public void setTablaAgtIdrInc(final String tablaAgtIdrInc) {
        this.tablaAgtIdrInc.setValue(tablaAgtIdrInc);
    }

    public void setTablaAgtIdrInc(final IAlphanumericValue tablaAgtIdrInc) {
        this.tablaAgtIdrInc.setValue(tablaAgtIdrInc);
    }

    public void setTablaCdusuari(final String tablaCdusuari) {
        this.tablaCdusuari.setValue(tablaCdusuari);
    }

    public void setTablaCdusuari(final IAlphanumericValue tablaCdusuari) {
        this.tablaCdusuari.setValue(tablaCdusuari);
    }

    public void setTablaCdempusu(final String tablaCdempusu) {
        this.tablaCdempusu.setValue(tablaCdempusu);
    }

    public void setTablaCdempusu(final IAlphanumericValue tablaCdempusu) {
        this.tablaCdempusu.setValue(tablaCdempusu);
    }

    public void setTablaNoprogra(final String tablaNoprogra) {
        this.tablaNoprogra.setValue(tablaNoprogra);
    }

    public void setTablaNoprogra(final IAlphanumericValue tablaNoprogra) {
        this.tablaNoprogra.setValue(tablaNoprogra);
    }

    public void setTablaTsultmod(final String tablaTsultmod) {
        this.tablaTsultmod.setValue(tablaTsultmod);
    }

    public void setTablaTsultmod(final IAlphanumericValue tablaTsultmod) {
        this.tablaTsultmod.setValue(tablaTsultmod);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.tablaTabAfe.clear();
        this.tablaNum.clear();
        this.tablaTipOpe.clear();
        this.tablaCodSql.clear();
        this.tablaCodEdo.clear();
        this.tablaCadOpe.clear();
        this.tablaAgtIncEss.clear();
        this.tablaAgtIdrInc.clear();
        this.tablaCdusuari.clear();
        this.tablaCdempusu.clear();
        this.tablaNoprogra.clear();
        this.tablaTsultmod.clear();
    }

}

