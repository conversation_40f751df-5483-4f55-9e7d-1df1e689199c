package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FOLIOXXN.
 *
 */
// 764049600*                                                                 04960002
// 765049700*                                                                 04970002
@Setter
@Getter
public class WsFolioxxn extends DataStruct {
    // Properties
    private AlphanumericVar wsFolioxx1n = new AlphanumericVar(this, 1);      // 05 WS-FOLIOXX1N X
    private UnsignedNumericVar wsFolioxx2n = new UnsignedNumericVar(this, 7, 0);             // 05 WS-FOLIOXX2N 9(7)

    public WsFolioxxn() {
        super();
        initialize();
    }

    public WsFolioxxn(DataContainer parent) {
        super(parent);
    }

    public WsFolioxxn(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFolioxx1n(final String wsFolioxx1n) {
        this.wsFolioxx1n.setValue(wsFolioxx1n);
    }

    public void setWsFolioxx1n(final IAlphanumericValue wsFolioxx1n) {
        this.wsFolioxx1n.setValue(wsFolioxx1n);
    }

    public void setWsFolioxx2n(final int wsFolioxx2n) {
        this.wsFolioxx2n.setValue(wsFolioxx2n);
    }

    public void setWsFolioxx2n(final BigDecimal wsFolioxx2n) {
        this.wsFolioxx2n.setValue(wsFolioxx2n);
    }

    public void setWsFolioxx2n(final INumericValue wsFolioxx2n) {
        this.wsFolioxx2n.setValue(wsFolioxx2n);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFolioxx1n.clear();
        this.wsFolioxx2n.clear();
    }

}

