package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-VARIABLES-NUME.
 *
 */
@Setter
@Getter
public class WsVariablesNume { // VStruct
    // Properties
    private int wsDscDsgCve;                                                // 03 WS-DSC-DSG-CVE 9(04)
    private int wsCnaMteCve;                                                // 03 WS-CNA-MTE-CVE 9(04)
    private int wsCnaTfpCve;                                                // 03 WS-CNA-TFP-CVE 9(02)
    // Level 88 - Conditional names
    private static final int EMPLEADOS = 12;

    private int wsAgtMteCve;                                                // 03 WS-AGT-MTE-CVE 9(04)
    private NumericVar wsImpIvaRtn = new NumericVar(13, 2);                  // 03 WS-IMP-IVA-RTN S9(11)V99
    private NumericVar wsImpIvaAcr = new NumericVar(13, 2);                  // 03 WS-IMP-IVA-ACR S9(11)V99
    private NumericVar wsImpIsr = new NumericVar(13, 2);                     // 03 WS-IMP-ISR S9(11)V99
    private NumericVar wsImpIsrRtn = new NumericVar(13, 2);                  // 03 WS-IMP-ISR-RTN S9(11)V99
    private NumericVar wsTasa = new NumericVar(5, 2);                        // 03 WS-TASA S9(03)V9(02)

    public WsVariablesNume() {
        initialize();
    }



    // Conditionals (88) - WS-CNA-TFP-CVE
    public boolean isEmpleados() {
        return this.wsCnaTfpCve == EMPLEADOS;
    }

    public void setEmpleados() {
        this.wsCnaTfpCve = EMPLEADOS;
    }


    public void setWsImpIvaRtn(final BigDecimal wsImpIvaRtn) {
        this.wsImpIvaRtn.setValue(wsImpIvaRtn);
    }

    public void setWsImpIvaRtn(final int wsImpIvaRtn) {
        this.wsImpIvaRtn.setValue(wsImpIvaRtn);
    }

    public void setWsImpIvaRtn(final INumericValue wsImpIvaRtn) {
        this.wsImpIvaRtn.setValue(wsImpIvaRtn);
    }

    public void setWsImpIvaAcr(final BigDecimal wsImpIvaAcr) {
        this.wsImpIvaAcr.setValue(wsImpIvaAcr);
    }

    public void setWsImpIvaAcr(final int wsImpIvaAcr) {
        this.wsImpIvaAcr.setValue(wsImpIvaAcr);
    }

    public void setWsImpIvaAcr(final INumericValue wsImpIvaAcr) {
        this.wsImpIvaAcr.setValue(wsImpIvaAcr);
    }

    public void setWsImpIsr(final BigDecimal wsImpIsr) {
        this.wsImpIsr.setValue(wsImpIsr);
    }

    public void setWsImpIsr(final int wsImpIsr) {
        this.wsImpIsr.setValue(wsImpIsr);
    }

    public void setWsImpIsr(final INumericValue wsImpIsr) {
        this.wsImpIsr.setValue(wsImpIsr);
    }

    public void setWsImpIsrRtn(final BigDecimal wsImpIsrRtn) {
        this.wsImpIsrRtn.setValue(wsImpIsrRtn);
    }

    public void setWsImpIsrRtn(final int wsImpIsrRtn) {
        this.wsImpIsrRtn.setValue(wsImpIsrRtn);
    }

    public void setWsImpIsrRtn(final INumericValue wsImpIsrRtn) {
        this.wsImpIsrRtn.setValue(wsImpIsrRtn);
    }

    public void setWsTasa(final BigDecimal wsTasa) {
        this.wsTasa.setValue(wsTasa);
    }

    public void setWsTasa(final int wsTasa) {
        this.wsTasa.setValue(wsTasa);
    }

    public void setWsTasa(final INumericValue wsTasa) {
        this.wsTasa.setValue(wsTasa);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.wsDscDsgCve = 0;
        this.wsCnaMteCve = 0;
        this.wsCnaTfpCve = 0;
        this.wsAgtMteCve = 0;
        this.wsImpIvaRtn.clear();
        this.wsImpIvaAcr.clear();
        this.wsImpIsr.clear();
        this.wsImpIsrRtn.clear();
        this.wsTasa.clear();
    }

}

