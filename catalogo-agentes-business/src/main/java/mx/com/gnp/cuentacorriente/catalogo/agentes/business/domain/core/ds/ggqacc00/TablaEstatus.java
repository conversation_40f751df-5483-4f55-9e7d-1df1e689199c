package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacc00.tablaestatus.TabEstatus;
import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: TABLA-ESTATUS.
 *
 */
@Setter
@Getter
public class TablaEstatus { // VStruct
    // Properties
    private TabEstatus tabEstatus = new  TabEstatus(100);                    // 02 TAB-ESTATUS [100]

    public TablaEstatus() {
        initialize();
    }



    public TabEstatus getTabEstatus(int index) {
        return this.tabEstatus.at(index);
    }


    // Deeper properties

    // Property: tabEstatus.tbMteCve -> 07 TB-MTE-CVE 9(04)
    public UnsignedNumericVar getTbMteCve() {
        return this.tabEstatus.getTbMteCve();
    }

    public void setTbMteCve(final int tbMteCve) {
        this.tabEstatus.setTbMteCve(tbMteCve);
    }

    public void setTbMteCve(final INumericValue tbMteCve) {
        this.tabEstatus.setTbMteCve(tbMteCve);
    }

    // Property: tabEstatus.tbMteEsaCve -> 07 TB-MTE-ESA-CVE 9(04)
    public UnsignedNumericVar getTbMteEsaCve() {
        return this.tabEstatus.getTbMteEsaCve();
    }

    public void setTbMteEsaCve(final int tbMteEsaCve) {
        this.tabEstatus.setTbMteEsaCve(tbMteEsaCve);
    }

    public void setTbMteEsaCve(final INumericValue tbMteEsaCve) {
        this.tabEstatus.setTbMteEsaCve(tbMteEsaCve);
    }


    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.tabEstatus.initialize();
    }

}

