package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggnf0700.wsindicadores;

import java.math.BigDecimal;
import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: WS-FOLIOXX.
 *
 */
// 755048700*                                                                 04870002
// 756048800*                                                                 04880002
@Setter
@Getter
public class WsFolioxx extends DataStruct {
    // Properties
    private AlphanumericVar wsFolioxx1 = new AlphanumericVar(this, 1);       // 05 WS-FOLIOXX1 X
    private UnsignedNumericVar wsFolioxx2 = new UnsignedNumericVar(this, 7, 0);              // 05 WS-FOLIOXX2 9(7)

    public WsFolioxx() {
        super();
        initialize();
    }

    public WsFolioxx(DataContainer parent) {
        super(parent);
    }

    public WsFolioxx(DataContainer parent, DataContainer redefines) {
        super(parent, redefines);
    }


    public void setWsFolioxx1(final String wsFolioxx1) {
        this.wsFolioxx1.setValue(wsFolioxx1);
    }

    public void setWsFolioxx1(final IAlphanumericValue wsFolioxx1) {
        this.wsFolioxx1.setValue(wsFolioxx1);
    }

    public void setWsFolioxx2(final int wsFolioxx2) {
        this.wsFolioxx2.setValue(wsFolioxx2);
    }

    public void setWsFolioxx2(final BigDecimal wsFolioxx2) {
        this.wsFolioxx2.setValue(wsFolioxx2);
    }

    public void setWsFolioxx2(final INumericValue wsFolioxx2) {
        this.wsFolioxx2.setValue(wsFolioxx2);
    }


    /**
     * Initializes the internal variables of this class.
     */
    @Override
    public void initialize() {
        super.initialize();
        this.wsFolioxx1.clear();
        this.wsFolioxx2.clear();
    }

}

