package mx.com.gnp.cuentacorriente.catalogo.agentes.business.domain.core.ds.ggqacb00;

import com.base100.caravel.support.common.var.*;
import com.base100.caravel.support.common.cbl.var.*;

import lombok.Getter;
import lombok.Setter;

/**
 *  Data Structure: SW-BANDERAS.
 *
 */
// 312      *---                                                              
@Setter
@Getter
public class SwBanderas { // VStruct
    // Properties
    private String filler1 = "";                                            // 02 FILLER X(01)
    // Level 88 - Conditional names
    private static final String SW_CNA0_FIN = "S";

    private int db2ReturnCode;                                              // 02 DB2-RETURN-CODE S9(9) COMPUTATIONAL
    // Level 88 - Conditional names
    private static final int DB2_OK = 0;
    private static final int DB2_NOTFND = 100;


    public SwBanderas() {
        initialize();
    }



    // Conditionals (88) - FILLER1
    public boolean isSwCna0Fin() {
        return this.filler1.equals(SW_CNA0_FIN);
    }

    public void setSwCna0Fin() {
        this.filler1 = SW_CNA0_FIN;
    }


    // Conditionals (88) - DB2-RETURN-CODE
    public boolean isDb2Ok() {
        return this.db2ReturnCode == DB2_OK;
    }

    public void setDb2Ok() {
        this.db2ReturnCode = DB2_OK;
    }

    public boolean isDb2Notfnd() {
        return this.db2ReturnCode == DB2_NOTFND;
    }

    public void setDb2Notfnd() {
        this.db2ReturnCode = DB2_NOTFND;
    }



    /**
     * Initializes the internal variables of this class.
     */
    public void initialize() {
        this.filler1 = "";
        this.db2ReturnCode = 0;
    }

}

