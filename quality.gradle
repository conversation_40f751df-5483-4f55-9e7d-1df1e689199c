apply plugin: 'jacoco'
apply plugin: 'org.sonarqube'
apply plugin: 'com.github.kt3k.coveralls'
apply plugin: 'checkstyle'

ext {

    minCoverageRates = [
            'instruction': 90,
            'branch'     : 90,
            'line'       : 90,
            'complexity' : 90,
            'method'     : 100,
            'class'      : 100
    ]

    checkstyleExclusions = []

    jacocoMinCoverageRates = [
            'instruction': 90,
            'branch'     : 90,
            'line'       : 90,
            'complexity' : 90,
            'method'     : 100,
            'class'      : 100
    ]
    jacocoExclusions = [
            'mx/com/gnp/aplicacion/Application.class',
            '**/config/**',
            '**/modulo/domain/*.class',
            '**/model/**'
    ]

    sonarExclusions = ""
    sonarCoverageExclusions = ""
}

checkstyle {
    configFile rootProject.file("checkstyle.xml")
    sourceSets = [project.sourceSets.main]
}

tasks.withType(Checkstyle).configureEach {
    reports {
        xml.required = false
        html.required = true
        html.outputLocation.set(layout.buildDirectory.file("reports/checkstyle/checkstyle.html"))


    }
    exclude checkstyleExclusions
    maxHeapSize = '2g' // Asigna 2 GB de heap a la tarea Checkstyle
}

tasks.withType(Checkstyle).each { checkstyleTask ->
    checkstyleTask.doLast {

        def htmlReport = layout.buildDirectory.file("reports/checkstyle/checkstyle.html").get().asFile

        if (htmlReport.exists() && (htmlReport.text.contains("<error ") || htmlReport.text.contains(">warning<"))) {

            String amarillo = "\u001B[1;33m";
            String negrita = "\u001B[0m";

            String mensajeResaltado = amarillo + "Hay advertencias o errores de checkstyle Para obtener mas informacion, revisa " + htmlReport + negrita;

//            throw new GradleException(mensajeResaltado)
        }
    }
}


tasks.named("build") {
    finalizedBy(jacocoTestReport)
}

jacoco {
    reportsDirectory.set(layout.buildDirectory.dir("reports/jacoco"))
}

jacocoTestReport {
    reports {
        xml.required = true
        html.required = true
        html.outputLocation.set(layout.buildDirectory.dir("reports/jacoco"))
        xml.outputLocation.set(layout.buildDirectory.file("reports/jacocoTestReport.xml"))
    }

    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: jacocoExclusions)
        }))
    }

    doLast {
        coverageVerification("build/reports/jacocoTestReport.xml", jacocoMinCoverageRates)
    }
}


coveralls {
    jacocoReportPath file("build/reports/jacocoTestReport.xml")
}

sonarqube {
    properties {
        property "sonar.buildbreaker.skip", "true"
        property "sonar.exclusions", sonarExclusions
        property "sonar.coverage.exclusions", sonarCoverageExclusions
        property "sonar.issue.ignore.multicriteria", "e1"
        property "sonar.issue.ignore.multicriteria.e1.ruleKey", "java:S105"
        property "sonar.issue.ignore.multicriteria.e1.resourceKey", "**/*.java"
    }
}

def coverageVerification(jacocoReportPath, minCoverageRates) {

    def reportXml = file(jacocoReportPath)
    def report = layout.buildDirectory.file("reports/jacoco/index.html").get().asFile

    String amarillo = "\u001B[1;33m";
    String negrita = "\u001B[0m";

    logger.lifecycle("${amarillo}Verificando resultados de cobertura xml: ${reportXml}${negrita}");
    logger.lifecycle("${amarillo}Verificando resultados de cobertura html: ${report}${negrita}");

    def parser = new XmlParser()

    parser.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
    parser.setFeature("http://apache.org/xml/features/disallow-doctype-decl", false)

    def results = parser.parse(report)

    def percentage = {

        if (it != null) {

            def covered = it.'@covered' as Double
            def missed = it.'@missed' as Double

            return ((covered / (covered + missed)) * 100).round(2)
        }

        return 100
    }

    def counters = results.counter
    def metrics = [:]

    metrics << [
            'instruction': percentage(counters.find { (it.'@type' == 'INSTRUCTION') }),
            'branch'     : percentage(counters.find { (it.'@type' == 'BRANCH') }),
            'line'       : percentage(counters.find { (it.'@type' == 'LINE') }),
            'complexity' : percentage(counters.find { (it.'@type' == 'COMPLEXITY') }),
            'method'     : percentage(counters.find { (it.'@type' == 'METHOD') }),
            'class'      : percentage(counters.find { (it.'@type' == 'CLASS') })
    ]

    def failures = []
    def successes = []

    metrics.each {
        def limit = minCoverageRates[it.key]
        def coverageRate = "- ${it.key} La tasa de cobertura es: ${it.value}%, el minimo es ${limit}%"
        if (it.value < limit) {
            failures.add(coverageRate)
        } else {
            successes.add(coverageRate)
        }
    }

    if (failures) {
        logger.quiet("------------------ La cobertura de codigo ha fallado. -----------------------")

        failures.each {
            logger.quiet(it)
        }

        logger.quiet("---------------------------------------------------------------")

        throw new GradleException("La cobertura de codigo fallo.")
    } else {

        logger.quiet("Se superaron las verificaciones de cobertura de codigo.")

        successes.each {
            logger.quiet(it)
        }
    }
}
