application:
  title: Program pgaqadbg
  description: |
    ### pgaqadbg in "Hexagonal Architecture"
  version: 1.0.0.SNAPSHOT

spring:
  main:
    web-application-type: NONE
  datasource:
    url: jdbc:db2://************:25010/GNPDB296:currentSchema=NPMPR;
    user: db2inst1
    password: gnppassword$1
context:
  sort-program-name: /opt/app/ttsortx64
os390:
  base-path: /nfs
  data-path: ${os390.base-path}/data

caravel:
  support:
    bucket:
      directory: ${os390.data-path}
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - 'PPCQS.QADBG00.EDPL506'
          - 'SYSBKW.QADBG00.RESPALDO(+1)'
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
