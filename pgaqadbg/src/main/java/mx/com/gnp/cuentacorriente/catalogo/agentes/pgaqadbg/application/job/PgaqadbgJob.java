/**
 *  JCL Program: PGAQADBG.
 *  Code generation type: KY<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pgaqadbg.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class PgaqadbgJob extends AbstractSpringBatchJobComponent {

    public PgaqadbgJob(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGAQADBG";
    }

    @Override
    public void initJob() {
        addParameter("(8600)");
        addParameter("'REPLICA-CTACORR'");
        addParameter("NOTIFY",getParameter("SYSUID"));
        addParameter("MSGLEVEL", "(1,1)");
        addParameter("MSGCLASS","J");
        addParameter("CLASS","G");
        /*//**/
        /*//*JOBPARM  P=PROC00,S=OSB*/
        addDataDefinition("JOBLIB", "SYS6.DB2.SDSNLOAD",  (joblib) -> {
            joblib.addParameter("DISP","SHR");
        });
        /*//**/
        /*//* REQUERIDA POR MIGRACION A Z12*/
        getJcllib().setOrder();
    }

    /*//**/
    @Bean("pgaqadbgQadbg00")
    public SpringBatchJobStep getQadbg00(){
        return initStep(new SpringBatchJobStep((qadbg00) -> {
            return qadbg00.executeProcedure("QADBG00");
        }));
    }
}
