application:
  title: Program pganfdbf
  description: |
    ### pganfdbf in "Hexagonal Architecture"
  version: 1.0.0.SNAPSHOT

spring:
  main:
    web-application-type: NONE
  datasource:
    url: jdbc:db2://************:25010/GNPDESA:currentSchema=NPMPR;
    user: db2inst1
    password: gnppassword$1
context:
  sort-program-name: /opt/app/ttsortx64
os390:
  base-path: /nfs
  data-path: ${os390.base-path}/data

caravel:
  support:
    bucket:
      directory: ${os390.data-path}
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - PGA.EAFVBF09
          - PGA.EAFVBF08
          - PGA.EAFVBF07
          - PGA.EAFVBF06
          - PGA.EAFVBF05
          - PGA.EAFVDM01
          - PGA.EAFVDN01
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - *.NFDBF07.SYSREC00
          - *.NFDBF05.SYSREC00
          - *.NFDBF04.SORTOUT
          - *.NFDBF06.SORTOUT
          - *.NFDBF03.SORTOUT
