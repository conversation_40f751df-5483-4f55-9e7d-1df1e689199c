/**
 *  JCL Program: PGANFDBF.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdbf.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class PganfdbfJob extends AbstractSpringBatchJobComponent {

    public PganfdbfJob(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGANFDBF";
    }

    @Override
    public void initJob() {
        addParameter("(8600)");
        addParameter("'ACT AGC-CAF DB2'");
        addParameter("CLASS","G");
        addParameter("MSGCLASS","J");
        addParameter("NOTIFY","TP5XXX");
        addParameter("MSGLEVEL", "(1,1)");
        addDataDefinition("JOBLIB",  (joblib) -> {
            /*//*JOBPARM  P=PROC00,S=OSB*/
            joblib.addDataDefinition("JOBLIB", "SYS1.CEE.SCEERUN",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.RUNLIB.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNEXIT",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
        });
        /*//**/
        /*//* REQUERIDA POR MIGRACION A Z12*/
        getJcllib().setOrder();
    }

    /*//**/
    @Bean("pganfdbfNfdbf00")
    public SpringBatchJobStep getNfdbf00(){
        return initStep(new SpringBatchJobStep((nfdbf00) -> {
            return nfdbf00.executeProcedure("NFDBF00");
        }));
    }
}
