package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdbf.infrastructure.config;

import com.base100.caravel.support.os390.springbatch.JobExecutionProcessorListener;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@AllArgsConstructor
public class JobConfig {

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager batchTransactionManager;
    private final Tasklet pganfdbfNfdbf00;

    @Bean("pganfdbf")
    public Job pganfdbf(JobExecutionProcessorListener processorListener) {
        return new JobBuilder("pganfdbf", jobRepository)
            .incrementer(new RunIdIncrementer())
            .listener(processorListener)
            .start(pganfdbfNfdbf00JobStep())
            .build();
    }

    public Step pganfdbfNfdbf00JobStep() {
        return new StepBuilder("nfdbf00", jobRepository)
            .tasklet(pganfdbfNfdbf00, batchTransactionManager)
            .build();
    }

}
