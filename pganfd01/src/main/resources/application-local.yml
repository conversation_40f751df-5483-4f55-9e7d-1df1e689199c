spring:
  datasource:
    # Configuración P6Spy para interceptar queries SQL
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    # url: *******************************************************;
    # url: ************************************************************;
    url: ****************************************************************;
    user: db2inst1
    password: db2inst1
    # password: gnppassword$1
  batch:
    jdbc:
      initialize-schema: always
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        generate_statistics: true
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 100

# Configuración de Actuator para métricas
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,httptrace,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
        jvm.gc.pause: true
        hikaricp.connections: true
    tags:
      application: pganfd01
      environment: local
context:
  sort-program-name: docker exec ttsort-container /app/ttsortx64
os390:
  base-path: /tmp/gnp/cuentacorriente
caravel:
  support:
    bucket:
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
logging:
  level:
    org:
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql:
              BasicBinder: TRACE
        stat: DEBUG
        engine:
          transaction:
            internal:
              TransactionImpl: DEBUG
        cache: DEBUG
      springframework:
        transaction: TRACE