server:
  port: 8080

spring:
  datasource:
    # Configuración P6Spy para queries completas con parámetros
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    # url: *******************************************************;
    # url: jdbc:db2://**************:50000/GNPDB296:currentSchema=NPMPR;
    url: jdbc:p6spy:db2://************:50000/GNPDB296:currentSchema=NPMPR;
    user: db2inst1
    password: db2inst1
    # password: gnppassword$1
    hikari:
      # Optimización de conexiones para procesos largos
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  batch:
    jdbc:
      initialize-schema: always
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        generate_statistics: true
        use_sql_comments: true
        show_sql: true
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 100

# Configuración de Actuator para métricas
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,httptrace,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  health:
    hazelcast:
      enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
        # Configuración asíncrona para no afectar rendimiento
        step: 10s
        descriptions: false
    distribution:
      percentiles-histogram:
        http.server.requests: true
        jvm.gc.pause: true
        hikaricp.connections: true
    tags:
      application: pganfd01
      environment: local
    # Configuración para procesos largos - métricas asíncronas
    web:
      server:
        request:
          autotime:
            enabled: false  # Deshabilitar timing automático para mejor rendimiento
context:
  sort-program-name: docker exec ttsort-container /app/ttsortx64
os390:
  base-path: /tmp/gnp/cuentacorriente
caravel:
  support:
    bucket:
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
logging:
  level:
    org:
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql:
              BasicBinder: TRACE
        stat: DEBUG
        engine:
          transaction:
            internal:
              TransactionImpl: DEBUG
        cache: DEBUG
        orm:
          jdbc:
            bind: TRACE
      springframework:
        transaction: TRACE
        batch: DEBUG
        jdbc: DEBUG
    com:
      zaxxer:
        hikari: DEBUG
    mx:
      com:
        gnp: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"