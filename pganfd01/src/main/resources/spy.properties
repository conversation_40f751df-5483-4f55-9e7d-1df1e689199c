# P6Spy Configuration for SQL Monitoring and Performance Analysis
# =======================================================================

# Driver configuration
driverlist=com.ibm.db2.jcc.DB2Driver
realdriver=com.ibm.db2.jcc.DB2Driver

# Logging configuration
logfile=pganfd01/logs/p6spy-sql.log
append=true

# Use SLF4J for better integration with Spring Boot logging
appender=com.p6spy.engine.spy.appender.Slf4JLogger

# Custom log format for CSV analysis
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat
customLogMessageFormat=%(currentTime)|%(executionTime)|%(category)|%(effectiveSql)|%(effectiveSqlSingleLine)

# Performance settings
useNanoTime=true
executionThreshold=10

# Outage detection for slow queries
outagedetection=true
outagedetectioninterval=100

# Filter configuration
filter=false
excludecategories=info,debug,result,resultset
includecategories=statement,commit,rollback

# Date format
dateformat=yyyy-MM-dd HH:mm:ss.SSS

# Performance optimizations
excludebinary=true
excluderesultset=true
stacktrace=false

# Module list
modulelist=com.p6spy.engine.spy.P6SpyFactory,com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory