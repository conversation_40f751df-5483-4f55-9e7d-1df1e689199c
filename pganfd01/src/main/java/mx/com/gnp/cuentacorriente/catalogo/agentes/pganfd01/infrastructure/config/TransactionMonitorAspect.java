package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC; // Importar MDC
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager; // Importar TransactionSynchronizationManager

@Aspect
@Component
public class TransactionMonitorAspect {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final String TRANSACTION_ID_KEY = "transactionId";

    @Async("transactionLoggingExecutor")  // Usa el executor configurado
    @Before("@annotation(org.springframework.transaction.annotation.Transactional)")
    public void logTransactionStart(JoinPoint joinPoint) {
        String transactionName = TransactionSynchronizationManager.getCurrentTransactionName();
        if (transactionName != null) {
            MDC.put(TRANSACTION_ID_KEY, transactionName); // Poner el ID de transacción en MDC
        }
        logger.debug("[TX START] >> {}.{}() [TX: {}]",
                joinPoint.getSignature().getDeclaringType().getSimpleName(),
                joinPoint.getSignature().getName(),
                transactionName);
    }

    @Async("transactionLoggingExecutor")  // Usa el executor configurado
    @After("@annotation(org.springframework.transaction.annotation.Transactional)")
    public void logTransactionEnd(JoinPoint joinPoint) {
        String transactionName = MDC.get(TRANSACTION_ID_KEY); // Obtener el ID de transacción del MDC
        logger.debug("[TX END] << {}.{}() [TX: {}]",
                joinPoint.getSignature().getDeclaringType().getSimpleName(),
                joinPoint.getSignature().getName(),
                transactionName);
        MDC.remove(TRANSACTION_ID_KEY); // Limpiar el MDC
    }
}