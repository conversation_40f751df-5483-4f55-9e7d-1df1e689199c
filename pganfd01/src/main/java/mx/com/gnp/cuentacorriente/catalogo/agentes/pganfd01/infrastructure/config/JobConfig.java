package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import com.base100.caravel.support.os390.springbatch.JobExecutionProcessorListener;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@AllArgsConstructor
public class JobConfig {

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager batchTransactionManager;
    private final Tasklet pganfd01Nfd0100;

    @Bean("pganfd01")
    public Job pganfd01(JobExecutionProcessorListener processorListener) {
        return new JobBuilder("pganfd01", jobRepository)
            .incrementer(new RunIdIncrementer())
            .listener(processorListener)
            .start(pganfd01Nfd0100JobStep())
            .build();
    }

    public Step pganfd01Nfd0100JobStep() {
        return new StepBuilder("nfd0100", jobRepository)
            .tasklet(pganfd01Nfd0100, batchTransactionManager)
            .build();
    }

}
