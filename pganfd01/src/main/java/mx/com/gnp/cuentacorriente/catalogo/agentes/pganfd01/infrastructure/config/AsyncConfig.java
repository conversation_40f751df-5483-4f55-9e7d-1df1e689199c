package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "transactionLoggingExecutor")
    public TaskExecutor transactionLoggingExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);         // Hilos mínimos
        executor.setMaxPoolSize(10);          // Hilos máximos
        executor.setQueueCapacity(100);       // Tareas en cola
        executor.setThreadNamePrefix("TX-Logger-");
        executor.initialize();
        return executor;
    }
}