package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd01.infrastructure.config;

import org.hibernate.SessionFactory;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import jakarta.persistence.EntityManagerFactory;

/**
 * Configuración de métricas para monitoreo de rendimiento JPA/Hibernate
 * Integra con Prometheus para análisis detallado de queries
 */
@Configuration
public class MetricsConfiguration {

    /**
     * Personaliza el registro de métricas para incluir tags específicos de la
     * aplicación
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> {
            registry.config()
                    .commonTags(
                            "application", "pganfd01",
                            "environment", "local",
                            "version", "1.0.0-SNAPSHOT");
        };
    }

    /**
     * Configuración de métricas de Hibernate
     * Habilita estadísticas detalladas de rendimiento
     */
    @Bean
    public HibernateMetricsConfiguration hibernateMetrics(EntityManagerFactory entityManagerFactory,
            MeterRegistry meterRegistry) {
        return new HibernateMetricsConfiguration(entityManagerFactory, meterRegistry);
    }

    /**
     * Clase interna para configurar métricas de Hibernate
     */
    public static class HibernateMetricsConfiguration {

        private final EntityManagerFactory entityManagerFactory;
        private final MeterRegistry meterRegistry;

        public HibernateMetricsConfiguration(EntityManagerFactory entityManagerFactory,
                MeterRegistry meterRegistry) {
            this.entityManagerFactory = entityManagerFactory;
            this.meterRegistry = meterRegistry;

            // Configurar métricas de Hibernate
            configureHibernateMetrics();
        }

        private void configureHibernateMetrics() {
            try {
                // Obtener SessionFactory de Hibernate
                SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);

                // Habilitar estadísticas de Hibernate
                sessionFactory.getStatistics().setStatisticsEnabled(true);

                // Registrar métricas personalizadas
                registerCustomMetrics();

            } catch (Exception e) {
                // Log error but don't fail application startup
                System.err.println("Warning: Could not configure Hibernate metrics: " + e.getMessage());
            }
        }

        private void registerCustomMetrics() {
            // Métricas de queries
            Timer.builder("hibernate.query.execution")
                    .description("Tiempo de ejecución de queries Hibernate")
                    .register(meterRegistry);

            // Métricas de cache
            meterRegistry.gauge("hibernate.cache.hit.ratio", 0.0);
            meterRegistry.gauge("hibernate.cache.miss.ratio", 0.0);

            // Métricas de conexiones
            meterRegistry.gauge("hibernate.connections.active", 0.0);
            meterRegistry.gauge("hibernate.connections.idle", 0.0);

            // Métricas de transacciones
            meterRegistry.counter("hibernate.transactions.total");
            meterRegistry.counter("hibernate.transactions.successful");
            meterRegistry.counter("hibernate.transactions.failed");
        }
    }

    /**
     * Bean para exponer métricas adicionales de la aplicación
     */
    @Bean
    public ApplicationMetrics applicationMetrics(MeterRegistry meterRegistry) {
        return new ApplicationMetrics(meterRegistry);
    }

    /**
     * Clase para métricas específicas de la aplicación
     */
    public static class ApplicationMetrics {

        private final MeterRegistry meterRegistry;
        private final Timer queryTimer;

        public ApplicationMetrics(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            this.queryTimer = Timer.builder("application.query.execution")
                    .description("Tiempo de ejecución de queries de la aplicación")
                    .register(meterRegistry);
        }

        /**
         * Registra el tiempo de ejecución de una query
         */
        public void recordQueryTime(String queryType, long executionTimeMs) {
            Timer.Sample sample = Timer.start(meterRegistry);
            sample.stop(Timer.builder("application.query.time")
                    .tag("query.type", queryType)
                    .description("Tiempo de ejecución por tipo de query")
                    .register(meterRegistry));
        }

        /**
         * Incrementa contador de queries por tipo
         */
        public void incrementQueryCounter(String queryType, String table) {
            meterRegistry.counter("application.query.count",
                    "query.type", queryType,
                    "table", table).increment();
        }

        /**
         * Registra queries lentas
         */
        public void recordSlowQuery(String queryType, String table, long executionTimeMs) {
            if (executionTimeMs > 100) { // Queries > 100ms se consideran lentas
                meterRegistry.counter("application.query.slow",
                        "query.type", queryType,
                        "table", table).increment();
            }
        }
    }
}
