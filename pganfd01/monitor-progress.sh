#!/bin/bash

# Script para monitorear el progreso de pganfd01
echo "=== Monitor de Progreso PGANFD01 ==="
echo "Presiona Ctrl+C para salir"
echo ""

# Función para mostrar métricas de Actuator
show_metrics() {
    echo "=== MÉTRICAS ACTUATOR ==="
    curl -s http://localhost:8080/actuator/metrics/hibernate.query.execution 2>/dev/null | jq '.' || echo "Actuator no disponible aún"
    echo ""
}

# Función para mostrar logs recientes
show_recent_logs() {
    echo "=== LOGS RECIENTES ==="
    if [ -f "logs/application.log" ]; then
        tail -20 logs/application.log | grep -E "(SQL|Transaction|Batch|Processing|Completed)" || echo "No hay logs SQL recientes"
    else
        echo "Archivo de log no encontrado"
    fi
    echo ""
}

# Función para mostrar estadísticas de conexiones
show_connection_stats() {
    echo "=== ESTADÍSTICAS DB ==="
    curl -s http://localhost:8080/actuator/metrics/hikaricp.connections.active 2>/dev/null | jq '.measurements[0].value' | xargs -I {} echo "Conexiones activas: {}" || echo "Métricas no disponibles"
    curl -s http://localhost:8080/actuator/metrics/hikaricp.connections.idle 2>/dev/null | jq '.measurements[0].value' | xargs -I {} echo "Conexiones idle: {}" || echo "Métricas no disponibles"
    echo ""
}

# Loop principal
while true; do
    clear
    echo "=== MONITOR PGANFD01 - $(date) ==="
    echo ""
    
    # Verificar si la aplicación está corriendo
    if curl -s http://localhost:8080/actuator/health >/dev/null 2>&1; then
        echo "✅ Aplicación CORRIENDO"
        show_metrics
        show_connection_stats
    else
        echo "⏳ Aplicación iniciando o no disponible..."
    fi
    
    show_recent_logs
    
    echo "Actualizando en 10 segundos..."
    sleep 10
done
