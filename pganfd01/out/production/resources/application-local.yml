spring:
  datasource:lo
    # url: *******************************************************;
    # url: ************************************************************;
    url: **********************************************************;
    user: db2inst1
    password: db2inst1
    # password: gnppassword$1
  batch:
    jdbc:
      initialize-schema: always
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        generate_statistics: true
context:
  sort-program-name: docker exec ttsort-container /app/ttsortx64
os390:
  base-path: /tmp/gnp/cuentacorriente
caravel:
  support:
    bucket:
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
logging:
  level:
    org:
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql:
              BasicBinder: TRACE
        stat: DEBUG
        engine:
          transaction:
            internal:
              TransactionImpl: DEBUG
        cache: DEBUG
      springframework:
        transaction: TRACE