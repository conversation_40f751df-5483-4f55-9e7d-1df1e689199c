#!/bin/bash

# Script para verificar métricas de Prometheus y P6Spy
# Autor: Optimización de Rendimiento JPA
# Fecha: $(date)

echo "🔍 VERIFICACIÓN DE MÉTRICAS - PGANFD01"
echo "====================================="

# Configuración
ACTUATOR_BASE="http://localhost:8080/actuator"
P6SPY_LOG="pganfd01/logs/p6spy-sql.log"
CSV_METRICS="pganfd01/logs/query-performance-metrics.csv"

# Función para verificar si el servicio está corriendo
check_service() {
    echo "📡 Verificando si el servicio está corriendo..."
    
    if curl -s "$ACTUATOR_BASE/health" > /dev/null 2>&1; then
        echo "✅ Servicio está corriendo en puerto 8080"
        return 0
    else
        echo "❌ Servicio no está corriendo o no responde en puerto 8080"
        echo "💡 Asegúrate de ejecutar: ./gradlew :pganfd01:bootRun -Pprofile=local"
        return 1
    fi
}

# Función para mostrar endpoints disponibles
show_endpoints() {
    echo ""
    echo "📋 ENDPOINTS DE ACTUATOR DISPONIBLES:"
    echo "   Health: $ACTUATOR_BASE/health"
    echo "   Metrics: $ACTUATOR_BASE/metrics"
    echo "   Prometheus: $ACTUATOR_BASE/prometheus"
    echo "   Info: $ACTUATOR_BASE/info"
    echo ""
}

# Función para mostrar métricas de JPA/Hibernate
show_jpa_metrics() {
    echo "🗄️  MÉTRICAS JPA/HIBERNATE:"
    echo "----------------------------------------"
    
    # Métricas de Hibernate
    echo "📊 Métricas de Hibernate:"
    curl -s "$ACTUATOR_BASE/metrics" | grep -E "(hibernate|jpa)" || echo "   No hay métricas de Hibernate disponibles"
    
    echo ""
    echo "📊 Métricas de DataSource:"
    curl -s "$ACTUATOR_BASE/metrics" | grep -E "(datasource|hikari|connection)" || echo "   No hay métricas de DataSource disponibles"
    
    echo ""
    echo "📊 Métricas de aplicación:"
    curl -s "$ACTUATOR_BASE/metrics" | grep -E "(application|query)" || echo "   No hay métricas de aplicación disponibles"
}

# Función para mostrar métricas de Prometheus
show_prometheus_metrics() {
    echo ""
    echo "📈 MÉTRICAS DE PROMETHEUS:"
    echo "----------------------------------------"
    
    echo "🔍 Buscando métricas relacionadas con queries..."
    prometheus_data=$(curl -s "$ACTUATOR_BASE/prometheus")
    
    if [ $? -eq 0 ]; then
        echo "✅ Endpoint de Prometheus disponible"
        
        # Filtrar métricas relevantes
        echo ""
        echo "📊 Métricas de JPA/Hibernate:"
        echo "$prometheus_data" | grep -E "(hibernate|jpa|query)" | head -10
        
        echo ""
        echo "📊 Métricas de DataSource:"
        echo "$prometheus_data" | grep -E "(hikaricp|datasource|connection)" | head -10
        
        echo ""
        echo "📊 Métricas de aplicación:"
        echo "$prometheus_data" | grep -E "(application|pganfd01)" | head -10
        
    else
        echo "❌ No se pudo acceder al endpoint de Prometheus"
    fi
}

# Función para verificar logs de P6Spy
check_p6spy_logs() {
    echo ""
    echo "📝 LOGS DE P6SPY:"
    echo "----------------------------------------"
    
    if [ -f "$P6SPY_LOG" ]; then
        echo "✅ Archivo de log P6Spy encontrado: $P6SPY_LOG"
        echo "📊 Tamaño: $(du -h "$P6SPY_LOG" | cut -f1)"
        echo "📈 Líneas: $(wc -l < "$P6SPY_LOG")"
        
        echo ""
        echo "🔍 Últimas 5 queries registradas:"
        tail -5 "$P6SPY_LOG" | while read line; do
            if [[ $line == *"|"* ]]; then
                IFS='|' read -ra PARTS <<< "$line"
                if [ ${#PARTS[@]} -ge 4 ]; then
                    timestamp="${PARTS[0]}"
                    execution_time="${PARTS[1]}"
                    category="${PARTS[2]}"
                    sql="${PARTS[3]:0:100}..." # Primeros 100 caracteres
                    echo "   ⏱️  ${execution_time}ms - ${category} - ${sql}"
                fi
            fi
        done
        
    else
        echo "❌ Archivo de log P6Spy no encontrado: $P6SPY_LOG"
        echo "💡 Verifica que P6Spy esté configurado correctamente"
    fi
}

# Función para verificar CSV de métricas
check_csv_metrics() {
    echo ""
    echo "📊 CSV DE MÉTRICAS:"
    echo "----------------------------------------"
    
    if [ -f "$CSV_METRICS" ]; then
        echo "✅ Archivo CSV encontrado: $CSV_METRICS"
        echo "📊 Tamaño: $(du -h "$CSV_METRICS" | cut -f1)"
        echo "📈 Registros: $(wc -l < "$CSV_METRICS")"
        
        echo ""
        echo "🔍 Últimas 5 métricas registradas:"
        tail -5 "$CSV_METRICS"
        
    else
        echo "❌ Archivo CSV no encontrado: $CSV_METRICS"
        echo "💡 El CSV se genera automáticamente durante la ejecución"
    fi
}

# Función para generar reporte de rendimiento
generate_performance_report() {
    echo ""
    echo "📋 REPORTE DE RENDIMIENTO:"
    echo "----------------------------------------"
    
    if [ -f "$P6SPY_LOG" ]; then
        echo "🐌 Top 5 queries más lentas (P6Spy):"
        grep "|" "$P6SPY_LOG" | sort -t'|' -k2 -nr | head -5 | while read line; do
            IFS='|' read -ra PARTS <<< "$line"
            if [ ${#PARTS[@]} -ge 4 ]; then
                execution_time="${PARTS[1]}"
                sql="${PARTS[3]:0:80}..."
                echo "   ⏱️  ${execution_time}ms - ${sql}"
            fi
        done
    fi
    
    if [ -f "$CSV_METRICS" ]; then
        echo ""
        echo "📊 Estadísticas del CSV:"
        if command -v awk >/dev/null 2>&1; then
            echo "   Total de queries: $(tail -n +2 "$CSV_METRICS" | wc -l)"
            echo "   Tiempo promedio: $(tail -n +2 "$CSV_METRICS" | awk -F',' '{sum+=$5; count++} END {if(count>0) printf "%.2f ms", sum/count}')"
            echo "   Query más lenta: $(tail -n +2 "$CSV_METRICS" | awk -F',' 'BEGIN{max=0} {if($5>max) max=$5} END {printf "%.2f ms", max}')"
        fi
    fi
}

# Función principal
main() {
    # Verificar si el servicio está corriendo
    if ! check_service; then
        exit 1
    fi
    
    show_endpoints
    show_jpa_metrics
    show_prometheus_metrics
    check_p6spy_logs
    check_csv_metrics
    generate_performance_report
    
    echo ""
    echo "✅ VERIFICACIÓN COMPLETADA"
    echo ""
    echo "💡 COMANDOS ÚTILES:"
    echo "   # Ver métricas en tiempo real"
    echo "   watch -n 5 'curl -s $ACTUATOR_BASE/metrics | grep -E \"(query|hibernate)\"'"
    echo ""
    echo "   # Monitorear P6Spy en tiempo real"
    echo "   tail -f $P6SPY_LOG"
    echo ""
    echo "   # Analizar CSV de métricas"
    echo "   ./pganfd01/run-performance-analysis.sh"
    echo ""
    echo "   # Acceder a Prometheus"
    echo "   curl $ACTUATOR_BASE/prometheus | grep application"
}

# Ejecutar función principal
main "$@"
