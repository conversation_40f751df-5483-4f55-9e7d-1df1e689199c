

dependencies {
    implementation project(':catalogo-agentes-business-spring-boot-starter')

    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-batch'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa' // Para JPA y Hibernate
    implementation 'org.springframework.boot:spring-boot-starter-actuator' // Para Actuator
    implementation 'org.springframework.boot:spring-boot-starter-web' // Para endpoints de Actuator

    // Micrometer y Prometheus
    implementation 'io.micrometer:micrometer-core'
    implementation 'io.micrometer:micrometer-registry-prometheus'

    // P6Spy para logging de SQL
    // implementation 'com.p6spy:p6spy:3.9.0'

    // DataSource Proxy para métricas adicionales
    // implementation 'net.ttddyy:datasource-proxy:1.9.0'

    // Hibernate para métricas JPA
    // implementation 'org.hibernate.orm:hibernate-micrometer'

    // Otras dependencias de tu proyecto
    implementation "mx.com.gnp.cuentacorriente:batch-spring-boot-starter:${gnpCuentaCorrienteCommonVersion}"

/*
    implementation fileTree(dir: '../caravel-support', include: '*.jar')

 */
    implementation("com.base100.caravel.support:os390-spring-batch:6.0.0-SNAPSHOT")
}

bootJar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
bootRun {
    // Asigna 1GB de memoria inicial y hasta 4GB de memoria máxima
    jvmArgs = ["-Xms1g", "-Xmx24g"]
}