branches:
  main:
    tag: ''  
    increment: Inherit
    source-branches:
      - major
      - feature
      - minor
    prevent-increment-of-merged-branch-version: true  

  major:
    regex: ^[Mm]ajor.*
    tag: 'rc.{BranchName}'
    increment: Major
    source-branches:
      - main
    prevent-increment-of-merged-branch-version: true

  feature:
    regex: ^[Ff]eature.*
    tag: 'alpha.{BranchName}'
    increment: Minor
    source-branches:
      - main
    prevent-increment-of-merged-branch-version: true

  minor:
    regex: ^[Mm]inor.*
    tag: 'beta.{BranchName}'
    increment: Patch
    source-branches:
      - main
    prevent-increment-of-merged-branch-version: true

ignore:
  sha: [ ]
