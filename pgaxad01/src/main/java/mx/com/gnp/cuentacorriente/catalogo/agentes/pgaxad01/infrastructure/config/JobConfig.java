package mx.com.gnp.cuentacorriente.catalogo.agentes.pgaxad01.infrastructure.config;

import com.base100.caravel.support.os390.springbatch.JobExecutionProcessorListener;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@AllArgsConstructor
public class JobConfig {

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager batchTransactionManager;
    private final Tasklet pgaxad01Xad0100;

    @Bean("pgaxad01")
    public Job pgaxad01(JobExecutionProcessorListener processorListener) {
        return new JobBuilder("pgaxad01", jobRepository)
            .incrementer(new RunIdIncrementer())
            .listener(processorListener)
            .start(pgaxad01Xad0100JobStep())
            .build();
    }

    public Step pgaxad01Xad0100JobStep() {
        return new StepBuilder("xad0100", jobRepository)
            .tasklet(pgaxad01Xad0100, batchTransactionManager)
            .build();
    }

}
