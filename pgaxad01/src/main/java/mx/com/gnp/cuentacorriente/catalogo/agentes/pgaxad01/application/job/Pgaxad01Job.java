/**
 *  JCL Program: PGAXAD01.
 *  Code generation type: <PERSON>Y<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pgaxad01.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class Pgaxad01Job extends AbstractSpringBatchJobComponent {

    public Pgaxad01Job(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGAXAD01";
    }

    @Override
    public void initJob() {
        addParameter("(8600)");
        addParameter("'EXTRAE DB2 CAT AGTS '");
        addParameter("CLASS","G");
        addParameter("MSGCLASS","X");
        addParameter("NOTIFY","TP1JRV");
        addParameter("MSGLEVEL", "(1,1)");
        addDataDefinition("JOBLIB",  (joblib) -> {
            /*//*JOBPARM P=PROC00,S=OSB*/
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS1.CEE.SCEELKED",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS1.CEE.SCEERUN",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.RUNLIB.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNEXIT",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
        });
        /*//**/
        /*//* REQUERIDA POR MIGRACION A Z12*/
        getJcllib().setOrder();
    }

    /*//**/
    @Bean("pgaxad01Xad0100")
    public SpringBatchJobStep getXad0100(){
        return initStep(new SpringBatchJobStep((xad0100) -> {
            return xad0100.executeProcedure("XAD0100");
        }));
    }
}
