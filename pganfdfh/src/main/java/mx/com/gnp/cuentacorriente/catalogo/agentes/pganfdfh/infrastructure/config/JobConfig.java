package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdfh.infrastructure.config;

import com.base100.caravel.support.os390.springbatch.JobExecutionProcessorListener;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@AllArgsConstructor
public class JobConfig {

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager batchTransactionManager;
    private final Tasklet pganfdfhNfdfh00;

    @Bean("pganfdfh")
    public Job pganfdfh(JobExecutionProcessorListener processorListener) {
        return new JobBuilder("pganfdfh", jobRepository)
            .incrementer(new RunIdIncrementer())
            .listener(processorListener)
            .start(pganfdfhNfdfh00JobStep())
            .build();
    }

    public Step pganfdfhNfdfh00JobStep() {
        return new StepBuilder("nfdfh00", jobRepository)
            .tasklet(pganfdfhNfdfh00, batchTransactionManager)
            .build();
    }

}
