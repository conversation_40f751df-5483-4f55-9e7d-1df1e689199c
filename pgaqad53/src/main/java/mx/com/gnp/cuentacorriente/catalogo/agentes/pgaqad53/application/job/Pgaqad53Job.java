/**
 *  JCL Program: PGAQAD53.
 *  Code generation type: <PERSON>Y<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pgaqad53.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class Pgaqad53Job extends AbstractSpringBatchJobComponent {

    public Pgaqad53Job(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGAQAD53";
    }

    @Override
    public void initJob() {
        addParameter("(8600)");
        addParameter("'VER_AGTS'");
        addParameter("NOTIFY",getParameter("SYSUID"));
        addParameter("MSGLEVEL", "(1,1)");
        addParameter("MSGCLASS","X");
        addParameter("CLASS","G");
        addDataDefinition("JOBLIB",  (joblib) -> {
            /*//*JOBPARM  P=PROC00,S=OSB*/
            /*//*****************************************************/
            /*//*****************************************************/
            joblib.addDataDefinition("JOBLIB", "SYS1.CEE.SCEERUN",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.RUNLIB.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNEXIT",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
        });
        /*//**/
        /*//* REQUERIDA POR MIGRACION A Z12*/
        getJcllib().setOrder();
    }

    /*//*EXEC CA11RMS,TYPRUN='F'*/
    /*//**/
    @Bean("pgaqad53Qad5300")
    public SpringBatchJobStep getQad5300(){
        return initStep(new SpringBatchJobStep((qad5300) -> {
            return qad5300.executeProcedure("QAD5300");
        }));
    }
}
