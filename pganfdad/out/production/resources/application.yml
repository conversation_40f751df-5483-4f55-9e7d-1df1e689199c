application:
  title: Program pganfdad
  description: |
    ### pganfdad in "Hexagonal Architecture"
  version: 1.0.0.SNAPSHOT

spring:
  main:
    web-application-type: NONE
  datasource:
    url: jdbc:db2://************:25010/GNPDB296:currentSchema=NPMPR;
    user: db2inst1
    password: gnppassword$1
context:
  sort-program-name: /opt/app/ttsortx64
os390:
  base-path: /nfs
  data-path: ${os390.base-path}/data

caravel:
  support:
    bucket:
      directory: ${os390.data-path}
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - 'PGAV.NFDAD00.EAFVADV2'
          - 'PGA.NFDAD00.OUTREC.SORT'
          - 'PGA.NFDAD00.EAFVAB04.OUTREC.S'
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
