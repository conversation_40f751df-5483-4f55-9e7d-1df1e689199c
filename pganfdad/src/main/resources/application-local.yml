spring:
  datasource:
    url: *******************************************************;
    user: db2inst1
    password: gnppassword$1
  batch:
    jdbc:
      initialize-schema: always
context:
  sort-program-name: C:/tmp/gnp/ttsort/ttsort.exe
os390:
  base-path: C:/tmp/gnp/cuentacorriente
caravel:
  support:
    bucket:
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-dev"
