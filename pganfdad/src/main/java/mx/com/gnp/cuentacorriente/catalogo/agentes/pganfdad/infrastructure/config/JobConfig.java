package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdad.infrastructure.config;

import com.base100.caravel.support.os390.springbatch.JobExecutionProcessorListener;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@AllArgsConstructor
public class JobConfig {

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager batchTransactionManager;
    private final Tasklet pganfdadNfdad00;

    @Bean("pganfdad")
    public Job pganfdad(JobExecutionProcessorListener processorListener) {
        return new JobBuilder("pganfdad", jobRepository)
            .incrementer(new RunIdIncrementer())
            .listener(processorListener)
            .start(pganfdadNfdad00JobStep())
            .build();
    }

    public Step pganfdadNfdad00JobStep() {
        return new StepBuilder("nfdad00", jobRepository)
            .tasklet(pganfdadNfdad00, batchTransactionManager)
            .build();
    }

}
