/**
 *  JCL Program: PGANFDAD.
 *  Code generation type: KYNDRYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdad.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class PganfdadJob extends AbstractSpringBatchJobComponent {

    public PganfdadJob(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGANFDAD";
    }

    @Override
    public void initJob() {
        addParameter("(8600)");
        addParameter("'CREA VSAM DESACO'");
        addParameter("CLASS","G");
        addParameter("MSGCLASS","J");
        addParameter("NOTIFY","TP5XXX");
        addParameter("MSGLEVEL", "(1,1)");
        addParameter("REGION","0M");
        addDataDefinition("JOBLIB",  (joblib) -> {
            /*//**/
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS1.CEE.SCEERUN",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.RUNLIB.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNEXIT",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
        });
        /*//**/
        getJcllib().setOrder();
    }

    /*//**/
    @Bean("pganfdadNfdad00")
    public SpringBatchJobStep getNfdad00(){
        return initStep(new SpringBatchJobStep((nfdad00) -> {
            return nfdad00.executeProcedure("NFDAD00");
        }));
    }
}
