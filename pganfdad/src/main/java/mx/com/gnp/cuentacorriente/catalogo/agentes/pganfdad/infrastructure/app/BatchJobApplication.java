package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdad.infrastructure.app;

import mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdad.infrastructure.config.JobConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

/** The type Application. */
@SpringBootApplication
@Import(JobConfig.class)
public class BatchJobApplication {
  public static void main(final String[] args) {
    System.exit(SpringApplication.exit(SpringApplication.run(BatchJobApplication.class, args)));
  }
}
