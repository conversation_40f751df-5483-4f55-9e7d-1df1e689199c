Compilado para funcionar en docker

Nota. Al estar compilado en estático, se ha comprobado empíricamente funciona correctamente en:
- Docker
- Debian 12
- Red Hat Enterprise Linux 8.8
- Red Hat Enterprise Linux 8.10
- WSL (Subsistema de Windows para Linux)
 
 La versión 0.100 corrige:
 - En el parámetro -includeccsid ahora admitimos 1047
 - Ahora -includeccsid no es solo para INCLUDE. También para OUTREC, OUTFIL y OVERLAY, ya que no estaba convirtiendo las constantes a EBCDIC, escribía en ASCII.
 
 