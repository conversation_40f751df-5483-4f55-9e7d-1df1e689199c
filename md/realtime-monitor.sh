#!/bin/bash

# Monitor en TIEMPO REAL para miles de queries
echo "🔥 MONITOR TIEMPO REAL - PGANFD01"
echo "Presiona Ctrl+C para salir"
echo ""

# Detectar directorio
if [ -f "pganfd01/logs/application.log" ]; then
    LOG_PATH="pganfd01/logs/application.log"
elif [ -f "logs/application.log" ]; then
    LOG_PATH="logs/application.log"
else
    echo "❌ No se encuentra el log. Ejecutar desde raíz del proyecto o desde pganfd01/"
    exit 1
fi

echo "📁 Monitoreando: $LOG_PATH"
echo "🔗 Prometheus: http://localhost:8080/actuator/prometheus"
echo "📊 Métricas: http://localhost:8080/actuator/metrics"
echo ""
echo "=== LOGS SQL EN TIEMPO REAL ==="

# TIEMPO REAL - Sin delays, stream continuo
tail -f "$LOG_PATH" | while read line; do
    # Filtrar solo lo importante
    if echo "$line" | grep -qE "(Hibernate:|SQL|select|insert|update|delete|Transaction|took|ms|Processing|Completed|ERROR|WARN)"; then
        # Extraer timestamp
        timestamp=$(echo "$line" | cut -d' ' -f1-2)
        
        # Colorear según tipo
        if echo "$line" | grep -qE "(select|SELECT)"; then
            echo "🔍 [$timestamp] SELECT: $(echo "$line" | grep -oE "select.*" | head -c 100)..."
        elif echo "$line" | grep -qE "(insert|INSERT)"; then
            echo "➕ [$timestamp] INSERT: $(echo "$line" | grep -oE "insert.*" | head -c 100)..."
        elif echo "$line" | grep -qE "(update|UPDATE)"; then
            echo "✏️  [$timestamp] UPDATE: $(echo "$line" | grep -oE "update.*" | head -c 100)..."
        elif echo "$line" | grep -qE "(delete|DELETE)"; then
            echo "🗑️  [$timestamp] DELETE: $(echo "$line" | grep -oE "delete.*" | head -c 100)..."
        elif echo "$line" | grep -qE "(Transaction|TRANSACTION)"; then
            echo "💾 [$timestamp] TRANSACTION: $(echo "$line" | grep -oE "Transaction.*" | head -c 80)..."
        elif echo "$line" | grep -qE "(took|ms)"; then
            echo "⏱️  [$timestamp] TIMING: $(echo "$line" | grep -oE "took.*ms" | head -c 50)"
        elif echo "$line" | grep -qE "(Processing|Completed)"; then
            echo "🔄 [$timestamp] BATCH: $(echo "$line" | grep -oE "(Processing|Completed).*" | head -c 80)..."
        elif echo "$line" | grep -qE "(ERROR|WARN)"; then
            echo "⚠️  [$timestamp] ALERT: $(echo "$line" | head -c 120)..."
        else
            echo "📝 [$timestamp] $(echo "$line" | head -c 100)..."
        fi
    fi
done
