# Plan de Optimización para Base de Datos DB2 - Proceso GGNF0100

## Introducción

Este documento detalla las recomendaciones de optimización para la base de datos DB2 que soporta el proceso batch `GGNF0100`.

El análisis del código Java (`Ggnf0100.java`) ha revelado que la principal causa de lentitud es la ejecución repetitiva de consultas dentro de bucles anidados. Si bien ya se han implementado optimizaciones significativas a nivel de aplicación mediante el uso de **caché inteligente**, es crucial asegurar que las consultas subyacentes que se envían a DB2 sean lo más eficientes posible.

Este plan se enfoca en la creación de **índices estratégicos** y en las **mejores prácticas de mantenimiento de DB2** para complementar las mejoras del código y maximizar el rendimiento del proceso.

---

## 1. Análisis de Consultas Clave

Las siguientes son las consultas fundamentales identificadas en el flujo del programa `GGNF0100.java`, que son ejecutadas de forma recurrente:

*   **Lectura de Agentes con Contratos de Tipo Específico y Conectados Recientemente (Tabla GFVTAGT0 y GFVTCNA0)**:
    Esta es una consulta más compleja que podría ser utilizada en un reporte real, buscando agentes que cumplen con criterios específicos en sus contratos.
    ```sql
    SELECT
        A.AGT_IDR,
        A.AGT_RFC,
        A.AGT_NOM,
        A.AGT_APE_PAT,
        A.AGT_APE_MAT
    FROM
        NPMPR.GFVTAGT0 A
    WHERE
        EXISTS (
            SELECT 1
            FROM NPMPR.GFVTCNA0 C
            WHERE C.AGT_IDR = A.AGT_IDR
              AND C.TFP_CVE = 7             -- Ejemplo: Tipo de Contrato 'Gerente'
              AND C.CNA_FEC_CNX > '2024-01-01' -- Ejemplo: Conectado después de esta fecha
        )
    ORDER BY
        A.AGT_IDR;
    ```
*   **Lectura de Contratos (Tabla GFVTCNA0)**:
    ```sql
    SELECT * FROM GFVTCNA0 WHERE AGT_IDR = ?;
    ```
*   **Lectura de Folios (Tabla GFVTFLA0)**:
    ```sql
    SELECT * FROM GFVTFLA0 WHERE AGT_IDR = ? AND CNA_NUM = ?;
    ```
*   **Lectura de Derechos del Contrato (Tabla GFVTDRC0)**:
    ```sql
    SELECT * FROM GFVTDRC0 WHERE AGT_IDR = ? AND CNA_NUM = ?;
    ```
*   **Lectura de Datos Personales (Tabla GFVTPRA0)**:
    ```sql
    SELECT * FROM GFVTPRA0 WHERE AGT_IDR = ?;
    ```
*   **Lectura de Domicilios (Tabla GFVTDMA0)**:
    ```sql
    SELECT * FROM GFVTDMA0 WHERE AGT_IDR = ? AND TDM_CVE = ?;
    ```

---

## 2. Recomendación de Índices para DB2

Para acelerar las consultas anteriores, se recomienda crear los siguientes índices. Si ya existen, se debe verificar que las columnas y el orden coincidan.

**Nota**: El esquema correcto es `NPMPR`.

```sql
-- 1. Índice para GFVTAGT0: Acelera la búsqueda y ordenamiento de agentes.
CREATE INDEX NPMPR.IDX_GFVTAGT0_AGTIDR ON NPMPR.GFVTAGT0 (AGT_IDR);

-- 2. Índice para GFVTCNA0: Acelera la búsqueda de contratos por agente.
CREATE INDEX NPMPR.IDX_GFVTCNA0_AGTIDR ON NPMPR.GFVTCNA0 (AGT_IDR);

-- 3. Índice Compuesto para GFVTFLA0: Clave para la búsqueda de folios por agente y contrato.
CREATE INDEX NPMPR.IDX_GFVTFLA0_AGT_CNA ON NPMPR.GFVTFLA0 (AGT_IDR, CNA_NUM);

-- 4. Índice Compuesto para GFVTDRC0: Esencial para la consulta de derechos, optimizada con caché.
CREATE INDEX NPMPR.IDX_GFVTDRC0_AGT_CNA ON NPMPR.GFVTDRC0 (AGT_IDR, CNA_NUM);

-- 5. Índice para GFVTPRA0: Para obtener rápidamente los datos personales del agente.
CREATE INDEX NPMPR.IDX_GFVTPRA0_AGTIDR ON NPMPR.GFVTPRA0 (AGT_IDR);

-- 6. Índice Compuesto para GFVTDMA0: Crítico para la búsqueda de domicilios por agente y tipo.
CREATE INDEX NPMPR.IDX_GFVTDMA0_AGT_TDM ON NPMPR.GFVTDMA0 (AGT_IDR, TDM_CVE);

-- 7. Índice para GFVTAFO0: Para la búsqueda de jefes en la tabla de afiliados.
CREATE INDEX NPMPR.IDX_GFVTAFO0_FLANUM ON NPMPR.GFVTAFO0 (FLA_NUM);

-- 8. Índice para GCCTORV0: Para la búsqueda por clave de organización de ventas.
CREATE INDEX NPMPR.IDX_GCCTORV0_ORVCVE ON NPMPR.GCCTORV0 (ORV_CVE);

-- 9. Índice para GFVTCMA0: Para la búsqueda de medios de contacto.
CREATE INDEX NPMPR.IDX_GFVTCMA0_AGT_DMA ON NPMPR.GFVTCMA0 (AGT_IDR, DMA_IDR);

-- Nuevo Índice para GFVTCNA0: Crucial para la consulta de agentes con contratos específicos.
CREATE INDEX NPMPR.IDX_GFVTCNA0_TIPO_FECHA ON NPMPR.GFVTCNA0 (AGT_IDR, TFP_CVE, CNA_FEC_CNX);
```

---

## 3. Verificación de Índices con `EXPLAIN PLAN`

Para verificar que DB2 utilizará los índices propuestos, se utiliza el comando `EXPLAIN PLAN`.

### Nota sobre el error `SQLCODE=-605`

Si al ejecutar `CREATE INDEX` recibe el error `SQLCODE=-605` (o `SQLCODE=605`), significa que un índice con una definición compatible **ya existe** para esa tabla. Esto es una buena noticia, ya que indica que la tabla ya está optimizada para esa consulta. Simplemente ignore el error y continúe con la creación de los demás índices.

### Cómo Ejecutar y Revisar Múltiples Planes de Acceso

Cada comando `EXPLAIN PLAN` por defecto sobrescribe los resultados del anterior. Para analizar varias consultas a la vez, debe asignar un número de consulta (`QUERYNO`) único a cada una.

**Paso 1: Ejecutar `EXPLAIN PLAN` para cada consulta**

Ejecute el siguiente bloque de sentencias completo. Cada `EXPLAIN` tiene un `QUERYNO` diferente.

**Nota**: Reemplace los valores de ejemplo (`12345`, `678`, etc.) con datos realistas de su entorno de pruebas.

```sql
EXPLAIN PLAN SET QUERYNO = 1 FOR SELECT * FROM NPMPR.GFVTCNA0 WHERE AGT_IDR = 12345;

EXPLAIN PLAN SET QUERYNO = 2 FOR SELECT * FROM NPMPR.GFVTFLA0 WHERE AGT_IDR = 12345 AND CNA_NUM = 678;

EXPLAIN PLAN SET QUERYNO = 3 FOR SELECT * FROM NPMPR.GFVTDRC0 WHERE AGT_IDR = 12345 AND CNA_NUM = 678;

EXPLAIN PLAN SET QUERYNO = 4 FOR SELECT * FROM NPMPR.GFVTDMA0 WHERE AGT_IDR = 12345 AND TDM_CVE = 1;

EXPLAIN PLAN SET QUERYNO = 5 FOR
SELECT
    A.AGT_IDR,
    A.AGT_RFC,
    A.AGT_NOM,
    A.AGT_APE_PAT,
    A.AGT_APE_MAT
FROM
    NPMPR.GFVTAGT0 A
WHERE
    EXISTS (
        SELECT 1
        FROM NPMPR.GFVTCNA0 C
        WHERE C.AGT_IDR = A.AGT_IDR
          AND C.TFP_CVE = 7
          AND C.CNA_FEC_CNX > '2024-01-01'
    )
ORDER BY
    A.AGT_IDR;
```

**Paso 2: Consultar los resultados de todos los planes**

Una vez ejecutado el bloque anterior, utilice esta consulta para ver los planes de acceso generados para todas sus sentencias.

```sql
SELECT
    QUERYNO,
    METHOD,
    TNAME AS TABLE_NAME,
    ACCESSTYPE,
    ACCESSNAME AS INDEX_USED,
    PREFETCH,
    SORTC_GROUPBY,
    SORTC_ORDERBY,
    JOIN_TYPE -- Esta columna es útil para joins
FROM
    DSN_PLAN_TABLE
WHERE
    QUERYNO IN (1, 2, 3, 4, 5)
ORDER BY
    QUERYNO,
    PLANNO,
    MIXOPSEQ;
```

**Cómo interpretar los resultados:**

*   **`QUERYNO`**: Identifica la consulta (1, 2, 3, 4 o 5) a la que pertenece la fila del plan.
*   **`ACCESSTYPE`**: Es la columna más importante para esta verificación.
    *   **`I` o `IX`**: Significa **Index Scan**. ¡Esto es lo que se busca! Indica que DB2 está usando un índice.
    *   **`R`**: Significa **Table Scan**. Esto indica un problema de rendimiento, ya que DB2 está leyendo la tabla completa. Si ve esto, el índice para esa tabla no existe, no es útil para la consulta, o las estadísticas no están actualizadas (ver `RUNSTATS` en la siguiente sección).
*   **`INDEX_USED`**: Muestra el nombre exacto del índice que DB2 ha decidido usar.

---

## 4. Recomendaciones Adicionales de Mantenimiento en DB2

La creación de índices es solo una parte de la optimización. Las siguientes acciones son cruciales para mantener un rendimiento óptimo a largo plazo.

### a. Actualizar Estadísticas (`RUNSTATS`)

Después de crear o modificar índices, es **mandatorio** ejecutar el comando `RUNSTATS`. Esto actualiza el catálogo de estadísticas de DB2, permitiendo que el optimizador de consultas tome decisiones informadas y elija los planes de acceso más eficientes.

**Comando recomendado:**
```sql
-- Ejecutar para cada una de las tablas modificadas y accedidas por el proceso
RUNSTATS ON TABLE NPMPR.GFVTAGT0 WITH DISTRIBUTION AND DETAILED INDEXES ALL;
RUNSTATS ON TABLE NPMPR.GFVTCNA0 WITH DISTRIBUTION AND DETAILED INDEXES ALL;
RUNSTATS ON TABLE NPMPR.GFVTFLA0 WITH DISTRIBUTION AND DETAILED INDEXES ALL;
RUNSTATS ON TABLE NPMPR.GFVTDRC0 WITH DISTRIBUTION AND DETAILED INDEXES ALL;
RUNSTATS ON TABLE NPMPR.GFVTPRA0 WITH DISTRIBUTION AND DETAILED INDEXES ALL;
RUNSTATS ON TABLE NPMPR.GFVTDMA0 WITH DISTRIBUTION AND DETAILED INDEXES ALL;
-- ... y así sucesivamente para el resto de tablas involucradas.
```

### b. Revisar `Buffer Pools`

Asegúrese de que las tablas e índices más accedidos en este proceso batch estén asignados a un `buffer pool` con memoria suficiente. Un `buffer pool` bien dimensionado reduce drásticamente la necesidad de leer datos desde el disco (I/O físico), que es una de las operaciones más costosas en una base de datos.

---

## Conclusión

La combinación de las optimizaciones de **caché en la aplicación** y la implementación de los **índices y mantenimientos en DB2** aquí descritos, debería resultar en una mejora sustancial del rendimiento del proceso `GGNF0100`, reduciendo drásticamente su tiempo de ejecución y la carga sobre la base de datos.