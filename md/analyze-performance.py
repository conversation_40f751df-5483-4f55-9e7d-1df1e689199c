#!/usr/bin/env python3
"""
Analizador de Rendimiento de Queries - PGANFD01
Procesa el CSV generado por MethodExecutionLoggingAspect y genera reportes de rendimiento
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import sys
import os

def load_data(csv_file):
    """Carga los datos del CSV"""
    try:
        df = pd.read_csv(csv_file)
        df['Timestamp'] = pd.to_datetime(df['Timestamp'])
        return df
    except FileNotFoundError:
        print(f"❌ Error: No se encontró el archivo {csv_file}")
        return None
    except Exception as e:
        print(f"❌ Error cargando datos: {e}")
        return None

def analyze_performance(df):
    """Analiza el rendimiento de las queries"""
    print("=" * 80)
    print("📊 ANÁLISIS DE RENDIMIENTO DE QUERIES - PGANFD01")
    print("=" * 80)
    
    # Estadísticas generales
    total_queries = len(df)
    total_time = df['ExecutionTimeMs'].sum()
    avg_time = df['ExecutionTimeMs'].mean()
    
    print(f"\n📈 ESTADÍSTICAS GENERALES:")
    print(f"   Total de queries ejecutadas: {total_queries:,}")
    print(f"   Tiempo total de ejecución: {total_time:,.2f} ms ({total_time/1000:.2f} segundos)")
    print(f"   Tiempo promedio por query: {avg_time:.3f} ms")
    
    # Top 10 queries más lentas
    print(f"\n🐌 TOP 10 QUERIES MÁS LENTAS:")
    slowest = df.nlargest(10, 'ExecutionTimeMs')[['MethodName', 'ExecutionTimeMs', 'OperationType', 'Category']]
    for i, (_, row) in enumerate(slowest.iterrows(), 1):
        print(f"   {i:2d}. {row['MethodName']:<50} {row['ExecutionTimeMs']:>8.2f}ms [{row['OperationType']}]")
    
    # Análisis por tipo de operación
    print(f"\n🔍 ANÁLISIS POR TIPO DE OPERACIÓN:")
    op_stats = df.groupby('OperationType').agg({
        'ExecutionTimeMs': ['count', 'sum', 'mean', 'max'],
        'HasException': 'sum'
    }).round(3)
    
    op_stats.columns = ['Count', 'TotalTime', 'AvgTime', 'MaxTime', 'Exceptions']
    op_stats = op_stats.sort_values('TotalTime', ascending=False)
    
    print(f"{'Tipo de Operación':<25} {'Count':<8} {'Total(ms)':<12} {'Avg(ms)':<10} {'Max(ms)':<10} {'Errors':<8}")
    print("-" * 80)
    for op_type, stats in op_stats.iterrows():
        print(f"{op_type:<25} {stats['Count']:<8.0f} {stats['TotalTime']:<12.2f} {stats['AvgTime']:<10.3f} {stats['MaxTime']:<10.2f} {stats['Exceptions']:<8.0f}")
    
    # Análisis de optimizaciones
    print(f"\n🚀 ANÁLISIS DE OPTIMIZACIONES:")
    optimized_queries = df[df['Category'] == 'OPTIMIZED_CACHE']
    if len(optimized_queries) > 0:
        print(f"   Queries optimizadas ejecutadas: {len(optimized_queries):,}")
        print(f"   Tiempo total optimizaciones: {optimized_queries['ExecutionTimeMs'].sum():.2f} ms")
        print(f"   Tiempo promedio optimizado: {optimized_queries['ExecutionTimeMs'].mean():.3f} ms")
        
        # Desglose por tabla optimizada
        opt_by_table = optimized_queries.groupby('OperationType').agg({
            'ExecutionTimeMs': ['count', 'sum', 'mean']
        }).round(3)
        opt_by_table.columns = ['Count', 'TotalTime', 'AvgTime']
        
        print(f"\n   📋 Desglose por tabla optimizada:")
        for table, stats in opt_by_table.iterrows():
            print(f"      {table:<20} {stats['Count']:<8.0f} queries, {stats['TotalTime']:<10.2f}ms total, {stats['AvgTime']:<8.3f}ms promedio")
    
    # Análisis temporal
    print(f"\n⏰ ANÁLISIS TEMPORAL:")
    df['TimeGroup'] = df['Timestamp'].dt.floor('1min')  # Agrupar por minuto
    temporal_stats = df.groupby('TimeGroup').agg({
        'ExecutionTimeMs': ['count', 'sum', 'mean']
    }).round(3)
    temporal_stats.columns = ['QueriesPerMinute', 'TotalTimePerMinute', 'AvgTimePerMinute']
    
    print(f"   Queries por minuto (promedio): {temporal_stats['QueriesPerMinute'].mean():.1f}")
    print(f"   Tiempo por minuto (promedio): {temporal_stats['TotalTimePerMinute'].mean():.2f} ms")
    
    # Detección de problemas
    print(f"\n⚠️  DETECCIÓN DE PROBLEMAS:")
    slow_threshold = df['ExecutionTimeMs'].quantile(0.95)  # 95th percentile
    slow_queries = df[df['ExecutionTimeMs'] > slow_threshold]
    
    if len(slow_queries) > 0:
        print(f"   Queries lentas (>P95={slow_threshold:.2f}ms): {len(slow_queries)} queries")
        problem_operations = slow_queries['OperationType'].value_counts().head(5)
        print(f"   Operaciones problemáticas:")
        for op, count in problem_operations.items():
            avg_time = slow_queries[slow_queries['OperationType'] == op]['ExecutionTimeMs'].mean()
            print(f"      {op:<25} {count} queries, {avg_time:.2f}ms promedio")
    
    exceptions = df[df['HasException'] == True]
    if len(exceptions) > 0:
        print(f"   Queries con excepciones: {len(exceptions)}")
        exc_types = exceptions['ExceptionType'].value_counts()
        for exc_type, count in exc_types.items():
            print(f"      {exc_type}: {count} veces")

def generate_charts(df, output_dir="pganfd01/logs"):
    """Genera gráficos de análisis"""
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Gráfico 1: Distribución de tiempos por tipo de operación
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        top_operations = df['OperationType'].value_counts().head(10)
        top_operations.plot(kind='bar')
        plt.title('Top 10 Operaciones por Frecuencia')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        plt.subplot(2, 2, 2)
        df.boxplot(column='ExecutionTimeMs', by='Category', ax=plt.gca())
        plt.title('Distribución de Tiempos por Categoría')
        plt.suptitle('')
        
        plt.subplot(2, 2, 3)
        df['ExecutionTimeMs'].hist(bins=50, alpha=0.7)
        plt.title('Histograma de Tiempos de Ejecución')
        plt.xlabel('Tiempo (ms)')
        plt.ylabel('Frecuencia')
        
        plt.subplot(2, 2, 4)
        # Timeline de queries por minuto
        df['TimeGroup'] = df['Timestamp'].dt.floor('1min')
        timeline = df.groupby('TimeGroup').size()
        timeline.plot()
        plt.title('Queries por Minuto')
        plt.xlabel('Tiempo')
        plt.ylabel('Número de Queries')
        
        plt.tight_layout()
        chart_file = f"{output_dir}/performance-analysis.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"\n📊 Gráficos guardados en: {chart_file}")
        
    except ImportError:
        print("\n📊 Para generar gráficos, instala: pip install matplotlib seaborn")
    except Exception as e:
        print(f"\n❌ Error generando gráficos: {e}")

def main():
    csv_file = "pganfd01/logs/query-performance-metrics.csv"
    
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    
    print(f"🔍 Analizando archivo: {csv_file}")
    
    df = load_data(csv_file)
    if df is None:
        print("\n💡 Para generar el CSV, ejecuta el programa con logging.level.mx.com.gnp=DEBUG")
        return
    
    if len(df) == 0:
        print("❌ El archivo CSV está vacío")
        return
    
    analyze_performance(df)
    generate_charts(df)
    
    print(f"\n✅ Análisis completado. Total de registros procesados: {len(df):,}")
    print(f"📁 Archivo CSV: {csv_file}")

if __name__ == "__main__":
    main()
