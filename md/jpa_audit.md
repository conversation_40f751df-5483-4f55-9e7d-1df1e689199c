# JPA Audit y Monitoreo de Performance

## Resumen
Este documento describe la implementación de auditoría JPA y monitoreo de performance en el proyecto pganfd01, incluyendo la configuración de Spring Boot Actuator, métricas de Hibernate, y herramientas de monitoreo en tiempo real.

## Problema Inicial Resuelto

### Import Spring Boot Actuator
**Problema**: El import `org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer` no se resolvía en el IDE.

**Solución**:
1. Verificamos que la dependencia ya estaba en `build.gradle`:
   ```gradle
   implementation 'org.springframework.boot:spring-boot-starter-actuator'
   ```

2. Resolvimos conflictos de dependencias comentando temporalmente:
   ```gradle
   // P6Spy para logging de SQL (comentado temporalmente por problemas de repositorio)
   // implementation 'com.p6spy:p6spy:3.9.0'

   // DataSource Proxy para métricas adicionales
   // implementation 'net.ttddyy:datasource-proxy:1.9.0'

   // Hibernate para métricas JPA
   // implementation 'org.hibernate.orm:hibernate-micrometer'
   ```

3. Configuramos DB2 directo en lugar de P6Spy:
   ```yaml
   spring:
     datasource:
       driver-class-name: com.ibm.db2.jcc.DB2Driver
       url: **********************************************************;
   ```

4. Deshabilitamos Hazelcast Health Check:
   ```yaml
   management:
     health:
       hazelcast:
         enabled: false
   ```

## Configuración de Métricas

### MetricsConfiguration.java
Clase principal para configuración de métricas de rendimiento JPA/Hibernate:

```java
@Configuration
public class MetricsConfiguration {

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> {
            registry.config()
                    .commonTags(
                            "application", "pganfd01",
                            "environment", "local",
                            "version", "1.0.0-SNAPSHOT");
        };
    }

    @Bean
    public HibernateMetricsConfiguration hibernateMetrics(
            EntityManagerFactory entityManagerFactory,
            MeterRegistry meterRegistry) {
        return new HibernateMetricsConfiguration(entityManagerFactory, meterRegistry);
    }

    @Bean
    public ApplicationMetrics applicationMetrics(MeterRegistry meterRegistry) {
        return new ApplicationMetrics(meterRegistry);
    }
}
```

### Métricas Personalizadas
- **hibernate.query.execution**: Tiempo de ejecución de queries Hibernate
- **hibernate.cache.hit.ratio**: Ratio de aciertos de caché
- **hibernate.connections.active**: Conexiones activas
- **hibernate.transactions.total**: Total de transacciones
- **application.query.slow**: Queries lentas (>100ms)

## Configuración de Actuator

### application-local.yml
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,httptrace,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  health:
    hazelcast:
      enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
        jvm.gc.pause: true
        hikaricp.connections: true
    tags:
      application: pganfd01
      environment: local
```

## Configuración de Logging Detallado

### Logging SQL y Transacciones
```yaml
logging:
  level:
    org:
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql:
              BasicBinder: TRACE
        stat: DEBUG
        engine:
          transaction:
            internal:
              TransactionImpl: DEBUG
        cache: DEBUG
      springframework:
        transaction: TRACE
        batch: DEBUG
        jdbc: DEBUG
    com:
      zaxxer:
        hikari: DEBUG
    mx:
      com:
        gnp: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### JPA Properties
```yaml
spring:
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        generate_statistics: true
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 100
```

## Herramientas de Monitoreo

### 1. Endpoints de Actuator
Una vez que la aplicación esté corriendo:

- **Health Check**: `http://localhost:8080/actuator/health`
- **Métricas generales**: `http://localhost:8080/actuator/metrics`
- **Métricas de Hibernate**: `http://localhost:8080/actuator/metrics/hibernate.query.execution`
- **Métricas de conexiones**: `http://localhost:8080/actuator/metrics/hikaricp.connections`
- **Prometheus (todas las métricas)**: `http://localhost:8080/actuator/prometheus`

### 2. Monitoreo de Logs en Tiempo Real

#### Ver todos los logs:
```bash
cd pganfd01
tail -f logs/application.log
```

#### Solo queries SQL:
```bash
tail -f logs/application.log | grep -E "(Hibernate:|SQL:|Transaction)"
```

#### Progreso del batch:
```bash
tail -f logs/application.log | grep -E "(Step|Job|Batch|Processing|Completed|Failed)"
```

### 3. Scripts de Monitoreo Automático

#### Script Principal: `md/monitor-jpa-progress.sh`

```bash
#!/bin/bash
# Script para monitorear el progreso de pganfd01
echo "=== Monitor de Progreso PGANFD01 ==="

while true; do
    clear
    echo "=== MONITOR PGANFD01 - $(date) ==="

    # Verificar si la aplicación está corriendo
    if curl -s http://localhost:8080/actuator/health >/dev/null 2>&1; then
        echo "✅ Aplicación CORRIENDO"

        # Métricas de Actuator
        curl -s http://localhost:8080/actuator/metrics/hibernate.query.execution 2>/dev/null | jq '.'

        # Estadísticas de conexiones
        curl -s http://localhost:8080/actuator/metrics/hikaricp.connections.active 2>/dev/null | jq '.measurements[0].value'
    else
        echo "⏳ Aplicación iniciando..."
    fi

    # Logs recientes
    if [ -f "logs/application.log" ]; then
        tail -20 logs/application.log | grep -E "(SQL|Transaction|Batch|Processing)"
    fi

    sleep 10
done
```

**Ubicaciones de Scripts**:
- `md/monitor-jpa-progress.sh` - Monitor principal JPA/Hibernate (RECOMENDADO)
- `md/run-performance-analysis.sh` - Análisis de performance existente
- `md/analyze-performance.py` - Análisis Python
- `pganfd01/monitor-progress.sh` - Monitor básico (alternativo)

**Uso del Monitor Principal**:
```bash
# Desde directorio pganfd01
cd pganfd01
../md/monitor-jpa-progress.sh

# O desde raíz del proyecto
./md/monitor-jpa-progress.sh
```

## Comandos de Ejecución

### Ejecutar la aplicación:
```bash
./gradlew :pganfd01:bootRun --args='--spring.profiles.active=local'
```

### Compilar solo:
```bash
./gradlew :pganfd01:compileJava
```

### Build completo:
```bash
./gradlew :pganfd01:clean :pganfd01:build
```

## Métricas Clave a Monitorear

### Durante Procesos Largos:
1. **Tiempo de queries**: `hibernate.query.execution`
2. **Conexiones activas**: `hikaricp.connections.active`
3. **Conexiones idle**: `hikaricp.connections.idle`
4. **Transacciones**: `hibernate.transactions.total`
5. **Queries lentas**: `application.query.slow`
6. **Cache hit ratio**: `hibernate.cache.hit.ratio`

### Endpoints de Métricas Específicas:
```bash
# Métricas de Hibernate
curl http://localhost:8080/actuator/metrics/hibernate.query.execution

# Conexiones de base de datos
curl http://localhost:8080/actuator/metrics/hikaricp.connections

# Métricas de JVM
curl http://localhost:8080/actuator/metrics/jvm.memory.used

# Todas las métricas en formato Prometheus
curl http://localhost:8080/actuator/prometheus
```

## Troubleshooting

### Problemas Comunes:

1. **Import Actuator no resuelve**:
   - Verificar dependencia en build.gradle
   - Ejecutar `./gradlew clean build --refresh-dependencies`

2. **P6Spy no disponible**:
   - Usar DB2 driver directo: `com.ibm.db2.jcc.DB2Driver`
   - URL sin p6spy: `***********************`

3. **Hazelcast Health Error**:
   - Deshabilitar: `management.health.hazelcast.enabled: false`

4. **Aplicación lenta al arrancar**:
   - Monitorear con `tail -f logs/application.log`
   - Verificar conexión a DB2
   - Revisar métricas de conexiones

## Optimización de Rendimiento

### 🚀 **Configuración Asíncrona - NO Afecta el Rendimiento**

**Todas las métricas y monitoreo están configuradas para NO ralentizar tu proceso:**

1. **Actuator**: Usa hilos separados
2. **Micrometer**: Métricas asíncronas por defecto
3. **Logging**: No-bloqueante
4. **Prometheus**: Exportación cada 10s en hilo separado

### Configuración de Pool de Conexiones Optimizada:
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20      # Más conexiones para procesos largos
      minimum-idle: 5            # Conexiones mínimas siempre disponibles
      connection-timeout: 30000  # 30s timeout
      idle-timeout: 600000       # 10min idle
      max-lifetime: 1800000      # 30min lifetime máximo
```

### Métricas Asíncronas:
```yaml
management:
  metrics:
    export:
      prometheus:
        step: 10s              # Exportar cada 10 segundos
        descriptions: false    # Menos overhead
    web:
      server:
        request:
          autotime:
            enabled: false     # Deshabilitar timing automático
```

### Puerto de la Aplicación:
- **Puerto por defecto**: `8080`
- **Endpoints Actuator**: `http://localhost:8080/actuator/*`
- **Cambiar puerto**: Agregar `server.port: 8081` en application-local.yml

## Comandos de Monitoreo Optimizados

### Monitoreo Ligero (Recomendado para procesos largos):
```bash
# Solo métricas clave cada 30 segundos
watch -n 30 'curl -s http://localhost:8080/actuator/metrics/hikaricp.connections.active | jq ".measurements[0].value"'

# Logs solo de queries lentas
tail -f logs/application.log | grep -E "(slow|SLOW|>100ms)"
```

### Monitoreo Completo (Solo cuando necesites debug):
```bash
# Script completo
./monitor-progress.sh

# Logs completos
tail -f logs/application.log
```

## Estado Actual

✅ **Resuelto**: Import Spring Boot Actuator
✅ **Resuelto**: Configuración de métricas
✅ **Resuelto**: Logging detallado
✅ **Resuelto**: Conexión DB2 directa
✅ **Implementado**: Herramientas de monitoreo
✅ **Optimizado**: Configuración asíncrona para procesos largos
✅ **Optimizado**: Pool de conexiones para batch

**🚀 La aplicación está completamente optimizada para procesos largos con monitoreo asíncrono que NO afecta el rendimiento.**