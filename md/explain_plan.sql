-- Planes de acceso extraídos de la documentación de optimización
EXPLAIN PLAN SET QUERYNO = 1 FOR SELECT * FROM NPMPR.GFVTCNA0 WHERE AGT_IDR = 12345;

EXPLAIN PLAN SET QUERYNO = 2 FOR SELECT * FROM NPMPR.GFVTFLA0 WHERE AGT_IDR = 12345 AND CNA_NUM = 678;

EXPLAIN PLAN SET QUERYNO = 3 FOR SELECT * FROM NPMPR.GFVTDRC0 WHERE AGT_IDR = 12345 AND CNA_NUM = 678;

EXPLAIN PLAN SET QUERYNO = 4 FOR SELECT * FROM NPMPR.GFVTDMA0 WHERE AGT_IDR = 12345 AND TDM_CVE = 1;

EXPLAIN PLAN SET QUERYNO = 5 FOR
SELECT
    A.AGT_IDR,
    A.AGT_RFC,
    A.AGT_NOM,
    A.AGT_APE_PAT,
    A.AGT_APE_MAT
FROM
    NPMPR.GFVTAGT0 A
WHERE
    EXISTS (
        SELECT 1
        FROM NPMPR.GFVTCNA0 C
        WHERE C.AGT_IDR = A.AGT_IDR
          AND C.TFP_CVE = 7
          AND C.CNA_FEC_CNX > '2024-01-01'
    )
ORDER BY
    A.AGT_IDR;

-- --------------------------------------------------------------------------------
-- Planes de acceso adicionales para análisis avanzado
-- --------------------------------------------------------------------------------

-- Query 6: Agentes y el número de contratos que tienen, ordenado por cantidad.
EXPLAIN PLAN SET QUERYNO = 6 FOR
SELECT A.AGT_IDR, A.AGT_NOM, COUNT(C.CNA_NUM) AS NUM_CONTRATOS
FROM NPMPR.GFVTAGT0 A
JOIN NPMPR.GFVTCNA0 C ON A.AGT_IDR = C.AGT_IDR
GROUP BY A.AGT_IDR, A.AGT_NOM
ORDER BY NUM_CONTRATOS DESC;

-- Query 7: Agentes con contratos de tipo 'Gerente' (TFP_CVE = 7) que no han tenido conexión reciente.
EXPLAIN PLAN SET QUERYNO = 7 FOR
SELECT A.AGT_IDR, A.AGT_NOM
FROM NPMPR.GFVTAGT0 A
JOIN NPMPR.GFVTCNA0 C ON A.AGT_IDR = C.AGT_IDR
WHERE C.TFP_CVE = 7 AND C.CNA_FEC_CNX < '2023-01-01';

-- Query 8: Folios emitidos para un rango de fechas de contrato.
EXPLAIN PLAN SET QUERYNO = 8 FOR
SELECT F.*
FROM NPMPR.GFVTFLA0 F
JOIN NPMPR.GFVTCNA0 C ON F.AGT_IDR = C.AGT_IDR AND F.CNA_NUM = C.CNA_NUM
WHERE C.CNA_FEC_CNX BETWEEN '2023-01-01' AND '2023-12-31';

-- Query 9: Derechos de contrato para agentes con un apellido específico.
EXPLAIN PLAN SET QUERYNO = 9 FOR
SELECT D.*
FROM NPMPR.GFVTDRC0 D
JOIN NPMPR.GFVTAGT0 A ON D.AGT_IDR = A.AGT_IDR
WHERE A.AGT_APE_PAT LIKE 'GARCIA%';

-- Query 10: Domicilios de agentes en un estado específico (ej. TDM_CVE para estado).
EXPLAIN PLAN SET QUERYNO = 10 FOR
SELECT A.AGT_NOM, M.*
FROM NPMPR.GFVTDMA0 M
JOIN NPMPR.GFVTAGT0 A ON M.AGT_IDR = A.AGT_IDR
WHERE M.TDM_CVE = 9; -- Suponiendo que 9 es un TDM_CVE para un estado

-- Query 12: Medios de contacto para agentes con un RFC específico.
EXPLAIN PLAN SET QUERYNO = 12 FOR
SELECT C.*
FROM NPMPR.GFVTCMA0 C
JOIN NPMPR.GFVTAGT0 A ON C.AGT_IDR = A.AGT_IDR
WHERE A.AGT_RFC = 'XXXX123456YYY';

-- Query 14: Agentes sin folios registrados (uso de LEFT JOIN).
EXPLAIN PLAN SET QUERYNO = 14 FOR
SELECT A.*
FROM NPMPR.GFVTAGT0 A
LEFT JOIN NPMPR.GFVTFLA0 F ON A.AGT_IDR = F.AGT_IDR
WHERE F.AGT_IDR IS NULL;

-- Query 17: Total de folios por tipo de contrato.
EXPLAIN PLAN SET QUERYNO = 17 FOR
SELECT C.TFP_CVE, COUNT(F.FLA_NUM) AS TOTAL_FOLIOS
FROM NPMPR.GFVTFLA0 F
JOIN NPMPR.GFVTCNA0 C ON F.AGT_IDR = C.AGT_IDR AND F.CNA_NUM = C.CNA_NUM
GROUP BY C.TFP_CVE;

-- Query 19: Usando UNION para combinar agentes de dos tipos de contrato.
EXPLAIN PLAN SET QUERYNO = 19 FOR
SELECT T1.AGT_IDR, T1.AGT_NOM FROM NPMPR.GFVTAGT0 T1 WHERE T1.AGT_IDR IN (SELECT T2.AGT_IDR FROM NPMPR.GFVTCNA0 T2 WHERE T2.TFP_CVE = 1)
UNION
SELECT T3.AGT_IDR, T3.AGT_NOM FROM NPMPR.GFVTAGT0 T3 WHERE T3.AGT_IDR IN (SELECT T4.AGT_IDR FROM NPMPR.GFVTCNA0 T4 WHERE T4.TFP_CVE = 2);

-- Query 20: Ranking de agentes por número de contratos (uso de window functions).
EXPLAIN PLAN SET QUERYNO = 20 FOR
SELECT A.AGT_IDR, A.AGT_NOM, RANK() OVER (ORDER BY COUNT(C.CNA_NUM) DESC) AS RANK_CONTRATOS
FROM NPMPR.GFVTAGT0 A
JOIN NPMPR.GFVTCNA0 C ON A.AGT_IDR = C.AGT_IDR
GROUP BY A.AGT_IDR, A.AGT_NOM;

-- Query 21: Contratos que no tienen derechos registrados (uso de NOT EXISTS).
EXPLAIN PLAN SET QUERYNO = 21 FOR
SELECT C.*
FROM NPMPR.GFVTCNA0 C
WHERE NOT EXISTS (SELECT 1 FROM NPMPR.GFVTDRC0 D WHERE D.AGT_IDR = C.AGT_IDR AND D.CNA_NUM = C.CNA_NUM);

-- Query 22: Domicilios fiscales (TDM_CVE=3) de agentes con contratos de 'Productor' (TFP_CVE=5).
EXPLAIN PLAN SET QUERYNO = 22 FOR
SELECT A.AGT_RFC, D.*
FROM NPMPR.GFVTDMA0 D
JOIN NPMPR.GFVTAGT0 A ON D.AGT_IDR = A.AGT_IDR
JOIN NPMPR.GFVTCNA0 C ON A.AGT_IDR = C.AGT_IDR
WHERE D.TDM_CVE = 3 AND C.TFP_CVE = 5;

-- Query 23: Agentes cuyo nombre o apellido materno contiene 'DE LA'.
EXPLAIN PLAN SET QUERYNO = 23 FOR
SELECT AGT_IDR, AGT_NOM, AGT_APE_MAT
FROM NPMPR.GFVTAGT0
WHERE AGT_NOM LIKE '%DE LA%' OR AGT_APE_MAT LIKE '%DE LA%';

-- Query 24: Categorizar agentes por número de contratos usando CASE.
EXPLAIN PLAN SET QUERYNO = 24 FOR
SELECT A.AGT_IDR, A.AGT_NOM,
  CASE
    WHEN (SELECT COUNT(*) FROM NPMPR.GFVTCNA0 C WHERE C.AGT_IDR = A.AGT_IDR) > 10 THEN 'Top'
    WHEN (SELECT COUNT(*) FROM NPMPR.GFVTCNA0 C WHERE C.AGT_IDR = A.AGT_IDR) > 5 THEN 'Medio'
    ELSE 'Regular'
  END AS CATEGORIA
FROM NPMPR.GFVTAGT0 A;

-- --------------------------------------------------------------------------------
-- Paso final: Consultar los resultados de todos los planes de acceso
-- --------------------------------------------------------------------------------
/*
SELECT
    QUERYNO,
    METHOD,
    TNAME AS TABLE_NAME,
    ACCESSTYPE,
    ACCESSNAME AS INDEX_USED,
    PREFETCH,
    SORTC_GROUPBY,
    SORTC_ORDERBY,
    JOIN_TYPE
FROM
    DSN_PLAN_TABLE
WHERE
    QUERYNO BETWEEN 1 AND 30
ORDER BY
    QUERYNO,
    PLANNO,
    MIXOPSEQ;
*/
