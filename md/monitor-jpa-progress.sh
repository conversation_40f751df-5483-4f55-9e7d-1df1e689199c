#!/bin/bash

# Script para monitorear el progreso JPA/Hibernate de pganfd01
# Ubicación: md/monitor-jpa-progress.sh
# Uso: cd pganfd01 && ../md/monitor-jpa-progress.sh

echo "=== Monitor de Progreso JPA/Hibernate PGANFD01 ==="
echo "Presiona Ctrl+C para salir"
echo ""

# Verificar que estamos en el directorio correcto
if [ ! -f "logs/application.log" ] && [ ! -d "../pganfd01/logs" ]; then
    echo "❌ Error: Ejecutar desde el directorio pganfd01 o desde la raíz del proyecto"
    echo "Uso: cd pganfd01 && ../md/monitor-jpa-progress.sh"
    echo "  o: ./md/monitor-jpa-progress.sh (desde raíz)"
    exit 1
fi

# Determinar la ruta del log
LOG_PATH="logs/application.log"
if [ ! -f "$LOG_PATH" ] && [ -f "pganfd01/logs/application.log" ]; then
    LOG_PATH="pganfd01/logs/application.log"
fi

# Función para mostrar métricas de Actuator
show_metrics() {
    echo "=== MÉTRICAS JPA/HIBERNATE ==="
    
    # Métricas de queries
    QUERY_METRICS=$(curl -s http://localhost:8080/actuator/metrics/hibernate.query.execution 2>/dev/null)
    if [ $? -eq 0 ] && [ "$QUERY_METRICS" != "" ]; then
        echo "📊 Métricas de Queries:"
        echo "$QUERY_METRICS" | jq -r '.measurements[] | "  \(.statistic): \(.value)"' 2>/dev/null || echo "  Datos disponibles en /actuator/metrics"
    else
        echo "⏳ Métricas de queries no disponibles aún"
    fi
    
    # Métricas de transacciones
    TRANSACTION_METRICS=$(curl -s http://localhost:8080/actuator/metrics/hibernate.transactions.total 2>/dev/null)
    if [ $? -eq 0 ] && [ "$TRANSACTION_METRICS" != "" ]; then
        echo "💾 Transacciones totales:"
        echo "$TRANSACTION_METRICS" | jq -r '.measurements[0].value' 2>/dev/null | xargs -I {} echo "  Total: {}"
    fi
    
    echo ""
}

# Función para mostrar logs recientes de JPA
show_recent_logs() {
    echo "=== LOGS JPA/SQL RECIENTES ==="
    if [ -f "$LOG_PATH" ]; then
        # Mostrar queries SQL y transacciones recientes
        tail -30 "$LOG_PATH" | grep -E "(Hibernate:|SQL|Transaction|select|insert|update|delete|Processing|Completed)" | tail -10 || echo "No hay logs SQL recientes"
    else
        echo "Archivo de log no encontrado: $LOG_PATH"
    fi
    echo ""
}

# Función para mostrar estadísticas de conexiones
show_connection_stats() {
    echo "=== ESTADÍSTICAS DE CONEXIONES DB ==="
    
    # Conexiones activas
    ACTIVE_CONN=$(curl -s http://localhost:8080/actuator/metrics/hikaricp.connections.active 2>/dev/null | jq -r '.measurements[0].value' 2>/dev/null)
    if [ "$ACTIVE_CONN" != "" ] && [ "$ACTIVE_CONN" != "null" ]; then
        echo "🔗 Conexiones activas: $ACTIVE_CONN"
    else
        echo "⏳ Conexiones activas: No disponible"
    fi
    
    # Conexiones idle
    IDLE_CONN=$(curl -s http://localhost:8080/actuator/metrics/hikaricp.connections.idle 2>/dev/null | jq -r '.measurements[0].value' 2>/dev/null)
    if [ "$IDLE_CONN" != "" ] && [ "$IDLE_CONN" != "null" ]; then
        echo "💤 Conexiones idle: $IDLE_CONN"
    else
        echo "⏳ Conexiones idle: No disponible"
    fi
    
    # Pool total
    TOTAL_CONN=$(curl -s http://localhost:8080/actuator/metrics/hikaricp.connections 2>/dev/null | jq -r '.measurements[0].value' 2>/dev/null)
    if [ "$TOTAL_CONN" != "" ] && [ "$TOTAL_CONN" != "null" ]; then
        echo "📊 Pool total: $TOTAL_CONN"
    fi
    
    echo ""
}

# Función para mostrar queries lentas
show_slow_queries() {
    echo "=== QUERIES LENTAS (>100ms) ==="
    if [ -f "$LOG_PATH" ]; then
        # Buscar queries que tardaron más de 100ms
        tail -50 "$LOG_PATH" | grep -E "(slow|SLOW|>100ms|took.*ms)" | tail -5 || echo "No hay queries lentas recientes"
    fi
    echo ""
}

# Función para mostrar resumen de performance
show_performance_summary() {
    echo "=== RESUMEN DE PERFORMANCE ==="
    
    # JVM Memory
    JVM_MEMORY=$(curl -s http://localhost:8080/actuator/metrics/jvm.memory.used 2>/dev/null | jq -r '.measurements[0].value' 2>/dev/null)
    if [ "$JVM_MEMORY" != "" ] && [ "$JVM_MEMORY" != "null" ]; then
        JVM_MB=$(echo "scale=2; $JVM_MEMORY / 1024 / 1024" | bc 2>/dev/null || echo "N/A")
        echo "🧠 Memoria JVM usada: ${JVM_MB} MB"
    fi
    
    # GC
    GC_TIME=$(curl -s http://localhost:8080/actuator/metrics/jvm.gc.pause 2>/dev/null | jq -r '.measurements[] | select(.statistic=="TOTAL_TIME") | .value' 2>/dev/null)
    if [ "$GC_TIME" != "" ] && [ "$GC_TIME" != "null" ]; then
        echo "🗑️  Tiempo total GC: ${GC_TIME}s"
    fi
    
    echo ""
}

# Loop principal
while true; do
    clear
    echo "=== MONITOR JPA/HIBERNATE PGANFD01 - $(date) ==="
    echo ""
    
    # Verificar si la aplicación está corriendo
    if curl -s http://localhost:8080/actuator/health >/dev/null 2>&1; then
        echo "✅ Aplicación CORRIENDO - Puerto 8080"
        echo ""
        show_metrics
        show_connection_stats
        show_performance_summary
        show_slow_queries
    else
        echo "⏳ Aplicación iniciando o no disponible en puerto 8080..."
        echo ""
    fi
    
    show_recent_logs
    
    echo "🔄 Actualizando en 15 segundos... (Ctrl+C para salir)"
    sleep 15
done
