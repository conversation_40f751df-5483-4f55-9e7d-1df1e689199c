/**
 *  JCL Program: PGAQAD54.
 *  Code generation type: <PERSON><PERSON><PERSON><PERSON>YL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pgaqad54.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class Pgaqad54Job extends AbstractSpringBatchJobComponent {

    public Pgaqad54Job(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGAQAD54";
    }

    @Override
    public void initJob() {
        addParameter("(8600)");
        addParameter("'VER_OFNAS'");
        addParameter("NOTIFY",getParameter("SYSUID"));
        addParameter("MSGLEVEL", "(1,1)");
        addParameter("MSGCLASS","X");
        addParameter("CLASS","G");
        /*//*JOBPARM  P=PROC00,S=OSB*/
        /*//*JCLLIB JCLLIB ORDER=PNCQP.BTCH.PROCLIB*/
        /*//*****************************************************/
        setParameter("SSID","DB2P");
        setParameter("ALIASS","PGA.");
        setParameter("ALIASW","PGA.");
        setParameter("VOLPER","SHAREDA");
        setParameter("VOLTEM","TEMPROD");
        setParameter("VOLTAP","DDVTAV");
        setParameter("BIBCAR","PNCQP.BTCH.CARDS");
        addDataDefinition("JOBLIB",  (joblib) -> {
            /*//*****************************************************/
            joblib.addDataDefinition("JOBLIB", "SYS1.CEE.SCEERUN",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.RUNLIB.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNEXIT",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
        });
        /*//*        DD DSN=SYS2.BDSCCVID.PROD.LOADLIB,DISP=SHR*/
        /*//*        DD DSN=SYS2.BDSCCVID.PROD.LOADSSC,DISP=SHR*/
        /*//**/
        /*//* REQUERIDA POR MIGRACION A Z12*/
        getJcllib().setOrder();
    }

    /*//**/
    @Bean("pgaqad54Qad5400")
    public SpringBatchJobStep getQad5400(){
        return initStep(new SpringBatchJobStep((qad5400) -> {
            return qad5400.executeProcedure("QAD5400");
        }));
    }
}
