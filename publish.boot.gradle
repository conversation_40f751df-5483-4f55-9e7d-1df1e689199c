apply plugin: 'maven-publish'
apply plugin: 'org.springframework.boot'


def getGitVersion() {
    try {

        if (project.hasProperty('gitVersion')) {
            return project.getProperty('gitVersion')
        }

        def cmd = ["git", "describe", "--tags", "--abbrev=0"]
        def proc = cmd.execute()
        proc.waitFor()
        def version = proc.in.text.trim()

        return version
    } catch (Exception e) {
        println("Error: No se pudo obtener la versión de Git, lanzando RuntimeException." + e)
        throw new RuntimeException("No se pudo obtener la versión de Git", e)
    }
}


project.version = getGitVersion()

tasks.named("bootJar") {
    manifest {
        attributes(
                'Implementation-Title': project.name,
                'Implementation-Version': project.version,
                'Implementation-Vendor-Id': project.group,
                'Implementation-Vendor': 'GNP',
                'Build-Time': new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        )
    }
}

tasks.named("jar") {
    enabled = false
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
            artifact bootJar
        }
    }
    repositories {
        maven {
            credentials {
                username = "${nexusUser}"
                password = "${nexusPassword}"
            }
            url = project.version.endsWith('-SNAPSHOT') ? "${nexusSnapUrl}" : "${nexusUrl}"
            allowInsecureProtocol = true
        }
    }
}

tasks.withType(AbstractPublishToMaven).configureEach {
    doLast {
        String urlString = (project.version.endsWith('-SNAPSHOT') ? nexusSnapUrl : nexusUrl) +
                convertPackageNameToPath(publication.groupId) + "/" +
                publication.artifactId + "/" +
                publication.version + "/" +
                publication.artifactId + "-" + publication.version + ".jar"

        def jarFile = bootJar.archiveFile.get().asFile
        def jarSize = jarFile.exists() ? String.format("%.2f MB", jarFile.length() / (1024.0 * 1024.0)) : "Archivo no encontrado"

        println " "
        println " "
        println " "
        println "******** Información NEXUS ********"
        println "************************************"
        println " Artefacto en NEXUS => " + urlString
        println " "
        println " URL Nexus => " + nexusUrl
        println " Nombre del Artefacto => " + publication.artifactId
        println " Grupo del Artefacto =>  " + publication.groupId
        println " Versión del Artefacto => " + publication.version
        println " Tamaño del JAR => " + jarSize
        println "************************************"
        println "************************************"
        println " "
        println " "
        println " "
    }
}

static String convertPackageNameToPath(String packageName) {
    return packageName.replace(".", "/");
}

tasks.named("publishToMavenLocal") {
    doFirst {
        def jarFile = bootJar.archiveFile.get().asFile
        def jarSize = jarFile.exists() ? String.format("%.2f MB", jarFile.length() / (1024.0 * 1024.0)) : "Archivo no encontrado"

        println "Publicando Artefacto: ${bootJar.archiveFile.get().asFile.name}"
        println "Versión: ${project.version}"
        println "Tamaño del JAR: ${jarSize}"
        println "URL del Repositorio: ${project.version.endsWith('-SNAPSHOT') ? nexusSnapUrl : nexusUrl}"
    }
}

publish {
    dependsOn assemble
}
