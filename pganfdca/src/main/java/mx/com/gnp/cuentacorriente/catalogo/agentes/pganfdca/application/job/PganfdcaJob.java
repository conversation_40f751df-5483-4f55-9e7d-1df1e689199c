/**
 *  JCL Program: PGANFDCA.
 *  Code generation type: KY<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfdca.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class PganfdcaJob extends AbstractSpringBatchJobComponent {

    public PganfdcaJob(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGANFDCA";
    }

    @Override
    public void initJob() {
        addParameter("(8600)");
        addParameter("'EXT-AGTS'");
        addParameter("NOTIFY",getParameter("SYSUID"));
        addParameter("MSGLEVEL", "(1,1)");
        addParameter("MSGCLASS","Y");
        addParameter("CLASS","G");
        /*//*****************************************************/
        /*//*JOBPARM  P=PROC00,S=OSB*/
        /*//*****************************************************/
        setParameter("OWNER","NPMPR.");
        setParameter("SSID","DB2P");
        setParameter("ALIASA","PGA.EAFV.");
        setParameter("ALIASS","PGA.EAFV.");
        setParameter("VOLPER","SHAREDA");
        setParameter("VOLTEM","TEMPROD");
        setParameter("BIBCAR","PNCQP.BTCH.CARDS");
        addDataDefinition("JOBLIB",  (joblib) -> {
            /*//*****************************************************/
            joblib.addDataDefinition("JOBLIB", "SYS1.CEE.SCEERUN",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.RUNLIB.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNEXIT",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "SYS6.DB2.SDSNLOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
        });
        /*//*        DD DSN=SYS2.BDSCCVID.PROD.LOADLIB,DISP=SHR*/
        /*//*        DD DSN=SYS2.BDSCCVID.PROD.LOADSSC,DISP=SHR*/
        /*//**/
        /*//* REQUERIDA POR MIGRACION A Z12*/
        getJcllib().setOrder();
    }

    /*//**/
    /*//**/
    /*//**/
    @Bean("pganfdcaNfdca00")
    public SpringBatchJobStep getNfdca00(){
        return initStep(new SpringBatchJobStep((nfdca00) -> {
            return nfdca00.executeProcedure("NFDCA00");
        }));
    }
}
