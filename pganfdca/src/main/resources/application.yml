application:
  title: Program pganfdca
  description: |
    ### pganfdca in "Hexagonal Architecture"
  version: 1.0.0.SNAPSHOT

spring:
  main:
    web-application-type: NONE
  datasource:
    url: **********************************************************;
    user: db2inst1
    password: gnppassword$1
context:
  sort-program-name: /opt/app/ttsortx64
os390:
  base-path: /nfs
  data-path: ${os390.base-path}/data

caravel:
  support:
    bucket:
      directory: ${os390.data-path}
      up:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
          - 'PGA.EAFV.NFDCA00.DESCARGA.GFVTAGC0'
          - 'PGA.EAFV.NFDCA00.DESCARGA.GFVTCAF0'
      down:
        bucketName: "gnp-mm-usc1-cuentacorriente-fs-sync-qas"
        files:
