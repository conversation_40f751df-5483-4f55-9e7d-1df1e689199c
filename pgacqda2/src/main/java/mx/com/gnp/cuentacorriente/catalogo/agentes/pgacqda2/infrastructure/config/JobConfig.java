package mx.com.gnp.cuentacorriente.catalogo.agentes.pgacqda2.infrastructure.config;

import com.base100.caravel.support.os390.springbatch.JobExecutionProcessorListener;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@AllArgsConstructor
public class JobConfig {

    private final JobLauncher jobLauncher;
    private final JobRepository jobRepository;
    private final PlatformTransactionManager batchTransactionManager;
    private final Tasklet pgacqda2Cqda200;

    @Bean("pgacqda2")
    public Job pgacqda2(JobExecutionProcessorListener processorListener) {
        return new JobBuilder("pgacqda2", jobRepository)
            .incrementer(new RunIdIncrementer())
            .listener(processorListener)
            .start(pgacqda2Cqda200JobStep())
            .build();
    }

    public Step pgacqda2Cqda200JobStep() {
        return new StepBuilder("cqda200", jobRepository)
            .tasklet(pgacqda2Cqda200, batchTransactionManager)
            .build();
    }

}
