/**
 *  JCL Program: PGACQDA2.
 *  Code generation type: <PERSON>Y<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pgacqda2.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class Pgacqda2Job extends AbstractSpringBatchJobComponent {

    public Pgacqda2Job(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGACQDA2";
    }

    @Override
    public void initJob() {
        addParameter("(PROD,DB2)");
        addParameter("'BKPDB2'");
        addParameter("CLASS","G");
        addParameter("MSGCLASS","J");
        addParameter("REGION","0M");
        addParameter("NOTIFY",getParameter("SYSUID"));
        addParameter("MSGLEVEL", "(1,1)");
        /*//**/
        /*//*JOBPARM  P=PROC00,S=OSB*/
        /*//**/
        getJcllib().setOrder();
    }

    /*//**/
    @Bean("pgacqda2Cqda200")
    public SpringBatchJobStep getCqda200(){
        return initStep(new SpringBatchJobStep((cqda200) -> {
            cqda200.addParameter("SSID","DB2P");
            cqda200.addParameter("UID","A200DB2P");
            cqda200.addParameter("CARD","CQDA201");
            return cqda200.executeProcedure("QABKP00");
        }));
    }
}
