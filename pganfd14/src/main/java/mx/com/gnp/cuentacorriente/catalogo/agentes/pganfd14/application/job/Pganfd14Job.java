/**
 *  JCL Program: PGANFD14.
 *  Code generation type: <PERSON>Y<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pganfd14.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class Pganfd14Job extends AbstractSpringBatchJobComponent {

    public Pganfd14Job(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGANFD14";
    }

    @Override
    public void initJob() {
        addParameter("(GNPS,6)");
        addParameter("'ERRTRIG'");
        addParameter("NOTIFY",getParameter("SYSUID"));
        addParameter("MSGCLASS","X");
        addParameter("REGION","0M");
        addParameter("CLASS","G");
        addParameter("MSGLEVEL", "(1,1)");
        addParameter("LINES","999999");
        addDataDefinition("JOBLIB",  (joblib) -> {
            /*//**********************************************************************/
            /*//*   NOMBRE PROCESO : VALIDAR ERRORES DE TRIGGERS*/
            /*//*   OBJETIVO       : ALERTAR DE ERRORES DE SINCRONIZACION*/
            /*//*                    ENTRE CANALES Y CUENTA CORRIENTE*/
            /*//*   DEPENDENCIAS   : DESPUES DE PGAFVDRI,*/
            /*//*                    ANTES DE PGANFD01, PGANFD08, PGAXAD01 & PGACQDA2*/
            /*//*   ELABORO        : GUILLERMO PACHECO*/
            /*//*   FECHA          : ABR 17, 2018*/
            /*//*   ID RELEASE     : RFC0038951*/
            /*//**/
            /*//*   MODIFICO       :*/
            /*//*   FECHA          :*/
            /*//*   ID RELEASE     :*/
            /*//**********************************************************************/
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PNCQP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PXSTP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
            joblib.addDataDefinition("JOBLIB", "PXSTP.BTCH.LOAD",  (_concat) -> {
                _concat.addParameter("DISP","SHR");
            });
        });
        /*//**********************************************************************/
        /*//**********************************************************************/
        getJcllib().setOrder("PNCQP.BTCH.PROCLIB", 
            "PXSTP.BTCH.PROCLIB");
    }

    /*//**********************************************************************/
    /*//**/
    /*//*RMS      EXEC CA11RMS,TYPRUN='F'*/
    /*//**/
    /*//**********************************************************************/
    @Bean("pganfd14Nfd1400")
    public SpringBatchJobStep getNfd1400(){
        return initStep(new SpringBatchJobStep((nfd1400) -> {
            return nfd1400.executeProcedure("NFD1400");
        }));
    }
}
