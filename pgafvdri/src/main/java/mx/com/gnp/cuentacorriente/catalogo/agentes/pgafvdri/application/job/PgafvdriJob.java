/**
 *  JCL Program: PGAFVDRI.
 *  Code generation type: <PERSON>Y<PERSON>RYL GNP CUENTA CORRIENTE
 *  Version: 7.0 - 2025/05/28
 *
 */
package mx.com.gnp.cuentacorriente.catalogo.agentes.pgafvdri.application.job;

import com.base100.caravel.support.os390.spring.context.factory.SpringContextFactoryOs390;
import com.base100.caravel.support.os390.springbatch.AbstractSpringBatchJobComponent;
import com.base100.caravel.support.os390.springbatch.SpringBatchJobStep;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class PgafvdriJob extends AbstractSpringBatchJobComponent {

    public PgafvdriJob(SpringContextFactoryOs390 contextFactory){
        super(contextFactory);
    }

    @Override
    public String getJobName() {
        return "PGAFVDRI";
    }

    @Override
    public void initJob() {
        addParameter("(PROD,DB2)");
        addParameter("'BKPDB2'");
        addParameter("CLASS","G");
        addParameter("MSGCLASS","J");
        addParameter("REGION","0M");
        addParameter("NOTIFY",getParameter("SYSUID"));
        addParameter("MSGLEVEL", "(1,1)");
        /*//**/
        /*//*JOBPARM  P=PROC00,S=OSB*/
        /*//**/
        getJcllib().setOrder();
    }

    /*//**/
    @Bean("pgafvdriFvdri00")
    public SpringBatchJobStep getFvdri00(){
        return initStep(new SpringBatchJobStep((fvdri00) -> {
            fvdri00.addParameter("SSID","DB2P");
            fvdri00.addParameter("UID","FVINDB2P");
            fvdri00.addParameter("CARD","FVDRI01");
            return fvdri00.executeProcedure("QABKP00");
        }));
    }
}
