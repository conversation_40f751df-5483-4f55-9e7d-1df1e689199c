plugins {
    id 'java-library'
    id 'io.spring.dependency-management'
}

dependencies {
    implementation project(':catalogo-agentes-business')
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
}

apply from: "${rootDir}/publish.java.gradle"
