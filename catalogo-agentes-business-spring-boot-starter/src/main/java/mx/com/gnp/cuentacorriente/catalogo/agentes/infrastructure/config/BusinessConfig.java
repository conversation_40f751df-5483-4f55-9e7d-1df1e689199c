package mx.com.gnp.cuentacorriente.catalogo.agentes.infrastructure.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableJpaRepositories("mx.com.gnp.cuentacorriente.catalogo.agentes")
@PropertySource("classpath:catalogo-agentes-business.properties")
@EnableTransactionManagement
@EntityScan("mx.com.gnp.cuentacorriente.catalogo.agentes")
@ComponentScan(basePackages = {"com.base100.caravel.support", "mx.com.gnp.cuentacorriente.catalogo.agentes"})
public class BusinessConfig {
}


