apply plugin: 'maven-publish'


def getGitVersion() {
    try {

        if (project.hasProperty('gitVersion')) {
            return project.getProperty('gitVersion')
        }

        def cmd = ["git", "describe", "--tags", "--abbrev=0"]
        def proc = cmd.execute()
        proc.waitFor()
        def version = proc.in.text.trim()

        return version
    } catch (Exception e) {
        println("Error: No se pudo obtener la versión de Git, lanzando RuntimeException." + e)
        throw new RuntimeException("No se pudo obtener la versión de Git", e)
    }
}


project.version = getGitVersion()

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            credentials {
                username = "${nexusUser}"
                password = "${nexusPassword}"
            }
            url = project.version.endsWith('-SNAPSHOT') ? "${nexusSnapUrl}" : "${nexusUrl}"
            allowInsecureProtocol = true
        }
    }
}

tasks.withType(AbstractPublishToMaven).configureEach {
    doLast {
        String urlString = (project.version.endsWith('-SNAPSHOT') ? nexusSnapUrl : nexusUrl) +
                convertPackageNameToPath(publication.groupId) + "/" +
                publication.artifactId + "/" +
                publication.version + "/" +
                publication.artifactId + "-" + publication.version + ".jar"

        def jarFile = jar.archiveFile.get().asFile
        def jarSize = jarFile.exists() ? String.format("%.2f MB", jarFile.length() / (1024.0 * 1024.0)) : "Archivo no encontrado"

        println " "
        println " "
        println " "
        println "******** Información NEXUS ********"
        println "************************************"
        println " Artefacto en NEXUS => " + urlString
        println " "
        println " URL Nexus => " + nexusUrl
        println " Nombre del Artefacto => " + publication.artifactId
        println " Grupo del Artefacto =>  " + publication.groupId
        println " Versión del Artefacto => " + publication.version
        println " Tamaño del JAR => " + jarSize
        println "************************************"
        println "************************************"
        println " "
        println " "
        println " "
    }
}

static String convertPackageNameToPath(String packageName) {
    return packageName.replace(".", "/");
}

tasks.named("publishToMavenLocal") {
    doFirst {
        def jarFile = jar.archiveFile.get().asFile
        def jarSize = jarFile.exists() ? String.format("%.2f MB", jarFile.length() / (1024.0 * 1024.0)) : "Archivo no encontrado"

        println "Publicando Artefacto: ${jar.archiveFile.get().asFile.name}"
        println "Versión: ${project.version}"
        println "Tamaño del JAR: ${jarSize}"
        println "URL del Repositorio: ${project.version.endsWith('-SNAPSHOT') ? nexusSnapUrl : nexusUrl}"
    }
}

publish {
    dependsOn assemble
}
